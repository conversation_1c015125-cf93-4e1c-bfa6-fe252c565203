# 构建输出
build-*/
dist/
*.tar.gz
*.deb
*.rpm

# 编译产物
*.o
*.so
*.a
*.dylib
*.exe

# CMake生成文件
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
compile_commands.json

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 配置文件（包含敏感信息）
config/production.json
*.key
*.crt
*.pem

# 前端依赖和构建
node_modules/
.npm
.yarn
frontend/dist/

# 测试覆盖率
coverage/
*.gcov
*.gcda
*.gcno

# 临时文件
tmp/
temp/
*.tmp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup

# 运行时文件
*.pid
*.sock