#include "backend/include/core/clock_state_machine.h"
#include "backend/include/core/signal_quality_evaluator.h"
#include <iostream>

using namespace timing_server::core;

int main() {
    std::cout << "=== 时钟状态机核心功能测试 ===" << std::endl;
    
    // 测试1: 基本状态机创建和初始化
    std::cout << "1. 创建状态机..." << std::endl;
    auto state_machine = std::make_unique<ClockStateMachine>(ClockState::FREE_RUN);
    
    if (!state_machine->Initialize()) {
        std::cerr << "❌ 状态机初始化失败" << std::endl;
        return 1;
    }
    std::cout << "✅ 状态机初始化成功" << std::endl;
    
    if (!state_machine->Start()) {
        std::cerr << "❌ 状态机启动失败" << std::endl;
        return 1;
    }
    std::cout << "✅ 状态机启动成功" << std::endl;
    
    std::cout << "📍 当前状态: " << ClockStateToString(state_machine->GetCurrentState()) << std::endl;
    
    // 测试2: GNSS信号质量评估
    std::cout << "\n2. 测试GNSS信号质量评估..." << std::endl;
    std::string good_gnss_data = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n"
        "$GPGSA,A,3,04,05,09,12,,,,,,,,,1.5,0.9,1.2*39\r\n";
    
    GnssSignalQuality quality = state_machine->EvaluateGnssSignal(good_gnss_data);
    std::cout << "卫星数量: " << quality.satellite_count << std::endl;
    std::cout << "定位质量: " << quality.fix_quality << std::endl;
    std::cout << "PDOP: " << quality.position_dilution << std::endl;
    std::cout << "信号强度: " << quality.signal_strength_dbm << " dBm" << std::endl;
    
    // 测试3: 简单状态转换
    std::cout << "\n3. 测试状态转换..." << std::endl;
    StateMachineEvent event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "测试GNSS信号");
    event.context["gnss_data"] = good_gnss_data;
    
    std::cout << "发送GNSS信号获取事件..." << std::endl;
    TransitionResult result = state_machine->ProcessEvent(event);
    std::cout << "转换结果: " << TransitionResultToString(result) << std::endl;
    std::cout << "当前状态: " << ClockStateToString(state_machine->GetCurrentState()) << std::endl;
    
    // 测试4: 驯服样本添加
    std::cout << "\n4. 测试驯服样本添加..." << std::endl;
    for (int i = 0; i < 5; ++i) {
        double phase_error = 100.0 - i * 15.0;
        double freq_error = 0.1 - i * 0.015;
        state_machine->AddDiscipliningSample(phase_error, freq_error);
        std::cout << "样本 " << (i+1) << ": 相位=" << phase_error << "ns, 频率=" << freq_error << "ppm" << std::endl;
    }
    
    bool converged = state_machine->IsDiscipliningConverged();
    std::cout << "驯服收敛状态: " << (converged ? "已收敛" : "未收敛") << std::endl;
    
    // 测试5: 统计信息
    std::cout << "\n5. 状态机统计信息:" << std::endl;
    std::cout << state_machine->GetStatistics() << std::endl;
    
    // 清理
    state_machine->Stop();
    std::cout << "✅ 测试完成" << std::endl;
    
    return 0;
}