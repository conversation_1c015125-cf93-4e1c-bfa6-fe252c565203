#!/bin/bash

# 高精度授时服务器系统启动包装脚本
# 支持不同的运行模式：守护进程、无头模式、开发模式

set -e

# 默认配置
DEFAULT_CONFIG="/etc/timing-server/config.json"
DEFAULT_LOG_LEVEL="INFO"
DEFAULT_DATA_DIR="/var/lib/timing-server"
DEFAULT_PID_FILE="/var/run/timing-server.pid"

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1" >&2
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1" >&2
}

print_error() {
    echo -e "${RED}[错误]${NC} $1" >&2
}

# 显示使用说明
show_usage() {
    echo "高精度授时服务器系统启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE       配置文件路径 (默认: $DEFAULT_CONFIG)"
    echo "  -d, --daemon            以守护进程模式运行"
    echo "  -h, --headless          以无头模式运行（无Web界面）"
    echo "  -l, --log-level LEVEL   日志级别 (TRACE|DEBUG|INFO|WARNING|ERROR|CRITICAL)"
    echo "  -p, --pid-file FILE     PID文件路径 (默认: $DEFAULT_PID_FILE)"
    echo "  -D, --data-dir DIR      数据目录路径 (默认: $DEFAULT_DATA_DIR)"
    echo "  -u, --user USER         运行用户 (仅root可用)"
    echo "  -g, --group GROUP       运行组 (仅root可用)"
    echo "  -v, --version           显示版本信息"
    echo "  --help                  显示此帮助信息"
    echo ""
    echo "运行模式:"
    echo "  正常模式    - 完整功能，包含Web界面"
    echo "  守护进程模式 - 后台运行，分离控制终端"
    echo "  无头模式    - 仅核心功能，禁用Web界面，适合嵌入式环境"
    echo ""
    echo "示例:"
    echo "  $0                                    # 正常模式运行"
    echo "  $0 --daemon                           # 守护进程模式"
    echo "  $0 --headless                         # 无头模式"
    echo "  $0 --config /path/to/config.json     # 指定配置文件"
    echo "  $0 --daemon --log-level DEBUG         # 守护进程+调试日志"
}

# 解析命令行参数
parse_arguments() {
    CONFIG_FILE="$DEFAULT_CONFIG"
    LOG_LEVEL="$DEFAULT_LOG_LEVEL"
    DATA_DIR="$DEFAULT_DATA_DIR"
    PID_FILE="$DEFAULT_PID_FILE"
    DAEMON_MODE=false
    HEADLESS_MODE=false
    RUN_USER=""
    RUN_GROUP=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -d|--daemon)
                DAEMON_MODE=true
                shift
                ;;
            -h|--headless)
                HEADLESS_MODE=true
                shift
                ;;
            -l|--log-level)
                LOG_LEVEL="$2"
                shift 2
                ;;
            -p|--pid-file)
                PID_FILE="$2"
                shift 2
                ;;
            -D|--data-dir)
                DATA_DIR="$2"
                shift 2
                ;;
            -u|--user)
                RUN_USER="$2"
                shift 2
                ;;
            -g|--group)
                RUN_GROUP="$2"
                shift 2
                ;;
            -v|--version)
                exec /usr/local/bin/timing-server --version
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 验证参数
validate_arguments() {
    # 检查配置文件
    if [[ ! -f "$CONFIG_FILE" ]]; then
        print_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    # 检查数据目录
    if [[ ! -d "$DATA_DIR" ]]; then
        print_error "数据目录不存在: $DATA_DIR"
        exit 1
    fi
    
    # 验证日志级别
    case $LOG_LEVEL in
        TRACE|DEBUG|INFO|WARNING|ERROR|CRITICAL)
            ;;
        *)
            print_error "无效的日志级别: $LOG_LEVEL"
            print_info "有效的日志级别: TRACE, DEBUG, INFO, WARNING, ERROR, CRITICAL"
            exit 1
            ;;
    esac
    
    # 检查用户权限
    if [[ -n "$RUN_USER" || -n "$RUN_GROUP" ]]; then
        if [[ $EUID -ne 0 ]]; then
            print_error "指定运行用户/组需要root权限"
            exit 1
        fi
    fi
}

# 设置环境变量
setup_environment() {
    export TIMING_SERVER_CONFIG="$CONFIG_FILE"
    export TIMING_SERVER_LOG_LEVEL="$LOG_LEVEL"
    export TIMING_SERVER_DATA_DIR="$DATA_DIR"
    export TIMING_SERVER_PID_FILE="$PID_FILE"
    
    if [[ "$HEADLESS_MODE" == "true" ]]; then
        export TIMING_SERVER_HEADLESS=1
        export TIMING_SERVER_DISABLE_WEB=1
    fi
    
    if [[ "$DAEMON_MODE" == "true" ]]; then
        export TIMING_SERVER_DAEMON=1
    fi
    
    # 设置时区为UTC（授时服务器标准）
    export TZ=UTC
    
    # 设置内存锁定（如果有权限）
    if [[ $EUID -eq 0 ]] || groups | grep -q timing-server; then
        ulimit -l unlimited 2>/dev/null || print_warning "无法设置内存锁定限制"
    fi
    
    # 设置文件描述符限制
    ulimit -n 65536 2>/dev/null || print_warning "无法设置文件描述符限制"
}

# 检查系统资源
check_system_resources() {
    # 检查内存
    local available_memory=$(free -m | awk '/^Mem:/{print $7}')
    if [[ $available_memory -lt 64 ]]; then
        print_warning "可用内存不足64MB，系统可能运行缓慢"
    fi
    
    # 检查磁盘空间
    local available_disk=$(df "$DATA_DIR" | awk 'NR==2{print $4}')
    if [[ $available_disk -lt 1048576 ]]; then  # 1GB in KB
        print_warning "数据目录可用空间不足1GB"
    fi
    
    # 检查CPU负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    if (( $(echo "$load_avg > 2.0" | bc -l) )); then
        print_warning "系统负载较高: $load_avg"
    fi
}

# 创建PID文件
create_pid_file() {
    if [[ "$DAEMON_MODE" == "true" ]]; then
        local pid_dir=$(dirname "$PID_FILE")
        if [[ ! -d "$pid_dir" ]]; then
            mkdir -p "$pid_dir"
        fi
        
        # 检查是否已有进程在运行
        if [[ -f "$PID_FILE" ]]; then
            local old_pid=$(cat "$PID_FILE")
            if kill -0 "$old_pid" 2>/dev/null; then
                print_error "进程已在运行 (PID: $old_pid)"
                exit 1
            else
                print_warning "删除过期的PID文件: $PID_FILE"
                rm -f "$PID_FILE"
            fi
        fi
        
        echo $$ > "$PID_FILE"
    fi
}

# 清理函数
cleanup() {
    if [[ -f "$PID_FILE" ]]; then
        rm -f "$PID_FILE"
    fi
}

# 信号处理
setup_signal_handlers() {
    trap cleanup EXIT
    trap 'print_info "收到SIGTERM信号，正在关闭..."; cleanup; exit 0' TERM
    trap 'print_info "收到SIGINT信号，正在关闭..."; cleanup; exit 0' INT
    trap 'print_info "收到SIGHUP信号，重新加载配置..."; kill -HUP $MAIN_PID 2>/dev/null || true' HUP
}

# 切换用户
switch_user() {
    if [[ -n "$RUN_USER" ]]; then
        if [[ $EUID -eq 0 ]]; then
            print_info "切换到用户: $RUN_USER"
            if [[ -n "$RUN_GROUP" ]]; then
                exec su -s "$0" "$RUN_USER" -g "$RUN_GROUP" -- "$@"
            else
                exec su -s "$0" "$RUN_USER" -- "$@"
            fi
        else
            print_warning "非root用户无法切换用户"
        fi
    fi
}

# 启动主程序
start_main_program() {
    local args=()
    
    # 基本参数
    args+=("--config" "$CONFIG_FILE")
    args+=("--log-level" "$LOG_LEVEL")
    args+=("--data-dir" "$DATA_DIR")
    
    # 模式参数
    if [[ "$DAEMON_MODE" == "true" ]]; then
        args+=("--daemon")
    fi
    
    if [[ "$HEADLESS_MODE" == "true" ]]; then
        args+=("--headless")
    fi
    
    # 启动主程序
    print_info "启动高精度授时服务器系统..."
    print_info "配置文件: $CONFIG_FILE"
    print_info "日志级别: $LOG_LEVEL"
    print_info "数据目录: $DATA_DIR"
    print_info "运行模式: $([ "$DAEMON_MODE" == "true" ] && echo "守护进程" || echo "前台") $([ "$HEADLESS_MODE" == "true" ] && echo "+ 无头模式" || echo "")"
    
    exec /usr/local/bin/timing-server "${args[@]}"
}

# 主程序
main() {
    parse_arguments "$@"
    validate_arguments
    switch_user "$@"
    setup_environment
    check_system_resources
    setup_signal_handlers
    create_pid_file
    start_main_program
}

# 执行主程序
main "$@"