[Unit]
Description=高精度授时服务器系统 (High-Precision Timing Server System)
Documentation=https://github.com/timing-server/timing-server-system
After=network.target time-sync.target multi-user.target
Wants=network.target
Requires=time-sync.target

[Service]
Type=notify
User=timing-server
Group=timing-server

# 主程序启动命令 - 支持无头模式
ExecStart=/usr/local/bin/timing-server --config /etc/timing-server/config.json --daemon --headless
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID

# 重启策略 - 确保高可用性
Restart=always
RestartSec=10
RestartPreventExitStatus=SIGKILL

# 超时设置
TimeoutStartSec=60
TimeoutStopSec=30
TimeoutAbortSec=10

# 工作目录和环境
WorkingDirectory=/var/lib/timing-server
Environment=TZ=UTC
Environment=TIMING_SERVER_HEADLESS=1
Environment=TIMING_SERVER_LOG_LEVEL=INFO

# 标准输入输出重定向（无头模式）
StandardInput=null
StandardOutput=journal
StandardError=journal

# 安全设置 - 增强的安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ProtectHostname=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectKernelLogs=true
ProtectControlGroups=true
ProtectProc=invisible
ProcSubset=pid

# 文件系统访问权限
ReadWritePaths=/var/lib/timing-server /var/log/timing-server
ReadOnlyPaths=/etc/timing-server

# 硬件设备访问权限 - 根据实际硬件配置调整
DeviceAllow=/dev/pps0 rw
DeviceAllow=/dev/pps1 rw
DeviceAllow=/dev/ttyS0 rw
DeviceAllow=/dev/ttyUSB0 rw
DeviceAllow=/dev/rtc0 rw
DeviceAllow=/dev/spidev0.0 rw
DeviceAllow=char-pps rw
DeviceAllow=char-rtc rw

# 网络设置
PrivateNetwork=false
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX AF_NETLINK

# 内存和进程限制
PrivateTmp=true
PrivateDevices=false
PrivateUsers=false
DynamicUser=false

# 系统调用过滤
SystemCallFilter=@system-service
SystemCallFilter=~@debug @mount @cpu-emulation @obsolete @privileged @reboot @swap
SystemCallErrorNumber=EPERM

# 资源限制 - 针对嵌入式环境优化
LimitNOFILE=65536
LimitMEMLOCK=infinity
LimitCORE=0
LimitNPROC=1024
LimitAS=134217728  # 128MB虚拟内存限制
LimitDATA=67108864 # 64MB数据段限制
LimitSTACK=8388608 # 8MB栈限制

# 实时调度优先级 - 确保时间关键任务优先级
Nice=-10
IOSchedulingClass=1
IOSchedulingPriority=4
CPUSchedulingPolicy=1
CPUSchedulingPriority=50

# 内存优化设置
MemoryDenyWriteExecute=true
LockPersonality=true

# 能力设置 - 最小权限原则
CapabilityBoundingSet=CAP_SYS_TIME CAP_SYS_NICE CAP_IPC_LOCK CAP_NET_BIND_SERVICE CAP_DAC_READ_SEARCH
AmbientCapabilities=CAP_SYS_TIME CAP_SYS_NICE CAP_IPC_LOCK CAP_NET_BIND_SERVICE

# 看门狗设置 - 系统健康监控
WatchdogSec=30
NotifyAccess=main

# 服务依赖和启动顺序
Requisite=network.target
BindsTo=time-sync.target

[Install]
WantedBy=multi-user.target
Alias=timing-server.service