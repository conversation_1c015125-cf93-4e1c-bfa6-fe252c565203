#!/bin/bash

# 高精度授时服务器系统安装脚本
# 支持自动硬件检测、用户创建、服务配置和系统集成

set -e  # 遇到错误立即退出

# 脚本参数
INSTALL_MODE=${1:-"install"}
FORCE_INSTALL=${2:-"false"}

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "高精度授时服务器系统安装脚本"
    echo ""
    echo "用法: $0 [模式] [强制安装]"
    echo ""
    echo "模式选项:"
    echo "  install     - 安装系统（默认）"
    echo "  uninstall   - 卸载系统"
    echo "  upgrade     - 升级系统"
    echo "  status      - 查看安装状态"
    echo ""
    echo "强制安装:"
    echo "  true        - 强制安装，覆盖现有配置"
    echo "  false       - 保护现有配置（默认）"
    echo ""
    echo "示例:"
    echo "  $0 install              # 标准安装"
    echo "  $0 install true         # 强制安装"
    echo "  $0 uninstall            # 卸载系统"
    echo "  $0 upgrade              # 升级系统"
    echo "  $0 status               # 查看状态"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "此脚本需要root权限运行"
        print_info "请使用: sudo $0 $*"
        exit 1
    fi
}

# 检测系统平台
detect_platform() {
    local os_name=$(uname -s)
    local arch=$(uname -m)
    
    case $os_name in
        "Linux")
            if [[ "$arch" == "loongarch64" ]]; then
                PLATFORM="loongarch64-linux"
                print_info "检测到龙芯LoongArch64平台"
            elif [[ "$arch" == "x86_64" ]]; then
                PLATFORM="x86_64-linux"
                print_info "检测到Linux x86_64平台"
            else
                print_error "不支持的架构: $arch"
                exit 1
            fi
            ;;
        *)
            print_error "不支持的操作系统: $os_name"
            print_info "此安装脚本仅支持Linux系统"
            exit 1
            ;;
    esac
}

# 检测Linux发行版
detect_distro() {
    if [[ -f /etc/os-release ]]; then
        source /etc/os-release
        DISTRO=$ID
        DISTRO_VERSION=$VERSION_ID
        print_info "检测到发行版: $PRETTY_NAME"
    else
        print_warning "无法检测Linux发行版，使用通用配置"
        DISTRO="unknown"
        DISTRO_VERSION="unknown"
    fi
}

# 检查系统依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查必需的命令
    local required_commands=("systemctl" "useradd" "groupadd" "chmod" "chown")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    # 检查必需的库文件
    local required_libs=("librt.so" "libpthread.so" "libsqlite3.so")
    
    for lib in "${required_libs[@]}"; do
        if ! ldconfig -p | grep -q "$lib"; then
            missing_deps+=("$lib")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少以下依赖:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        print_info "请安装缺少的依赖后重新运行安装脚本"
        exit 1
    fi
    
    print_success "系统依赖检查通过"
}

# 硬件检测
detect_hardware() {
    print_info "检测硬件设备..."
    
    # 检测串口设备（GNSS接收机）
    GNSS_DEVICES=()
    for device in /dev/ttyS* /dev/ttyUSB* /dev/ttyACM*; do
        if [[ -c "$device" ]]; then
            GNSS_DEVICES+=("$device")
        fi
    done
    
    # 检测PPS设备
    PPS_DEVICES=()
    for device in /dev/pps*; do
        if [[ -c "$device" ]]; then
            PPS_DEVICES+=("$device")
        fi
    done
    
    # 检测RTC设备
    RTC_DEVICES=()
    for device in /dev/rtc*; do
        if [[ -c "$device" ]]; then
            RTC_DEVICES+=("$device")
        fi
    done
    
    # 检测网络接口和PHC
    NETWORK_INTERFACES=()
    PHC_DEVICES=()
    for iface in /sys/class/net/*; do
        if [[ -d "$iface" ]]; then
            iface_name=$(basename "$iface")
            if [[ "$iface_name" != "lo" ]]; then
                NETWORK_INTERFACES+=("$iface_name")
                
                # 检查是否支持PTP硬件时钟
                if [[ -f "/sys/class/net/$iface_name/device/ptp" ]]; then
                    ptp_device=$(ls /sys/class/net/$iface_name/device/ptp/ 2>/dev/null | head -n1)
                    if [[ -n "$ptp_device" ]]; then
                        PHC_DEVICES+=("/dev/$ptp_device")
                    fi
                fi
            fi
        fi
    done
    
    # 显示检测结果
    print_info "硬件检测结果:"
    echo "  GNSS设备: ${GNSS_DEVICES[*]:-无}"
    echo "  PPS设备: ${PPS_DEVICES[*]:-无}"
    echo "  RTC设备: ${RTC_DEVICES[*]:-无}"
    echo "  网络接口: ${NETWORK_INTERFACES[*]:-无}"
    echo "  PHC设备: ${PHC_DEVICES[*]:-无}"
}

# 创建系统用户和组
create_system_user() {
    print_info "创建系统用户和组..."
    
    # 创建timing-server组
    if ! getent group timing-server &>/dev/null; then
        groupadd --system timing-server
        print_success "创建系统组: timing-server"
    else
        print_info "系统组已存在: timing-server"
    fi
    
    # 创建timing-server用户
    if ! getent passwd timing-server &>/dev/null; then
        useradd --system \
                --gid timing-server \
                --home-dir /var/lib/timing-server \
                --shell /bin/false \
                --comment "Timing Server System User" \
                timing-server
        print_success "创建系统用户: timing-server"
    else
        print_info "系统用户已存在: timing-server"
    fi
    
    # 将用户添加到必要的组以访问硬件设备
    local hardware_groups=("dialout" "tty" "audio" "video")
    for group in "${hardware_groups[@]}"; do
        if getent group "$group" &>/dev/null; then
            usermod -a -G "$group" timing-server
            print_info "将用户timing-server添加到组: $group"
        fi
    done
}

# 创建目录结构
create_directories() {
    print_info "创建目录结构..."
    
    # 定义目录列表
    local directories=(
        "/etc/timing-server"
        "/etc/timing-server/ssl"
        "/var/lib/timing-server"
        "/var/lib/timing-server/data"
        "/var/lib/timing-server/learning"
        "/var/log/timing-server"
        "/usr/local/bin"
        "/usr/local/share/timing-server"
        "/usr/local/share/timing-server/web"
    )
    
    # 创建目录
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            print_success "创建目录: $dir"
        else
            print_info "目录已存在: $dir"
        fi
    done
    
    # 设置目录权限
    chown -R timing-server:timing-server /var/lib/timing-server
    chown -R timing-server:timing-server /var/log/timing-server
    chown -R root:timing-server /etc/timing-server
    
    # 设置目录权限模式
    chmod 755 /etc/timing-server
    chmod 750 /etc/timing-server/ssl
    chmod 755 /var/lib/timing-server
    chmod 755 /var/log/timing-server
    
    print_success "目录权限设置完成"
}

# 安装二进制文件
install_binaries() {
    print_info "安装二进制文件..."
    
    # 获取脚本所在目录
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local project_root="$(dirname "$script_dir")"
    
    # 查找构建目录
    local build_dir=""
    for dir in "$project_root"/build-*-Release; do
        if [[ -d "$dir" && -f "$dir/timing-server" ]]; then
            build_dir="$dir"
            break
        fi
    done
    
    if [[ -z "$build_dir" ]]; then
        print_error "未找到构建的二进制文件"
        print_info "请先运行构建脚本: ./build_scripts/build.sh"
        exit 1
    fi
    
    print_info "从构建目录安装: $build_dir"
    
    # 安装主程序
    if [[ -f "$build_dir/timing-server" ]]; then
        cp "$build_dir/timing-server" /usr/local/bin/
        chmod 755 /usr/local/bin/timing-server
        chown root:root /usr/local/bin/timing-server
        print_success "安装主程序: /usr/local/bin/timing-server"
    else
        print_error "未找到主程序文件: $build_dir/timing-server"
        exit 1
    fi
    
    # 安装示例程序（可选）
    local examples=("hal-demo" "rest-api-example" "websocket-server-example")
    for example in "${examples[@]}"; do
        if [[ -f "$build_dir/$example" ]]; then
            cp "$build_dir/$example" /usr/local/bin/
            chmod 755 "/usr/local/bin/$example"
            chown root:root "/usr/local/bin/$example"
            print_info "安装示例程序: /usr/local/bin/$example"
        fi
    done
}

# 生成配置文件
generate_config() {
    print_info "生成配置文件..."
    
    local config_file="/etc/timing-server/config.json"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local template_file="$script_dir/config/config.json"
    
    # 如果配置文件已存在且不是强制安装，则备份
    if [[ -f "$config_file" && "$FORCE_INSTALL" != "true" ]]; then
        local backup_file="$config_file.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$config_file" "$backup_file"
        print_info "备份现有配置: $backup_file"
    fi
    
    # 复制模板配置文件
    if [[ -f "$template_file" ]]; then
        cp "$template_file" "$config_file"
    else
        # 如果模板不存在，生成基本配置
        cat > "$config_file" << 'EOF'
{
  "system": {
    "log_level": "INFO",
    "data_dir": "/var/lib/timing-server",
    "pid_file": "/var/run/timing-server.pid",
    "user": "timing-server",
    "group": "timing-server"
  },
  "timing": {
    "sources": {
      "gnss": {
        "enabled": true,
        "device": "/dev/ttyS0",
        "baudrate": 9600,
        "priority": 1
      },
      "rubidium": {
        "enabled": false,
        "device": "/dev/spidev0.0",
        "priority": 2
      },
      "rtc": {
        "enabled": true,
        "device": "/dev/rtc0",
        "priority": 5
      }
    }
  },
  "api": {
    "http_port": 8080,
    "https_port": 8443,
    "websocket_port": 8081,
    "bind_address": "0.0.0.0"
  }
}
EOF
    fi
    
    # 根据硬件检测结果调整配置
    if [[ ${#GNSS_DEVICES[@]} -gt 0 ]]; then
        # 使用检测到的第一个GNSS设备
        sed -i "s|/dev/ttyS0|${GNSS_DEVICES[0]}|g" "$config_file"
        print_info "配置GNSS设备: ${GNSS_DEVICES[0]}"
    fi
    
    if [[ ${#RTC_DEVICES[@]} -gt 0 ]]; then
        # 使用检测到的第一个RTC设备
        sed -i "s|/dev/rtc0|${RTC_DEVICES[0]}|g" "$config_file"
        print_info "配置RTC设备: ${RTC_DEVICES[0]}"
    fi
    
    # 设置配置文件权限
    chown root:timing-server "$config_file"
    chmod 640 "$config_file"
    
    print_success "配置文件生成完成: $config_file"
}

# 安装systemd服务
install_systemd_service() {
    print_info "安装systemd服务..."
    
    local service_file="/etc/systemd/system/timing-server.service"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local template_file="$script_dir/systemd/timing-server.service"
    
    # 复制服务文件
    if [[ -f "$template_file" ]]; then
        cp "$template_file" "$service_file"
        print_success "安装服务文件: $service_file"
    else
        print_error "未找到服务模板文件: $template_file"
        exit 1
    fi
    
    # 设置服务文件权限
    chown root:root "$service_file"
    chmod 644 "$service_file"
    
    # 重新加载systemd配置
    systemctl daemon-reload
    print_success "重新加载systemd配置"
    
    # 启用服务（但不立即启动）
    systemctl enable timing-server.service
    print_success "启用timing-server服务"
}

# 配置防火墙
configure_firewall() {
    print_info "配置防火墙规则..."
    
    # 检查防火墙类型
    if command -v ufw &> /dev/null && ufw status | grep -q "Status: active"; then
        # Ubuntu/Debian UFW
        print_info "检测到UFW防火墙，配置规则..."
        ufw allow 8080/tcp comment "Timing Server HTTP API"
        ufw allow 8443/tcp comment "Timing Server HTTPS API"
        ufw allow 8081/tcp comment "Timing Server WebSocket"
        ufw allow 123/udp comment "NTP Service"
        print_success "UFW防火墙规则配置完成"
        
    elif command -v firewall-cmd &> /dev/null && systemctl is-active firewalld &> /dev/null; then
        # CentOS/RHEL/Fedora firewalld
        print_info "检测到firewalld防火墙，配置规则..."
        firewall-cmd --permanent --add-port=8080/tcp --add-port=8443/tcp --add-port=8081/tcp
        firewall-cmd --permanent --add-service=ntp
        firewall-cmd --reload
        print_success "firewalld防火墙规则配置完成"
        
    elif command -v iptables &> /dev/null; then
        # 传统iptables
        print_info "检测到iptables防火墙，配置规则..."
        iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
        iptables -A INPUT -p tcp --dport 8443 -j ACCEPT
        iptables -A INPUT -p tcp --dport 8081 -j ACCEPT
        iptables -A INPUT -p udp --dport 123 -j ACCEPT
        
        # 保存iptables规则（根据发行版不同）
        if command -v iptables-save &> /dev/null; then
            iptables-save > /etc/iptables/rules.v4 2>/dev/null || \
            iptables-save > /etc/sysconfig/iptables 2>/dev/null || \
            print_warning "无法保存iptables规则，重启后可能失效"
        fi
        print_success "iptables防火墙规则配置完成"
        
    else
        print_warning "未检测到支持的防火墙，请手动配置以下端口:"
        echo "  - TCP 8080 (HTTP API)"
        echo "  - TCP 8443 (HTTPS API)"
        echo "  - TCP 8081 (WebSocket)"
        echo "  - UDP 123 (NTP)"
    fi
}

# 安装Web界面
install_web_interface() {
    print_info "安装Web管理界面..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local project_root="$(dirname "$script_dir")"
    local frontend_dir="$project_root/frontend"
    local web_install_dir="/usr/local/share/timing-server/web"
    
    # 检查前端构建文件
    if [[ -d "$frontend_dir/dist" ]]; then
        print_info "安装前端构建文件..."
        cp -r "$frontend_dir/dist"/* "$web_install_dir/"
        chown -R root:root "$web_install_dir"
        chmod -R 644 "$web_install_dir"
        find "$web_install_dir" -type d -exec chmod 755 {} \;
        print_success "Web界面安装完成"
    else
        print_warning "未找到前端构建文件，跳过Web界面安装"
        print_info "请运行以下命令构建前端:"
        echo "  cd $frontend_dir"
        echo "  npm install"
        echo "  npm run build"
    fi
}

# 执行安装
perform_install() {
    print_info "开始安装高精度授时服务器系统..."
    
    detect_platform
    detect_distro
    check_dependencies
    detect_hardware
    create_system_user
    create_directories
    install_binaries
    generate_config
    install_systemd_service
    configure_firewall
    install_web_interface
    
    print_success "安装完成！"
    print_info ""
    print_info "后续步骤:"
    echo "  1. 检查配置文件: /etc/timing-server/config.json"
    echo "  2. 启动服务: sudo systemctl start timing-server"
    echo "  3. 查看状态: sudo systemctl status timing-server"
    echo "  4. 查看日志: sudo journalctl -u timing-server -f"
    echo "  5. 访问Web界面: http://localhost:8080"
    print_info ""
    print_info "配置建议:"
    echo "  - 根据实际硬件调整 /etc/timing-server/config.json"
    echo "  - 配置HTTPS证书以启用安全连接"
    echo "  - 设置适当的防火墙规则"
    echo "  - 定期检查系统日志和性能指标"
}

# 执行卸载
perform_uninstall() {
    print_info "开始卸载高精度授时服务器系统..."
    
    # 停止并禁用服务
    if systemctl is-active timing-server &>/dev/null; then
        systemctl stop timing-server
        print_info "停止timing-server服务"
    fi
    
    if systemctl is-enabled timing-server &>/dev/null; then
        systemctl disable timing-server
        print_info "禁用timing-server服务"
    fi
    
    # 删除服务文件
    if [[ -f /etc/systemd/system/timing-server.service ]]; then
        rm -f /etc/systemd/system/timing-server.service
        systemctl daemon-reload
        print_info "删除systemd服务文件"
    fi
    
    # 删除二进制文件
    local binaries=("timing-server" "hal-demo" "rest-api-example" "websocket-server-example")
    for binary in "${binaries[@]}"; do
        if [[ -f "/usr/local/bin/$binary" ]]; then
            rm -f "/usr/local/bin/$binary"
            print_info "删除二进制文件: $binary"
        fi
    done
    
    # 询问是否删除配置和数据
    read -p "是否删除配置文件和数据目录? [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf /etc/timing-server
        rm -rf /var/lib/timing-server
        rm -rf /var/log/timing-server
        rm -rf /usr/local/share/timing-server
        print_info "删除配置和数据目录"
    else
        print_info "保留配置和数据目录"
    fi
    
    # 询问是否删除系统用户
    read -p "是否删除系统用户timing-server? [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if getent passwd timing-server &>/dev/null; then
            userdel timing-server
            print_info "删除系统用户: timing-server"
        fi
        if getent group timing-server &>/dev/null; then
            groupdel timing-server
            print_info "删除系统组: timing-server"
        fi
    else
        print_info "保留系统用户和组"
    fi
    
    print_success "卸载完成！"
}

# 显示安装状态
show_status() {
    print_info "高精度授时服务器系统状态:"
    echo ""
    
    # 检查二进制文件
    if [[ -f /usr/local/bin/timing-server ]]; then
        local version=$(/usr/local/bin/timing-server --version 2>/dev/null || echo "未知")
        print_success "二进制文件: 已安装 (版本: $version)"
    else
        print_error "二进制文件: 未安装"
    fi
    
    # 检查配置文件
    if [[ -f /etc/timing-server/config.json ]]; then
        print_success "配置文件: 已安装"
    else
        print_error "配置文件: 未安装"
    fi
    
    # 检查systemd服务
    if [[ -f /etc/systemd/system/timing-server.service ]]; then
        print_success "systemd服务: 已安装"
        
        # 检查服务状态
        if systemctl is-enabled timing-server &>/dev/null; then
            print_success "服务启用: 是"
        else
            print_warning "服务启用: 否"
        fi
        
        if systemctl is-active timing-server &>/dev/null; then
            print_success "服务运行: 是"
        else
            print_warning "服务运行: 否"
        fi
    else
        print_error "systemd服务: 未安装"
    fi
    
    # 检查系统用户
    if getent passwd timing-server &>/dev/null; then
        print_success "系统用户: 已创建"
    else
        print_error "系统用户: 未创建"
    fi
    
    # 检查目录
    local directories=("/etc/timing-server" "/var/lib/timing-server" "/var/log/timing-server")
    for dir in "${directories[@]}"; do
        if [[ -d "$dir" ]]; then
            print_success "目录 $dir: 存在"
        else
            print_error "目录 $dir: 不存在"
        fi
    done
}

# 主程序逻辑
main() {
    case $INSTALL_MODE in
        "install")
            check_root
            perform_install
            ;;
        "uninstall")
            check_root
            perform_uninstall
            ;;
        "upgrade")
            check_root
            print_info "执行升级安装..."
            FORCE_INSTALL="false"  # 升级时保护现有配置
            perform_install
            ;;
        "status")
            show_status
            ;;
        *)
            print_error "未知模式: $INSTALL_MODE"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"