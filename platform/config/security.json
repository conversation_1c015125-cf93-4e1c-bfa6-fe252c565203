{"security": {"enabled": true, "rate_limit": {"max_requests": 60, "time_window_seconds": 60, "burst_limit": 10, "block_duration_seconds": 300, "enable_progressive_delay": true}, "tls": {"cert_file": "/etc/timing-server/ssl/server.crt", "key_file": "/etc/timing-server/ssl/server.key", "ca_file": "/etc/timing-server/ssl/ca.crt", "min_tls_version": "1.2", "require_client_cert": false, "enable_ocsp_stapling": false, "cipher_suites": ["ECDHE-RSA-AES256-GCM-SHA384", "ECDHE-RSA-AES128-GCM-SHA256", "ECDHE-ECDSA-AES256-GCM-SHA384", "ECDHE-ECDSA-AES128-GCM-SHA256", "ECDHE-RSA-AES256-SHA384", "ECDHE-RSA-AES128-SHA256"]}, "access_control": {"whitelist": ["127.0.0.1", "::1", "***********/16", "10.0.0.0/8"], "blacklist": [], "default_policy": "allow"}, "attack_detection": {"brute_force_threshold": 10, "brute_force_window_seconds": 300, "suspicious_user_agents": ["bot", "crawler", "spider", "scanner", "sqlmap", "nikto", "nmap"], "auto_block_duration_seconds": 3600}, "monitoring": {"log_security_events": true, "alert_on_critical_events": true, "max_events_in_memory": 5000, "event_retention_days": 30}}}