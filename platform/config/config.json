{"system": {"log_level": "INFO", "data_dir": "/var/lib/timing-server", "pid_file": "/var/run/timing-server.pid", "user": "timing-server", "group": "timing-server"}, "timing": {"sources": {"gnss": {"enabled": true, "device": "/dev/ttyS0", "baudrate": 9600, "priority": 1, "timeout_ms": 5000}, "rubidium": {"enabled": true, "device": "/dev/spidev0.0", "priority": 2, "temperature_compensation": true}, "rtc": {"enabled": true, "device": "/dev/rtc0", "priority": 5}, "external_pps": {"enabled": false, "device": "/dev/pps1", "priority": 3}, "external_10mhz": {"enabled": false, "device": "/dev/pps2", "priority": 4}}, "disciplining": {"convergence_threshold_ns": 50.0, "convergence_time_s": 300, "phase_gain": 0.1, "frequency_gain": 0.01, "measurement_interval_ms": 1000}, "holdover": {"max_holdover_hours": 24, "frequency_drift_limit_ppm": 1.0, "learning_duration_hours": 72, "enable_temperature_compensation": true}}, "api": {"http_port": 8080, "https_port": 8443, "websocket_port": 8081, "bind_address": "0.0.0.0", "max_connections": 100, "request_timeout_ms": 30000}, "security": {"jwt_secret": "your-secret-key-change-in-production", "session_timeout_minutes": 30, "require_https": false, "allowed_ips": ["127.0.0.1", "***********/24"], "max_requests_per_minute": 60}, "alarms": {"phase_offset_warning_ns": 100.0, "phase_offset_critical_ns": 500.0, "frequency_offset_warning_ppm": 0.01, "frequency_offset_critical_ppm": 0.1, "gnss_satellites_warning": 6, "gnss_satellites_critical": 4, "gnss_snr_warning_db": -145.0, "gnss_snr_critical_db": -150.0, "cpu_usage_warning": 80.0, "memory_usage_warning": 80.0, "temperature_warning": 70.0, "temperature_critical": 75.0}}