#!/bin/bash

# 高精度授时服务器系统监控脚本
# 用于系统健康检查、资源监控和性能优化

set -e

# 默认配置
DEFAULT_CONFIG="/etc/timing-server/config.json"
DEFAULT_LOG_FILE="/var/log/timing-server/monitor.log"
DEFAULT_CHECK_INTERVAL=60
DEFAULT_ALERT_THRESHOLD_CPU=80
DEFAULT_ALERT_THRESHOLD_MEMORY=80
DEFAULT_ALERT_THRESHOLD_DISK=85

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 记录日志
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# 显示使用说明
show_usage() {
    echo "高精度授时服务器系统监控脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  status          显示系统状态"
    echo "  monitor         持续监控系统"
    echo "  check           执行一次健康检查"
    echo "  optimize        执行系统优化"
    echo "  report          生成系统报告"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE       配置文件路径"
    echo "  -l, --log-file FILE     日志文件路径"
    echo "  -i, --interval SECONDS  监控间隔（秒）"
    echo "  -v, --verbose           详细输出"
    echo "  --help                  显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status                           # 显示系统状态"
    echo "  $0 monitor --interval 30            # 每30秒监控一次"
    echo "  $0 check --verbose                  # 详细健康检查"
    echo "  $0 optimize                         # 执行系统优化"
}

# 解析命令行参数
parse_arguments() {
    COMMAND=""
    CONFIG_FILE="$DEFAULT_CONFIG"
    LOG_FILE="$DEFAULT_LOG_FILE"
    CHECK_INTERVAL="$DEFAULT_CHECK_INTERVAL"
    VERBOSE=false
    
    if [[ $# -eq 0 ]]; then
        COMMAND="status"
        return
    fi
    
    COMMAND="$1"
    shift
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -l|--log-file)
                LOG_FILE="$2"
                shift 2
                ;;
            -i|--interval)
                CHECK_INTERVAL="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 检查timing-server服务状态
check_service_status() {
    local status="未知"
    local pid=""
    local uptime=""
    
    if systemctl is-active timing-server &>/dev/null; then
        status="运行中"
        pid=$(systemctl show timing-server --property=MainPID --value)
        if [[ -n "$pid" && "$pid" != "0" ]]; then
            uptime=$(ps -o etime= -p "$pid" 2>/dev/null | tr -d ' ' || echo "未知")
        fi
    elif systemctl is-enabled timing-server &>/dev/null; then
        status="已停止"
    else
        status="未安装"
    fi
    
    echo "服务状态: $status"
    if [[ -n "$pid" && "$pid" != "0" ]]; then
        echo "进程ID: $pid"
        echo "运行时间: $uptime"
    fi
}

# 检查系统资源使用情况
check_system_resources() {
    # CPU使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    cpu_usage=${cpu_usage%.*}  # 去掉小数部分
    
    # 内存使用情况
    local memory_info=$(free -m)
    local total_memory=$(echo "$memory_info" | awk '/^Mem:/{print $2}')
    local used_memory=$(echo "$memory_info" | awk '/^Mem:/{print $3}')
    local memory_usage=$((used_memory * 100 / total_memory))
    
    # 磁盘使用情况
    local disk_usage=$(df /var/lib/timing-server 2>/dev/null | awk 'NR==2{print $5}' | sed 's/%//' || echo "0")
    
    # 系统负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    echo "系统资源使用情况:"
    echo "  CPU使用率: ${cpu_usage}%"
    echo "  内存使用率: ${memory_usage}% (${used_memory}MB/${total_memory}MB)"
    echo "  磁盘使用率: ${disk_usage}%"
    echo "  系统负载: $load_avg"
    
    # 检查告警阈值
    local alerts=()
    if [[ $cpu_usage -gt $DEFAULT_ALERT_THRESHOLD_CPU ]]; then
        alerts+=("CPU使用率过高: ${cpu_usage}%")
    fi
    if [[ $memory_usage -gt $DEFAULT_ALERT_THRESHOLD_MEMORY ]]; then
        alerts+=("内存使用率过高: ${memory_usage}%")
    fi
    if [[ $disk_usage -gt $DEFAULT_ALERT_THRESHOLD_DISK ]]; then
        alerts+=("磁盘使用率过高: ${disk_usage}%")
    fi
    
    if [[ ${#alerts[@]} -gt 0 ]]; then
        echo ""
        print_warning "资源告警:"
        for alert in "${alerts[@]}"; do
            echo "  - $alert"
            log_message "WARNING" "$alert"
        done
    fi
}

# 检查硬件设备状态
check_hardware_status() {
    echo "硬件设备状态:"
    
    # 检查GNSS设备
    local gnss_devices=()
    for device in /dev/ttyS* /dev/ttyUSB* /dev/ttyACM*; do
        if [[ -c "$device" ]]; then
            gnss_devices+=("$device")
        fi
    done
    echo "  GNSS设备: ${gnss_devices[*]:-无}"
    
    # 检查PPS设备
    local pps_devices=()
    for device in /dev/pps*; do
        if [[ -c "$device" ]]; then
            pps_devices+=("$device")
        fi
    done
    echo "  PPS设备: ${pps_devices[*]:-无}"
    
    # 检查RTC设备
    local rtc_devices=()
    for device in /dev/rtc*; do
        if [[ -c "$device" ]]; then
            rtc_devices+=("$device")
        fi
    done
    echo "  RTC设备: ${rtc_devices[*]:-无}"
    
    # 检查网络接口PHC
    local phc_devices=()
    for iface in /sys/class/net/*; do
        if [[ -d "$iface" ]]; then
            local iface_name=$(basename "$iface")
            if [[ "$iface_name" != "lo" && -f "/sys/class/net/$iface_name/device/ptp" ]]; then
                local ptp_device=$(ls /sys/class/net/$iface_name/device/ptp/ 2>/dev/null | head -n1)
                if [[ -n "$ptp_device" ]]; then
                    phc_devices+=("$iface_name:/dev/$ptp_device")
                fi
            fi
        fi
    done
    echo "  PHC设备: ${phc_devices[*]:-无}"
}

# 检查网络连接状态
check_network_status() {
    echo "网络连接状态:"
    
    # 检查API端口
    local api_ports=("8080" "8443" "8081")
    for port in "${api_ports[@]}"; do
        if netstat -ln 2>/dev/null | grep -q ":$port "; then
            echo "  端口 $port: 监听中"
        else
            echo "  端口 $port: 未监听"
        fi
    done
    
    # 检查NTP服务
    if netstat -ln 2>/dev/null | grep -q ":123 "; then
        echo "  NTP服务: 运行中"
    else
        echo "  NTP服务: 未运行"
    fi
    
    # 检查网络接口状态
    local active_interfaces=$(ip link show up | grep -E '^[0-9]+:' | grep -v lo | wc -l)
    echo "  活跃网络接口: $active_interfaces"
}

# 检查日志文件
check_log_files() {
    echo "日志文件状态:"
    
    local log_dirs=("/var/log/timing-server" "/var/log")
    for log_dir in "${log_dirs[@]}"; do
        if [[ -d "$log_dir" ]]; then
            local log_size=$(du -sh "$log_dir" 2>/dev/null | awk '{print $1}' || echo "未知")
            echo "  $log_dir: $log_size"
            
            # 检查最近的错误日志
            local error_count=$(find "$log_dir" -name "*.log" -mtime -1 -exec grep -c "ERROR\|CRITICAL" {} \; 2>/dev/null | awk '{sum+=$1} END {print sum+0}')
            if [[ $error_count -gt 0 ]]; then
                print_warning "  最近24小时内发现 $error_count 个错误"
            fi
        fi
    done
}

# 执行系统优化
perform_optimization() {
    print_info "执行系统优化..."
    
    # 检查是否有root权限
    if [[ $EUID -ne 0 ]]; then
        print_warning "系统优化需要root权限，跳过部分优化项"
    fi
    
    # 优化项列表
    local optimizations=()
    
    # 1. 清理临时文件
    if [[ -d /tmp ]]; then
        local temp_size_before=$(du -sh /tmp 2>/dev/null | awk '{print $1}')
        find /tmp -type f -atime +7 -delete 2>/dev/null || true
        local temp_size_after=$(du -sh /tmp 2>/dev/null | awk '{print $1}')
        optimizations+=("清理临时文件: $temp_size_before -> $temp_size_after")
    fi
    
    # 2. 清理日志文件
    if [[ -d /var/log/timing-server ]]; then
        find /var/log/timing-server -name "*.log.*" -mtime +30 -delete 2>/dev/null || true
        optimizations+=("清理过期日志文件")
    fi
    
    # 3. 优化内存使用
    if [[ $EUID -eq 0 ]]; then
        echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
        optimizations+=("清理系统缓存")
    fi
    
    # 4. 检查并重启服务（如果需要）
    if systemctl is-active timing-server &>/dev/null; then
        local service_memory=$(ps -o rss= -p $(systemctl show timing-server --property=MainPID --value) 2>/dev/null | tr -d ' ')
        if [[ -n "$service_memory" && $service_memory -gt 102400 ]]; then  # 100MB
            print_warning "服务内存使用过高 (${service_memory}KB)，建议重启"
            optimizations+=("检测到高内存使用，建议重启服务")
        fi
    fi
    
    # 显示优化结果
    if [[ ${#optimizations[@]} -gt 0 ]]; then
        print_success "优化完成:"
        for opt in "${optimizations[@]}"; do
            echo "  - $opt"
        done
    else
        print_info "系统状态良好，无需优化"
    fi
}

# 生成系统报告
generate_report() {
    local report_file="/tmp/timing-server-report-$(date +%Y%m%d_%H%M%S).txt"
    
    print_info "生成系统报告: $report_file"
    
    {
        echo "高精度授时服务器系统报告"
        echo "生成时间: $(date)"
        echo "系统信息: $(uname -a)"
        echo ""
        
        echo "=== 服务状态 ==="
        check_service_status
        echo ""
        
        echo "=== 系统资源 ==="
        check_system_resources
        echo ""
        
        echo "=== 硬件设备 ==="
        check_hardware_status
        echo ""
        
        echo "=== 网络状态 ==="
        check_network_status
        echo ""
        
        echo "=== 日志状态 ==="
        check_log_files
        echo ""
        
        echo "=== 系统配置 ==="
        if [[ -f "$CONFIG_FILE" ]]; then
            echo "配置文件: $CONFIG_FILE"
            echo "配置大小: $(du -h "$CONFIG_FILE" | awk '{print $1}')"
            echo "最后修改: $(stat -c %y "$CONFIG_FILE")"
        else
            echo "配置文件不存在: $CONFIG_FILE"
        fi
        
    } > "$report_file"
    
    print_success "报告已生成: $report_file"
}

# 持续监控
continuous_monitor() {
    print_info "开始持续监控，间隔: ${CHECK_INTERVAL}秒"
    print_info "按 Ctrl+C 停止监控"
    
    # 创建日志目录
    local log_dir=$(dirname "$LOG_FILE")
    if [[ ! -d "$log_dir" ]]; then
        mkdir -p "$log_dir" 2>/dev/null || true
    fi
    
    log_message "INFO" "开始持续监控"
    
    while true; do
        echo ""
        echo "=== 监控检查 $(date) ==="
        
        # 执行检查
        check_service_status
        echo ""
        check_system_resources
        
        if [[ "$VERBOSE" == "true" ]]; then
            echo ""
            check_hardware_status
            echo ""
            check_network_status
        fi
        
        # 等待下次检查
        sleep "$CHECK_INTERVAL"
    done
}

# 执行健康检查
perform_health_check() {
    print_info "执行系统健康检查..."
    
    local issues=()
    
    # 检查服务状态
    if ! systemctl is-active timing-server &>/dev/null; then
        issues+=("timing-server服务未运行")
    fi
    
    # 检查配置文件
    if [[ ! -f "$CONFIG_FILE" ]]; then
        issues+=("配置文件不存在: $CONFIG_FILE")
    fi
    
    # 检查数据目录
    if [[ ! -d "/var/lib/timing-server" ]]; then
        issues+=("数据目录不存在: /var/lib/timing-server")
    fi
    
    # 检查日志目录
    if [[ ! -d "/var/log/timing-server" ]]; then
        issues+=("日志目录不存在: /var/log/timing-server")
    fi
    
    # 检查系统资源
    local memory_usage=$(free | awk '/^Mem:/{printf "%.0f", $3/$2*100}')
    if [[ $memory_usage -gt 90 ]]; then
        issues+=("内存使用率过高: ${memory_usage}%")
    fi
    
    # 显示检查结果
    if [[ ${#issues[@]} -eq 0 ]]; then
        print_success "健康检查通过，系统状态良好"
        return 0
    else
        print_error "发现以下问题:"
        for issue in "${issues[@]}"; do
            echo "  - $issue"
            log_message "ERROR" "$issue"
        done
        return 1
    fi
}

# 显示系统状态
show_status() {
    echo "高精度授时服务器系统状态"
    echo "=========================="
    echo ""
    
    check_service_status
    echo ""
    check_system_resources
    echo ""
    check_hardware_status
    echo ""
    check_network_status
    echo ""
    check_log_files
}

# 主程序
main() {
    parse_arguments "$@"
    
    # 创建日志目录
    local log_dir=$(dirname "$LOG_FILE")
    if [[ ! -d "$log_dir" ]]; then
        mkdir -p "$log_dir" 2>/dev/null || true
    fi
    
    case $COMMAND in
        "status")
            show_status
            ;;
        "monitor")
            continuous_monitor
            ;;
        "check")
            perform_health_check
            ;;
        "optimize")
            perform_optimization
            ;;
        "report")
            generate_report
            ;;
        *)
            print_error "未知命令: $COMMAND"
            show_usage
            exit 1
            ;;
    esac
}

# 执行主程序
main "$@"