# 高精度授时服务器系统

一个综合授时服务器系统，提供高精度时间同步服务，集成多种时间源（GNSS、铷原子钟、高精度RTC、外部1PPS和10MHz信号）。

## 系统特性

- **高精度时间同步**: ±50ns绝对时间精度（GNSS锁定状态）和±1μs守时精度（24小时）
- **多平台支持**: Linux x86_64、龙芯LoongArch64平台部署，macOS开发环境
- **标准协议支持**: PTP特级主时钟和NTP Stratum 1服务
- **Web管理界面**: 苹果风格的现代化管理界面

## 项目结构

```
timing-server-system/
├── backend/                    # C++后端服务
│   ├── src/                   # 源代码
│   ├── include/               # 头文件
│   ├── tests/                 # 单元测试
│   └── CMakeLists.txt         # 构建配置
├── frontend/                   # Vue.js前端
│   ├── src/                   # 前端源码
│   ├── public/                # 静态资源
│   └── package.json           # 前端依赖
├── build_scripts/             # 构建脚本
│   ├── toolchains/            # 交叉编译工具链
│   └── build.sh               # 自动化构建脚本
├── platform/                  # 平台特定文件
│   ├── systemd/               # systemd服务文件
│   ├── config/                # 配置文件模板
│   └── install.sh             # 安装脚本
├── docs/                      # 文档
└── CMakeLists.txt             # 根构建文件
```

## 快速开始

### 构建和部署

#### 自动化构建（推荐）
```bash
# 多平台构建
./build_scripts/build.sh [平台] [构建类型] [清理构建]

# 示例
./build_scripts/build.sh native Release        # 本地发布版本
./build_scripts/build.sh loongarch64 Debug     # 龙芯调试版本
./build_scripts/build.sh linux Release true    # Linux发布版本（清理构建）
```

#### 创建部署包
```bash
# 创建标准化部署包
./build_scripts/create_deployment_package.sh [平台] [构建类型] [包类型] [输出目录]

# 示例
./build_scripts/create_deployment_package.sh linux Release all ./packages
./build_scripts/create_deployment_package.sh loongarch64 Release deb
```

#### 自动化测试
```bash
# 运行测试套件
./build_scripts/run_tests.sh [测试类型] [平台] [构建类型] [详细输出]

# 示例
./build_scripts/run_tests.sh unit linux Debug true      # 单元测试
./build_scripts/run_tests.sh integration auto Debug     # 集成测试
./build_scripts/run_tests.sh coverage auto Debug        # 测试覆盖率
./build_scripts/run_tests.sh performance linux Release  # 性能测试
./build_scripts/run_tests.sh all linux Release          # 完整测试套件

# 端到端集成测试
./build_scripts/run_integration_tests.sh auto Debug true true

# 长期稳定性测试
./build_scripts/run_long_term_tests.sh 24 linux Release 300 true
```

#### 版本管理
```bash
# 版本管理和发布
./build_scripts/release_management.sh [操作] [版本] [平台] [发布类型]

# 示例
./build_scripts/release_management.sh bump 1.1.0        # 升级版本
./build_scripts/release_management.sh release 1.1.0     # 创建发布
./build_scripts/release_management.sh rollback 1.0.0    # 回滚版本
```

#### CMake手动构建
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/usr/local
make -j$(nproc)
sudo make install
```

### 系统安装

```bash
# 使用CMake安装（推荐）
cd build
sudo make install

# 或使用安装脚本
sudo ./platform/install.sh install

# 启动服务
sudo systemctl enable --now timing-server
```

### Web界面访问

安装完成后，可通过以下方式访问Web管理界面：
- HTTP: http://localhost:8080
- HTTPS: https://localhost:8443
- WebSocket: ws://localhost:8081

### 系统监控

```bash
# 查看系统状态
sudo timing-server-monitor status

# 持续监控
sudo timing-server-monitor monitor

# 健康检查
sudo timing-server-monitor check
```

## 安装目录结构

```
/usr/local/
├── bin/
│   ├── timing-server                    # 主程序
│   ├── timing-server-wrapper           # 包装脚本
│   └── timing-server-monitor           # 监控脚本
├── etc/timing-server/                   # 配置文件
├── share/timing-server/                 # 脚本和Web文件
└── var/lib/timing-server/               # 数据和日志
```

## 技术栈

- **后端**: C++17, CMake, SQLite, oatpp
- **前端**: Vue.js 3, TypeScript, Vite
- **测试框架**: Google Test, Google Mock, 端到端集成测试
- **平台**: Linux x86_64, LoongArch64, macOS (开发)
- **协议**: PTP, NTP, HTTP/HTTPS, WebSocket

## CI/CD 和自动化

系统支持完整的CI/CD流水线，包括：

- **多平台自动构建**: Linux x86_64, LoongArch64, macOS
- **自动化测试**: 单元测试、集成测试、性能测试、安全扫描
- **代码质量检查**: 静态分析、测试覆盖率、内存泄漏检测
- **自动化部署**: 测试环境、预生产环境、生产环境
- **版本管理**: 语义化版本、自动发布、配置迁移、回滚机制

### GitHub Actions 工作流

- **代码质量检查**: 格式检查、静态分析、结构验证
- **多平台构建**: 并行构建所有支持平台
- **自动化测试**: 单元测试、集成测试、性能测试
- **安全扫描**: CodeQL分析、依赖漏洞扫描
- **自动发布**: 版本标签触发自动发布流程

## 文档

详细文档请参考：
- [部署指南](docs/deployment-guide.md) - 完整的安装和配置指南
- [CI/CD指南](docs/ci-cd-guide.md) - 持续集成和部署流程
- [用户手册](docs/user-manual-zh.md) - 中文用户操作手册
- [HAL架构](docs/hal-architecture.md) - 硬件抽象层设计
- [API文档](docs/README.md) - REST API和WebSocket接口

## 许可证

本项目采用MIT许可证。