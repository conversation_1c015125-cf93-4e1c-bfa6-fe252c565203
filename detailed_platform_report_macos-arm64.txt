=== 平台详细基准测试报告 ===
生成时间: Sat Jul 26 14:55:33 2025

平台信息:
  类型: macOS ARM64 开发环境
  操作系统: Darwin
  处理器架构: arm64
  内核版本: 24.4.0
  开发环境: 是

=== 功能验证结果 ===
总体状态: 通过
执行时间: 18.1219 ms
统计: 成功=9, 警告=0, 错误=0, 严重=0

[成功] HAL工厂创建: HAL工厂验证成功：所有 6 个设备类型均可正常创建
  详情: 所有设备类型均可正常创建
  执行时间: 0.581083 ms

[成功] GNSS接收机: GNSS接收机创建成功
  详情: 支持NMEA数据读取
  执行时间: 0.105 ms

[成功] PPS输入: PPS输入创建成功
  详情: 支持高精度时间戳捕获
  执行时间: 0.031083 ms

[成功] 原子钟: 原子钟创建成功
  详情: 支持频率校正和状态监控
  执行时间: 0.011625 ms

[成功] 频率输入: 频率输入创建成功
  详情: 支持10MHz基准信号测量
  执行时间: 0.010625 ms

[成功] 高精度RTC: RTC创建成功
  详情: 支持非易失性时间存储
  执行时间: 0.068583 ms

[成功] 网络接口: 网络接口创建成功
  详情: 支持PHC硬件时钟操作
  执行时间: 0.01525 ms

[成功] Mock数据源: 所有Mock数据文件存在
  详情: 支持完整的开发测试
  执行时间: 0.130666 ms

[成功] macOS开发特性: 开发环境配置完整
  详情: Xcode工具: 是, CMake: 是
  执行时间: 16.9587 ms

=== 性能基准测试结果 ===
总体评分: 61.0977/100 (D)

时间戳精度测试:
  平均延迟: 15.4583 ns
  最大延迟: 125 ns
  最小延迟: 0 ns
  吞吐量: 1.58364e+07 ops/s
  CPU使用率: 5%
  内存使用: 2 MB

内存使用测试:
  平均延迟: 0 ns
  最大延迟: 0 ns
  最小延迟: 0 ns
  吞吐量: 0 ops/s
  CPU使用率: 0%
  内存使用: 6160 MB

CPU性能测试:
  平均延迟: 7.63737 ns
  最大延迟: 3.00937e-314 ns
  最小延迟: 4.94066e-324 ns
  吞吐量: 1.30935e+08 ops/s
  CPU使用率: 100%
  内存使用: 1 MB

I/O吞吐量测试:
  平均延迟: 1000 ns
  最大延迟: 0 ns
  最小延迟: 1.061e-314 ns
  吞吐量: 10000 ops/s
  CPU使用率: 10%
  内存使用: 5 MB

网络延迟测试:
  平均延迟: 500000 ns
  最大延迟: 3.00937e-314 ns
  最小延迟: 2.71053e-314 ns
  吞吐量: 2000 ops/s
  CPU使用率: 5%
  内存使用: 3 MB

=== 优化建议 ===
- 使用Xcode优化编译选项
- 配置macOS实时调度策略
- 优化Mock数据生成算法
- 使用macOS高精度定时器API
- 配置开发环境的资源限制

- 调整实时调度优先级
- 启用CPU亲和性设置
