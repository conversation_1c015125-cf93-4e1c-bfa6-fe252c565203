<template>
  <div class="config-view">
    <div class="page-header">
      <h1>系统资源</h1>
      <p>监控系统资源使用情况和硬件状态</p>
    </div>

    <!-- 系统概览 -->
    <div class="overview-section">
      <div class="overview-cards">
        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon cpu">💻</div>
            <h3>CPU使用率</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ systemMetrics.cpuUsage }}%</div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: systemMetrics.cpuUsage + '%' }"></div>
            </div>
            <div class="metric-details">
              <span>核心数: {{ systemMetrics.cpuCores }}</span>
              <span>频率: {{ systemMetrics.cpuFreq }}GHz</span>
            </div>
          </div>
        </div>

        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon memory">🧠</div>
            <h3>内存使用</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ systemMetrics.memoryUsage }}%</div>
            <div class="progress-bar">
              <div class="progress-fill memory" :style="{ width: systemMetrics.memoryUsage + '%' }"></div>
            </div>
            <div class="metric-details">
              <span>已用: {{ systemMetrics.memoryUsed }}GB</span>
              <span>总计: {{ systemMetrics.memoryTotal }}GB</span>
            </div>
          </div>
        </div>

        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon disk">💾</div>
            <h3>磁盘使用</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ systemMetrics.diskUsage }}%</div>
            <div class="progress-bar">
              <div class="progress-fill disk" :style="{ width: systemMetrics.diskUsage + '%' }"></div>
            </div>
            <div class="metric-details">
              <span>已用: {{ systemMetrics.diskUsed }}GB</span>
              <span>总计: {{ systemMetrics.diskTotal }}GB</span>
            </div>
          </div>
        </div>

        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon network">🌐</div>
            <h3>网络流量</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ systemMetrics.networkSpeed }}Mbps</div>
            <div class="network-stats">
              <div class="stat-item">
                <span class="stat-label">上传:</span>
                <span class="stat-value">{{ systemMetrics.networkUp }}MB/s</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">下载:</span>
                <span class="stat-value">{{ systemMetrics.networkDown }}MB/s</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="system-info-section">
      <div class="info-grid">
        <div class="info-card glass-card">
          <h3>系统信息</h3>
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">操作系统:</span>
              <span class="info-value">{{ systemInfo.os }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">内核版本:</span>
              <span class="info-value">{{ systemInfo.kernel }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">系统架构:</span>
              <span class="info-value">{{ systemInfo.arch }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行时间:</span>
              <span class="info-value">{{ systemInfo.uptime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">系统负载:</span>
              <span class="info-value">{{ systemInfo.loadAvg }}</span>
            </div>
          </div>
        </div>

        <div class="info-card glass-card">
          <h3>硬件信息</h3>
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">处理器:</span>
              <span class="info-value">{{ hardwareInfo.cpu }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">主板:</span>
              <span class="info-value">{{ hardwareInfo.motherboard }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">内存类型:</span>
              <span class="info-value">{{ hardwareInfo.memoryType }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">存储设备:</span>
              <span class="info-value">{{ hardwareInfo.storage }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">网络接口:</span>
              <span class="info-value">{{ hardwareInfo.network }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 系统指标数据
const systemMetrics = ref({
  cpuUsage: 15.2,
  cpuCores: 8,
  cpuFreq: 3.2,
  memoryUsage: 42.8,
  memoryUsed: 6.8,
  memoryTotal: 16,
  diskUsage: 68.5,
  diskUsed: 274,
  diskTotal: 400,
  networkSpeed: 125.6,
  networkUp: 2.3,
  networkDown: 8.7
})

// 系统信息
const systemInfo = ref({
  os: 'Linux Ubuntu 22.04 LTS',
  kernel: '5.15.0-78-generic',
  arch: 'x86_64',
  uptime: '15天 8小时 32分钟',
  loadAvg: '0.15, 0.23, 0.18'
})

// 硬件信息
const hardwareInfo = ref({
  cpu: 'Intel Core i7-12700K @ 3.60GHz',
  motherboard: 'ASUS ROG STRIX Z690-E',
  memoryType: 'DDR4-3200 16GB',
  storage: 'Samsung 980 PRO 500GB NVMe SSD',
  network: 'Intel I225-V 2.5Gbps Ethernet'
})

let updateInterval: ReturnType<typeof setInterval>

// 模拟实时数据更新
const updateMetrics = () => {
  // 模拟CPU使用率变化
  systemMetrics.value.cpuUsage = Math.max(5, Math.min(95,
    systemMetrics.value.cpuUsage + (Math.random() - 0.5) * 10))

  // 模拟内存使用率变化
  systemMetrics.value.memoryUsage = Math.max(20, Math.min(90,
    systemMetrics.value.memoryUsage + (Math.random() - 0.5) * 5))

  // 模拟网络流量变化
  systemMetrics.value.networkSpeed = Math.max(0,
    systemMetrics.value.networkSpeed + (Math.random() - 0.5) * 50)
  systemMetrics.value.networkUp = Math.max(0,
    systemMetrics.value.networkUp + (Math.random() - 0.5) * 2)
  systemMetrics.value.networkDown = Math.max(0,
    systemMetrics.value.networkDown + (Math.random() - 0.5) * 5)

  // 保留小数点后一位
  systemMetrics.value.cpuUsage = Math.round(systemMetrics.value.cpuUsage * 10) / 10
  systemMetrics.value.memoryUsage = Math.round(systemMetrics.value.memoryUsage * 10) / 10
  systemMetrics.value.networkSpeed = Math.round(systemMetrics.value.networkSpeed * 10) / 10
  systemMetrics.value.networkUp = Math.round(systemMetrics.value.networkUp * 10) / 10
  systemMetrics.value.networkDown = Math.round(systemMetrics.value.networkDown * 10) / 10
}

onMounted(() => {
  // 每3秒更新一次数据
  updateInterval = setInterval(updateMetrics, 3000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style lang="scss" scoped>
.config-view {
  padding: var(--spacing-lg);
}

.page-header {
  margin-bottom: var(--spacing-xl);

  h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }

  p {
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
}

.overview-section {
  margin-bottom: var(--spacing-2xl);

  .overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);

    .resource-card {
      padding: var(--spacing-lg);

      .card-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);

        .card-icon {
          font-size: 2rem;
          width: 50px;
          height: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: var(--radius-md);

          &.cpu { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
          &.memory { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
          &.disk { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
          &.network { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        }

        h3 {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-primary);
        }
      }

      .card-content {
        .metric-value {
          font-size: 2.5rem;
          font-weight: 700;
          color: var(--text-primary);
          margin-bottom: var(--spacing-md);
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: var(--spacing-md);

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: width 0.3s ease;

            &.memory { background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%); }
            &.disk { background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%); }
          }
        }

        .metric-details {
          display: flex;
          justify-content: space-between;
          font-size: 0.9rem;
          color: var(--text-secondary);
        }

        .network-stats {
          .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-sm);

            .stat-label {
              color: var(--text-secondary);
            }

            .stat-value {
              color: var(--text-primary);
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

.system-info-section {
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);

    .info-card {
      padding: var(--spacing-lg);

      h3 {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
      }

      .info-list {
        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-md) 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);

          &:last-child {
            border-bottom: none;
          }

          .info-label {
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 120px;
          }

          .info-value {
            color: var(--text-primary);
            font-weight: 600;
            text-align: right;
            flex: 1;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .resource-card .card-content .metric-value {
    font-size: 2rem;
  }
}
</style>