<template>
  <div class="users-view">
    <div class="page-header">
      <h1>用户管理</h1>
      <p>管理系统用户和权限</p>
      <div class="header-actions">
        <button @click="showCreateUser = true" class="btn-primary">
          <span class="icon">👤</span>
          创建用户
        </button>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="users-section">
      <div class="section-header">
        <h2>用户列表</h2>
        <div class="search-box">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索用户..."
            class="search-input"
          />
        </div>
      </div>

      <div class="users-table-container glass-card">
        <table class="users-table">
          <thead>
            <tr>
              <th>用户名</th>
              <th>邮箱</th>
              <th>角色</th>
              <th>状态</th>
              <th>最后登录</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in filteredUsers" :key="user.id">
              <td>
                <div class="user-info">
                  <div class="user-avatar">{{ user.username.charAt(0).toUpperCase() }}</div>
                  <span>{{ user.username }}</span>
                </div>
              </td>
              <td>{{ user.email }}</td>
              <td>
                <span class="role-badge" :class="getRoleClass(user.role)">
                  {{ getRoleText(user.role) }}
                </span>
              </td>
              <td>
                <span class="status-badge" :class="user.active ? 'active' : 'inactive'">
                  {{ user.active ? '活跃' : '禁用' }}
                </span>
              </td>
              <td>{{ formatDate(user.lastLogin) }}</td>
              <td>
                <div class="action-buttons">
                  <button @click="editUser(user)" class="btn-icon" title="编辑">
                    ✏️
                  </button>
                  <button @click="toggleUserStatus(user)" class="btn-icon" :title="user.active ? '禁用' : '启用'">
                    {{ user.active ? '🚫' : '✅' }}
                  </button>
                  <button @click="deleteUser(user)" class="btn-icon btn-danger" title="删除">
                    🗑️
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 系统配置 -->
    <div class="config-section">
      <div class="section-header">
        <h2>系统配置</h2>
      </div>

      <div class="config-tabs">
        <button
          v-for="tab in configTabs"
          :key="tab.id"
          @click="activeConfigTab = tab.id"
          class="tab-button"
          :class="{ active: activeConfigTab === tab.id }"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          {{ tab.name }}
        </button>
      </div>

      <div class="config-content glass-card">
        <!-- 网络设置 -->
        <div v-if="activeConfigTab === 'network'" class="config-panel">
          <NetworkConfig v-model="systemConfig.network" @save="saveNetworkConfig" />
        </div>

        <!-- 安全配置 -->
        <div v-if="activeConfigTab === 'security'" class="config-panel">
          <SecurityConfig v-model="systemConfig.security" @save="saveSecurityConfig" />
        </div>

        <!-- 日志设置 -->
        <div v-if="activeConfigTab === 'logging'" class="config-panel">
          <LoggingConfig v-model="systemConfig.logging" @save="saveLoggingConfig" />
        </div>

        <!-- 系统维护 -->
        <div v-if="activeConfigTab === 'maintenance'" class="config-panel">
          <MaintenancePanel @restart="restartSystem" @backup="createBackup" @restore="restoreBackup" />
        </div>
      </div>
    </div>

    <!-- 操作审计日志 -->
    <div class="audit-section">
      <div class="section-header">
        <h2>操作审计日志</h2>
        <div class="audit-filters">
          <select v-model="auditFilter.level" class="filter-select">
            <option value="">所有级别</option>
            <option value="info">信息</option>
            <option value="warning">警告</option>
            <option value="error">错误</option>
            <option value="critical">严重</option>
          </select>
          <input
            v-model="auditFilter.dateRange.start"
            type="datetime-local"
            class="filter-input"
          />
          <input
            v-model="auditFilter.dateRange.end"
            type="datetime-local"
            class="filter-input"
          />
          <button @click="loadAuditLogs" class="btn-secondary">刷新</button>
        </div>
      </div>

      <div class="audit-logs-container glass-card">
        <AuditLogViewer :logs="auditLogs" :loading="auditLoading" />
      </div>
    </div>

    <!-- 创建/编辑用户对话框 -->
    <UserDialog
      v-if="showCreateUser || editingUser"
      :user="editingUser"
      @save="saveUser"
      @cancel="cancelUserEdit"
    />

    <!-- 确认删除对话框 -->
    <ConfirmDialog
      v-if="userToDelete"
      title="删除用户"
      :message="`确定要删除用户 ${userToDelete.username} 吗？此操作不可撤销。`"
      @confirm="confirmDeleteUser"
      @cancel="userToDelete = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { User, SystemConfiguration, LogEntry } from '@/types/system'
import { userApi, systemApi, logApi } from '@/services/api'
import { handleApiError } from '@/services/api'
import NetworkConfig from '@/components/Config/NetworkConfig.vue'
import SecurityConfig from '@/components/Config/SecurityConfig.vue'
import LoggingConfig from '@/components/Config/LoggingConfig.vue'
import MaintenancePanel from '@/components/Config/MaintenancePanel.vue'
import AuditLogViewer from '@/components/Logs/AuditLogViewer.vue'
import UserDialog from '@/components/Users/<USER>'
import ConfirmDialog from '@/components/Common/ConfirmDialog.vue'

// 用户管理状态
const users = ref<User[]>([])
const searchQuery = ref('')
const showCreateUser = ref(false)
const editingUser = ref<User | null>(null)
const userToDelete = ref<User | null>(null)
const usersLoading = ref(false)

// 系统配置状态
const systemConfig = ref<SystemConfiguration>({
  network: {
    interfaces: []
  },
  time: {
    timezone: 'Asia/Shanghai',
    ntpServers: [],
    ptpDomain: 0,
    clockClass: 6
  },
  security: {
    httpsEnabled: true,
    certificatePath: '/etc/ssl/certs/server.crt',
    allowedIPs: [],
    sessionTimeout: 1800
  },
  logging: {
    level: 'info',
    maxSize: 100,
    retention: 30,
    remoteLogging: false
  },
  alerts: {
    emailEnabled: false,
    recipients: [],
    thresholds: {
      temperatureHigh: 70,
      temperatureLow: 10,
      accuracyThreshold: '±100ns',
      syncLossTimeout: 300
    }
  }
})

const activeConfigTab = ref('network')
const configTabs = [
  { id: 'network', name: '网络设置', icon: '🌐' },
  { id: 'security', name: '安全配置', icon: '🔒' },
  { id: 'logging', name: '日志设置', icon: '📝' },
  { id: 'maintenance', name: '系统维护', icon: '🔧' }
]

// 审计日志状态
const auditLogs = ref<LogEntry[]>([])
const auditLoading = ref(false)
const auditFilter = ref({
  level: '',
  dateRange: {
    start: '',
    end: ''
  }
})

// 计算属性
const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  
  const query = searchQuery.value.toLowerCase()
  return users.value.filter(user =>
    user.username.toLowerCase().includes(query) ||
    user.email.toLowerCase().includes(query)
  )
})

// 方法
const loadUsers = async () => {
  try {
    usersLoading.value = true
    const response = await userApi.getUsers()
    users.value = response.data.data
  } catch (error) {
    console.error('Failed to load users:', error)
    // 使用模拟数据
    users.value = [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'administrator',
        lastLogin: new Date().toISOString(),
        active: true,
        permissions: ['read', 'write', 'config', 'admin']
      },
      {
        id: '2',
        username: 'operator',
        email: '<EMAIL>',
        role: 'operator',
        lastLogin: new Date(Date.now() - 86400000).toISOString(),
        active: true,
        permissions: ['read', 'write', 'config']
      },
      {
        id: '3',
        username: 'viewer',
        email: '<EMAIL>',
        role: 'viewer',
        lastLogin: new Date(Date.now() - 172800000).toISOString(),
        active: true,
        permissions: ['read']
      }
    ]
  } finally {
    usersLoading.value = false
  }
}

const loadSystemConfig = async () => {
  try {
    const response = await systemApi.getSystemConfig()
    systemConfig.value = response.data.data
  } catch (error) {
    console.error('Failed to load system config:', error)
  }
}

const loadAuditLogs = async () => {
  try {
    auditLoading.value = true
    const query = {
      level: auditFilter.value.level,
      startTime: auditFilter.value.dateRange.start,
      endTime: auditFilter.value.dateRange.end,
      limit: 100
    }
    
    const response = await logApi.getLogs(query)
    auditLogs.value = response.data.data
  } catch (error) {
    console.error('Failed to load audit logs:', error)
    // 使用模拟数据
    auditLogs.value = [
      {
        timestamp: new Date().toISOString(),
        level: 'info',
        component: 'AUTH',
        message: '用户 admin 登录成功',
        details: { ip: '*************', userAgent: 'Mozilla/5.0...' }
      },
      {
        timestamp: new Date(Date.now() - 300000).toISOString(),
        level: 'warning',
        component: 'CONFIG',
        message: '系统配置已更改',
        details: { user: 'admin', changes: ['network.interfaces'] }
      }
    ]
  } finally {
    auditLoading.value = false
  }
}

const editUser = (user: User) => {
  editingUser.value = { ...user }
}

const deleteUser = (user: User) => {
  userToDelete.value = user
}

const toggleUserStatus = async (user: User) => {
  try {
    await userApi.updateUser(user.id, { active: !user.active })
    user.active = !user.active
  } catch (error) {
    console.error('Failed to toggle user status:', error)
  }
}

const saveUser = async (userData: Partial<User>) => {
  try {
    if (editingUser.value) {
      // 更新用户
      await userApi.updateUser(editingUser.value.id, userData)
      const index = users.value.findIndex(u => u.id === editingUser.value!.id)
      if (index !== -1) {
        users.value[index] = { ...users.value[index], ...userData }
      }
    } else {
      // 创建用户
      const response = await userApi.createUser(userData as Omit<User, 'id' | 'lastLogin'>)
      users.value.push(response.data.data)
    }
    
    cancelUserEdit()
  } catch (error) {
    console.error('Failed to save user:', error)
  }
}

const confirmDeleteUser = async () => {
  if (!userToDelete.value) return
  
  try {
    await userApi.deleteUser(userToDelete.value.id)
    users.value = users.value.filter(u => u.id !== userToDelete.value!.id)
    userToDelete.value = null
  } catch (error) {
    console.error('Failed to delete user:', error)
  }
}

const cancelUserEdit = () => {
  showCreateUser.value = false
  editingUser.value = null
}

const saveNetworkConfig = async (config: any) => {
  try {
    await systemApi.updateSystemConfig({ network: config })
    systemConfig.value.network = config
  } catch (error) {
    console.error('Failed to save network config:', error)
  }
}

const saveSecurityConfig = async (config: any) => {
  try {
    await systemApi.updateSystemConfig({ security: config })
    systemConfig.value.security = config
  } catch (error) {
    console.error('Failed to save security config:', error)
  }
}

const saveLoggingConfig = async (config: any) => {
  try {
    await systemApi.updateSystemConfig({ logging: config })
    systemConfig.value.logging = config
  } catch (error) {
    console.error('Failed to save logging config:', error)
  }
}

const restartSystem = async () => {
  try {
    await systemApi.restartSystem()
    // 显示重启确认消息
  } catch (error) {
    console.error('Failed to restart system:', error)
  }
}

const createBackup = async () => {
  try {
    // 实现备份逻辑
    console.log('Creating backup...')
  } catch (error) {
    console.error('Failed to create backup:', error)
  }
}

const restoreBackup = async () => {
  try {
    // 实现恢复逻辑
    console.log('Restoring backup...')
  } catch (error) {
    console.error('Failed to restore backup:', error)
  }
}

// 工具函数
const getRoleClass = (role: string) => {
  const classes = {
    administrator: 'role-admin',
    operator: 'role-operator',
    viewer: 'role-viewer'
  }
  return classes[role as keyof typeof classes] || 'role-viewer'
}

const getRoleText = (role: string) => {
  const texts = {
    administrator: '管理员',
    operator: '操作员',
    viewer: '查看者'
  }
  return texts[role as keyof typeof texts] || '未知'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadUsers()
  loadSystemConfig()
  loadAuditLogs()
  
  // 设置默认日期范围（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  auditFilter.value.dateRange.start = yesterday.toISOString().slice(0, 16)
  auditFilter.value.dateRange.end = now.toISOString().slice(0, 16)
})
</script>

<style lang="scss" scoped>
.users-view {
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
  
  h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  p {
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
  
  .header-actions {
    display: flex;
    gap: var(--spacing-md);
  }
}

.users-section, .config-section, .audit-section {
  margin-bottom: var(--spacing-2xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  
  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
  }
  
  .search-box {
    .search-input {
      padding: var(--spacing-sm) var(--spacing-md);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-md);
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      width: 300px;
      
      &::placeholder {
        color: var(--text-secondary);
      }
      
      &:focus {
        outline: none;
        border-color: var(--status-info);
        background: rgba(255, 255, 255, 0.15);
      }
    }
  }
  
  .audit-filters {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    
    .filter-select, .filter-input {
      padding: var(--spacing-sm);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-sm);
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      
      &:focus {
        outline: none;
        border-color: var(--status-info);
      }
    }
  }
}

.users-table-container {
  padding: var(--spacing-lg);
  overflow-x: auto;
}

.users-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  th {
    font-weight: 600;
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
  }
  
  td {
    color: var(--text-secondary);
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--status-info);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.9rem;
    }
  }
  
  .role-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    
    &.role-admin {
      background: rgba(239, 68, 68, 0.2);
      color: var(--status-error);
    }
    
    &.role-operator {
      background: rgba(245, 158, 11, 0.2);
      color: var(--status-warning);
    }
    
    &.role-viewer {
      background: rgba(59, 130, 246, 0.2);
      color: var(--status-info);
    }
  }
  
  .status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    
    &.active {
      background: rgba(34, 197, 94, 0.2);
      color: var(--status-success);
    }
    
    &.inactive {
      background: rgba(107, 114, 128, 0.2);
      color: var(--text-secondary);
    }
  }
  
  .action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    
    .btn-icon {
      padding: var(--spacing-xs);
      border: none;
      background: rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-sm);
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }
      
      &.btn-danger:hover {
        background: rgba(239, 68, 68, 0.2);
      }
    }
  }
}

.config-tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  
  .tab-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: var(--text-primary);
    }
    
    &.active {
      background: var(--status-info);
      color: white;
    }
    
    .tab-icon {
      font-size: 1.2rem;
    }
  }
}

.config-content {
  padding: var(--spacing-xl);
}

.audit-logs-container {
  padding: var(--spacing-lg);
}

// 按钮样式
.btn-primary {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--status-info);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #2563eb;
    transform: translateY(-1px);
  }
  
  .icon {
    font-size: 1.1rem;
  }
}

.btn-secondary {
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }
  
  .config-tabs {
    flex-wrap: wrap;
  }
  
  .users-table-container {
    padding: var(--spacing-md);
  }
  
  .users-table {
    font-size: 0.9rem;
    
    th, td {
      padding: var(--spacing-sm);
    }
  }
}
</style>