<template>
  <div class="time-source-view">
    <div class="page-header">
      <h1>时间源配置</h1>
      <p>配置和管理系统时间源</p>
    </div>
    
    <div class="config-tabs">
      <div class="tab-nav">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          class="tab-button"
          :class="{ active: activeTab === tab.id }"
          @click="activeTab = tab.id"
        >
          <span class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-label">{{ tab.label }}</span>
        </button>
      </div>
      
      <div class="tab-content">
        <!-- GNSS配置 -->
        <div v-if="activeTab === 'gnss'" class="tab-panel">
          <GnssConfig @config-change="handleGnssConfigChange" />
        </div>
        
        <!-- PTP配置 -->
        <div v-if="activeTab === 'ptp'" class="tab-panel">
          <PtpConfig @config-change="handlePtpConfigChange" />
        </div>
        
        <!-- NTP配置 -->
        <div v-if="activeTab === 'ntp'" class="tab-panel">
          <NtpConfig @config-change="handleNtpConfigChange" />
        </div>
        
        <!-- 原子钟配置 -->
        <div v-if="activeTab === 'atomic'" class="tab-panel">
          <AtomicClockConfig @config-change="handleAtomicConfigChange" />
        </div>
        
        <!-- 时间源优先级 -->
        <div v-if="activeTab === 'priority'" class="tab-panel">
          <TimeSourcePriority @config-change="handlePriorityConfigChange" />
        </div>
        
        <!-- 配置备份 -->
        <div v-if="activeTab === 'backup'" class="tab-panel">
          <ConfigBackup />
        </div>
      </div>
    </div>
    
    <!-- 保存配置按钮 -->
    <div class="config-actions" v-if="hasUnsavedChanges">
      <div class="actions-container">
        <div class="unsaved-indicator">
          <span class="indicator-dot"></span>
          <span class="indicator-text">有未保存的更改</span>
        </div>
        <div class="action-buttons">
          <button 
            class="btn btn-secondary" 
            @click="discardChanges"
            :disabled="saving"
          >
            放弃更改
          </button>
          <button 
            class="btn btn-primary" 
            @click="saveAllConfigs"
            :disabled="saving"
          >
            <span v-if="saving">保存中...</span>
            <span v-else>保存配置</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import GnssConfig from '@/components/TimeSource/GnssConfig.vue'
import PtpConfig from '@/components/TimeSource/PtpConfig.vue'
import NtpConfig from '@/components/TimeSource/NtpConfig.vue'
import AtomicClockConfig from '@/components/TimeSource/AtomicClockConfig.vue'
import TimeSourcePriority from '@/components/TimeSource/TimeSourcePriority.vue'
import ConfigBackup from '@/components/TimeSource/ConfigBackup.vue'
import { timeSourceApi } from '@/services/api'
import type { 
  GnssConfig as GnssConfigType, 
  PtpConfig as PtpConfigType, 
  NtpConfig as NtpConfigType, 
  AtomicClockConfig as AtomicClockConfigType,
  TimeSourcePriority as TimeSourcePriorityType
} from '@/types/system'

const activeTab = ref('gnss')
const saving = ref(false)

const tabs = [
  { id: 'gnss', label: 'GNSS接收器', icon: '🛰️' },
  { id: 'ptp', label: 'PTP服务', icon: '🌐' },
  { id: 'ntp', label: 'NTP服务', icon: '🕐' },
  { id: 'atomic', label: '原子钟', icon: '⚛️' },
  { id: 'priority', label: '优先级配置', icon: '📊' },
  { id: 'backup', label: '配置备份', icon: '💾' }
]

// 配置状态管理
const configChanges = reactive({
  gnss: null as GnssConfigType | null,
  ptp: null as PtpConfigType | null,
  ntp: null as NtpConfigType | null,
  atomic: null as AtomicClockConfigType | null,
  priority: null as TimeSourcePriorityType | null
})

const hasUnsavedChanges = computed(() => {
  return Object.values(configChanges).some(config => config !== null)
})

// 配置变更处理函数
const handleGnssConfigChange = (config: GnssConfigType) => {
  configChanges.gnss = config
}

const handlePtpConfigChange = (config: PtpConfigType) => {
  configChanges.ptp = config
}

const handleNtpConfigChange = (config: NtpConfigType) => {
  configChanges.ntp = config
}

const handleAtomicConfigChange = (config: AtomicClockConfigType) => {
  configChanges.atomic = config
}

const handlePriorityConfigChange = (config: TimeSourcePriorityType) => {
  configChanges.priority = config
}

// 保存所有配置
const saveAllConfigs = async () => {
  saving.value = true
  
  try {
    const savePromises = []
    
    if (configChanges.gnss) {
      savePromises.push(timeSourceApi.updateGnssConfig(configChanges.gnss))
    }
    
    if (configChanges.ptp) {
      savePromises.push(timeSourceApi.updatePtpConfig(configChanges.ptp))
    }
    
    if (configChanges.ntp) {
      savePromises.push(timeSourceApi.updateNtpConfig(configChanges.ntp))
    }
    
    if (configChanges.atomic) {
      savePromises.push(timeSourceApi.updateAtomicClockConfig(configChanges.atomic))
    }
    
    if (configChanges.priority) {
      savePromises.push(timeSourceApi.updateTimeSourcePriority(configChanges.priority))
    }
    
    await Promise.all(savePromises)
    
    // 清除未保存的更改
    Object.keys(configChanges).forEach(key => {
      configChanges[key as keyof typeof configChanges] = null
    })
    
    // 显示成功消息
    alert('配置保存成功')
    
  } catch (error) {
    console.error('保存配置失败:', error)
    alert('保存配置失败，请重试')
  } finally {
    saving.value = false
  }
}

// 放弃更改
const discardChanges = () => {
  if (confirm('确定要放弃所有未保存的更改吗？')) {
    Object.keys(configChanges).forEach(key => {
      configChanges[key as keyof typeof configChanges] = null
    })
  }
}
</script>

<style lang="scss" scoped>
.time-source-view {
  padding: var(--spacing-lg);
  min-height: 100vh;
  position: relative;
}

.page-header {
  margin-bottom: var(--spacing-xl);
  
  h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  p {
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
}

.config-tabs {
  .tab-nav {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    overflow-x: auto;
    padding-bottom: var(--spacing-sm);
    
    .tab-button {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-md) var(--spacing-lg);
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-lg);
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
      
      &:hover {
        background: var(--glass-hover);
        color: var(--text-primary);
        transform: translateY(-2px);
      }
      
      &.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        
        &:hover {
          background: var(--primary-color-dark);
          transform: translateY(-2px);
        }
      }
      
      .tab-icon {
        font-size: 1.2rem;
      }
      
      .tab-label {
        font-weight: 500;
      }
    }
  }
  
  .tab-content {
    .tab-panel {
      animation: fadeIn 0.3s ease-in-out;
    }
  }
}

.config-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-top: 1px solid var(--glass-border);
  padding: var(--spacing-md) var(--spacing-lg);
  z-index: 100;
  
  .actions-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .unsaved-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .indicator-dot {
      width: 8px;
      height: 8px;
      background: var(--warning-color);
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
    
    .indicator-text {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &.btn-primary {
    background: var(--primary-color);
    color: white;
    
    &:hover:not(:disabled) {
      background: var(--primary-color-dark);
      transform: translateY(-1px);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  &.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    
    &:hover:not(:disabled) {
      background: var(--glass-hover);
      transform: translateY(-1px);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .time-source-view {
    padding: var(--spacing-md);
  }
  
  .config-tabs .tab-nav {
    .tab-button {
      padding: var(--spacing-sm) var(--spacing-md);
      
      .tab-label {
        display: none;
      }
    }
  }
  
  .config-actions {
    .actions-container {
      flex-direction: column;
      gap: var(--spacing-sm);
    }
  }
}
</style>