<template>
  <div class="gnss-view">
    <div class="page-header">
      <h1>GNSS接收器配置</h1>
      <p>配置和管理GNSS接收器设置</p>
    </div>
    
    <div class="gnss-content">
      <GnssConfig @config-change="handleConfigChange" />
    </div>
    
    <!-- 保存配置按钮 -->
    <div class="config-actions" v-if="hasUnsavedChanges">
      <div class="actions-container">
        <div class="unsaved-indicator">
          <span class="indicator-dot"></span>
          <span class="indicator-text">有未保存的更改</span>
        </div>
        <div class="action-buttons">
          <button 
            class="btn btn-secondary" 
            @click="discardChanges"
            :disabled="saving"
          >
            放弃更改
          </button>
          <button 
            class="btn btn-primary" 
            @click="saveConfig"
            :disabled="saving"
          >
            <span v-if="saving">保存中...</span>
            <span v-else>保存配置</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import GnssConfig from '@/components/TimeSource/GnssConfig.vue'
import { timeSourceApi } from '@/services/api'
import type { GnssConfig as GnssConfigType } from '@/types/system'

const saving = ref(false)
const configChanges = ref<GnssConfigType | null>(null)

const hasUnsavedChanges = computed(() => {
  return configChanges.value !== null
})

const handleConfigChange = (config: GnssConfigType) => {
  configChanges.value = config
}

const saveConfig = async () => {
  if (!configChanges.value) return
  
  saving.value = true
  
  try {
    await timeSourceApi.updateGnssConfig(configChanges.value)
    configChanges.value = null
    alert('GNSS配置保存成功')
  } catch (error) {
    console.error('保存GNSS配置失败:', error)
    alert('保存配置失败，请重试')
  } finally {
    saving.value = false
  }
}

const discardChanges = () => {
  configChanges.value = null
}
</script>

<style scoped>
.gnss-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  color: #ffffff;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #a0a9c0;
  font-size: 16px;
  margin: 0;
}

.gnss-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px;
  margin-bottom: 20px;
}

.config-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.actions-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.unsaved-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffd700;
  font-size: 14px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  background: #ffd700;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}
</style>
