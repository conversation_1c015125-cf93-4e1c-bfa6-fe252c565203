<template>
  <div class="tod-view">
    <div class="page-header">
      <h1>TOD设置</h1>
      <p>配置和管理时间输出(Time of Day)设置</p>
    </div>
    
    <div class="tod-content">
      <!-- TOD基本设置 -->
      <div class="config-section">
        <h3>基本设置</h3>
        <div class="form-grid">
          <div class="form-group">
            <label>启用TOD输出</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="tod-enabled" 
                v-model="config.enabled"
                @change="onConfigChange"
              >
              <label for="tod-enabled"></label>
            </div>
          </div>
          
          <div class="form-group">
            <label>输出格式</label>
            <select v-model="config.format" @change="onConfigChange">
              <option value="ascii">ASCII格式</option>
              <option value="binary">二进制格式</option>
              <option value="irig-b">IRIG-B格式</option>
              <option value="dcf77">DCF77格式</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>输出端口</label>
            <select v-model="config.port" @change="onConfigChange">
              <option value="serial1">串口1 (/dev/ttyS0)</option>
              <option value="serial2">串口2 (/dev/ttyS1)</option>
              <option value="ethernet">以太网</option>
              <option value="gpio">GPIO输出</option>
            </select>
          </div>
        </div>
      </div>
      
      <!-- 串口设置 -->
      <div class="config-section" v-if="config.port.startsWith('serial')">
        <h3>串口设置</h3>
        <div class="form-grid">
          <div class="form-group">
            <label>波特率</label>
            <select v-model="config.serial.baudRate" @change="onConfigChange">
              <option value="9600">9600</option>
              <option value="19200">19200</option>
              <option value="38400">38400</option>
              <option value="57600">57600</option>
              <option value="115200">115200</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>数据位</label>
            <select v-model="config.serial.dataBits" @change="onConfigChange">
              <option value="7">7位</option>
              <option value="8">8位</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>停止位</label>
            <select v-model="config.serial.stopBits" @change="onConfigChange">
              <option value="1">1位</option>
              <option value="2">2位</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>校验位</label>
            <select v-model="config.serial.parity" @change="onConfigChange">
              <option value="none">无校验</option>
              <option value="odd">奇校验</option>
              <option value="even">偶校验</option>
            </select>
          </div>
        </div>
      </div>
      
      <!-- 时间格式设置 -->
      <div class="config-section">
        <h3>时间格式设置</h3>
        <div class="form-grid">
          <div class="form-group">
            <label>时区</label>
            <select v-model="config.timezone" @change="onConfigChange">
              <option value="UTC">UTC</option>
              <option value="Asia/Shanghai">北京时间 (UTC+8)</option>
              <option value="Asia/Tokyo">东京时间 (UTC+9)</option>
              <option value="Europe/London">伦敦时间</option>
              <option value="America/New_York">纽约时间</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>输出频率</label>
            <select v-model="config.frequency" @change="onConfigChange">
              <option value="1">每秒1次</option>
              <option value="10">每秒10次</option>
              <option value="100">每秒100次</option>
              <option value="1000">每秒1000次</option>
            </select>
          </div>
          
          <div class="form-group">
            <label>精度</label>
            <select v-model="config.precision" @change="onConfigChange">
              <option value="second">秒级</option>
              <option value="millisecond">毫秒级</option>
              <option value="microsecond">微秒级</option>
              <option value="nanosecond">纳秒级</option>
            </select>
          </div>
        </div>
      </div>
      
      <!-- 状态显示 -->
      <div class="config-section">
        <h3>输出状态</h3>
        <div class="status-grid">
          <div class="status-item">
            <span class="status-label">当前状态:</span>
            <span class="status-value" :class="config.enabled ? 'active' : 'inactive'">
              {{ config.enabled ? '运行中' : '已停止' }}
            </span>
          </div>
          
          <div class="status-item">
            <span class="status-label">输出计数:</span>
            <span class="status-value">{{ outputCount }}</span>
          </div>
          
          <div class="status-item">
            <span class="status-label">最后输出:</span>
            <span class="status-value">{{ lastOutput || '无' }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 保存配置按钮 -->
    <div class="config-actions" v-if="hasUnsavedChanges">
      <div class="actions-container">
        <div class="unsaved-indicator">
          <span class="indicator-dot"></span>
          <span class="indicator-text">有未保存的更改</span>
        </div>
        <div class="action-buttons">
          <button 
            class="btn btn-secondary" 
            @click="discardChanges"
            :disabled="saving"
          >
            放弃更改
          </button>
          <button 
            class="btn btn-primary" 
            @click="saveConfig"
            :disabled="saving"
          >
            <span v-if="saving">保存中...</span>
            <span v-else>保存配置</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

interface TodConfig {
  enabled: boolean
  format: string
  port: string
  serial: {
    baudRate: number
    dataBits: number
    stopBits: number
    parity: string
  }
  timezone: string
  frequency: number
  precision: string
}

const saving = ref(false)
const hasChanges = ref(false)
const outputCount = ref(0)
const lastOutput = ref('')

const config = reactive<TodConfig>({
  enabled: false,
  format: 'ascii',
  port: 'serial1',
  serial: {
    baudRate: 9600,
    dataBits: 8,
    stopBits: 1,
    parity: 'none'
  },
  timezone: 'UTC',
  frequency: 1,
  precision: 'second'
})

const hasUnsavedChanges = computed(() => {
  return hasChanges.value
})

const onConfigChange = () => {
  hasChanges.value = true
}

const saveConfig = async () => {
  saving.value = true
  
  try {
    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))
    hasChanges.value = false
    alert('TOD配置保存成功')
  } catch (error) {
    console.error('保存TOD配置失败:', error)
    alert('保存配置失败，请重试')
  } finally {
    saving.value = false
  }
}

const discardChanges = () => {
  hasChanges.value = false
  // 重置配置到初始状态
  Object.assign(config, {
    enabled: false,
    format: 'ascii',
    port: 'serial1',
    serial: {
      baudRate: 9600,
      dataBits: 8,
      stopBits: 1,
      parity: 'none'
    },
    timezone: 'UTC',
    frequency: 1,
    precision: 'second'
  })
}

// 模拟输出计数更新
let countInterval: number | null = null

onMounted(() => {
  if (config.enabled) {
    countInterval = setInterval(() => {
      outputCount.value++
      lastOutput.value = new Date().toISOString()
    }, 1000) as unknown as number
  }
})

onUnmounted(() => {
  if (countInterval) {
    clearInterval(countInterval)
  }
})
</script>

<style scoped>
.tod-view {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  color: #ffffff;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #a0a9c0;
  font-size: 16px;
  margin: 0;
}

.tod-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px;
  margin-bottom: 20px;
}

.config-section {
  margin-bottom: 32px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.config-section h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #a0a9c0;
  font-size: 14px;
  font-weight: 500;
}

.form-group input,
.form-group select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: #667eea;
}

.toggle-switch input:checked + label:before {
  transform: translateX(26px);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-label {
  color: #a0a9c0;
  font-size: 14px;
}

.status-value {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.status-value.active {
  color: #4ade80;
}

.status-value.inactive {
  color: #f87171;
}

.config-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.actions-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.unsaved-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffd700;
  font-size: 14px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  background: #ffd700;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}
</style>
