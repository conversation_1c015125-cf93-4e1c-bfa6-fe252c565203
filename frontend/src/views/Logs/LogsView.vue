<template>
  <div class="logs-view">
    <div class="page-header">
      <h1>系统日志</h1>
      <p>查看和分析系统运行日志</p>
    </div>

    <!-- 日志统计概览 -->
    <div class="overview-section">
      <div class="overview-cards">
        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon info">ℹ️</div>
            <h3>信息日志</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ logStats.info }}</div>
            <div class="metric-details">
              <span>正常运行信息</span>
            </div>
          </div>
        </div>

        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon warn">⚠️</div>
            <h3>警告日志</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ logStats.warn }}</div>
            <div class="metric-details">
              <span>需要关注的警告</span>
            </div>
          </div>
        </div>

        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon error">❌</div>
            <h3>错误日志</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ logStats.error }}</div>
            <div class="metric-details">
              <span>系统错误记录</span>
            </div>
          </div>
        </div>

        <div class="resource-card glass-card apple-hover">
          <div class="card-header">
            <div class="card-icon debug">�</div>
            <h3>调试日志</h3>
          </div>
          <div class="card-content">
            <div class="metric-value">{{ logStats.debug }}</div>
            <div class="metric-details">
              <span>调试信息记录</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志控制面板 -->
    <div class="control-section">
      <div class="section-header">
        <h2>日志筛选与控制</h2>
      </div>
      <div class="control-panel glass-card">
        <div class="controls-left">
          <div class="filter-group">
            <label>日志级别:</label>
            <select v-model="selectedLevel" @change="filterLogs" class="level-select">
              <option value="all">全部</option>
              <option value="info">信息</option>
              <option value="warn">警告</option>
              <option value="error">错误</option>
              <option value="debug">调试</option>
            </select>
          </div>

          <div class="filter-group">
            <label>日志来源:</label>
            <select v-model="selectedSource" @change="filterLogs" class="source-select">
              <option value="all">全部</option>
              <option value="timing-server">授时服务器</option>
              <option value="api-server">API服务器</option>
              <option value="websocket">WebSocket服务</option>
              <option value="system">系统</option>
            </select>
          </div>

          <div class="search-group">
            <input
              v-model="searchQuery"
              @input="filterLogs"
              placeholder="搜索日志内容..."
              class="search-input"
            />
          </div>
        </div>

        <div class="controls-right">
          <button @click="refreshLogs" class="btn btn-secondary apple-hover">
            <span class="btn-icon">🔄</span>
            刷新
          </button>
          <button @click="clearLogs" class="btn btn-danger apple-hover">
            <span class="btn-icon">�️</span>
            清空
          </button>
          <button @click="exportLogs" class="btn btn-primary apple-hover">
            <span class="btn-icon">📥</span>
            导出
          </button>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="logs-section">
      <div class="section-header">
        <h2>日志记录</h2>
        <div class="log-info">
          <span>显示 {{ filteredLogs.length }} 条日志</span>
          <div class="auto-refresh-toggle">
            <input type="checkbox" v-model="autoRefresh" id="auto-refresh" />
            <label for="auto-refresh" class="toggle-label">自动刷新</label>
          </div>
        </div>
      </div>

      <div class="log-container glass-card">
        <div class="log-list" ref="logListRef">
          <div
            v-for="log in filteredLogs"
            :key="log.id"
            class="log-entry apple-hover"
            :class="['level-' + log.level]"
          >
            <div class="log-time">{{ formatTime(log.timestamp) }}</div>
            <div class="log-level" :class="'level-' + log.level">
              {{ log.level.toUpperCase() }}
            </div>
            <div class="log-source">{{ log.source }}</div>
            <div class="log-message">{{ log.message }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'

// 响应式数据
const selectedLevel = ref('all')
const selectedSource = ref('all')
const searchQuery = ref('')
const autoRefresh = ref(false)
const logListRef = ref(null)

const logStats = reactive({
  info: 0,
  warn: 0,
  error: 0,
  debug: 0
})

// 模拟日志数据
const logs = ref([
  {
    id: 1,
    timestamp: new Date().toISOString(),
    level: 'info',
    source: 'timing-server',
    message: '授时服务器启动成功'
  },
  {
    id: 2,
    timestamp: new Date(Date.now() - 60000).toISOString(),
    level: 'warn',
    source: 'api-server',
    message: 'API请求超时，正在重试'
  },
  {
    id: 3,
    timestamp: new Date(Date.now() - 120000).toISOString(),
    level: 'error',
    source: 'websocket',
    message: 'WebSocket连接断开'
  }
])

// 计算属性
const filteredLogs = computed(() => {
  return logs.value.filter(log => {
    const levelMatch = selectedLevel.value === 'all' || log.level === selectedLevel.value
    const sourceMatch = selectedSource.value === 'all' || log.source === selectedSource.value
    const searchMatch = !searchQuery.value || log.message.includes(searchQuery.value)
    return levelMatch && sourceMatch && searchMatch
  })
})

// 方法
const filterLogs = () => {
  // 过滤逻辑已通过计算属性实现
  console.log('Filtering logs:', { selectedLevel: selectedLevel.value, selectedSource: selectedSource.value, searchQuery: searchQuery.value })
}

const refreshLogs = () => {
  // TODO: 实现刷新日志逻辑
  console.log('Refreshing logs')
}

const clearLogs = () => {
  logs.value = []
  console.log('Clearing logs')
}

const exportLogs = () => {
  // TODO: 实现导出日志逻辑
  const dataStr = JSON.stringify(filteredLogs.value, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `logs_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  console.log('Exporting logs')
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 初始化日志统计数据
  logStats.info = 156
  logStats.warn = 23
  logStats.error = 8
  logStats.debug = 45
})
</script>

<style lang="scss" scoped>
.logs-view {
  padding: var(--spacing-lg);
}

.page-header {
  margin-bottom: var(--spacing-xl);

  h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }

  p {
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
}

// 概览卡片样式 - 与其他页面保持一致
.overview-section {
  margin-bottom: var(--spacing-xl);
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.resource-card {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);

    .card-icon {
      font-size: 2rem;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: var(--border-radius-md);

      &.info {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      }

      &.warn {
        background: linear-gradient(135deg, #f59e0b, #d97706);
      }

      &.error {
        background: linear-gradient(135deg, #ef4444, #dc2626);
      }

      &.debug {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
      }
    }

    h3 {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0;
    }
  }

  .card-content {
    .metric-value {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .metric-details {
      span {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }
    }
  }
}

// 控制面板样式
.control-section {
  margin-bottom: var(--spacing-xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }
}

.control-panel {
  padding: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;

  .controls-left {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    flex: 1;
  }

  .filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    label {
      font-weight: 500;
      color: var(--text-primary);
      white-space: nowrap;
    }

    select, input {
      padding: var(--spacing-sm) var(--spacing-md);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-md);
      background: var(--bg-secondary);
      color: var(--text-primary);
      font-size: 0.9rem;

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .search-group {
    .search-input {
      min-width: 200px;
      padding: var(--spacing-sm) var(--spacing-md);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-md);
      background: var(--bg-secondary);
      color: var(--text-primary);

      &::placeholder {
        color: var(--text-secondary);
      }

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .controls-right {
    display: flex;
    gap: var(--spacing-md);

    .btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm) var(--spacing-md);
      border: none;
      border-radius: var(--border-radius-md);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      .btn-icon {
        font-size: 1rem;
      }

      &.btn-primary {
        background: var(--primary-color);
        color: white;

        &:hover {
          background: var(--primary-hover);
        }
      }

      &.btn-secondary {
        background: var(--bg-secondary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);

        &:hover {
          background: var(--bg-tertiary);
        }
      }

      &.btn-danger {
        background: #ef4444;
        color: white;

        &:hover {
          background: #dc2626;
        }
      }
    }
  }
}