<template>
  <div class="signal-output-view">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">信号输出状态管理</h1>
        <p class="page-subtitle">监控和配置1PPS、10MHz、TOD等信号输出</p>
      </div>
      <div class="header-actions">
        <button class="btn btn-secondary" @click="refreshData" :disabled="loading">
          <RefreshIcon class="btn-icon" />
          刷新数据
        </button>
        <button class="btn btn-primary" @click="showConfigModal = true">
          <SettingsIcon class="btn-icon" />
          全局配置
        </button>
      </div>
    </div>

    <!-- 信号输出状态概览 -->
    <div class="status-overview">
      <div class="overview-card" v-for="summary in statusSummary" :key="summary.type">
        <div class="card-icon" :class="summary.status">
          <component :is="summary.icon" />
        </div>
        <div class="card-content">
          <h3>{{ summary.title }}</h3>
          <p class="status-text" :class="summary.status">{{ summary.statusText }}</p>
          <div class="metrics">
            <span class="metric">{{ summary.enabled }}/{{ summary.total }} 已启用</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 信号输出状态表格 -->
    <div class="signal-table-section">
      <div class="section-header">
        <h2>信号输出详情</h2>
        <div class="table-controls">
          <div class="filter-group">
            <select v-model="statusFilter" class="filter-select">
              <option value="">全部状态</option>
              <option value="normal">正常</option>
              <option value="warning">警告</option>
              <option value="error">故障</option>
              <option value="disabled">禁用</option>
    