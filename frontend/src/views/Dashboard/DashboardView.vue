<template>
  <div class="dashboard">
    <!-- 系统状态提示栏 -->
    <div class="dashboard-header">
      <div class="system-alert" v-if="!systemStore.isConnected">
        <div class="alert-content">
          <span class="alert-icon pulse">⚠️</span>
          <span class="alert-text">WebSocket连接异常，正在重连...</span>
        </div>
      </div>
      
      <div class="system-alert success" v-else-if="systemStore.isHealthy">
        <div class="alert-content">
          <span class="alert-icon">✅</span>
          <span class="alert-text">系统运行正常，所有服务状态良好</span>
        </div>
      </div>
    </div>

    <!-- 中央时钟显示区域 -->
    <div class="central-clock-section">
      <CentralClock />
    </div>

    <!-- 系统状态卡片网格 -->
    <div class="status-grid">
      <!-- 主控字钟状态 -->
      <MasterClockCard />
      
      <!-- GNSS卫星状态 -->
      <GnssStatusCard />
      
      <!-- PTP/NTP服务状态 -->
      <PtpNtpStatusCard />
      
      <!-- 系统资源状态 -->
      <SystemResourcesCard />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useSystemStore } from '@/stores/system'

// 导入组件
import CentralClock from '@/components/Dashboard/CentralClock.vue'
import MasterClockCard from '@/components/Dashboard/MasterClockCard.vue'
import GnssStatusCard from '@/components/Dashboard/GnssStatusCard.vue'
import PtpNtpStatusCard from '@/components/Dashboard/PtpNtpStatusCard.vue'
import SystemResourcesCard from '@/components/Dashboard/SystemResourcesCard.vue'

const systemStore = useSystemStore()

onMounted(() => {
  // 初始化系统状态和WebSocket连接
  systemStore.initialize()
})

onUnmounted(() => {
  // 清理资源
  systemStore.cleanup()
})
</script>

<style lang="scss" scoped>
.dashboard {
  min-height: 100%;
  padding: var(--spacing-lg);
  animation: fadeIn 0.5s ease-out;
}

.dashboard-header {
  margin-bottom: var(--spacing-lg);
  
  .system-alert {
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    animation: slideDown 0.3s ease-out;
    
    &:not(.success) {
      background: rgba(245, 158, 11, 0.2);
      border: 1px solid rgba(245, 158, 11, 0.3);
      
      .alert-content {
        color: #fbbf24;
      }
    }
    
    &.success {
      background: rgba(16, 185, 129, 0.2);
      border: 1px solid rgba(16, 185, 129, 0.3);
      
      .alert-content {
        color: var(--status-success);
      }
    }
    
    .alert-content {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      .alert-icon {
        font-size: 1.2rem;
        
        &.pulse {
          animation: pulse 2s infinite;
        }
      }
      
      .alert-text {
        font-weight: 500;
      }
    }
  }
}

.central-clock-section {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-2xl);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: var(--spacing-lg);
  
  > * {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
    
    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
  }
}

// 动画定义
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { 
    opacity: 0; 
    transform: translateY(-20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes fadeInUp {
  from { 
    opacity: 0; 
    transform: translateY(30px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    padding: var(--spacing-md);
  }
  
  .status-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: var(--spacing-sm);
  }
  
  .central-clock-section {
    margin-bottom: var(--spacing-xl);
  }
}
</style>