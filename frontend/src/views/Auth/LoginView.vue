<template>
  <div class="login-view">
    <div class="login-container">
      <div class="login-card glass-card">
        <div class="login-header">
          <div class="logo">⚡</div>
          <h1>高精度授时服务器系统</h1>
          <p>请登录以访问管理界面</p>
        </div>
        
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="username">用户名</label>
            <input
              id="username"
              v-model="loginForm.username"
              type="text"
              placeholder="请输入用户名"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="password">密码</label>
            <input
              id="password"
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              required
            />
          </div>
          
          <button type="submit" class="login-button" :disabled="loading">
            <span v-if="loading">登录中...</span>
            <span v-else>登录</span>
          </button>
          
          <div v-if="error" class="error-message">
            {{ error }}
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loginForm = ref({
  username: '',
  password: ''
})

const loading = ref(false)
const error = ref('')

const handleLogin = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await authStore.login(loginForm.value.username, loginForm.value.password)
    router.push('/')
  } catch (err: any) {
    error.value = err.response?.data?.error?.message || authStore.error || '登录失败，请重试'
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  padding: var(--spacing-lg);
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  padding: var(--spacing-2xl);
  text-align: center;
}

.login-header {
  margin-bottom: var(--spacing-2xl);
  
  .logo {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    color: var(--status-info);
  }
  
  h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.login-form {
  .form-group {
    margin-bottom: var(--spacing-lg);
    text-align: left;
    
    label {
      display: block;
      margin-bottom: var(--spacing-sm);
      color: var(--text-primary);
      font-weight: 500;
    }
    
    input {
      width: 100%;
      padding: var(--spacing-md);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-md);
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      font-size: 1rem;
      
      &::placeholder {
        color: var(--text-secondary);
      }
      
      &:focus {
        outline: none;
        border-color: var(--status-info);
        background: rgba(255, 255, 255, 0.15);
      }
    }
  }
  
  .login-button {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--status-info);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #2563eb;
      transform: translateY(-1px);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .error-message {
    margin-top: var(--spacing-md);
    padding: var(--spacing-sm);
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: var(--radius-sm);
    color: var(--status-error);
    font-size: 0.9rem;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-view {
    padding: var(--spacing-md);
  }
  
  .login-card {
    padding: var(--spacing-xl);
  }
  
  .login-header {
    h1 {
      font-size: 1.3rem;
    }
  }
}
</style>