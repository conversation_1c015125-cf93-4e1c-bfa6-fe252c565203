<template>
  <div class="performance-view">
    <div class="page-header">
      <h1>性能监控</h1>
      <p>监控系统性能指标和信号输出状态</p>
    </div>
    
    <!-- 信号输出状态 -->
    <div class="signal-output-grid">
      <!-- 输出通道状态 -->
      <div class="status-card glass-card">
        <h3>输出通道状态</h3>
        <div class="channel-list">
          <div v-for="channel in outputChannels" :key="channel.id" class="channel-item">
            <div class="channel-info">
              <span class="channel-name">{{ channel.name }}</span>
              <span class="channel-type">{{ channel.type }}</span>
            </div>
            <div class="channel-status">
              <span class="status-indicator" :class="channel.status"></span>
              <span class="status-text">{{ getStatusText(channel.status) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 信号质量监控 -->
      <div class="quality-card glass-card">
        <h3>信号质量监控</h3>
        <div class="quality-metrics">
          <div class="metric-item">
            <span class="metric-label">频率稳定度</span>
            <span class="metric-value">{{ signalQuality.frequency }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">相位噪声</span>
            <span class="metric-value">{{ signalQuality.phaseNoise }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">幅度稳定度</span>
            <span class="metric-value">{{ signalQuality.amplitude }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">谐波失真</span>
            <span class="metric-value">{{ signalQuality.distortion }}</span>
          </div>
        </div>
      </div>

      <!-- 输出配置 -->
      <div class="config-card glass-card">
        <h3>输出配置</h3>
        <div class="config-list">
          <div class="config-item">
            <label>输出频率</label>
            <select v-model="outputConfig.frequency">
              <option value="1pps">1 PPS</option>
              <option value="10mhz">10 MHz</option>
              <option value="100mhz">100 MHz</option>
              <option value="custom">自定义</option>
            </select>
          </div>
          <div class="config-item">
            <label>输出幅度</label>
            <input type="range" v-model="outputConfig.amplitude" min="0" max="100" />
            <span>{{ outputConfig.amplitude }}%</span>
          </div>
          <div class="config-item">
            <label>输出阻抗</label>
            <select v-model="outputConfig.impedance">
              <option value="50">50Ω</option>
              <option value="75">75Ω</option>
              <option value="high">高阻抗</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 性能统计 -->
      <div class="stats-card glass-card">
        <h3>性能统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-value">{{ performanceStats.uptime }}</span>
            <span class="stat-label">运行时间</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ performanceStats.accuracy }}</span>
            <span class="stat-label">时间精度</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ performanceStats.stability }}</span>
            <span class="stat-label">频率稳定度</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ performanceStats.temperature }}</span>
            <span class="stat-label">工作温度</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'

// 输出通道数据
const outputChannels = ref([
  { id: 1, name: '通道1', type: '1PPS', status: 'active' },
  { id: 2, name: '通道2', type: '10MHz', status: 'active' },
  { id: 3, name: '通道3', type: 'TOD', status: 'inactive' },
  { id: 4, name: '通道4', type: 'NTP', status: 'active' }
])

// 信号质量数据
const signalQuality = reactive({
  frequency: '±1×10⁻¹²',
  phaseNoise: '-140 dBc/Hz',
  amplitude: '±0.1%',
  distortion: '<0.01%'
})

// 输出配置
const outputConfig = reactive({
  frequency: '1pps',
  amplitude: 80,
  impedance: '50'
})

// 性能统计
const performanceStats = reactive({
  uptime: '15天 8小时',
  accuracy: '±50ns',
  stability: '1×10⁻¹²',
  temperature: '25.3°C'
})

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active': return '正常'
    case 'inactive': return '停用'
    case 'error': return '错误'
    default: return '未知'
  }
}

// 模拟数据更新
let updateInterval: number | null = null

onMounted(() => {
  updateInterval = setInterval(() => {
    // 更新温度
    performanceStats.temperature = (25 + Math.random() * 2).toFixed(1) + '°C'

    // 随机更新通道状态
    if (Math.random() < 0.1) {
      const randomChannel = outputChannels.value[Math.floor(Math.random() * outputChannels.value.length)]
      randomChannel.status = Math.random() > 0.8 ? 'inactive' : 'active'
    }
  }, 2000) as unknown as number
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style lang="scss" scoped>
.performance-view {
  padding: var(--spacing-lg);
}

.page-header {
  margin-bottom: var(--spacing-xl);
  
  h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
  }
  
  p {
    color: var(--text-secondary);
    font-size: 1.1rem;
  }
}

.signal-output-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg);

  h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.channel-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.channel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
  }
}

.channel-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .channel-name {
    color: var(--text-primary);
    font-weight: 500;
  }

  .channel-type {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.channel-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.active {
      background: #4ade80;
      box-shadow: 0 0 8px rgba(74, 222, 128, 0.5);
    }

    &.inactive {
      background: #6b7280;
    }

    &.error {
      background: #f87171;
      box-shadow: 0 0 8px rgba(248, 113, 113, 0.5);
    }
  }

  .status-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;

  .metric-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  .metric-value {
    color: var(--text-primary);
    font-weight: 500;
    font-family: 'Monaco', 'Menlo', monospace;
  }
}

.config-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);

  label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
  }

  select, input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: var(--spacing-sm);
    color: var(--text-primary);
    font-size: 0.9rem;

    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }
  }

  input[type="range"] {
    -webkit-appearance: none;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 16px;
      height: 16px;
      background: #667eea;
      border-radius: 50%;
      cursor: pointer;
    }
  }

  span {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
  }

  .stat-value {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
  }

  .stat-label {
    color: var(--text-secondary);
    font-size: 0.8rem;
  }
}
</style>