<template>
  <div id="app" class="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()

onMounted(() => {
  // 初始化系统状态
  systemStore.initialize()
})
</script>

<style lang="scss">
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1d3a 0%, #2d1b69 100%);
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>