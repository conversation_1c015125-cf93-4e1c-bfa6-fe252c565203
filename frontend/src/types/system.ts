// 系统状态类型定义

export type SystemStatusType = 'normal' | 'warning' | 'error' | 'maintenance' | 'unknown'

export interface SystemStatus {
  timestamp: string
  uptime: number
  sync: {
    status: SystemStatusType
    currentSource: string
    accuracy: string
    lastSync: string
  }
  gnss: {
    status: SystemStatusType
    visibleSatellites: number
    trackingSatellites: number
    signalQuality: string
    accuracy: string
    lastUpdate: string
  }
  ptp: {
    status: SystemStatusType
    mode: 'grandmaster' | 'slave' | 'passive'
    clients: number
    accuracy: string
    networkDelay: string
    clockClass: number
  }
  ntp: {
    status: SystemStatusType
    stratum: number
    clients: number
    averageDelay: string
    lastSync: string
  }
  clock: {
    status: SystemStatusType
    frequency: string
    temperature: number
    uptime: string
    stability: string
  }
  resources: {
    cpu: {
      usage: number
      temperature: number
    }
    memory: {
      usage: number
      total: number
      available: number
    }
    network: {
      throughput: string
      connections: number
    }
    storage: {
      usage: number
      total: number
      available: number
    }
  }
}

export interface TimeSourceInfo {
  gnss: {
    receiver: {
      model: string
      firmware: string
      serialNumber: string
    }
    antenna: {
      type: string
      location: string
      cableLength: number
    }
    satellites: {
      gps: number
      glonass: number
      galileo: number
      beidou: number
    }
    position: {
      latitude: number
      longitude: number
      altitude: number
      accuracy: number
    }
  }
  atomic: {
    type: 'rubidium' | 'cesium' | 'hydrogen'
    model: string
    serialNumber: string
    temperature: number
    frequency: string
    aging: number
    learningStatus: string
  }
  external: {
    pps: {
      enabled: boolean
      source: string
      quality: string
    }
    freq10mhz: {
      enabled: boolean
      source: string
      quality: string
    }
  }
}

// 时间源配置类型定义
export interface GnssConfig {
  enabled: boolean
  device: string
  baudRate: number
  dataBits: 8 | 7 | 6 | 5
  stopBits: 1 | 2
  parity: 'none' | 'odd' | 'even'
  receiver: {
    type: 'ublox' | 'trimble' | 'novatel' | 'generic'
    model: string
    firmware: string
  }
  antenna: {
    type: 'active' | 'passive'
    cableLength: number
    cableDelay: number
    location: string
  }
  satellites: {
    gps: boolean
    glonass: boolean
    galileo: boolean
    beidou: boolean
    qzss: boolean
    irnss: boolean
  }
  positioning: {
    mode: 'auto' | 'static' | 'kinematic'
    elevation: number
    pdop: number
    hdop: number
    vdop: number
  }
  timing: {
    ppsEnabled: boolean
    ppsPolarity: 'rising' | 'falling'
    ppsWidth: number
    ppsOffset: number
    timePulseEnabled: boolean
    timePulseFreq: number
  }
  advanced: {
    jamming: boolean
    spoofing: boolean
    multipath: boolean
    ionospheric: boolean
    tropospheric: boolean
  }
}

export interface PtpConfig {
  enabled: boolean
  interface: string
  domain: number
  priority1: number
  priority2: number
  clockClass: number
  clockAccuracy: number
  offsetScaledLogVariance: number
  timeSource: 'atomic' | 'gps' | 'terrestrial' | 'ptp' | 'ntp' | 'hand_set' | 'other' | 'internal'
  network: {
    transport: 'udp4' | 'udp6' | 'l2'
    delayMechanism: 'e2e' | 'p2p'
    networkTransport: 'UDPv4' | 'UDPv6' | 'L2'
    hwTimestamp: boolean
    twoStep: boolean
  }
  timing: {
    announceInterval: number
    syncInterval: number
    delayReqInterval: number
    announceTimeout: number
    syncTimeout: number
  }
  grandmaster: {
    identity: string
    utcOffset: number
    utcValid: boolean
    leap59: boolean
    leap61: boolean
    timeTraceable: boolean
    frequencyTraceable: boolean
    ptpTimescale: boolean
    timeSource: number
  }
  servo: {
    type: 'pi' | 'linreg' | 'ntpshm' | 'nullf'
    piProportional: number
    piIntegral: number
    piMaxFreq: number
    firstStepThreshold: number
    stepThreshold: number
  }
  monitoring: {
    enabled: boolean
    logLevel: number
    logFile: string
    statisticsEnabled: boolean
    metricsEnabled: boolean
  }
}

export interface NtpConfig {
  enabled: boolean
  stratum: number
  referenceId: string
  server: {
    port: number
    bindAddress: string
    maxClients: number
    clientTimeout: number
  }
  access: {
    allowedNetworks: string[]
    deniedNetworks: string[]
    restrictDefault: boolean
    kod: boolean
    limited: boolean
    nomodify: boolean
    nopeer: boolean
    noquery: boolean
    notrap: boolean
    notrust: boolean
  }
  refclock: {
    phc: {
      enabled: boolean
      device: string
      poll: number
      dpoll: number
      offset: number
      prefer: boolean
      trust: boolean
      require: boolean
      tai: boolean
      filter: number
      precision: number
      refid: string
    }
    shm: {
      enabled: boolean
      unit: number
      poll: number
      dpoll: number
      offset: number
      delay: number
      refid: string
      precision: number
      noselect: boolean
    }
    pps: {
      enabled: boolean
      device: string
      poll: number
      dpoll: number
      lock: string
      rate: number
      refid: string
      precision: number
    }
  }
  chrony: {
    driftFile: string
    logDir: string
    maxUpdateSkew: number
    makestep: {
      threshold: number
      limit: number
    }
    rtcsync: boolean
    hwclockfile: string
    local: {
      stratum: number
      orphan: boolean
      distance: number
    }
    smoothtime: {
      maxFreq: number
      maxWander: number
      leapOnly: boolean
    }
    leapsectz: string
    leapsecmode: 'system' | 'slew' | 'step' | 'ignore'
  }
  monitoring: {
    tracking: boolean
    measurements: boolean
    statistics: boolean
    rtc: boolean
    refclocks: boolean
    tempcomp: boolean
    selection: boolean
    rawmeasurements: boolean
  }
}

export interface AtomicClockConfig {
  enabled: boolean
  type: 'rubidium' | 'cesium' | 'hydrogen'
  device: {
    interface: 'spi' | 'i2c' | 'uart' | 'usb'
    address: string
    baudRate?: number
    timeout: number
  }
  control: {
    warmupTime: number
    lockTimeout: number
    disciplineMode: 'auto' | 'manual' | 'holdover'
    frequencyControl: boolean
    temperatureControl: boolean
  }
  monitoring: {
    temperature: {
      enabled: boolean
      interval: number
      lowThreshold: number
      highThreshold: number
      criticalThreshold: number
    }
    frequency: {
      enabled: boolean
      interval: number
      tolerance: number
      agingRate: number
    }
    phase: {
      enabled: boolean
      interval: number
      tolerance: number
    }
    voltage: {
      enabled: boolean
      interval: number
      lowThreshold: number
      highThreshold: number
    }
  }
  learning: {
    enabled: boolean
    duration: number
    samples: number
    convergenceThreshold: number
    agingCompensation: boolean
    temperatureCompensation: boolean
    saveInterval: number
    dataRetention: number
  }
  holdover: {
    enabled: boolean
    maxDuration: number
    accuracyThreshold: number
    stabilityThreshold: number
    exitConditions: {
      gnssLock: boolean
      ptpSync: boolean
      manualOverride: boolean
    }
  }
  alarms: {
    temperatureAlarm: boolean
    frequencyAlarm: boolean
    phaseAlarm: boolean
    voltageAlarm: boolean
    lockLossAlarm: boolean
    agingAlarm: boolean
  }
}

export interface TimeSourcePriority {
  sources: Array<{
    id: string
    name: string
    type: 'gnss' | 'atomic' | 'ptp' | 'ntp' | 'external_pps' | 'external_10mhz' | 'rtc'
    priority: number
    enabled: boolean
    qualityThreshold: number
    switchingDelay: number
    holdoverCapable: boolean
  }>
  switching: {
    mode: 'automatic' | 'manual' | 'priority_only'
    hysteresis: number
    minSwitchInterval: number
    maxSwitchInterval: number
    qualityWeight: number
    stabilityWeight: number
    availabilityWeight: number
  }
  failover: {
    enabled: boolean
    timeout: number
    retryInterval: number
    maxRetries: number
    fallbackSource: string
    emergencyMode: boolean
  }
  validation: {
    crossCheck: boolean
    maxOffset: number
    maxFreqError: number
    minSources: number
    consensusThreshold: number
  }
}

export interface ConfigBackup {
  id: string
  name: string
  description: string
  timestamp: string
  version: string
  size: number
  checksum: string
  configs: {
    gnss: GnssConfig
    ptp: PtpConfig
    ntp: NtpConfig
    atomic: AtomicClockConfig
    priority: TimeSourcePriority
    system: SystemConfiguration
  }
  metadata: {
    createdBy: string
    platform: string
    systemVersion: string
    configVersion: string
    tags: string[]
  }
}

export interface ConfigValidation {
  valid: boolean
  errors: Array<{
    field: string
    message: string
    severity: 'error' | 'warning' | 'info'
    suggestion?: string
  }>
  warnings: Array<{
    field: string
    message: string
    impact: string
    recommendation?: string
  }>
  summary: {
    totalErrors: number
    totalWarnings: number
    criticalIssues: number
    configScore: number
  }
}

export interface SignalOutput {
  pps: {
    enabled: boolean
    frequency: string
    voltage: number
    accuracy: string
    load: string
    status: SystemStatusType
  }
  freq10mhz: {
    enabled: boolean
    frequency: string
    voltage: number
    accuracy: string
    load: string
    status: SystemStatusType
  }
  tod: {
    enabled: boolean
    format: string
    baudRate: number
    voltage: number
    accuracy: string
    status: SystemStatusType
  }
  custom: Array<{
    id: string
    name: string
    enabled: boolean
    frequency: string
    voltage: number
    status: SystemStatusType
  }>
}

export interface SystemAlert {
  id: string
  timestamp: string
  type: 'info' | 'warning' | 'error' | 'critical'
  source: string
  message: string
  details?: string
  acknowledged: boolean
  acknowledgedBy?: string
  acknowledgedAt?: string
}

export interface SystemEvent {
  id: string
  timestamp: string
  type: string
  source: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  data?: Record<string, any>
}

export interface PerformanceMetric {
  timestamp: string
  metric: string
  value: number
  unit: string
  source: string
}

export interface SystemConfiguration {
  network: {
    interfaces: Array<{
      name: string
      ip: string
      netmask: string
      gateway: string
      dns: string[]
      enabled: boolean
    }>
  }
  time: {
    timezone: string
    ntpServers: string[]
    ptpDomain: number
    clockClass: number
  }
  security: {
    httpsEnabled: boolean
    certificatePath: string
    allowedIPs: string[]
    sessionTimeout: number
  }
  logging: {
    level: 'debug' | 'info' | 'warning' | 'error'
    maxSize: number
    retention: number
    remoteLogging: boolean
    remoteServer?: string
  }
  alerts: {
    emailEnabled: boolean
    emailServer?: string
    recipients: string[]
    thresholds: {
      temperatureHigh: number
      temperatureLow: number
      accuracyThreshold: string
      syncLossTimeout: number
    }
  }
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  timestamp: string
}

export interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: string
  }
  timestamp: string
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: string
  payload: any
  timestamp: string
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  role: 'viewer' | 'operator' | 'administrator'
  lastLogin: string
  active: boolean
  permissions: string[]
}

export interface AuthToken {
  accessToken?: string
  token?: string  // 支持API返回的token字段
  refreshToken: string
  expiresIn?: number
  tokenType?: 'Bearer'
}

// 日志相关类型
export interface LogEntry {
  timestamp: string
  level: 'debug' | 'info' | 'warning' | 'error' | 'critical'
  component: string
  message: string
  details?: Record<string, any>
}

export interface LogQuery {
  startTime?: string
  endTime?: string
  level?: string
  component?: string
  search?: string
  limit?: number
  offset?: number
}