import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 导入页面组件
import Layout from '@/components/Layout/MainLayout.vue'
import Dashboard from '@/views/Dashboard/DashboardView.vue'
import TimeSource from '@/views/TimeSource/TimeSourceView.vue'
import Performance from '@/views/Performance/PerformanceView.vue'
import Config from '@/views/Config/ConfigView.vue'
import Users from '@/views/Users/<USER>'
import Logs from '@/views/Logs/LogsView.vue'
import Login from '@/views/Auth/LoginView.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '系统状态仪表盘', icon: 'dashboard' }
      },
      {
        path: '/gnss',
        name: 'GNSS',
        component: () => import('@/views/TimeSource/GnssView.vue'),
        meta: { title: 'GNSS接收', icon: 'satellite' }
      },
      {
        path: '/ptp',
        name: 'PTP',
        component: () => import('@/views/TimeSource/PtpView.vue'),
        meta: { title: 'PTP设置', icon: 'network' }
      },
      {
        path: '/ntp',
        name: 'NTP',
        component: () => import('@/views/TimeSource/NtpView.vue'),
        meta: { title: 'NTP设置', icon: 'clock' }
      },
      {
        path: '/tod',
        name: 'TOD',
        component: () => import('@/views/TimeSource/TodView.vue'),
        meta: { title: 'TOD设置', icon: 'time' }
      },
      {
        path: '/performance',
        name: 'Performance',
        component: Performance,
        meta: { title: '信号输出状态', icon: 'chart' }
      },
      {
        path: '/config',
        name: 'Config',
        component: Config,
        meta: { title: '系统资源', icon: 'settings' }
      },
      {
        path: '/users',
        name: 'Users',
        component: Users,
        meta: { title: '用户安全', icon: 'users' }
      },
      {
        path: '/logs',
        name: 'Logs',
        component: Logs,
        meta: { title: '系统日志', icon: 'logs' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const authStore = useAuthStore()
  
  // 初始化认证状态
  if (!authStore.user && authStore.token) {
    try {
      await authStore.initialize()
    } catch (error) {
      console.error('Auth initialization failed:', error)
    }
  }
  
  if (to.meta.requiresAuth !== false && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router