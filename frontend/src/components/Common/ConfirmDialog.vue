<template>
  <div class="confirm-dialog-overlay" @click="handleOverlayClick">
    <div class="confirm-dialog glass-card" @click.stop>
      <div class="dialog-header">
        <div class="dialog-icon" :class="getIconClass()">
          {{ getIcon() }}
        </div>
        <h3>{{ title }}</h3>
      </div>

      <div class="dialog-content">
        <p>{{ message }}</p>
        
        <div v-if="details" class="dialog-details">
          <div class="details-toggle">
            <button @click="showDetails = !showDetails" class="toggle-button">
              {{ showDetails ? '隐藏详情' : '显示详情' }}
              <span class="toggle-icon">{{ showDetails ? '▲' : '▼' }}</span>
            </button>
          </div>
          
          <div v-show="showDetails" class="details-content">
            <pre>{{ details }}</pre>
          </div>
        </div>

        <div v-if="requireConfirmation" class="confirmation-input">
          <label>
            请输入 <strong>{{ confirmationText }}</strong> 以确认操作：
          </label>
          <input
            v-model="confirmationInput"
            type="text"
            :placeholder="confirmationText"
            class="confirmation-field"
            @keyup.enter="handleConfirm"
          />
        </div>
      </div>

      <div class="dialog-actions">
        <button @click="handleCancel" class="btn-cancel">
          {{ cancelText }}
        </button>
        <button
          @click="handleConfirm"
          class="btn-confirm"
          :class="getConfirmButtonClass()"
          :disabled="!canConfirm"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  title: string
  message: string
  details?: string
  type?: 'info' | 'warning' | 'danger' | 'success'
  confirmText?: string
  cancelText?: string
  requireConfirmation?: boolean
  confirmationText?: string
}

interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'warning',
  confirmText: '确认',
  cancelText: '取消',
  requireConfirmation: false,
  confirmationText: 'CONFIRM'
})

const emit = defineEmits<Emits>()

const showDetails = ref(false)
const confirmationInput = ref('')

// 计算属性
const canConfirm = computed(() => {
  if (!props.requireConfirmation) return true
  return confirmationInput.value === props.confirmationText
})

// 方法
const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    handleCancel()
  }
}

const handleConfirm = () => {
  if (canConfirm.value) {
    emit('confirm')
  }
}

const handleCancel = () => {
  emit('cancel')
}

const getIcon = () => {
  const icons = {
    info: 'ℹ️',
    warning: '⚠️',
    danger: '🚨',
    success: '✅'
  }
  return icons[props.type]
}

const getIconClass = () => {
  return `icon-${props.type}`
}

const getConfirmButtonClass = () => {
  const classes = {
    info: 'btn-info',
    warning: 'btn-warning',
    danger: 'btn-danger',
    success: 'btn-success'
  }
  return classes[props.type]
}
</script>

<style lang="scss" scoped>
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.confirm-dialog {
  width: 100%;
  max-width: 500px;
  padding: var(--spacing-xl);
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  
  .dialog-icon {
    font-size: 2rem;
    flex-shrink: 0;
    
    &.icon-info {
      color: var(--status-info);
    }
    
    &.icon-warning {
      color: var(--status-warning);
    }
    
    &.icon-danger {
      color: var(--status-error);
    }
    
    &.icon-success {
      color: var(--status-success);
    }
  }
  
  h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
  }
}

.dialog-content {
  margin-bottom: var(--spacing-xl);
  
  p {
    color: var(--text-primary);
    line-height: 1.5;
    margin-bottom: var(--spacing-lg);
  }
  
  .dialog-details {
    margin-bottom: var(--spacing-lg);
    
    .details-toggle {
      margin-bottom: var(--spacing-md);
      
      .toggle-button {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: none;
        border: none;
        color: var(--status-info);
        cursor: pointer;
        font-size: 0.9rem;
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
        transition: all 0.2s ease;
        
        &:hover {
          background: rgba(59, 130, 246, 0.1);
        }
        
        .toggle-icon {
          font-size: 0.8rem;
          transition: transform 0.2s ease;
        }
      }
    }
    
    .details-content {
      background: rgba(0, 0, 0, 0.3);
      border-radius: var(--radius-md);
      padding: var(--spacing-md);
      
      pre {
        color: var(--text-primary);
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-word;
        margin: 0;
      }
    }
  }
  
  .confirmation-input {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    
    label {
      display: block;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
      font-weight: 500;
      
      strong {
        color: var(--status-error);
        font-family: monospace;
      }
    }
    
    .confirmation-field {
      width: 100%;
      padding: var(--spacing-md);
      border: 1px solid rgba(239, 68, 68, 0.3);
      border-radius: var(--radius-md);
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      font-family: monospace;
      
      &::placeholder {
        color: var(--text-secondary);
      }
      
      &:focus {
        outline: none;
        border-color: var(--status-error);
        background: rgba(255, 255, 255, 0.15);
      }
    }
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  
  button {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .btn-cancel {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
  
  .btn-confirm {
    color: white;
    
    &.btn-info {
      background: var(--status-info);
      
      &:hover:not(:disabled) {
        background: #2563eb;
      }
    }
    
    &.btn-warning {
      background: var(--status-warning);
      
      &:hover:not(:disabled) {
        background: #d97706;
      }
    }
    
    &.btn-danger {
      background: var(--status-error);
      
      &:hover:not(:disabled) {
        background: #dc2626;
      }
    }
    
    &.btn-success {
      background: var(--status-success);
      
      &:hover:not(:disabled) {
        background: #16a34a;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .confirm-dialog-overlay {
    padding: var(--spacing-md);
  }
  
  .confirm-dialog {
    padding: var(--spacing-lg);
  }
  
  .dialog-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }
  
  .dialog-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
}
</style>