<template>
  <div class="time-source-priority">
    <div class="config-header">
      <h3>时间源优先级配置</h3>
      <div class="header-actions">
        <button 
          class="btn btn-secondary" 
          @click="showSwitchHistory = !showSwitchHistory"
        >
          切换历史
        </button>
        <button 
          class="btn btn-info" 
          @click="manualSwitch = !manualSwitch"
        >
          手动切换
        </button>
      </div>
    </div>

    <div class="config-content">
      <!-- 时间源列表 -->
      <div class="config-section">
        <h4>时间源优先级</h4>
        <div class="sources-list">
          <div 
            v-for="(source, index) in config.sources" 
            :key="source.id"
            class="source-item"
            :class="{ 'active': source.enabled, 'current': isCurrentSource(source.id) }"
          >
            <div class="source-header">
              <div class="source-info">
                <div class="source-icon">
                  <component :is="getSourceIcon(source.type)" />
                </div>
                <div class="source-details">
                  <h5>{{ source.name }}</h5>
                  <span class="source-type">{{ getSourceTypeName(source.type) }}</span>
                </div>
              </div>
              <div class="source-controls">
                <div class="priority-control">
                  <label>优先级</label>
                  <input 
                    type="number" 
                    v-model.number="source.priority" 
                    min="1" 
                    max="100"
                    @input="onConfigChange"
                  >
                </div>
                <div class="toggle-switch">
                  <input 
                    type="checkbox" 
                    :id="`source-${source.id}`" 
                    v-model="source.enabled"
                    @change="onConfigChange"
                  >
                  <label :for="`source-${source.id}`"></label>
                </div>
                <div class="move-buttons">
                  <button 
                    class="btn btn-sm btn-secondary" 
                    @click="moveSourceUp(index)"
                    :disabled="index === 0"
                  >
                    ↑
                  </button>
                  <button 
                    class="btn btn-sm btn-secondary" 
                    @click="moveSourceDown(index)"
                    :disabled="index === config.sources.length - 1"
                  >
                    ↓
                  </button>
                </div>
              </div>
            </div>
            
            <div class="source-config">
              <div class="config-grid">
                <div class="config-item">
                  <label>质量阈值</label>
                  <input 
                    type="number" 
                    v-model.number="source.qualityThreshold" 
                    min="0" 
                    max="100"
                    @input="onConfigChange"
                  >
                </div>
                <div class="config-item">
                  <label>切换延迟 (秒)</label>
                  <input 
                    type="number" 
                    v-model.number="source.switchingDelay" 
                    min="0" 
                    max="3600"
                    @input="onConfigChange"
                  >
                </div>
                <div class="config-item">
                  <label>守时能力</label>
                  <div class="toggle-switch">
                    <input 
                      type="checkbox" 
                      :id="`holdover-${source.id}`" 
                      v-model="source.holdoverCapable"
                      @change="onConfigChange"
                    >
                    <label :for="`holdover-${source.id}`"></label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 切换策略 -->
      <div class="config-section">
        <h4>切换策略</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>切换模式</label>
            <select v-model="config.switching.mode" @change="onConfigChange">
              <option value="automatic">自动切换</option>
              <option value="manual">手动切换</option>
              <option value="priority_only">仅优先级</option>
            </select>
          </div>

          <div class="form-group">
            <label>滞后时间 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.switching.hysteresis" 
              min="0" 
              max="3600"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最小切换间隔 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.switching.minSwitchInterval" 
              min="1" 
              max="3600"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最大切换间隔 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.switching.maxSwitchInterval" 
              min="1" 
              max="86400"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>质量权重</label>
            <input 
              type="number" 
              v-model.number="config.switching.qualityWeight" 
              min="0" 
              max="1" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>稳定性权重</label>
            <input 
              type="number" 
              v-model.number="config.switching.stabilityWeight" 
              min="0" 
              max="1" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>可用性权重</label>
            <input 
              type="number" 
              v-model.number="config.switching.availabilityWeight" 
              min="0" 
              max="1" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 故障切换 -->
      <div class="config-section">
        <h4>故障切换</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用故障切换</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="failover-enabled" 
                v-model="config.failover.enabled"
                @change="onConfigChange"
              >
              <label for="failover-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>超时时间 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.failover.timeout" 
              min="1" 
              max="3600"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>重试间隔 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.failover.retryInterval" 
              min="1" 
              max="3600"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最大重试次数</label>
            <input 
              type="number" 
              v-model.number="config.failover.maxRetries" 
              min="0" 
              max="100"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>备用时间源</label>
            <select v-model="config.failover.fallbackSource" @change="onConfigChange">
              <option value="">无</option>
              <option v-for="source in config.sources" :key="source.id" :value="source.id">
                {{ source.name }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label>紧急模式</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="emergency-mode" 
                v-model="config.failover.emergencyMode"
                @change="onConfigChange"
              >
              <label for="emergency-mode"></label>
            </div>
          </div>
        </div>
      </div>

      <!-- 验证设置 -->
      <div class="config-section">
        <h4>验证设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>交叉检查</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="cross-check" 
                v-model="config.validation.crossCheck"
                @change="onConfigChange"
              >
              <label for="cross-check"></label>
            </div>
          </div>

          <div class="form-group">
            <label>最大偏移 (ns)</label>
            <input 
              type="number" 
              v-model.number="config.validation.maxOffset" 
              min="1" 
              max="1000000"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最大频率误差 (ppm)</label>
            <input 
              type="number" 
              v-model.number="config.validation.maxFreqError" 
              min="0.001" 
              max="1000" 
              step="0.001"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最少时间源数</label>
            <input 
              type="number" 
              v-model.number="config.validation.minSources" 
              min="1" 
              max="10"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>一致性阈值</label>
            <input 
              type="number" 
              v-model.number="config.validation.consensusThreshold" 
              min="0.5" 
              max="1" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 手动切换面板 -->
    <div class="manual-switch-panel" v-if="manualSwitch">
      <h4>手动切换时间源</h4>
      <div class="switch-options">
        <div 
          v-for="source in availableSources" 
          :key="source.id"
          class="switch-option"
          :class="{ 'current': isCurrentSource(source.id) }"
          @click="switchToSource(source.id)"
        >
          <div class="option-icon">
            <component :is="getSourceIcon(source.type)" />
          </div>
          <div class="option-info">
            <span class="option-name">{{ source.name }}</span>
            <span class="option-status" :class="getSourceStatusClass(source)">
              {{ getSourceStatus(source) }}
            </span>
          </div>
          <div class="option-quality">
            <div class="quality-bar">
              <div 
                class="quality-fill" 
                :style="{ width: `${source.quality || 0}%` }"
              ></div>
            </div>
            <span class="quality-text">{{ source.quality || 0 }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 切换历史 -->
    <div class="switch-history" v-if="showSwitchHistory && switchHistory.length > 0">
      <h4>切换历史</h4>
      <div class="history-table">
        <table>
          <thead>
            <tr>
              <th>时间</th>
              <th>从</th>
              <th>到</th>
              <th>原因</th>
              <th>持续时间</th>
              <th>类型</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="record in switchHistory" :key="record.id">
              <td>{{ formatTime(record.timestamp) }}</td>
              <td>{{ getSourceName(record.fromSource) }}</td>
              <td>{{ getSourceName(record.toSource) }}</td>
              <td>{{ record.reason }}</td>
              <td>{{ formatDuration(record.duration) }}</td>
              <td>
                <span class="switch-type" :class="record.type">
                  {{ getSwitchTypeName(record.type) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { timeSourceApi } from '@/services/api'
import type { TimeSourcePriority } from '@/types/system'

const emit = defineEmits<{
  configChange: [config: TimeSourcePriority]
}>()

const config = reactive<TimeSourcePriority>({
  sources: [
    {
      id: 'gnss',
      name: 'GNSS接收器',
      type: 'gnss',
      priority: 1,
      enabled: true,
      qualityThreshold: 80,
      switchingDelay: 30,
      holdoverCapable: false
    },
    {
      id: 'atomic',
      name: '铷原子钟',
      type: 'atomic',
      priority: 2,
      enabled: true,
      qualityThreshold: 90,
      switchingDelay: 60,
      holdoverCapable: true
    },
    {
      id: 'ptp',
      name: 'PTP时钟',
      type: 'ptp',
      priority: 3,
      enabled: true,
      qualityThreshold: 70,
      switchingDelay: 10,
      holdoverCapable: false
    },
    {
      id: 'ntp',
      name: 'NTP服务器',
      type: 'ntp',
      priority: 4,
      enabled: true,
      qualityThreshold: 60,
      switchingDelay: 5,
      holdoverCapable: false
    },
    {
      id: 'external_pps',
      name: '外部PPS',
      type: 'external_pps',
      priority: 5,
      enabled: false,
      qualityThreshold: 85,
      switchingDelay: 15,
      holdoverCapable: false
    },
    {
      id: 'rtc',
      name: '高精度RTC',
      type: 'rtc',
      priority: 6,
      enabled: true,
      qualityThreshold: 30,
      switchingDelay: 1,
      holdoverCapable: false
    }
  ],
  switching: {
    mode: 'automatic',
    hysteresis: 30,
    minSwitchInterval: 60,
    maxSwitchInterval: 3600,
    qualityWeight: 0.4,
    stabilityWeight: 0.3,
    availabilityWeight: 0.3
  },
  failover: {
    enabled: true,
    timeout: 300,
    retryInterval: 60,
    maxRetries: 3,
    fallbackSource: 'rtc',
    emergencyMode: false
  },
  validation: {
    crossCheck: true,
    maxOffset: 1000,
    maxFreqError: 0.1,
    minSources: 2,
    consensusThreshold: 0.7
  }
})

const currentSource = ref<string>('gnss')
const switchHistory = ref<any[]>([])
const showSwitchHistory = ref(false)
const manualSwitch = ref(false)
const switching = ref(false)

const availableSources = computed(() => {
  return config.sources.filter(source => source.enabled)
})

const sourceTypeNames: Record<string, string> = {
  gnss: 'GNSS',
  atomic: '原子钟',
  ptp: 'PTP',
  ntp: 'NTP',
  external_pps: '外部PPS',
  external_10mhz: '外部10MHz',
  rtc: 'RTC'
}

const switchTypeNames: Record<string, string> = {
  automatic: '自动',
  manual: '手动',
  failover: '故障切换',
  emergency: '紧急'
}

const getSourceTypeName = (type: string): string => {
  return sourceTypeNames[type] || type.toUpperCase()
}

const getSwitchTypeName = (type: string): string => {
  return switchTypeNames[type] || type
}

const getSourceIcon = (type: string) => {
  // 这里应该返回对应的图标组件
  // 为了简化，返回文本表示
  const icons: Record<string, string> = {
    gnss: '🛰️',
    atomic: '⚛️',
    ptp: '🌐',
    ntp: '🕐',
    external_pps: '📡',
    external_10mhz: '📶',
    rtc: '⏰'
  }
  return icons[type] || '❓'
}

const isCurrentSource = (sourceId: string): boolean => {
  return currentSource.value === sourceId
}

const getSourceName = (sourceId: string): string => {
  const source = config.sources.find(s => s.id === sourceId)
  return source?.name || sourceId
}

const getSourceStatus = (source: any): string => {
  if (!source.enabled) return '已禁用'
  if (source.quality > 80) return '优秀'
  if (source.quality > 60) return '良好'
  if (source.quality > 40) return '一般'
  return '较差'
}

const getSourceStatusClass = (source: any): string => {
  if (!source.enabled) return 'disabled'
  if (source.quality > 80) return 'excellent'
  if (source.quality > 60) return 'good'
  if (source.quality > 40) return 'fair'
  return 'poor'
}

const formatTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString()
}

const formatDuration = (duration: number): string => {
  if (duration < 60) return `${duration}秒`
  if (duration < 3600) return `${Math.floor(duration / 60)}分钟`
  return `${Math.floor(duration / 3600)}小时`
}

const moveSourceUp = (index: number) => {
  if (index > 0) {
    const temp = config.sources[index]
    config.sources[index] = config.sources[index - 1]
    config.sources[index - 1] = temp
    onConfigChange()
  }
}

const moveSourceDown = (index: number) => {
  if (index < config.sources.length - 1) {
    const temp = config.sources[index]
    config.sources[index] = config.sources[index + 1]
    config.sources[index + 1] = temp
    onConfigChange()
  }
}

const switchToSource = async (sourceId: string) => {
  if (switching.value || isCurrentSource(sourceId)) return
  
  switching.value = true
  try {
    await timeSourceApi.switchTimeSource(sourceId)
    currentSource.value = sourceId
    // 重新加载切换历史
    await loadSwitchHistory()
  } catch (error) {
    console.error('切换时间源失败:', error)
  } finally {
    switching.value = false
  }
}

const onConfigChange = () => {
  emit('configChange', { ...config })
}

const loadConfig = async () => {
  try {
    const response = await timeSourceApi.getTimeSourcePriority()
    Object.assign(config, response.data.data)
  } catch (error) {
    console.error('加载时间源优先级配置失败:', error)
  }
}

const loadSwitchHistory = async () => {
  try {
    const response = await timeSourceApi.getTimeSourceSwitchHistory()
    switchHistory.value = response.data.data
  } catch (error) {
    console.error('加载切换历史失败:', error)
  }
}

onMounted(() => {
  loadConfig()
  loadSwitchHistory()
  
  // 定期更新切换历史
  const historyInterval = setInterval(() => {
    if (showSwitchHistory.value) {
      loadSwitchHistory()
    }
  }, 10000)
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(historyInterval)
  })
})
</script>

<style lang="scss" scoped>
.time-source-priority {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      color: var(--text-primary);
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  .config-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .sources-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .source-item {
    background: var(--card-bg);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    transition: all 0.3s ease;
    
    &.active {
      border-color: var(--primary-color);
    }
    
    &.current {
      background: rgba(59, 130, 246, 0.1);
      border-color: var(--primary-color);
      
      &::before {
        content: '当前';
        position: absolute;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        background: var(--primary-color);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: 0.8rem;
        font-weight: 500;
      }
    }
    
    position: relative;
  }
  
  .source-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
  }
  
  .source-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    .source-icon {
      font-size: 2rem;
    }
    
    .source-details {
      h5 {
        margin: 0;
        color: var(--text-primary);
        font-size: 1.1rem;
        font-weight: 600;
      }
      
      .source-type {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }
    }
  }
  
  .source-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    
    .priority-control {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xs);
      
      label {
        color: var(--text-secondary);
        font-size: 0.8rem;
      }
      
      input {
        width: 60px;
        padding: var(--spacing-xs);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        background: var(--input-bg);
        color: var(--text-primary);
        text-align: center;
      }
    }
    
    .move-buttons {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }
  }
  
  .source-config {
    .config-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
    }
    
    .config-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      
      label {
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input {
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
      }
    }
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    
    .form-group {
      display: flex;
      flex-direction: column;
      
      label {
        margin-bottom: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input, select {
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    
    input {
      opacity: 0;
      width: 0;
      height: 0;
      
      &:checked + label {
        background-color: var(--primary-color);
        
        &:before {
          transform: translateX(20px);
        }
      }
    }
    
    label {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--toggle-bg);
      border-radius: 24px;
      transition: all 0.3s ease;
      
      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
    }
  }
  
  .manual-switch-panel, .switch-history {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .switch-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
  }
  
  .switch-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--card-bg);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
    }
    
    &.current {
      border-color: var(--success-color);
      background: rgba(16, 185, 129, 0.1);
      cursor: default;
      
      &:hover {
        transform: none;
      }
    }
    
    .option-icon {
      font-size: 2rem;
    }
    
    .option-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      
      .option-name {
        color: var(--text-primary);
        font-weight: 600;
      }
      
      .option-status {
        font-size: 0.9rem;
        
        &.excellent { color: var(--success-color); }
        &.good { color: var(--info-color); }
        &.fair { color: var(--warning-color); }
        &.poor { color: var(--error-color); }
        &.disabled { color: var(--text-tertiary); }
      }
    }
    
    .option-quality {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-xs);
      
      .quality-bar {
        width: 60px;
        height: 8px;
        background: var(--progress-bg);
        border-radius: 4px;
        overflow: hidden;
        
        .quality-fill {
          height: 100%;
          background: var(--primary-color);
          transition: width 0.3s ease;
        }
      }
      
      .quality-text {
        font-size: 0.8rem;
        color: var(--text-secondary);
      }
    }
  }
  
  .history-table {
    overflow-x: auto;
    
    table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        padding: var(--spacing-sm);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }
      
      th {
        background: var(--card-bg);
        color: var(--text-secondary);
        font-weight: 600;
        font-size: 0.9rem;
      }
      
      td {
        color: var(--text-primary);
        font-size: 0.9rem;
      }
    }
  }
  
  .switch-type {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    
    &.automatic {
      background: rgba(59, 130, 246, 0.1);
      color: var(--primary-color);
    }
    
    &.manual {
      background: rgba(16, 185, 129, 0.1);
      color: var(--success-color);
    }
    
    &.failover {
      background: rgba(245, 158, 11, 0.1);
      color: var(--warning-color);
    }
    
    &.emergency {
      background: rgba(239, 68, 68, 0.1);
      color: var(--error-color);
    }
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.btn-sm {
      padding: var(--spacing-xs) var(--spacing-sm);
      font-size: 0.8rem;
    }
    
    &.btn-secondary {
      background: var(--glass-bg);
      color: var(--text-primary);
      border: 1px solid var(--glass-border);
      
      &:hover:not(:disabled) {
        background: var(--glass-hover);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    &.btn-info {
      background: var(--info-color);
      color: white;
      
      &:hover {
        background: var(--info-color-dark);
        transform: translateY(-1px);
      }
    }
  }
}
</style>