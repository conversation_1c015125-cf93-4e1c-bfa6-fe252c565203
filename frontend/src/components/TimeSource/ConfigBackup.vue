<template>
  <div class="config-backup">
    <div class="config-header">
      <h3>配置备份与恢复</h3>
      <div class="header-actions">
        <button 
          class="btn btn-primary" 
          @click="showCreateBackup = true"
        >
          创建备份
        </button>
        <button 
          class="btn btn-secondary" 
          @click="exportConfig"
          :disabled="exporting"
        >
          <span v-if="exporting">导出中...</span>
          <span v-else>导出配置</span>
        </button>
        <label class="btn btn-secondary file-input-label">
          <input 
            type="file" 
            accept=".json,.zip" 
            @change="handleFileImport"
            style="display: none"
          >
          导入配置
        </label>
      </div>
    </div>

    <div class="backup-content">
      <!-- 备份列表 -->
      <div class="backup-section">
        <h4>配置备份列表</h4>
        <div class="backup-list">
          <div 
            v-for="backup in backups" 
            :key="backup.id"
            class="backup-item"
          >
            <div class="backup-info">
              <div class="backup-header">
                <h5>{{ backup.name }}</h5>
                <div class="backup-meta">
                  <span class="backup-date">{{ formatTime(backup.timestamp) }}</span>
                  <span class="backup-size">{{ formatSize(backup.size) }}</span>
                  <span class="backup-version">v{{ backup.version }}</span>
                </div>
              </div>
              <p class="backup-description">{{ backup.description || '无描述' }}</p>
              <div class="backup-details">
                <div class="detail-item">
                  <span class="detail-label">创建者:</span>
                  <span class="detail-value">{{ backup.metadata.createdBy }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">平台:</span>
                  <span class="detail-value">{{ backup.metadata.platform }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">校验和:</span>
                  <span class="detail-value">{{ backup.checksum.substring(0, 8) }}...</span>
                </div>
                <div class="detail-item" v-if="backup.metadata.tags.length > 0">
                  <span class="detail-label">标签:</span>
                  <div class="tags">
                    <span 
                      v-for="tag in backup.metadata.tags" 
                      :key="tag"
                      class="tag"
                    >
                      {{ tag }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="backup-actions">
              <button 
                class="btn btn-success btn-sm" 
                @click="restoreBackup(backup.id)"
                :disabled="restoring === backup.id"
              >
                <span v-if="restoring === backup.id">恢复中...</span>
                <span v-else>恢复</span>
              </button>
              <button 
                class="btn btn-info btn-sm" 
                @click="downloadBackup(backup.id)"
              >
                下载
              </button>
              <button 
                class="btn btn-secondary btn-sm" 
                @click="compareBackup(backup)"
              >
                比较
              </button>
              <button 
                class="btn btn-danger btn-sm" 
                @click="deleteBackup(backup.id)"
                :disabled="deleting === backup.id"
              >
                <span v-if="deleting === backup.id">删除中...</span>
                <span v-else>删除</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 配置模板 -->
      <div class="template-section">
        <h4>配置模板</h4>
        <div class="template-list">
          <div 
            v-for="template in templates" 
            :key="template.id"
            class="template-item"
          >
            <div class="template-info">
              <h5>{{ template.name }}</h5>
              <p>{{ template.description }}</p>
              <div class="template-meta">
                <span class="template-type">{{ template.type }}</span>
                <span class="template-version">v{{ template.version }}</span>
              </div>
            </div>
            <div class="template-actions">
              <button 
                class="btn btn-primary btn-sm" 
                @click="applyTemplate(template.id)"
                :disabled="applying === template.id"
              >
                <span v-if="applying === template.id">应用中...</span>
                <span v-else>应用模板</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建备份对话框 -->
    <div class="modal-overlay" v-if="showCreateBackup" @click="showCreateBackup = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>创建配置备份</h4>
          <button class="modal-close" @click="showCreateBackup = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label>备份名称</label>
            <input 
              type="text" 
              v-model="newBackup.name" 
              placeholder="输入备份名称"
              required
            >
          </div>
          <div class="form-group">
            <label>描述</label>
            <textarea 
              v-model="newBackup.description" 
              placeholder="输入备份描述（可选）"
              rows="3"
            ></textarea>
          </div>
          <div class="form-group">
            <label>标签</label>
            <div class="tag-input">
              <input 
                type="text" 
                v-model="tagInput" 
                placeholder="输入标签后按回车"
                @keyup.enter="addTag"
              >
              <div class="tags" v-if="newBackup.tags.length > 0">
                <span 
                  v-for="(tag, index) in newBackup.tags" 
                  :key="index"
                  class="tag"
                >
                  {{ tag }}
                  <button @click="removeTag(index)">×</button>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showCreateBackup = false">取消</button>
          <button 
            class="btn btn-primary" 
            @click="createBackup"
            :disabled="creating || !newBackup.name"
          >
            <span v-if="creating">创建中...</span>
            <span v-else>创建备份</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 配置验证结果 -->
    <div class="validation-section" v-if="validationResult">
      <h4>配置验证结果</h4>
      <div class="validation-summary">
        <div class="summary-item">
          <span class="summary-label">验证状态:</span>
          <span class="summary-value" :class="validationResult.valid ? 'success' : 'error'">
            {{ validationResult.valid ? '通过' : '失败' }}
          </span>
        </div>
        <div class="summary-item">
          <span class="summary-label">配置评分:</span>
          <span class="summary-value">{{ validationResult.summary.configScore }}/100</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">错误数量:</span>
          <span class="summary-value error">{{ validationResult.summary.totalErrors }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">警告数量:</span>
          <span class="summary-value warning">{{ validationResult.summary.totalWarnings }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">严重问题:</span>
          <span class="summary-value error">{{ validationResult.summary.criticalIssues }}</span>
        </div>
      </div>

      <!-- 错误列表 -->
      <div class="validation-errors" v-if="validationResult.errors.length > 0">
        <h5>错误详情</h5>
        <div class="error-list">
          <div 
            v-for="error in validationResult.errors" 
            :key="error.field"
            class="error-item"
            :class="error.severity"
          >
            <div class="error-header">
              <span class="error-field">{{ error.field }}</span>
              <span class="error-severity">{{ getSeverityName(error.severity) }}</span>
            </div>
            <p class="error-message">{{ error.message }}</p>
            <p class="error-suggestion" v-if="error.suggestion">
              建议: {{ error.suggestion }}
            </p>
          </div>
        </div>
      </div>

      <!-- 警告列表 -->
      <div class="validation-warnings" v-if="validationResult.warnings.length > 0">
        <h5>警告详情</h5>
        <div class="warning-list">
          <div 
            v-for="warning in validationResult.warnings" 
            :key="warning.field"
            class="warning-item"
          >
            <div class="warning-header">
              <span class="warning-field">{{ warning.field }}</span>
            </div>
            <p class="warning-message">{{ warning.message }}</p>
            <p class="warning-impact">影响: {{ warning.impact }}</p>
            <p class="warning-recommendation" v-if="warning.recommendation">
              建议: {{ warning.recommendation }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { configApi } from '@/services/api'
import type { ConfigBackup, ConfigValidation } from '@/types/system'

const backups = ref<ConfigBackup[]>([])
const templates = ref<any[]>([])
const validationResult = ref<ConfigValidation | null>(null)
const showCreateBackup = ref(false)
const restoring = ref<string | null>(null)
const deleting = ref<string | null>(null)
const applying = ref<string | null>(null)
const creating = ref(false)
const exporting = ref(false)
const tagInput = ref('')

const newBackup = reactive({
  name: '',
  description: '',
  tags: [] as string[]
})

const severityNames: Record<string, string> = {
  error: '错误',
  warning: '警告',
  info: '信息'
}

const getSeverityName = (severity: string): string => {
  return severityNames[severity] || severity
}

const formatTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString()
}

const formatSize = (size: number): string => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !newBackup.tags.includes(tag)) {
    newBackup.tags.push(tag)
    tagInput.value = ''
  }
}

const removeTag = (index: number) => {
  newBackup.tags.splice(index, 1)
}

const createBackup = async () => {
  creating.value = true
  try {
    await configApi.createConfigBackup(newBackup.name, newBackup.description)
    showCreateBackup.value = false
    // 重置表单
    newBackup.name = ''
    newBackup.description = ''
    newBackup.tags = []
    // 重新加载备份列表
    await loadBackups()
  } catch (error) {
    console.error('创建备份失败:', error)
  } finally {
    creating.value = false
  }
}

const restoreBackup = async (backupId: string) => {
  if (!confirm('确定要恢复此备份吗？这将覆盖当前配置。')) return
  
  restoring.value = backupId
  try {
    await configApi.restoreConfigBackup(backupId)
    // 重新加载页面或通知用户重启系统
    alert('备份恢复成功，请重启系统以应用新配置。')
  } catch (error) {
    console.error('恢复备份失败:', error)
  } finally {
    restoring.value = null
  }
}

const deleteBackup = async (backupId: string) => {
  if (!confirm('确定要删除此备份吗？此操作不可撤销。')) return
  
  deleting.value = backupId
  try {
    await configApi.deleteConfigBackup(backupId)
    // 重新加载备份列表
    await loadBackups()
  } catch (error) {
    console.error('删除备份失败:', error)
  } finally {
    deleting.value = null
  }
}

const downloadBackup = async (backupId: string) => {
  try {
    const response = await configApi.downloadConfigBackup(backupId)
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `config-backup-${backupId}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('下载备份失败:', error)
  }
}

const compareBackup = async (backup: ConfigBackup) => {
  // 实现配置比较功能
  console.log('比较备份:', backup)
}

const exportConfig = async () => {
  exporting.value = true
  try {
    const response = await configApi.exportConfig()
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `timing-server-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('导出配置失败:', error)
  } finally {
    exporting.value = false
  }
}

const handleFileImport = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (!file) return
  
  try {
    const response = await configApi.importConfig(file)
    alert('配置导入成功')
    // 验证导入的配置
    await validateImportedConfig()
  } catch (error) {
    console.error('导入配置失败:', error)
    alert('配置导入失败')
  }
  
  // 清空文件输入
  target.value = ''
}

const validateImportedConfig = async () => {
  try {
    // 这里应该获取当前配置并验证
    // const currentConfig = await getCurrentConfig()
    // const response = await configApi.validateConfig(currentConfig)
    // validationResult.value = response.data.data
  } catch (error) {
    console.error('验证配置失败:', error)
  }
}

const applyTemplate = async (templateId: string) => {
  if (!confirm('确定要应用此模板吗？这将覆盖当前配置。')) return
  
  applying.value = templateId
  try {
    await configApi.applyConfigTemplate(templateId)
    alert('模板应用成功')
  } catch (error) {
    console.error('应用模板失败:', error)
  } finally {
    applying.value = null
  }
}

const loadBackups = async () => {
  try {
    const response = await configApi.getConfigBackups()
    backups.value = response.data.data
  } catch (error) {
    console.error('加载备份列表失败:', error)
  }
}

const loadTemplates = async () => {
  try {
    const response = await configApi.getConfigTemplates()
    templates.value = response.data.data
  } catch (error) {
    console.error('加载模板列表失败:', error)
  }
}

onMounted(() => {
  loadBackups()
  loadTemplates()
})
</script>

<style lang="scss" scoped>
.config-backup {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      color: var(--text-primary);
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  .backup-section, .template-section, .validation-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4, h5 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-weight: 600;
    }
    
    h4 {
      font-size: 1.1rem;
    }
    
    h5 {
      font-size: 1rem;
    }
  }
  
  .backup-list, .template-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .backup-item, .template-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-md);
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
  }
  
  .backup-info, .template-info {
    flex: 1;
    
    .backup-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-sm);
      
      h5 {
        margin: 0;
        color: var(--text-primary);
        font-size: 1.1rem;
      }
      
      .backup-meta {
        display: flex;
        gap: var(--spacing-sm);
        font-size: 0.9rem;
        color: var(--text-secondary);
        
        .backup-date, .backup-size, .backup-version {
          padding: var(--spacing-xs) var(--spacing-sm);
          background: var(--glass-bg);
          border-radius: var(--radius-sm);
        }
      }
    }
    
    .backup-description {
      margin: 0 0 var(--spacing-sm) 0;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .backup-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-sm);
      
      .detail-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: 0.9rem;
        
        .detail-label {
          color: var(--text-secondary);
          font-weight: 500;
        }
        
        .detail-value {
          color: var(--text-primary);
        }
        
        .tags {
          display: flex;
          gap: var(--spacing-xs);
          flex-wrap: wrap;
        }
      }
    }
    
    .template-meta {
      display: flex;
      gap: var(--spacing-sm);
      margin-top: var(--spacing-sm);
      
      .template-type, .template-version {
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--glass-bg);
        border-radius: var(--radius-sm);
        font-size: 0.8rem;
        color: var(--text-secondary);
      }
    }
  }
  
  .backup-actions, .template-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
  }
  
  .tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    
    button {
      background: none;
      border: none;
      color: white;
      margin-left: var(--spacing-xs);
      cursor: pointer;
      font-size: 0.9rem;
      
      &:hover {
        opacity: 0.8;
      }
    }
  }
  
  .validation-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-sm);
      background: var(--card-bg);
      border-radius: var(--radius-md);
      
      .summary-label {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }
      
      .summary-value {
        font-weight: 600;
        
        &.success {
          color: var(--success-color);
        }
        
        &.error {
          color: var(--error-color);
        }
        
        &.warning {
          color: var(--warning-color);
        }
      }
    }
  }
  
  .error-list, .warning-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .error-item, .warning-item {
    padding: var(--spacing-md);
    background: var(--card-bg);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--error-color);
    
    &.warning {
      border-left-color: var(--warning-color);
    }
    
    &.info {
      border-left-color: var(--info-color);
    }
    
    .error-header, .warning-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-sm);
      
      .error-field, .warning-field {
        font-weight: 600;
        color: var(--text-primary);
      }
      
      .error-severity {
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--error-color);
        color: white;
        border-radius: var(--radius-sm);
        font-size: 0.8rem;
        font-weight: 500;
      }
    }
    
    .error-message, .warning-message {
      margin: 0 0 var(--spacing-sm) 0;
      color: var(--text-primary);
    }
    
    .error-suggestion, .warning-impact, .warning-recommendation {
      margin: 0;
      color: var(--text-secondary);
      font-size: 0.9rem;
      font-style: italic;
    }
  }
  
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    
    h4 {
      margin: 0;
      color: var(--text-primary);
    }
    
    .modal-close {
      background: none;
      border: none;
      font-size: 1.5rem;
      color: var(--text-secondary);
      cursor: pointer;
      
      &:hover {
        color: var(--text-primary);
      }
    }
  }
  
  .modal-body {
    padding: var(--spacing-lg);
    
    .form-group {
      margin-bottom: var(--spacing-md);
      
      label {
        display: block;
        margin-bottom: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input, textarea {
        width: 100%;
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }
      
      textarea {
        resize: vertical;
        min-height: 80px;
      }
    }
    
    .tag-input {
      .tags {
        display: flex;
        gap: var(--spacing-xs);
        flex-wrap: wrap;
        margin-top: var(--spacing-sm);
      }
    }
  }
  
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.btn-sm {
      padding: var(--spacing-xs) var(--spacing-sm);
      font-size: 0.8rem;
    }
    
    &.btn-primary {
      background: var(--primary-color);
      color: white;
      
      &:hover:not(:disabled) {
        background: var(--primary-color-dark);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    &.btn-secondary {
      background: var(--glass-bg);
      color: var(--text-primary);
      border: 1px solid var(--glass-border);
      
      &:hover:not(:disabled) {
        background: var(--glass-hover);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    &.btn-success {
      background: var(--success-color);
      color: white;
      
      &:hover:not(:disabled) {
        background: var(--success-color-dark);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    &.btn-info {
      background: var(--info-color);
      color: white;
      
      &:hover {
        background: var(--info-color-dark);
        transform: translateY(-1px);
      }
    }
    
    &.btn-danger {
      background: var(--error-color);
      color: white;
      
      &:hover:not(:disabled) {
        background: var(--error-color-dark);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
  
  .file-input-label {
    display: inline-block;
  }
}
</style>