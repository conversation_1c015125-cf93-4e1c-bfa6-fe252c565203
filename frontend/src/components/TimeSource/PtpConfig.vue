<template>
  <div class="ptp-config">
    <div class="config-header">
      <h3>PTP服务配置</h3>
      <div class="header-actions">
        <button 
          class="btn btn-secondary" 
          @click="restartService"
          :disabled="restarting"
        >
          <span v-if="restarting">重启中...</span>
          <span v-else>重启服务</span>
        </button>
        <button 
          class="btn btn-info" 
          @click="showClients = !showClients"
        >
          客户端列表 ({{ clients.length }})
        </button>
      </div>
    </div>

    <div class="config-content">
      <!-- 基本设置 -->
      <div class="config-section">
        <h4>基本设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用PTP服务</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="ptp-enabled" 
                v-model="config.enabled"
                @change="onConfigChange"
              >
              <label for="ptp-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>网络接口</label>
            <input 
              type="text" 
              v-model="config.interface" 
              placeholder="eth0"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>PTP域</label>
            <input 
              type="number" 
              v-model.number="config.domain" 
              min="0" 
              max="255"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>优先级1</label>
            <input 
              type="number" 
              v-model.number="config.priority1" 
              min="0" 
              max="255"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>优先级2</label>
            <input 
              type="number" 
              v-model.number="config.priority2" 
              min="0" 
              max="255"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>时钟类别</label>
            <select v-model="config.clockClass" @change="onConfigChange">
              <option :value="6">6 - GPS锁定</option>
              <option :value="7">7 - 守时模式</option>
              <option :value="52">52 - 驯服中</option>
              <option :value="248">248 - 默认</option>
            </select>
          </div>

          <div class="form-group">
            <label>时钟精度</label>
            <select v-model="config.clockAccuracy" @change="onConfigChange">
              <option :value="0x20">0x20 - 25ns</option>
              <option :value="0x21">0x21 - 100ns</option>
              <option :value="0x22">0x22 - 250ns</option>
              <option :value="0x23">0x23 - 1μs</option>
              <option :value="0x24">0x24 - 2.5μs</option>
              <option :value="0x25">0x25 - 10μs</option>
            </select>
          </div>

          <div class="form-group">
            <label>时间源</label>
            <select v-model="config.timeSource" @change="onConfigChange">
              <option value="atomic">原子钟</option>
              <option value="gps">GPS</option>
              <option value="terrestrial">地面</option>
              <option value="ptp">PTP</option>
              <option value="ntp">NTP</option>
              <option value="hand_set">手动设置</option>
              <option value="other">其他</option>
              <option value="internal">内部</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 网络设置 -->
      <div class="config-section">
        <h4>网络设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>传输协议</label>
            <select v-model="config.network.transport" @change="onConfigChange">
              <option value="udp4">UDP IPv4</option>
              <option value="udp6">UDP IPv6</option>
              <option value="l2">Layer 2</option>
            </select>
          </div>

          <div class="form-group">
            <label>延迟机制</label>
            <select v-model="config.network.delayMechanism" @change="onConfigChange">
              <option value="e2e">端到端 (E2E)</option>
              <option value="p2p">点对点 (P2P)</option>
            </select>
          </div>

          <div class="form-group">
            <label>硬件时间戳</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="hw-timestamp" 
                v-model="config.network.hwTimestamp"
                @change="onConfigChange"
              >
              <label for="hw-timestamp"></label>
            </div>
          </div>

          <div class="form-group">
            <label>两步模式</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="two-step" 
                v-model="config.network.twoStep"
                @change="onConfigChange"
              >
              <label for="two-step"></label>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间间隔设置 -->
      <div class="config-section">
        <h4>时间间隔设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>公告间隔 (log2秒)</label>
            <input 
              type="number" 
              v-model.number="config.timing.announceInterval" 
              min="-3" 
              max="4"
              @input="onConfigChange"
            >
            <small>{{ getIntervalDescription(config.timing.announceInterval) }}</small>
          </div>

          <div class="form-group">
            <label>同步间隔 (log2秒)</label>
            <input 
              type="number" 
              v-model.number="config.timing.syncInterval" 
              min="-7" 
              max="4"
              @input="onConfigChange"
            >
            <small>{{ getIntervalDescription(config.timing.syncInterval) }}</small>
          </div>

          <div class="form-group">
            <label>延迟请求间隔 (log2秒)</label>
            <input 
              type="number" 
              v-model.number="config.timing.delayReqInterval" 
              min="-7" 
              max="4"
              @input="onConfigChange"
            >
            <small>{{ getIntervalDescription(config.timing.delayReqInterval) }}</small>
          </div>

          <div class="form-group">
            <label>公告超时</label>
            <input 
              type="number" 
              v-model.number="config.timing.announceTimeout" 
              min="2" 
              max="10"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>同步超时</label>
            <input 
              type="number" 
              v-model.number="config.timing.syncTimeout" 
              min="0" 
              max="10"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 特级主时钟设置 -->
      <div class="config-section">
        <h4>特级主时钟设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>时钟身份</label>
            <input 
              type="text" 
              v-model="config.grandmaster.identity" 
              placeholder="000000.FFFE.000001"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>UTC偏移</label>
            <input 
              type="number" 
              v-model.number="config.grandmaster.utcOffset" 
              min="-43200" 
              max="43200"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>UTC有效</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="utc-valid" 
                v-model="config.grandmaster.utcValid"
                @change="onConfigChange"
              >
              <label for="utc-valid"></label>
            </div>
          </div>

          <div class="form-group">
            <label>时间可追溯</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="time-traceable" 
                v-model="config.grandmaster.timeTraceable"
                @change="onConfigChange"
              >
              <label for="time-traceable"></label>
            </div>
          </div>

          <div class="form-group">
            <label>频率可追溯</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="freq-traceable" 
                v-model="config.grandmaster.frequencyTraceable"
                @change="onConfigChange"
              >
              <label for="freq-traceable"></label>
            </div>
          </div>

          <div class="form-group">
            <label>PTP时间标度</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="ptp-timescale" 
                v-model="config.grandmaster.ptpTimescale"
                @change="onConfigChange"
              >
              <label for="ptp-timescale"></label>
            </div>
          </div>
        </div>
      </div>

      <!-- 伺服设置 -->
      <div class="config-section">
        <h4>伺服设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>伺服类型</label>
            <select v-model="config.servo.type" @change="onConfigChange">
              <option value="pi">PI控制器</option>
              <option value="linreg">线性回归</option>
              <option value="ntpshm">NTP共享内存</option>
              <option value="nullf">空伺服</option>
            </select>
          </div>

          <div class="form-group">
            <label>比例常数</label>
            <input 
              type="number" 
              v-model.number="config.servo.piProportional" 
              min="0" 
              max="1" 
              step="0.01"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>积分常数</label>
            <input 
              type="number" 
              v-model.number="config.servo.piIntegral" 
              min="0" 
              max="1" 
              step="0.01"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最大频率 (ppm)</label>
            <input 
              type="number" 
              v-model.number="config.servo.piMaxFreq" 
              min="0" 
              max="1000"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>首次步进阈值 (ns)</label>
            <input 
              type="number" 
              v-model.number="config.servo.firstStepThreshold" 
              min="0" 
              max="1000000000"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>步进阈值 (ns)</label>
            <input 
              type="number" 
              v-model.number="config.servo.stepThreshold" 
              min="0" 
              max="1000000000"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 监控设置 -->
      <div class="config-section">
        <h4>监控设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用监控</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="monitoring-enabled" 
                v-model="config.monitoring.enabled"
                @change="onConfigChange"
              >
              <label for="monitoring-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>日志级别</label>
            <select v-model="config.monitoring.logLevel" @change="onConfigChange">
              <option :value="0">0 - 紧急</option>
              <option :value="1">1 - 告警</option>
              <option :value="2">2 - 严重</option>
              <option :value="3">3 - 错误</option>
              <option :value="4">4 - 警告</option>
              <option :value="5">5 - 通知</option>
              <option :value="6">6 - 信息</option>
              <option :value="7">7 - 调试</option>
            </select>
          </div>

          <div class="form-group">
            <label>日志文件</label>
            <input 
              type="text" 
              v-model="config.monitoring.logFile" 
              placeholder="/var/log/ptp4l.log"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>统计信息</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="statistics-enabled" 
                v-model="config.monitoring.statisticsEnabled"
                @change="onConfigChange"
              >
              <label for="statistics-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>性能指标</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="metrics-enabled" 
                v-model="config.monitoring.metricsEnabled"
                @change="onConfigChange"
              >
              <label for="metrics-enabled"></label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态显示 -->
    <div class="status-section" v-if="status">
      <h4>PTP服务状态</h4>
      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">服务状态</span>
          <span class="status-value" :class="getStatusClass(status.running)">
            {{ status.running ? '运行中' : '已停止' }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">时钟状态</span>
          <span class="status-value">{{ status.clockState || 'N/A' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">偏移量</span>
          <span class="status-value">{{ formatOffset(status.offset) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">客户端数量</span>
          <span class="status-value">{{ status.clientCount || 0 }}</span>
        </div>
      </div>
    </div>

    <!-- 客户端列表 -->
    <div class="clients-section" v-if="showClients">
      <h4>PTP客户端列表</h4>
      <div class="clients-table">
        <table>
          <thead>
            <tr>
              <th>客户端ID</th>
              <th>IP地址</th>
              <th>偏移量</th>
              <th>延迟</th>
              <th>状态</th>
              <th>最后更新</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="client in clients" :key="client.id">
              <td>{{ client.id }}</td>
              <td>{{ client.address }}</td>
              <td>{{ formatOffset(client.offset) }}</td>
              <td>{{ formatDelay(client.delay) }}</td>
              <td>
                <span class="status-badge" :class="getStatusClass(client.active)">
                  {{ client.active ? '活跃' : '非活跃' }}
                </span>
              </td>
              <td>{{ formatTime(client.lastUpdate) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { timeSourceApi } from '@/services/api'
import type { PtpConfig } from '@/types/system'

const emit = defineEmits<{
  configChange: [config: PtpConfig]
}>()

const config = reactive<PtpConfig>({
  enabled: true,
  interface: 'eth0',
  domain: 0,
  priority1: 128,
  priority2: 128,
  clockClass: 6,
  clockAccuracy: 0x20,
  offsetScaledLogVariance: 0x4E5D,
  timeSource: 'gps',
  network: {
    transport: 'udp4',
    delayMechanism: 'e2e',
    networkTransport: 'UDPv4',
    hwTimestamp: true,
    twoStep: true
  },
  timing: {
    announceInterval: 1,
    syncInterval: 0,
    delayReqInterval: 0,
    announceTimeout: 3,
    syncTimeout: 0
  },
  grandmaster: {
    identity: '000000.FFFE.000001',
    utcOffset: 37,
    utcValid: true,
    leap59: false,
    leap61: false,
    timeTraceable: true,
    frequencyTraceable: true,
    ptpTimescale: true,
    timeSource: 0x20
  },
  servo: {
    type: 'pi',
    piProportional: 0.0,
    piIntegral: 0.0,
    piMaxFreq: 900000000,
    firstStepThreshold: 0.00002,
    stepThreshold: 0.00002
  },
  monitoring: {
    enabled: true,
    logLevel: 6,
    logFile: '/var/log/ptp4l.log',
    statisticsEnabled: true,
    metricsEnabled: true
  }
})

const status = ref<any>(null)
const clients = ref<any[]>([])
const showClients = ref(false)
const restarting = ref(false)

const getIntervalDescription = (logValue: number): string => {
  const seconds = Math.pow(2, logValue)
  if (seconds < 1) {
    return `${(seconds * 1000).toFixed(0)}ms`
  } else if (seconds < 60) {
    return `${seconds}s`
  } else {
    return `${(seconds / 60).toFixed(1)}min`
  }
}

const getStatusClass = (status: boolean): string => {
  return status ? 'success' : 'error'
}

const formatOffset = (offset: number | undefined): string => {
  if (offset === undefined || offset === null) return 'N/A'
  if (Math.abs(offset) < 1000) {
    return `${offset.toFixed(1)}ns`
  } else if (Math.abs(offset) < 1000000) {
    return `${(offset / 1000).toFixed(1)}μs`
  } else {
    return `${(offset / 1000000).toFixed(1)}ms`
  }
}

const formatDelay = (delay: number | undefined): string => {
  if (delay === undefined || delay === null) return 'N/A'
  return `${(delay / 1000).toFixed(1)}μs`
}

const formatTime = (timestamp: string | undefined): string => {
  if (!timestamp) return 'N/A'
  return new Date(timestamp).toLocaleString()
}

const onConfigChange = () => {
  emit('configChange', { ...config })
}

const restartService = async () => {
  restarting.value = true
  try {
    await timeSourceApi.restartPtpService()
    // 重新加载状态
    await loadStatus()
  } catch (error) {
    console.error('重启PTP服务失败:', error)
  } finally {
    restarting.value = false
  }
}

const loadConfig = async () => {
  try {
    const response = await timeSourceApi.getPtpConfig()
    Object.assign(config, response.data.data)
  } catch (error) {
    console.error('加载PTP配置失败:', error)
  }
}

const loadStatus = async () => {
  try {
    const response = await timeSourceApi.getPtpStatus()
    status.value = response.data.data
  } catch (error) {
    console.error('加载PTP状态失败:', error)
  }
}

const loadClients = async () => {
  try {
    const response = await timeSourceApi.getPtpClients()
    clients.value = response.data.data
  } catch (error) {
    console.error('加载PTP客户端失败:', error)
  }
}

onMounted(() => {
  loadConfig()
  loadStatus()
  loadClients()
  
  // 定期更新状态和客户端列表
  const statusInterval = setInterval(() => {
    loadStatus()
    if (showClients.value) {
      loadClients()
    }
  }, 5000)
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})
</script>

<style lang="scss" scoped>
.ptp-config {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      color: var(--text-primary);
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  .config-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    
    .form-group {
      display: flex;
      flex-direction: column;
      
      label {
        margin-bottom: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input, select {
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }
      
      small {
        margin-top: var(--spacing-xs);
        color: var(--text-tertiary);
        font-size: 0.8rem;
      }
    }
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    
    input {
      opacity: 0;
      width: 0;
      height: 0;
      
      &:checked + label {
        background-color: var(--primary-color);
        
        &:before {
          transform: translateX(20px);
        }
      }
    }
    
    label {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--toggle-bg);
      border-radius: 24px;
      transition: all 0.3s ease;
      
      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
    }
  }
  
  .status-section, .clients-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--card-bg);
    border-radius: var(--radius-md);
    
    .status-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .status-value {
      font-weight: 600;
      
      &.success {
        color: var(--success-color);
      }
      
      &.error {
        color: var(--error-color);
      }
    }
  }
  
  .clients-table {
    overflow-x: auto;
    
    table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        padding: var(--spacing-sm);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }
      
      th {
        background: var(--card-bg);
        color: var(--text-secondary);
        font-weight: 600;
        font-size: 0.9rem;
      }
      
      td {
        color: var(--text-primary);
        font-size: 0.9rem;
      }
    }
  }
  
  .status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    
    &.success {
      background: rgba(16, 185, 129, 0.1);
      color: var(--success-color);
    }
    
    &.error {
      background: rgba(239, 68, 68, 0.1);
      color: var(--error-color);
    }
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.btn-secondary {
      background: var(--glass-bg);
      color: var(--text-primary);
      border: 1px solid var(--glass-border);
      
      &:hover:not(:disabled) {
        background: var(--glass-hover);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    &.btn-info {
      background: var(--info-color);
      color: white;
      
      &:hover {
        background: var(--info-color-dark);
        transform: translateY(-1px);
      }
    }
  }
}
</style>