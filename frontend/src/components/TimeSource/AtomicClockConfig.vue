<template>
  <div class="atomic-clock-config">
    <div class="config-header">
      <h3>原子钟（铷钟）管理</h3>
      <div class="header-actions">
        <button 
          class="btn btn-secondary" 
          @click="calibrateClock"
          :disabled="calibrating"
        >
          <span v-if="calibrating">校准中...</span>
          <span v-else>校准时钟</span>
        </button>
        <button 
          class="btn btn-warning" 
          @click="resetLearning"
          :disabled="resetting"
        >
          <span v-if="resetting">重置中...</span>
          <span v-else>重置学习</span>
        </button>
        <button 
          class="btn btn-info" 
          @click="showLearningData = !showLearningData"
        >
          学习数据
        </button>
      </div>
    </div>

    <div class="config-content">
      <!-- 基本设置 -->
      <div class="config-section">
        <h4>基本设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用原子钟</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="atomic-enabled" 
                v-model="config.enabled"
                @change="onConfigChange"
              >
              <label for="atomic-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>时钟类型</label>
            <select v-model="config.type" @change="onConfigChange">
              <option value="rubidium">铷钟</option>
              <option value="cesium">铯钟</option>
              <option value="hydrogen">氢钟</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 设备接口设置 -->
      <div class="config-section">
        <h4>设备接口设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>接口类型</label>
            <select v-model="config.device.interface" @change="onConfigChange">
              <option value="spi">SPI</option>
              <option value="i2c">I2C</option>
              <option value="uart">UART</option>
              <option value="usb">USB</option>
            </select>
          </div>

          <div class="form-group">
            <label>设备地址</label>
            <input 
              type="text" 
              v-model="config.device.address" 
              placeholder="/dev/spidev0.0"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group" v-if="config.device.interface === 'uart'">
            <label>波特率</label>
            <select v-model="config.device.baudRate" @change="onConfigChange">
              <option :value="9600">9600</option>
              <option :value="19200">19200</option>
              <option :value="38400">38400</option>
              <option :value="57600">57600</option>
              <option :value="115200">115200</option>
            </select>
          </div>

          <div class="form-group">
            <label>超时时间 (毫秒)</label>
            <input 
              type="number" 
              v-model.number="config.device.timeout" 
              min="100" 
              max="10000"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 控制设置 -->
      <div class="config-section">
        <h4>控制设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>预热时间 (分钟)</label>
            <input 
              type="number" 
              v-model.number="config.control.warmupTime" 
              min="1" 
              max="1440"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>锁定超时 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.control.lockTimeout" 
              min="10" 
              max="3600"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>驯服模式</label>
            <select v-model="config.control.disciplineMode" @change="onConfigChange">
              <option value="auto">自动</option>
              <option value="manual">手动</option>
              <option value="holdover">守时</option>
            </select>
          </div>

          <div class="form-group">
            <label>频率控制</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="freq-control" 
                v-model="config.control.frequencyControl"
                @change="onConfigChange"
              >
              <label for="freq-control"></label>
            </div>
          </div>

          <div class="form-group">
            <label>温度控制</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="temp-control" 
                v-model="config.control.temperatureControl"
                @change="onConfigChange"
              >
              <label for="temp-control"></label>
            </div>
          </div>
        </div>
      </div>

      <!-- 监控设置 -->
      <div class="config-section">
        <h4>监控设置</h4>
        
        <!-- 温度监控 -->
        <div class="monitoring-subsection">
          <h5>温度监控</h5>
          <div class="form-grid">
            <div class="form-group">
              <label>启用温度监控</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="temp-monitoring" 
                  v-model="config.monitoring.temperature.enabled"
                  @change="onConfigChange"
                >
                <label for="temp-monitoring"></label>
              </div>
            </div>

            <div class="form-group">
              <label>监控间隔 (秒)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.temperature.interval" 
                min="1" 
                max="3600"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>低温阈值 (°C)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.temperature.lowThreshold" 
                min="-50" 
                max="100" 
                step="0.1"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>高温阈值 (°C)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.temperature.highThreshold" 
                min="-50" 
                max="100" 
                step="0.1"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>严重阈值 (°C)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.temperature.criticalThreshold" 
                min="-50" 
                max="100" 
                step="0.1"
                @input="onConfigChange"
              >
            </div>
          </div>
        </div>

        <!-- 频率监控 -->
        <div class="monitoring-subsection">
          <h5>频率监控</h5>
          <div class="form-grid">
            <div class="form-group">
              <label>启用频率监控</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="freq-monitoring" 
                  v-model="config.monitoring.frequency.enabled"
                  @change="onConfigChange"
                >
                <label for="freq-monitoring"></label>
              </div>
            </div>

            <div class="form-group">
              <label>监控间隔 (秒)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.frequency.interval" 
                min="1" 
                max="3600"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>频率容差 (ppm)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.frequency.tolerance" 
                min="0" 
                max="1000" 
                step="0.001"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>老化率 (ppm/年)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.frequency.agingRate" 
                min="0" 
                max="100" 
                step="0.001"
                @input="onConfigChange"
              >
            </div>
          </div>
        </div>

        <!-- 相位监控 -->
        <div class="monitoring-subsection">
          <h5>相位监控</h5>
          <div class="form-grid">
            <div class="form-group">
              <label>启用相位监控</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="phase-monitoring" 
                  v-model="config.monitoring.phase.enabled"
                  @change="onConfigChange"
                >
                <label for="phase-monitoring"></label>
              </div>
            </div>

            <div class="form-group">
              <label>监控间隔 (秒)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.phase.interval" 
                min="1" 
                max="3600"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>相位容差 (ns)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.phase.tolerance" 
                min="0" 
                max="1000000" 
                step="1"
                @input="onConfigChange"
              >
            </div>
          </div>
        </div>

        <!-- 电压监控 -->
        <div class="monitoring-subsection">
          <h5>电压监控</h5>
          <div class="form-grid">
            <div class="form-group">
              <label>启用电压监控</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="voltage-monitoring" 
                  v-model="config.monitoring.voltage.enabled"
                  @change="onConfigChange"
                >
                <label for="voltage-monitoring"></label>
              </div>
            </div>

            <div class="form-group">
              <label>监控间隔 (秒)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.voltage.interval" 
                min="1" 
                max="3600"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>低压阈值 (V)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.voltage.lowThreshold" 
                min="0" 
                max="50" 
                step="0.1"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>高压阈值 (V)</label>
              <input 
                type="number" 
                v-model.number="config.monitoring.voltage.highThreshold" 
                min="0" 
                max="50" 
                step="0.1"
                @input="onConfigChange"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 学习设置 -->
      <div class="config-section">
        <h4>学习设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用学习</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="learning-enabled" 
                v-model="config.learning.enabled"
                @change="onConfigChange"
              >
              <label for="learning-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>学习持续时间 (小时)</label>
            <input 
              type="number" 
              v-model.number="config.learning.duration" 
              min="1" 
              max="8760"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>采样数量</label>
            <input 
              type="number" 
              v-model.number="config.learning.samples" 
              min="10" 
              max="100000"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>收敛阈值</label>
            <input 
              type="number" 
              v-model.number="config.learning.convergenceThreshold" 
              min="0" 
              max="1" 
              step="0.001"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>老化补偿</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="aging-compensation" 
                v-model="config.learning.agingCompensation"
                @change="onConfigChange"
              >
              <label for="aging-compensation"></label>
            </div>
          </div>

          <div class="form-group">
            <label>温度补偿</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="temp-compensation" 
                v-model="config.learning.temperatureCompensation"
                @change="onConfigChange"
              >
              <label for="temp-compensation"></label>
            </div>
          </div>

          <div class="form-group">
            <label>保存间隔 (分钟)</label>
            <input 
              type="number" 
              v-model.number="config.learning.saveInterval" 
              min="1" 
              max="1440"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>数据保留 (天)</label>
            <input 
              type="number" 
              v-model.number="config.learning.dataRetention" 
              min="1" 
              max="3650"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 守时设置 -->
      <div class="config-section">
        <h4>守时设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用守时</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="holdover-enabled" 
                v-model="config.holdover.enabled"
                @change="onConfigChange"
              >
              <label for="holdover-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>最大守时时间 (小时)</label>
            <input 
              type="number" 
              v-model.number="config.holdover.maxDuration" 
              min="1" 
              max="8760"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>精度阈值 (ns)</label>
            <input 
              type="number" 
              v-model.number="config.holdover.accuracyThreshold" 
              min="1" 
              max="1000000"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>稳定度阈值 (ppm)</label>
            <input 
              type="number" 
              v-model.number="config.holdover.stabilityThreshold" 
              min="0.001" 
              max="1000" 
              step="0.001"
              @input="onConfigChange"
            >
          </div>

          <div class="exit-conditions">
            <h5>退出条件</h5>
            <div class="conditions-grid">
              <div class="condition-item">
                <div class="toggle-switch">
                  <input 
                    type="checkbox" 
                    id="exit-gnss" 
                    v-model="config.holdover.exitConditions.gnssLock"
                    @change="onConfigChange"
                  >
                  <label for="exit-gnss"></label>
                </div>
                <label for="exit-gnss" class="condition-label">GNSS锁定</label>
              </div>

              <div class="condition-item">
                <div class="toggle-switch">
                  <input 
                    type="checkbox" 
                    id="exit-ptp" 
                    v-model="config.holdover.exitConditions.ptpSync"
                    @change="onConfigChange"
                  >
                  <label for="exit-ptp"></label>
                </div>
                <label for="exit-ptp" class="condition-label">PTP同步</label>
              </div>

              <div class="condition-item">
                <div class="toggle-switch">
                  <input 
                    type="checkbox" 
                    id="exit-manual" 
                    v-model="config.holdover.exitConditions.manualOverride"
                    @change="onConfigChange"
                  >
                  <label for="exit-manual"></label>
                </div>
                <label for="exit-manual" class="condition-label">手动覆盖</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 告警设置 -->
      <div class="config-section">
        <h4>告警设置</h4>
        <div class="alarms-grid">
          <div class="alarm-item" v-for="(enabled, alarm) in config.alarms" :key="alarm">
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                :id="`alarm-${alarm}`" 
                v-model="config.alarms[alarm]"
                @change="onConfigChange"
              >
              <label :for="`alarm-${alarm}`"></label>
            </div>
            <label :for="`alarm-${alarm}`" class="alarm-label">
              {{ getAlarmName(alarm) }}
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态显示 -->
    <div class="status-section" v-if="status">
      <h4>原子钟状态</h4>
      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">运行状态</span>
          <span class="status-value" :class="getStatusClass(status.running)">
            {{ status.running ? '运行中' : '已停止' }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">锁定状态</span>
          <span class="status-value" :class="getStatusClass(status.locked)">
            {{ status.locked ? '已锁定' : '未锁定' }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">温度</span>
          <span class="status-value">{{ formatTemperature(status.temperature) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">频率偏移</span>
          <span class="status-value">{{ formatFrequency(status.frequencyOffset) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">相位偏移</span>
          <span class="status-value">{{ formatPhase(status.phaseOffset) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">学习状态</span>
          <span class="status-value">{{ status.learningStatus || 'N/A' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">守时时间</span>
          <span class="status-value">{{ formatDuration(status.holdoverDuration) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">运行时间</span>
          <span class="status-value">{{ formatDuration(status.uptime) }}</span>
        </div>
      </div>
    </div>

    <!-- 学习数据显示 -->
    <div class="learning-section" v-if="showLearningData && learningData">
      <h4>学习数据</h4>
      <div class="learning-stats">
        <div class="stat-item">
          <span class="stat-label">学习进度</span>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${learningData.progress}%` }"></div>
          </div>
          <span class="stat-value">{{ learningData.progress.toFixed(1) }}%</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">采样数量</span>
          <span class="stat-value">{{ learningData.samples }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">收敛度</span>
          <span class="stat-value">{{ learningData.convergence.toFixed(4) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">老化率</span>
          <span class="stat-value">{{ learningData.agingRate.toFixed(6) }} ppm/年</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">温度系数</span>
          <span class="stat-value">{{ learningData.tempCoeff.toFixed(6) }} ppm/°C</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最后更新</span>
          <span class="stat-value">{{ formatTime(learningData.lastUpdate) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { timeSourceApi } from '@/services/api'
import type { AtomicClockConfig } from '@/types/system'

const emit = defineEmits<{
  configChange: [config: AtomicClockConfig]
}>()

const config = reactive<AtomicClockConfig>({
  enabled: true,
  type: 'rubidium',
  device: {
    interface: 'spi',
    address: '/dev/spidev0.0',
    baudRate: 9600,
    timeout: 1000
  },
  control: {
    warmupTime: 30,
    lockTimeout: 300,
    disciplineMode: 'auto',
    frequencyControl: true,
    temperatureControl: true
  },
  monitoring: {
    temperature: {
      enabled: true,
      interval: 60,
      lowThreshold: 50.0,
      highThreshold: 80.0,
      criticalThreshold: 85.0
    },
    frequency: {
      enabled: true,
      interval: 10,
      tolerance: 0.001,
      agingRate: 0.01
    },
    phase: {
      enabled: true,
      interval: 1,
      tolerance: 100
    },
    voltage: {
      enabled: true,
      interval: 60,
      lowThreshold: 11.5,
      highThreshold: 12.5
    }
  },
  learning: {
    enabled: true,
    duration: 72,
    samples: 10000,
    convergenceThreshold: 0.001,
    agingCompensation: true,
    temperatureCompensation: true,
    saveInterval: 15,
    dataRetention: 365
  },
  holdover: {
    enabled: true,
    maxDuration: 24,
    accuracyThreshold: 1000,
    stabilityThreshold: 0.01,
    exitConditions: {
      gnssLock: true,
      ptpSync: true,
      manualOverride: true
    }
  },
  alarms: {
    temperatureAlarm: true,
    frequencyAlarm: true,
    phaseAlarm: true,
    voltageAlarm: true,
    lockLossAlarm: true,
    agingAlarm: true
  }
})

const status = ref<any>(null)
const learningData = ref<any>(null)
const showLearningData = ref(false)
const calibrating = ref(false)
const resetting = ref(false)

const alarmNames: Record<string, string> = {
  temperatureAlarm: '温度告警',
  frequencyAlarm: '频率告警',
  phaseAlarm: '相位告警',
  voltageAlarm: '电压告警',
  lockLossAlarm: '失锁告警',
  agingAlarm: '老化告警'
}

const getAlarmName = (alarm: string): string => {
  return alarmNames[alarm] || alarm
}

const getStatusClass = (status: boolean): string => {
  return status ? 'success' : 'error'
}

const formatTemperature = (temp: number | undefined): string => {
  if (temp === undefined || temp === null) return 'N/A'
  return `${temp.toFixed(1)}°C`
}

const formatFrequency = (freq: number | undefined): string => {
  if (freq === undefined || freq === null) return 'N/A'
  return `${freq.toFixed(6)} ppm`
}

const formatPhase = (phase: number | undefined): string => {
  if (phase === undefined || phase === null) return 'N/A'
  return `${phase.toFixed(1)} ns`
}

const formatDuration = (duration: number | undefined): string => {
  if (duration === undefined || duration === null) return 'N/A'
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  return `${hours}h ${minutes}m`
}

const formatTime = (timestamp: string | undefined): string => {
  if (!timestamp) return 'N/A'
  return new Date(timestamp).toLocaleString()
}

const onConfigChange = () => {
  emit('configChange', { ...config })
}

const calibrateClock = async () => {
  calibrating.value = true
  try {
    await timeSourceApi.calibrateAtomicClock()
    // 重新加载状态
    await loadStatus()
  } catch (error) {
    console.error('校准原子钟失败:', error)
  } finally {
    calibrating.value = false
  }
}

const resetLearning = async () => {
  resetting.value = true
  try {
    await timeSourceApi.resetAtomicClockLearning()
    // 重新加载学习数据
    await loadLearningData()
  } catch (error) {
    console.error('重置学习数据失败:', error)
  } finally {
    resetting.value = false
  }
}

const loadConfig = async () => {
  try {
    const response = await timeSourceApi.getAtomicClockConfig()
    Object.assign(config, response.data.data)
  } catch (error) {
    console.error('加载原子钟配置失败:', error)
  }
}

const loadStatus = async () => {
  try {
    const response = await timeSourceApi.getAtomicClockStatus()
    status.value = response.data.data
  } catch (error) {
    console.error('加载原子钟状态失败:', error)
  }
}

const loadLearningData = async () => {
  try {
    const response = await timeSourceApi.getAtomicClockLearningData()
    learningData.value = response.data.data
  } catch (error) {
    console.error('加载学习数据失败:', error)
  }
}

onMounted(() => {
  loadConfig()
  loadStatus()
  loadLearningData()
  
  // 定期更新状态和学习数据
  const statusInterval = setInterval(() => {
    loadStatus()
    if (showLearningData.value) {
      loadLearningData()
    }
  }, 5000)
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})
</script>

<style lang="scss" scoped>
.atomic-clock-config {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      color: var(--text-primary);
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  .config-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
    
    h5 {
      margin: var(--spacing-md) 0 var(--spacing-sm) 0;
      color: var(--text-secondary);
      font-size: 1rem;
      font-weight: 500;
    }
  }
  
  .monitoring-subsection {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-md);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    
    .form-group {
      display: flex;
      flex-direction: column;
      
      label {
        margin-bottom: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input, select {
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
  
  .exit-conditions {
    grid-column: 1 / -1;
    margin-top: var(--spacing-md);
    
    h5 {
      margin-bottom: var(--spacing-sm);
    }
  }
  
  .conditions-grid, .alarms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .condition-item, .alarm-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .condition-label, .alarm-label {
      color: var(--text-primary);
      font-size: 0.9rem;
      cursor: pointer;
    }
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    
    input {
      opacity: 0;
      width: 0;
      height: 0;
      
      &:checked + label {
        background-color: var(--primary-color);
        
        &:before {
          transform: translateX(20px);
        }
      }
    }
    
    label {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--toggle-bg);
      border-radius: 24px;
      transition: all 0.3s ease;
      
      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
    }
  }
  
  .status-section, .learning-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--card-bg);
    border-radius: var(--radius-md);
    
    .status-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .status-value {
      font-weight: 600;
      
      &.success {
        color: var(--success-color);
      }
      
      &.error {
        color: var(--error-color);
      }
    }
  }
  
  .learning-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
  }
  
  .stat-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: var(--card-bg);
    border-radius: var(--radius-md);
    
    .stat-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .stat-value {
      color: var(--text-primary);
      font-weight: 600;
    }
    
    .progress-bar {
      width: 100%;
      height: 8px;
      background: var(--progress-bg);
      border-radius: 4px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: var(--primary-color);
        transition: width 0.3s ease;
      }
    }
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.btn-secondary {
      background: var(--glass-bg);
      color: var(--text-primary);
      border: 1px solid var(--glass-border);
      
      &:hover:not(:disabled) {
        background: var(--glass-hover);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    &.btn-info {
      background: var(--info-color);
      color: white;
      
      &:hover {
        background: var(--info-color-dark);
        transform: translateY(-1px);
      }
    }
    
    &.btn-warning {
      background: var(--warning-color);
      color: white;
      
      &:hover:not(:disabled) {
        background: var(--warning-color-dark);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}
</style>