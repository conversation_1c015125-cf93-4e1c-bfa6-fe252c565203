<template>
  <div class="ntp-config">
    <div class="config-header">
      <h3>NTP服务配置</h3>
      <div class="header-actions">
        <button 
          class="btn btn-secondary" 
          @click="restartService"
          :disabled="restarting"
        >
          <span v-if="restarting">重启中...</span>
          <span v-else>重启服务</span>
        </button>
        <button 
          class="btn btn-info" 
          @click="showClients = !showClients"
        >
          客户端列表 ({{ clients.length }})
        </button>
      </div>
    </div>

    <div class="config-content">
      <!-- 基本设置 -->
      <div class="config-section">
        <h4>基本设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用NTP服务</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="ntp-enabled" 
                v-model="config.enabled"
                @change="onConfigChange"
              >
              <label for="ntp-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>层级 (Stratum)</label>
            <input 
              type="number" 
              v-model.number="config.stratum" 
              min="1" 
              max="15"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>参考ID</label>
            <input 
              type="text" 
              v-model="config.referenceId" 
              placeholder="GPS"
              maxlength="4"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 服务器设置 -->
      <div class="config-section">
        <h4>服务器设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>服务端口</label>
            <input 
              type="number" 
              v-model.number="config.server.port" 
              min="1" 
              max="65535"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>绑定地址</label>
            <input 
              type="text" 
              v-model="config.server.bindAddress" 
              placeholder="0.0.0.0"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最大客户端数</label>
            <input 
              type="number" 
              v-model.number="config.server.maxClients" 
              min="1" 
              max="10000"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>客户端超时 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.server.clientTimeout" 
              min="1" 
              max="3600"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 访问控制 -->
      <div class="config-section">
        <h4>访问控制</h4>
        <div class="form-grid">
          <div class="form-group full-width">
            <label>允许的网络</label>
            <div class="network-list">
              <div v-for="(network, index) in config.access.allowedNetworks" :key="index" class="network-item">
                <input 
                  type="text" 
                  v-model="config.access.allowedNetworks[index]" 
                  placeholder="***********/24"
                  @input="onConfigChange"
                >
                <button class="btn btn-danger btn-sm" @click="removeAllowedNetwork(index)">删除</button>
              </div>
              <button class="btn btn-secondary btn-sm" @click="addAllowedNetwork">添加网络</button>
            </div>
          </div>

          <div class="form-group full-width">
            <label>拒绝的网络</label>
            <div class="network-list">
              <div v-for="(network, index) in config.access.deniedNetworks" :key="index" class="network-item">
                <input 
                  type="text" 
                  v-model="config.access.deniedNetworks[index]" 
                  placeholder="10.0.0.0/8"
                  @input="onConfigChange"
                >
                <button class="btn btn-danger btn-sm" @click="removeDeniedNetwork(index)">删除</button>
              </div>
              <button class="btn btn-secondary btn-sm" @click="addDeniedNetwork">添加网络</button>
            </div>
          </div>

          <div class="access-flags">
            <h5>访问限制</h5>
            <div class="flags-grid">
              <div class="flag-item" v-for="(value, flag) in accessFlags" :key="flag">
                <div class="toggle-switch">
                  <input 
                    type="checkbox" 
                    :id="`access-${flag}`" 
                    v-model="config.access[flag]"
                    @change="onConfigChange"
                  >
                  <label :for="`access-${flag}`"></label>
                </div>
                <label :for="`access-${flag}`" class="flag-label">
                  {{ getAccessFlagName(flag) }}
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 参考时钟设置 -->
      <div class="config-section">
        <h4>参考时钟设置</h4>
        
        <!-- PHC参考时钟 -->
        <div class="refclock-subsection">
          <h5>PHC参考时钟</h5>
          <div class="form-grid">
            <div class="form-group">
              <label>启用PHC</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="phc-enabled" 
                  v-model="config.refclock.phc.enabled"
                  @change="onConfigChange"
                >
                <label for="phc-enabled"></label>
              </div>
            </div>

            <div class="form-group">
              <label>PHC设备</label>
              <input 
                type="text" 
                v-model="config.refclock.phc.device" 
                placeholder="/dev/ptp0"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>轮询间隔</label>
              <input 
                type="number" 
                v-model.number="config.refclock.phc.poll" 
                min="-7" 
                max="17"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>偏移量 (秒)</label>
              <input 
                type="number" 
                v-model.number="config.refclock.phc.offset" 
                step="0.000001"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>参考ID</label>
              <input 
                type="text" 
                v-model="config.refclock.phc.refid" 
                placeholder="PTP"
                maxlength="4"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>首选</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="phc-prefer" 
                  v-model="config.refclock.phc.prefer"
                  @change="onConfigChange"
                >
                <label for="phc-prefer"></label>
              </div>
            </div>
          </div>
        </div>

        <!-- SHM参考时钟 -->
        <div class="refclock-subsection">
          <h5>SHM参考时钟 (GNSS)</h5>
          <div class="form-grid">
            <div class="form-group">
              <label>启用SHM</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="shm-enabled" 
                  v-model="config.refclock.shm.enabled"
                  @change="onConfigChange"
                >
                <label for="shm-enabled"></label>
              </div>
            </div>

            <div class="form-group">
              <label>SHM单元</label>
              <input 
                type="number" 
                v-model.number="config.refclock.shm.unit" 
                min="0" 
                max="3"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>偏移量 (秒)</label>
              <input 
                type="number" 
                v-model.number="config.refclock.shm.offset" 
                step="0.000001"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>延迟 (秒)</label>
              <input 
                type="number" 
                v-model.number="config.refclock.shm.delay" 
                step="0.000001"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>参考ID</label>
              <input 
                type="text" 
                v-model="config.refclock.shm.refid" 
                placeholder="GPS"
                maxlength="4"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>不选择</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="shm-noselect" 
                  v-model="config.refclock.shm.noselect"
                  @change="onConfigChange"
                >
                <label for="shm-noselect"></label>
              </div>
            </div>
          </div>
        </div>

        <!-- PPS参考时钟 -->
        <div class="refclock-subsection">
          <h5>PPS参考时钟</h5>
          <div class="form-grid">
            <div class="form-group">
              <label>启用PPS</label>
              <div class="toggle-switch">
                <input 
                  type="checkbox" 
                  id="pps-enabled" 
                  v-model="config.refclock.pps.enabled"
                  @change="onConfigChange"
                >
                <label for="pps-enabled"></label>
              </div>
            </div>

            <div class="form-group">
              <label>PPS设备</label>
              <input 
                type="text" 
                v-model="config.refclock.pps.device" 
                placeholder="/dev/pps0"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>锁定源</label>
              <input 
                type="text" 
                v-model="config.refclock.pps.lock" 
                placeholder="GPS"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>采样率</label>
              <input 
                type="number" 
                v-model.number="config.refclock.pps.rate" 
                min="1" 
                max="1000"
                @input="onConfigChange"
              >
            </div>

            <div class="form-group">
              <label>参考ID</label>
              <input 
                type="text" 
                v-model="config.refclock.pps.refid" 
                placeholder="PPS"
                maxlength="4"
                @input="onConfigChange"
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Chrony特定设置 -->
      <div class="config-section">
        <h4>Chrony特定设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>漂移文件</label>
            <input 
              type="text" 
              v-model="config.chrony.driftFile" 
              placeholder="/var/lib/chrony/drift"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>日志目录</label>
            <input 
              type="text" 
              v-model="config.chrony.logDir" 
              placeholder="/var/log/chrony"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>最大更新偏差</label>
            <input 
              type="number" 
              v-model.number="config.chrony.maxUpdateSkew" 
              min="1" 
              max="1000"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>步进阈值 (秒)</label>
            <input 
              type="number" 
              v-model.number="config.chrony.makestep.threshold" 
              min="0" 
              max="1000" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>步进限制</label>
            <input 
              type="number" 
              v-model.number="config.chrony.makestep.limit" 
              min="-1" 
              max="100"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>RTC同步</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="rtc-sync" 
                v-model="config.chrony.rtcsync"
                @change="onConfigChange"
              >
              <label for="rtc-sync"></label>
            </div>
          </div>

          <div class="form-group">
            <label>本地层级</label>
            <input 
              type="number" 
              v-model.number="config.chrony.local.stratum" 
              min="1" 
              max="15"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>孤儿模式</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="local-orphan" 
                v-model="config.chrony.local.orphan"
                @change="onConfigChange"
              >
              <label for="local-orphan"></label>
            </div>
          </div>

          <div class="form-group">
            <label>闰秒模式</label>
            <select v-model="config.chrony.leapsecmode" @change="onConfigChange">
              <option value="system">系统</option>
              <option value="slew">渐变</option>
              <option value="step">步进</option>
              <option value="ignore">忽略</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 监控设置 -->
      <div class="config-section">
        <h4>监控设置</h4>
        <div class="monitoring-grid">
          <div class="monitoring-item" v-for="(enabled, monitor) in config.monitoring" :key="monitor">
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                :id="`monitor-${monitor}`" 
                v-model="config.monitoring[monitor]"
                @change="onConfigChange"
              >
              <label :for="`monitor-${monitor}`"></label>
            </div>
            <label :for="`monitor-${monitor}`" class="monitor-label">
              {{ getMonitoringName(monitor) }}
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态显示 -->
    <div class="status-section" v-if="status">
      <h4>NTP服务状态</h4>
      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">服务状态</span>
          <span class="status-value" :class="getStatusClass(status.running)">
            {{ status.running ? '运行中' : '已停止' }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">层级</span>
          <span class="status-value">{{ status.stratum || 'N/A' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">参考源</span>
          <span class="status-value">{{ status.refSource || 'N/A' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">客户端数量</span>
          <span class="status-value">{{ status.clientCount || 0 }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">系统偏移</span>
          <span class="status-value">{{ formatOffset(status.systemOffset) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">RMS偏移</span>
          <span class="status-value">{{ formatOffset(status.rmsOffset) }}</span>
        </div>
      </div>
    </div>

    <!-- 客户端列表 -->
    <div class="clients-section" v-if="showClients">
      <h4>NTP客户端列表</h4>
      <div class="clients-table">
        <table>
          <thead>
            <tr>
              <th>客户端IP</th>
              <th>版本</th>
              <th>模式</th>
              <th>层级</th>
              <th>轮询间隔</th>
              <th>到达时间</th>
              <th>最后包</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="client in clients" :key="client.address">
              <td>{{ client.address }}</td>
              <td>{{ client.version }}</td>
              <td>{{ client.mode }}</td>
              <td>{{ client.stratum }}</td>
              <td>{{ client.poll }}s</td>
              <td>{{ formatTime(client.reach) }}</td>
              <td>{{ formatTime(client.lastPacket) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { timeSourceApi } from '@/services/api'
import type { NtpConfig } from '@/types/system'

const emit = defineEmits<{
  configChange: [config: NtpConfig]
}>()

const config = reactive<NtpConfig>({
  enabled: true,
  stratum: 1,
  referenceId: 'GPS',
  server: {
    port: 123,
    bindAddress: '0.0.0.0',
    maxClients: 1000,
    clientTimeout: 300
  },
  access: {
    allowedNetworks: ['***********/24'],
    deniedNetworks: [],
    restrictDefault: true,
    kod: true,
    limited: false,
    nomodify: true,
    nopeer: true,
    noquery: false,
    notrap: true,
    notrust: false
  },
  refclock: {
    phc: {
      enabled: true,
      device: '/dev/ptp0',
      poll: 0,
      dpoll: 0,
      offset: 0,
      prefer: true,
      trust: true,
      require: false,
      tai: false,
      filter: 1,
      precision: -20,
      refid: 'PTP'
    },
    shm: {
      enabled: true,
      unit: 0,
      poll: 0,
      dpoll: 0,
      offset: 0.0,
      delay: 0.2,
      refid: 'GPS',
      precision: -20,
      noselect: true
    },
    pps: {
      enabled: false,
      device: '/dev/pps0',
      poll: 0,
      dpoll: 0,
      lock: 'GPS',
      rate: 1,
      refid: 'PPS',
      precision: -20
    }
  },
  chrony: {
    driftFile: '/var/lib/chrony/drift',
    logDir: '/var/log/chrony',
    maxUpdateSkew: 100.0,
    makestep: {
      threshold: 1.0,
      limit: 3
    },
    rtcsync: true,
    hwclockfile: '/etc/adjtime',
    local: {
      stratum: 1,
      orphan: false,
      distance: 0.0
    },
    smoothtime: {
      maxFreq: 100000,
      maxWander: 1.0,
      leapOnly: false
    },
    leapsectz: 'right/UTC',
    leapsecmode: 'system'
  },
  monitoring: {
    tracking: true,
    measurements: true,
    statistics: true,
    rtc: false,
    refclocks: true,
    tempcomp: false,
    selection: true,
    rawmeasurements: false
  }
})

const status = ref<any>(null)
const clients = ref<any[]>([])
const showClients = ref(false)
const restarting = ref(false)

const accessFlags = {
  restrictDefault: '默认限制',
  kod: 'Kiss-of-Death',
  limited: '限制访问',
  nomodify: '禁止修改',
  nopeer: '禁止对等',
  noquery: '禁止查询',
  notrap: '禁止陷阱',
  notrust: '不信任'
}

const monitoringNames: Record<string, string> = {
  tracking: '跟踪日志',
  measurements: '测量日志',
  statistics: '统计日志',
  rtc: 'RTC日志',
  refclocks: '参考时钟日志',
  tempcomp: '温度补偿日志',
  selection: '选择日志',
  rawmeasurements: '原始测量日志'
}

const getAccessFlagName = (flag: string): string => {
  return accessFlags[flag as keyof typeof accessFlags] || flag
}

const getMonitoringName = (monitor: string): string => {
  return monitoringNames[monitor] || monitor
}

const getStatusClass = (status: boolean): string => {
  return status ? 'success' : 'error'
}

const formatOffset = (offset: number | undefined): string => {
  if (offset === undefined || offset === null) return 'N/A'
  if (Math.abs(offset) < 0.001) {
    return `${(offset * 1000000).toFixed(1)}μs`
  } else {
    return `${(offset * 1000).toFixed(1)}ms`
  }
}

const formatTime = (timestamp: string | undefined): string => {
  if (!timestamp) return 'N/A'
  return new Date(timestamp).toLocaleString()
}

const addAllowedNetwork = () => {
  config.access.allowedNetworks.push('')
  onConfigChange()
}

const removeAllowedNetwork = (index: number) => {
  config.access.allowedNetworks.splice(index, 1)
  onConfigChange()
}

const addDeniedNetwork = () => {
  config.access.deniedNetworks.push('')
  onConfigChange()
}

const removeDeniedNetwork = (index: number) => {
  config.access.deniedNetworks.splice(index, 1)
  onConfigChange()
}

const onConfigChange = () => {
  emit('configChange', { ...config })
}

const restartService = async () => {
  restarting.value = true
  try {
    await timeSourceApi.restartNtpService()
    // 重新加载状态
    await loadStatus()
  } catch (error) {
    console.error('重启NTP服务失败:', error)
  } finally {
    restarting.value = false
  }
}

const loadConfig = async () => {
  try {
    const response = await timeSourceApi.getNtpConfig()
    Object.assign(config, response.data.data)
  } catch (error) {
    console.error('加载NTP配置失败:', error)
  }
}

const loadStatus = async () => {
  try {
    const response = await timeSourceApi.getNtpStatus()
    status.value = response.data.data
  } catch (error) {
    console.error('加载NTP状态失败:', error)
  }
}

const loadClients = async () => {
  try {
    const response = await timeSourceApi.getNtpClients()
    clients.value = response.data.data
  } catch (error) {
    console.error('加载NTP客户端失败:', error)
  }
}

onMounted(() => {
  loadConfig()
  loadStatus()
  loadClients()
  
  // 定期更新状态和客户端列表
  const statusInterval = setInterval(() => {
    loadStatus()
    if (showClients.value) {
      loadClients()
    }
  }, 5000)
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})
</script>

<style lang="scss" scoped>
.ntp-config {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      color: var(--text-primary);
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  .config-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
    
    h5 {
      margin: var(--spacing-md) 0 var(--spacing-sm) 0;
      color: var(--text-secondary);
      font-size: 1rem;
      font-weight: 500;
    }
  }
  
  .refclock-subsection {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-md);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    
    .form-group {
      display: flex;
      flex-direction: column;
      
      &.full-width {
        grid-column: 1 / -1;
      }
      
      label {
        margin-bottom: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input, select {
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
  
  .network-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    
    .network-item {
      display: flex;
      gap: var(--spacing-sm);
      align-items: center;
      
      input {
        flex: 1;
      }
    }
  }
  
  .access-flags {
    grid-column: 1 / -1;
    margin-top: var(--spacing-md);
    
    h5 {
      margin-bottom: var(--spacing-sm);
    }
  }
  
  .flags-grid, .monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .flag-item, .monitoring-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .flag-label, .monitor-label {
      color: var(--text-primary);
      font-size: 0.9rem;
      cursor: pointer;
    }
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    
    input {
      opacity: 0;
      width: 0;
      height: 0;
      
      &:checked + label {
        background-color: var(--primary-color);
        
        &:before {
          transform: translateX(20px);
        }
      }
    }
    
    label {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--toggle-bg);
      border-radius: 24px;
      transition: all 0.3s ease;
      
      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
    }
  }
  
  .status-section, .clients-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--card-bg);
    border-radius: var(--radius-md);
    
    .status-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .status-value {
      font-weight: 600;
      
      &.success {
        color: var(--success-color);
      }
      
      &.error {
        color: var(--error-color);
      }
    }
  }
  
  .clients-table {
    overflow-x: auto;
    
    table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        padding: var(--spacing-sm);
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }
      
      th {
        background: var(--card-bg);
        color: var(--text-secondary);
        font-weight: 600;
        font-size: 0.9rem;
      }
      
      td {
        color: var(--text-primary);
        font-size: 0.9rem;
      }
    }
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.btn-sm {
      padding: var(--spacing-xs) var(--spacing-sm);
      font-size: 0.8rem;
    }
    
    &.btn-secondary {
      background: var(--glass-bg);
      color: var(--text-primary);
      border: 1px solid var(--glass-border);
      
      &:hover:not(:disabled) {
        background: var(--glass-hover);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    &.btn-info {
      background: var(--info-color);
      color: white;
      
      &:hover {
        background: var(--info-color-dark);
        transform: translateY(-1px);
      }
    }
    
    &.btn-danger {
      background: var(--error-color);
      color: white;
      
      &:hover {
        background: var(--error-color-dark);
        transform: translateY(-1px);
      }
    }
  }
}
</style>