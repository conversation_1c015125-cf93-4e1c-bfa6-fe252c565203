<template>
  <div class="gnss-config">
    <div class="config-header">
      <h3>GNSS接收器配置</h3>
      <div class="header-actions">
        <button 
          class="btn btn-secondary" 
          @click="testConnection"
          :disabled="testing"
        >
          <span v-if="testing">测试中...</span>
          <span v-else>测试连接</span>
        </button>
        <button 
          class="btn btn-secondary" 
          @click="resetReceiver"
          :disabled="resetting"
        >
          <span v-if="resetting">重置中...</span>
          <span v-else>重置接收器</span>
        </button>
      </div>
    </div>

    <div class="config-content">
      <!-- 基本设置 -->
      <div class="config-section">
        <h4>基本设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用GNSS</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="gnss-enabled" 
                v-model="config.enabled"
                @change="onConfigChange"
              >
              <label for="gnss-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>设备路径</label>
            <input 
              type="text" 
              v-model="config.device" 
              placeholder="/dev/ttyS0"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>波特率</label>
            <select v-model="config.baudRate" @change="onConfigChange">
              <option value="4800">4800</option>
              <option value="9600">9600</option>
              <option value="19200">19200</option>
              <option value="38400">38400</option>
              <option value="57600">57600</option>
              <option value="115200">115200</option>
            </select>
          </div>

          <div class="form-group">
            <label>数据位</label>
            <select v-model="config.dataBits" @change="onConfigChange">
              <option :value="8">8</option>
              <option :value="7">7</option>
              <option :value="6">6</option>
              <option :value="5">5</option>
            </select>
          </div>

          <div class="form-group">
            <label>停止位</label>
            <select v-model="config.stopBits" @change="onConfigChange">
              <option :value="1">1</option>
              <option :value="2">2</option>
            </select>
          </div>

          <div class="form-group">
            <label>校验位</label>
            <select v-model="config.parity" @change="onConfigChange">
              <option value="none">无</option>
              <option value="odd">奇校验</option>
              <option value="even">偶校验</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 接收器设置 -->
      <div class="config-section">
        <h4>接收器设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>接收器类型</label>
            <select v-model="config.receiver.type" @change="onConfigChange">
              <option value="ublox">u-blox</option>
              <option value="trimble">Trimble</option>
              <option value="novatel">NovAtel</option>
              <option value="generic">通用</option>
            </select>
          </div>

          <div class="form-group">
            <label>型号</label>
            <input 
              type="text" 
              v-model="config.receiver.model" 
              placeholder="接收器型号"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>固件版本</label>
            <input 
              type="text" 
              v-model="config.receiver.firmware" 
              placeholder="固件版本"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 天线设置 -->
      <div class="config-section">
        <h4>天线设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>天线类型</label>
            <select v-model="config.antenna.type" @change="onConfigChange">
              <option value="active">有源天线</option>
              <option value="passive">无源天线</option>
            </select>
          </div>

          <div class="form-group">
            <label>电缆长度 (米)</label>
            <input 
              type="number" 
              v-model.number="config.antenna.cableLength" 
              min="0" 
              max="1000" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>电缆延迟 (纳秒)</label>
            <input 
              type="number" 
              v-model.number="config.antenna.cableDelay" 
              min="0" 
              max="10000" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group full-width">
            <label>天线位置</label>
            <input 
              type="text" 
              v-model="config.antenna.location" 
              placeholder="天线安装位置描述"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 卫星系统 -->
      <div class="config-section">
        <h4>卫星系统</h4>
        <div class="satellite-grid">
          <div class="satellite-item" v-for="(enabled, system) in config.satellites" :key="system">
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                :id="`sat-${system}`" 
                v-model="config.satellites[system]"
                @change="onConfigChange"
              >
              <label :for="`sat-${system}`"></label>
            </div>
            <label :for="`sat-${system}`" class="satellite-label">
              {{ getSatelliteSystemName(system) }}
            </label>
          </div>
        </div>
      </div>

      <!-- 定位设置 -->
      <div class="config-section">
        <h4>定位设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>定位模式</label>
            <select v-model="config.positioning.mode" @change="onConfigChange">
              <option value="auto">自动</option>
              <option value="static">静态</option>
              <option value="kinematic">动态</option>
            </select>
          </div>

          <div class="form-group">
            <label>高度角 (度)</label>
            <input 
              type="number" 
              v-model.number="config.positioning.elevation" 
              min="0" 
              max="90" 
              step="1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>PDOP阈值</label>
            <input 
              type="number" 
              v-model.number="config.positioning.pdop" 
              min="1" 
              max="20" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>HDOP阈值</label>
            <input 
              type="number" 
              v-model.number="config.positioning.hdop" 
              min="1" 
              max="20" 
              step="0.1"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 时间脉冲设置 -->
      <div class="config-section">
        <h4>时间脉冲设置</h4>
        <div class="form-grid">
          <div class="form-group">
            <label>启用PPS</label>
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                id="pps-enabled" 
                v-model="config.timing.ppsEnabled"
                @change="onConfigChange"
              >
              <label for="pps-enabled"></label>
            </div>
          </div>

          <div class="form-group">
            <label>PPS极性</label>
            <select v-model="config.timing.ppsPolarity" @change="onConfigChange">
              <option value="rising">上升沿</option>
              <option value="falling">下降沿</option>
            </select>
          </div>

          <div class="form-group">
            <label>脉冲宽度 (微秒)</label>
            <input 
              type="number" 
              v-model.number="config.timing.ppsWidth" 
              min="1" 
              max="1000000" 
              step="1"
              @input="onConfigChange"
            >
          </div>

          <div class="form-group">
            <label>PPS偏移 (纳秒)</label>
            <input 
              type="number" 
              v-model.number="config.timing.ppsOffset" 
              min="-1000000" 
              max="1000000" 
              step="1"
              @input="onConfigChange"
            >
          </div>
        </div>
      </div>

      <!-- 高级设置 -->
      <div class="config-section">
        <h4>高级设置</h4>
        <div class="advanced-grid">
          <div class="advanced-item" v-for="(enabled, feature) in config.advanced" :key="feature">
            <div class="toggle-switch">
              <input 
                type="checkbox" 
                :id="`adv-${feature}`" 
                v-model="config.advanced[feature]"
                @change="onConfigChange"
              >
              <label :for="`adv-${feature}`"></label>
            </div>
            <label :for="`adv-${feature}`" class="advanced-label">
              {{ getAdvancedFeatureName(feature) }}
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态显示 -->
    <div class="status-section" v-if="status">
      <h4>接收器状态</h4>
      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">连接状态</span>
          <span class="status-value" :class="status.connected ? 'success' : 'error'">
            {{ status.connected ? '已连接' : '未连接' }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">卫星数量</span>
          <span class="status-value">{{ status.satellites || 0 }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">信号质量</span>
          <span class="status-value">{{ status.signalQuality || 'N/A' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">定位精度</span>
          <span class="status-value">{{ status.accuracy || 'N/A' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { timeSourceApi } from '@/services/api'
import type { GnssConfig } from '@/types/system'

const emit = defineEmits<{
  configChange: [config: GnssConfig]
}>()

const config = reactive<GnssConfig>({
  enabled: true,
  device: '/dev/ttyS0',
  baudRate: 9600,
  dataBits: 8,
  stopBits: 1,
  parity: 'none',
  receiver: {
    type: 'ublox',
    model: '',
    firmware: ''
  },
  antenna: {
    type: 'active',
    cableLength: 10,
    cableDelay: 50,
    location: ''
  },
  satellites: {
    gps: true,
    glonass: true,
    galileo: true,
    beidou: true,
    qzss: false,
    irnss: false
  },
  positioning: {
    mode: 'auto',
    elevation: 10,
    pdop: 6.0,
    hdop: 4.0,
    vdop: 6.0
  },
  timing: {
    ppsEnabled: true,
    ppsPolarity: 'rising',
    ppsWidth: 100000,
    ppsOffset: 0,
    timePulseEnabled: false,
    timePulseFreq: 1
  },
  advanced: {
    jamming: true,
    spoofing: true,
    multipath: true,
    ionospheric: true,
    tropospheric: true
  }
})

const status = ref<any>(null)
const testing = ref(false)
const resetting = ref(false)

const satelliteSystemNames: Record<string, string> = {
  gps: 'GPS',
  glonass: 'GLONASS',
  galileo: 'Galileo',
  beidou: '北斗',
  qzss: 'QZSS',
  irnss: 'IRNSS'
}

const advancedFeatureNames: Record<string, string> = {
  jamming: '干扰检测',
  spoofing: '欺骗检测',
  multipath: '多径抑制',
  ionospheric: '电离层校正',
  tropospheric: '对流层校正'
}

const getSatelliteSystemName = (system: string): string => {
  return satelliteSystemNames[system] || system.toUpperCase()
}

const getAdvancedFeatureName = (feature: string): string => {
  return advancedFeatureNames[feature] || feature
}

const onConfigChange = () => {
  emit('configChange', { ...config })
}

const testConnection = async () => {
  testing.value = true
  try {
    const response = await timeSourceApi.testGnssConnection(config)
    // 处理测试结果
    console.log('GNSS连接测试结果:', response.data)
  } catch (error) {
    console.error('GNSS连接测试失败:', error)
  } finally {
    testing.value = false
  }
}

const resetReceiver = async () => {
  resetting.value = true
  try {
    await timeSourceApi.resetGnssReceiver()
    // 重新加载状态
    await loadStatus()
  } catch (error) {
    console.error('重置GNSS接收器失败:', error)
  } finally {
    resetting.value = false
  }
}

const loadConfig = async () => {
  try {
    const response = await timeSourceApi.getGnssConfig()
    Object.assign(config, response.data.data)
  } catch (error) {
    console.error('加载GNSS配置失败:', error)
  }
}

const loadStatus = async () => {
  try {
    const response = await timeSourceApi.getGnssStatus()
    status.value = response.data.data
  } catch (error) {
    console.error('加载GNSS状态失败:', error)
  }
}

onMounted(() => {
  loadConfig()
  loadStatus()
  
  // 定期更新状态
  const statusInterval = setInterval(loadStatus, 5000)
  
  // 组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(statusInterval)
  })
})

// 监听配置变化
watch(config, () => {
  onConfigChange()
}, { deep: true })
</script>

<style lang="scss" scoped>
.gnss-config {
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h3 {
      margin: 0;
      color: var(--text-primary);
    }
    
    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }
  
  .config-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    
    .form-group {
      display: flex;
      flex-direction: column;
      
      &.full-width {
        grid-column: 1 / -1;
      }
      
      label {
        margin-bottom: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      input, select {
        padding: var(--spacing-sm);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--input-bg);
        color: var(--text-primary);
        font-size: 0.9rem;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
  
  .satellite-grid, .advanced-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .satellite-item, .advanced-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .satellite-label, .advanced-label {
      color: var(--text-primary);
      font-size: 0.9rem;
      cursor: pointer;
    }
  }
  
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    
    input {
      opacity: 0;
      width: 0;
      height: 0;
      
      &:checked + label {
        background-color: var(--primary-color);
        
        &:before {
          transform: translateX(20px);
        }
      }
    }
    
    label {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--toggle-bg);
      border-radius: 24px;
      transition: all 0.3s ease;
      
      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
    }
  }
  
  .status-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    
    h4 {
      margin: 0 0 var(--spacing-md) 0;
      color: var(--text-primary);
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
  
  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--card-bg);
    border-radius: var(--radius-md);
    
    .status-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
    
    .status-value {
      font-weight: 600;
      
      &.success {
        color: var(--success-color);
      }
      
      &.error {
        color: var(--error-color);
      }
    }
  }
  
  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.btn-secondary {
      background: var(--glass-bg);
      color: var(--text-primary);
      border: 1px solid var(--glass-border);
      
      &:hover:not(:disabled) {
        background: var(--glass-hover);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}
</style>