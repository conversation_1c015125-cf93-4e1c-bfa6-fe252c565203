<template>
  <div class="user-dialog-overlay" @click="handleOverlayClick">
    <div class="user-dialog glass-card" @click.stop>
      <div class="dialog-header">
        <h3>{{ user ? '编辑用户' : '创建用户' }}</h3>
        <button @click="$emit('cancel')" class="close-button">✕</button>
      </div>

      <form @submit.prevent="handleSubmit" class="user-form">
        <div class="form-group">
          <label for="username">用户名 *</label>
          <input
            id="username"
            v-model="formData.username"
            type="text"
            required
            :disabled="!!user"
            placeholder="请输入用户名"
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label for="email">邮箱 *</label>
          <input
            id="email"
            v-model="formData.email"
            type="email"
            required
            placeholder="请输入邮箱地址"
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label for="role">角色 *</label>
          <select id="role" v-model="formData.role" required class="form-select">
            <option value="">请选择角色</option>
            <option value="viewer">查看者</option>
            <option value="operator">操作员</option>
            <option value="administrator">管理员</option>
          </select>
          <div class="role-description">
            <div v-if="formData.role === 'viewer'" class="role-info">
              <strong>查看者权限：</strong>只能查看系统状态和日志，无法进行任何配置更改
            </div>
            <div v-if="formData.role === 'operator'" class="role-info">
              <strong>操作员权限：</strong>可以查看系统状态、修改配置，但无法管理用户
            </div>
            <div v-if="formData.role === 'administrator'" class="role-info">
              <strong>管理员权限：</strong>拥有所有权限，包括用户管理和系统维护
            </div>
          </div>
        </div>

        <div v-if="!user" class="form-group">
          <label for="password">密码 *</label>
          <input
            id="password"
            v-model="formData.password"
            type="password"
            required
            placeholder="请输入密码"
            class="form-input"
          />
          <div class="password-requirements">
            <div class="requirement" :class="{ valid: passwordChecks.length }">
              {{ passwordChecks.length ? '✓' : '✗' }} 至少8个字符
            </div>
            <div class="requirement" :class="{ valid: passwordChecks.uppercase }">
              {{ passwordChecks.uppercase ? '✓' : '✗' }} 包含大写字母
            </div>
            <div class="requirement" :class="{ valid: passwordChecks.lowercase }">
              {{ passwordChecks.lowercase ? '✓' : '✗' }} 包含小写字母
            </div>
            <div class="requirement" :class="{ valid: passwordChecks.number }">
              {{ passwordChecks.number ? '✓' : '✗' }} 包含数字
            </div>
          </div>
        </div>

        <div v-if="!user" class="form-group">
          <label for="confirmPassword">确认密码 *</label>
          <input
            id="confirmPassword"
            v-model="formData.confirmPassword"
            type="password"
            required
            placeholder="请再次输入密码"
            class="form-input"
          />
          <div v-if="formData.confirmPassword && !passwordsMatch" class="error-message">
            密码不匹配
          </div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="formData.active"
              type="checkbox"
              class="form-checkbox"
            />
            <span class="checkbox-text">启用用户</span>
          </label>
        </div>

        <div class="permissions-section">
          <h4>权限设置</h4>
          <div class="permissions-grid">
            <label
              v-for="permission in availablePermissions"
              :key="permission.id"
              class="permission-item"
            >
              <input
                v-model="formData.permissions"
                :value="permission.id"
                type="checkbox"
                :disabled="!canEditPermission(permission.id)"
                class="permission-checkbox"
              />
              <div class="permission-info">
                <span class="permission-name">{{ permission.name }}</span>
                <span class="permission-description">{{ permission.description }}</span>
              </div>
            </label>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" @click="$emit('cancel')" class="btn-secondary">
            取消
          </button>
          <button type="submit" :disabled="!isFormValid" class="btn-primary">
            {{ user ? '更新' : '创建' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { User } from '@/types/system'

interface Props {
  user?: User | null
}

interface Emits {
  (e: 'save', userData: Partial<User>): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  username: '',
  email: '',
  role: '',
  password: '',
  confirmPassword: '',
  active: true,
  permissions: [] as string[]
})

// 可用权限列表
const availablePermissions = [
  {
    id: 'read',
    name: '查看权限',
    description: '查看系统状态和配置'
  },
  {
    id: 'write',
    name: '写入权限',
    description: '修改系统配置'
  },
  {
    id: 'config',
    name: '配置权限',
    description: '管理系统配置'
  },
  {
    id: 'admin',
    name: '管理权限',
    description: '用户管理和系统维护'
  }
]

// 密码验证
const passwordChecks = computed(() => {
  const password = formData.value.password
  return {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /\d/.test(password)
  }
})

const passwordsMatch = computed(() => {
  return formData.value.password === formData.value.confirmPassword
})

const isPasswordValid = computed(() => {
  if (props.user) return true // 编辑用户时不需要验证密码
  
  return Object.values(passwordChecks.value).every(check => check) && passwordsMatch.value
})

// 表单验证
const isFormValid = computed(() => {
  return formData.value.username &&
         formData.value.email &&
         formData.value.role &&
         isPasswordValid.value
})

// 根据角色自动设置权限
watch(() => formData.value.role, (newRole) => {
  switch (newRole) {
    case 'viewer':
      formData.value.permissions = ['read']
      break
    case 'operator':
      formData.value.permissions = ['read', 'write', 'config']
      break
    case 'administrator':
      formData.value.permissions = ['read', 'write', 'config', 'admin']
      break
    default:
      formData.value.permissions = []
  }
})

// 检查是否可以编辑特定权限
const canEditPermission = (permissionId: string) => {
  // 管理员权限只有管理员角色才能拥有
  if (permissionId === 'admin') {
    return formData.value.role === 'administrator'
  }
  return true
}

// 处理表单提交
const handleSubmit = () => {
  if (!isFormValid.value) return
  
  const userData: Partial<User> = {
    username: formData.value.username,
    email: formData.value.email,
    role: formData.value.role as User['role'],
    active: formData.value.active,
    permissions: formData.value.permissions
  }
  
  // 只在创建用户时包含密码
  if (!props.user) {
    (userData as any).password = formData.value.password
  }
  
  emit('save', userData)
}

// 处理遮罩层点击
const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('cancel')
  }
}

// 初始化表单数据
onMounted(() => {
  if (props.user) {
    formData.value = {
      username: props.user.username,
      email: props.user.email,
      role: props.user.role,
      password: '',
      confirmPassword: '',
      active: props.user.active,
      permissions: [...props.user.permissions]
    }
  }
})
</script>

<style lang="scss" scoped>
.user-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.user-dialog {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  padding: var(--spacing-xl);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
    }
  }
}

.user-form {
  .form-group {
    margin-bottom: var(--spacing-lg);
    
    label {
      display: block;
      margin-bottom: var(--spacing-sm);
      color: var(--text-primary);
      font-weight: 500;
    }
    
    .form-input, .form-select {
      width: 100%;
      padding: var(--spacing-md);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-md);
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      font-size: 1rem;
      
      &::placeholder {
        color: var(--text-secondary);
      }
      
      &:focus {
        outline: none;
        border-color: var(--status-info);
        background: rgba(255, 255, 255, 0.15);
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
    
    .role-description {
      margin-top: var(--spacing-sm);
      
      .role-info {
        padding: var(--spacing-sm);
        background: rgba(59, 130, 246, 0.1);
        border-radius: var(--radius-sm);
        font-size: 0.9rem;
        color: var(--text-secondary);
        
        strong {
          color: var(--status-info);
        }
      }
    }
    
    .password-requirements {
      margin-top: var(--spacing-sm);
      
      .requirement {
        font-size: 0.8rem;
        color: var(--status-error);
        margin-bottom: var(--spacing-xs);
        
        &.valid {
          color: var(--status-success);
        }
      }
    }
    
    .error-message {
      margin-top: var(--spacing-sm);
      color: var(--status-error);
      font-size: 0.9rem;
    }
    
    .checkbox-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      cursor: pointer;
      
      .form-checkbox {
        width: auto;
      }
      
      .checkbox-text {
        color: var(--text-primary);
      }
    }
  }
}

.permissions-section {
  margin-bottom: var(--spacing-xl);
  
  h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
  }
  
  .permissions-grid {
    display: grid;
    gap: var(--spacing-md);
    
    .permission-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      padding: var(--spacing-md);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      
      .permission-checkbox {
        margin-top: 2px;
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
      
      .permission-info {
        flex: 1;
        
        .permission-name {
          display: block;
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .permission-description {
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
      }
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  .btn-secondary {
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
  
  .btn-primary {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--status-info);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #2563eb;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-dialog-overlay {
    padding: var(--spacing-md);
  }
  
  .user-dialog {
    padding: var(--spacing-lg);
  }
  
  .form-actions {
    flex-direction: column;
    
    .btn-secondary, .btn-primary {
      width: 100%;
    }
  }
}
</style>