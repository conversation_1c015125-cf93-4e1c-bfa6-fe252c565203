<template>
  <div class="main-layout">
    <!-- 左侧可折叠导航栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <!-- 侧边栏头部 -->
      <div class="sidebar-header">
        <div class="logo-section">
          <div class="logo-icon">⚡</div>
          <transition name="fade">
            <div v-show="!sidebarCollapsed" class="logo-text">授时服务器</div>
          </transition>
        </div>
        <button class="collapse-btn" @click="toggleSidebar">
          <div class="hamburger" :class="{ active: !sidebarCollapsed }">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </div>
      
      <!-- 导航菜单 -->
      <nav class="sidebar-nav">
        <router-link
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.path"
          class="nav-item"
          :class="{ active: $route.name === item.name }"
          :title="sidebarCollapsed ? item.title : ''"
        >
          <div class="nav-icon">
            <component :is="getIcon(item.icon)" />
          </div>
          <transition name="slide-fade">
            <span v-show="!sidebarCollapsed" class="nav-text">{{ item.title }}</span>
          </transition>
        </router-link>
      </nav>
      
      <!-- 侧边栏底部状态 -->
      <div class="sidebar-footer">
        <div class="system-status" :class="systemStatus">
          <div class="status-dot"></div>
          <transition name="slide-fade">
            <span v-show="!sidebarCollapsed" class="status-text">{{ systemStatusText }}</span>
          </transition>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content" :class="{ expanded: sidebarCollapsed }">
      <!-- 顶部状态栏 -->
      <header class="top-header">
        <div class="header-left">
          <h2 class="page-title">{{ currentPageTitle }}</h2>
        </div>
        <div class="header-center">
          <!-- 系统标题 -->
          <div class="system-title">
            <h1>高精度授时服务器系统</h1>
            <p class="subtitle">高精度授时 - 时钟同步服务 - 全球同步</p>
          </div>
        </div>
        <div class="header-right">
          <div class="header-info">
            <div class="current-time">{{ currentTime }}</div>
            <div class="version-info">v2.1</div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'date-fns'
import { useSystemStore } from '@/stores/system'

// 图标组件
import DashboardIcon from '@/components/Icons/DashboardIcon.vue'
import SatelliteIcon from '@/components/Icons/SatelliteIcon.vue'
import NetworkIcon from '@/components/Icons/NetworkIcon.vue'
import ClockIcon from '@/components/Icons/ClockIcon.vue'
import TimeIcon from '@/components/Icons/TimeIcon.vue'
import ChartIcon from '@/components/Icons/ChartIcon.vue'
import SettingsIcon from '@/components/Icons/SettingsIcon.vue'
import UsersIcon from '@/components/Icons/UsersIcon.vue'
import LogsIcon from '@/components/Icons/LogsIcon.vue'

const route = useRoute()
const systemStore = useSystemStore()
const currentTime = ref('')
const sidebarCollapsed = ref(false)
let timeInterval: ReturnType<typeof setInterval>

// 导航菜单项
const navigationItems = [
  { name: 'Dashboard', path: '/', title: '系统仪表盘', icon: 'dashboard' },
  { name: 'GNSS', path: '/gnss', title: 'GNSS接收', icon: 'satellite' },
  { name: 'PTP', path: '/ptp', title: 'PTP服务', icon: 'network' },
  { name: 'NTP', path: '/ntp', title: 'NTP服务', icon: 'clock' },
  { name: 'TOD', path: '/tod', title: 'TOD设置', icon: 'time' },
  { name: 'Performance', path: '/performance', title: '信号输出状态', icon: 'chart' },
  { name: 'Config', path: '/config', title: '系统资源', icon: 'settings' },
  { name: 'Users', path: '/users', title: '用户安全', icon: 'users' },
  { name: 'Logs', path: '/logs', title: '系统日志', icon: 'logs' }
]

// 当前页面标题
const currentPageTitle = computed(() => {
  const item = navigationItems.find(item => item.name === route.name)
  return item?.title || '系统仪表盘'
})

// 系统状态
const systemStatus = computed(() => {
  return systemStore.overallStatus || 'normal'
})

const systemStatusText = computed(() => {
  const statusMap = {
    normal: '正常运行',
    warning: '警告状态', 
    error: '故障状态',
    maintenance: '维护模式'
  }
  return statusMap[systemStatus.value as keyof typeof statusMap] || '未知状态'
})

// 侧边栏折叠切换
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 图标映射
const iconComponents = {
  dashboard: DashboardIcon,
  satellite: SatelliteIcon,
  network: NetworkIcon,
  clock: ClockIcon,
  time: TimeIcon,
  chart: ChartIcon,
  settings: SettingsIcon,
  users: UsersIcon,
  logs: LogsIcon
}

const getIcon = (iconName: string) => {
  return iconComponents[iconName as keyof typeof iconComponents] || DashboardIcon
}

// 更新时间
const updateTime = () => {
  currentTime.value = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background: var(--primary-gradient);
}

.sidebar {
  width: 240px;
  background: rgba(0, 0, 0, 0.2);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  
  &.collapsed {
    width: 60px;
  }
  
  .sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .logo-section {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      .logo-icon {
        font-size: 1.5rem;
        color: var(--status-info);
      }
      
      .logo-text {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--text-primary);
        white-space: nowrap;
      }
    }
    
    .collapse-btn {
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      padding: var(--spacing-xs);
      border-radius: var(--radius-sm);
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
      }
      
      .hamburger {
        width: 20px;
        height: 16px;
        position: relative;
        
        span {
          display: block;
          height: 2px;
          width: 100%;
          background: currentColor;
          border-radius: 1px;
          position: absolute;
          transition: all 0.3s ease;
          
          &:nth-child(1) { top: 0; }
          &:nth-child(2) { top: 7px; }
          &:nth-child(3) { top: 14px; }
        }
        
        &.active {
          span:nth-child(1) {
            transform: rotate(45deg);
            top: 7px;
          }
          span:nth-child(2) {
            opacity: 0;
          }
          span:nth-child(3) {
            transform: rotate(-45deg);
            top: 7px;
          }
        }
      }
    }
  }
  
  .sidebar-nav {
    flex: 1;
    padding: var(--spacing-md);
    
    .nav-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      border-radius: var(--radius-md);
      color: var(--text-secondary);
      text-decoration: none;
      transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      cursor: pointer;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
        transform: translateY(-1px) scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0) scale(1.01);
        transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      &.active {
        background: var(--status-info);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

        .nav-icon {
          color: white;
        }

        &:hover {
          transform: translateY(-2px) scale(1.02);
          box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }
      }
      
      .nav-icon {
        width: 20px;
        height: 20px;
        margin-right: var(--spacing-md);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }
      
      .nav-text {
        font-size: 0.9rem;
        font-weight: 500;
        white-space: nowrap;
      }
    }
  }
  
  .sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    
    .system-status {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm);
      border-radius: var(--radius-sm);
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;
      }
      
      .status-text {
        font-size: 0.8rem;
        color: var(--text-secondary);
        white-space: nowrap;
      }
      
      &.normal .status-dot { background: var(--status-success); }
      &.warning .status-dot { background: var(--status-warning); }
      &.error .status-dot { background: var(--status-error); }
      &.maintenance .status-dot { background: var(--status-info); }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-left 0.3s ease;
  
  &.expanded {
    margin-left: 0;
  }
}

.top-header {
  height: 60px;
  padding: 0 var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  
  .header-left {
    .page-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }
  }
  
  .header-center {
    flex: 1;
    text-align: center;
    
    .system-title {
      h1 {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
      }
      
      .subtitle {
        font-size: 0.8rem;
        color: var(--text-secondary);
        opacity: 0.8;
      }
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    
    .header-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: var(--spacing-xs);
      
      .current-time {
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.9rem;
        color: var(--text-secondary);
      }
      
      .version-info {
        font-size: 0.8rem;
        color: var(--text-muted);
        background: rgba(255, 255, 255, 0.1);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
      }
    }
  }
}

.page-content {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

// 过渡动画
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active, .slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter-from, .slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
    
    &.collapsed {
      width: 50px;
    }
  }
  
  .top-header {
    padding: 0 var(--spacing-md);
    
    .header-center {
      .system-title h1 {
        font-size: 1rem;
      }
      
      .subtitle {
        display: none;
      }
    }
    
    .header-right {
      gap: var(--spacing-md);
      
      .header-info {
        .current-time {
          font-size: 0.8rem;
        }
      }
    }
  }
  
  .page-content {
    padding: var(--spacing-md);
  }
}
</style>