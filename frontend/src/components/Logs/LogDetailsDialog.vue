<template>
  <div class="log-details-overlay" @click="handleOverlayClick">
    <div class="log-details-dialog glass-card" @click.stop>
      <div class="dialog-header">
        <h3>日志详情</h3>
        <button @click="$emit('close')" class="close-button">✕</button>
      </div>

      <div class="log-details-content">
        <div class="basic-info">
          <div class="info-row">
            <span class="info-label">时间:</span>
            <span class="info-value">{{ formatTimestamp(log.timestamp) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">级别:</span>
            <span class="info-value">
              <span class="level-badge" :class="getLevelBadgeClass(log.level)">
                {{ getLevelText(log.level) }}
              </span>
            </span>
          </div>
          <div class="info-row">
            <span class="info-label">组件:</span>
            <span class="info-value component-name">{{ log.component }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">消息:</span>
            <span class="info-value message-text">{{ log.message }}</span>
          </div>
        </div>

        <div v-if="log.details" class="details-section">
          <div class="section-header">
            <h4>详细信息</h4>
            <div class="section-actions">
              <button @click="copyDetails" class="btn-copy">
                📋 复制
              </button>
              <button @click="toggleFormat" class="btn-format">
                {{ isFormatted ? '原始' : '格式化' }}
              </button>
            </div>
          </div>
          
          <div class="details-content">
            <pre v-if="isFormatted" class="formatted-details">{{ formattedDetails }}</pre>
            <pre v-else class="raw-details">{{ rawDetails }}</pre>
          </div>
        </div>

        <div v-if="relatedLogs.length > 0" class="related-section">
          <h4>相关日志</h4>
          <div class="related-logs">
            <div
              v-for="(relatedLog, index) in relatedLogs"
              :key="index"
              class="related-log-item"
              :class="getLogLevelClass(relatedLog.level)"
            >
              <div class="related-log-time">{{ formatTime(relatedLog.timestamp) }}</div>
              <div class="related-log-level">
                <span class="level-badge" :class="getLevelBadgeClass(relatedLog.level)">
                  {{ getLevelText(relatedLog.level) }}
                </span>
              </div>
              <div class="related-log-message">{{ relatedLog.message }}</div>
            </div>
          </div>
        </div>

        <div class="context-section">
          <h4>上下文信息</h4>
          <div class="context-info">
            <div class="context-item">
              <span class="context-label">线程ID:</span>
              <span class="context-value">{{ getContextValue('threadId', 'main') }}</span>
            </div>
            <div class="context-item">
              <span class="context-label">进程ID:</span>
              <span class="context-value">{{ getContextValue('processId', '1234') }}</span>
            </div>
            <div class="context-item">
              <span class="context-label">用户:</span>
              <span class="context-value">{{ getContextValue('user', 'system') }}</span>
            </div>
            <div class="context-item">
              <span class="context-label">会话ID:</span>
              <span class="context-value">{{ getContextValue('sessionId', 'N/A') }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-actions">
        <button @click="exportLog" class="btn-export">
          📥 导出此日志
        </button>
        <button @click="$emit('close')" class="btn-close">
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { LogEntry } from '@/types/system'

interface Props {
  log: LogEntry
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isFormatted = ref(true)

// 计算属性
const formattedDetails = computed(() => {
  if (!props.log.details) return ''
  try {
    return JSON.stringify(props.log.details, null, 2)
  } catch {
    return String(props.log.details)
  }
})

const rawDetails = computed(() => {
  if (!props.log.details) return ''
  try {
    return JSON.stringify(props.log.details)
  } catch {
    return String(props.log.details)
  }
})

// 模拟相关日志（实际应用中应该从API获取）
const relatedLogs = computed(() => {
  // 这里应该根据组件、时间范围等条件获取相关日志
  return []
})

// 方法
const handleOverlayClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const formatTimestamp = (timestamp: string) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3
  })
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const getLevelBadgeClass = (level: string) => {
  const classes = {
    debug: 'badge-debug',
    info: 'badge-info',
    warning: 'badge-warning',
    error: 'badge-error',
    critical: 'badge-critical'
  }
  return classes[level as keyof typeof classes] || 'badge-info'
}

const getLogLevelClass = (level: string) => {
  const classes = {
    debug: 'log-debug',
    info: 'log-info',
    warning: 'log-warning',
    error: 'log-error',
    critical: 'log-critical'
  }
  return classes[level as keyof typeof classes] || 'log-info'
}

const getLevelText = (level: string) => {
  const texts = {
    debug: '调试',
    info: '信息',
    warning: '警告',
    error: '错误',
    critical: '严重'
  }
  return texts[level as keyof typeof texts] || level.toUpperCase()
}

const getContextValue = (key: string, defaultValue: string) => {
  if (props.log.details && typeof props.log.details === 'object') {
    return (props.log.details as any)[key] || defaultValue
  }
  return defaultValue
}

const toggleFormat = () => {
  isFormatted.value = !isFormatted.value
}

const copyDetails = async () => {
  try {
    const text = isFormatted.value ? formattedDetails.value : rawDetails.value
    await navigator.clipboard.writeText(text)
    // 可以添加复制成功的提示
  } catch (error) {
    console.error('Failed to copy details:', error)
  }
}

const exportLog = () => {
  try {
    const logData = {
      timestamp: props.log.timestamp,
      level: props.log.level,
      component: props.log.component,
      message: props.log.message,
      details: props.log.details
    }
    
    const logText = JSON.stringify(logData, null, 2)
    const blob = new Blob([logText], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `log-entry-${new Date(props.log.timestamp).toISOString().replace(/[:.]/g, '-')}.json`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export log:', error)
  }
}
</script>

<style lang="scss" scoped>
.log-details-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-lg);
}

.log-details-dialog {
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  padding: var(--spacing-xl);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  
  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
    }
  }
}

.log-details-content {
  .basic-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: var(--spacing-md);
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-label {
        min-width: 80px;
        color: var(--text-secondary);
        font-weight: 500;
      }
      
      .info-value {
        color: var(--text-primary);
        flex: 1;
        
        &.component-name {
          font-weight: 500;
          color: var(--status-info);
        }
        
        &.message-text {
          line-height: 1.4;
        }
      }
    }
  }
  
  .details-section, .related-section, .context-section {
    margin-bottom: var(--spacing-xl);
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      
      h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
      }
      
      .section-actions {
        display: flex;
        gap: var(--spacing-sm);
        
        button {
          padding: var(--spacing-xs) var(--spacing-sm);
          border: none;
          border-radius: var(--radius-sm);
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 0.8rem;
        }
        
        .btn-copy {
          background: var(--status-info);
          color: white;
          
          &:hover {
            background: #2563eb;
          }
        }
        
        .btn-format {
          background: rgba(255, 255, 255, 0.1);
          color: var(--text-primary);
          
          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }
    }
    
    .details-content {
      background: rgba(0, 0, 0, 0.3);
      border-radius: var(--radius-md);
      padding: var(--spacing-lg);
      
      .formatted-details, .raw-details {
        color: var(--text-primary);
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
        line-height: 1.4;
        white-space: pre-wrap;
        word-break: break-all;
        margin: 0;
      }
    }
  }
  
  .related-logs {
    .related-log-item {
      display: grid;
      grid-template-columns: 100px 80px 1fr;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-sm);
      border-left: 3px solid transparent;
      
      &.log-error, &.log-critical {
        border-left-color: var(--status-error);
      }
      
      &.log-warning {
        border-left-color: var(--status-warning);
      }
      
      &.log-info {
        border-left-color: var(--status-info);
      }
      
      .related-log-time {
        font-size: 0.8rem;
        color: var(--text-secondary);
      }
      
      .related-log-message {
        color: var(--text-primary);
        font-size: 0.9rem;
      }
    }
  }
  
  .context-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    
    .context-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-md);
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .context-label {
        color: var(--text-secondary);
        font-weight: 500;
      }
      
      .context-value {
        color: var(--text-primary);
        font-family: monospace;
      }
    }
  }
}

.level-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
  
  &.badge-debug {
    background: rgba(107, 114, 128, 0.2);
    color: var(--text-secondary);
  }
  
  &.badge-info {
    background: rgba(59, 130, 246, 0.2);
    color: var(--status-info);
  }
  
  &.badge-warning {
    background: rgba(245, 158, 11, 0.2);
    color: var(--status-warning);
  }
  
  &.badge-error {
    background: rgba(239, 68, 68, 0.2);
    color: var(--status-error);
  }
  
  &.badge-critical {
    background: rgba(239, 68, 68, 0.3);
    color: var(--status-error);
    font-weight: 600;
  }
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  button {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .btn-export {
    background: var(--status-success);
    color: white;
    
    &:hover {
      background: #16a34a;
    }
  }
  
  .btn-close {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .log-details-overlay {
    padding: var(--spacing-md);
  }
  
  .log-details-dialog {
    padding: var(--spacing-lg);
  }
  
  .related-log-item {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .context-item {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .dialog-actions {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
}
</style>