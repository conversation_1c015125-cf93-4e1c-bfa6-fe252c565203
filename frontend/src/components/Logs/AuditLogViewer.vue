<template>
  <div class="audit-log-viewer">
    <div class="log-header">
      <div class="log-stats">
        <div class="stat-item">
          <span class="stat-label">总计:</span>
          <span class="stat-value">{{ logs.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">错误:</span>
          <span class="stat-value error">{{ errorCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">警告:</span>
          <span class="stat-value warning">{{ warningCount }}</span>
        </div>
      </div>
      
      <div class="log-actions">
        <button @click="exportLogs" class="btn-export">
          📥 导出日志
        </button>
        <button @click="clearLogs" class="btn-clear">
          🗑️ 清空显示
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <span>加载日志中...</span>
    </div>

    <div v-else-if="logs.length === 0" class="empty-state">
      <div class="empty-icon">📝</div>
      <h3>暂无日志记录</h3>
      <p>在指定的时间范围内没有找到审计日志</p>
    </div>

    <div v-else class="log-container">
      <div class="log-table">
        <div class="log-table-header">
          <div class="header-cell time">时间</div>
          <div class="header-cell level">级别</div>
          <div class="header-cell component">组件</div>
          <div class="header-cell message">消息</div>
          <div class="header-cell actions">操作</div>
        </div>
        
        <div class="log-table-body">
          <div
            v-for="(log, index) in paginatedLogs"
            :key="index"
            class="log-row"
            :class="getLogLevelClass(log.level)"
          >
            <div class="log-cell time">
              <div class="time-display">
                <div class="time-main">{{ formatTime(log.timestamp) }}</div>
                <div class="time-date">{{ formatDate(log.timestamp) }}</div>
              </div>
            </div>
            
            <div class="log-cell level">
              <span class="level-badge" :class="getLevelBadgeClass(log.level)">
                {{ getLevelText(log.level) }}
              </span>
            </div>
            
            <div class="log-cell component">
              <span class="component-name">{{ log.component }}</span>
            </div>
            
            <div class="log-cell message">
              <div class="message-content">
                <div class="message-text">{{ log.message }}</div>
                <div v-if="log.details" class="message-details">
                  <button
                    @click="toggleDetails(index)"
                    class="details-toggle"
                  >
                    {{ expandedRows.has(index) ? '收起详情' : '展开详情' }}
                  </button>
                </div>
              </div>
            </div>
            
            <div class="log-cell actions">
              <button
                @click="copyLogEntry(log)"
                class="action-btn"
                title="复制日志条目"
              >
                📋
              </button>
              <button
                v-if="log.details"
                @click="viewDetails(log)"
                class="action-btn"
                title="查看详情"
              >
                🔍
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 展开的详情行 -->
      <div
        v-for="(log, index) in paginatedLogs"
        :key="`details-${index}`"
        v-show="expandedRows.has(index)"
        class="log-details-row"
      >
        <div class="details-content">
          <h4>详细信息</h4>
          <pre class="details-json">{{ formatDetails(log.details) }}</pre>
        </div>
      </div>

      <!-- 分页控件 -->
      <div v-if="totalPages > 1" class="pagination">
        <button
          @click="currentPage = 1"
          :disabled="currentPage === 1"
          class="page-btn"
        >
          ⏮️
        </button>
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="page-btn"
        >
          ⏪
        </button>
        
        <div class="page-info">
          <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
          <span class="page-size-selector">
            每页显示:
            <select v-model="pageSize" @change="currentPage = 1">
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </span>
        </div>
        
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="page-btn"
        >
          ⏩
        </button>
        <button
          @click="currentPage = totalPages"
          :disabled="currentPage === totalPages"
          class="page-btn"
        >
          ⏭️
        </button>
      </div>
    </div>

    <!-- 详情对话框 -->
    <LogDetailsDialog
      v-if="selectedLog"
      :log="selectedLog"
      @close="selectedLog = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { LogEntry } from '@/types/system'
import LogDetailsDialog from './LogDetailsDialog.vue'

interface Props {
  logs: LogEntry[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 状态管理
const currentPage = ref(1)
const pageSize = ref(50)
const expandedRows = ref(new Set<number>())
const selectedLog = ref<LogEntry | null>(null)

// 计算属性
const errorCount = computed(() => {
  return props.logs.filter(log => log.level === 'error' || log.level === 'critical').length
})

const warningCount = computed(() => {
  return props.logs.filter(log => log.level === 'warning').length
})

const totalPages = computed(() => {
  return Math.ceil(props.logs.length / pageSize.value)
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return props.logs.slice(start, end)
})

// 监听页面大小变化
watch(pageSize, () => {
  currentPage.value = 1
})

// 方法
const getLogLevelClass = (level: string) => {
  const classes = {
    debug: 'log-debug',
    info: 'log-info',
    warning: 'log-warning',
    error: 'log-error',
    critical: 'log-critical'
  }
  return classes[level as keyof typeof classes] || 'log-info'
}

const getLevelBadgeClass = (level: string) => {
  const classes = {
    debug: 'badge-debug',
    info: 'badge-info',
    warning: 'badge-warning',
    error: 'badge-error',
    critical: 'badge-critical'
  }
  return classes[level as keyof typeof classes] || 'badge-info'
}

const getLevelText = (level: string) => {
  const texts = {
    debug: '调试',
    info: '信息',
    warning: '警告',
    error: '错误',
    critical: '严重'
  }
  return texts[level as keyof typeof texts] || level.toUpperCase()
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const formatDate = (timestamp: string) => {
  return new Date(timestamp).toLocaleDateString('zh-CN')
}

const formatDetails = (details: any) => {
  if (!details) return ''
  return JSON.stringify(details, null, 2)
}

const toggleDetails = (index: number) => {
  if (expandedRows.value.has(index)) {
    expandedRows.value.delete(index)
  } else {
    expandedRows.value.add(index)
  }
}

const viewDetails = (log: LogEntry) => {
  selectedLog.value = log
}

const copyLogEntry = async (log: LogEntry) => {
  try {
    const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.component}: ${log.message}`
    await navigator.clipboard.writeText(logText)
    // 可以添加复制成功的提示
  } catch (error) {
    console.error('Failed to copy log entry:', error)
  }
}

const exportLogs = () => {
  try {
    const logText = props.logs.map(log => 
      `[${log.timestamp}] ${log.level.toUpperCase()} ${log.component}: ${log.message}`
    ).join('\n')
    
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.txt`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export logs:', error)
  }
}

const clearLogs = () => {
  expandedRows.value.clear()
  currentPage.value = 1
}
</script>

<style lang="scss" scoped>
.audit-log-viewer {
  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
    
    .log-stats {
      display: flex;
      gap: var(--spacing-lg);
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .stat-label {
          color: var(--text-secondary);
          font-size: 0.9rem;
        }
        
        .stat-value {
          font-weight: 600;
          color: var(--text-primary);
          
          &.error {
            color: var(--status-error);
          }
          
          &.warning {
            color: var(--status-warning);
          }
        }
      }
    }
    
    .log-actions {
      display: flex;
      gap: var(--spacing-sm);
      
      button {
        padding: var(--spacing-sm) var(--spacing-md);
        border: none;
        border-radius: var(--radius-sm);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.9rem;
      }
      
      .btn-export {
        background: var(--status-info);
        color: white;
        
        &:hover {
          background: #2563eb;
        }
      }
      
      .btn-clear {
        background: rgba(255, 255, 255, 0.1);
        color: var(--text-primary);
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid var(--status-info);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  text-align: center;
  
  .empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
  }
  
  h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
  }
  
  p {
    color: var(--text-secondary);
  }
}

.log-container {
  .log-table {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    overflow: hidden;
    
    .log-table-header {
      display: grid;
      grid-template-columns: 150px 80px 120px 1fr 80px;
      background: rgba(255, 255, 255, 0.1);
      
      .header-cell {
        padding: var(--spacing-md);
        font-weight: 600;
        color: var(--text-primary);
        border-right: 1px solid rgba(255, 255, 255, 0.1);
        
        &:last-child {
          border-right: none;
        }
      }
    }
    
    .log-table-body {
      .log-row {
        display: grid;
        grid-template-columns: 150px 80px 120px 1fr 80px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.2s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.05);
        }
        
        &.log-error, &.log-critical {
          border-left: 3px solid var(--status-error);
        }
        
        &.log-warning {
          border-left: 3px solid var(--status-warning);
        }
        
        &.log-info {
          border-left: 3px solid var(--status-info);
        }
        
        &.log-debug {
          border-left: 3px solid var(--text-secondary);
        }
        
        .log-cell {
          padding: var(--spacing-md);
          border-right: 1px solid rgba(255, 255, 255, 0.05);
          display: flex;
          align-items: center;
          
          &:last-child {
            border-right: none;
          }
          
          &.time {
            .time-display {
              .time-main {
                font-weight: 500;
                color: var(--text-primary);
                font-size: 0.9rem;
              }
              
              .time-date {
                font-size: 0.8rem;
                color: var(--text-secondary);
              }
            }
          }
          
          &.level {
            .level-badge {
              padding: var(--spacing-xs) var(--spacing-sm);
              border-radius: var(--radius-sm);
              font-size: 0.8rem;
              font-weight: 500;
              
              &.badge-debug {
                background: rgba(107, 114, 128, 0.2);
                color: var(--text-secondary);
              }
              
              &.badge-info {
                background: rgba(59, 130, 246, 0.2);
                color: var(--status-info);
              }
              
              &.badge-warning {
                background: rgba(245, 158, 11, 0.2);
                color: var(--status-warning);
              }
              
              &.badge-error {
                background: rgba(239, 68, 68, 0.2);
                color: var(--status-error);
              }
              
              &.badge-critical {
                background: rgba(239, 68, 68, 0.3);
                color: var(--status-error);
                font-weight: 600;
              }
            }
          }
          
          &.component {
            .component-name {
              font-weight: 500;
              color: var(--text-primary);
              font-size: 0.9rem;
            }
          }
          
          &.message {
            .message-content {
              width: 100%;
              
              .message-text {
                color: var(--text-primary);
                line-height: 1.4;
                word-break: break-word;
              }
              
              .message-details {
                margin-top: var(--spacing-sm);
                
                .details-toggle {
                  background: none;
                  border: none;
                  color: var(--status-info);
                  cursor: pointer;
                  font-size: 0.8rem;
                  text-decoration: underline;
                  
                  &:hover {
                    color: #2563eb;
                  }
                }
              }
            }
          }
          
          &.actions {
            justify-content: center;
            gap: var(--spacing-sm);
            
            .action-btn {
              background: none;
              border: none;
              cursor: pointer;
              padding: var(--spacing-xs);
              border-radius: var(--radius-sm);
              transition: all 0.2s ease;
              
              &:hover {
                background: rgba(255, 255, 255, 0.1);
              }
            }
          }
        }
      }
    }
  }
}

.log-details-row {
  background: rgba(59, 130, 246, 0.1);
  border-radius: var(--radius-md);
  margin: var(--spacing-sm) 0;
  
  .details-content {
    padding: var(--spacing-lg);
    
    h4 {
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
      font-size: 1rem;
    }
    
    .details-json {
      background: rgba(0, 0, 0, 0.3);
      border-radius: var(--radius-sm);
      padding: var(--spacing-md);
      color: var(--text-primary);
      font-family: 'Courier New', monospace;
      font-size: 0.8rem;
      overflow-x: auto;
      white-space: pre-wrap;
    }
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  
  .page-btn {
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.2);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .page-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: 0.9rem;
    
    .page-size-selector {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      select {
        padding: var(--spacing-xs);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-sm);
        color: var(--text-primary);
        
        &:focus {
          outline: none;
          border-color: var(--status-info);
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .log-table-header,
  .log-row {
    grid-template-columns: 1fr;
    
    .log-cell {
      border-right: none;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
  
  .log-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .pagination {
    flex-wrap: wrap;
  }
}
</style>