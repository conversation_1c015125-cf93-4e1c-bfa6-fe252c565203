<template>
  <div class="logging-config">
    <div class="config-header">
      <h3>日志设置</h3>
      <p>配置系统日志记录和管理参数</p>
    </div>

    <!-- 基本日志设置 -->
    <div class="basic-logging-section">
      <div class="section-header">
        <h4>基本设置</h4>
      </div>

      <div class="basic-config">
        <div class="form-row">
          <div class="form-group">
            <label>日志级别</label>
            <select v-model="config.level" class="form-select">
              <option value="debug">调试 (DEBUG)</option>
              <option value="info">信息 (INFO)</option>
              <option value="warning">警告 (WARNING)</option>
              <option value="error">错误 (ERROR)</option>
              <option value="critical">严重 (CRITICAL)</option>
            </select>
            <small class="form-help">
              选择更高级别将减少日志输出量，但可能遗漏重要信息
            </small>
          </div>
          <div class="form-group">
            <label>日志格式</label>
            <select v-model="loggingConfig.format" class="form-select">
              <option value="json">JSON格式</option>
              <option value="text">文本格式</option>
              <option value="structured">结构化格式</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>单个日志文件最大大小 (MB)</label>
            <input
              v-model.number="config.maxSize"
              type="number"
              min="1"
              max="1000"
              placeholder="100"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>日志文件保留天数</label>
            <input
              v-model.number="config.retention"
              type="number"
              min="1"
              max="3650"
              placeholder="30"
              class="form-input"
            />
          </div>
        </div>

        <div class="form-group">
          <label>日志文件路径</label>
          <input
            v-model="loggingConfig.logPath"
            type="text"
            placeholder="/var/log/timing-server"
            class="form-input"
          />
        </div>
      </div>
    </div>

    <!-- 组件日志设置 -->
    <div class="component-logging-section">
      <div class="section-header">
        <h4>组件日志设置</h4>
      </div>

      <div class="component-config">
        <div class="component-list">
          <div
            v-for="component in componentSettings"
            :key="component.name"
            class="component-item"
          >
            <div class="component-header">
              <div class="component-info">
                <span class="component-name">{{ component.displayName }}</span>
                <span class="component-description">{{ component.description }}</span>
              </div>
              <label class="toggle-label">
                <input
                  v-model="component.enabled"
                  type="checkbox"
                  class="toggle-checkbox"
                />
                <span class="toggle-slider"></span>
              </label>
            </div>

            <div v-if="component.enabled" class="component-settings">
              <div class="form-row">
                <div class="form-group">
                  <label>日志级别</label>
                  <select v-model="component.level" class="form-select">
                    <option value="debug">调试</option>
                    <option value="info">信息</option>
                    <option value="warning">警告</option>
                    <option value="error">错误</option>
                    <option value="critical">严重</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>采样率 (%)</label>
                  <input
                    v-model.number="component.samplingRate"
                    type="number"
                    min="1"
                    max="100"
                    placeholder="100"
                    class="form-input"
                  />
                </div>
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input
                    v-model="component.includeStackTrace"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  <span>包含堆栈跟踪</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 远程日志设置 -->
    <div class="remote-logging-section">
      <div class="section-header">
        <h4>远程日志</h4>
      </div>

      <div class="remote-config">
        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="config.remoteLogging"
              type="checkbox"
              class="form-checkbox"
            />
            <span>启用远程日志</span>
          </label>
        </div>

        <div v-if="config.remoteLogging" class="remote-settings">
          <div class="form-row">
            <div class="form-group">
              <label>远程服务器地址</label>
              <input
                v-model="config.remoteServer"
                type="text"
                placeholder="syslog.example.com"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>端口</label>
              <input
                v-model.number="remoteConfig.port"
                type="number"
                min="1"
                max="65535"
                placeholder="514"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>协议</label>
              <select v-model="remoteConfig.protocol" class="form-select">
                <option value="udp">UDP</option>
                <option value="tcp">TCP</option>
                <option value="tls">TLS</option>
              </select>
            </div>
            <div class="form-group">
              <label>设施代码</label>
              <select v-model="remoteConfig.facility" class="form-select">
                <option value="16">本地使用0 (local0)</option>
                <option value="17">本地使用1 (local1)</option>
                <option value="18">本地使用2 (local2)</option>
                <option value="19">本地使用3 (local3)</option>
                <option value="20">本地使用4 (local4)</option>
                <option value="21">本地使用5 (local5)</option>
                <option value="22">本地使用6 (local6)</option>
                <option value="23">本地使用7 (local7)</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="remoteConfig.compression"
                type="checkbox"
                class="form-checkbox"
              />
              <span>启用压缩</span>
            </label>
          </div>

          <div class="remote-test">
            <button @click="testRemoteConnection" class="btn-test" :disabled="testingRemote">
              {{ testingRemote ? '测试中...' : '测试连接' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志轮转设置 -->
    <div class="rotation-section">
      <div class="section-header">
        <h4>日志轮转</h4>
      </div>

      <div class="rotation-config">
        <div class="form-row">
          <div class="form-group">
            <label>轮转策略</label>
            <select v-model="rotationConfig.strategy" class="form-select">
              <option value="size">按大小轮转</option>
              <option value="time">按时间轮转</option>
              <option value="both">大小和时间</option>
            </select>
          </div>
          <div class="form-group">
            <label>轮转间隔</label>
            <select v-model="rotationConfig.interval" class="form-select">
              <option value="hourly">每小时</option>
              <option value="daily">每天</option>
              <option value="weekly">每周</option>
              <option value="monthly">每月</option>
            </select>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>保留文件数量</label>
            <input
              v-model.number="rotationConfig.keepFiles"
              type="number"
              min="1"
              max="100"
              placeholder="10"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>压缩延迟（小时）</label>
            <input
              v-model.number="rotationConfig.compressDelay"
              type="number"
              min="0"
              max="168"
              placeholder="24"
              class="form-input"
            />
          </div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="rotationConfig.compressOld"
              type="checkbox"
              class="form-checkbox"
            />
            <span>压缩旧日志文件</span>
          </label>
        </div>
      </div>
    </div>

    <!-- 日志分析设置 -->
    <div class="analysis-section">
      <div class="section-header">
        <h4>日志分析</h4>
      </div>

      <div class="analysis-config">
        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="analysisConfig.enabled"
              type="checkbox"
              class="form-checkbox"
            />
            <span>启用日志分析</span>
          </label>
        </div>

        <div v-if="analysisConfig.enabled" class="analysis-settings">
          <div class="analysis-rules">
            <label>分析规则</label>
            <div class="rule-checkboxes">
              <label class="checkbox-label">
                <input
                  v-model="analysisConfig.rules.errorPatterns"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>错误模式检测</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="analysisConfig.rules.performanceIssues"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>性能问题检测</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="analysisConfig.rules.securityEvents"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>安全事件检测</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="analysisConfig.rules.anomalyDetection"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>异常检测</span>
              </label>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>分析间隔（分钟）</label>
              <input
                v-model.number="analysisConfig.interval"
                type="number"
                min="1"
                max="1440"
                placeholder="5"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>告警阈值</label>
              <input
                v-model.number="analysisConfig.alertThreshold"
                type="number"
                min="1"
                max="1000"
                placeholder="10"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="analysisConfig.autoReport"
                type="checkbox"
                class="form-checkbox"
              />
              <span>自动生成分析报告</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="config-actions">
      <button @click="testLogging" class="btn-test" :disabled="testing">
        {{ testing ? '测试中...' : '测试日志配置' }}
      </button>
      <button @click="saveConfiguration" class="btn-save" :disabled="saving">
        {{ saving ? '保存中...' : '保存配置' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

interface Props {
  modelValue: {
    level: string
    maxSize: number
    retention: number
    remoteLogging: boolean
    remoteServer?: string
  }
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'save', config: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 配置数据
const config = reactive({
  level: props.modelValue.level || 'info',
  maxSize: props.modelValue.maxSize || 100,
  retention: props.modelValue.retention || 30,
  remoteLogging: props.modelValue.remoteLogging || false,
  remoteServer: props.modelValue.remoteServer || ''
})

const loggingConfig = reactive({
  format: 'json',
  logPath: '/var/log/timing-server'
})

const remoteConfig = reactive({
  port: 514,
  protocol: 'udp',
  facility: 16,
  compression: false
})

const rotationConfig = reactive({
  strategy: 'size',
  interval: 'daily',
  keepFiles: 10,
  compressDelay: 24,
  compressOld: true
})

const analysisConfig = reactive({
  enabled: false,
  rules: {
    errorPatterns: true,
    performanceIssues: true,
    securityEvents: true,
    anomalyDetection: false
  },
  interval: 5,
  alertThreshold: 10,
  autoReport: false
})

// 组件日志设置
const componentSettings = reactive([
  {
    name: 'timing_engine',
    displayName: '授时引擎',
    description: '核心授时服务组件',
    enabled: true,
    level: 'info',
    samplingRate: 100,
    includeStackTrace: false
  },
  {
    name: 'gnss',
    displayName: 'GNSS接收器',
    description: 'GNSS信号接收和处理',
    enabled: true,
    level: 'info',
    samplingRate: 100,
    includeStackTrace: false
  },
  {
    name: 'ptp',
    displayName: 'PTP服务',
    description: 'PTP协议处理',
    enabled: true,
    level: 'warning',
    samplingRate: 100,
    includeStackTrace: false
  },
  {
    name: 'ntp',
    displayName: 'NTP服务',
    description: 'NTP协议处理',
    enabled: true,
    level: 'warning',
    samplingRate: 100,
    includeStackTrace: false
  },
  {
    name: 'api',
    displayName: 'API服务',
    description: 'REST API和WebSocket服务',
    enabled: true,
    level: 'info',
    samplingRate: 50,
    includeStackTrace: false
  },
  {
    name: 'auth',
    displayName: '认证服务',
    description: '用户认证和授权',
    enabled: true,
    level: 'warning',
    samplingRate: 100,
    includeStackTrace: true
  },
  {
    name: 'config',
    displayName: '配置管理',
    description: '系统配置管理',
    enabled: true,
    level: 'info',
    samplingRate: 100,
    includeStackTrace: false
  },
  {
    name: 'hardware',
    displayName: '硬件抽象层',
    description: '硬件接口和驱动',
    enabled: true,
    level: 'error',
    samplingRate: 100,
    includeStackTrace: true
  }
])

const testing = ref(false)
const testingRemote = ref(false)
const saving = ref(false)

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 方法
const testRemoteConnection = async () => {
  testingRemote.value = true
  try {
    // 模拟远程连接测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('Remote logging connection test passed')
  } catch (error) {
    console.error('Remote logging connection test failed:', error)
  } finally {
    testingRemote.value = false
  }
}

const testLogging = async () => {
  testing.value = true
  try {
    // 模拟日志配置测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('Logging configuration test passed')
  } catch (error) {
    console.error('Logging configuration test failed:', error)
  } finally {
    testing.value = false
  }
}

const saveConfiguration = async () => {
  saving.value = true
  try {
    const fullConfig = {
      ...config,
      logging: loggingConfig,
      remote: remoteConfig,
      rotation: rotationConfig,
      analysis: analysisConfig,
      components: componentSettings
    }
    emit('save', fullConfig)
  } catch (error) {
    console.error('Failed to save logging configuration:', error)
  } finally {
    saving.value = false
  }
}
</script>

<style lang="scss" scoped>
.logging-config {
  .config-header {
    margin-bottom: var(--spacing-xl);
    
    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    p {
      color: var(--text-secondary);
    }
  }
  
  .section-header {
    margin-bottom: var(--spacing-lg);
    
    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }
  }
}

.basic-logging-section, .component-logging-section, .remote-logging-section,
.rotation-section, .analysis-section {
  margin-bottom: var(--spacing-2xl);
}

.remote-settings, .analysis-settings {
  background: rgba(59, 130, 246, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.component-list {
  .component-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    
    .component-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-md);
      
      .component-info {
        .component-name {
          display: block;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: var(--spacing-xs);
        }
        
        .component-description {
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
      }
    }
    
    .component-settings {
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-md);
      padding: var(--spacing-md);
      margin-top: var(--spacing-md);
    }
  }
}

.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  
  .toggle-checkbox {
    display: none;
  }
  
  .toggle-slider {
    width: 44px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    position: relative;
    transition: all 0.2s ease;
    
    &::before {
      content: '';
      position: absolute;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      top: 2px;
      left: 2px;
      transition: all 0.2s ease;
    }
  }
  
  .toggle-checkbox:checked + .toggle-slider {
    background: var(--status-success);
    
    &::before {
      transform: translateX(20px);
    }
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-group {
  margin-bottom: var(--spacing-lg);
  
  label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
  }
  
  .form-input, .form-select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    
    &::placeholder {
      color: var(--text-secondary);
    }
    
    &:focus {
      outline: none;
      border-color: var(--status-info);
      background: rgba(255, 255, 255, 0.15);
    }
  }
  
  .form-help {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-secondary);
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    
    .form-checkbox {
      width: auto;
    }
    
    span {
      color: var(--text-primary);
    }
  }
}

.rule-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.remote-test {
  margin-top: var(--spacing-lg);
  
  .btn-test {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--status-warning);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #d97706;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.config-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  .btn-test {
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(245, 158, 11, 0.2);
    color: var(--status-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: rgba(245, 158, 11, 0.3);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .btn-save {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--status-success);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #16a34a;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}
</style>