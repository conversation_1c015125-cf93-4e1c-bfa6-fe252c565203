<template>
  <div class="network-config">
    <div class="config-header">
      <h3>网络设置</h3>
      <p>配置系统网络接口和连接参数</p>
    </div>

    <div class="interfaces-section">
      <div class="section-header">
        <h4>网络接口</h4>
        <button @click="addInterface" class="btn-add">
          <span class="icon">➕</span>
          添加接口
        </button>
      </div>

      <div class="interfaces-list">
        <div
          v-for="(interface_, index) in config.interfaces"
          :key="index"
          class="interface-card"
        >
          <div class="interface-header">
            <div class="interface-name">
              <input
                v-model="interface_.name"
                type="text"
                placeholder="接口名称 (如: eth0)"
                class="form-input"
              />
              <label class="toggle-label">
                <input
                  v-model="interface_.enabled"
                  type="checkbox"
                  class="toggle-checkbox"
                />
                <span class="toggle-slider"></span>
                <span class="toggle-text">{{ interface_.enabled ? '启用' : '禁用' }}</span>
              </label>
            </div>
            <button @click="removeInterface(index)" class="btn-remove">
              🗑️
            </button>
          </div>

          <div class="interface-config">
            <div class="form-row">
              <div class="form-group">
                <label>IP地址</label>
                <input
                  v-model="interface_.ip"
                  type="text"
                  placeholder="***********00"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label>子网掩码</label>
                <input
                  v-model="interface_.netmask"
                  type="text"
                  placeholder="*************"
                  class="form-input"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label>网关</label>
                <input
                  v-model="interface_.gateway"
                  type="text"
                  placeholder="***********"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label>DNS服务器</label>
                <div class="dns-inputs">
                  <input
                    v-for="(dns, dnsIndex) in interface_.dns"
                    :key="dnsIndex"
                    v-model="interface_.dns[dnsIndex]"
                    type="text"
                    placeholder="*******"
                    class="form-input dns-input"
                  />
                  <button @click="addDnsServer(interface_)" class="btn-add-dns">
                    ➕
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="advanced-section">
      <div class="section-header">
        <h4>高级网络设置</h4>
      </div>

      <div class="advanced-config">
        <div class="form-row">
          <div class="form-group">
            <label>主机名</label>
            <input
              v-model="advancedConfig.hostname"
              type="text"
              placeholder="timing-server"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>域名</label>
            <input
              v-model="advancedConfig.domain"
              type="text"
              placeholder="local"
              class="form-input"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>MTU大小</label>
            <input
              v-model.number="advancedConfig.mtu"
              type="number"
              min="576"
              max="9000"
              placeholder="1500"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>网络模式</label>
            <select v-model="advancedConfig.mode" class="form-select">
              <option value="static">静态IP</option>
              <option value="dhcp">DHCP</option>
              <option value="pppoe">PPPoE</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="advancedConfig.ipv6Enabled"
              type="checkbox"
              class="form-checkbox"
            />
            <span>启用IPv6</span>
          </label>
        </div>

        <div v-if="advancedConfig.ipv6Enabled" class="ipv6-config">
          <div class="form-group">
            <label>IPv6地址</label>
            <input
              v-model="advancedConfig.ipv6Address"
              type="text"
              placeholder="2001:db8::1/64"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>IPv6网关</label>
            <input
              v-model="advancedConfig.ipv6Gateway"
              type="text"
              placeholder="2001:db8::1"
              class="form-input"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="firewall-section">
      <div class="section-header">
        <h4>防火墙设置</h4>
      </div>

      <div class="firewall-config">
        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="firewallConfig.enabled"
              type="checkbox"
              class="form-checkbox"
            />
            <span>启用防火墙</span>
          </label>
        </div>

        <div v-if="firewallConfig.enabled" class="firewall-rules">
          <div class="form-group">
            <label>允许的端口</label>
            <div class="port-inputs">
              <input
                v-for="(port, portIndex) in firewallConfig.allowedPorts"
                :key="portIndex"
                v-model="firewallConfig.allowedPorts[portIndex]"
                type="text"
                placeholder="80, 443, 8080"
                class="form-input port-input"
              />
              <button @click="addAllowedPort" class="btn-add-port">
                ➕
              </button>
            </div>
          </div>

          <div class="form-group">
            <label>阻止的IP地址</label>
            <div class="ip-inputs">
              <input
                v-for="(ip, ipIndex) in firewallConfig.blockedIPs"
                :key="ipIndex"
                v-model="firewallConfig.blockedIPs[ipIndex]"
                type="text"
                placeholder="***********00"
                class="form-input ip-input"
              />
              <button @click="addBlockedIP" class="btn-add-ip">
                ➕
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="config-actions">
      <button @click="testConfiguration" class="btn-test" :disabled="testing">
        {{ testing ? '测试中...' : '测试配置' }}
      </button>
      <button @click="saveConfiguration" class="btn-save" :disabled="saving">
        {{ saving ? '保存中...' : '保存配置' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

interface NetworkInterface {
  name: string
  ip: string
  netmask: string
  gateway: string
  dns: string[]
  enabled: boolean
}

interface Props {
  modelValue: {
    interfaces: NetworkInterface[]
  }
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'save', config: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 配置数据
const config = reactive({
  interfaces: props.modelValue.interfaces || []
})

const advancedConfig = reactive({
  hostname: 'timing-server',
  domain: 'local',
  mtu: 1500,
  mode: 'static',
  ipv6Enabled: false,
  ipv6Address: '',
  ipv6Gateway: ''
})

const firewallConfig = reactive({
  enabled: false,
  allowedPorts: ['22', '80', '443', '8080'],
  blockedIPs: [] as string[]
})

const testing = ref(false)
const saving = ref(false)

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 方法
const addInterface = () => {
  config.interfaces.push({
    name: '',
    ip: '',
    netmask: '*************',
    gateway: '',
    dns: ['*******', '*******'],
    enabled: true
  })
}

const removeInterface = (index: number) => {
  config.interfaces.splice(index, 1)
}

const addDnsServer = (interface_: NetworkInterface) => {
  interface_.dns.push('')
}

const addAllowedPort = () => {
  firewallConfig.allowedPorts.push('')
}

const addBlockedIP = () => {
  firewallConfig.blockedIPs.push('')
}

const testConfiguration = async () => {
  testing.value = true
  try {
    // 模拟测试配置
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('Configuration test passed')
  } catch (error) {
    console.error('Configuration test failed:', error)
  } finally {
    testing.value = false
  }
}

const saveConfiguration = async () => {
  saving.value = true
  try {
    const fullConfig = {
      ...config,
      advanced: advancedConfig,
      firewall: firewallConfig
    }
    emit('save', fullConfig)
  } catch (error) {
    console.error('Failed to save configuration:', error)
  } finally {
    saving.value = false
  }
}

// 初始化默认接口
if (config.interfaces.length === 0) {
  addInterface()
}
</script>

<style lang="scss" scoped>
.network-config {
  .config-header {
    margin-bottom: var(--spacing-xl);
    
    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    p {
      color: var(--text-secondary);
    }
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }
  }
}

.interfaces-section, .advanced-section, .firewall-section {
  margin-bottom: var(--spacing-2xl);
}

.interface-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  
  .interface-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    
    .interface-name {
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);
      flex: 1;
      
      .form-input {
        max-width: 200px;
      }
    }
    
    .btn-remove {
      background: rgba(239, 68, 68, 0.2);
      border: none;
      border-radius: var(--radius-sm);
      padding: var(--spacing-sm);
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(239, 68, 68, 0.3);
      }
    }
  }
  
  .interface-config {
    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }
  }
}

.dns-inputs, .port-inputs, .ip-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  
  .dns-input, .port-input, .ip-input {
    flex: 1;
    min-width: 120px;
  }
  
  .btn-add-dns, .btn-add-port, .btn-add-ip {
    background: var(--status-info);
    border: none;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #2563eb;
    }
  }
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  
  .toggle-checkbox {
    display: none;
  }
  
  .toggle-slider {
    width: 44px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    position: relative;
    transition: all 0.2s ease;
    
    &::before {
      content: '';
      position: absolute;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      top: 2px;
      left: 2px;
      transition: all 0.2s ease;
    }
  }
  
  .toggle-checkbox:checked + .toggle-slider {
    background: var(--status-success);
    
    &::before {
      transform: translateX(20px);
    }
  }
  
  .toggle-text {
    color: var(--text-primary);
    font-size: 0.9rem;
  }
}

.form-group {
  margin-bottom: var(--spacing-lg);
  
  label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
  }
  
  .form-input, .form-select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    
    &::placeholder {
      color: var(--text-secondary);
    }
    
    &:focus {
      outline: none;
      border-color: var(--status-info);
      background: rgba(255, 255, 255, 0.15);
    }
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    
    .form-checkbox {
      width: auto;
    }
    
    span {
      color: var(--text-primary);
    }
  }
}

.ipv6-config {
  background: rgba(59, 130, 246, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.firewall-rules {
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.config-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  .btn-test {
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(245, 158, 11, 0.2);
    color: var(--status-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: rgba(245, 158, 11, 0.3);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .btn-save {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--status-success);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #16a34a;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.btn-add {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--status-info);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #2563eb;
  }
  
  .icon {
    font-size: 0.9rem;
  }
}
</style>