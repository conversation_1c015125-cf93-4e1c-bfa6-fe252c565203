<template>
  <div class="security-config">
    <div class="config-header">
      <h3>安全配置</h3>
      <p>配置系统安全设置和访问控制</p>
    </div>

    <!-- HTTPS配置 -->
    <div class="https-section">
      <div class="section-header">
        <h4>HTTPS配置</h4>
      </div>

      <div class="https-config">
        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="config.httpsEnabled"
              type="checkbox"
              class="form-checkbox"
            />
            <span>启用HTTPS</span>
          </label>
        </div>

        <div v-if="config.httpsEnabled" class="https-settings">
          <div class="form-row">
            <div class="form-group">
              <label>SSL证书路径</label>
              <input
                v-model="config.certificatePath"
                type="text"
                placeholder="/etc/ssl/certs/server.crt"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>私钥路径</label>
              <input
                v-model="httpsConfig.keyPath"
                type="text"
                placeholder="/etc/ssl/private/server.key"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>CA证书路径</label>
              <input
                v-model="httpsConfig.caPath"
                type="text"
                placeholder="/etc/ssl/certs/ca.crt"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>TLS版本</label>
              <select v-model="httpsConfig.tlsVersion" class="form-select">
                <option value="1.2">TLS 1.2</option>
                <option value="1.3">TLS 1.3</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="httpsConfig.requireClientCert"
                type="checkbox"
                class="form-checkbox"
              />
              <span>要求客户端证书</span>
            </label>
          </div>

          <div class="certificate-actions">
            <button @click="generateCertificate" class="btn-generate">
              生成自签名证书
            </button>
            <button @click="uploadCertificate" class="btn-upload">
              上传证书
            </button>
            <button @click="testCertificate" class="btn-test">
              测试证书
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 访问控制 -->
    <div class="access-control-section">
      <div class="section-header">
        <h4>访问控制</h4>
      </div>

      <div class="access-control-config">
        <div class="form-group">
          <label>会话超时时间（秒）</label>
          <input
            v-model.number="config.sessionTimeout"
            type="number"
            min="300"
            max="86400"
            placeholder="1800"
            class="form-input"
          />
        </div>

        <div class="form-group">
          <label>允许的IP地址</label>
          <div class="ip-list">
            <div
              v-for="(ip, index) in config.allowedIPs"
              :key="index"
              class="ip-item"
            >
              <input
                v-model="config.allowedIPs[index]"
                type="text"
                placeholder="***********/24"
                class="form-input"
              />
              <button @click="removeAllowedIP(index)" class="btn-remove">
                🗑️
              </button>
            </div>
            <button @click="addAllowedIP" class="btn-add-ip">
              ➕ 添加IP
            </button>
          </div>
        </div>

        <div class="form-group">
          <label>阻止的IP地址</label>
          <div class="ip-list">
            <div
              v-for="(ip, index) in accessConfig.blockedIPs"
              :key="index"
              class="ip-item"
            >
              <input
                v-model="accessConfig.blockedIPs[index]"
                type="text"
                placeholder="*************"
                class="form-input"
              />
              <button @click="removeBlockedIP(index)" class="btn-remove">
                🗑️
              </button>
            </div>
            <button @click="addBlockedIP" class="btn-add-ip">
              ➕ 添加IP
            </button>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label>最大登录尝试次数</label>
            <input
              v-model.number="accessConfig.maxLoginAttempts"
              type="number"
              min="1"
              max="10"
              placeholder="5"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>锁定时间（分钟）</label>
            <input
              v-model.number="accessConfig.lockoutDuration"
              type="number"
              min="1"
              max="1440"
              placeholder="30"
              class="form-input"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 密码策略 -->
    <div class="password-policy-section">
      <div class="section-header">
        <h4>密码策略</h4>
      </div>

      <div class="password-policy-config">
        <div class="form-row">
          <div class="form-group">
            <label>最小密码长度</label>
            <input
              v-model.number="passwordPolicy.minLength"
              type="number"
              min="6"
              max="32"
              placeholder="8"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>密码过期天数</label>
            <input
              v-model.number="passwordPolicy.expiryDays"
              type="number"
              min="0"
              max="365"
              placeholder="90"
              class="form-input"
            />
          </div>
        </div>

        <div class="password-requirements">
          <label class="checkbox-label">
            <input
              v-model="passwordPolicy.requireUppercase"
              type="checkbox"
              class="form-checkbox"
            />
            <span>要求大写字母</span>
          </label>
          <label class="checkbox-label">
            <input
              v-model="passwordPolicy.requireLowercase"
              type="checkbox"
              class="form-checkbox"
            />
            <span>要求小写字母</span>
          </label>
          <label class="checkbox-label">
            <input
              v-model="passwordPolicy.requireNumbers"
              type="checkbox"
              class="form-checkbox"
            />
            <span>要求数字</span>
          </label>
          <label class="checkbox-label">
            <input
              v-model="passwordPolicy.requireSpecialChars"
              type="checkbox"
              class="form-checkbox"
            />
            <span>要求特殊字符</span>
          </label>
        </div>

        <div class="form-group">
          <label>密码历史记录</label>
          <input
            v-model.number="passwordPolicy.historyCount"
            type="number"
            min="0"
            max="24"
            placeholder="5"
            class="form-input"
          />
          <small class="form-help">防止重复使用最近的密码</small>
        </div>
      </div>
    </div>

    <!-- 审计设置 -->
    <div class="audit-section">
      <div class="section-header">
        <h4>审计设置</h4>
      </div>

      <div class="audit-config">
        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="auditConfig.enabled"
              type="checkbox"
              class="form-checkbox"
            />
            <span>启用审计日志</span>
          </label>
        </div>

        <div v-if="auditConfig.enabled" class="audit-settings">
          <div class="audit-events">
            <label>记录的事件类型</label>
            <div class="event-checkboxes">
              <label class="checkbox-label">
                <input
                  v-model="auditConfig.events.login"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>登录/登出</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="auditConfig.events.configChange"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>配置更改</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="auditConfig.events.userManagement"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>用户管理</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="auditConfig.events.systemControl"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>系统控制</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="auditConfig.events.securityEvents"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>安全事件</span>
              </label>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>审计日志保留天数</label>
              <input
                v-model.number="auditConfig.retentionDays"
                type="number"
                min="1"
                max="3650"
                placeholder="365"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>日志级别</label>
              <select v-model="auditConfig.logLevel" class="form-select">
                <option value="info">信息</option>
                <option value="warning">警告</option>
                <option value="error">错误</option>
                <option value="critical">严重</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="auditConfig.remoteLogging"
                type="checkbox"
                class="form-checkbox"
              />
              <span>启用远程日志</span>
            </label>
          </div>

          <div v-if="auditConfig.remoteLogging" class="remote-logging">
            <div class="form-row">
              <div class="form-group">
                <label>远程日志服务器</label>
                <input
                  v-model="auditConfig.remoteServer"
                  type="text"
                  placeholder="syslog.example.com"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label>端口</label>
                <input
                  v-model.number="auditConfig.remotePort"
                  type="number"
                  min="1"
                  max="65535"
                  placeholder="514"
                  class="form-input"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全事件监控 -->
    <div class="security-monitoring-section">
      <div class="section-header">
        <h4>安全事件监控</h4>
      </div>

      <div class="monitoring-config">
        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="monitoringConfig.enabled"
              type="checkbox"
              class="form-checkbox"
            />
            <span>启用安全监控</span>
          </label>
        </div>

        <div v-if="monitoringConfig.enabled" class="monitoring-settings">
          <div class="monitoring-rules">
            <label>监控规则</label>
            <div class="rule-checkboxes">
              <label class="checkbox-label">
                <input
                  v-model="monitoringConfig.rules.bruteForce"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>暴力破解检测</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="monitoringConfig.rules.suspiciousActivity"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>可疑活动检测</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="monitoringConfig.rules.unauthorizedAccess"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>未授权访问检测</span>
              </label>
              <label class="checkbox-label">
                <input
                  v-model="monitoringConfig.rules.configTampering"
                  type="checkbox"
                  class="form-checkbox"
                />
                <span>配置篡改检测</span>
              </label>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>告警阈值</label>
              <input
                v-model.number="monitoringConfig.alertThreshold"
                type="number"
                min="1"
                max="100"
                placeholder="5"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label>检查间隔（秒）</label>
              <input
                v-model.number="monitoringConfig.checkInterval"
                type="number"
                min="1"
                max="3600"
                placeholder="60"
                class="form-input"
              />
            </div>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="monitoringConfig.autoBlock"
                type="checkbox"
                class="form-checkbox"
              />
              <span>自动阻止可疑IP</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <div class="config-actions">
      <button @click="testSecurity" class="btn-test" :disabled="testing">
        {{ testing ? '测试中...' : '测试安全配置' }}
      </button>
      <button @click="saveConfiguration" class="btn-save" :disabled="saving">
        {{ saving ? '保存中...' : '保存配置' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

interface Props {
  modelValue: {
    httpsEnabled: boolean
    certificatePath: string
    allowedIPs: string[]
    sessionTimeout: number
  }
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'save', config: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 配置数据
const config = reactive({
  httpsEnabled: props.modelValue.httpsEnabled || false,
  certificatePath: props.modelValue.certificatePath || '/etc/ssl/certs/server.crt',
  allowedIPs: props.modelValue.allowedIPs || [],
  sessionTimeout: props.modelValue.sessionTimeout || 1800
})

const httpsConfig = reactive({
  keyPath: '/etc/ssl/private/server.key',
  caPath: '/etc/ssl/certs/ca.crt',
  tlsVersion: '1.3',
  requireClientCert: false
})

const accessConfig = reactive({
  blockedIPs: [] as string[],
  maxLoginAttempts: 5,
  lockoutDuration: 30
})

const passwordPolicy = reactive({
  minLength: 8,
  expiryDays: 90,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: false,
  historyCount: 5
})

const auditConfig = reactive({
  enabled: true,
  events: {
    login: true,
    configChange: true,
    userManagement: true,
    systemControl: true,
    securityEvents: true
  },
  retentionDays: 365,
  logLevel: 'info',
  remoteLogging: false,
  remoteServer: '',
  remotePort: 514
})

const monitoringConfig = reactive({
  enabled: true,
  rules: {
    bruteForce: true,
    suspiciousActivity: true,
    unauthorizedAccess: true,
    configTampering: true
  },
  alertThreshold: 5,
  checkInterval: 60,
  autoBlock: false
})

const testing = ref(false)
const saving = ref(false)

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
}, { deep: true })

// 方法
const addAllowedIP = () => {
  config.allowedIPs.push('')
}

const removeAllowedIP = (index: number) => {
  config.allowedIPs.splice(index, 1)
}

const addBlockedIP = () => {
  accessConfig.blockedIPs.push('')
}

const removeBlockedIP = (index: number) => {
  accessConfig.blockedIPs.splice(index, 1)
}

const generateCertificate = async () => {
  try {
    console.log('Generating self-signed certificate...')
    // 实现证书生成逻辑
  } catch (error) {
    console.error('Failed to generate certificate:', error)
  }
}

const uploadCertificate = () => {
  // 实现证书上传逻辑
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.crt,.pem'
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      console.log('Uploading certificate:', file.name)
      // 处理文件上传
    }
  }
  input.click()
}

const testCertificate = async () => {
  try {
    console.log('Testing certificate...')
    // 实现证书测试逻辑
  } catch (error) {
    console.error('Certificate test failed:', error)
  }
}

const testSecurity = async () => {
  testing.value = true
  try {
    // 模拟安全配置测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('Security configuration test passed')
  } catch (error) {
    console.error('Security configuration test failed:', error)
  } finally {
    testing.value = false
  }
}

const saveConfiguration = async () => {
  saving.value = true
  try {
    const fullConfig = {
      ...config,
      https: httpsConfig,
      access: accessConfig,
      passwordPolicy,
      audit: auditConfig,
      monitoring: monitoringConfig
    }
    emit('save', fullConfig)
  } catch (error) {
    console.error('Failed to save security configuration:', error)
  } finally {
    saving.value = false
  }
}

// 初始化默认IP
if (config.allowedIPs.length === 0) {
  config.allowedIPs.push('127.0.0.1', '***********/24')
}
</script>

<style lang="scss" scoped>
.security-config {
  .config-header {
    margin-bottom: var(--spacing-xl);
    
    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    p {
      color: var(--text-secondary);
    }
  }
  
  .section-header {
    margin-bottom: var(--spacing-lg);
    
    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }
  }
}

.https-section, .access-control-section, .password-policy-section, 
.audit-section, .security-monitoring-section {
  margin-bottom: var(--spacing-2xl);
}

.https-settings, .audit-settings, .monitoring-settings, .remote-logging {
  background: rgba(59, 130, 246, 0.1);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-group {
  margin-bottom: var(--spacing-lg);
  
  label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
  }
  
  .form-input, .form-select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    
    &::placeholder {
      color: var(--text-secondary);
    }
    
    &:focus {
      outline: none;
      border-color: var(--status-info);
      background: rgba(255, 255, 255, 0.15);
    }
  }
  
  .form-help {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-secondary);
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    
    .form-checkbox {
      width: auto;
    }
    
    span {
      color: var(--text-primary);
    }
  }
}

.ip-list {
  .ip-item {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    
    .form-input {
      flex: 1;
    }
    
    .btn-remove {
      background: rgba(239, 68, 68, 0.2);
      border: none;
      border-radius: var(--radius-sm);
      padding: var(--spacing-sm);
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: rgba(239, 68, 68, 0.3);
      }
    }
  }
  
  .btn-add-ip {
    background: var(--status-info);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #2563eb;
    }
  }
}

.password-requirements, .event-checkboxes, .rule-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.certificate-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
  
  button {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .btn-generate {
    background: var(--status-success);
    color: white;
    
    &:hover {
      background: #16a34a;
    }
  }
  
  .btn-upload {
    background: var(--status-info);
    color: white;
    
    &:hover {
      background: #2563eb;
    }
  }
  
  .btn-test {
    background: var(--status-warning);
    color: white;
    
    &:hover {
      background: #d97706;
    }
  }
}

.config-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  
  .btn-test {
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(245, 158, 11, 0.2);
    color: var(--status-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: rgba(245, 158, 11, 0.3);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .btn-save {
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--status-success);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #16a34a;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}
</style>