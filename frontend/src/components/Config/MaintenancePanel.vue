<template>
  <div class="maintenance-panel">
    <div class="config-header">
      <h3>系统维护</h3>
      <p>系统重启、备份恢复和固件升级操作</p>
    </div>

    <!-- 系统控制 -->
    <div class="system-control-section">
      <div class="section-header">
        <h4>系统控制</h4>
      </div>

      <div class="control-actions">
        <div class="action-card">
          <div class="action-icon">🔄</div>
          <div class="action-info">
            <h5>重启系统</h5>
            <p>重启授时服务器系统，所有服务将暂时中断</p>
          </div>
          <button @click="showRestartDialog = true" class="btn-action btn-warning">
            重启系统
          </button>
        </div>

        <div class="action-card">
          <div class="action-icon">⏹️</div>
          <div class="action-info">
            <h5>关闭系统</h5>
            <p>安全关闭系统，需要手动重新启动</p>
          </div>
          <button @click="showShutdownDialog = true" class="btn-action btn-danger">
            关闭系统
          </button>
        </div>

        <div class="action-card">
          <div class="action-icon">🔧</div>
          <div class="action-info">
            <h5>重启服务</h5>
            <p>仅重启授时服务，不影响系统运行</p>
          </div>
          <button @click="restartService" class="btn-action btn-info" :disabled="restarting">
            {{ restarting ? '重启中...' : '重启服务' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 备份管理 -->
    <div class="backup-section">
      <div class="section-header">
        <h4>备份管理</h4>
      </div>

      <div class="backup-actions">
        <div class="backup-create">
          <div class="form-group">
            <label>备份名称</label>
            <input
              v-model="backupName"
              type="text"
              placeholder="输入备份名称"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>备份描述</label>
            <textarea
              v-model="backupDescription"
              placeholder="输入备份描述（可选）"
              class="form-textarea"
              rows="3"
            ></textarea>
          </div>
          <button @click="createBackup" class="btn-create-backup" :disabled="creating">
            {{ creating ? '创建中...' : '创建备份' }}
          </button>
        </div>

        <div class="backup-list">
          <h5>现有备份</h5>
          <div class="backup-items">
            <div
              v-for="backup in backups"
              :key="backup.id"
              class="backup-item"
            >
              <div class="backup-info">
                <div class="backup-name">{{ backup.name }}</div>
                <div class="backup-details">
                  <span class="backup-date">{{ formatDate(backup.timestamp) }}</span>
                  <span class="backup-size">{{ formatSize(backup.size) }}</span>
                </div>
                <div v-if="backup.description" class="backup-description">
                  {{ backup.description }}
                </div>
              </div>
              <div class="backup-actions">
                <button @click="restoreBackup(backup)" class="btn-restore">
                  恢复
                </button>
                <button @click="downloadBackup(backup)" class="btn-download">
                  下载
                </button>
                <button @click="deleteBackup(backup)" class="btn-delete">
                  删除
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 固件升级 -->
    <div class="firmware-section">
      <div class="section-header">
        <h4>固件升级</h4>
      </div>

      <div class="firmware-info">
        <div class="current-version">
          <h5>当前版本信息</h5>
          <div class="version-details">
            <div class="version-item">
              <span class="version-label">系统版本:</span>
              <span class="version-value">{{ systemVersion.version }}</span>
            </div>
            <div class="version-item">
              <span class="version-label">构建时间:</span>
              <span class="version-value">{{ systemVersion.buildTime }}</span>
            </div>
            <div class="version-item">
              <span class="version-label">Git提交:</span>
              <span class="version-value">{{ systemVersion.gitCommit }}</span>
            </div>
            <div class="version-item">
              <span class="version-label">平台:</span>
              <span class="version-value">{{ systemVersion.platform }}</span>
            </div>
          </div>
        </div>

        <div class="upgrade-actions">
          <div class="upload-firmware">
            <input
              ref="firmwareInput"
              type="file"
              accept=".bin,.img,.tar.gz"
              @change="handleFirmwareSelect"
              style="display: none"
            />
            <button @click="selectFirmware" class="btn-select-firmware">
              选择固件文件
            </button>
            <div v-if="selectedFirmware" class="selected-file">
              已选择: {{ selectedFirmware.name }}
            </div>
          </div>

          <div class="upgrade-options">
            <label class="checkbox-label">
              <input
                v-model="upgradeOptions.backupBeforeUpgrade"
                type="checkbox"
                class="form-checkbox"
              />
              <span>升级前自动备份</span>
            </label>
            <label class="checkbox-label">
              <input
                v-model="upgradeOptions.verifyChecksum"
                type="checkbox"
                class="form-checkbox"
              />
              <span>验证文件校验和</span>
            </label>
            <label class="checkbox-label">
              <input
                v-model="upgradeOptions.rebootAfterUpgrade"
                type="checkbox"
                class="form-checkbox"
              />
              <span>升级后自动重启</span>
            </label>
          </div>

          <button
            @click="startUpgrade"
            class="btn-upgrade"
            :disabled="!selectedFirmware || upgrading"
          >
            {{ upgrading ? '升级中...' : '开始升级' }}
          </button>
        </div>
      </div>

      <div v-if="upgrading" class="upgrade-progress">
        <div class="progress-header">
          <h5>升级进度</h5>
          <span class="progress-percentage">{{ upgradeProgress }}%</span>
        </div>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: upgradeProgress + '%' }"
          ></div>
        </div>
        <div class="progress-status">{{ upgradeStatus }}</div>
      </div>
    </div>

    <!-- 系统诊断 -->
    <div class="diagnostics-section">
      <div class="section-header">
        <h4>系统诊断</h4>
      </div>

      <div class="diagnostic-actions">
        <button @click="runDiagnostics" class="btn-diagnostics" :disabled="diagnosing">
          {{ diagnosing ? '诊断中...' : '运行系统诊断' }}
        </button>
        <button @click="generateReport" class="btn-report" :disabled="generating">
          {{ generating ? '生成中...' : '生成系统报告' }}
        </button>
        <button @click="exportLogs" class="btn-export">
          导出系统日志
        </button>
      </div>

      <div v-if="diagnosticResults" class="diagnostic-results">
        <h5>诊断结果</h5>
        <div class="results-summary">
          <div class="result-item" :class="getResultClass(diagnosticResults.overall)">
            <span class="result-label">总体状态:</span>
            <span class="result-value">{{ diagnosticResults.overall }}</span>
          </div>
        </div>
        <div class="results-details">
          <div
            v-for="(result, component) in diagnosticResults.components"
            :key="component"
            class="component-result"
            :class="getResultClass(result.status)"
          >
            <div class="component-name">{{ component }}</div>
            <div class="component-status">{{ result.status }}</div>
            <div v-if="result.message" class="component-message">{{ result.message }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <ConfirmDialog
      v-if="showRestartDialog"
      title="确认重启系统"
      message="系统将重启，所有连接将断开。确定要继续吗？"
      @confirm="confirmRestart"
      @cancel="showRestartDialog = false"
    />

    <ConfirmDialog
      v-if="showShutdownDialog"
      title="确认关闭系统"
      message="系统将关闭，需要手动重新启动。确定要继续吗？"
      @confirm="confirmShutdown"
      @cancel="showShutdownDialog = false"
    />

    <ConfirmDialog
      v-if="backupToRestore"
      title="确认恢复备份"
      :message="`确定要恢复备份 '${backupToRestore.name}' 吗？当前配置将被覆盖。`"
      @confirm="confirmRestore"
      @cancel="backupToRestore = null"
    />

    <ConfirmDialog
      v-if="backupToDelete"
      title="确认删除备份"
      :message="`确定要删除备份 '${backupToDelete.name}' 吗？此操作不可撤销。`"
      @confirm="confirmDelete"
      @cancel="backupToDelete = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { ConfigBackup } from '@/types/system'
import { systemApi, configApi, logApi } from '@/services/api'
import ConfirmDialog from '@/components/Common/ConfirmDialog.vue'

interface Emits {
  (e: 'restart'): void
  (e: 'backup'): void
  (e: 'restore'): void
}

const emit = defineEmits<Emits>()

// 状态管理
const showRestartDialog = ref(false)
const showShutdownDialog = ref(false)
const restarting = ref(false)
const creating = ref(false)
const upgrading = ref(false)
const diagnosing = ref(false)
const generating = ref(false)

// 备份相关
const backupName = ref('')
const backupDescription = ref('')
const backups = ref<ConfigBackup[]>([])
const backupToRestore = ref<ConfigBackup | null>(null)
const backupToDelete = ref<ConfigBackup | null>(null)

// 固件升级相关
const firmwareInput = ref<HTMLInputElement>()
const selectedFirmware = ref<File | null>(null)
const upgradeProgress = ref(0)
const upgradeStatus = ref('')

const systemVersion = reactive({
  version: '1.0.0',
  buildTime: '2024-01-15 10:30:00',
  gitCommit: 'a1b2c3d4',
  platform: 'Linux x86_64'
})

const upgradeOptions = reactive({
  backupBeforeUpgrade: true,
  verifyChecksum: true,
  rebootAfterUpgrade: true
})

// 诊断结果
const diagnosticResults = ref<any>(null)

// 方法
const restartService = async () => {
  restarting.value = true
  try {
    // 模拟服务重启
    await new Promise(resolve => setTimeout(resolve, 3000))
    console.log('Service restarted')
  } catch (error) {
    console.error('Failed to restart service:', error)
  } finally {
    restarting.value = false
  }
}

const confirmRestart = async () => {
  showRestartDialog.value = false
  try {
    await systemApi.restartSystem()
    emit('restart')
  } catch (error) {
    console.error('Failed to restart system:', error)
  }
}

const confirmShutdown = async () => {
  showShutdownDialog.value = false
  try {
    await systemApi.shutdownSystem()
  } catch (error) {
    console.error('Failed to shutdown system:', error)
  }
}

const createBackup = async () => {
  if (!backupName.value.trim()) {
    alert('请输入备份名称')
    return
  }

  creating.value = true
  try {
    const response = await configApi.createConfigBackup(
      backupName.value,
      backupDescription.value
    )
    backups.value.unshift(response.data.data)
    backupName.value = ''
    backupDescription.value = ''
    emit('backup')
  } catch (error) {
    console.error('Failed to create backup:', error)
  } finally {
    creating.value = false
  }
}

const restoreBackup = (backup: ConfigBackup) => {
  backupToRestore.value = backup
}

const confirmRestore = async () => {
  if (!backupToRestore.value) return

  try {
    await configApi.restoreConfigBackup(backupToRestore.value.id)
    backupToRestore.value = null
    emit('restore')
  } catch (error) {
    console.error('Failed to restore backup:', error)
  }
}

const downloadBackup = async (backup: ConfigBackup) => {
  try {
    const response = await configApi.downloadConfigBackup(backup.id)
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `${backup.name}.tar.gz`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to download backup:', error)
  }
}

const deleteBackup = (backup: ConfigBackup) => {
  backupToDelete.value = backup
}

const confirmDelete = async () => {
  if (!backupToDelete.value) return

  try {
    await configApi.deleteConfigBackup(backupToDelete.value.id)
    backups.value = backups.value.filter(b => b.id !== backupToDelete.value!.id)
    backupToDelete.value = null
  } catch (error) {
    console.error('Failed to delete backup:', error)
  }
}

const selectFirmware = () => {
  firmwareInput.value?.click()
}

const handleFirmwareSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    selectedFirmware.value = file
  }
}

const startUpgrade = async () => {
  if (!selectedFirmware.value) return

  upgrading.value = true
  upgradeProgress.value = 0
  upgradeStatus.value = '准备升级...'

  try {
    // 模拟升级过程
    const steps = [
      '验证固件文件...',
      '创建备份...',
      '上传固件...',
      '安装固件...',
      '验证安装...',
      '重启系统...'
    ]

    for (let i = 0; i < steps.length; i++) {
      upgradeStatus.value = steps[i]
      upgradeProgress.value = ((i + 1) / steps.length) * 100
      await new Promise(resolve => setTimeout(resolve, 2000))
    }

    upgradeStatus.value = '升级完成'
  } catch (error) {
    console.error('Firmware upgrade failed:', error)
    upgradeStatus.value = '升级失败'
  } finally {
    upgrading.value = false
  }
}

const runDiagnostics = async () => {
  diagnosing.value = true
  try {
    // 模拟系统诊断
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    diagnosticResults.value = {
      overall: 'healthy',
      components: {
        'GNSS接收器': { status: 'healthy', message: '信号正常' },
        'PTP服务': { status: 'healthy', message: '同步正常' },
        'NTP服务': { status: 'warning', message: '客户端连接较多' },
        '原子钟': { status: 'healthy', message: '温度正常' },
        '网络接口': { status: 'healthy', message: '连接正常' },
        '存储系统': { status: 'warning', message: '使用率较高' }
      }
    }
  } catch (error) {
    console.error('Diagnostics failed:', error)
  } finally {
    diagnosing.value = false
  }
}

const generateReport = async () => {
  generating.value = true
  try {
    // 模拟报告生成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 创建并下载报告
    const reportContent = `系统报告\n生成时间: ${new Date().toLocaleString()}\n\n...`
    const blob = new Blob([reportContent], { type: 'text/plain' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `system-report-${new Date().toISOString().split('T')[0]}.txt`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to generate report:', error)
  } finally {
    generating.value = false
  }
}

const exportLogs = async () => {
  try {
    const response = await logApi.downloadLogs({
      startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      endTime: new Date().toISOString()
    })
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = `system-logs-${new Date().toISOString().split('T')[0]}.log`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export logs:', error)
  }
}

// 工具函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatSize = (bytes: number) => {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}

const getResultClass = (status: string) => {
  const classes = {
    healthy: 'result-healthy',
    warning: 'result-warning',
    error: 'result-error',
    critical: 'result-critical'
  }
  return classes[status as keyof typeof classes] || 'result-unknown'
}

// 加载备份列表
const loadBackups = async () => {
  try {
    const response = await configApi.getConfigBackups()
    backups.value = response.data.data
  } catch (error) {
    console.error('Failed to load backups:', error)
    // 使用模拟数据
    backups.value = [
      {
        id: '1',
        name: '系统升级前备份',
        description: '升级到v1.0.0前的配置备份',
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        version: '0.9.5',
        size: 1024 * 1024 * 2.5,
        checksum: 'abc123',
        configs: {} as any,
        metadata: {} as any
      },
      {
        id: '2',
        name: '每日自动备份',
        description: '系统自动创建的每日备份',
        timestamp: new Date(Date.now() - 172800000).toISOString(),
        version: '1.0.0',
        size: 1024 * 1024 * 2.3,
        checksum: 'def456',
        configs: {} as any,
        metadata: {} as any
      }
    ]
  }
}

onMounted(() => {
  loadBackups()
})
</script>

<style lang="scss" scoped>
.maintenance-panel {
  .config-header {
    margin-bottom: var(--spacing-xl);
    
    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }
    
    p {
      color: var(--text-secondary);
    }
  }
  
  .section-header {
    margin-bottom: var(--spacing-lg);
    
    h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }
  }
}

.system-control-section, .backup-section, .firmware-section, .diagnostics-section {
  margin-bottom: var(--spacing-2xl);
}

.control-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  
  .action-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    
    .action-icon {
      font-size: 2rem;
      flex-shrink: 0;
    }
    
    .action-info {
      flex: 1;
      
      h5 {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }
      
      p {
        color: var(--text-secondary);
        font-size: 0.9rem;
      }
    }
    
    .btn-action {
      padding: var(--spacing-md) var(--spacing-lg);
      border: none;
      border-radius: var(--radius-md);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &.btn-warning {
        background: var(--status-warning);
        color: white;
        
        &:hover {
          background: #d97706;
        }
      }
      
      &.btn-danger {
        background: var(--status-error);
        color: white;
        
        &:hover {
          background: #dc2626;
        }
      }
      
      &.btn-info {
        background: var(--status-info);
        color: white;
        
        &:hover:not(:disabled) {
          background: #2563eb;
        }
      }
      
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

.backup-actions {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-xl);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.backup-create {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  
  .form-group {
    margin-bottom: var(--spacing-lg);
    
    label {
      display: block;
      margin-bottom: var(--spacing-sm);
      color: var(--text-primary);
      font-weight: 500;
    }
    
    .form-input, .form-textarea {
      width: 100%;
      padding: var(--spacing-md);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-md);
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      resize: vertical;
      
      &::placeholder {
        color: var(--text-secondary);
      }
      
      &:focus {
        outline: none;
        border-color: var(--status-info);
        background: rgba(255, 255, 255, 0.15);
      }
    }
  }
  
  .btn-create-backup {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--status-success);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #16a34a;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.backup-list {
  h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
  }
  
  .backup-items {
    .backup-item {
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-md);
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .backup-info {
        flex: 1;
        
        .backup-name {
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: var(--spacing-sm);
        }
        
        .backup-details {
          display: flex;
          gap: var(--spacing-md);
          margin-bottom: var(--spacing-sm);
          
          .backup-date, .backup-size {
            font-size: 0.9rem;
            color: var(--text-secondary);
          }
        }
        
        .backup-description {
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
      }
      
      .backup-actions {
        display: flex;
        gap: var(--spacing-sm);
        
        button {
          padding: var(--spacing-sm) var(--spacing-md);
          border: none;
          border-radius: var(--radius-sm);
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 0.9rem;
        }
        
        .btn-restore {
          background: var(--status-success);
          color: white;
          
          &:hover {
            background: #16a34a;
          }
        }
        
        .btn-download {
          background: var(--status-info);
          color: white;
          
          &:hover {
            background: #2563eb;
          }
        }
        
        .btn-delete {
          background: var(--status-error);
          color: white;
          
          &:hover {
            background: #dc2626;
          }
        }
      }
    }
  }
}

.firmware-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.current-version {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  
  h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
  }
  
  .version-details {
    .version-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-md);
      
      .version-label {
        color: var(--text-secondary);
      }
      
      .version-value {
        color: var(--text-primary);
        font-weight: 500;
      }
    }
  }
}

.upgrade-actions {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  
  .upload-firmware {
    margin-bottom: var(--spacing-lg);
    
    .btn-select-firmware {
      padding: var(--spacing-md) var(--spacing-lg);
      background: var(--status-info);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: #2563eb;
      }
    }
    
    .selected-file {
      margin-top: var(--spacing-sm);
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
  }
  
  .upgrade-options {
    margin-bottom: var(--spacing-lg);
    
    .checkbox-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-md);
      cursor: pointer;
      
      .form-checkbox {
        width: auto;
      }
      
      span {
        color: var(--text-primary);
      }
    }
  }
  
  .btn-upgrade {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--status-warning);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(:disabled) {
      background: #d97706;
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

.upgrade-progress {
  background: rgba(245, 158, 11, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    
    h5 {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .progress-percentage {
      color: var(--status-warning);
      font-weight: 600;
    }
  }
  
  .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-md);
    
    .progress-fill {
      height: 100%;
      background: var(--status-warning);
      transition: width 0.3s ease;
    }
  }
  
  .progress-status {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

.diagnostic-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  
  button {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
  
  .btn-diagnostics {
    background: var(--status-info);
    color: white;
    
    &:hover:not(:disabled) {
      background: #2563eb;
    }
  }
  
  .btn-report {
    background: var(--status-success);
    color: white;
    
    &:hover:not(:disabled) {
      background: #16a34a;
    }
  }
  
  .btn-export {
    background: var(--status-warning);
    color: white;
    
    &:hover {
      background: #d97706;
    }
  }
}

.diagnostic-results {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  
  h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
  }
  
  .results-summary {
    margin-bottom: var(--spacing-lg);
    
    .result-item {
      display: flex;
      justify-content: space-between;
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      
      &.result-healthy {
        background: rgba(34, 197, 94, 0.2);
        
        .result-value {
          color: var(--status-success);
        }
      }
      
      &.result-warning {
        background: rgba(245, 158, 11, 0.2);
        
        .result-value {
          color: var(--status-warning);
        }
      }
      
      &.result-error {
        background: rgba(239, 68, 68, 0.2);
        
        .result-value {
          color: var(--status-error);
        }
      }
    }
  }
  
  .results-details {
    .component-result {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-md);
      border-radius: var(--radius-md);
      margin-bottom: var(--spacing-sm);
      
      &.result-healthy {
        background: rgba(34, 197, 94, 0.1);
      }
      
      &.result-warning {
        background: rgba(245, 158, 11, 0.1);
      }
      
      &.result-error {
        background: rgba(239, 68, 68, 0.1);
      }
      
      .component-name {
        font-weight: 500;
        color: var(--text-primary);
      }
      
      .component-status {
        font-weight: 500;
      }
      
      .component-message {
        font-size: 0.9rem;
        color: var(--text-secondary);
      }
    }
  }
}
</style>