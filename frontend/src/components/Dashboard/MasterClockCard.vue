<template>
  <div class="status-card glass-card">
    <div class="card-header">
      <div class="card-title-section">
        <h3 class="card-title">主控字钟</h3>
        <div class="card-subtitle">Rubidium Atomic Clock</div>
      </div>
      <div class="card-icon">⚛️</div>
    </div>
    
    <div class="card-content">
      <div class="metrics-grid">
        <div class="metric-item">
          <div class="metric-label">频率</div>
          <div class="metric-value">{{ clockData.frequency }}</div>
          <div class="metric-unit">GHz</div>
        </div>
        
        <div class="metric-item">
          <div class="metric-label">温度</div>
          <div class="metric-value" :class="getTemperatureStatus(clockData.temperature)">
            {{ clockData.temperature }}
          </div>
          <div class="metric-unit">°C</div>
        </div>
        
        <div class="metric-item">
          <div class="metric-label">运行时间</div>
          <div class="metric-value">{{ clockData.uptime }}</div>
          <div class="metric-unit">天</div>
        </div>
        
        <div class="metric-item">
          <div class="metric-label">稳定度</div>
          <div class="metric-value">{{ clockData.stability }}</div>
          <div class="metric-unit">ppm</div>
        </div>
      </div>
      
      <div class="frequency-chart">
        <div class="chart-header">
          <span class="chart-title">频率漂移趋势</span>
          <span class="chart-period">最近24小时</span>
        </div>
        <div class="mini-chart">
          <svg viewBox="0 0 200 60" class="chart-svg">
            <path 
              :d="frequencyTrendPath" 
              fill="none" 
              stroke="var(--status-info)" 
              stroke-width="2"
              opacity="0.8"
            />
            <circle 
              v-for="(point, index) in frequencyTrendPoints" 
              :key="index"
              :cx="point.x" 
              :cy="point.y" 
              r="2" 
              fill="var(--status-info)"
              opacity="0.6"
            />
          </svg>
        </div>
      </div>
      
      <div class="status-bar">
        <div class="status-indicator" :class="clockData.status">
          <div class="status-dot pulse"></div>
          <span class="status-text">{{ getStatusText(clockData.status) }}</span>
        </div>
        
        <div class="learning-status">
          <div class="learning-icon">🧠</div>
          <span class="learning-text">{{ clockData.learningStatus }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()

// 从store获取数据
const clockData = computed(() => {
  const clockInfo = systemStore.systemStatus?.clock
  if (!clockInfo) {
    return {
      frequency: '9.192631770',
      temperature: 45.3,
      uptime: '247.5',
      stability: '1.2×10⁻¹²',
      status: 'normal',
      learningStatus: '学习中 (72%)'
    }
  }
  
  return {
    frequency: clockInfo.frequency?.replace(' GHz', '') || '9.192631770',
    temperature: clockInfo.temperature || 45.3,
    uptime: clockInfo.uptime?.replace('天', '.').replace('小时', '') || '247.5',
    stability: clockInfo.stability || '1.2×10⁻¹²',
    status: clockInfo.status || 'normal',
    learningStatus: '学习中 (72%)'
  }
})

// 模拟频率漂移数据
const frequencyTrendData = ref([
  0.1, 0.15, 0.12, 0.08, 0.11, 0.09, 0.13, 0.10, 0.14, 0.11,
  0.08, 0.12, 0.15, 0.13, 0.09, 0.11, 0.14, 0.10, 0.12, 0.08
])

// 计算属性
const frequencyTrendPoints = computed(() => {
  const width = 200
  const height = 60
  const padding = 10
  
  return frequencyTrendData.value.map((value, index) => {
    const x = padding + (index / (frequencyTrendData.value.length - 1)) * (width - 2 * padding)
    const y = height - padding - ((value - Math.min(...frequencyTrendData.value)) / 
      (Math.max(...frequencyTrendData.value) - Math.min(...frequencyTrendData.value))) * (height - 2 * padding)
    
    return { x, y }
  })
})

const frequencyTrendPath = computed(() => {
  if (frequencyTrendPoints.value.length === 0) return ''
  
  let path = `M ${frequencyTrendPoints.value[0].x} ${frequencyTrendPoints.value[0].y}`
  
  for (let i = 1; i < frequencyTrendPoints.value.length; i++) {
    const point = frequencyTrendPoints.value[i]
    path += ` L ${point.x} ${point.y}`
  }
  
  return path
})

// 方法
const getTemperatureStatus = (temp: number) => {
  if (temp < 40) return 'cold'
  if (temp > 50) return 'hot'
  return 'normal'
}

const getStatusText = (status: string) => {
  const statusMap = {
    normal: '正常运行',
    warning: '需要注意',
    error: '故障状态',
    maintenance: '维护模式'
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}
</script>

<style lang="scss" scoped>
.status-card {
  padding: var(--spacing-lg);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    
    .card-title-section {
      .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
      }
      
      .card-subtitle {
        font-size: 0.8rem;
        color: var(--text-secondary);
        opacity: 0.8;
      }
    }
    
    .card-icon {
      font-size: 2rem;
      opacity: 0.7;
    }
  }
  
  .card-content {
    .metrics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
      
      .metric-item {
        text-align: center;
        
        .metric-label {
          font-size: 0.8rem;
          color: var(--text-secondary);
          margin-bottom: var(--spacing-sm);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        
        .metric-value {
          font-size: 1.4rem;
          font-weight: 600;
          color: var(--text-primary);
          font-family: 'Monaco', 'Menlo', monospace;
          margin-bottom: var(--spacing-xs);
          
          &.cold { color: var(--status-info); }
          &.normal { color: var(--status-success); }
          &.hot { color: var(--status-warning); }
        }
        
        .metric-unit {
          font-size: 0.7rem;
          color: var(--text-secondary);
          opacity: 0.8;
        }
      }
    }
    
    .frequency-chart {
      margin-bottom: var(--spacing-lg);
      padding: var(--spacing-md);
      background: rgba(255, 255, 255, 0.03);
      border-radius: var(--radius-md);
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        
        .chart-title {
          font-size: 0.9rem;
          color: var(--text-primary);
          font-weight: 500;
        }
        
        .chart-period {
          font-size: 0.8rem;
          color: var(--text-secondary);
        }
      }
      
      .mini-chart {
        height: 60px;
        
        .chart-svg {
          width: 100%;
          height: 100%;
        }
      }
    }
    
    .status-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: var(--spacing-md);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      
      .status-indicator {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          
          &.normal { background: var(--status-success); }
          &.warning { background: var(--status-warning); }
          &.error { background: var(--status-error); }
        }
        
        .status-text {
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
      }
      
      .learning-status {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .learning-icon {
          font-size: 1rem;
        }
        
        .learning-text {
          font-size: 0.8rem;
          color: var(--text-secondary);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .status-card {
    .card-content {
      .metrics-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
      
      .status-bar {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
      }
    }
  }
}
</style>