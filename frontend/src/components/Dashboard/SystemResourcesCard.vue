<template>
  <div class="status-card glass-card">
    <div class="card-header">
      <div class="card-title-section">
        <h3 class="card-title">系统资源</h3>
        <div class="card-subtitle">System Resources Monitor</div>
      </div>
      <div class="card-icon">💻</div>
    </div>
    
    <div class="card-content">
      <!-- CPU使用率 -->
      <div class="resource-item">
        <div class="resource-header">
          <div class="resource-info">
            <div class="resource-icon">🔥</div>
            <div class="resource-details">
              <div class="resource-label">CPU使用率</div>
              <div class="resource-spec">{{ systemData.cpu.cores }}核心 @ {{ systemData.cpu.frequency }}</div>
            </div>
          </div>
          <div class="resource-value" :class="getCpuStatus(systemData.cpu.usage)">
            {{ systemData.cpu.usage }}%
          </div>
        </div>
        
        <div class="progress-container">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: systemData.cpu.usage + '%' }"
              :class="getCpuStatus(systemData.cpu.usage)"
            ></div>
          </div>
          <div class="resource-extra">
            <span class="temp-info">温度: {{ systemData.cpu.temperature }}°C</span>
          </div>
        </div>
      </div>
      
      <!-- 内存使用率 -->
      <div class="resource-item">
        <div class="resource-header">
          <div class="resource-info">
            <div class="resource-icon">🧠</div>
            <div class="resource-details">
              <div class="resource-label">内存使用率</div>
              <div class="resource-spec">{{ formatBytes(systemData.memory.used) }} / {{ formatBytes(systemData.memory.total) }}</div>
            </div>
          </div>
          <div class="resource-value" :class="getMemoryStatus(systemData.memory.usage)">
            {{ systemData.memory.usage }}%
          </div>
        </div>
        
        <div class="progress-container">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: systemData.memory.usage + '%' }"
              :class="getMemoryStatus(systemData.memory.usage)"
            ></div>
          </div>
          <div class="resource-extra">
            <span class="available-info">可用: {{ formatBytes(systemData.memory.available) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 网络流量 -->
      <div class="resource-item">
        <div class="resource-header">
          <div class="resource-info">
            <div class="resource-icon">🌐</div>
            <div class="resource-details">
              <div class="resource-label">网络流量</div>
              <div class="resource-spec">{{ systemData.network.interface }}</div>
            </div>
          </div>
          <div class="resource-value">
            {{ systemData.network.throughput }}
          </div>
        </div>
        
        <div class="network-details">
          <div class="network-metric">
            <span class="metric-label">上行:</span>
            <span class="metric-value">{{ systemData.network.upload }}</span>
          </div>
          <div class="network-metric">
            <span class="metric-label">下行:</span>
            <span class="metric-value">{{ systemData.network.download }}</span>
          </div>
          <div class="network-metric">
            <span class="metric-label">连接数:</span>
            <span class="metric-value">{{ systemData.network.connections }}</span>
          </div>
        </div>
      </div>
      
      <!-- 存储使用率 -->
      <div class="resource-item">
        <div class="resource-header">
          <div class="resource-info">
            <div class="resource-icon">💾</div>
            <div class="resource-details">
              <div class="resource-label">存储使用率</div>
              <div class="resource-spec">{{ formatBytes(systemData.storage.used) }} / {{ formatBytes(systemData.storage.total) }}</div>
            </div>
          </div>
          <div class="resource-value" :class="getStorageStatus(systemData.storage.usage)">
            {{ systemData.storage.usage }}%
          </div>
        </div>
        
        <div class="progress-container">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: systemData.storage.usage + '%' }"
              :class="getStorageStatus(systemData.storage.usage)"
            ></div>
          </div>
          <div class="resource-extra">
            <span class="available-info">可用: {{ formatBytes(systemData.storage.available) }}</span>
          </div>
        </div>
      </div>
      
      <!-- 系统负载 -->
      <div class="load-metrics">
        <div class="load-header">
          <span class="load-title">系统负载</span>
          <span class="load-period">1分钟 / 5分钟 / 15分钟</span>
        </div>
        
        <div class="load-values">
          <div class="load-item">
            <div class="load-value" :class="getLoadStatus(systemData.load.load1)">
              {{ systemData.load.load1 }}
            </div>
            <div class="load-label">1分钟</div>
          </div>
          
          <div class="load-item">
            <div class="load-value" :class="getLoadStatus(systemData.load.load5)">
              {{ systemData.load.load5 }}
            </div>
            <div class="load-label">5分钟</div>
          </div>
          
          <div class="load-item">
            <div class="load-value" :class="getLoadStatus(systemData.load.load15)">
              {{ systemData.load.load15 }}
            </div>
            <div class="load-label">15分钟</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()

// 从store获取数据
const systemData = computed(() => {
  const resources = systemStore.systemStatus?.resources
  if (!resources) {
    return {
      cpu: {
        usage: 15,
        cores: 8,
        frequency: '2.4GHz',
        temperature: 42
      },
      memory: {
        usage: 68,
        total: 16 * 1024 * 1024 * 1024,
        used: 10.9 * 1024 * 1024 * 1024,
        available: 5.1 * 1024 * 1024 * 1024
      },
      network: {
        interface: 'eth0',
        throughput: '1.1 MB/s',
        upload: '0.3 MB/s',
        download: '0.8 MB/s',
        connections: 47
      },
      storage: {
        usage: 35,
        total: 500 * 1024 * 1024 * 1024,
        used: 175 * 1024 * 1024 * 1024,
        available: 325 * 1024 * 1024 * 1024
      },
      load: {
        load1: 0.15,
        load5: 0.22,
        load15: 0.18
      }
    }
  }
  
  return {
    cpu: {
      usage: Math.round(resources.cpu?.usage || 15),
      cores: 8,
      frequency: '2.4GHz',
      temperature: Math.round(resources.cpu?.temperature || 42)
    },
    memory: {
      usage: Math.round(resources.memory?.usage || 68),
      total: resources.memory?.total || 16 * 1024 * 1024 * 1024,
      used: (resources.memory?.total || 16 * 1024 * 1024 * 1024) * (resources.memory?.usage || 68) / 100,
      available: resources.memory?.available || 5.1 * 1024 * 1024 * 1024
    },
    network: {
      interface: 'eth0',
      throughput: resources.network?.throughput || '1.1 MB/s',
      upload: '0.3 MB/s',
      download: '0.8 MB/s',
      connections: resources.network?.connections || 47
    },
    storage: {
      usage: Math.round(resources.storage?.usage || 35),
      total: resources.storage?.total || 500 * 1024 * 1024 * 1024,
      used: (resources.storage?.total || 500 * 1024 * 1024 * 1024) * (resources.storage?.usage || 35) / 100,
      available: resources.storage?.available || 325 * 1024 * 1024 * 1024
    },
    load: {
      load1: 0.15,
      load5: 0.22,
      load15: 0.18
    }
  }
})

// 方法
const formatBytes = (bytes: number) => {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 B'
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

const getCpuStatus = (usage: number) => {
  if (usage >= 80) return 'critical'
  if (usage >= 60) return 'warning'
  if (usage >= 40) return 'normal'
  return 'low'
}

const getMemoryStatus = (usage: number) => {
  if (usage >= 90) return 'critical'
  if (usage >= 75) return 'warning'
  if (usage >= 50) return 'normal'
  return 'low'
}

const getStorageStatus = (usage: number) => {
  if (usage >= 90) return 'critical'
  if (usage >= 80) return 'warning'
  if (usage >= 60) return 'normal'
  return 'low'
}

const getLoadStatus = (load: number) => {
  if (load >= 2.0) return 'critical'
  if (load >= 1.0) return 'warning'
  if (load >= 0.5) return 'normal'
  return 'low'
}
</script>

<style lang="scss" scoped>
.status-card {
  padding: var(--spacing-lg);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    
    .card-title-section {
      .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
      }
      
      .card-subtitle {
        font-size: 0.8rem;
        color: var(--text-secondary);
        opacity: 0.8;
      }
    }
    
    .card-icon {
      font-size: 2rem;
      opacity: 0.7;
    }
  }
  
  .card-content {
    .resource-item {
      margin-bottom: var(--spacing-lg);
      
      .resource-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);
        
        .resource-info {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          
          .resource-icon {
            font-size: 1.2rem;
          }
          
          .resource-details {
            .resource-label {
              font-size: 0.9rem;
              color: var(--text-primary);
              font-weight: 500;
            }
            
            .resource-spec {
              font-size: 0.8rem;
              color: var(--text-secondary);
              margin-top: var(--spacing-xs);
            }
          }
        }
        
        .resource-value {
          font-size: 1.2rem;
          font-weight: 600;
          font-family: 'Monaco', 'Menlo', monospace;
          
          &.low { color: var(--status-success); }
          &.normal { color: var(--status-info); }
          &.warning { color: var(--status-warning); }
          &.critical { color: var(--status-error); }
        }
      }
      
      .progress-container {
        .progress-bar {
          height: 8px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: var(--spacing-sm);
          
          .progress-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
            
            &.low { background: var(--status-success); }
            &.normal { background: var(--status-info); }
            &.warning { background: var(--status-warning); }
            &.critical { background: var(--status-error); }
          }
        }
        
        .resource-extra {
          display: flex;
          justify-content: space-between;
          font-size: 0.8rem;
          color: var(--text-secondary);
        }
      }
      
      .network-details {
        display: flex;
        justify-content: space-between;
        padding: var(--spacing-sm);
        background: rgba(255, 255, 255, 0.03);
        border-radius: var(--radius-sm);
        
        .network-metric {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--spacing-xs);
          
          .metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
          }
          
          .metric-value {
            font-size: 0.9rem;
            color: var(--text-primary);
            font-weight: 500;
            font-family: 'Monaco', 'Menlo', monospace;
          }
        }
      }
    }
    
    .load-metrics {
      padding: var(--spacing-md);
      background: rgba(255, 255, 255, 0.03);
      border-radius: var(--radius-md);
      
      .load-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        
        .load-title {
          font-size: 0.9rem;
          color: var(--text-primary);
          font-weight: 500;
        }
        
        .load-period {
          font-size: 0.8rem;
          color: var(--text-secondary);
        }
      }
      
      .load-values {
        display: flex;
        justify-content: space-around;
        
        .load-item {
          text-align: center;
          
          .load-value {
            font-size: 1.4rem;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
            margin-bottom: var(--spacing-xs);
            
            &.low { color: var(--status-success); }
            &.normal { color: var(--status-info); }
            &.warning { color: var(--status-warning); }
            &.critical { color: var(--status-error); }
          }
          
          .load-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .status-card {
    .card-content {
      .resource-item {
        .resource-header {
          flex-direction: column;
          align-items: flex-start;
          gap: var(--spacing-sm);
        }
        
        .network-details {
          flex-direction: column;
          gap: var(--spacing-sm);
          
          .network-metric {
            flex-direction: row;
            justify-content: space-between;
          }
        }
      }
      
      .load-metrics {
        .load-header {
          flex-direction: column;
          gap: var(--spacing-sm);
          align-items: flex-start;
        }
      }
    }
  }
}
</style>