<template>
  <div class="status-card glass-card">
    <div class="card-header">
      <div class="card-title-section">
        <h3 class="card-title">GNSS接收器</h3>
        <div class="card-subtitle">Global Navigation Satellite System</div>
      </div>
      <div class="card-icon">🛰️</div>
    </div>
    
    <div class="card-content">
      <div class="satellite-overview">
        <div class="satellite-count">
          <div class="count-display">
            <span class="visible-count">{{ gnssData.visibleSats }}</span>
            <span class="count-separator">/</span>
            <span class="tracking-count">{{ gnssData.trackingSats }}</span>
          </div>
          <div class="count-label">可见/跟踪卫星</div>
        </div>
        
        <div class="signal-quality">
          <div class="quality-meter">
            <div class="quality-bar">
              <div 
                class="quality-fill" 
                :style="{ width: gnssData.signalQuality + '%' }"
                :class="getQualityClass(gnssData.signalQuality)"
              ></div>
            </div>
            <div class="quality-value">{{ gnssData.signalQuality }}%</div>
          </div>
          <div class="quality-label">信号质量</div>
        </div>
      </div>
      
      <div class="constellation-grid">
        <div 
          v-for="constellation in constellations" 
          :key="constellation.name"
          class="constellation-item"
        >
          <div class="constellation-icon">{{ constellation.icon }}</div>
          <div class="constellation-info">
            <div class="constellation-name">{{ constellation.name }}</div>
            <div class="constellation-count">{{ constellation.count }}颗</div>
          </div>
          <div class="constellation-status" :class="constellation.status">
            <div class="status-dot"></div>
          </div>
        </div>
      </div>
      
      <div class="accuracy-metrics">
        <div class="metric-row">
          <span class="metric-label">定位精度</span>
          <span class="metric-value">{{ gnssData.accuracy }}</span>
        </div>
        <div class="metric-row">
          <span class="metric-label">时间精度</span>
          <span class="metric-value">{{ gnssData.timeAccuracy }}</span>
        </div>
        <div class="metric-row">
          <span class="metric-label">信号强度</span>
          <span class="metric-value">{{ gnssData.signalStrength }}</span>
        </div>
        <div class="metric-row">
          <span class="metric-label">最后更新</span>
          <span class="metric-value">{{ gnssData.lastUpdate }}</span>
        </div>
      </div>
      
      <div class="status-bar">
        <div class="status-indicator" :class="gnssData.status">
          <div class="status-dot pulse"></div>
          <span class="status-text">{{ getStatusText(gnssData.status) }}</span>
        </div>
        
        <div class="fix-type">
          <div class="fix-icon">📍</div>
          <span class="fix-text">{{ gnssData.fixType }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()

// 从store获取数据
const gnssData = computed(() => {
  const gnssInfo = systemStore.systemStatus?.gnss
  if (!gnssInfo) {
    return {
      visibleSats: 24,
      trackingSats: 17,
      signalQuality: 85,
      accuracy: '2.4m',
      timeAccuracy: '±12ns',
      signalStrength: '-142dBm',
      lastUpdate: '2秒前',
      status: 'normal',
      fixType: '3D Fix'
    }
  }
  
  return {
    visibleSats: gnssInfo.visibleSatellites || 24,
    trackingSats: gnssInfo.trackingSatellites || 17,
    signalQuality: parseInt(gnssInfo.signalQuality?.replace('%', '') || '85'),
    accuracy: gnssInfo.accuracy || '2.4m',
    timeAccuracy: '±12ns',
    signalStrength: '-142dBm',
    lastUpdate: gnssInfo.lastUpdate ? '刚刚' : '2秒前',
    status: gnssInfo.status || 'normal',
    fixType: '3D Fix'
  }
})

const constellations = ref([
  { name: 'GPS', icon: '🇺🇸', count: 8, status: 'active' },
  { name: 'GLONASS', icon: '🇷🇺', count: 4, status: 'active' },
  { name: 'Galileo', icon: '🇪🇺', count: 3, status: 'active' },
  { name: 'BeiDou', icon: '🇨🇳', count: 2, status: 'active' }
])

// 计算属性
const getQualityClass = (quality: number) => {
  if (quality >= 80) return 'excellent'
  if (quality >= 60) return 'good'
  if (quality >= 40) return 'fair'
  return 'poor'
}

const getStatusText = (status: string) => {
  const statusMap = {
    normal: '信号正常',
    warning: '信号弱',
    error: '信号丢失',
    acquiring: '搜星中'
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}
</script>

<style lang="scss" scoped>
.status-card {
  padding: var(--spacing-lg);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    
    .card-title-section {
      .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
      }
      
      .card-subtitle {
        font-size: 0.8rem;
        color: var(--text-secondary);
        opacity: 0.8;
      }
    }
    
    .card-icon {
      font-size: 2rem;
      opacity: 0.7;
    }
  }
  
  .card-content {
    .satellite-overview {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-xl);
      
      .satellite-count {
        text-align: center;
        
        .count-display {
          font-size: 2rem;
          font-weight: 600;
          font-family: 'Monaco', 'Menlo', monospace;
          margin-bottom: var(--spacing-sm);
          
          .visible-count {
            color: var(--status-success);
          }
          
          .count-separator {
            color: var(--text-secondary);
            margin: 0 var(--spacing-xs);
          }
          
          .tracking-count {
            color: var(--status-info);
          }
        }
        
        .count-label {
          font-size: 0.8rem;
          color: var(--text-secondary);
        }
      }
      
      .signal-quality {
        text-align: center;
        
        .quality-meter {
          margin-bottom: var(--spacing-sm);
          
          .quality-bar {
            width: 80px;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--spacing-xs);
            
            .quality-fill {
              height: 100%;
              border-radius: 4px;
              transition: width 0.3s ease;
              
              &.excellent { background: var(--status-success); }
              &.good { background: var(--status-info); }
              &.fair { background: var(--status-warning); }
              &.poor { background: var(--status-error); }
            }
          }
          
          .quality-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            font-family: 'Monaco', 'Menlo', monospace;
          }
        }
        
        .quality-label {
          font-size: 0.8rem;
          color: var(--text-secondary);
        }
      }
    }
    
    .constellation-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      
      .constellation-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
        background: rgba(255, 255, 255, 0.03);
        border-radius: var(--radius-md);
        
        .constellation-icon {
          font-size: 1.2rem;
        }
        
        .constellation-info {
          flex: 1;
          
          .constellation-name {
            font-size: 0.9rem;
            color: var(--text-primary);
            font-weight: 500;
          }
          
          .constellation-count {
            font-size: 0.8rem;
            color: var(--text-secondary);
          }
        }
        
        .constellation-status {
          .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            
            &.active { background: var(--status-success); }
            &.inactive { background: var(--text-secondary); }
          }
        }
      }
    }
    
    .accuracy-metrics {
      margin-bottom: var(--spacing-lg);
      
      .metric-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);
        
        .metric-label {
          color: var(--text-secondary);
          font-size: 0.9rem;
        }
        
        .metric-value {
          color: var(--text-primary);
          font-weight: 500;
          font-family: 'Monaco', 'Menlo', monospace;
        }
      }
    }
    
    .status-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: var(--spacing-md);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      
      .status-indicator {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          
          &.normal { background: var(--status-success); }
          &.warning { background: var(--status-warning); }
          &.error { background: var(--status-error); }
          &.acquiring { background: var(--status-info); }
        }
        
        .status-text {
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
      }
      
      .fix-type {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        
        .fix-icon {
          font-size: 1rem;
        }
        
        .fix-text {
          font-size: 0.8rem;
          color: var(--text-secondary);
          font-weight: 500;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .status-card {
    .card-content {
      .satellite-overview {
        flex-direction: column;
        gap: var(--spacing-lg);
        text-align: center;
      }
      
      .constellation-grid {
        grid-template-columns: 1fr;
      }
      
      .status-bar {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
      }
    }
  }
}
</style>