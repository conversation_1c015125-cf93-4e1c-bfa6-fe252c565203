<template>
  <div class="central-clock glass-card">
    <div class="clock-header">
      <div class="date-info">
        <span class="clock-date">{{ currentDate }}</span>
        <span class="clock-timezone">{{ timezone }}</span>
      </div>
      <div class="sync-indicator" :class="syncStatus">
        <div class="sync-dot pulse"></div>
        <span class="sync-text">{{ syncStatusText }}</span>
      </div>
    </div>
    
    <div class="main-clock">
      <div class="time-display">{{ formattedTime }}</div>
      <div class="milliseconds">.{{ milliseconds }}</div>
    </div>
    
    <div class="clock-footer">
      <div class="time-source-info">
        <div class="source-label">时间源</div>
        <div class="source-value" :class="timeSourceStatus">
          <div class="source-icon">{{ getTimeSourceIcon(currentTimeSource) }}</div>
          <span>{{ currentTimeSource }}</span>
        </div>
      </div>
      
      <div class="accuracy-info">
        <div class="accuracy-label">同步精度</div>
        <div class="accuracy-value">{{ syncAccuracy }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { format } from 'date-fns'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()

// 响应式数据
const currentTime = ref(new Date())
const timezone = ref('北京时间 (UTC+8)')

// 计算属性
const formattedTime = computed(() => {
  return format(currentTime.value, 'HH:mm:ss')
})

const milliseconds = computed(() => {
  return String(currentTime.value.getMilliseconds()).padStart(3, '0')
})

const currentDate = computed(() => {
  return format(currentTime.value, 'yyyy年MM月dd日 EEEE')
})

const syncStatus = computed(() => {
  return systemStore.syncStatus || 'normal'
})

const syncStatusText = computed(() => {
  const statusMap = {
    normal: '同步正常',
    warning: '同步警告', 
    error: '同步异常',
    unknown: '状态未知'
  }
  return statusMap[syncStatus.value as keyof typeof statusMap] || '未知状态'
})

const currentTimeSource = computed(() => {
  return systemStore.currentTimeSource || 'GNSS'
})

const timeSourceStatus = computed(() => {
  // 根据时间源类型返回状态
  const sourceStatusMap = {
    'GNSS': 'excellent',
    'RUBIDIUM': 'good', 
    'RTC': 'fair',
    'EXTERNAL_PPS': 'good',
    'EXTERNAL_10MHZ': 'good'
  }
  return sourceStatusMap[currentTimeSource.value as keyof typeof sourceStatusMap] || 'unknown'
})

const syncAccuracy = computed(() => {
  if (systemStore.systemStatus?.sync?.accuracy) {
    return systemStore.systemStatus.sync.accuracy
  }
  
  // 根据时间源提供默认精度
  const accuracyMap = {
    'GNSS': '±50ns',
    'RUBIDIUM': '±1μs',
    'RTC': '±100ms',
    'EXTERNAL_PPS': '±10ns',
    'EXTERNAL_10MHZ': '±1ns'
  }
  return accuracyMap[currentTimeSource.value as keyof typeof accuracyMap] || '未知'
})

// 方法
const getTimeSourceIcon = (source: string) => {
  const iconMap = {
    'GNSS': '🛰️',
    'RUBIDIUM': '⚛️',
    'RTC': '🕐',
    'EXTERNAL_PPS': '📡',
    'EXTERNAL_10MHZ': '📶'
  }
  return iconMap[source as keyof typeof iconMap] || '❓'
}

// 定时器
let timeInterval: ReturnType<typeof setInterval>

onMounted(() => {
  // 高频率更新时间以显示毫秒
  timeInterval = setInterval(() => {
    currentTime.value = new Date()
  }, 10) // 10ms更新一次
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style lang="scss" scoped>
.central-clock {
  padding: var(--spacing-2xl);
  text-align: center;
  min-width: 500px;
  max-width: 600px;
  margin: 0 auto;
  
  .clock-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    
    .date-info {
      text-align: left;
      
      .clock-date {
        display: block;
        font-size: 1rem;
        color: var(--text-primary);
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
      }
      
      .clock-timezone {
        font-size: 0.8rem;
        color: var(--text-secondary);
      }
    }
    
    .sync-indicator {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-md);
      background: rgba(255, 255, 255, 0.05);
      
      .sync-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
      
      .sync-text {
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      &.normal {
        .sync-dot { background: var(--status-success); }
        .sync-text { color: var(--status-success); }
      }
      
      &.warning {
        .sync-dot { background: var(--status-warning); }
        .sync-text { color: var(--status-warning); }
      }
      
      &.error {
        .sync-dot { background: var(--status-error); }
        .sync-text { color: var(--status-error); }
      }
    }
  }
  
  .main-clock {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin: var(--spacing-2xl) 0;
    
    .time-display {
      font-size: 4.5rem;
      font-weight: 200;
      font-family: 'Monaco', 'Menlo', 'SF Mono', monospace;
      color: var(--text-primary);
      letter-spacing: 0.05em;
      text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    }
    
    .milliseconds {
      font-size: 2rem;
      font-weight: 300;
      font-family: 'Monaco', 'Menlo', 'SF Mono', monospace;
      color: var(--text-secondary);
      margin-left: var(--spacing-sm);
      opacity: 0.8;
    }
  }
  
  .clock-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    
    .time-source-info, .accuracy-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-sm);
    }
    
    .source-label, .accuracy-label {
      font-size: 0.8rem;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .source-value {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--radius-md);
      font-weight: 500;
      
      .source-icon {
        font-size: 1.2rem;
      }
      
      &.excellent {
        background: rgba(16, 185, 129, 0.2);
        color: var(--status-success);
      }
      
      &.good {
        background: rgba(59, 130, 246, 0.2);
        color: var(--status-info);
      }
      
      &.fair {
        background: rgba(245, 158, 11, 0.2);
        color: var(--status-warning);
      }
      
      &.unknown {
        background: rgba(107, 114, 128, 0.2);
        color: var(--text-secondary);
      }
    }
    
    .accuracy-value {
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
      padding: var(--spacing-sm) var(--spacing-md);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-md);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .central-clock {
    min-width: auto;
    width: 100%;
    padding: var(--spacing-xl);
    
    .main-clock {
      .time-display {
        font-size: 3rem;
      }
      
      .milliseconds {
        font-size: 1.5rem;
      }
    }
    
    .clock-footer {
      flex-direction: column;
      gap: var(--spacing-lg);
      
      .time-source-info, .accuracy-info {
        width: 100%;
      }
    }
  }
}
</style>