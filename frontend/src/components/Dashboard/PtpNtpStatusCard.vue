<template>
  <div class="status-card glass-card">
    <div class="card-header">
      <div class="card-title-section">
        <h3 class="card-title">PTP/NTP服务</h3>
        <div class="card-subtitle">Precision Time Protocol & Network Time Protocol</div>
      </div>
      <div class="card-icon">🌐</div>
    </div>
    
    <div class="card-content">
      <!-- PTP服务状态 -->
      <div class="service-section">
        <div class="service-header">
          <div class="service-title">
            <div class="service-icon">⚡</div>
            <span>PTP服务</span>
          </div>
          <div class="service-status" :class="ptpData.status">
            <div class="status-dot pulse"></div>
            <span>{{ getServiceStatusText(ptpData.status) }}</span>
          </div>
        </div>
        
        <div class="service-metrics">
          <div class="metric-grid">
            <div class="metric-item">
              <div class="metric-label">时钟等级</div>
              <div class="metric-value clock-class" :class="getClockClassStatus(ptpData.clockClass)">
                {{ ptpData.clockClass }}
              </div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">从时钟数</div>
              <div class="metric-value">{{ ptpData.clients }}</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">同步精度</div>
              <div class="metric-value">{{ ptpData.accuracy }}</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">网络延迟</div>
              <div class="metric-value">{{ ptpData.networkDelay }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分隔线 -->
      <div class="service-divider"></div>
      
      <!-- NTP服务状态 -->
      <div class="service-section">
        <div class="service-header">
          <div class="service-title">
            <div class="service-icon">⏰</div>
            <span>NTP服务</span>
          </div>
          <div class="service-status" :class="ntpData.status">
            <div class="status-dot pulse"></div>
            <span>{{ getServiceStatusText(ntpData.status) }}</span>
          </div>
        </div>
        
        <div class="service-metrics">
          <div class="metric-grid">
            <div class="metric-item">
              <div class="metric-label">服务层级</div>
              <div class="metric-value stratum" :class="getStratumStatus(ntpData.stratum)">
                {{ ntpData.stratum }}
              </div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">客户端数</div>
              <div class="metric-value">{{ ntpData.clients }}</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">平均延迟</div>
              <div class="metric-value">{{ ntpData.averageDelay }}</div>
            </div>
            
            <div class="metric-item">
              <div class="metric-label">最后同步</div>
              <div class="metric-value">{{ ntpData.lastSync }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 客户端连接图表 -->
      <div class="clients-chart">
        <div class="chart-header">
          <span class="chart-title">客户端连接趋势</span>
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-dot ptp"></div>
              <span>PTP</span>
            </div>
            <div class="legend-item">
              <div class="legend-dot ntp"></div>
              <span>NTP</span>
            </div>
          </div>
        </div>
        
        <div class="mini-chart">
          <svg viewBox="0 0 200 80" class="chart-svg">
            <!-- PTP客户端趋势线 -->
            <path 
              :d="ptpClientsTrendPath" 
              fill="none" 
              stroke="var(--status-info)" 
              stroke-width="2"
              opacity="0.8"
            />
            <!-- NTP客户端趋势线 -->
            <path 
              :d="ntpClientsTrendPath" 
              fill="none" 
              stroke="var(--status-success)" 
              stroke-width="2"
              opacity="0.8"
            />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSystemStore } from '@/stores/system'

const systemStore = useSystemStore()

// 从store获取数据
const ptpData = computed(() => {
  const ptpInfo = systemStore.systemStatus?.ptp
  if (!ptpInfo) {
    return {
      status: 'normal',
      clockClass: 6,
      clients: 25,
      accuracy: '±50ns',
      networkDelay: '125μs'
    }
  }
  
  return {
    status: ptpInfo.status || 'normal',
    clockClass: ptpInfo.clockClass || 6,
    clients: ptpInfo.clients || 25,
    accuracy: ptpInfo.accuracy || '±50ns',
    networkDelay: ptpInfo.networkDelay || '125μs'
  }
})

const ntpData = computed(() => {
  const ntpInfo = systemStore.systemStatus?.ntp
  if (!ntpInfo) {
    return {
      status: 'normal',
      stratum: 1,
      clients: 139,
      averageDelay: '2.6ms',
      lastSync: '3秒前'
    }
  }
  
  return {
    status: ntpInfo.status || 'normal',
    stratum: ntpInfo.stratum || 1,
    clients: ntpInfo.clients || 139,
    averageDelay: ntpInfo.averageDelay || '2.6ms',
    lastSync: ntpInfo.lastSync ? '刚刚' : '3秒前'
  }
})

// 模拟客户端连接趋势数据
const ptpClientsData = ref([20, 22, 25, 23, 26, 24, 25, 27, 25, 24])
const ntpClientsData = ref([120, 125, 130, 135, 139, 142, 138, 140, 139, 137])

// 计算属性
const ptpClientsTrendPath = computed(() => {
  return generateTrendPath(ptpClientsData.value, 200, 80, 10)
})

const ntpClientsTrendPath = computed(() => {
  return generateTrendPath(ntpClientsData.value, 200, 80, 50)
})

// 方法
const generateTrendPath = (data: number[], width: number, height: number, yOffset: number) => {
  if (data.length === 0) return ''
  
  const padding = 10
  const maxValue = Math.max(...data)
  const minValue = Math.min(...data)
  const range = maxValue - minValue || 1
  
  let path = ''
  
  data.forEach((value, index) => {
    const x = padding + (index / (data.length - 1)) * (width - 2 * padding)
    const y = yOffset + padding + ((maxValue - value) / range) * (height / 2 - 2 * padding)
    
    if (index === 0) {
      path += `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })
  
  return path
}

const getServiceStatusText = (status: string) => {
  const statusMap = {
    normal: '正常运行',
    warning: '需要注意',
    error: '服务异常',
    stopped: '服务停止'
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}

const getClockClassStatus = (clockClass: number) => {
  if (clockClass <= 6) return 'excellent'
  if (clockClass <= 7) return 'good'
  if (clockClass <= 52) return 'fair'
  return 'poor'
}

const getStratumStatus = (stratum: number) => {
  if (stratum === 1) return 'excellent'
  if (stratum <= 3) return 'good'
  if (stratum <= 8) return 'fair'
  return 'poor'
}
</script>

<style lang="scss" scoped>
.status-card {
  padding: var(--spacing-lg);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-lg);
    
    .card-title-section {
      .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
      }
      
      .card-subtitle {
        font-size: 0.8rem;
        color: var(--text-secondary);
        opacity: 0.8;
      }
    }
    
    .card-icon {
      font-size: 2rem;
      opacity: 0.7;
    }
  }
  
  .card-content {
    .service-section {
      margin-bottom: var(--spacing-lg);
      
      .service-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        
        .service-title {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          font-weight: 600;
          color: var(--text-primary);
          
          .service-icon {
            font-size: 1.2rem;
          }
        }
        
        .service-status {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          font-size: 0.9rem;
          
          .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            
            &.normal { background: var(--status-success); }
            &.warning { background: var(--status-warning); }
            &.error { background: var(--status-error); }
            &.stopped { background: var(--text-secondary); }
          }
          
          span {
            color: var(--text-secondary);
          }
        }
      }
      
      .service-metrics {
        .metric-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--spacing-md);
          
          .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            background: rgba(255, 255, 255, 0.03);
            border-radius: var(--radius-sm);
            
            .metric-label {
              font-size: 0.8rem;
              color: var(--text-secondary);
            }
            
            .metric-value {
              font-weight: 600;
              color: var(--text-primary);
              font-family: 'Monaco', 'Menlo', monospace;
              
              &.clock-class, &.stratum {
                padding: var(--spacing-xs) var(--spacing-sm);
                border-radius: var(--radius-sm);
                font-size: 0.9rem;
                
                &.excellent {
                  background: rgba(16, 185, 129, 0.2);
                  color: var(--status-success);
                }
                
                &.good {
                  background: rgba(59, 130, 246, 0.2);
                  color: var(--status-info);
                }
                
                &.fair {
                  background: rgba(245, 158, 11, 0.2);
                  color: var(--status-warning);
                }
                
                &.poor {
                  background: rgba(239, 68, 68, 0.2);
                  color: var(--status-error);
                }
              }
            }
          }
        }
      }
    }
    
    .service-divider {
      height: 1px;
      background: rgba(255, 255, 255, 0.1);
      margin: var(--spacing-lg) 0;
    }
    
    .clients-chart {
      padding: var(--spacing-md);
      background: rgba(255, 255, 255, 0.03);
      border-radius: var(--radius-md);
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        
        .chart-title {
          font-size: 0.9rem;
          color: var(--text-primary);
          font-weight: 500;
        }
        
        .chart-legend {
          display: flex;
          gap: var(--spacing-md);
          
          .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: 0.8rem;
            color: var(--text-secondary);
            
            .legend-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              
              &.ptp { background: var(--status-info); }
              &.ntp { background: var(--status-success); }
            }
          }
        }
      }
      
      .mini-chart {
        height: 80px;
        
        .chart-svg {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .status-card {
    .card-content {
      .service-section {
        .service-metrics {
          .metric-grid {
            grid-template-columns: 1fr;
          }
        }
      }
      
      .clients-chart {
        .chart-header {
          flex-direction: column;
          gap: var(--spacing-sm);
          align-items: flex-start;
        }
      }
    }
  }
}
</style>