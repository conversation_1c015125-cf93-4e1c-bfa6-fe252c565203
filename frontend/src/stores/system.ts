import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { SystemStatus, TimeSourceInfo, SignalOutput } from '@/types/system'
import { systemApi } from '@/services/api'
import { useAuthStore } from '@/stores/auth'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const systemStatus = ref<SystemStatus | null>(null)
  const timeSourceInfo = ref<TimeSourceInfo | null>(null)
  const signalOutput = ref<SignalOutput | null>(null)
  const isConnected = ref(false)
  const lastUpdateTime = ref<Date | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // WebSocket连接
  let websocket: WebSocket | null = null
  let reconnectTimer: ReturnType<typeof setTimeout> | null = null
  const reconnectInterval = 5000 // 5秒重连间隔

  // 计算属性
  const overallStatus = computed(() => {
    if (!systemStatus.value) return 'unknown'

    // 根据各个子系统状态计算总体状态
    const statuses = [
      systemStatus.value.gnss?.status,
      systemStatus.value.ptp?.status,
      systemStatus.value.ntp?.status,
      systemStatus.value.clock?.status
    ].filter(Boolean)

    if (statuses.includes('error')) return 'error'
    if (statuses.includes('warning')) return 'warning'
    if (statuses.includes('normal')) return 'normal'

    return 'unknown'
  })

  const syncStatus = computed(() => {
    return systemStatus.value?.sync?.status || 'unknown'
  })

  const currentTimeSource = computed(() => {
    return systemStatus.value?.sync?.currentSource || 'Unknown'
  })

  const isHealthy = computed(() => {
    return overallStatus.value === 'normal' && isConnected.value
  })

  // 方法
  const initialize = async () => {
    try {
      loading.value = true
      error.value = null

      // 获取认证store
      const authStore = useAuthStore()
      
      if (authStore.isAuthenticated) {
        // 只有在已登录时才获取系统状态
        try {
          await fetchSystemStatus()
        } catch (err) {
          // 如果是401错误，说明token已过期，不需要显示错误
          if (err instanceof Error && err.message.includes('401')) {
            console.log('Token expired, user needs to login')
          } else {
            throw err
          }
        }
        
        // 建立WebSocket连接
        connectWebSocket()
      } else {
        console.log('User not authenticated, skipping system status fetch')
      }

      // 启动模拟数据更新（开发环境）
      if (import.meta.env.DEV) {
        startMockDataUpdates()
      }

    } catch (err) {
      error.value = err instanceof Error ? err.message : '系统初始化失败'
      console.error('System store initialization failed:', err)
    } finally {
      loading.value = false
    }
  }

  // 模拟数据更新（开发环境使用）
  let mockUpdateInterval: ReturnType<typeof setInterval> | null = null

  const startMockDataUpdates = () => {
    // 初始化模拟数据
    systemStatus.value = {
      timestamp: new Date().toISOString(),
      uptime: 247 * 24 * 3600 + 12 * 3600, // 247天12小时
      sync: {
        status: 'normal',
        currentSource: 'GNSS',
        accuracy: '±50ns',
        lastSync: new Date().toISOString()
      },
      gnss: {
        status: 'normal',
        visibleSatellites: 24,
        trackingSatellites: 17,
        signalQuality: '85%',
        accuracy: '2.4m',
        lastUpdate: new Date().toISOString()
      },
      ptp: {
        status: 'normal',
        mode: 'grandmaster',
        clients: 25,
        accuracy: '±50ns',
        networkDelay: '125μs',
        clockClass: 6
      },
      ntp: {
        status: 'normal',
        stratum: 1,
        clients: 139,
        averageDelay: '2.6ms',
        lastSync: new Date().toISOString()
      },
      clock: {
        status: 'normal',
        frequency: '9.192631770 GHz',
        temperature: 45.3,
        uptime: '247天12小时',
        stability: '1.2×10⁻¹²'
      },
      resources: {
        cpu: {
          usage: 15,
          temperature: 42
        },
        memory: {
          usage: 68,
          total: 16 * 1024 * 1024 * 1024,
          available: 5.1 * 1024 * 1024 * 1024
        },
        network: {
          throughput: '1.1 MB/s',
          connections: 47
        },
        storage: {
          usage: 35,
          total: 500 * 1024 * 1024 * 1024,
          available: 325 * 1024 * 1024 * 1024
        }
      }
    }

    // 定期更新模拟数据
    mockUpdateInterval = setInterval(() => {
      if (systemStatus.value) {
        // 模拟CPU使用率变化
        systemStatus.value.resources.cpu.usage = Math.max(5, Math.min(95,
          systemStatus.value.resources.cpu.usage + (Math.random() - 0.5) * 10))

        // 模拟内存使用率变化
        systemStatus.value.resources.memory.usage = Math.max(30, Math.min(90,
          systemStatus.value.resources.memory.usage + (Math.random() - 0.5) * 5))

        // 模拟温度变化
        systemStatus.value.clock.temperature = Math.max(40, Math.min(50,
          systemStatus.value.clock.temperature + (Math.random() - 0.5) * 2))

        // 模拟客户端数量变化
        systemStatus.value.ptp.clients = Math.max(20, Math.min(30,
          systemStatus.value.ptp.clients + Math.floor((Math.random() - 0.5) * 4)))

        systemStatus.value.ntp.clients = Math.max(130, Math.min(150,
          systemStatus.value.ntp.clients + Math.floor((Math.random() - 0.5) * 8)))

        // 更新时间戳
        systemStatus.value.timestamp = new Date().toISOString()
        lastUpdateTime.value = new Date()
      }
    }, 2000) // 每2秒更新一次
  }

  const fetchSystemStatus = async () => {
    try {
      const response = await systemApi.getSystemStatus()
      systemStatus.value = response.data.data
      lastUpdateTime.value = new Date()
      error.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取系统状态失败'
      throw err
    }
  }

  const fetchTimeSourceInfo = async () => {
    try {
      const response = await systemApi.getTimeSourceInfo()
      timeSourceInfo.value = response.data.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取时间源信息失败'
      throw err
    }
  }

  const fetchSignalOutput = async () => {
    try {
      const response = await systemApi.getSignalOutput()
      signalOutput.value = response.data.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取信号输出状态失败'
      throw err
    }
  }

  const connectWebSocket = () => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.hostname
      const wsUrl = `${protocol}//${host}:8081`

      websocket = new WebSocket(wsUrl)

      websocket.onopen = () => {
        console.log('WebSocket connected')
        isConnected.value = true
        error.value = null

        // 清除重连定时器
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
          reconnectTimer = null
        }
      }

      websocket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleWebSocketMessage(data)
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }

      websocket.onclose = (event) => {
        console.log('WebSocket disconnected', event.code, event.reason)
        isConnected.value = false

        // 如果是404错误（WebSocket不支持），不要重连
        if (event.code === 1002 || event.code === 1006) {
          console.log('WebSocket not supported by server, falling back to HTTP polling')
          return
        }

        // 自动重连（仅在非404错误时）
        if (!reconnectTimer) {
          reconnectTimer = setTimeout(() => {
            reconnectTimer = null
            connectWebSocket()
          }, reconnectInterval)
        }
      }

      websocket.onerror = (err) => {
        console.log('WebSocket error (server may not support WebSocket):', err)
        // 不设置error状态，因为这是预期的行为
      }

    } catch (err) {
      console.error('Failed to connect WebSocket:', err)
      // 不设置error状态，因为WebSocket是可选的
    }
  }

  const handleWebSocketMessage = (data: any) => {
    try {
      switch (data.type) {
        case 'system_status':
          systemStatus.value = data.payload
          lastUpdateTime.value = new Date()
          break

        case 'time_source_update':
          if (timeSourceInfo.value) {
            Object.assign(timeSourceInfo.value, data.payload)
          }
          break

        case 'signal_output_update':
          if (signalOutput.value) {
            Object.assign(signalOutput.value, data.payload)
          }
          break

        case 'alert':
          // 处理告警消息
          console.warn('System alert:', data.payload)
          // 可以在这里触发通知或更新告警状态
          break

        case 'heartbeat':
          // 心跳消息，更新连接状态
          lastUpdateTime.value = new Date()
          break

        case 'status_update':
          // 实时状态更新
          if (data.payload && systemStatus.value) {
            // 深度合并状态更新
            mergeStatusUpdate(systemStatus.value, data.payload)
            lastUpdateTime.value = new Date()
          }
          break

        default:
          console.log('Unknown WebSocket message type:', data.type)
      }
    } catch (err) {
      console.error('Failed to handle WebSocket message:', err)
    }
  }

  const mergeStatusUpdate = (current: any, update: any) => {
    for (const key in update) {
      if (update.hasOwnProperty(key)) {
        if (typeof update[key] === 'object' && update[key] !== null && !Array.isArray(update[key])) {
          if (!current[key]) {
            current[key] = {}
          }
          mergeStatusUpdate(current[key], update[key])
        } else {
          current[key] = update[key]
        }
      }
    }
  }

  const disconnectWebSocket = () => {
    if (websocket) {
      websocket.close()
      websocket = null
    }

    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    isConnected.value = false
  }

  const refreshData = async () => {
    try {
      loading.value = true
      await Promise.all([
        fetchSystemStatus(),
        fetchTimeSourceInfo(),
        fetchSignalOutput()
      ])
    } catch (err) {
      console.error('Failed to refresh data:', err)
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  // 清理函数
  const cleanup = () => {
    disconnectWebSocket()

    // 清理模拟数据更新定时器
    if (mockUpdateInterval) {
      clearInterval(mockUpdateInterval)
      mockUpdateInterval = null
    }
  }

  return {
    // 状态
    systemStatus,
    timeSourceInfo,
    signalOutput,
    isConnected,
    lastUpdateTime,
    loading,
    error,

    // 计算属性
    overallStatus,
    syncStatus,
    currentTimeSource,
    isHealthy,

    // 方法
    initialize,
    fetchSystemStatus,
    fetchTimeSourceInfo,
    fetchSignalOutput,
    connectWebSocket,
    disconnectWebSocket,
    refreshData,
    clearError,
    cleanup
  }
})