import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authApi } from '@/services/api'
import type { User, AuthToken } from '@/types/system'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refresh_token'))
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isLoading = computed(() => loading.value)

  // 方法
  const login = async (username: string, password: string) => {
    try {
      loading.value = true
      error.value = null

      const response = await authApi.login(username, password)
      const authData: AuthToken = response.data.data

      // 保存token (API返回的是token，不是accessToken)
      const accessToken = authData.token || authData.accessToken
      if (!accessToken) {
        throw new Error('No access token received')
      }

      token.value = accessToken
      refreshToken.value = authData.refreshToken

      // 保存到localStorage
      localStorage.setItem('auth_token', accessToken)
      if (authData.refreshToken) {
        localStorage.setItem('refresh_token', authData.refreshToken)
      }

      // 获取用户信息
      await getCurrentUser()

      return authData
    } catch (err: any) {
      error.value = err.response?.data?.error?.message || '登录失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      loading.value = true
      
      // 调用后端登出API
      if (token.value) {
        await authApi.logout()
      }
    } catch (err) {
      console.error('Logout API call failed:', err)
    } finally {
      // 清除本地状态
      clearAuthData()
      loading.value = false
    }
  }

  const clearAuthData = () => {
    token.value = null
    refreshToken.value = null
    user.value = null
    error.value = null

    // 清除localStorage
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_info')
  }

  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }

      const response = await authApi.refreshToken(refreshToken.value)
      const authData: AuthToken = response.data.data

      // 更新token
      const accessToken = authData.token || authData.accessToken
      if (!accessToken) {
        throw new Error('No access token received')
      }

      token.value = accessToken
      if (authData.refreshToken) {
        refreshToken.value = authData.refreshToken
        localStorage.setItem('refresh_token', authData.refreshToken)
      }
      localStorage.setItem('auth_token', accessToken)

      return accessToken
    } catch (err) {
      // 刷新失败，清除认证数据
      clearAuthData()
      throw err
    }
  }

  const getCurrentUser = async () => {
    try {
      if (!token.value) {
        throw new Error('No auth token')
      }

      const response = await authApi.getCurrentUser()
      user.value = response.data.data
      localStorage.setItem('user_info', JSON.stringify(user.value))

      return user.value
    } catch (err) {
      console.error('Failed to get current user:', err)
      throw err
    }
  }

  const initialize = async () => {
    try {
      // 从localStorage恢复用户信息
      const savedUser = localStorage.getItem('user_info')
      if (savedUser && token.value) {
        user.value = JSON.parse(savedUser)
        
        // 验证token是否仍然有效
        try {
          await getCurrentUser()
        } catch (err) {
          // token无效，清除认证数据
          clearAuthData()
        }
      }
    } catch (err) {
      console.error('Auth store initialization failed:', err)
      clearAuthData()
    }
  }

  const checkAuthStatus = () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('user_info')
    
    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)
      return true
    }
    
    return false
  }

  return {
    // 状态
    user,
    token,
    refreshToken,
    loading,
    error,
    
    // 计算属性
    isAuthenticated,
    isLoading,
    
    // 方法
    login,
    logout,
    clearAuthData,
    refreshAccessToken,
    getCurrentUser,
    initialize,
    checkAuthStatus
  }
})