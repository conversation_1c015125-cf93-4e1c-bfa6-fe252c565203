import axios, { type AxiosInstance, type AxiosResponse } from 'axios'
import type { 
  SystemStatus, 
  TimeSourceInfo, 
  SignalOutput, 
  SystemConfiguration,
  SystemAlert,
  SystemEvent,
  LogEntry,
  LogQuery,
  User,
  AuthToken,
  ApiResponse,
  GnssConfig,
  PtpConfig,
  NtpConfig,
  AtomicClockConfig,
  TimeSourcePriority,
  ConfigBackup,
  ConfigValidation
} from '@/types/system'

// 创建axios实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: '/api/v1',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器 - 添加认证token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('auth_token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器 - 处理错误和token刷新
  instance.interceptors.response.use(
    (response) => {
      return response
    },
    async (error) => {
      const originalRequest = error.config

      // 处理401未授权错误
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true

        try {
          const refreshToken = localStorage.getItem('refresh_token')
          if (refreshToken) {
            const response = await instance.post('/auth/refresh', {
              refreshToken
            })
            
            const { accessToken } = response.data
            localStorage.setItem('auth_token', accessToken)
            
            // 重试原始请求
            originalRequest.headers.Authorization = `Bearer ${accessToken}`
            return instance(originalRequest)
          }
        } catch (refreshError) {
          // 刷新token失败，跳转到登录页
          localStorage.removeItem('auth_token')
          localStorage.removeItem('refresh_token')
          window.location.href = '/login'
          return Promise.reject(refreshError)
        }
      }

      return Promise.reject(error)
    }
  )

  return instance
}

const api = createApiInstance()

// 系统状态API
export const systemApi = {
  // 获取系统状态
  getSystemStatus(): Promise<AxiosResponse<ApiResponse<SystemStatus>>> {
    return api.get('/system/status')
  },

  // 获取时间源信息
  getTimeSourceInfo(): Promise<AxiosResponse<ApiResponse<TimeSourceInfo>>> {
    return api.get('/timesource/info')
  },

  // 获取信号输出状态
  getSignalOutput(): Promise<AxiosResponse<ApiResponse<SignalOutput>>> {
    return api.get('/signal/output')
  },

  // 获取系统配置
  getSystemConfig(): Promise<AxiosResponse<ApiResponse<SystemConfiguration>>> {
    return api.get('/system/config')
  },

  // 更新系统配置
  updateSystemConfig(config: Partial<SystemConfiguration>): Promise<AxiosResponse<ApiResponse<SystemConfiguration>>> {
    return api.put('/system/config', config)
  },

  // 重启系统
  restartSystem(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/system/restart')
  },

  // 关闭系统
  shutdownSystem(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/system/shutdown')
  }
}

// 时间源配置API
export const timeSourceApi = {
  // GNSS配置
  getGnssConfig(): Promise<AxiosResponse<ApiResponse<GnssConfig>>> {
    return api.get('/timesource/gnss/config')
  },

  updateGnssConfig(config: GnssConfig): Promise<AxiosResponse<ApiResponse<GnssConfig>>> {
    return api.put('/timesource/gnss/config', config)
  },

  testGnssConnection(config: Partial<GnssConfig>): Promise<AxiosResponse<ApiResponse<{ status: string; message: string }>>> {
    return api.post('/timesource/gnss/test', config)
  },

  getGnssStatus(): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/timesource/gnss/status')
  },

  resetGnssReceiver(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/timesource/gnss/reset')
  },

  // PTP配置
  getPtpConfig(): Promise<AxiosResponse<ApiResponse<PtpConfig>>> {
    return api.get('/timesource/ptp/config')
  },

  updatePtpConfig(config: PtpConfig): Promise<AxiosResponse<ApiResponse<PtpConfig>>> {
    return api.put('/timesource/ptp/config', config)
  },

  getPtpStatus(): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/timesource/ptp/status')
  },

  restartPtpService(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/timesource/ptp/restart')
  },

  getPtpClients(): Promise<AxiosResponse<ApiResponse<any[]>>> {
    return api.get('/timesource/ptp/clients')
  },

  // NTP配置
  getNtpConfig(): Promise<AxiosResponse<ApiResponse<NtpConfig>>> {
    return api.get('/timesource/ntp/config')
  },

  updateNtpConfig(config: NtpConfig): Promise<AxiosResponse<ApiResponse<NtpConfig>>> {
    return api.put('/timesource/ntp/config', config)
  },

  getNtpStatus(): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/timesource/ntp/status')
  },

  restartNtpService(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/timesource/ntp/restart')
  },

  getNtpClients(): Promise<AxiosResponse<ApiResponse<any[]>>> {
    return api.get('/timesource/ntp/clients')
  },

  // 原子钟配置
  getAtomicClockConfig(): Promise<AxiosResponse<ApiResponse<AtomicClockConfig>>> {
    return api.get('/timesource/atomic/config')
  },

  updateAtomicClockConfig(config: AtomicClockConfig): Promise<AxiosResponse<ApiResponse<AtomicClockConfig>>> {
    return api.put('/timesource/atomic/config', config)
  },

  getAtomicClockStatus(): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/timesource/atomic/status')
  },

  calibrateAtomicClock(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/timesource/atomic/calibrate')
  },

  getAtomicClockLearningData(): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/timesource/atomic/learning')
  },

  resetAtomicClockLearning(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/timesource/atomic/learning/reset')
  },

  // 时间源优先级配置
  getTimeSourcePriority(): Promise<AxiosResponse<ApiResponse<TimeSourcePriority>>> {
    return api.get('/timesource/priority')
  },

  updateTimeSourcePriority(config: TimeSourcePriority): Promise<AxiosResponse<ApiResponse<TimeSourcePriority>>> {
    return api.put('/timesource/priority', config)
  },

  switchTimeSource(sourceId: string): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/timesource/switch', { sourceId })
  },

  getTimeSourceSwitchHistory(): Promise<AxiosResponse<ApiResponse<any[]>>> {
    return api.get('/timesource/switch/history')
  }
}

// 信号输出配置API
export const signalApi = {
  // 获取信号输出配置
  getOutputConfig(): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/signal/config')
  },

  // 更新信号输出配置
  updateOutputConfig(config: any): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.put('/signal/config', config)
  },

  // 启用/禁用信号输出
  toggleOutput(outputId: string, enabled: boolean): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.post(`/signal/output/${outputId}/toggle`, { enabled })
  }
}

// 告警和事件API
export const alertApi = {
  // 获取告警列表
  getAlerts(params?: { limit?: number; offset?: number }): Promise<AxiosResponse<ApiResponse<SystemAlert[]>>> {
    return api.get('/alerts', { params })
  },

  // 确认告警
  acknowledgeAlert(alertId: string): Promise<AxiosResponse<ApiResponse<SystemAlert>>> {
    return api.post(`/alerts/${alertId}/acknowledge`)
  },

  // 获取系统事件
  getEvents(params?: { limit?: number; offset?: number }): Promise<AxiosResponse<ApiResponse<SystemEvent[]>>> {
    return api.get('/events', { params })
  }
}

// 日志API
export const logApi = {
  // 获取日志
  getLogs(query: LogQuery): Promise<AxiosResponse<ApiResponse<LogEntry[]>>> {
    return api.get('/logs', { params: query })
  },

  // 下载日志文件
  downloadLogs(params: { startTime: string; endTime: string }): Promise<AxiosResponse<Blob>> {
    return api.get('/logs/download', {
      params,
      responseType: 'blob'
    })
  }
}

// 用户管理API
export const userApi = {
  // 获取用户列表
  getUsers(): Promise<AxiosResponse<ApiResponse<User[]>>> {
    return api.get('/users')
  },

  // 创建用户
  createUser(user: Omit<User, 'id' | 'lastLogin'>): Promise<AxiosResponse<ApiResponse<User>>> {
    return api.post('/users', user)
  },

  // 更新用户
  updateUser(userId: string, user: Partial<User>): Promise<AxiosResponse<ApiResponse<User>>> {
    return api.put(`/users/${userId}`, user)
  },

  // 删除用户
  deleteUser(userId: string): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.delete(`/users/${userId}`)
  },

  // 修改密码
  changePassword(userId: string, oldPassword: string, newPassword: string): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post(`/users/${userId}/password`, {
      oldPassword,
      newPassword
    })
  }
}

// 认证API
export const authApi = {
  // 登录
  login(username: string, password: string): Promise<AxiosResponse<ApiResponse<AuthToken>>> {
    return api.post('/auth/login', { username, password })
  },

  // 登出
  logout(): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post('/auth/logout')
  },

  // 刷新token
  refreshToken(refreshToken: string): Promise<AxiosResponse<ApiResponse<AuthToken>>> {
    return api.post('/auth/refresh', { refreshToken })
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<AxiosResponse<ApiResponse<User>>> {
    return api.get('/auth/user')
  }
}

// 性能监控API
export const performanceApi = {
  // 获取性能指标
  getMetrics(params?: { 
    startTime?: string
    endTime?: string
    metric?: string
    interval?: string
  }): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/performance/metrics', { params })
  },

  // 获取实时性能数据
  getRealTimeMetrics(): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.get('/performance/realtime')
  }
}

// 配置导入导出API
export const configApi = {
  // 导出配置
  exportConfig(): Promise<AxiosResponse<Blob>> {
    return api.get('/config/export', {
      responseType: 'blob'
    })
  },

  // 导入配置
  importConfig(file: File): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    const formData = new FormData()
    formData.append('config', file)
    
    return api.post('/config/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 验证配置
  validateConfig(config: any): Promise<AxiosResponse<ApiResponse<ConfigValidation>>> {
    return api.post('/config/validate', config)
  },

  // 配置备份管理
  getConfigBackups(): Promise<AxiosResponse<ApiResponse<ConfigBackup[]>>> {
    return api.get('/config/backups')
  },

  createConfigBackup(name: string, description?: string): Promise<AxiosResponse<ApiResponse<ConfigBackup>>> {
    return api.post('/config/backups', { name, description })
  },

  restoreConfigBackup(backupId: string): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post(`/config/backups/${backupId}/restore`)
  },

  deleteConfigBackup(backupId: string): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.delete(`/config/backups/${backupId}`)
  },

  downloadConfigBackup(backupId: string): Promise<AxiosResponse<Blob>> {
    return api.get(`/config/backups/${backupId}/download`, {
      responseType: 'blob'
    })
  },

  // 配置比较
  compareConfigs(config1: any, config2: any): Promise<AxiosResponse<ApiResponse<any>>> {
    return api.post('/config/compare', { config1, config2 })
  },

  // 配置模板
  getConfigTemplates(): Promise<AxiosResponse<ApiResponse<any[]>>> {
    return api.get('/config/templates')
  },

  applyConfigTemplate(templateId: string): Promise<AxiosResponse<ApiResponse<{ message: string }>>> {
    return api.post(`/config/templates/${templateId}/apply`)
  }
}

// 错误处理工具函数
export const handleApiError = (error: any): string => {
  if (error.response?.data?.error?.message) {
    return error.response.data.error.message
  }
  
  if (error.response?.status) {
    const statusMessages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '权限不足',
      404: '资源不存在',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用'
    }
    
    return statusMessages[error.response.status] || `HTTP错误: ${error.response.status}`
  }
  
  if (error.code === 'NETWORK_ERROR') {
    return '网络连接错误'
  }
  
  if (error.code === 'TIMEOUT') {
    return '请求超时'
  }
  
  return error.message || '未知错误'
}

export default api