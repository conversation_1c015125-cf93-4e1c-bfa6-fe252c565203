// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow-x: hidden;
}

// CSS变量定义
:root {
  // 主色调
  --primary-color: #3b82f6;
  --primary-color-dark: #2563eb;
  --success-color: #10b981;
  --success-color-dark: #059669;
  --warning-color: #f59e0b;
  --warning-color-dark: #d97706;
  --error-color: #ef4444;
  --error-color-dark: #dc2626;
  --info-color: #3b82f6;
  --info-color-dark: #2563eb;
  
  // 文本颜色
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  
  // 背景颜色
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-hover: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.1);
  --card-bg: rgba(255, 255, 255, 0.03);
  --input-bg: rgba(255, 255, 255, 0.05);
  --border-color: rgba(255, 255, 255, 0.1);
  --toggle-bg: #64748b;
  --progress-bg: rgba(255, 255, 255, 0.1);
  
  // 颜色系统（保持兼容性）
  --primary-gradient: linear-gradient(135deg, #1a1d3a 0%, #2d1b69 100%);
  --card-border: rgba(255, 255, 255, 0.2);
  --text-muted: rgba(255, 255, 255, 0.5);
  
  // 状态颜色（保持兼容性）
  --status-success: #10b981;
  --status-warning: #f59e0b;
  --status-error: #ef4444;
  --status-info: #3b82f6;
  
  // 间距系统
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  // 圆角
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  // 毛玻璃效果
  --glass-backdrop: blur(10px);
}

// 通用工具类
.glass-card {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--shadow-lg);
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--spacing-sm);
  
  &.success { background-color: var(--status-success); }
  &.warning { background-color: var(--status-warning); }
  &.error { background-color: var(--status-error); }
  &.info { background-color: var(--status-info); }
}

// 动画
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// 响应式断点
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

// 苹果风格的悬停效果
.apple-hover {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;

  &:hover {
    transform: scale(1.02) translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.98);
    transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 微妙的悬停效果
.subtle-hover {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;

  &:hover {
    transform: translateY(-1px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
    transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 按钮悬停效果
.button-hover {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
  }

  &:active {
    transform: translateY(-1px);
    transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 卡片悬停效果
.card-hover {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;

  &:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
  }

  &:active {
    transform: translateY(-2px) scale(1.005);
    transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}