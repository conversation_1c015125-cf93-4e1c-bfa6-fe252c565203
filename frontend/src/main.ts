import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import { useAuthStore } from './stores/auth'

// 导入全局样式
import './styles/main.scss'

const app = createApp(App)
const pinia = createPinia()

// 安装插件
app.use(pinia)
app.use(router)

// 初始化认证状态
const authStore = useAuthStore()
authStore.checkAuthStatus()

// 挂载应用
app.mount('#app')