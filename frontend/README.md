# 高精度授时服务器Web管理界面

## 项目概述

基于Vue.js 3 + TypeScript的现代化Web管理界面，采用深色主题设计，为高精度授时服务器系统提供直观的监控和管理功能。

## 设计特色

### 🎨 视觉设计
- **深色主题**: 深蓝色渐变背景，护眼且专业
- **毛玻璃效果**: 现代化的半透明卡片设计
- **响应式布局**: 支持桌面端和移动端适配
- **流畅动画**: 页面切换和状态变化的平滑过渡

### 📊 功能模块
- **实时监控仪表板**: 系统状态总览
- **时间源管理**: GNSS、原子钟、PTP等时间源配置
- **性能监控**: 实时图表和历史数据分析
- **系统配置**: 参数设置和配置管理
- **用户管理**: 权限控制和安全设置
- **日志查看**: 系统日志和事件记录

## 技术栈

- **框架**: Vue.js 3.3+ (Composition API)
- **语言**: TypeScript 5.0+
- **构建工具**: Vite 4.3+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **UI组件**: 自定义组件库
- **图表**: Chart.js 4.3+ with vue-chartjs 5.2+
- **HTTP客户端**: Axios 1.4+
- **工具库**: @vueuse/core 10.0+

## 项目结构

```
frontend/
├── src/
│   ├── components/          # 通用组件
│   │   ├── common/         # 基础组件
│   │   ├── charts/         # 图表组件
│   │   └── forms/          # 表单组件
│   ├── views/              # 页面组件
│   │   ├── Dashboard/      # 仪表板
│   │   ├── TimeSource/     # 时间源管理
│   │   ├── Performance/    # 性能监控
│   │   ├── Config/         # 系统配置
│   │   ├── Users/          # 用户管理
│   │   └── Logs/           # 日志查看
│   ├── stores/             # Pinia状态管理
│   ├── services/           # API服务
│   ├── utils/              # 工具函数
│   ├── types/              # TypeScript类型定义
│   ├── styles/             # 样式文件
│   └── main.ts             # 应用入口
├── public/                 # 静态资源
├── package.json
├── vite.config.ts
└── tsconfig.json
```

## 开发计划

### 阶段1: 项目基础搭建
- [x] 项目初始化和依赖配置
- [ ] 基础组件库开发
- [ ] 路由和状态管理配置
- [ ] API服务层实现

### 阶段2: 核心功能开发
- [ ] 仪表板页面实现
- [ ] 时间源管理界面
- [ ] 实时数据展示
- [ ] 配置管理功能

### 阶段3: 高级功能
- [ ] 用户权限管理
- [ ] 日志查看和搜索
- [ ] 性能监控图表
- [ ] 移动端适配

## 快速开始

```bash
# 安装依赖
npm install

# 开发服务器
npm run dev

# 构建生产版本
npm run build

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 设计规范

### 颜色系统
- **主色调**: 深蓝色渐变 (#1a1d3a → #2d1b69)
- **卡片背景**: 半透明深色 (rgba(255,255,255,0.1))
- **文本颜色**: 白色/浅灰色
- **状态颜色**: 绿色(正常)、黄色(警告)、红色(错误)

### 组件规范
- **卡片**: 圆角12px，毛玻璃效果
- **按钮**: 圆角8px，悬停效果
- **输入框**: 深色背景，白色边框
- **图标**: 统一使用SVG图标

## API集成

与后端REST API和WebSocket服务集成：

- **REST API**: 配置管理、用户认证
- **WebSocket**: 实时状态更新、事件推送
- **数据格式**: JSON标准格式
- **错误处理**: 统一错误处理机制

## 部署说明

支持多种部署方式：
- **开发环境**: Vite开发服务器
- **生产环境**: 静态文件部署
- **容器化**: Docker部署支持