{"name": "timing-server-frontend", "version": "1.0.0", "description": "高精度授时服务器Web管理界面", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@vueuse/core": "^10.3.0", "axios": "^1.4.0", "chart.js": "^4.3.0", "date-fns": "^2.30.0", "lodash-es": "^4.17.21", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.4"}, "devDependencies": {"@types/lodash-es": "^4.17.8", "@types/node": "^20.4.5", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-vue": "^4.3.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "sass": "^1.89.2", "terser": "^5.43.1", "typescript": "~5.1.6", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}