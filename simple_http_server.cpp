#include <iostream>
#include <string>
#include <thread>
#include <atomic>
#include <sstream>
#include <regex>
#include <csignal>
#include <sys/socket.h>
#include <netinet/in.h>
#include <unistd.h>
#include <cstring>
#include <map>

// 全局变量
std::atomic<bool> g_running{true};

void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在关闭服务器..." << std::endl;
    g_running.store(false);
}

// 解析HTTP请求
struct HttpRequest {
    std::string method;
    std::string path;
    std::string body;
    std::map<std::string, std::string> headers;
};

HttpRequest parseHttpRequest(const std::string& request) {
    HttpRequest req;
    std::istringstream stream(request);
    std::string line;
    
    // 解析请求行
    if (std::getline(stream, line)) {
        std::istringstream lineStream(line);
        lineStream >> req.method >> req.path;
    }
    
    // 解析头部
    while (std::getline(stream, line) && line != "\r") {
        size_t colonPos = line.find(':');
        if (colonPos != std::string::npos) {
            std::string key = line.substr(0, colonPos);
            std::string value = line.substr(colonPos + 2); // 跳过 ": "
            // 移除末尾的 \r
            if (!value.empty() && value.back() == '\r') {
                value.pop_back();
            }
            req.headers[key] = value;
        }
    }
    
    // 解析请求体
    std::string body;
    while (std::getline(stream, line)) {
        body += line;
    }
    req.body = body;
    
    return req;
}

// 创建HTTP响应
std::string createHttpResponse(int statusCode, const std::string& statusText, 
                              const std::string& contentType, const std::string& body) {
    std::ostringstream response;
    response << "HTTP/1.1 " << statusCode << " " << statusText << "\r\n";
    response << "Content-Type: " << contentType << "\r\n";
    response << "Content-Length: " << body.length() << "\r\n";
    response << "Access-Control-Allow-Origin: *\r\n";
    response << "Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS\r\n";
    response << "Access-Control-Allow-Headers: Content-Type, Authorization\r\n";
    response << "\r\n";
    response << body;
    return response.str();
}

// 简单的JSON值提取函数
std::string extractJsonValue(const std::string& json, const std::string& key) {
    std::string searchKey = "\"" + key + "\"";
    size_t keyPos = json.find(searchKey);
    if (keyPos == std::string::npos) return "";

    size_t colonPos = json.find(":", keyPos);
    if (colonPos == std::string::npos) return "";

    size_t valueStart = json.find("\"", colonPos);
    if (valueStart == std::string::npos) return "";
    valueStart++; // 跳过引号

    size_t valueEnd = json.find("\"", valueStart);
    if (valueEnd == std::string::npos) return "";

    return json.substr(valueStart, valueEnd - valueStart);
}

// 处理登录请求
std::string handleLogin(const std::string& body) {
    // 简单的JSON解析
    std::string username = extractJsonValue(body, "username");
    std::string password = extractJsonValue(body, "password");
    
    std::cout << "登录尝试: 用户名=" << username << ", 密码=" << password << std::endl;
    
    // 验证凭据（简化版本）
    if (username == "admin" && password == "admin123") {
        // 生成模拟的JWT令牌
        std::string accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluaXN0cmF0b3IiLCJleHAiOjE3MjE5ODQ0MDB9.mock_signature";
        std::string refreshToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImFkbWluIiwidHlwZSI6InJlZnJlc2giLCJleHAiOjE3MjI1ODkyMDB9.mock_refresh_signature";
        
        std::string responseBody = R"({
    "success": true,
    "data": {
        "accessToken": ")" + accessToken + R"(",
        "refreshToken": ")" + refreshToken + R"(",
        "tokenType": "Bearer",
        "expiresIn": 3600,
        "user": {
            "username": "admin",
            "role": "administrator",
            "permissions": ["read", "write", "admin"]
        }
    }
})";
        
        std::cout << "登录成功: " << username << std::endl;
        return createHttpResponse(200, "OK", "application/json", responseBody);
    } else {
        std::string errorBody = R"({
    "success": false,
    "error": {
        "code": "INVALID_CREDENTIALS",
        "message": "用户名或密码错误"
    }
})";
        
        std::cout << "登录失败: 无效凭据" << std::endl;
        return createHttpResponse(401, "Unauthorized", "application/json", errorBody);
    }
}

// 处理HTTP请求
std::string handleRequest(const HttpRequest& req) {
    std::cout << "收到请求: " << req.method << " " << req.path << std::endl;
    
    // 处理CORS预检请求
    if (req.method == "OPTIONS") {
        return createHttpResponse(200, "OK", "text/plain", "");
    }
    
    // 处理登录请求
    if (req.method == "POST" && req.path == "/api/v1/auth/login") {
        return handleLogin(req.body);
    }
    
    // 处理获取当前用户信息请求
    if (req.method == "GET" && req.path == "/api/v1/auth/me") {
        std::string responseBody = R"({
    "success": true,
    "data": {
        "username": "admin",
        "role": "administrator",
        "permissions": ["read", "write", "admin"]
    }
})";
        return createHttpResponse(200, "OK", "application/json", responseBody);
    }
    
    // 默认404响应
    std::string errorBody = R"({
    "success": false,
    "error": {
        "code": "NOT_FOUND",
        "message": "接口不存在"
    }
})";
    
    return createHttpResponse(404, "Not Found", "application/json", errorBody);
}

// 处理客户端连接
void handleClient(int clientSocket) {
    char buffer[4096];
    ssize_t bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
    
    if (bytesRead > 0) {
        buffer[bytesRead] = '\0';
        std::string request(buffer);
        
        HttpRequest httpReq = parseHttpRequest(request);
        std::string response = handleRequest(httpReq);
        
        send(clientSocket, response.c_str(), response.length(), 0);
    }
    
    close(clientSocket);
}

int main() {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    std::cout << "=== 简单HTTP API服务器 ===" << std::endl;
    std::cout << "为高精度授时服务器系统提供登录API" << std::endl;
    
    const int port = 8080;
    
    // 创建socket
    int serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (serverSocket == -1) {
        std::cerr << "创建socket失败" << std::endl;
        return 1;
    }
    
    // 设置socket选项
    int opt = 1;
    if (setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        std::cerr << "设置socket选项失败" << std::endl;
        close(serverSocket);
        return 1;
    }
    
    // 绑定地址
    struct sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(port);
    
    if (bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        std::cerr << "绑定端口失败" << std::endl;
        close(serverSocket);
        return 1;
    }
    
    // 开始监听
    if (listen(serverSocket, 10) < 0) {
        std::cerr << "监听失败" << std::endl;
        close(serverSocket);
        return 1;
    }
    
    std::cout << "HTTP API服务器已启动，监听端口: " << port << std::endl;
    std::cout << "支持的API端点:" << std::endl;
    std::cout << "  POST /api/v1/auth/login  - 用户登录" << std::endl;
    std::cout << "  GET  /api/v1/auth/me     - 获取当前用户信息" << std::endl;
    std::cout << "默认登录凭据: admin / admin123" << std::endl;
    std::cout << "按 Ctrl+C 停止服务器" << std::endl;
    std::cout << std::endl;
    
    // 主循环
    while (g_running.load()) {
        struct sockaddr_in clientAddr;
        socklen_t clientAddrLen = sizeof(clientAddr);
        
        int clientSocket = accept(serverSocket, (struct sockaddr*)&clientAddr, &clientAddrLen);
        if (clientSocket < 0) {
            if (g_running.load()) {
                std::cerr << "接受连接失败" << std::endl;
            }
            continue;
        }
        
        // 在新线程中处理客户端请求
        std::thread clientThread(handleClient, clientSocket);
        clientThread.detach();
    }
    
    std::cout << "正在关闭服务器..." << std::endl;
    close(serverSocket);
    std::cout << "HTTP API服务器已关闭" << std::endl;
    
    return 0;
}
