#include "backend/include/core/disciplining_algorithm.h"
#include <iostream>
#include <chrono>
#include <thread>

using namespace timing_server::core;

int main() {
    std::cout << "=== 时钟驯服算法简单测试 ===" << std::endl;
    
    // 测试1: 混合驯服算法基本功能
    std::cout << "\n1. 测试混合驯服算法初始化..." << std::endl;
    
    DiscipliningParameters config;
    config.convergence_threshold_ns = 50.0;
    config.convergence_time_s = 300;
    config.phase_gain = 1.0;
    config.frequency_gain = 0.1;
    config.measurement_interval_ms = 1000;
    
    HybridDiscipliningAlgorithm algorithm;
    if (algorithm.Initialize(config)) {
        std::cout << "✓ 驯服算法初始化成功" << std::endl;
        
        auto status = algorithm.GetStatus();
        std::cout << "  状态: " << static_cast<int>(status.state) << std::endl;
        std::cout << "  算法类型: " << static_cast<int>(status.algorithm) << std::endl;
        std::cout << "  环路带宽: " << status.loop_bandwidth_hz << " Hz" << std::endl;
        std::cout << "  时间常数: " << status.time_constant_s << " s" << std::endl;
    } else {
        std::cout << "✗ 驯服算法初始化失败" << std::endl;
        return 1;
    }
    
    // 测试2: 处理测量数据
    std::cout << "\n2. 测试测量数据处理..." << std::endl;
    
    for (int i = 0; i < 10; ++i) {
        TimeMeasurement measurement;
        measurement.timestamp_ns = GetCurrentTimestampNs();
        measurement.phase_offset_ns = 1000.0 * std::exp(-0.1 * i); // 指数收敛
        measurement.frequency_offset_ppm = 1.0 * std::exp(-0.1 * i);
        measurement.measurement_noise_ns = 10.0;
        measurement.source = TimeSource::GNSS;
        measurement.is_valid = true;
        
        if (algorithm.ProcessMeasurement(measurement)) {
            auto status = algorithm.GetStatus();
            std::cout << "  测量 " << i+1 << ": 相位误差=" << status.current_phase_error_ns 
                      << "ns, 频率误差=" << status.current_frequency_error_ppm << "ppm" << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 测试3: 铷钟学习算法
    std::cout << "\n3. 测试铷钟学习算法..." << std::endl;
    
    HoldoverParameters holdover_config;
    holdover_config.max_holdover_hours = 24;
    holdover_config.frequency_drift_limit_ppm = 1.0;
    holdover_config.learning_duration_hours = 72;
    holdover_config.enable_temperature_compensation = true;
    
    RubidiumLearningAlgorithm learning;
    if (learning.Initialize(holdover_config)) {
        std::cout << "✓ 学习算法初始化成功" << std::endl;
        
        // 添加一些学习样本
        uint64_t base_time = GetCurrentTimestampNs();
        for (int i = 0; i < 50; ++i) {
            double frequency_offset = 0.001 * i / 10.0; // 模拟老化
            double temperature = 25.0 + 5.0 * std::sin(i * 0.1); // 温度变化
            uint64_t timestamp = base_time + i * 3600ULL * 1000000000ULL; // 每小时
            
            learning.AddSample(frequency_offset, temperature, timestamp);
        }
        
        auto learning_data = learning.GetLearningData();
        std::cout << "  样本数量: " << learning_data.sample_count << std::endl;
        std::cout << "  学习时长: " << learning_data.learning_duration_hours << " 小时" << std::endl;
        std::cout << "  老化率: " << learning_data.aging_rate_ppm_per_day << " ppm/天" << std::endl;
        std::cout << "  温度系数: " << learning_data.temperature_coefficient << " ppm/°C" << std::endl;
        
        // 测试预测
        double predicted = learning.PredictFrequencyOffset(30.0, 48.0);
        std::cout << "  预测偏移(30°C, 48小时): " << predicted << " ppm" << std::endl;
    } else {
        std::cout << "✗ 学习算法初始化失败" << std::endl;
    }
    
    // 测试4: 守时预测算法
    std::cout << "\n4. 测试守时预测算法..." << std::endl;
    
    RubidiumLearningData mock_learning_data;
    mock_learning_data.aging_rate_ppm_per_day = 0.001;
    mock_learning_data.temperature_coefficient = 0.0001;
    mock_learning_data.temperature_reference = 25.0;
    mock_learning_data.confidence_level = 0.9;
    
    HoldoverPredictionAlgorithm prediction;
    if (prediction.Initialize(mock_learning_data, holdover_config)) {
        std::cout << "✓ 预测算法初始化成功" << std::endl;
        
        if (prediction.StartHoldover(0.1, 25.0)) {
            std::cout << "✓ 守时预测已启动" << std::endl;
            
            // 模拟时间流逝
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            
            // 更新温度
            prediction.UpdateTemperature(30.0);
            
            auto pred_data = prediction.GetCurrentPrediction();
            std::cout << "  预测频率漂移: " << pred_data.predicted_frequency_drift_ppm << " ppm" << std::endl;
            std::cout << "  预测相位漂移: " << pred_data.predicted_phase_drift_ns << " ns" << std::endl;
            std::cout << "  温度补偿: " << pred_data.temperature_compensation_ppm << " ppm" << std::endl;
            
            double freq_correction = prediction.GetFrequencyCorrection();
            double phase_correction = prediction.GetPhaseCorrection();
            std::cout << "  频率校正: " << freq_correction << " ppm" << std::endl;
            std::cout << "  相位校正: " << phase_correction << " ns" << std::endl;
            
            prediction.StopHoldover();
        }
    } else {
        std::cout << "✗ 预测算法初始化失败" << std::endl;
    }
    
    // 测试5: 驯服管理器集成测试
    std::cout << "\n5. 测试驯服管理器..." << std::endl;
    
    TimingConfig timing_config;
    timing_config.discipline = config;
    timing_config.holdover = holdover_config;
    
    ClockDiscipliningManager manager;
    if (manager.Initialize(timing_config)) {
        std::cout << "✓ 驯服管理器初始化成功" << std::endl;
        
        // 启动GNSS驯服
        if (manager.StartGnssDisciplining()) {
            std::cout << "✓ GNSS驯服已启动" << std::endl;
            
            // 处理一些测量数据
            for (int i = 0; i < 5; ++i) {
                TimeMeasurement measurement;
                measurement.timestamp_ns = GetCurrentTimestampNs();
                measurement.phase_offset_ns = 200.0 * std::exp(-0.2 * i);
                measurement.frequency_offset_ppm = 0.5 * std::exp(-0.2 * i);
                measurement.measurement_noise_ns = 5.0;
                measurement.source = TimeSource::GNSS;
                measurement.is_valid = true;
                
                manager.ProcessTimeMeasurement(measurement);
                manager.UpdateRubidiumTemperature(25.0 + i);
                
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            
            auto status = manager.GetDiscipliningStatus();
            std::cout << "  驯服状态: " << static_cast<int>(status.state) << std::endl;
            std::cout << "  是否收敛: " << (status.is_converged ? "是" : "否") << std::endl;
            
            double freq_correction = manager.GetFrequencyCorrection();
            double phase_correction = manager.GetPhaseCorrection();
            std::cout << "  频率校正: " << freq_correction << " ppm" << std::endl;
            std::cout << "  相位校正: " << phase_correction << " ns" << std::endl;
            
            // 测试切换到守时模式
            if (manager.StartHoldover()) {
                std::cout << "✓ 守时模式已启动" << std::endl;
                
                auto holdover_pred = manager.GetHoldoverPrediction();
                std::cout << "  守时预测频率漂移: " << holdover_pred.predicted_frequency_drift_ppm << " ppm" << std::endl;
                
                manager.StopHoldover();
            }
            
            manager.StopGnssDisciplining();
        }
    } else {
        std::cout << "✗ 驯服管理器初始化失败" << std::endl;
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    return 0;
}