#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <mutex>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <signal.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <unistd.h>
#include <cstring>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <openssl/bio.h>
#include <openssl/buffer.h>

std::atomic<bool> g_running{true};

void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在关闭服务器..." << std::endl;
    g_running = false;
}

std::string base64_encode(const std::string& input) {
    BIO *bio, *b64;
    BUF_MEM *bufferPtr;

    b64 = BIO_new(BIO_f_base64());
    bio = BIO_new(BIO_s_mem());
    bio = BIO_push(b64, bio);

    BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL);
    BIO_write(bio, input.c_str(), input.length());
    BIO_flush(bio);
    BIO_get_mem_ptr(bio, &bufferPtr);

    std::string result(bufferPtr->data, bufferPtr->length);
    BIO_free_all(bio);

    return result;
}

std::string generateWebSocketAccept(const std::string& key) {
    std::string magic = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11";
    std::string combined = key + magic;
    
    unsigned char hash[SHA_DIGEST_LENGTH];
    SHA1(reinterpret_cast<const unsigned char*>(combined.c_str()), combined.length(), hash);
    
    std::string hashStr(reinterpret_cast<char*>(hash), SHA_DIGEST_LENGTH);
    return base64_encode(hashStr);
}

bool performWebSocketHandshake(int clientSocket) {
    char buffer[4096];
    int bytesRead = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
    if (bytesRead <= 0) {
        return false;
    }
    
    buffer[bytesRead] = '\0';
    std::string request(buffer);
    
    // 查找WebSocket Key
    size_t keyPos = request.find("Sec-WebSocket-Key: ");
    if (keyPos == std::string::npos) {
        return false;
    }
    
    keyPos += 19; // "Sec-WebSocket-Key: " 的长度
    size_t keyEnd = request.find("\r\n", keyPos);
    if (keyEnd == std::string::npos) {
        return false;
    }
    
    std::string key = request.substr(keyPos, keyEnd - keyPos);
    std::string accept = generateWebSocketAccept(key);
    
    // 发送WebSocket握手响应
    std::string response = 
        "HTTP/1.1 101 Switching Protocols\r\n"
        "Upgrade: websocket\r\n"
        "Connection: Upgrade\r\n"
        "Sec-WebSocket-Accept: " + accept + "\r\n"
        "Access-Control-Allow-Origin: *\r\n"
        "\r\n";
    
    send(clientSocket, response.c_str(), response.length(), 0);
    return true;
}

void sendWebSocketFrame(int clientSocket, const std::string& message) {
    std::vector<uint8_t> frame;
    
    // FIN=1, RSV=000, Opcode=0001 (text frame)
    frame.push_back(0x81);
    
    size_t messageLen = message.length();
    if (messageLen < 126) {
        frame.push_back(static_cast<uint8_t>(messageLen));
    } else if (messageLen < 65536) {
        frame.push_back(126);
        frame.push_back(static_cast<uint8_t>((messageLen >> 8) & 0xFF));
        frame.push_back(static_cast<uint8_t>(messageLen & 0xFF));
    } else {
        frame.push_back(127);
        for (int i = 7; i >= 0; i--) {
            frame.push_back(static_cast<uint8_t>((messageLen >> (i * 8)) & 0xFF));
        }
    }
    
    // 添加消息内容
    for (char c : message) {
        frame.push_back(static_cast<uint8_t>(c));
    }
    
    send(clientSocket, frame.data(), frame.size(), 0);
}

std::string getCurrentTimeJson() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << "{"
       << "\"type\":\"status_update\","
       << "\"timestamp\":\"" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") 
       << "." << std::setfill('0') << std::setw(3) << ms.count() << "\","
       << "\"system_status\":\"normal\","
       << "\"sync_status\":\"synchronized\","
       << "\"current_source\":\"GPS\","
       << "\"precision\":\"±50ns\","
       << "\"connections\":" << 1
       << "}";
    return ss.str();
}

void handleClient(int clientSocket) {
    std::cout << "新客户端连接，执行WebSocket握手..." << std::endl;
    
    if (!performWebSocketHandshake(clientSocket)) {
        std::cout << "WebSocket握手失败" << std::endl;
        close(clientSocket);
        return;
    }
    
    std::cout << "WebSocket握手成功，开始发送数据..." << std::endl;
    
    // 发送欢迎消息
    std::string welcomeMsg = R"({"type":"welcome","message":"连接到高精度授时服务器WebSocket服务"})";
    sendWebSocketFrame(clientSocket, welcomeMsg);
    
    // 定期发送状态更新
    while (g_running) {
        std::string statusMsg = getCurrentTimeJson();
        sendWebSocketFrame(clientSocket, statusMsg);
        
        // 每秒发送一次状态更新
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 检查客户端是否还连接着
        char testByte;
        int result = recv(clientSocket, &testByte, 1, MSG_PEEK | MSG_DONTWAIT);
        if (result == 0) {
            std::cout << "客户端断开连接" << std::endl;
            break;
        }
    }
    
    close(clientSocket);
    std::cout << "客户端连接已关闭" << std::endl;
}

int main() {
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    std::cout << "=== 简单WebSocket服务器 ===" << std::endl;
    std::cout << "为高精度授时服务器系统提供WebSocket服务" << std::endl;
    
    const int port = 8081;
    
    // 创建socket
    int serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (serverSocket == -1) {
        std::cerr << "创建socket失败" << std::endl;
        return 1;
    }
    
    // 设置socket选项
    int opt = 1;
    if (setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        std::cerr << "设置socket选项失败" << std::endl;
        close(serverSocket);
        return 1;
    }
    
    // 绑定地址
    struct sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(port);
    
    if (bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) < 0) {
        std::cerr << "绑定端口失败" << std::endl;
        close(serverSocket);
        return 1;
    }
    
    // 开始监听
    if (listen(serverSocket, 10) < 0) {
        std::cerr << "监听失败" << std::endl;
        close(serverSocket);
        return 1;
    }
    
    std::cout << "WebSocket服务器启动成功，监听端口: " << port << std::endl;
    std::cout << "等待客户端连接..." << std::endl;
    
    while (g_running) {
        struct sockaddr_in clientAddr;
        socklen_t clientLen = sizeof(clientAddr);
        
        int clientSocket = accept(serverSocket, (struct sockaddr*)&clientAddr, &clientLen);
        if (clientSocket < 0) {
            if (g_running) {
                std::cerr << "接受连接失败" << std::endl;
            }
            continue;
        }
        
        // 为每个客户端创建新线程
        std::thread clientThread(handleClient, clientSocket);
        clientThread.detach();
    }
    
    close(serverSocket);
    std::cout << "WebSocket服务器已关闭" << std::endl;
    return 0;
}
