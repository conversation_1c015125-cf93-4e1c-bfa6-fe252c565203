#include "backend/include/core/timing_engine.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace timing_server::core;

int main() {
    std::cout << "测试授时引擎..." << std::endl;
    
    try {
        // 创建默认配置
        TimingConfig config;
        config.priorities.priorities[TimeSource::GNSS] = 1;
        config.priorities.priorities[TimeSource::RUBIDIUM] = 2;
        config.priorities.priorities[TimeSource::RTC] = 5;
        config.priorities.auto_failover = true;
        config.priorities.failover_delay_ms = 1000;
        
        // 创建授时引擎
        TimingEngine engine(config);
        
        std::cout << "✓ 授时引擎创建成功" << std::endl;
        std::cout << "初始状态: " << ClockStateToString(engine.GetCurrentState()) << std::endl;
        std::cout << "活跃时间源: " << TimeSourceToString(engine.GetActiveTimeSource()) << std::endl;
        
        // 启动引擎
        if (engine.Start()) {
            std::cout << "✓ 授时引擎启动成功" << std::endl;
            
            // 运行一段时间
            std::this_thread::sleep_for(std::chrono::seconds(2));
            
            // 获取系统状态
            SystemStatus status = engine.GetSystemStatus();
            std::cout << "系统状态:" << std::endl;
            std::cout << "  当前状态: " << ClockStateToString(status.current_state) << std::endl;
            std::cout << "  活跃时间源: " << TimeSourceToString(status.active_source) << std::endl;
            std::cout << "  运行时间: " << status.uptime_seconds << " 秒" << std::endl;
            std::cout << "  系统健康: " << SystemHealthToString(status.health) << std::endl;
            std::cout << "  平台: " << status.platform << std::endl;
            std::cout << "  版本: " << status.version << std::endl;
            
            // 获取时间源信息
            auto sources = engine.GetAllTimeSourceInfo();
            std::cout << "时间源信息 (" << sources.size() << " 个):" << std::endl;
            for (const auto& source : sources) {
                std::cout << "  " << TimeSourceToString(source.type) 
                          << ": " << TimeSourceStatusToString(source.status)
                          << " (优先级: " << source.priority << ")" << std::endl;
            }
            
            // 停止引擎
            if (engine.Stop()) {
                std::cout << "✓ 授时引擎停止成功" << std::endl;
            } else {
                std::cout << "✗ 授时引擎停止失败" << std::endl;
            }
            
        } else {
            std::cout << "✗ 授时引擎启动失败" << std::endl;
            return 1;
        }
        
        std::cout << "✓ 授时引擎测试完成" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "✗ 测试异常: " << e.what() << std::endl;
        return 1;
    }
}