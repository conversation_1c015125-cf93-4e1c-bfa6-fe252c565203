# CMake generated Testfile for 
# Source directory: /Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests
# Build directory: /Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(timing-server-unit-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/timing-server-tests")
set_tests_properties(timing-server-unit-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;164;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(timing-server-hal-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/timing-server-hal-tests")
set_tests_properties(timing-server-hal-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;165;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(api-integration-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/test_api_integration")
set_tests_properties(api-integration-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;166;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(websocket-integration-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/test_websocket_integration")
set_tests_properties(websocket-integration-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;167;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(end-to-end-integration-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/test_end_to_end_integration")
set_tests_properties(end-to-end-integration-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;168;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(system-performance-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/test_system_performance")
set_tests_properties(system-performance-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;169;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(web-interface-functionality-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/test_web_interface_functionality")
set_tests_properties(web-interface-functionality-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;170;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(long-term-stability-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/test_long_term_stability")
set_tests_properties(long-term-stability-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;171;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
add_test(platform-compatibility-tests "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/build/backend/tests/test_platform_compatibility")
set_tests_properties(platform-compatibility-tests PROPERTIES  _BACKTRACE_TRIPLES "/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;172;add_test;/Users/<USER>/Downloads/test/Loongarch64-Linux-Macos/backend/tests/CMakeLists.txt;0;")
