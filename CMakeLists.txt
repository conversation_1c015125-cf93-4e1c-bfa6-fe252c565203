cmake_minimum_required(VERSION 3.16)
project(timing-server-system VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 编译选项
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# 平台检测和HAL选择
message(STATUS "检测到系统: ${CMAKE_SYSTEM_NAME}")
message(STATUS "检测到处理器: ${CMAKE_SYSTEM_PROCESSOR}")

if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "loongarch64")
        set(PLATFORM_NAME "loongarch64-linux")
        set(USE_LOONGARCH_HAL ON)
        message(STATUS "配置龙芯LoongArch64平台构建")
    else()
        set(PLATFORM_NAME "x86_64-linux")
        set(USE_LINUX_HAL ON)
        message(STATUS "配置Linux x86_64平台构建")
    endif()
elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
    set(PLATFORM_NAME "x86_64-darwin")
    set(USE_MOCK_HAL ON)
    message(STATUS "配置macOS开发环境构建")
else()
    message(FATAL_ERROR "不支持的平台: ${CMAKE_SYSTEM_NAME}")
endif()

# 定义平台宏
add_compile_definitions(PLATFORM_NAME="${PLATFORM_NAME}")

# 包含目录
include_directories(backend/include)

# 查找依赖库
find_package(Threads REQUIRED)

# 查找SQLite3库
find_package(PkgConfig REQUIRED)
pkg_check_modules(SQLITE3 REQUIRED sqlite3)

# 查找libcurl库（用于Webhook通知）
find_package(CURL QUIET)
if(NOT CURL_FOUND)
    message(STATUS "libcurl未找到，Webhook通知功能将被禁用")
    set(USE_CURL_PLACEHOLDER ON)
else()
    message(STATUS "找到libcurl库: ${CURL_VERSION_STRING}")
    set(USE_CURL_PLACEHOLDER OFF)
endif()

# 查找jsoncpp库（用于JSON处理）
find_package(PkgConfig REQUIRED)
pkg_check_modules(JSONCPP jsoncpp)
if(NOT JSONCPP_FOUND)
    message(STATUS "jsoncpp未找到，将使用内置JSON实现")
    set(USE_JSONCPP_PLACEHOLDER ON)
else()
    message(STATUS "找到jsoncpp库")
    message(STATUS "JSONCPP_LIBRARIES: ${JSONCPP_LIBRARIES}")
    message(STATUS "JSONCPP_LIBRARY_DIRS: ${JSONCPP_LIBRARY_DIRS}")
    message(STATUS "JSONCPP_INCLUDE_DIRS: ${JSONCPP_INCLUDE_DIRS}")
    set(USE_JSONCPP_PLACEHOLDER OFF)
endif()

# 查找oatpp库（用于REST API）
find_package(oatpp QUIET)
if(NOT oatpp_FOUND)
    message(STATUS "oatpp未找到，将使用占位实现")
    set(USE_OATPP_PLACEHOLDER ON)
else()
    message(STATUS "找到oatpp库: ${oatpp_VERSION}")
    set(USE_OATPP_PLACEHOLDER OFF)
endif()

# 平台特定的依赖
if(USE_LINUX_HAL OR USE_LOONGARCH_HAL)
    # Linux平台依赖
    find_library(RT_LIB rt)
    if(NOT RT_LIB)
        message(FATAL_ERROR "未找到librt库")
    endif()
endif()

# 创建核心库
add_library(timing-core STATIC
    backend/src/core/clock_state_machine.cpp
    backend/src/core/signal_quality_evaluator.cpp
    backend/src/core/timing_engine.cpp
    backend/src/core/config_manager.cpp
    backend/src/core/serialization.cpp
    backend/src/core/disciplining_algorithm.cpp
    backend/src/core/daemon_manager.cpp
    backend/src/core/database_manager.cpp
    backend/src/core/logger.cpp
    backend/src/core/error_handler.cpp
    backend/src/core/rubidium_learning.cpp
    backend/src/core/precision_monitor.cpp
    backend/src/core/system_resource_monitor.cpp
    backend/src/core/alarm_system.cpp
    backend/src/core/notification_system.cpp
    backend/src/core/performance_metrics.cpp
    backend/src/core/json_fallback.cpp
    backend/src/core/platform_validator.cpp
)

target_include_directories(timing-core PUBLIC backend/include)
target_include_directories(timing-core PRIVATE ${SQLITE3_INCLUDE_DIRS})

# 链接基础库
target_link_libraries(timing-core Threads::Threads ${SQLITE3_LIBRARIES})

# 链接libcurl（如果可用）
if(NOT USE_CURL_PLACEHOLDER)
    target_link_libraries(timing-core ${CURL_LIBRARIES})
    target_include_directories(timing-core PRIVATE ${CURL_INCLUDE_DIRS})
    target_compile_definitions(timing-core PRIVATE USE_CURL=1)
else()
    target_compile_definitions(timing-core PRIVATE USE_CURL_PLACEHOLDER=1)
endif()

# 链接jsoncpp（如果可用）
if(NOT USE_JSONCPP_PLACEHOLDER)
    find_library(JSONCPP_LIB jsoncpp PATHS ${JSONCPP_LIBRARY_DIRS})
    target_link_libraries(timing-core ${JSONCPP_LIB})
    target_include_directories(timing-core PRIVATE ${JSONCPP_INCLUDE_DIRS})
    target_compile_definitions(timing-core PRIVATE USE_JSONCPP=1)
else()
    target_compile_definitions(timing-core PRIVATE USE_JSONCPP_PLACEHOLDER=1)
endif()

# 创建HAL库 - 包含通用组件和平台特定实现
set(HAL_COMMON_SOURCES
    backend/src/hal/platform_detector.cpp
    backend/src/hal/hal_factory.cpp
)

if(USE_LINUX_HAL)
    add_library(timing-hal STATIC
        ${HAL_COMMON_SOURCES}
        backend/src/hal/linux/linux_hal_factory.cpp
        backend/src/hal/linux/linux_gnss_receiver.cpp
        backend/src/hal/linux/linux_pps_input.cpp
        backend/src/hal/linux/linux_atomic_clock.cpp
        backend/src/hal/linux/linux_frequency_input.cpp
        backend/src/hal/linux/linux_rtc.cpp
        backend/src/hal/linux/linux_network_interface.cpp
    )
    target_link_libraries(timing-hal ${RT_LIB})
elseif(USE_LOONGARCH_HAL)
    add_library(timing-hal STATIC
        ${HAL_COMMON_SOURCES}
        backend/src/hal/linux/linux_hal_factory.cpp
        backend/src/hal/linux/linux_gnss_receiver.cpp
        backend/src/hal/linux/linux_pps_input.cpp
        backend/src/hal/linux/linux_atomic_clock.cpp
        backend/src/hal/linux/linux_frequency_input.cpp
        backend/src/hal/linux/linux_rtc.cpp
        backend/src/hal/linux/linux_network_interface.cpp
    )
    target_link_libraries(timing-hal ${RT_LIB})
    # 注意：龙芯平台目前使用Linux HAL实现，后续可以添加特定优化
elseif(USE_MOCK_HAL)
    add_library(timing-hal STATIC
        ${HAL_COMMON_SOURCES}
        backend/src/hal/mock/mock_hal_factory.cpp
        backend/src/hal/mock/mock_gnss_receiver.cpp
        backend/src/hal/mock/mock_pps_input.cpp
        backend/src/hal/mock/mock_atomic_clock.cpp
        backend/src/hal/mock/mock_frequency_input.cpp
        backend/src/hal/mock/mock_rtc.cpp
        backend/src/hal/mock/mock_network_interface.cpp
    )
endif()

target_include_directories(timing-hal PUBLIC backend/include)

# 查找OpenSSL库（WebSocket需要）
find_package(OpenSSL REQUIRED)

# 查找nlohmann/json库
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann/json未找到，将使用内置实现")
    set(USE_BUILTIN_JSON ON)
else()
    message(STATUS "找到nlohmann/json库")
    set(USE_BUILTIN_JSON OFF)
endif()

# 创建API库
if(USE_OATPP_PLACEHOLDER)
    # 使用占位实现（当oatpp不可用时）
    add_library(timing-api STATIC
        backend/src/api/rest_server.cpp
        backend/src/api/timing_service_impl.cpp
        backend/src/api/websocket_server.cpp
        backend/src/api/websocket_event_listener.cpp
        backend/src/api/auth_manager.cpp
        backend/src/api/auth_controller.cpp
        backend/src/api/security_manager.cpp
        backend/src/api/security_tls.cpp
    )
    target_compile_definitions(timing-api PRIVATE USE_OATPP_PLACEHOLDER=1)
else()
    # 使用完整的oatpp实现
    add_library(timing-api STATIC
        backend/src/api/rest_server.cpp
        backend/src/api/timing_controller.cpp
        backend/src/api/timing_service_impl.cpp
        backend/src/api/websocket_server.cpp
        backend/src/api/websocket_event_listener.cpp
        backend/src/api/auth_manager.cpp
        backend/src/api/auth_controller.cpp
        backend/src/api/security_manager.cpp
        backend/src/api/security_tls.cpp
    )
    target_link_libraries(timing-api oatpp::oatpp)
endif()

# 链接WebSocket相关依赖
target_link_libraries(timing-api OpenSSL::SSL OpenSSL::Crypto)

if(NOT USE_BUILTIN_JSON)
    target_link_libraries(timing-api nlohmann_json::nlohmann_json)
endif()

target_include_directories(timing-api PUBLIC backend/include)
target_link_libraries(timing-api timing-core)

# 主程序
add_executable(timing-server
    backend/src/main.cpp
)

target_link_libraries(timing-server 
    timing-core 
    timing-api 
    timing-hal
    Threads::Threads
)

# HAL演示程序
add_executable(hal-demo
    backend/src/hal/hal_demo.cpp
)

target_link_libraries(hal-demo
    timing-hal
    Threads::Threads
)

# REST API示例程序
add_executable(rest-api-example
    backend/examples/rest_api_example.cpp
)

target_link_libraries(rest-api-example
    timing-api
    timing-core
    Threads::Threads
)

# WebSocket服务器示例程序
add_executable(websocket-server-example
    backend/examples/websocket_server_example.cpp
)

target_link_libraries(websocket-server-example
    timing-api
    timing-core
    timing-hal
    Threads::Threads
)

# 认证系统示例程序
add_executable(auth-example
    backend/examples/auth_example.cpp
)

target_link_libraries(auth-example
    timing-api
    timing-core
    Threads::Threads
)

# 守护进程管理器示例程序
add_executable(daemon-manager-example
    backend/examples/daemon_manager_example.cpp
)

target_link_libraries(daemon-manager-example
    timing-core
    Threads::Threads
)

# 铷钟学习示例程序
add_executable(rubidium-learning-example
    backend/examples/rubidium_learning_example.cpp
)

target_link_libraries(rubidium-learning-example
    timing-core
    timing-hal
    Threads::Threads
)

# 安全集成示例程序
add_executable(security-integration-example
    backend/examples/security_integration_example.cpp
)

target_link_libraries(security-integration-example
    timing-api
    timing-core
    Threads::Threads
)

# 平台基准测试工具
add_executable(platform-benchmark
    backend/src/core/platform_benchmark.cpp
)

target_link_libraries(platform-benchmark
    timing-core
    timing-hal
    timing-api
    Threads::Threads
)

# 安装规则
install(TARGETS timing-server platform-benchmark
    RUNTIME DESTINATION bin
)

# 安装配置文件
install(DIRECTORY platform/config/
    DESTINATION etc/timing-server
    FILES_MATCHING PATTERN "*.json" PATTERN "*.conf"
)

# 安装systemd服务文件
install(FILES platform/systemd/timing-server.service
    DESTINATION lib/systemd/system
    PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ
)

# 安装脚本文件
install(PROGRAMS 
    platform/install.sh
    platform/uninstall.sh
    platform/timing-server-wrapper.sh
    platform/system-monitor.sh
    DESTINATION share/timing-server/scripts
)

# 创建符号链接到系统路径（在安装后脚本中处理）
install(CODE "
    message(STATUS \"创建脚本符号链接...\")
    execute_process(COMMAND \${CMAKE_COMMAND} -E create_symlink
        \${CMAKE_INSTALL_PREFIX}/share/timing-server/scripts/timing-server-wrapper.sh
        \${CMAKE_INSTALL_PREFIX}/bin/timing-server-wrapper
    )
    execute_process(COMMAND \${CMAKE_COMMAND} -E create_symlink
        \${CMAKE_INSTALL_PREFIX}/share/timing-server/scripts/system-monitor.sh
        \${CMAKE_INSTALL_PREFIX}/bin/timing-server-monitor
    )
")

# 安装Web界面文件（如果存在）
if(EXISTS \${CMAKE_SOURCE_DIR}/frontend/dist)
    install(DIRECTORY frontend/dist/
        DESTINATION share/timing-server/web
        FILES_MATCHING 
        PATTERN "*.html"
        PATTERN "*.js"
        PATTERN "*.css"
        PATTERN "*.ico"
        PATTERN "*.png"
        PATTERN "*.svg"
    )
endif()

# 创建运行时目录结构
install(DIRECTORY DESTINATION var/lib/timing-server)
install(DIRECTORY DESTINATION var/log/timing-server)
install(DIRECTORY DESTINATION etc/timing-server/ssl)

# 测试支持
enable_testing()
add_subdirectory(backend/tests)

# 打包配置
set(CPACK_PACKAGE_NAME "timing-server-system")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "高精度授时服务器系统")
set(CPACK_PACKAGE_VENDOR "Timing Server Team")

if(USE_LOONGARCH_HAL)
    set(CPACK_SYSTEM_NAME "loongarch64-linux")
elseif(USE_LINUX_HAL)
    set(CPACK_SYSTEM_NAME "x86_64-linux")
else()
    set(CPACK_SYSTEM_NAME "x86_64-darwin")
endif()

include(CPack)