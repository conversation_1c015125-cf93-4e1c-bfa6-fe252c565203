#include "backend/include/core/clock_state_machine.h"
#include <iostream>

using namespace timing_server::core;

int main() {
    std::cout << "=== 调试状态机转换问题 ===" << std::endl;
    
    auto state_machine = std::make_unique<ClockStateMachine>(ClockState::FREE_RUN);
    state_machine->Initialize();
    state_machine->Start();
    
    std::cout << "初始状态: " << ClockStateToString(state_machine->GetCurrentState()) << std::endl;
    
    // 测试转换有效性检查
    bool valid = state_machine->IsTransitionValid(
        ClockState::FREE_RUN, 
        ClockState::DISCIPLINING, 
        ClockEvent::GNSS_SIGNAL_ACQUIRED
    );
    
    std::cout << "转换有效性检查 (FREE_RUN -> DISCIPLINING, GNSS_SIGNAL_ACQUIRED): " 
              << (valid ? "有效" : "无效") << std::endl;
    
    // 创建事件
    StateMachineEvent event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "测试事件");
    event.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n";
    
    std::cout << "事件类型: " << ClockEventToString(event.event) << std::endl;
    std::cout << "事件描述: " << event.description << std::endl;
    
    // 处理事件
    std::cout << "处理事件..." << std::endl;
    TransitionResult result = state_machine->ProcessEvent(event);
    
    std::cout << "转换结果: " << TransitionResultToString(result) << std::endl;
    std::cout << "最终状态: " << ClockStateToString(state_machine->GetCurrentState()) << std::endl;
    
    return 0;
}