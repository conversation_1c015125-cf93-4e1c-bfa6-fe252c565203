#!/usr/bin/env node

const WebSocket = require('ws');

console.log('=== WebSocket连接测试 ===');

// 连接到WebSocket服务器
const ws = new WebSocket('ws://localhost:8081');

let messageCount = 0;
let startTime = Date.now();

ws.on('open', function open() {
    console.log('✅ WebSocket连接已建立');
    console.log('监听服务器消息...');
    
    // 发送一个测试消息
    ws.send(JSON.stringify({
        type: 'ping',
        timestamp: Date.now()
    }));
    console.log('📤 发送ping消息');
});

ws.on('message', function message(data) {
    messageCount++;
    const message = data.toString();
    
    try {
        const parsed = JSON.parse(message);
        console.log(`📥 收到消息 #${messageCount}:`, {
            type: parsed.type || 'unknown',
            timestamp: parsed.timestamp || 'none',
            size: message.length + ' bytes'
        });
        
        // 显示部分消息内容（避免输出过长）
        if (message.length < 200) {
            console.log('   内容:', parsed);
        } else {
            console.log('   内容: [消息过长，已省略]');
        }
    } catch (e) {
        console.log(`📥 收到非JSON消息 #${messageCount}:`, message.substring(0, 100));
    }
});

ws.on('error', function error(err) {
    console.error('❌ WebSocket错误:', err.message);
});

ws.on('close', function close(code, reason) {
    const duration = (Date.now() - startTime) / 1000;
    console.log(`🔌 WebSocket连接已关闭`);
    console.log(`   关闭代码: ${code}`);
    console.log(`   关闭原因: ${reason || '无'}`);
    console.log(`   连接时长: ${duration.toFixed(2)}秒`);
    console.log(`   收到消息数: ${messageCount}`);
});

// 10秒后自动关闭连接
setTimeout(() => {
    console.log('⏰ 测试时间到，关闭连接...');
    ws.close();
}, 10000);

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n🛑 收到中断信号，关闭连接...');
    ws.close();
    process.exit(0);
});
