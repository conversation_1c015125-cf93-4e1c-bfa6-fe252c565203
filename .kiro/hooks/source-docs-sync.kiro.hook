{"enabled": true, "name": "Source to Docs Sync", "description": "Monitors all C++ source files (.cpp, .h), Vue.js frontend files (.vue, .ts), CMake files, and configuration files for changes, then updates documentation in README.md and /docs folder to reflect code changes", "version": "1", "when": {"type": "fileEdited", "patterns": ["backend/src/**/*.cpp", "backend/include/**/*.h", "backend/examples/*.cpp", "backend/tests/*.cpp", "frontend/src/**/*.vue", "frontend/src/**/*.ts", "CMakeLists.txt", "backend/tests/CMakeLists.txt", "platform/config/*.json", "*.cpp", "*.h"]}, "then": {"type": "askAgent", "prompt": "Source code files have been modified in this timing server project. Please analyze the changes and update the documentation accordingly. Focus on:\n\n1. Update README.md if there are significant architectural changes, new features, or build process modifications\n2. Update relevant files in the /docs folder based on the specific area of changes:\n   - For HAL changes: update docs/hal-architecture.md or docs/mock-hal-implementation.md\n   - For daemon/core changes: update docs/daemon_manager_optimizations.md\n   - For requirements/design changes: update docs/requirements_design_updates.md\n3. Ensure documentation reflects current API endpoints, configuration options, and system capabilities\n4. Update any code examples or usage instructions that may have changed\n5. Keep documentation consistent with the C++17/Vue.js 3 technology stack and multi-platform support (Linux x86_64, LoongArch64, macOS)\n\nPlease review the modified files and provide updated documentation that accurately reflects the current state of the codebase."}}