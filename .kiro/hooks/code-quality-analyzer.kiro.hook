{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and provides automated code quality analysis, including suggestions for improvements in readability, maintainability, and performance while maintaining existing functionality", "version": "1", "when": {"type": "fileEdited", "patterns": ["backend/src/**/*.cpp", "backend/include/**/*.h", "frontend/src/**/*.vue", "frontend/src/**/*.ts", "*.cpp", "*.h"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify long methods, large classes, duplicate code, complex conditionals, and other maintainability issues\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check for C++17/TypeScript best practices, proper error handling, resource management (RAII), and naming conventions\n4. **Performance**: Identify potential performance bottlenecks, memory leaks, inefficient algorithms, or unnecessary allocations\n5. **Readability**: Suggest improvements for code clarity, documentation, and structure\n6. **Architecture Compliance**: Ensure changes align with the HAL pattern, state machine design, and overall system architecture\n\nFor each suggestion, provide:\n- Clear explanation of the issue\n- Specific code improvement recommendation\n- Rationale for why the change improves quality\n- Ensure suggestions maintain existing functionality\n\nConsider the project's C++17 standard, Vue.js 3 + TypeScript frontend, and the timing server's high-precision requirements."}}