# Product Overview

## 高精度授时服务器系统 (High-Precision Timing Server System)

A comprehensive timing server system that provides high-precision time synchronization services, integrating multiple time sources including GNSS, rubidium atomic clocks, high-precision RTC, and external 1PPS/10MHz signals.

### Key Features

- **High-precision time synchronization**: ±50ns absolute time accuracy (GNSS locked) and ±1μs holdover accuracy (24 hours)
- **Multi-platform support**: Linux x86_64, LoongArch64 deployment, macOS development environment
- **Standard protocol support**: PTP Grandmaster Clock and NTP Stratum 1 services
- **Modern web management interface**: Apple-style glassmorphism design

### Target Platforms

- **Production**: Linux x86_64, LoongArch64 (龙芯)
- **Development**: macOS (uses mock HAL implementations)

### Core Components

- **Backend**: C++ timing engine with hardware abstraction layer
- **Frontend**: Vue.js 3 + TypeScript web interface
- **HAL**: Hardware abstraction supporting multiple platforms
- **API**: REST and WebSocket interfaces for management

### Time Sources Priority

1. GNSS (GPS/BeiDou/GLONASS) - Primary reference
2. Rubidium atomic clock - High stability backup
3. External PPS - External reference input
4. External 10MHz - Frequency reference
5. High-precision RTC - Fallback timing source