# Project Structure

## Root Directory Layout

```
timing-server-system/
├── backend/                    # C++ backend service
├── frontend/                   # Vue.js frontend interface
├── build_scripts/             # Build automation scripts
├── platform/                  # Platform-specific configurations
├── docs/                      # Documentation
├── CMakeLists.txt             # Root build configuration
├── README.md                  # Project documentation
└── .gitignore                 # Git ignore rules
```

## Backend Structure (`backend/`)

### Include Directory (`backend/include/`)
- **`core/types.h`**: Core data structures, enums, and type definitions
- **`hal/interfaces.h`**: Hardware abstraction layer interfaces

### Source Directory (`backend/src/`)

#### Core Components (`backend/src/core/`)
- **`clock_state_machine.cpp`**: State machine for clock disciplining
- **`timing_engine.cpp`**: Main timing synchronization engine
- **`config_manager.cpp`**: Configuration file management

#### API Layer (`backend/src/api/`)
- **`rest_server.cpp`**: HTTP REST API server
- **`websocket_server.cpp`**: WebSocket real-time communication
- **`auth_manager.cpp`**: Authentication and authorization

#### Hardware Abstraction (`backend/src/hal/`)
- **`linux/`**: Linux-specific HAL implementations
- **`mock/`**: Mock implementations for development/testing
- **`loongarch/`**: LoongArch64-specific implementations (future)

### Tests Directory (`backend/tests/`)
- **`CMakeLists.txt`**: Test build configuration
- **`test_*.cpp`**: Unit tests for each component

## Frontend Structure (`frontend/`)

```
frontend/
├── src/
│   └── main.ts                # Application entry point
├── package.json               # Dependencies and scripts
└── [standard Vue.js structure]
```

## Build Scripts (`build_scripts/`)

- **`build.sh`**: Main build automation script
- **`validate_structure.sh`**: Project structure validation
- **`toolchains/`**: Cross-compilation toolchain configurations

## Platform Configuration (`platform/`)

### Config Templates (`platform/config/`)
- **`config.json`**: Main system configuration template

### System Integration (`platform/systemd/`)
- **`timing-server.service`**: systemd service definition

## Naming Conventions

### C++ Code
- **Namespaces**: `timing_server::core`, `timing_server::hal`
- **Classes**: PascalCase with `I_` prefix for interfaces (e.g., `I_GnssReceiver`)
- **Functions**: snake_case (e.g., `get_satellite_info()`)
- **Variables**: snake_case (e.g., `current_state`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_HOLDOVER_HOURS`)
- **Enums**: PascalCase (e.g., `ClockState::LOCKED`)

### Files and Directories
- **Headers**: snake_case.h (e.g., `clock_state_machine.h`)
- **Sources**: snake_case.cpp (e.g., `timing_engine.cpp`)
- **Directories**: snake_case (e.g., `build_scripts`)

### Frontend Code
- **Components**: PascalCase (e.g., `TimingDashboard.vue`)
- **Files**: kebab-case (e.g., `timing-dashboard.vue`)
- **Variables**: camelCase (e.g., `currentState`)

## Architecture Layers

### 1. Hardware Abstraction Layer (HAL)
- **Location**: `backend/src/hal/`
- **Purpose**: Platform-specific hardware access
- **Pattern**: Factory pattern with interface-based polymorphism

### 2. Core Engine Layer
- **Location**: `backend/src/core/`
- **Purpose**: Platform-independent timing logic
- **Components**: State machine, timing engine, configuration

### 3. API Layer
- **Location**: `backend/src/api/`
- **Purpose**: External communication interfaces
- **Protocols**: REST HTTP, WebSocket, potentially PTP/NTP

### 4. Frontend Layer
- **Location**: `frontend/src/`
- **Purpose**: Web-based management interface
- **Architecture**: Vue.js SPA with component-based design

## Configuration Hierarchy

1. **Compile-time**: CMake platform detection and HAL selection
2. **Runtime**: JSON configuration files with hot-reload support
3. **Environment**: Platform-specific paths and system integration

## Testing Strategy

- **Unit Tests**: Individual component testing in `backend/tests/`
- **Integration Tests**: HAL and system-level testing
- **Mock Testing**: Development environment testing with mock HAL
- **Cross-platform**: Validation across Linux x86_64, LoongArch64, and macOS

## Build Artifacts

### Debug Build (`build-native-Debug/`)
- Unoptimized binaries with debug symbols
- Verbose logging enabled
- Development-friendly configuration

### Release Build (`build-native-Release/`)
- Optimized production binaries
- Minimal logging
- Performance-optimized configuration