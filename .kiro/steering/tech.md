# Technology Stack

## Build System

- **Primary**: CMake 3.16+ with cross-platform support
- **C++ Standard**: C++17 (required, extensions disabled)
- **Build Types**: Debug (-g -O0 -Wall -Wextra), Release (-O3 -DNDEBUG)

## Backend Technology Stack

### Core Technologies
- **Language**: C++17
- **Threading**: std::thread, POSIX threads
- **Time APIs**: clock_gettime, PTP hardware clocks
- **Platform APIs**: Linux sysfs, device drivers

### Dependencies
- **Required**: librt (Linux), pthread
- **Hardware**: SPI, UART, PPS devices
- **Network**: Raw sockets for PTP, standard sockets for API

### Architecture Patterns
- **HAL Pattern**: Hardware Abstraction Layer with factory pattern
- **State Machine**: Clock disciplining state management
- **Observer Pattern**: Event-driven time source monitoring
- **RAII**: Resource management with smart pointers

## Frontend Technology Stack

### Core Framework
- **Framework**: Vue.js 3.3+
- **Language**: TypeScript 5.0+
- **Build Tool**: Vite 4.3+
- **State Management**: Pinia 2.1+
- **Router**: Vue Router 4.2+

### UI Libraries
- **Utilities**: @vueuse/core 10.0+
- **HTTP Client**: Axios 1.4+
- **Charts**: Chart.js 4.3+ with vue-chartjs 5.2+
- **Design**: Apple-style glassmorphism, custom CSS

### Development Tools
- **Linting**: ESLint with Vue/TypeScript configs
- **Type Checking**: vue-tsc
- **Package Manager**: npm/yarn

## Platform-Specific HAL Implementations

### Linux HAL (`backend/src/hal/linux/`)
- Direct hardware access via device files
- PPS input via `/dev/pps*`
- GNSS via serial `/dev/ttyS*`
- Atomic clock via SPI `/dev/spidev*`
- Network PHC via ethtool APIs

### Mock HAL (`backend/src/hal/mock/`)
- Simulated hardware for development
- Used on macOS and testing environments
- Provides realistic timing behavior without hardware

### LoongArch HAL
- Platform-specific optimizations for 龙芯 processors
- Similar interface to Linux HAL with architecture-specific code

## Common Build Commands

### Native Build (Linux/macOS)
```bash
./build_scripts/build.sh native Release
./build_scripts/build.sh native Debug
```

### Cross-compilation (LoongArch64)
```bash
./build_scripts/build.sh loongarch64 Release
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev          # Development server
npm run build        # Production build
npm run type-check   # TypeScript checking
npm run lint         # Code linting
```

### Testing
```bash
# Backend tests (after build)
cd build-native-Debug
make test

# Frontend tests
cd frontend
npm run test
```

### Manual CMake Build
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

## Configuration Management

- **Format**: JSON configuration files
- **Location**: `/etc/timing-server/config.json` (production)
- **Development**: `platform/config/config.json`
- **Validation**: Schema-based validation in ConfigManager
- **Hot Reload**: Configuration changes detected at runtime