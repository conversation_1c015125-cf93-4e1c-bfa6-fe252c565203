# 实施计划

- [x] 1. 建立项目结构和核心接口
  - 创建完整的Monorepo项目结构，包含backend、frontend、build_scripts、platform等目录
  - 定义所有HAL抽象接口和核心数据结构，使用简体中文注释
  - 设置CMake跨平台构建系统，支持Linux x86_64、龙芯LoongArch64和macOS开发环境
  - 创建龙芯交叉编译工具链文件和自动化构建脚本
  - _需求: 2.1, 2.2, 16.1, 28.1_

- [-] 2. 实现HAL层基础架构
- [x] 2.1 创建HAL工厂模式和基础接口
  - 实现I_HalFactory接口和所有硬件抽象基类，使用详细的中文注释说明接口用途
  - 创建平台检测机制，自动选择合适的HAL实现（Linux/龙芯/macOS）
  - 编写HAL接口的单元测试框架，包含Mock对象和测试用例
  - 实现统一的错误处理机制和中文错误描述
  - _需求: 2.1, 2.3, 16.1, 28.1_

- [x] 2.2 实现macOS Mock HAL实现
  - 创建MockHalFactory和所有Mock硬件实现类，支持完整的开发和测试流程
  - 实现从文件读取模拟NMEA数据的MockGnssReceiver，支持多种卫星状态模拟
  - 实现模拟PPS信号和时间戳的MockPpsInput，提供可配置的信号质量
  - 实现模拟原子钟状态的MockAtomicClock，包含温度和频率漂移模拟
  - 添加详细的中文注释说明Mock实现的工作原理和使用方法
  - _需求: 2.1, 2.4, 15.1, 28.2_

- [x] 2.3 实现Linux真实HAL实现
  - 创建LinuxHalFactory和Linux平台的具体实现，支持x86_64和龙芯LoongArch64
  - 实现LinuxGnssReceiver，操作串口设备读取NMEA数据，处理不同厂商的GNSS接收机
  - 实现LinuxPpsInput，使用PPS API捕获高精度时间戳，支持多种PPS设备
  - 实现LinuxAtomicClock，通过SPI/I2C控制铷钟，包含温度补偿和频率校正
  - 实现LinuxNetworkInterface，操作PHC硬件时钟，支持Intel E810等高端网卡
  - 添加详细的中文注释说明硬件操作原理和错误处理机制
  - _需求: 2.1, 2.2, 25.1, 28.3_

- [x] 3. 实现核心数据模型和配置系统
- [x] 3.1 创建时间数据结构和质量指标
  - 实现TimeData、TimeQuality、SystemStatus等核心数据结构
  - 创建时间源枚举和优先级定义
  - 实现数据序列化和反序列化功能
  - _需求: 1.4, 5.2_

- [x] 3.2 实现配置管理系统
  - 创建TimingConfig结构和JSON配置文件解析
  - 实现配置验证和默认值处理
  - 创建配置热重载机制
  - 编写配置系统的单元测试
  - _需求: 4.3, 8.4_

- [x] 4. 实现时钟状态机
- [x] 4.1 创建状态机核心逻辑
  - 实现ClockStateMachine类和四个状态（FreeRun、Disciplining、Locked、Holdover）
  - 定义状态转换条件和事件处理机制
  - 实现状态持久化和恢复功能
  - _需求: 1.1, 1.2_

- [x] 4.2 实现状态转换算法
  - 编写GNSS信号检测和质量评估算法
  - 实现驯服收敛判断逻辑
  - 创建守时模式切换和恢复机制
  - 编写状态机的单元测试和集成测试
  - _需求: 1.2, 1.3_

- [-] 5. 实现授时引擎核心功能
- [x] 5.1 创建授时引擎主控制器
  - 实现TimingEngine类，协调所有时间源和状态机
  - 创建时间源管理和优先级选择逻辑
  - 实现系统启动和关闭流程
  - _需求: 1.1, 6.1_

- [x] 5.2 实现时钟驯服算法
  - 编写PLL/FLL驯服算法，使用GNSS校准PHC，实现±50ns绝对时间精度
  - 实现铷钟特性学习和频率校正算法，支持温度补偿和老化预测
  - 创建守时期间的时间预测和补偿机制，确保±1μs守时精度（24小时）
  - 添加详细的中文注释说明算法原理、数学公式和实现步骤
  - 编写驯服算法的单元测试，验证收敛性和稳定性
  - _需求: 1.4, 9.1, 9.2, 28.3_

- [x] 6. 实现Linux守护进程管理
- [x] 6.1 创建守护进程管理器
  - 实现DaemonManager类，管理ptp4l、chrony、ts2phc进程
  - 创建进程启动、停止、重启和监控机制
  - 实现配置文件动态生成和更新
  - _需求: 3.1, 3.2_

- [x] 6.2 实现守护进程配置和冲突解决
  - 编写ptp4l和chrony配置文件生成逻辑，支持动态clockClass调整（GPS锁定=6，守时=7）
  - 实现守护进程冲突检测和自动解决机制，处理端口和资源竞争
  - 创建进程健康检查和故障恢复流程，支持自动重启和降级策略
  - 实现ts2phc配置管理，支持1PPS和10MHz频率基准
  - 编写守护进程管理的集成测试，验证配置正确性和故障恢复能力
  - _需求: 3.3, 3.4, 24.2, 25.2, 26.1_

- [x] 7. 实现日志记录和错误处理系统
- [ ] 7.1 创建结构化日志系统
  - 实现Logger类，支持多级别日志记录
  - 创建日志轮转和压缩机制
  - 实现日志搜索和过滤功能
  - _需求: 5.1, 5.3, 5.4_

- [x] 7.2 实现错误处理和恢复机制
  - 创建ErrorHandler类和错误分类系统
  - 实现自动错误恢复和降级策略
  - 创建系统健康监控和告警机制
  - 编写错误处理的单元测试
  - _需求: 4.4, 6.4_

- [x] 8. 实现数据持久化和历史记录
- [x] 8.1 创建SQLite数据库存储
  - 设计数据库模式，存储性能指标和事件日志
  - 实现数据库连接池和事务管理
  - 创建数据清理和归档机制
  - _需求: 5.2, 5.3_

- [x] 8.2 实现铷钟学习数据存储
  - 创建二进制格式存储铷钟特性参数
  - 实现学习数据的加载和保存机制
  - 创建数据完整性检查和备份功能
  - 编写数据持久化的单元测试
  - _需求: 1.4, 5.2_

- [-] 9. 实现REST API后端服务
- [x] 9.1 创建HTTP服务器和路由系统
  - 使用oatpp框架实现轻量级HTTP服务器，确保<10ms响应时间
  - 定义完整的RESTful API端点（12个端点），包含SystemStatusDto、PtpConfigDto等数据格式
  - 实现请求验证和标准化错误响应机制，支持中文错误描述
  - 添加API性能监控和请求日志记录功能
  - _需求: 19.1, 20.1, 22.1, 23.1_

- [x] 9.2 实现WebSocket实时通信
  - 创建WebSocket服务器，支持至少100个并发连接和<50ms延迟
  - 实现客户端连接管理和消息广播，支持事件订阅和过滤
  - 创建心跳检测和自动重连机制，确保连接稳定性
  - 实现实时状态推送，包含状态变化、告警事件和性能指标
  - 编写API服务的集成测试，验证并发性能和数据一致性
  - _需求: 19.2, 19.3, 20.2, 23.2_

- [x] 10. 实现身份认证和安全控制
- [x] 10.1 创建用户认证系统
  - 实现基于JWT的身份认证机制，支持角色基础访问控制（RBAC）
  - 创建用户管理和权限控制系统，定义Viewer、Operator、Administrator角色
  - 实现API访问日志和审计功能，记录所有配置更改和安全事件
  - 添加令牌自动刷新和安全过期机制
  - _需求: 21.1, 21.2, 22.1, 12.1_

- [x] 10.2 实现安全防护机制
  - 创建IP白名单和访问频率限制，支持动态黑名单和攻击检测
  - 实现HTTPS支持和证书管理，强制TLS 1.2+加密连接
  - 创建安全事件检测和响应机制，自动阻断异常访问
  - 实现客户端证书验证（可选）和安全密码套件配置
  - 编写安全系统的单元测试，验证防护机制的有效性
  - _需求: 21.3, 22.2, 22.3, 22.4_

- [-] 11. 实现现代化Web管理界面
- [x] 11.1 创建Vue.js项目基础架构和设计系统
  - 设置Vue.js 3 + TypeScript项目，配置Vite构建工具和开发环境
  - 创建深色主题设计系统，融合两种界面风格的优点
  - 实现可折叠侧边栏导航和响应式布局，支持桌面端和移动端适配
  - 创建毛玻璃效果卡片组件库，包含圆角、阴影、渐变等视觉元素
  - 配置Pinia状态管理和Vue Router路由系统
  - 建立与后端REST API和WebSocket的通信模块
  - _需求: 27.1, 27.2, 28.1, 28.2_

- [x] 11.2 实现系统仪表盘和实时监控界面
  - 创建中央大时钟显示组件，突出显示系统当前时间（类似00:45:22格式）
  - 实现主控字钟状态卡片，显示频率、温度、运行时间等关键指标
  - 创建GNSS卫星状态监控组件，显示卫星数量、信号质量、定位精度
  - 实现PTP/NTP服务状态卡片，显示服务状态、客户端数量、同步精度
  - 创建系统资源监控组件，显示CPU使用率、内存占用、网络流量
  - 实现WebSocket实时数据更新，确保所有状态信息的实时性
  - 添加状态指示器和动画效果，提供直观的视觉反馈
  - _需求: 27.1, 27.3, 4.1, 4.2, 4.4, 19.2_

- [x] 11.3 实现信号输出状态管理界面
  - 创建信号输出状态表格组件，显示1PPS、10MHz、TOD等信号输出详情
  - 实现信号质量监控，包含频率、电压、精度、负载等参数显示
  - 创建信号输出配置界面，支持输出使能/禁用、参数调整
  - 实现信号状态的颜色编码显示（绿色正常、黄色警告、红色故障）
  - 添加信号输出历史记录和趋势分析功能
  - 创建信号输出告警和通知机制
  - _需求: 27.1, 27.3, 9.1, 9.2, 23.1_

- [x] 11.4 实现时间源配置和管理界面
  - 创建GNSS接收器配置界面，支持接收机选择、天线配置、卫星系统设置
  - 实现PTP服务配置管理，包含Grandmaster设置、网络参数、时钟类别配置
  - 创建NTP服务配置界面，支持服务器设置、客户端管理、同步策略配置
  - 实现原子钟（铷钟）管理界面，显示钟源状态、学习参数、温度补偿
  - 创建时间源优先级设置和切换策略配置
  - 实现配置验证、导入导出和备份恢复功能
  - _需求: 27.1, 27.3, 4.3, 20.2, 23.3_

- [x] 11.5 实现用户管理和系统配置界面
  - 创建用户认证和登录界面，支持JWT令牌认证和角色权限控制
  - 实现用户管理界面，支持用户创建、编辑、删除和权限分配
  - 创建系统配置管理界面，包含网络设置、安全配置、日志设置
  - 实现系统日志查看和搜索功能，支持日志级别过滤和时间范围查询
  - 创建系统维护界面，支持重启、备份、恢复、固件升级等操作
  - 实现操作审计日志和安全事件监控界面
  - _需求: 21.1, 21.2, 22.1, 22.2, 5.1, 5.3_

- [-] 12. 实现系统集成和服务化
- [x] 12.1 创建系统服务和启动脚本
  - 编写systemd服务文件，支持自动启动
  - 创建系统安装和卸载脚本
  - 实现无头模式运行和资源优化
  - _需求: 6.1, 6.2, 6.3_

- [x] 12.2 实现跨平台构建和部署
  - 配置CI/CD流程，支持Linux x86_64和龙芯LoongArch64平台自动构建
  - 创建标准化部署包和安装脚本，支持自动硬件检测和配置生成
  - 实现自动化测试和部署流程，包含单元测试、集成测试和性能测试
  - 编写详细的部署文档和用户手册，包含中文说明和故障排除指南
  - 创建版本管理和发布流程，支持配置迁移和回滚机制
  - _需求: 16.1, 17.1, 17.2, 18.1, 18.2_

- [x] 13. 进行系统测试和优化
- [x] 13.1 执行端到端集成测试
  - 实现完整的端到端集成测试框架（test_end_to_end_integration.cpp）
  - 测试完整的时钟驯服流程和状态转换（FREE_RUN → DISCIPLINING → LOCKED）
  - 验证多时间源切换和故障恢复机制（GNSS信号丢失和恢复测试）
  - 测试Web界面和API的完整功能（REST API、认证、令牌验证）
  - 进行性能测试和资源使用优化（API响应时间<10ms，并发处理>100req/s）
  - 实现错误处理和恢复机制测试（硬件故障、配置错误处理）
  - 添加长期运行稳定性测试（简化版30秒测试，稳定性>95%）
  - 使用Mock HAL实现完整的硬件行为模拟
  - 集成所有核心组件（Logger、ConfigManager、ErrorHandler、DatabaseManager等）
  - _需求: 1.1, 1.2, 1.3, 4.1_

- [x] 13.2 进行长期稳定性测试
  - 执行7x24小时连续运行测试，验证99.9%系统可用性目标
  - 验证内存泄漏和资源清理机制，确保<100MB内存占用
  - 测试各种异常情况和恢复能力，包含硬件故障和网络中断
  - 优化系统性能和资源使用效率，确保<5% CPU使用率
  - _需求: 11.2, 11.3, 13.1, 13.2_

- [-] 14. 实现高级功能和优化
- [x] 14.1 实现精度监控和告警系统
  - 创建时间精度实时监控，确保±50ns绝对精度和±1μs守时精度
  - 实现智能告警系统，支持多级别告警和自动通知
  - 创建性能指标收集和历史趋势分析功能
  - 实现系统健康评分和预测性维护建议
  - _需求: 9.1, 9.2, 23.1, 24.1_

- [x] 14.2 实现多平台兼容性验证
  - 验证Linux x86_64平台的完整功能和性能指标
  - 验证龙芯LoongArch64平台的交叉编译和运行稳定性
  - 验证macOS开发环境的Mock HAL功能完整性
  - 创建平台特定的性能基准测试和优化建议
  - _需求: 16.1, 16.2, 16.3, 16.4_

- [ ] 14.3 实现UI/UX优化和可访问性
  - 优化苹果风格界面的动画性能和视觉效果
  - 实现完整的键盘导航和ARIA标签支持
  - 创建多语言支持框架（中英文切换）
  - 实现响应式设计和移动端适配
  - 添加用户体验优化，包含加载状态和错误处理
  - _需求: 27.4, 界面可访问性和用户体验_

- [ ] 14.4 实现代码质量保证和文档完善
  - 执行全面的代码审查，确保中文注释的完整性和准确性
  - 创建API文档和开发者指南，包含详细的使用示例
  - 实现自动化代码质量检查，包含静态分析和格式检查
  - 编写完整的用户手册和运维指南，包含故障排除和性能调优
  - _需求: 28.1, 28.2, 28.3, 28.4_