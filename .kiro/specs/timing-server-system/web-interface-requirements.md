# Web管理界面需求规格书

## 1. 概述

### 1.1 项目背景
高精度授时服务器系统需要一个现代化的Web管理界面，为用户提供直观、高效的系统监控和配置管理功能。界面设计需要融合两种现代化设计风格，提供优秀的用户体验。

### 1.2 设计理念
- **现代化设计**: 深色主题，毛玻璃效果，渐变背景
- **信息密度**: 在有限空间内展示丰富的系统信息
- **实时性**: 所有关键数据实时更新，响应迅速
- **易用性**: 直观的操作界面，符合用户使用习惯
- **响应式**: 支持桌面端和移动端访问

## 2. 功能需求

### 2.1 系统仪表盘 (Dashboard)

#### 2.1.1 中央时钟显示
- **大时钟组件**: 居中显示当前系统时间，格式为 HH:MM:SS
- **时间精度指示**: 显示当前时间同步精度状态
- **时间源指示**: 显示当前主时间源（GNSS/原子钟/网络）
- **同步状态**: 实时显示系统同步状态和质量指标

#### 2.1.2 系统状态卡片
- **主控字钟状态**
  - 频率显示: 9.192 GHz
  - 温度监控: 45.3°C
  - 运行时间: 247天12小时
  - 状态指示器: 绿色(正常)/黄色(警告)/红色(故障)

- **GNSS卫星状态**
  - 可见卫星数: 24颗
  - 跟踪卫星数: 17颗
  - 定位精度: 2.4m
  - 信号质量: 优秀/良好/一般/差

- **PTP服务状态**
  - 服务状态: 主时钟模式
  - 客户端数量: 10个
  - 同步精度: ±50ns
  - 网络延迟: 125μs

- **NTP服务状态**
  - 服务状态: 运行中
  - 客户端连接: 139个
  - 服务层级: Stratum 1
  - 平均延迟: 2.6ms

- **系统资源状态**
  - CPU使用率: 进度条显示
  - 内存使用率: 进度条显示
  - 网络流量: 1.1 MB/s
  - 存储空间: 使用情况显示

### 2.2 信号输出状态界面

#### 2.2.1 信号输出表格
- **1PPS输出**
  - 频率: 1 PPS
  - 电压: 3.2V
  - 精度: 8.3°
  - 负载: 6.0010μm
  - 状态: 正常输出 (绿色指示)

- **10MHz输出**
  - 频率: 10.000 MHz
  - 电压: 0.3V
  - 精度: 8.3°
  - 负载: 6.0010μm
  - 状态: 正常输出 (绿色指示)

- **TOD输出**
  - 频率: 100.000 MHz
  - 电压: 0.8V
  - 精度: 1.2°
  - 负载: 6.0010μm
  - 状态: 可用输出 (黄色指示)

#### 2.2.2 信号质量监控
- **实时波形显示**: 信号输出的实时波形图
- **频率稳定度**: 长期和短期频率稳定度指标
- **相位噪声**: 相位噪声谱密度显示
- **告警阈值**: 可配置的告警阈值和通知机制

### 2.3 时间源管理界面

#### 2.3.1 GNSS接收器管理
- **接收器状态**: 显示GNSS接收器工作状态
- **卫星信息**: 各卫星系统的信号强度和质量
- **天线配置**: 天线类型、位置、增益设置
- **接收机配置**: 波特率、数据格式、更新频率

#### 2.3.2 PTP服务配置
- **时钟配置**: Grandmaster Clock设置
- **网络参数**: 网络接口、VLAN、优先级配置
- **时钟类别**: clockClass动态调整设置
- **客户端管理**: 连接的PTP客户端列表和状态

#### 2.3.3 NTP服务配置
- **服务器设置**: NTP服务器参数配置
- **客户端管理**: NTP客户端连接管理
- **同步策略**: 时间同步算法和参数设置
- **访问控制**: 客户端访问权限和安全设置

### 2.4 系统配置和管理

#### 2.4.1 用户管理
- **用户认证**: JWT令牌认证和会话管理
- **角色权限**: Viewer、Operator、Administrator角色
- **用户操作**: 用户创建、编辑、删除、权限分配
- **安全审计**: 用户操作日志和安全事件记录

#### 2.4.2 系统设置
- **网络配置**: IP地址、网关、DNS设置
- **安全配置**: HTTPS证书、访问控制、防火墙
- **日志配置**: 日志级别、轮转策略、存储设置
- **告警配置**: 告警阈值、通知方式、联系人设置

## 3. 技术需求

### 3.1 前端技术栈
- **框架**: Vue.js 3.3+ (Composition API)
- **语言**: TypeScript 5.0+
- **构建工具**: Vite 4.3+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **HTTP客户端**: Axios 1.4+
- **图表库**: Chart.js 4.3+ with vue-chartjs 5.2+
- **工具库**: @vueuse/core 10.0+

### 3.2 设计规范
- **主题**: 深色主题，深蓝色渐变背景 (#1a1d3a → #2d1b69)
- **卡片**: 毛玻璃效果，圆角12px，半透明背景
- **字体**: 系统字体栈，支持中文显示
- **图标**: SVG图标，统一风格
- **动画**: 流畅的过渡动画，加载状态反馈

### 3.3 响应式设计
- **桌面端**: 1920x1080及以上分辨率优化
- **平板端**: 768px-1024px适配
- **移动端**: 320px-768px适配
- **可折叠侧边栏**: 适应不同屏幕尺寸

### 3.4 性能要求
- **首屏加载**: <3秒
- **页面切换**: <500ms
- **数据更新**: 实时更新，延迟<100ms
- **内存占用**: <100MB
- **网络流量**: 优化资源加载，支持缓存

## 4. 接口需求

### 4.1 REST API接口
- **系统状态**: GET /api/v1/system/status
- **时间源信息**: GET /api/v1/timesource/info
- **信号输出**: GET /api/v1/signal/output
- **配置管理**: GET/PUT /api/v1/config
- **用户管理**: CRUD /api/v1/users
- **日志查询**: GET /api/v1/logs

### 4.2 WebSocket接口
- **实时状态**: ws://server/ws/status
- **告警事件**: ws://server/ws/alerts
- **性能指标**: ws://server/ws/metrics
- **系统事件**: ws://server/ws/events

### 4.3 数据格式
- **时间格式**: ISO 8601标准
- **数值精度**: 根据实际需求确定小数位数
- **状态枚举**: 标准化状态值和描述
- **错误处理**: 统一错误码和中文错误信息

## 5. 安全需求

### 5.1 身份认证
- **JWT令牌**: 访问令牌和刷新令牌机制
- **会话管理**: 会话超时和自动续期
- **多因素认证**: 支持TOTP等二次验证
- **密码策略**: 强密码要求和定期更换

### 5.2 访问控制
- **角色权限**: 基于角色的访问控制(RBAC)
- **API权限**: 细粒度的API访问控制
- **操作审计**: 所有操作的审计日志记录
- **IP限制**: 支持IP白名单和访问频率限制

### 5.3 数据安全
- **HTTPS**: 强制HTTPS加密传输
- **数据加密**: 敏感数据加密存储
- **输入验证**: 防止XSS、CSRF等攻击
- **安全头**: 设置安全相关的HTTP头

## 6. 部署需求

### 6.1 开发环境
- **Node.js**: 16.0+
- **包管理器**: npm 8.0+ 或 yarn 1.22+
- **开发服务器**: Vite开发服务器
- **热重载**: 支持代码热重载和实时预览

### 6.2 生产环境
- **静态部署**: 构建为静态文件部署
- **Web服务器**: Nginx或Apache
- **CDN支持**: 支持CDN加速
- **缓存策略**: 合理的缓存策略配置

### 6.3 容器化部署
- **Docker**: 支持Docker容器化部署
- **多阶段构建**: 优化镜像大小
- **环境变量**: 支持环境变量配置
- **健康检查**: 容器健康检查机制

## 7. 测试需求

### 7.1 单元测试
- **组件测试**: Vue组件单元测试
- **工具函数**: 工具函数和服务层测试
- **覆盖率**: 代码覆盖率>80%
- **测试框架**: Vitest或Jest

### 7.2 集成测试
- **API集成**: 与后端API的集成测试
- **WebSocket**: WebSocket连接和消息测试
- **用户流程**: 关键用户操作流程测试
- **跨浏览器**: 主流浏览器兼容性测试

### 7.3 性能测试
- **加载性能**: 页面加载时间测试
- **运行性能**: 长时间运行稳定性测试
- **内存泄漏**: 内存使用情况监控
- **网络优化**: 网络请求优化验证

## 8. 维护需求

### 8.1 代码质量
- **代码规范**: ESLint和Prettier代码格式化
- **类型检查**: TypeScript严格模式
- **代码审查**: 代码提交前审查流程
- **文档**: 完整的代码注释和API文档

### 8.2 监控和日志
- **错误监控**: 前端错误监控和上报
- **性能监控**: 页面性能指标收集
- **用户行为**: 用户操作行为分析
- **日志记录**: 关键操作日志记录

### 8.3 更新和升级
- **版本管理**: 语义化版本管理
- **更新机制**: 支持在线更新和回滚
- **兼容性**: 向后兼容性保证
- **迁移工具**: 数据和配置迁移工具