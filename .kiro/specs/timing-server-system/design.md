# 设计文档

## 概述

本设计文档详细描述了高精度授时服务器系统的技术架构和实现方案。系统采用三层解耦架构，集成多种时间源（GNSS、铷原子钟、高精度RTC、外部1PPS和10MHz信号），通过智能时钟驯服算法提供高精度时间同步服务，并提供基于Web的实时监控和管理界面。

### 设计目标

- **高精度时间同步**: 实现±50ns绝对时间精度（GNSS锁定状态）和±1μs守时精度（24小时）
- **多平台支持**: 支持Linux x86_64、龙芯LoongArch64平台部署，macOS开发环境
- **高可用性**: 达到99.9%系统可用性，MTBF>8760小时，MTTR<5分钟
- **资源优化**: 内存占用<100MB，CPU使用率<5%，适合嵌入式环境
- **标准协议支持**: 同时提供PTP特级主时钟和NTP Stratum 1服务

### 核心技术特性

- **四状态时钟驯服**: FreeRun → Disciplining → Locked → Holdover状态机
- **智能时间源融合**: GNSS绝对精度 + 铷钟守时稳定性 + 多重备份机制
- **动态配置管理**: 根据系统状态自动调整PTP clockClass和守护进程配置
- **硬件抽象层**: 跨平台HAL设计，支持真实硬件和Mock开发环境
- **实时监控API**: RESTful API + WebSocket实时推送，<10ms响应时间
- **工业级日志系统**: 结构化多级别日志、异步处理、自动轮转压缩、高级搜索过滤
- **智能错误处理**: 自动错误检测分类、多策略恢复机制、系统健康监控、告警通知
- **健壮进程管理**: 进程组管理、SIGCHLD信号处理、自动重启、详细错误反馈

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "硬件层"
        GNSS[GNSS接收机<br/>NMEA + 1PPS]
        RB[铷原子钟<br/>Rb Oscillator]
        RTC[高精度RTC<br/>Battery Backed]
        EXT_PPS[外部1PPS<br/>External Reference]
        EXT_10M[外部10MHz<br/>Frequency Reference]
        NIC[网络接口卡<br/>Intel E810 + PHC]
    end
    
    subgraph "硬件抽象层 (HAL)"
        HAL_GNSS[GNSS驱动抽象]
        HAL_RB[铷钟驱动抽象]
        HAL_RTC[RTC驱动抽象]
        HAL_PPS[PPS驱动抽象]
        HAL_10M[10MHz驱动抽象]
        HAL_NIC[NIC/PHC驱动抽象]
    end
    
    subgraph "核心服务层"
        TE[授时引擎<br/>Timing Engine]
        SM[状态机<br/>Clock State Machine]
        DA[驯服算法<br/>Disciplining Algorithm]
        DM[守护进程管理<br/>Daemon Manager]
        LOGGER[结构化日志系统<br/>Logger]
        ERROR_HANDLER[错误处理系统<br/>Error Handler]
        HEALTH_MONITOR[系统健康监控<br/>Health Monitor]
        subgraph "Linux守护进程"
            PTP4L[ptp4l]
            CHRONY[chrony]
            TS2PHC[ts2phc]
        end
    end
    
    subgraph "管理与表示层"
        API[REST/WebSocket API<br/>C++ Backend]
        WEB[Vue.js前端<br/>Web Dashboard]
    end
    
    GNSS --> HAL_GNSS
    RB --> HAL_RB
    RTC --> HAL_RTC
    EXT_PPS --> HAL_PPS
    EXT_10M --> HAL_10M
    NIC --> HAL_NIC
    
    HAL_GNSS --> TE
    HAL_RB --> TE
    HAL_RTC --> TE
    HAL_PPS --> TE
    HAL_10M --> TE
    HAL_NIC --> TE
    
    TE --> SM
    TE --> DA
    TE --> DM
    TE --> LOGGER
    TE --> ERROR_HANDLER
    
    DM --> PTP4L
    DM --> CHRONY
    DM --> TS2PHC
    DM --> LOGGER
    DM --> ERROR_HANDLER
    
    ERROR_HANDLER --> HEALTH_MONITOR
    LOGGER --> ERROR_HANDLER
    
    TE --> API
    API --> WEB
```

### 三层架构详述

#### 1. 硬件抽象层 (HAL)
HAL层是实现跨平台兼容性的关键组件，支持在macOS上开发、在Linux和龙芯平台上部署的工作流程。通过基于抽象基类和纯虚函数的运行时多态，HAL提供统一的硬件接口，完全屏蔽底层平台差异。

**HAL核心价值：**
- **平台无关性**: 核心授时引擎只针对抽象接口编程，不感知底层平台实现
- **开发效率**: 在macOS上使用Mock实现进行开发和测试，无需每次部署到目标硬件
- **可测试性**: 通过模拟实现支持完整的单元测试和集成测试
- **可维护性**: 避免条件编译指令，保持代码清洁和可维护

**平台实现策略：**
- **部署平台 (Linux/龙芯)**: 提供真实硬件操作的具体实现，使用系统调用操作实际设备
- **开发平台 (macOS)**: 提供Mock实现，从文件或网络读取模拟数据，支持完整开发流程

**HAL接口定义：**

| 接口名称 | 核心方法 | 功能描述 |
|----------|----------|----------|
| I_GnssReceiver | ReadNmeaSentence() | 从GNSS接收器读取NMEA数据 |
| I_PpsInput | WaitForPpsEdge(timeout_ms) | 等待PPS信号边沿或超时返回 |
| I_AtomicClock | GetStatus(), SetFrequencyCorrection() | 获取原子钟状态和设置频率校正 |
| I_FrequencyInput | MeasureFrequency() | 测量外部频率输入(10MHz)的实际频率 |
| I_HighPrecisionRtc | GetTime(), SetTime() | 读取或设置高精度RTC时间 |
| I_NetworkInterface | GetPHCTime(), SetPHCTime() | 操作网卡PTP硬件时钟 |

#### 2. 核心服务层
核心服务层实现系统的主要业务逻辑，由常驻后台的C++守护进程组成：

**核心授时引擎 (Timing Engine)**:
- **HAL实例化与管理**: 检测运行平台，动态加载对应HAL实现(LinuxHal.so/MockHal.so)
- **时钟驯服状态机**: 严格执行四状态驯服流程，基于硬件状态和算法收敛情况进行状态转移
- **硬件状态监控**: 持续轮询所有硬件模块状态，记录异常并触发状态机变化
- **动态配置生成**: 根据运行状态动态调整PTP时钟等级(clockClass)和生成配置文件
- **外部守护进程管理**: 启动、停止、监控ptp4l、phc2sys、ts2phc、chronyd等Linux守护进程
- **状态与指标输出**: 通过IPC机制实时推送内部状态和性能指标给管理API

**PTP特级主时钟集成 (linuxptp)**:
- **ts2phc**: 将GNSS 1PPS信号作为外部时间戳输入，驯服网卡PHC，支持10MHz驯服NIC DPLL
- **ptp4l**: 配置为特级主时钟，使用PHC作为时间基准提供网络PTP服务
- **phc2sys**: 将PHC时间同步到系统时钟(CLOCK_REALTIME)，确保本地应用时间一致性

**NTP Stratum 1集成 (chrony)**:
- **PHC参考源**: chrony直接引用被驯服的PHC作为主要参考时钟
- **GNSS监控**: 通过gpsd SHM接口监控原始GNSS时间，用于交叉验证
- **Stratum 1服务**: 向网络提供高精度NTP授时服务

#### 3. 管理与表示层
提供用户交互和系统监控功能：

**RESTful管理API (C++后端)**:
- **技术选型**: 使用oatpp框架，高性能、低内存占用、零外部依赖
- **API设计**: 遵循RESTful原则，使用标准HTTP方法和JSON格式
- **核心端点**:
  - `/api/v1/status`: 获取系统完整状态信息
  - `/api/v1/config/ptp`: PTP配置管理
  - `/api/v1/config/ntp`: NTP配置管理  
  - `/api/v1/logs`: 系统日志查询
  - `/ws/status`: WebSocket实时状态推送

**Vue.js前端仪表盘**:
- **技术栈**: Vue.js 3 + Pinia状态管理 + 自定义苹果风格UI组件库
- **设计语言**: 苹果风格设计系统，毛玻璃质感，现代化视觉体验
- **核心页面**:
  - **概览仪表盘**: 关键状态指示器、GNSS卫星视图、协议状态摘要
  - **PTP/NTP配置页面**: 图形化配置参数编辑
  - **系统日志页面**: 实时日志查看器，支持过滤和搜索
  - **性能分析页面**: 历史数据可视化和趋势分析
- **实时更新**: WebSocket -> Pinia -> Vue组件的响应式数据流
- **视觉特性**: 毛玻璃背景、流畅动画、微交互反馈、响应式布局

## 组件和接口

### 时钟状态机设计

系统实现一个精密的四状态时钟驯服状态机，管理复杂的时间源融合和驯服过程：

```mermaid
stateDiagram-v2
    [*] --> FreeRun: 系统启动
    FreeRun --> Disciplining: 检测到GNSS信号
    Disciplining --> Locked: 驯服收敛
    Locked --> Holdover: GNSS信号丢失
    Holdover --> Disciplining: GNSS信号恢复
    Locked --> FreeRun: 系统重启/重置
    Holdover --> FreeRun: 长时间无信号
    
    FreeRun: 自由运行<br/>使用RTC初始时间<br/>精度较低但足以启动服务
    Disciplining: 驯服中<br/>使用ts2phc工具<br/>GNSS 1PPS驯服PHC<br/>10MHz驯服NIC DPLL
    Locked: 锁定<br/>最佳工作状态<br/>学习铷钟特性参数<br/>为守时做准备
    Holdover: 守时<br/>使用铷钟作为主参考<br/>应用学习的校正因子<br/>持续尝试重新捕获GNSS
```

#### 状态详细描述

**状态1: 自由运行 (Free Run / Cold Start)**
- **触发条件**: 系统初次上电或无任何有效外部参考
- **主要操作**: 使用板载高精度RTC设置初始系统时间
- **特点**: 时间精度较低，但足以启动各项服务
- **持续时间**: 直到检测到有效GNSS信号

**状态2: 驯服中 (Disciplining)**
- **触发条件**: 检测到有效GNSS信号并成功锁定卫星
- **主要操作**: 
  - 接收NMEA报文获取日期时间信息
  - 使用ts2phc工具将GNSS 1PPS信号作为外部时间戳事件
  - 精确校准和驯服网络接口卡(NIC)上的PTP硬件时钟(PHC)
  - 如果硬件支持(如Intel E810系列网卡)，使用外部10MHz信号驯服NIC的DPLL
- **收敛判断**: PHC与GNSS参考源的相位和频率误差收敛到极小阈值

**状态3: 锁定 (Locked)**
- **特点**: 系统最佳工作状态，提供最高精度时间服务
- **主要操作**:
  - 持续将内部铷原子钟频率与已被GNSS锁定的高精度PHC进行比对
  - "学习"并量化铷钟的频率漂移率、温度敏感性等关键参数
  - 将学习数据存储，为可能的守时操作做准备
- **监控**: 持续监控GNSS信号质量和系统稳定性

**状态4: 守时 (Holdover)**
- **触发条件**: GNSS信号中断(天气影响、天线故障或恶意干扰)
- **主要操作**:
  - 启用铷原子钟作为主要频率参考
  - 应用预先计算的校正因子补偿铷钟固有漂移
  - 在无外部参考情况下维持高精度时间输出
  - 持续尝试重新捕获GNSS信号
- **恢复**: 一旦GNSS信号恢复，重新进入驯服状态

### 时间源等级化驯服策略

系统集成的多种时间源并非简单的备选关系，而是在复杂的时钟驯服体系中扮演不同但互补的角色：

| 时间源 | 主要角色 | 守时优先级 | 接口类型 | 状态机使用 | 特性描述 |
|--------|----------|------------|----------|------------|----------|
| GNSS (NMEA+1PPS) | 绝对精度主要参考 | 1 | /dev/ttyS*, /dev/pps* | Disciplining, Locked | 提供绝对UTC时间精度，但信号可能中断 |
| 铷原子钟 | 高稳定度守时源 | 2 | SPI/I2C/GPIO | Holdover | 卓越的中短期频率稳定度，理想的守时振荡器，但会缓慢漂移需长期校准 |
| 外部10MHz | 高稳定度频率基准 | 3 | NIC硬件引脚, ts2phc | Disciplining, Locked | 提供高稳定度频率基准，用于驯服NIC的DPLL |
| 外部1PPS | 精确相位参考 | 4 | NIC硬件引脚, ts2phc | Disciplining | 提供精确的秒脉冲相位对齐 |
| 高精度RTC | 非易失性时间源 | 5 | /dev/rtc* | FreeRun | 系统冷启动时提供合理的初始时间 |

#### 时间源融合原理

**GNSS + 1PPS 组合**:
- NMEA报文提供绝对时间信息(日期、时间)
- 1PPS信号提供精确的秒边界对齐
- 通过ts2phc工具将1PPS作为外部时间戳事件驯服PHC

**铷原子钟学习机制**:
- 在Locked状态下，持续比对铷钟频率与GNSS锁定的PHC
- 学习并量化频率漂移率、温度敏感性等参数
- 为Holdover状态提供精确的校正因子

**10MHz频率基准**:
- 在支持的硬件(如Intel E810)上驯服NIC的DPLL
- 进一步提升频率精度和稳定性
- 与1PPS信号协同工作提供完整的时频参考

### 核心接口定义

#### HAL层接口
```cpp
// GNSS接收机抽象接口
class I_GnssReceiver {
public:
    virtual ~I_GnssReceiver() = default;
    virtual bool Initialize() = 0;
    virtual std::string ReadNmeaSentence() = 0;
    virtual bool IsSignalValid() = 0;
    virtual SatelliteInfo GetSatelliteInfo() = 0;
    virtual void Close() = 0;
};

// PPS输入抽象接口
class I_PpsInput {
public:
    virtual ~I_PpsInput() = default;
    virtual bool Initialize() = 0;
    virtual bool WaitForPpsEdge(int timeout_ms) = 0;
    virtual uint64_t GetLastPpsTimestamp() = 0;
    virtual void Close() = 0;
};

// 原子钟抽象接口
class I_AtomicClock {
public:
    virtual ~I_AtomicClock() = default;
    virtual bool Initialize() = 0;
    virtual ClockState GetStatus() = 0;
    virtual bool SetFrequencyCorrection(double ppm) = 0;
    virtual double GetFrequencyOffset() = 0;
    virtual ClockHealth GetHealth() = 0;
    virtual void Close() = 0;
};

// 频率输入抽象接口
class I_FrequencyInput {
public:
    virtual ~I_FrequencyInput() = default;
    virtual bool Initialize() = 0;
    virtual double MeasureFrequency() = 0;
    virtual bool IsSignalPresent() = 0;
    virtual void Close() = 0;
};

// 高精度RTC抽象接口
class I_HighPrecisionRtc {
public:
    virtual ~I_HighPrecisionRtc() = default;
    virtual bool Initialize() = 0;
    virtual timespec GetTime() = 0;
    virtual bool SetTime(const timespec& ts) = 0;
    virtual bool IsValid() = 0;
    virtual void Close() = 0;
};

// 网络接口抽象接口
class I_NetworkInterface {
public:
    virtual ~I_NetworkInterface() = default;
    virtual bool Initialize() = 0;
    virtual timespec GetPHCTime() = 0;
    virtual bool SetPHCTime(const timespec& ts) = 0;
    virtual bool ConfigurePTP(const PTPConfig& config) = 0;
    virtual PHCStatus GetPHCStatus() = 0;
    virtual void Close() = 0;
};

// HAL工厂接口 - 用于创建平台特定的实现
class I_HalFactory {
public:
    virtual ~I_HalFactory() = default;
    virtual std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() = 0;
    virtual std::unique_ptr<I_PpsInput> CreatePpsInput() = 0;
    virtual std::unique_ptr<I_AtomicClock> CreateAtomicClock() = 0;
    virtual std::unique_ptr<I_FrequencyInput> CreateFrequencyInput() = 0;
    virtual std::unique_ptr<I_HighPrecisionRtc> CreateRtc() = 0;
    virtual std::unique_ptr<I_NetworkInterface> CreateNetworkInterface() = 0;
};

// 平台特定实现示例
#ifdef __linux__
class LinuxHalFactory : public I_HalFactory {
public:
    std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() override;
    // ... 其他方法实现
};
#elif __APPLE__
class MockHalFactory : public I_HalFactory {
public:
    std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() override;
    // ... 其他方法实现 (返回Mock对象)
};
#endif
```

#### 核心服务接口
```cpp
// 授时引擎接口
class ITimingEngine {
public:
    virtual bool start() = 0;
    virtual bool stop() = 0;
    virtual ClockState getCurrentState() = 0;
    virtual SystemStatus getSystemStatus() = 0;
    virtual bool configureTimeSource(const TimeSourceConfig& config) = 0;
};

// 状态机接口
class IClockStateMachine {
public:
    virtual ClockState getCurrentState() = 0;
    virtual bool transitionTo(ClockState newState) = 0;
    virtual void processEvent(const ClockEvent& event) = 0;
};
```

## 数据模型

### 核心数据结构

```cpp
// 时间数据结构
struct TimeData {
    uint64_t timestamp_ns;      // 纳秒级时间戳
    double frequency_offset;    // 频率偏移 (ppm)
    double phase_offset;        // 相位偏移 (ns)
    TimeQuality quality;        // 时间质量指标
    TimeSource source;          // 时间源标识
};

// 时间质量指标
struct TimeQuality {
    double accuracy_ns;         // 精度 (纳秒)
    double stability_ppm;       // 稳定度 (ppm)
    uint32_t confidence;        // 置信度 (0-100)
    bool is_valid;             // 有效性标志
};

// 系统状态
struct SystemStatus {
    ClockState current_state;   // 当前状态
    TimeSource active_source;   // 活跃时间源
    std::vector<TimeSourceStatus> sources; // 所有时间源状态
    SystemHealth health;        // 系统健康状况
    uint64_t uptime_seconds;   // 运行时间
};

// 配置数据
struct TimingConfig {
    TimeSourcePriority priorities;     // 时间源优先级
    DiscipliningParameters discipline; // 驯服参数
    HoldoverParameters holdover;       // 守时参数
    AlarmThresholds alarms;           // 告警阈值
};
```

### 数据持久化

系统使用以下存储机制：
- **配置数据**: JSON格式配置文件 (`/etc/timing-server/config.json`)
- **历史数据**: SQLite数据库存储性能指标和事件日志
- **学习数据**: 二进制格式存储铷钟特性学习结果
- **日志数据**: 结构化日志文件，支持轮转和压缩

## 错误处理

### 错误分类和处理策略

#### 1. 硬件错误
- **GNSS信号丢失**: 自动切换到守时模式，启用铷钟
- **铷钟故障**: 降级到其他可用时间源，记录告警
- **NIC/PHC错误**: 尝试重新初始化，必要时重启相关服务
- **RTC错误**: 使用系统时间作为备选，记录警告

#### 2. 软件错误
- **守护进程崩溃**: 自动重启，记录故障信息
- **配置错误**: 回退到默认配置，通知管理员
- **内存不足**: 清理缓存，降低采样频率
- **文件系统错误**: 切换到内存存储，记录告警

#### 3. 网络错误
- **API服务故障**: 核心服务继续运行，尝试重启API服务
- **WebSocket连接断开**: 客户端自动重连机制
- **认证失败**: 记录安全事件，可选IP封禁

### 错误恢复机制

```cpp
class ErrorHandler {
public:
    enum class ErrorSeverity {
        INFO,       // 信息性事件
        WARNING,    // 警告，不影响核心功能
        ERROR,      // 错误，影响部分功能
        CRITICAL    // 严重错误，影响核心功能
    };
    
    struct ErrorEvent {
        ErrorSeverity severity;
        std::string component;
        std::string description;
        uint64_t timestamp;
        std::map<std::string, std::string> context;
    };
    
    virtual void handleError(const ErrorEvent& error) = 0;
    virtual void registerRecoveryAction(const std::string& errorType, 
                                      std::function<bool()> action) = 0;
};
```

## 日志记录和错误处理系统

### 结构化日志系统设计

系统实现了工业级的结构化日志记录系统，支持多级别、多组件的日志管理，为生产环境的问题排查和性能分析提供强有力的支持。

#### 日志架构设计

```mermaid
graph TB
    subgraph "日志生成层"
        APP[应用程序代码]
        MACRO[日志宏定义]
        CONTEXT[上下文信息]
    end
    
    subgraph "日志处理层"
        LOGGER[Logger单例]
        QUEUE[异步日志队列]
        WORKER[工作线程]
        FILTER[日志过滤器]
    end
    
    subgraph "日志输出层"
        FILE[文件输出<br/>支持轮转压缩]
        CONSOLE[控制台输出<br/>支持颜色显示]
        SYSLOG[系统日志输出<br/>集成syslog]
    end
    
    subgraph "日志管理层"
        ROTATION[日志轮转管理]
        SEARCH[日志搜索引擎]
        HISTORY[历史记录管理]
    end
    
    APP --> MACRO
    MACRO --> LOGGER
    CONTEXT --> LOGGER
    LOGGER --> QUEUE
    QUEUE --> WORKER
    WORKER --> FILTER
    FILTER --> FILE
    FILTER --> CONSOLE
    FILTER --> SYSLOG
    
    FILE --> ROTATION
    LOGGER --> SEARCH
    LOGGER --> HISTORY
```

#### 日志级别和组件分类

**日志级别定义：**
```cpp
enum class LogLevel {
    TRACE = 0,      // 跟踪级别 - 最详细的调试信息
    DEBUG = 1,      // 调试级别 - 调试信息
    INFO = 2,       // 信息级别 - 一般信息
    WARNING = 3,    // 警告级别 - 警告信息
    ERROR = 4,      // 错误级别 - 错误信息
    CRITICAL = 5    // 严重级别 - 严重错误信息
};
```

**组件分类系统：**
```cpp
enum class LogComponent {
    SYSTEM,         // 系统组件
    TIMING_ENGINE,  // 授时引擎
    STATE_MACHINE,  // 状态机
    HAL_GNSS,       // GNSS硬件抽象层
    HAL_RUBIDIUM,   // 铷钟硬件抽象层
    HAL_RTC,        // RTC硬件抽象层
    HAL_PPS,        // PPS硬件抽象层
    HAL_FREQ,       // 频率输入硬件抽象层
    HAL_NETWORK,    // 网络接口硬件抽象层
    DAEMON_MANAGER, // 守护进程管理器
    CONFIG_MANAGER, // 配置管理器
    API_SERVER,     // API服务器
    WEBSOCKET,      // WebSocket服务
    AUTH_MANAGER,   // 认证管理器
    PTP4L,          // ptp4l守护进程
    CHRONY,         // chrony守护进程
    TS2PHC          // ts2phc守护进程
};
```

#### 日志轮转和压缩机制

**轮转策略配置：**
```cpp
struct LogRotationConfig {
    size_t max_file_size_mb = 100;             // 单个日志文件最大大小（MB）
    size_t max_files = 10;                     // 保留的日志文件数量
    bool enable_compression = true;             // 是否启用压缩
    std::string compression_format = "gzip";    // 压缩格式
    uint32_t rotation_check_interval_s = 60;   // 轮转检查间隔（秒）
};
```

**轮转实现机制：**
- **大小触发轮转**: 当日志文件达到配置的最大大小时自动轮转
- **时间触发轮转**: 支持按时间间隔进行轮转（可选）
- **压缩存储**: 自动压缩历史日志文件，节省存储空间
- **文件清理**: 自动删除超过保留数量的旧日志文件

#### 日志搜索和过滤功能

**搜索过滤器设计：**
```cpp
struct LogFilter {
    LogLevel min_level = LogLevel::TRACE;       // 最小日志级别
    LogLevel max_level = LogLevel::CRITICAL;    // 最大日志级别
    std::vector<LogComponent> components;       // 组件过滤列表
    uint64_t start_time_ns = 0;                // 开始时间（纳秒）
    uint64_t end_time_ns = UINT64_MAX;         // 结束时间（纳秒）
    std::string message_pattern;               // 消息模式匹配（支持正则表达式）
    size_t max_results = 1000;                 // 最大结果数量
};
```

**搜索功能特性：**
- **多维度过滤**: 支持按级别、组件、时间范围、消息内容进行过滤
- **正则表达式支持**: 消息内容支持正则表达式模式匹配
- **高性能搜索**: 内存中维护索引，支持快速搜索
- **结果分页**: 支持大量搜索结果的分页显示

### 智能错误处理和恢复系统

系统实现了全面的错误处理和自动恢复机制，能够智能识别错误类型、评估严重程度并执行相应的恢复策略。

#### 错误分类体系

**错误类型枚举：**
```cpp
enum class ErrorType {
    // 硬件相关错误
    HARDWARE_FAULT,         // 硬件故障
    GNSS_SIGNAL_LOST,       // GNSS信号丢失
    RUBIDIUM_FAULT,         // 铷钟故障
    RTC_FAULT,              // RTC故障
    PPS_SIGNAL_LOST,        // PPS信号丢失
    FREQ_REF_LOST,          // 频率基准丢失
    NETWORK_INTERFACE_DOWN, // 网络接口故障
    
    // 软件相关错误
    CONFIG_ERROR,           // 配置错误
    DAEMON_CRASH,           // 守护进程崩溃
    API_ERROR,              // API错误
    DATABASE_ERROR,         // 数据库错误
    FILE_SYSTEM_ERROR,      // 文件系统错误
    MEMORY_ERROR,           // 内存错误
    
    // 系统相关错误
    SYSTEM_OVERLOAD,        // 系统过载
    TEMPERATURE_ALARM,      // 温度告警
    POWER_SUPPLY_FAULT,     // 电源故障
    CLOCK_SYNC_LOST,        // 时钟同步丢失
    HOLDOVER_TIMEOUT,       // 守时超时
    
    // 网络相关错误
    NETWORK_TIMEOUT,        // 网络超时
    PTP_SYNC_LOST,          // PTP同步丢失
    NTP_SYNC_LOST,          // NTP同步丢失
    
    // 用户相关错误
    AUTHENTICATION_FAILED,  // 认证失败
    AUTHORIZATION_DENIED,   // 授权拒绝
    INVALID_REQUEST,        // 无效请求
    
    UNKNOWN_ERROR           // 未知错误
};
```

**错误严重程度分级：**
```cpp
enum class ErrorSeverity {
    LOW,        // 低 - 不影响核心功能
    MEDIUM,     // 中 - 影响部分功能
    HIGH,       // 高 - 影响核心功能
    CRITICAL    // 严重 - 系统无法正常工作
};
```

#### 自动恢复策略

**恢复策略类型：**
```cpp
enum class RecoveryStrategy {
    IGNORE,             // 忽略错误
    RETRY,              // 重试操作
    RESTART_COMPONENT,  // 重启组件
    FAILOVER,           // 故障切换
    DEGRADE_SERVICE,    // 降级服务
    SHUTDOWN_SYSTEM,    // 关闭系统
    MANUAL_INTERVENTION // 需要人工干预
};
```

**恢复处理器架构：**
```cpp
class IErrorRecoveryHandler {
public:
    virtual ~IErrorRecoveryHandler() = default;
    virtual bool HandleRecovery(ErrorInfo& error_info) = 0;
    virtual std::string GetHandlerName() const = 0;
    virtual bool CanHandle(ErrorType error_type) const = 0;
};

// 重试恢复处理器
class RetryRecoveryHandler : public IErrorRecoveryHandler {
    // 实现自动重试逻辑，支持指数退避
};

// 故障切换恢复处理器
class FailoverRecoveryHandler : public IErrorRecoveryHandler {
    // 实现时间源故障切换、网络接口切换等
};

// 组件重启恢复处理器
class RestartRecoveryHandler : public IErrorRecoveryHandler {
    // 实现守护进程重启、服务重启等
};
```

#### 系统健康监控

**健康监控指标：**
```cpp
struct HealthMonitorConfig {
    uint32_t check_interval_ms;                // 检查间隔（毫秒）
    uint32_t cpu_threshold_percent;            // CPU使用率阈值
    uint32_t memory_threshold_percent;         // 内存使用率阈值
    double temperature_threshold_celsius;      // 温度阈值（摄氏度）
    uint32_t disk_usage_threshold_percent;     // 磁盘使用率阈值
    uint32_t network_timeout_ms;               // 网络超时时间
    bool enable_proactive_monitoring;          // 是否启用主动监控
    std::vector<std::string> monitored_processes; // 监控的进程列表
};
```

**健康状态评估：**
```cpp
enum class SystemHealth {
    HEALTHY,        // 健康状态 - 所有功能正常
    WARNING,        // 警告状态 - 存在非关键问题
    ERROR,          // 错误状态 - 存在影响功能的问题
    CRITICAL        // 严重状态 - 系统功能严重受损
};
```

### 工业级守护进程管理优化

基于您的专业建议，系统实现了工业级的守护进程管理机制，显著提升了系统的健壮性和可靠性。

#### 进程组管理机制

**进程组创建：**
```cpp
bool DaemonManager::StartDaemonProcessWithGroup(DaemonType type) {
    pid_t pid = fork();
    if (pid == 0) {
        // 子进程：创建新的进程组和会话
        if (setsid() == -1) {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                     "setsid失败: " + std::string(strerror(errno)));
            _exit(1);
        }
        // 继续执行守护进程...
    }
    // 父进程处理...
}
```

**进程组终止：**
```cpp
bool DaemonManager::StopDaemonProcessGroup(DaemonType type) {
    // 向整个进程组发送SIGTERM信号
    if (kill(-pid, SIGTERM) == -1) {
        // 错误处理
    }
    
    // 如果进程仍未退出，发送SIGKILL信号到整个进程组
    if (kill(-pid, SIGKILL) == -1) {
        // 错误处理
    }
}
```

#### SIGCHLD信号处理机制

**信号处理器安装：**
```cpp
bool DaemonManager::InstallSigchldHandler() {
    struct sigaction sa;
    sa.sa_handler = sigchld_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = SA_RESTART | SA_NOCLDSTOP;
    
    if (sigaction(SIGCHLD, &sa, nullptr) == -1) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "安装SIGCHLD信号处理器失败: " + std::string(strerror(errno)));
        return false;
    }
    return true;
}
```

**子进程状态处理：**
```cpp
void DaemonManager::ProcessChildExit(pid_t pid, int status) {
    // 分析退出状态
    if (WIFEXITED(status)) {
        int exit_code = WEXITSTATUS(status);
        // 处理正常退出
    } else if (WIFSIGNALED(status)) {
        int signal_num = WTERMSIG(status);
        // 处理信号终止
        REPORT_ERROR_WITH_DETAILS(ErrorType::DAEMON_CRASH, ErrorSeverity::CRITICAL,
                                 LogComponent::DAEMON_MANAGER,
                                 "守护进程被信号终止", 
                                 "信号: " + std::to_string(signal_num));
    }
    
    // 触发自动重启机制
    if (daemon.config.auto_restart && 
        daemon.restart_count < daemon.config.max_restart_attempts) {
        // 执行自动重启
    }
}
```

#### 增强的错误反馈机制

**操作结果枚举：**
```cpp
enum class DaemonOperationResult {
    SUCCESS,                    // 操作成功
    ALREADY_RUNNING,           // 进程已在运行
    ALREADY_STOPPED,           // 进程已停止
    EXECUTABLE_NOT_FOUND,      // 可执行文件未找到
    PERMISSION_DENIED,         // 权限不足
    FORK_FAILED,              // fork失败
    CONFIG_ERROR,             // 配置错误
    TIMEOUT,                  // 操作超时
    SIGNAL_FAILED,            // 信号发送失败
    UNKNOWN_DAEMON_TYPE,      // 未知守护进程类型
    PROCESS_NOT_RESPONDING,   // 进程无响应
    RESOURCE_UNAVAILABLE,     // 资源不可用
    UNKNOWN_ERROR             // 未知错误
};
```

**详细错误信息结构：**
```cpp
struct DaemonOperationInfo {
    DaemonOperationResult result;
    std::string message;
    int error_code;
    std::string details;
    
    bool IsSuccess() const { return result == DaemonOperationResult::SUCCESS; }
    std::string ToString() const;
};
```

### 集成测试和验证

**日志系统测试：**
- 多级别日志记录测试
- 异步日志性能测试
- 日志轮转和压缩测试
- 日志搜索功能测试

**错误处理系统测试：**
- 错误检测和分类测试
- 自动恢复机制测试
- 系统健康监控测试
- 告警通知功能测试

**守护进程管理测试：**
- 进程组管理测试
- SIGCHLD信号处理测试
- 自动重启机制测试
- 错误反馈机制测试

## API接口规范

### REST API详细设计

#### 系统状态API
```http
GET /api/v1/status
Response: {
  "system": {
    "state": "LOCKED",
    "uptime": 86400,
    "version": "1.0.0",
    "platform": "linux-x86_64"
  },
  "timing": {
    "current_source": "GNSS",
    "accuracy_ns": 50,
    "stability_ppm": 1e-12,
    "phase_offset_ns": 12.5,
    "frequency_offset_ppm": 0.001
  },
  "sources": [
    {
      "type": "GNSS",
      "status": "ACTIVE",
      "quality": 95,
      "satellites": 12,
      "signal_strength": -142
    },
    {
      "type": "RUBIDIUM",
      "status": "STANDBY",
      "temperature": 65.2,
      "frequency_offset": 0.0001
    }
  ]
}
```

#### 配置管理API
```http
GET /api/v1/config
PUT /api/v1/config
POST /api/v1/config/validate
GET /api/v1/config/schema
```

#### 日志查询API
```http
GET /api/v1/logs?level=ERROR&from=2024-01-01&to=2024-01-02&limit=100
Response: {
  "logs": [
    {
      "timestamp": "2024-01-01T12:00:00Z",
      "level": "ERROR",
      "component": "GNSS",
      "message": "Signal lost",
      "context": {
        "satellites": 3,
        "snr": -150
      }
    }
  ],
  "total": 1,
  "has_more": false
}
```

### WebSocket事件规范

#### 连接建立
```javascript
// 客户端连接
const ws = new WebSocket('ws://localhost:8080/ws/status');

// 认证消息
ws.send(JSON.stringify({
  type: 'auth',
  token: 'jwt_token_here'
}));
```

#### 实时状态推送
```json
{
  "type": "status_update",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "state_change": {
      "from": "DISCIPLINING",
      "to": "LOCKED",
      "reason": "convergence_achieved"
    },
    "metrics": {
      "phase_offset_ns": 8.2,
      "frequency_offset_ppm": 0.0005
    }
  }
}
```

#### 告警事件
```json
{
  "type": "alarm",
  "severity": "WARNING",
  "component": "GNSS",
  "message": "Signal quality degraded",
  "timestamp": "2024-01-01T12:00:00Z",
  "context": {
    "satellites": 4,
    "previous_satellites": 12
  }
}
```

## 数据库设计

### SQLite数据库模式

#### 性能指标表 (metrics)
```sql
CREATE TABLE metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    metric_type TEXT NOT NULL,
    source TEXT NOT NULL,
    value REAL NOT NULL,
    unit TEXT NOT NULL,
    quality INTEGER DEFAULT 100,
    INDEX idx_timestamp (timestamp),
    INDEX idx_metric_type (metric_type),
    INDEX idx_source (source)
);
```

#### 事件日志表 (events)
```sql
CREATE TABLE events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    level TEXT NOT NULL,
    component TEXT NOT NULL,
    event_type TEXT NOT NULL,
    message TEXT NOT NULL,
    context TEXT, -- JSON格式的上下文数据
    INDEX idx_timestamp (timestamp),
    INDEX idx_level (level),
    INDEX idx_component (component)
);
```

#### 状态转换表 (state_transitions)
```sql
CREATE TABLE state_transitions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    from_state TEXT NOT NULL,
    to_state TEXT NOT NULL,
    trigger_event TEXT NOT NULL,
    duration_ms INTEGER,
    success BOOLEAN NOT NULL,
    INDEX idx_timestamp (timestamp),
    INDEX idx_states (from_state, to_state)
);
```

#### 铷钟学习数据表 (rubidium_learning)
```sql
CREATE TABLE rubidium_learning (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    temperature REAL NOT NULL,
    frequency_offset REAL NOT NULL,
    aging_rate REAL,
    confidence REAL NOT NULL,
    learning_duration INTEGER NOT NULL,
    INDEX idx_timestamp (timestamp)
);
```

### 数据保留策略

| 数据类型 | 保留期限 | 采样间隔 | 压缩策略 |
|----------|----------|----------|----------|
| 实时指标 | 24小时 | 1秒 | 无压缩 |
| 小时统计 | 30天 | 1小时 | 平均值/最大值/最小值 |
| 日统计 | 1年 | 1天 | 平均值/最大值/最小值 |
| 事件日志 | 90天 | 事件驱动 | gzip压缩 |
| 学习数据 | 永久 | 状态变化时 | 无压缩 |

## 性能指标和监控

### 关键性能指标 (KPI)

#### 时间精度指标
- **绝对时间精度**: ±50ns (GNSS锁定状态)
- **相对时间稳定度**: 1×10⁻¹² (1秒平均)
- **守时精度**: ±1μs (24小时守时期间)
- **状态转换时间**: <30秒 (冷启动到锁定)

#### 系统性能指标
- **CPU使用率**: <5% (正常运行)
- **内存使用**: <100MB (包含缓存)
- **磁盘I/O**: <1MB/s (日志和数据库)
- **网络延迟**: <1ms (本地API调用)

#### 可用性指标
- **系统可用性**: 99.9% (年度)
- **MTBF**: >8760小时 (1年)
- **MTTR**: <5分钟 (自动恢复)
- **数据完整性**: 99.99%

### 监控告警阈值

```cpp
struct AlarmThresholds {
    // 时间精度告警
    double phase_offset_warning_ns = 100.0;    // 相位偏移警告阈值
    double phase_offset_critical_ns = 500.0;   // 相位偏移严重阈值
    double frequency_offset_warning_ppm = 0.01; // 频率偏移警告阈值
    double frequency_offset_critical_ppm = 0.1; // 频率偏移严重阈值
    
    // 信号质量告警
    int gnss_satellites_warning = 6;           // GNSS卫星数警告阈值
    int gnss_satellites_critical = 4;          // GNSS卫星数严重阈值
    double gnss_snr_warning_db = -145.0;       // 信噪比警告阈值
    double gnss_snr_critical_db = -150.0;      // 信噪比严重阈值
    
    // 系统资源告警
    double cpu_usage_warning = 80.0;           // CPU使用率警告阈值
    double memory_usage_warning = 80.0;        // 内存使用率警告阈值
    double disk_usage_warning = 85.0;          // 磁盘使用率警告阈值
    
    // 温度告警
    double rubidium_temp_warning = 70.0;       // 铷钟温度警告阈值
    double rubidium_temp_critical = 75.0;      // 铷钟温度严重阈值
};
```

## 安全架构设计

### 认证和授权

#### JWT令牌结构
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "admin",
    "role": "administrator",
    "permissions": ["read", "write", "config"],
    "iat": 1640995200,
    "exp": 1641081600
  }
}
```

#### 权限级别定义
```cpp
enum class Permission {
    READ_STATUS = 1,        // 读取系统状态
    READ_LOGS = 2,          // 读取日志
    WRITE_CONFIG = 4,       // 修改配置
    CONTROL_SYSTEM = 8,     // 控制系统启停
    ADMIN_USERS = 16        // 用户管理
};

enum class Role {
    VIEWER = READ_STATUS | READ_LOGS,
    OPERATOR = VIEWER | WRITE_CONFIG,
    ADMINISTRATOR = OPERATOR | CONTROL_SYSTEM | ADMIN_USERS
};
```

### 网络安全

#### HTTPS配置
```cpp
struct TLSConfig {
    std::string cert_file = "/etc/timing-server/ssl/server.crt";
    std::string key_file = "/etc/timing-server/ssl/server.key";
    std::string ca_file = "/etc/timing-server/ssl/ca.crt";
    std::vector<std::string> cipher_suites = {
        "ECDHE-RSA-AES256-GCM-SHA384",
        "ECDHE-RSA-AES128-GCM-SHA256"
    };
    std::string min_tls_version = "1.2";
};
```

#### 访问控制
```cpp
struct AccessControl {
    std::vector<std::string> allowed_ips;      // IP白名单
    int max_requests_per_minute = 60;          // 频率限制
    int max_concurrent_connections = 10;       // 并发连接限制
    int session_timeout_minutes = 30;          // 会话超时
    bool require_client_cert = false;          // 是否需要客户端证书
};
```

## 配置管理详细设计

### 配置文件结构

#### 主配置文件 (/etc/timing-server/config.json)
```json
{
  "system": {
    "log_level": "INFO",
    "data_dir": "/var/lib/timing-server",
    "pid_file": "/var/run/timing-server.pid"
  },
  "timing": {
    "sources": {
      "gnss": {
        "enabled": true,
        "device": "/dev/ttyS0",
        "baudrate": 9600,
        "priority": 1
      },
      "rubidium": {
        "enabled": true,
        "device": "/dev/spidev0.0",
        "priority": 2
      },
      "rtc": {
        "enabled": true,
        "device": "/dev/rtc0",
        "priority": 5
      }
    },
    "disciplining": {
      "convergence_threshold_ns": 50.0,
      "holdover_timeout_hours": 24,
      "learning_duration_hours": 72
    }
  },
  "api": {
    "http_port": 8080,
    "https_port": 8443,
    "websocket_port": 8081,
    "max_connections": 100
  },
  "security": {
    "jwt_secret": "your-secret-key",
    "session_timeout": 1800,
    "require_https": true
  }
}
```

#### 守护进程配置模板

**PTP4L配置模板**
```ini
[global]
clockClass {{clock_class}}
clockAccuracy {{clock_accuracy}}
offsetScaledLogVariance {{log_variance}}
priority1 {{priority1}}
priority2 {{priority2}}
domainNumber {{domain}}
slaveOnly 0
masterOnly 1

[{{interface}}]
network_transport L2
delay_mechanism E2E
```

**Chrony配置模板**
```conf
# PHC作为主要参考源
refclock PHC {{phc_device}} poll 0 dpoll 0 offset 0

# GNSS作为备用参考源  
refclock SHM 0 poll 0 dpoll 0 offset {{gnss_offset}}

# 服务器配置
port 123
bindaddress {{bind_address}}
allow {{allowed_networks}}

# 日志配置
logdir /var/log/chrony
log measurements statistics tracking
```

### 配置验证规则

```cpp
class ConfigValidator {
public:
    struct ValidationRule {
        std::string path;
        std::function<bool(const json&)> validator;
        std::string error_message;
    };
    
    std::vector<ValidationRule> rules = {
        {
            "timing.sources.gnss.baudrate",
            [](const json& val) { 
                return val.is_number() && val >= 1200 && val <= 115200; 
            },
            "GNSS baudrate must be between 1200 and 115200"
        },
        {
            "timing.disciplining.convergence_threshold_ns",
            [](const json& val) { 
                return val.is_number() && val > 0 && val < 1000; 
            },
            "Convergence threshold must be between 0 and 1000 ns"
        },
        {
            "api.http_port",
            [](const json& val) { 
                return val.is_number() && val >= 1024 && val <= 65535; 
            },
            "HTTP port must be between 1024 and 65535"
        }
    };
};
```

## 部署架构

### 系统部署图

```mermaid
graph TB
    subgraph "硬件层"
        GPS[GPS天线]
        RB_OSC[铷振荡器]
        RTC_CHIP[RTC芯片]
        NIC_CARD[网卡E810]
    end
    
    subgraph "操作系统层"
        KERNEL[Linux内核]
        PPS_DRIVER[PPS驱动]
        PHC_DRIVER[PHC驱动]
        SERIAL_DRIVER[串口驱动]
    end
    
    subgraph "应用层"
        TIMING_SERVER[授时服务器]
        PTP4L[ptp4l守护进程]
        CHRONY[chrony守护进程]
        TS2PHC[ts2phc守护进程]
    end
    
    subgraph "管理层"
        WEB_UI[Web管理界面]
        API_SERVER[API服务器]
        DATABASE[SQLite数据库]
    end
    
    subgraph "网络层"
        PTP_CLIENTS[PTP客户端]
        NTP_CLIENTS[NTP客户端]
        MGMT_CLIENTS[管理客户端]
    end
    
    GPS --> PPS_DRIVER
    RB_OSC --> SERIAL_DRIVER
    RTC_CHIP --> KERNEL
    NIC_CARD --> PHC_DRIVER
    
    PPS_DRIVER --> TS2PHC
    PHC_DRIVER --> PTP4L
    SERIAL_DRIVER --> TIMING_SERVER
    
    TIMING_SERVER --> API_SERVER
    API_SERVER --> WEB_UI
    API_SERVER --> DATABASE
    
    PTP4L --> PTP_CLIENTS
    CHRONY --> NTP_CLIENTS
    WEB_UI --> MGMT_CLIENTS
```

### 多平台构建系统

#### CMake跨平台构建配置

**主CMakeLists.txt结构：**
```cmake
cmake_minimum_required(VERSION 3.16)
project(timing-server VERSION 1.0.0)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 平台检测
if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "loongarch64")
        set(PLATFORM_NAME "loongarch64-linux")
        set(USE_LOONGARCH_HAL ON)
    else()
        set(PLATFORM_NAME "x86_64-linux")
        set(USE_LINUX_HAL ON)
    endif()
elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
    set(PLATFORM_NAME "x86_64-darwin")
    set(USE_MOCK_HAL ON)
endif()

# 条件编译HAL实现
if(USE_LINUX_HAL)
    add_subdirectory(src/hal/linux)
elseif(USE_LOONGARCH_HAL)
    add_subdirectory(src/hal/loongson)
elseif(USE_MOCK_HAL)
    add_subdirectory(src/hal/mock)
endif()

# 核心库
add_subdirectory(src/core)
add_subdirectory(src/api)

# 主程序
add_executable(timing-server src/main.cpp)
target_link_libraries(timing-server 
    timing-core 
    timing-api 
    timing-hal
)
```

**龙芯交叉编译工具链文件 (build_scripts/toolchains/loongarch64-linux-gnu.cmake)：**
```cmake
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR loongarch64)

# 工具链路径
set(TOOLCHAIN_PREFIX loongarch64-linux-gnu)
set(CMAKE_C_COMPILER ${TOOLCHAIN_PREFIX}-gcc)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_PREFIX}-g++)

# 系统根目录
set(CMAKE_FIND_ROOT_PATH /opt/loongarch64-linux-gnu)
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

# 编译标志
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -march=loongarch64 -mabi=lp64d")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -march=loongarch64 -mabi=lp64d")

# 链接标志
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -static-libgcc -static-libstdc++")
```

#### 自动化构建脚本

**构建脚本 (build_scripts/build.sh)：**
```bash
#!/bin/bash

PLATFORM=${1:-"native"}
BUILD_TYPE=${2:-"Release"}
BUILD_DIR="build-${PLATFORM}-${BUILD_TYPE}"

case $PLATFORM in
    "loongarch64")
        CMAKE_ARGS="-DCMAKE_TOOLCHAIN_FILE=build_scripts/toolchains/loongarch64-linux-gnu.cmake"
        ;;
    "linux")
        CMAKE_ARGS=""
        ;;
    "darwin"|"native")
        CMAKE_ARGS=""
        ;;
    *)
        echo "Unsupported platform: $PLATFORM"
        exit 1
        ;;
esac

# 创建构建目录
mkdir -p $BUILD_DIR
cd $BUILD_DIR

# 配置和构建
cmake .. -DCMAKE_BUILD_TYPE=$BUILD_TYPE $CMAKE_ARGS
make -j$(nproc)

# 运行测试
if [ "$BUILD_TYPE" = "Debug" ]; then
    ctest --output-on-failure
fi

# 打包
make package
```

### 系统服务集成

#### Systemd服务单元文件

**主服务文件 (platform/systemd/timing-server.service)：**
```ini
[Unit]
Description=High-Precision Timing Server
Documentation=https://docs.timing-server.com
After=network.target time-sync.target
Wants=network.target
Requires=time-sync.target

[Service]
Type=notify
User=timing-server
Group=timing-server
ExecStart=/usr/local/bin/timing-server --config /etc/timing-server/config.json
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/timing-server /var/log/timing-server
PrivateTmp=true

# 资源限制
LimitNOFILE=65536
LimitMEMLOCK=infinity

# 实时调度
Nice=-10
IOSchedulingClass=1
IOSchedulingPriority=4

[Install]
WantedBy=multi-user.target
```

**安装脚本 (platform/install.sh)：**
```bash
#!/bin/bash

# 创建用户和组
useradd -r -s /bin/false timing-server

# 创建目录
mkdir -p /etc/timing-server
mkdir -p /var/lib/timing-server
mkdir -p /var/log/timing-server

# 设置权限
chown timing-server:timing-server /var/lib/timing-server
chown timing-server:timing-server /var/log/timing-server

# 安装二进制文件
cp timing-server /usr/local/bin/
chmod +x /usr/local/bin/timing-server

# 安装配置文件
cp config/config.json /etc/timing-server/
cp config/ptp4l.conf.template /etc/timing-server/
cp config/chrony.conf.template /etc/timing-server/

# 安装systemd服务
cp platform/systemd/timing-server.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable timing-server

echo "Installation completed. Start service with: systemctl start timing-server"
```

## 测试策略

### 单元测试
- **HAL层测试**: 模拟硬件接口，验证抽象层正确性
- **算法测试**: 验证驯服算法的收敛性和稳定性
- **状态机测试**: 验证状态转换逻辑和事件处理
- **API测试**: 验证REST接口和WebSocket通信

### 集成测试
- **端到端测试**: 模拟完整的时钟驯服流程
- **故障注入测试**: 验证错误处理和恢复机制
- **性能测试**: 验证系统在各种负载下的表现
- **兼容性测试**: 验证不同平台和硬件的兼容性

### 硬件在环测试
- **时间精度测试**: 使用高精度测试设备验证输出精度
- **长期稳定性测试**: 长时间运行验证系统稳定性
- **环境适应性测试**: 验证温度、湿度等环境因素的影响

### 测试工具和框架
- **单元测试**: Google Test (gtest)
- **模拟框架**: Google Mock (gmock)
- **性能分析**: Valgrind, perf
- **代码覆盖率**: gcov, lcov
- **静态分析**: Clang Static Analyzer, Cppcheck

## UI/UX设计规范

### 苹果风格设计系统

#### 视觉设计原则

**设计语言特征：**
- **简洁性**: 去除不必要的装饰元素，专注于功能和内容
- **层次感**: 通过毛玻璃效果和阴影创建清晰的视觉层次
- **一致性**: 统一的颜色、字体、间距和交互模式
- **优雅性**: 精致的细节处理和流畅的动画效果

**核心视觉元素：**
```css
/* 毛玻璃效果基础样式 */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 深色模式毛玻璃 */
.glass-morphism-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 按钮样式 */
.apple-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.apple-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
}
```

#### 颜色系统

**主色调配置：**
```scss
// 主色调
$primary-blue: #007AFF;
$primary-purple: #5856D6;
$primary-green: #34C759;
$primary-orange: #FF9500;
$primary-red: #FF3B30;

// 中性色
$gray-50: #F9FAFB;
$gray-100: #F3F4F6;
$gray-200: #E5E7EB;
$gray-300: #D1D5DB;
$gray-400: #9CA3AF;
$gray-500: #6B7280;
$gray-600: #4B5563;
$gray-700: #374151;
$gray-800: #1F2937;
$gray-900: #111827;

// 毛玻璃背景色
$glass-light: rgba(255, 255, 255, 0.1);
$glass-dark: rgba(0, 0, 0, 0.2);
$glass-border-light: rgba(255, 255, 255, 0.2);
$glass-border-dark: rgba(255, 255, 255, 0.1);
```

#### 组件设计规范

**卡片组件设计：**
```vue
<template>
  <div class="apple-card" :class="{ 'dark': isDarkMode }">
    <div class="card-header">
      <h3 class="card-title">{{ title }}</h3>
      <div class="card-actions">
        <slot name="actions"></slot>
      </div>
    </div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.apple-card {
  @apply glass-morphism;
  padding: 24px;
  margin: 16px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.apple-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}
</style>
```

**状态指示器设计：**
```vue
<template>
  <div class="status-indicator" :class="statusClass">
    <div class="status-dot"></div>
    <span class="status-text">{{ statusText }}</span>
  </div>
</template>

<style scoped>
.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.locked .status-dot {
  background: #34C759;
}

.status-indicator.disciplining .status-dot {
  background: #FF9500;
}

.status-indicator.holdover .status-dot {
  background: #FF3B30;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
</style>
```

#### 动画和过渡效果

**页面切换动画：**
```css
/* 页面进入动画 */
.page-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.page-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 模态框动画 */
.modal-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.6, 1);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
```

**微交互效果：**
```css
/* 按钮点击反馈 */
.interactive-element {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:active {
  transform: scale(0.98);
}

/* 悬停效果 */
.hoverable {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hoverable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}
```

#### 响应式设计

**断点系统：**
```scss
// 断点定义
$breakpoints: (
  'xs': 0,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px
);

// 响应式混合器
@mixin respond-to($breakpoint) {
  @media (min-width: map-get($breakpoints, $breakpoint)) {
    @content;
  }
}

// 使用示例
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  
  @include respond-to('md') {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include respond-to('lg') {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @include respond-to('xl') {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

#### 深色模式支持

**主题切换系统：**
```typescript
// 主题管理器
export class ThemeManager {
  private isDark = false;
  
  constructor() {
    this.initTheme();
  }
  
  private initTheme() {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    this.isDark = savedTheme === 'dark' || (!savedTheme && prefersDark);
    this.applyTheme();
  }
  
  toggleTheme() {
    this.isDark = !this.isDark;
    this.applyTheme();
    localStorage.setItem('theme', this.isDark ? 'dark' : 'light');
  }
  
  private applyTheme() {
    document.documentElement.classList.toggle('dark', this.isDark);
  }
}
```

**CSS变量系统：**
```css
:root {
  /* 浅色模式 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1a202c;
  --text-secondary: #4a5568;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

:root.dark {
  /* 深色模式 */
  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --glass-bg: rgba(0, 0, 0, 0.2);
  --glass-border: rgba(255, 255, 255, 0.1);
}
```

### 用户体验优化

#### 性能优化策略

**懒加载和代码分割：**
```typescript
// 路由懒加载
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/views/Dashboard.vue')
  },
  {
    path: '/config',
    component: () => import('@/views/Configuration.vue')
  },
  {
    path: '/logs',
    component: () => import('@/views/SystemLogs.vue')
  }
];

// 组件懒加载
const LazyChart = defineAsyncComponent(() => import('@/components/Chart.vue'));
```

**图片和资源优化：**
```typescript
// 图片懒加载指令
const vLazyLoad = {
  mounted(el: HTMLImageElement, binding: any) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          el.src = binding.value;
          observer.unobserve(el);
        }
      });
    });
    observer.observe(el);
  }
};
```

#### 可访问性设计

**键盘导航支持：**
```css
/* 焦点样式 */
.focusable:focus {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
  border-radius: 4px;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #007AFF;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}
```

**ARIA标签和语义化：**
```vue
<template>
  <div class="status-card" role="region" :aria-label="cardTitle">
    <h3 :id="titleId">{{ title }}</h3>
    <div class="status-value" :aria-describedby="titleId">
      {{ value }}
    </div>
    <div class="status-trend" :aria-label="trendDescription">
      <span :aria-hidden="true">{{ trendIcon }}</span>
      {{ trendText }}
    </div>
  </div>
</template>
```

## 代码规范与维护性设计

### 中文注释标准

#### C++代码注释规范

**类和接口注释：**
```cpp
/**
 * @brief GNSS接收机硬件抽象接口
 * 
 * 该接口定义了与GNSS接收机交互的标准方法，支持NMEA数据读取、
 * 信号质量监控和卫星信息获取。实现类需要处理不同厂商的GNSS
 * 接收机差异，提供统一的访问接口。
 * 
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0.0
 */
class I_GnssReceiver {
public:
    /**
     * @brief 初始化GNSS接收机
     * 
     * 执行接收机的初始化操作，包括串口配置、波特率设置、
     * 消息格式配置等。初始化成功后，接收机应能正常输出
     * NMEA格式的定位数据。
     * 
     * @return true 初始化成功
     * @return false 初始化失败，可能是设备不存在或配置错误
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 读取一条NMEA语句
     * 
     * 从GNSS接收机读取一条完整的NMEA语句。该方法会阻塞等待
     * 直到接收到完整的语句（以\r\n结尾）或发生超时。
     * 
     * @return std::string NMEA语句内容，如果读取失败返回空字符串
     * 
     * @note 返回的字符串不包含行结束符
     * @warning 该方法可能阻塞，建议在独立线程中调用
     */
    virtual std::string ReadNmeaSentence() = 0;
    
    /**
     * @brief 检查GNSS信号是否有效
     * 
     * 判断当前GNSS接收机是否接收到有效的卫星信号。
     * 有效信号的判断标准包括：至少4颗卫星、定位质量良好、
     * 时间同步状态正常等。
     * 
     * @return true 信号有效，可用于时间同步
     * @return false 信号无效或质量不佳
     */
    virtual bool IsSignalValid() = 0;
};
```

**函数实现注释：**
```cpp
/**
 * @brief 时钟状态机状态转换处理
 * 
 * 根据当前系统状态和触发事件，执行状态机的状态转换。
 * 状态转换遵循严格的规则：
 * 1. FreeRun -> Disciplining: 检测到有效GNSS信号
 * 2. Disciplining -> Locked: 驯服算法收敛
 * 3. Locked -> Holdover: GNSS信号丢失
 * 4. Holdover -> Disciplining: GNSS信号恢复
 * 
 * @param event 触发状态转换的事件
 * @return true 状态转换成功
 * @return false 状态转换失败或不允许的转换
 */
bool ClockStateMachine::processStateTransition(const ClockEvent& event) {
    ClockState currentState = getCurrentState();
    ClockState targetState = determineTargetState(currentState, event);
    
    // 验证状态转换的合法性
    if (!isValidTransition(currentState, targetState)) {
        LOG_WARNING("非法的状态转换: {} -> {}", 
                   stateToString(currentState), 
                   stateToString(targetState));
        return false;
    }
    
    // 执行状态退出处理
    if (!exitState(currentState)) {
        LOG_ERROR("状态退出处理失败: {}", stateToString(currentState));
        return false;
    }
    
    // 更新状态
    setState(targetState);
    
    // 执行状态进入处理
    if (!enterState(targetState)) {
        LOG_ERROR("状态进入处理失败: {}", stateToString(targetState));
        // 尝试回滚到原状态
        setState(currentState);
        return false;
    }
    
    // 记录状态转换事件
    logStateTransition(currentState, targetState, event);
    
    return true;
}
```

**复杂算法注释：**
```cpp
/**
 * @brief PLL时钟驯服算法实现
 * 
 * 使用比例积分（PI）控制器对本地时钟进行驯服，使其与
 * GNSS参考时钟保持同步。算法原理：
 * 
 * 1. 相位检测：计算本地时钟与参考时钟的相位差
 * 2. 环路滤波：使用PI控制器处理相位误差
 * 3. 频率调整：根据控制器输出调整本地振荡器频率
 * 
 * 控制器传递函数：
 * H(s) = Kp + Ki/s
 * 其中 Kp 为比例增益，Ki 为积分增益
 * 
 * @param phaseError 相位误差（纳秒）
 * @param deltaTime 采样间隔（秒）
 * @return double 频率调整量（ppm）
 */
double PLLDiscipliningAlgorithm::calculateFrequencyAdjustment(
    double phaseError, double deltaTime) {
    
    // 比例项：直接响应当前相位误差
    double proportionalTerm = m_kp * phaseError;
    
    // 积分项：累积历史误差，消除稳态误差
    m_integralSum += phaseError * deltaTime;
    
    // 防止积分饱和
    if (m_integralSum > m_integralLimit) {
        m_integralSum = m_integralLimit;
    } else if (m_integralSum < -m_integralLimit) {
        m_integralSum = -m_integralLimit;
    }
    
    double integralTerm = m_ki * m_integralSum;
    
    // 计算总的控制输出
    double controlOutput = proportionalTerm + integralTerm;
    
    // 限制输出范围，防止过度调整
    if (controlOutput > m_maxAdjustment) {
        controlOutput = m_maxAdjustment;
    } else if (controlOutput < -m_maxAdjustment) {
        controlOutput = -m_maxAdjustment;
    }
    
    // 记录调试信息
    LOG_DEBUG("PLL控制器: 相位误差={:.2f}ns, 比例项={:.6f}ppm, "
              "积分项={:.6f}ppm, 输出={:.6f}ppm", 
              phaseError, proportionalTerm, integralTerm, controlOutput);
    
    return controlOutput;
}
```

#### JavaScript/TypeScript注释规范

**Vue组件注释：**
```typescript
/**
 * @fileoverview 系统状态监控仪表盘组件
 * 
 * 该组件负责显示授时服务器的实时状态信息，包括：
 * - 当前时钟状态（自由运行/驯服中/锁定/守时）
 * - 各时间源的状态和质量指标
 * - 系统性能指标和告警信息
 * - 实时图表和历史趋势
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */

import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import { useTimingStore } from '@/stores/timing';
import { WebSocketManager } from '@/utils/websocket';

/**
 * 系统状态仪表盘组件
 */
export default defineComponent({
  name: 'SystemDashboard',
  
  setup() {
    // 响应式数据定义
    const timingStore = useTimingStore();
    const wsManager = ref<WebSocketManager | null>(null);
    const isConnected = ref(false);
    
    /**
     * 初始化WebSocket连接
     * 
     * 建立与后端的WebSocket连接，用于接收实时状态更新。
     * 连接建立后会自动订阅状态变化事件。
     */
    const initializeWebSocket = () => {
      wsManager.value = new WebSocketManager('/ws/status');
      
      // 连接成功处理
      wsManager.value.onConnect(() => {
        isConnected.value = true;
        console.log('WebSocket连接已建立');
        
        // 订阅状态更新事件
        wsManager.value?.subscribe('status_update', handleStatusUpdate);
        wsManager.value?.subscribe('alarm', handleAlarmEvent);
      });
      
      // 连接断开处理
      wsManager.value.onDisconnect(() => {
        isConnected.value = false;
        console.warn('WebSocket连接已断开，尝试重连...');
      });
      
      // 建立连接
      wsManager.value.connect();
    };
    
    /**
     * 处理状态更新事件
     * 
     * 当收到后端推送的状态更新时，更新本地状态存储。
     * 状态更新包括时钟状态变化、性能指标更新等。
     * 
     * @param data 状态更新数据
     */
    const handleStatusUpdate = (data: any) => {
      // 更新时钟状态
      if (data.state_change) {
        timingStore.updateClockState(data.state_change);
        
        // 显示状态变化通知
        showStateChangeNotification(
          data.state_change.from, 
          data.state_change.to,
          data.state_change.reason
        );
      }
      
      // 更新性能指标
      if (data.metrics) {
        timingStore.updateMetrics(data.metrics);
      }
    };
    
    /**
     * 显示状态变化通知
     * 
     * 在界面上显示时钟状态变化的通知消息，
     * 帮助用户了解系统状态的变化情况。
     * 
     * @param fromState 原状态
     * @param toState 目标状态
     * @param reason 变化原因
     */
    const showStateChangeNotification = (
      fromState: string, 
      toState: string, 
      reason: string
    ) => {
      const stateNames = {
        'FREERUN': '自由运行',
        'DISCIPLINING': '驯服中',
        'LOCKED': '锁定',
        'HOLDOVER': '守时'
      };
      
      const message = `时钟状态已从"${stateNames[fromState]}"切换到"${stateNames[toState]}"`;
      
      // 根据状态变化类型选择通知样式
      const notificationType = toState === 'LOCKED' ? 'success' : 
                              toState === 'HOLDOVER' ? 'warning' : 'info';
      
      // 显示通知（假设使用某个通知库）
      showNotification({
        type: notificationType,
        title: '系统状态变化',
        message: message,
        duration: 5000
      });
    };
    
    // 组件挂载时初始化
    onMounted(() => {
      initializeWebSocket();
      // 获取初始状态数据
      timingStore.fetchSystemStatus();
    });
    
    // 组件卸载时清理资源
    onUnmounted(() => {
      wsManager.value?.disconnect();
    });
    
    return {
      timingStore,
      isConnected,
      // ... 其他返回值
    };
  }
});
```

### 代码可读性设计原则

#### 命名规范

**C++命名约定：**
```cpp
// 类名：大驼峰命名法，使用描述性名称
class TimingEngineController;
class GnssSignalProcessor;
class ClockStateMachine;

// 接口名：以I_开头
class I_GnssReceiver;
class I_AtomicClock;

// 成员变量：m_前缀 + 小驼峰
class TimingEngine {
private:
    std::unique_ptr<I_GnssReceiver> m_gnssReceiver;  // GNSS接收机实例
    ClockState m_currentState;                       // 当前时钟状态
    double m_phaseOffset;                           // 相位偏移量（纳秒）
    std::chrono::steady_clock::time_point m_lastUpdate; // 上次更新时间
};

// 函数名：小驼峰命名法，动词开头
bool initializeHardware();
void updateClockState();
double calculatePhaseError();
std::string formatTimestamp();

// 常量：全大写，下划线分隔
const double MAX_PHASE_ERROR_NS = 1000.0;
const int DEFAULT_POLLING_INTERVAL_MS = 1000;
const std::string CONFIG_FILE_PATH = "/etc/timing-server/config.json";

// 枚举：大驼峰，值全大写
enum class ClockState {
    FREE_RUN,      // 自由运行
    DISCIPLINING,  // 驯服中
    LOCKED,        // 锁定
    HOLDOVER       // 守时
};
```

**JavaScript/TypeScript命名约定：**
```typescript
// 类名：大驼峰命名法
class WebSocketManager {
  // 私有成员：#前缀
  #socket: WebSocket | null = null;
  #reconnectTimer: number | null = null;
  
  // 公共方法：小驼峰命名法
  public connect(): void { }
  public disconnect(): void { }
  public sendMessage(data: any): void { }
}

// 接口名：大驼峰，I前缀（可选）
interface SystemStatus {
  clockState: ClockState;
  activeTimeSource: TimeSource;
  performanceMetrics: PerformanceMetrics;
}

// 函数名：小驼峰，动词开头
function formatTimestamp(timestamp: number): string { }
function calculateUptime(startTime: Date): number { }
function validateConfiguration(config: TimingConfig): boolean { }

// 常量：全大写，下划线分隔
const API_BASE_URL = '/api/v1';
const WEBSOCKET_RECONNECT_DELAY = 5000;
const MAX_RETRY_ATTEMPTS = 3;

// Vue组件：大驼峰命名
const SystemDashboard = defineComponent({ });
const ConfigurationPanel = defineComponent({ });
```

### 错误处理和日志规范

**统一错误处理：**
```cpp
// include/timeserver/common/error_codes.h
namespace timeserver {
namespace common {

/**
 * @brief 系统错误代码定义
 */
enum class ErrorCode {
    SUCCESS = 0,                    // 成功
    
    // 硬件相关错误 (1000-1999)
    HARDWARE_NOT_FOUND = 1001,     // 硬件设备未找到
    HARDWARE_INIT_FAILED = 1002,   // 硬件初始化失败
    GNSS_SIGNAL_LOST = 1003,       // GNSS信号丢失
    ATOMIC_CLOCK_FAULT = 1004,     // 原子钟故障
    
    // 配置相关错误 (2000-2999)
    CONFIG_FILE_NOT_FOUND = 2001,  // 配置文件未找到
    CONFIG_PARSE_ERROR = 2002,     // 配置解析错误
    CONFIG_VALIDATION_FAILED = 2003, // 配置验证失败
    
    // 网络相关错误 (3000-3999)
    NETWORK_CONNECTION_FAILED = 3001, // 网络连接失败
    API_REQUEST_TIMEOUT = 3002,     // API请求超时
    WEBSOCKET_CONNECTION_LOST = 3003, // WebSocket连接丢失
};

/**
 * @brief 获取错误代码的中文描述
 * @param code 错误代码
 * @return std::string 错误描述
 */
std::string getErrorDescription(ErrorCode code);

} // namespace common
} // namespace timeserver
```

**结构化日志系统：**
```cpp
// include/timeserver/common/logger.h
#include <spdlog/spdlog.h>
#include <spdlog/fmt/fmt.h>

namespace timeserver {
namespace common {

/**
 * @brief 日志记录器封装类
 * 
 * 提供统一的日志记录接口，支持不同级别的日志输出，
 * 自动添加时间戳、线程ID、组件名称等上下文信息。
 */
class Logger {
public:
    /**
     * @brief 记录调试信息
     * @param format 格式化字符串
     * @param args 参数列表
     */
    template<typename... Args>
    static void debug(const std::string& format, Args&&... args) {
        spdlog::debug(format, std::forward<Args>(args)...);
    }
    
    /**
     * @brief 记录一般信息
     * @param format 格式化字符串
     * @param args 参数列表
     */
    template<typename... Args>
    static void info(const std::string& format, Args&&... args) {
        spdlog::info(format, std::forward<Args>(args)...);
    }
    
    /**
     * @brief 记录警告信息
     * @param format 格式化字符串
     * @param args 参数列表
     */
    template<typename... Args>
    static void warning(const std::string& format, Args&&... args) {
        spdlog::warn(format, std::forward<Args>(args)...);
    }
    
    /**
     * @brief 记录错误信息
     * @param format 格式化字符串
     * @param args 参数列表
     */
    template<typename... Args>
    static void error(const std::string& format, Args&&... args) {
        spdlog::error(format, std::forward<Args>(args)...);
    }
};

// 便捷宏定义
#define LOG_DEBUG(format, ...) Logger::debug(format, ##__VA_ARGS__)
#define LOG_INFO(format, ...)  Logger::info(format, ##__VA_ARGS__)
#define LOG_WARN(format, ...)  Logger::warning(format, ##__VA_ARGS__)
#define LOG_ERROR(format, ...) Logger::error(format, ##__VA_ARGS__)

} // namespace common
} // namespace timeserver
```