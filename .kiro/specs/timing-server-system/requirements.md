# 需求文档

## 简介

本文档概述了一个综合授时服务器系统的需求，该系统提供高精度时间同步服务。系统集成多种时间源，包括GNSS接收机、铷原子钟、高精度RTC、外部1PPS和10MHz信号，并管理标准的Linux授时守护进程。系统采用三层架构，包括硬件抽象层、核心授时服务和基于Web的管理界面。

### 系统目标
- 提供纳秒级时间同步精度（±50ns）
- 支持PTP特级主时钟和NTP Stratum 1服务
- 实现99.9%系统可用性
- 支持Linux x86_64和龙芯LoongArch64平台
- 提供24小时无GNSS信号守时能力

### 关键性能指标
- 绝对时间精度：±50ns（GNSS锁定状态）
- 守时精度：±1μs（24小时无外部参考）
- 状态转换时间：<30秒（冷启动到锁定）
- 系统资源占用：<100MB内存，<5% CPU
- 网络延迟：<1ms（本地API调用）

## 需求

### 需求 1

**用户故事：** 作为系统管理员，我希望通过统一界面配置和监控多个时间源，以确保最佳的时间同步精度和可靠性。

#### 验收标准

1. 当系统启动时，系统应自动检测并初始化所有可用的时间源（GNSS、铷钟、RTC、1PPS、10MHz）
2. 当时间源不可用时，系统应自动切换到下一个最佳可用源
3. 当时间源质量发生变化时，系统应记录事件并更新管理界面
4. 如果有多个时间源可用，系统应根据预定义的优先级和质量指标选择最准确的源

### 需求 2

**用户故事：** 作为授时工程师，我希望系统在不同平台上提供硬件抽象，以便相同的软件可以在Linux和龙芯系统上无需修改即可运行。

#### 验收标准

1. 当系统在不同平台上运行时，HAL应提供相同的接口，无论底层硬件差异如何
2. 当硬件驱动程序更改时，只有HAL层需要更新，核心服务不需要更改
3. 当添加新硬件时，HAL应支持基于插件的驱动程序集成
4. 如果平台特定的设备路径不同，HAL应透明地抽象这些差异

### 需求 3

**用户故事：** 作为网络管理员，我希望授时引擎管理标准的Linux授时守护进程，以便在添加高级功能的同时利用现有的授时基础设施。

#### 验收标准

1. 当授时引擎启动时，应自动配置和管理ptp4l和chrony守护进程
2. 当需要调整授时参数时，系统应动态重新配置受管理的守护进程
3. 当守护进程冲突发生时，系统应自动解决或警告管理员
4. 如果受管理的守护进程失败，系统应尝试重启和回退程序

### 需求 4

**用户故事：** 作为系统操作员，我希望通过Web界面进行实时监控和控制，以便远程管理授时服务器并快速响应问题。

#### 验收标准

1. 当访问Web界面时，用户应看到所有时间源和系统健康状况的实时状态
2. 当系统参数更改时，界面应通过WebSocket连接自动更新
3. 当进行配置更改时，应立即验证并应用更改
4. 如果发生严重错误，界面应显示突出的警报和建议的操作

### 需求 5

**用户故事：** 作为维护工程师，我希望有全面的结构化日志记录和智能错误处理系统，以便快速分析系统性能、排除故障并实现自动恢复。

#### 验收标准

1. 当系统事件发生时，应记录纳秒级时间戳、多级别严重性（TRACE/DEBUG/INFO/WARNING/ERROR/CRITICAL）和结构化上下文信息
2. 当收集性能指标时，应支持异步日志处理、多种输出方式（文件/控制台/系统日志）并存储用于历史分析
3. 当日志达到大小限制时，应自动轮转、压缩历史文件并保留可配置数量的备份文件
4. 如果需要日志分析，系统应提供高级搜索功能，支持按级别、组件、时间范围、消息模式进行过滤和查询
5. 当系统错误发生时，应自动分类错误类型、评估严重程度并触发相应的恢复策略（重试/故障切换/组件重启）
6. 当错误恢复失败时，系统应发送告警通知、记录详细故障信息并支持手动干预机制

### 需求 6

**用户故事：** 作为系统集成商，我希望系统作为无头服务运行，以便在嵌入式设备上可靠运行而无需用户交互。

#### 验收标准

1. 当系统启动时，应自动初始化所有服务而无需用户输入
2. 当无头运行时，所有核心授时功能应在没有Web界面的情况下正常运行
3. 当系统资源有限时，核心服务应优先考虑授时精度而非管理功能
4. 如果Web界面不可用，核心授时服务应继续不间断运行

### 需求 7

**用户故事：** 作为安全管理员，我希望管理界面有安全访问控制，以便只有授权人员可以修改授时配置。

#### 验收标准

1. 当用户访问管理界面时，应使用有效凭据进行身份验证
2. 当执行配置更改时，用户应具有适当的授权级别
3. 当进行API调用时，应验证并记录以供审计
4. 如果尝试未授权访问，系统应记录尝试并可选择阻止源

### 需求 8

**用户故事：** 作为系统架构师，我希望模块化和可扩展的设计，以便在不中断现有功能的情况下添加新功能和硬件支持。

#### 验收标准

1. 当添加新时间源时，应通过现有HAL接口集成
2. 当更新核心算法时，不应影响HAL或表示层
3. 当增强Web界面时，不应需要更改核心授时引擎
4. 如果需要第三方集成，API应提供稳定的版本化端点

### 需求 9

**用户故事：** 作为时间同步专家，我希望系统提供精确的时间精度指标和性能监控，以便验证系统是否满足严格的时间同步要求。

#### 验收标准

1. 当系统处于锁定状态时，绝对时间精度应保持在±50纳秒以内
2. 当进行守时操作时，24小时内时间漂移应控制在±1微秒以内
3. 当测量频率稳定度时，1秒平均Allan偏差应优于1×10⁻¹²
4. 如果精度超出阈值，系统应立即触发告警并记录详细诊断信息

### 需求 10

**用户故事：** 作为网络时间服务提供商，我希望系统能够同时提供PTP和NTP服务，以支持不同类型的客户端设备。

#### 验收标准

1. 当配置为PTP特级主时钟时，系统应宣告clockClass 6（GPS锁定）或clockClass 7（守时模式）
2. 当提供NTP服务时，系统应作为Stratum 1服务器运行，参考源标识为GPS
3. 当同时运行PTP和NTP时，两种协议的时间输出应保持一致（<100ns差异）
4. 如果外部参考丢失，系统应自动调整PTP clockClass为适当的守时等级

### 需求 11

**用户故事：** 作为系统运维人员，我希望系统具备完善的故障检测和自动恢复能力，以最小化人工干预需求。

#### 验收标准

1. 当GNSS信号中断时，系统应在5秒内检测到并切换到守时模式
2. 当硬件故障发生时，系统应自动隔离故障组件并使用备用时间源
3. 当软件异常导致守护进程崩溃时，系统应在30秒内自动重启相关服务
4. 如果关键错误无法自动恢复，系统应发送告警通知并记录详细故障信息

### 需求 12

**用户故事：** 作为合规审计员，我希望系统提供完整的操作审计日志，以满足时间同步服务的合规性要求。

#### 验收标准

1. 当进行任何配置更改时，系统应记录操作者身份、时间戳和具体变更内容
2. 当系统状态发生转换时，应记录转换原因、持续时间和相关性能指标
3. 当发生安全相关事件时，应生成不可篡改的审计记录
4. 如果需要合规报告，系统应能够生成指定时间段的完整操作历史

### 需求 13

**用户故事：** 作为嵌入式系统开发者，我希望系统支持资源受限环境，以便在工业控制器和边缘设备上部署。

#### 验收标准

1. 当运行在最小配置时，系统内存占用应不超过64MB
2. 当CPU资源有限时，核心授时功能应优先获得计算资源
3. 当存储空间不足时，系统应自动清理历史数据并保留关键配置
4. 如果系统资源严重不足，应降级运行并发出资源告警

### 需求 14

**用户故事：** 作为时间同步网络架构师，我希望系统支持冗余部署和负载均衡，以构建高可用的时间同步基础设施。

#### 验收标准

1. 当部署多个授时服务器时，系统应支持主备模式和负载分担模式
2. 当主服务器故障时，备用服务器应在60秒内接管服务
3. 当网络分区发生时，每个分区应能独立提供时间服务
4. 如果检测到时间源冲突，系统应提供仲裁机制选择最优参考源

### 需求 15

**用户故事：** 作为系统集成测试工程师，我希望系统提供完善的测试接口和模拟功能，以支持自动化测试和验证。

#### 验收标准

1. 当运行在测试模式时，系统应支持模拟各种硬件状态和故障场景
2. 当进行性能测试时，系统应提供详细的内部状态和性能计数器
3. 当执行集成测试时，系统应支持确定性的时间注入和状态控制
4. 如果测试发现问题，系统应提供详细的诊断信息和错误上下文

### 需求 16

**用户故事：** 作为构建工程师，我希望系统支持多平台自动化构建和部署，以便为不同的目标平台（Linux x86_64、龙芯LoongArch64、macOS开发环境）提供一致的构建体验。

#### 验收标准

1. 当为龙芯LoongArch64平台构建时，系统应使用专门的交叉编译工具链和CMake工具链文件
2. 当在macOS上开发时，系统应自动使用Mock HAL实现，无需修改核心代码
3. 当执行CI/CD流程时，系统应自动检测目标平台并选择相应的构建配置
4. 如果构建失败，系统应提供详细的错误信息和平台特定的诊断建议

### 需求 17

**用户故事：** 作为部署工程师，我希望系统提供标准化的部署包和安装脚本，以简化在不同环境中的部署过程。

#### 验收标准

1. 当生成部署包时，系统应包含所有必要的二进制文件、配置文件和依赖库
2. 当在目标系统上安装时，安装脚本应自动检测硬件配置并生成适配的配置文件
3. 当进行系统服务注册时，应自动创建systemd服务单元并配置开机自启动
4. 如果部署环境不满足要求，安装程序应提供清晰的错误信息和解决建议

### 需求 18

**用户故事：** 作为质量保证工程师，我希望系统具备完善的版本管理和发布流程，以确保软件质量和可追溯性。

#### 验收标准

1. 当构建发布版本时，系统应自动嵌入版本号、构建时间和Git提交哈希
2. 当进行版本升级时，系统应支持配置文件迁移和数据库模式升级
3. 当发布补丁版本时，系统应支持热更新机制，最小化服务中断
4. 如果版本不兼容，系统应提供明确的升级路径和回滚机制

### 需求 19

**用户故事：** 作为API开发者，我希望系统提供高性能、轻量级的RESTful API和WebSocket接口，以支持远程监控和实时数据传输。

#### 验收标准

1. 当处理API请求时，系统响应时间应在10毫秒以内（本地网络）
2. 当建立WebSocket连接时，系统应支持至少100个并发连接
3. 当传输实时数据时，WebSocket延迟应低于50毫秒
4. 如果API服务异常，核心授时功能应不受影响并继续正常运行

### 需求 20

**用户故事：** 作为前端开发者，我希望API提供标准化的JSON数据格式和完整的错误处理机制，以简化客户端开发工作。

#### 验收标准

1. 当返回API响应时，所有数据应使用一致的JSON格式和命名约定
2. 当发生错误时，API应返回标准的HTTP状态码和详细的错误信息
3. 当进行数据验证时，API应提供具体的字段级错误描述
4. 如果API版本更新，应保持向后兼容性或提供清晰的迁移指南

### 需求 21

**用户故事：** 作为系统安全专家，我希望API具备完善的安全防护机制，以保护授时服务器免受网络攻击。

#### 验收标准

1. 当访问敏感API时，系统应要求有效的JWT令牌认证
2. 当检测到异常访问模式时，系统应自动触发防护机制
3. 当传输敏感数据时，必须使用HTTPS加密连接
4. 如果发现安全威胁，系统应记录详细的安全事件并可选择性阻断攻击源

### 需求 22

**用户故事：** 作为API集成开发者，我希望系统提供完整的API规范和数据契约定义，以确保客户端和服务端的数据交换一致性。

#### 验收标准

1. 当定义API端点时，每个端点应有明确的HTTP方法、URI路径和数据格式规范
2. 当设计数据传输对象时，应提供完整的JSON Schema定义和示例
3. 当发生API错误时，应返回标准化的错误响应格式和错误代码
4. 如果API版本升级，应提供向后兼容性保证和迁移文档

### 需求 23

**用户故事：** 作为运维监控工程师，我希望系统提供丰富的监控指标和健康检查接口，以便集成到现有的监控系统中。

#### 验收标准

1. 当查询系统指标时，API应提供CPU、内存、磁盘使用率等基础监控数据
2. 当进行健康检查时，系统应返回各个组件的运行状态和依赖关系
3. 当收集性能数据时，API应支持时间范围查询和数据聚合功能
4. 如果系统异常，健康检查接口应返回具体的故障信息和建议操作

### 需求 24

**用户故事：** 作为Linux系统管理员，我希望系统能够智能管理和配置标准的授时守护进程，以确保最佳的时间同步性能和配置一致性。

#### 验收标准

1. 当系统状态变化时，应自动生成并应用相应的chrony、ptp4l、ts2phc配置文件
2. 当配置PTP特级主时钟时，clockClass应根据时间源状态动态调整（GPS锁定=6，守时=7）
3. 当配置NTP Stratum 1服务时，应正确设置PHC作为主参考源，GNSS作为监控源
4. 如果守护进程配置冲突，系统应自动检测并解决端口、资源竞争问题

### 需求 25

**用户故事：** 作为时间同步技术专家，我希望系统配置能够充分利用硬件特性，以实现最佳的时间同步精度和稳定性。

#### 验收标准

1. 当使用Intel E810等高端网卡时，系统应自动配置DPLL和10MHz频率基准
2. 当配置ts2phc时，应正确设置1PPS信号极性、校正值和引脚映射
3. 当优化chrony性能时，应启用内存锁定、实时调度和适当的轮询间隔
4. 如果硬件不支持某些特性，系统应自动降级到兼容配置并记录警告

### 需求 26

**用户故事：** 作为PTP网络架构师，我希望系统能够根据运行状态动态调整PTP配置参数，以向网络正确宣告时钟质量和精度等级。

#### 验收标准

1. 当系统处于GNSS锁定状态时，PTP clockClass应设置为6，clockAccuracy为0x21（25ns）
2. 当系统进入守时模式时，应自动调整clockClass为7，clockAccuracy为0x25（1μs）
3. 当系统正在驯服过程中时，应设置clockClass为52，表示正在同步状态
4. 如果配置更新失败，系统应回滚到上一个有效配置并记录错误信息

### 需求 27

**用户故事：** 作为用户体验设计师，我希望Web管理界面采用现代化的苹果风格设计，具备毛玻璃质感和优雅的视觉效果，以提供专业且美观的用户体验。

#### 验收标准

1. 当设计界面布局时，应采用苹果风格的设计语言，包括圆角、阴影、渐变等视觉元素
2. 当实现界面背景时，应使用毛玻璃（frosted glass）效果，包括背景模糊和半透明层叠
3. 当设计交互元素时，应具备流畅的动画过渡效果和微交互反馈
4. 如果浏览器不支持某些视觉效果，应提供优雅的降级方案并保持基本功能可用

### 需求 28

**用户故事：** 作为代码维护工程师，我希望项目代码具备高可读性和易维护性，使用简体中文注释，以便团队成员能够快速理解和维护代码。

#### 验收标准

1. 当编写代码注释时，必须使用简体中文，包括类、函数、变量和复杂逻辑的说明
2. 当设计代码结构时，应遵循清晰的命名规范和模块化设计原则
3. 当实现复杂算法时，应提供详细的中文注释说明算法原理和实现步骤
4. 如果代码涉及英文缩写或专业术语，应在注释中提供中文解释和上下文说明

### 需求 29

**用户故事：** 作为系统运维专家，我希望守护进程管理系统具备工业级的健壮性和可靠性，以确保在生产环境中能够稳定运行并快速响应异常情况。

#### 验收标准

1. 当启动守护进程时，系统应使用进程组管理（setsid）创建独立会话，确保子进程及其后代进程能够被完整清理
2. 当守护进程异常退出时，系统应通过SIGCHLD信号处理器立即检测到状态变化，防止僵尸进程产生并支持自动重启策略
3. 当停止守护进程时，系统应向整个进程组发送信号（kill(-pid, signal)），确保所有相关进程都被正确终止
4. 如果守护进程操作失败，系统应提供详细的错误分类和上下文信息，包括具体的失败原因、错误代码和建议的解决方案
5. 当系统检测到守护进程崩溃时，应自动报告错误、分析崩溃原因并根据配置的恢复策略执行相应的恢复操作
6. 如果守护进程频繁重启或恢复失败，系统应实施退避策略、记录详细诊断信息并通知运维人员进行人工干预

## 多平台构建与部署策略

### 4.1 针对龙芯LoongArch64的交叉编译

为龙芯LoongArch64架构进行交叉编译是本项目的核心技术挑战之一。这需要专门配置的工具链和精确定义的CMake工具链文件。

**实施方案：**
- 项目包含`build_scripts/toolchains/loongarch64-linux-gnu.cmake`工具链文件
- 通过`-DCMAKE_TOOLCHAIN_FILE`参数指定工具链文件，CMake自动切换到交叉编译模式
- 工具链文件定义编译器、链接器、系统根（sysroot）以及LoongArch特定的编译和链接标志
- 支持静态链接和动态链接两种部署模式

### 4.2 开发环境支持

**macOS开发环境：**
- 使用Mock HAL实现，从文件或网络读取模拟数据
- 支持完整的开发、测试和调试流程
- 自动检测平台并选择相应的HAL实现

**Linux开发环境：**
- 支持本地编译和交叉编译两种模式
- 提供开发用的模拟硬件接口
- 集成调试工具和性能分析工具

### 4.3 CI/CD集成

**自动化构建流程：**
- 支持GitHub Actions、GitLab CI等主流CI/CD平台
- 自动检测目标平台并选择相应的构建配置
- 并行构建多个目标平台的二进制文件
- 自动运行单元测试和集成测试

**质量保证：**
- 静态代码分析（Clang Static Analyzer、Cppcheck）
- 代码覆盖率检测（gcov、lcov）
- 内存泄漏检测（Valgrind、AddressSanitizer）
- 性能基准测试和回归检测

## API设计与技术架构

### 5.1 Web API框架选型

为了实现对授时服务器的远程监控和配置，需要一个稳定、高效的API。考虑到嵌入式环境的资源限制，选择轻量级、零依赖的C++ Web框架至关重要。

**技术选型：**
- **oatpp框架**：高性能、低内存占用、无外部依赖，现代化的C++ API
- **适用场景**：嵌入式设备上的RESTful API服务
- **优势**：零依赖、内存占用小、支持异步处理

### 5.2 API设计原则

API遵循RESTful设计原则，使用标准的HTTP方法（GET, PUT, POST, DELETE）和JSON数据格式，确保易于理解和集成。

**核心端点设计：**

**状态查询 (`/api/v1/status`)**：
- 通过GET请求返回包含系统所有关键信息的JSON对象
- 包含当前主时间源、锁定状态、所有参考源的偏移/抖动/漂移统计
- 提供PTP和NTP客户端连接数、CPU、内存等系统资源占用率

**配置管理 (`/api/v1/config/ptp`, `/api/v1/config/ntp`)**：
- GET请求获取当前PTP或NTP配置参数
- PUT请求提交新配置，包含参数校验和安全重启机制
- 支持配置版本控制和回滚功能

**日志服务 (`/api/v1/logs`)**：
- GET请求获取系统日志和守护进程日志
- 支持分页、级别过滤、时间范围查询
- 提供日志搜索和导出功能

**实时数据流 (`/ws/status`)**：
- WebSocket端点提供实时状态更新
- 通过IPC从核心授时引擎接收状态变化
- 立即推送JSON状态对象给所有连接的客户端
- 支持客户端订阅特定事件类型

### 5.3 数据格式规范

**状态响应格式示例：**
```json
{
  "system": {
    "state": "LOCKED",
    "uptime": 86400,
    "version": "1.0.0",
    "platform": "loongarch64-linux"
  },
  "timing": {
    "current_source": "GNSS",
    "accuracy_ns": 50,
    "stability_ppm": 1e-12,
    "phase_offset_ns": 12.5,
    "frequency_offset_ppm": 0.001
  },
  "sources": [
    {
      "type": "GNSS",
      "status": "ACTIVE",
      "quality": 95,
      "satellites": 12,
      "signal_strength": -142
    }
  ]
}
```

**配置请求格式示例：**
```json
{
  "ptp": {
    "domain": 0,
    "priority1": 128,
    "priority2": 128,
    "clockClass": 6,
    "clockAccuracy": 0x20,
    "interface": "eth0"
  }
}
```

### 5.4 安全与认证

**JWT令牌认证：**
- 基于JSON Web Token的无状态认证
- 支持角色基础的访问控制（RBAC）
- 令牌自动刷新和安全过期机制

**HTTPS支持：**
- 强制HTTPS连接，支持TLS 1.2+
- 客户端证书验证（可选）
- 安全密码套件配置

**访问控制：**
- IP白名单和黑名单
- 请求频率限制
- 并发连接数限制
- 会话超时管理

### 5.5 REST API端点定义

| HTTP方法 | URI路径 | 请求体Schema | 成功响应(代码 & Schema) | 描述 |
|----------|---------|-------------|----------------------|------|
| GET | `/api/v1/status` | N/A | 200 OK (JSON: SystemStatusDto) | 获取系统当前完整状态 |
| GET | `/api/v1/config/ptp` | N/A | 200 OK (JSON: PtpConfigDto) | 获取当前PTP配置 |
| PUT | `/api/v1/config/ptp` | JSON: PtpConfigDto | 200 OK (JSON: PtpConfigDto) | 更新PTP配置 |
| GET | `/api/v1/config/ntp` | N/A | 200 OK (JSON: NtpConfigDto) | 获取当前NTP配置 |
| PUT | `/api/v1/config/ntp` | JSON: NtpConfigDto | 200 OK (JSON: NtpConfigDto) | 更新NTP配置 |
| GET | `/api/v1/logs` | Query Params: level, page | 200 OK (JSON: LogEntriesDto) | 获取系统日志 |
| POST | `/api/v1/auth/login` | JSON: LoginRequestDto | 200 OK (JSON: AuthResponseDto) | 用户身份认证 |
| POST | `/api/v1/auth/refresh` | JSON: RefreshTokenDto | 200 OK (JSON: AuthResponseDto) | 刷新访问令牌 |
| GET | `/api/v1/health` | N/A | 200 OK (JSON: HealthStatusDto) | 系统健康检查 |
| POST | `/api/v1/system/restart` | N/A | 202 Accepted | 重启系统服务 |
| GET | `/api/v1/metrics` | Query Params: from, to | 200 OK (JSON: MetricsDto) | 获取性能指标 |
| WebSocket | `/ws/status` | N/A | Stream of SystemStatusDto JSON | 实时状态更新推送 |

### 5.6 数据传输对象(DTO)定义

**SystemStatusDto结构：**
```json
{
  "system": {
    "state": "LOCKED|DISCIPLINING|HOLDOVER|FREERUN",
    "uptime_seconds": 86400,
    "version": "1.0.0",
    "platform": "loongarch64-linux",
    "cpu_usage_percent": 2.5,
    "memory_usage_mb": 45,
    "disk_usage_percent": 15.2
  },
  "timing": {
    "current_source": "GNSS|RUBIDIUM|RTC|EXTERNAL_PPS|EXTERNAL_10MHZ",
    "accuracy_ns": 50.0,
    "stability_ppm": 1e-12,
    "phase_offset_ns": 12.5,
    "frequency_offset_ppm": 0.001,
    "holdover_duration_seconds": 0
  },
  "sources": [
    {
      "type": "GNSS",
      "status": "ACTIVE|STANDBY|FAULT|UNAVAILABLE",
      "quality": 95,
      "satellites": 12,
      "signal_strength_dbm": -142,
      "last_update": "2024-01-01T12:00:00Z"
    }
  ],
  "services": {
    "ptp4l": {
      "status": "RUNNING|STOPPED|ERROR",
      "clients_count": 25,
      "clock_class": 6
    },
    "chrony": {
      "status": "RUNNING|STOPPED|ERROR",
      "clients_count": 150,
      "stratum": 1
    }
  }
}
```

**PtpConfigDto结构：**
```json
{
  "domain": 0,
  "priority1": 128,
  "priority2": 128,
  "clock_class": 6,
  "clock_accuracy": 32,
  "offset_scaled_log_variance": 65535,
  "interface": "eth0",
  "network_transport": "L2",
  "delay_mechanism": "E2E",
  "announce_interval": 1,
  "sync_interval": 0,
  "delay_req_interval": 0
}
```

**NtpConfigDto结构：**
```json
{
  "stratum": 1,
  "reference_id": "GPS",
  "server_address": "0.0.0.0",
  "server_port": 123,
  "allowed_networks": ["***********/24", "10.0.0.0/8"],
  "max_clients": 1000,
  "poll_interval": 6,
  "precision": -20
}
```

**LogEntriesDto结构：**
```json
{
  "logs": [
    {
      "timestamp": "2024-01-01T12:00:00Z",
      "level": "INFO|WARNING|ERROR|CRITICAL",
      "component": "GNSS|TIMING_ENGINE|PTP4L|CHRONY|API",
      "message": "Signal acquired successfully",
      "context": {
        "satellites": 12,
        "snr": -142
      }
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 10,
    "total_entries": 1000,
    "entries_per_page": 100
  }
}
```

**AuthResponseDto结构：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user": {
    "username": "admin",
    "role": "administrator",
    "permissions": ["read", "write", "config", "admin"]
  }
}
```

### 5.7 错误响应格式

**标准错误响应结构：**
```json
{
  "error": {
    "code": "INVALID_CONFIG",
    "message": "PTP domain value must be between 0 and 255",
    "details": {
      "field": "domain",
      "provided_value": 300,
      "valid_range": "0-255"
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789"
  }
}
```

**常见错误代码：**
- `400 BAD_REQUEST`: 请求参数无效
- `401 UNAUTHORIZED`: 身份认证失败
- `403 FORBIDDEN`: 权限不足
- `404 NOT_FOUND`: 资源不存在
- `409 CONFLICT`: 配置冲突
- `422 UNPROCESSABLE_ENTITY`: 数据验证失败
- `500 INTERNAL_SERVER_ERROR`: 服务器内部错误
- `503 SERVICE_UNAVAILABLE`: 服务暂时不可用

## 系统配置规范

### 6.1 Chrony NTP Stratum 1配置示例

为了实现高精度的NTP Stratum 1服务，系统需要精确配置chrony守护进程。以下是关键配置指令及其技术原理：

**核心配置指令：**

| 指令 | 值 | 理由 |
|------|----|----- |
| `refclock PHC /dev/ptp0 poll 0 prefer refid PTP` | 将/dev/ptp0的PHC作为首选(prefer)参考时钟 | poll 0表示尽可能快地轮询(约1s)，refid PTP为该源命名 |
| `refclock SHM 0 offset 0.0 delay 0.2 refid GPS noselect` | 引用gpsd的共享内存段0（NMEA） | noselect使其不用于同步，仅用于监控和交叉验证 |
| `local stratum 1` | 允许以Stratum 1身份提供服务 | 当chrony没有更高层级服务器时，直接引用硬件时钟 |
| `allow ***********/24` | 允许指定子网的客户端同步 | 控制NTP服务的访问权限 |
| `log tracking measurements statistics` | 开启详细日志记录 | 便于性能分析和故障排查 |
| `lock_all` | 将chronyd进程锁定在内存中 | 避免被交换到磁盘，减少延迟，提升响应稳定性 |

**完整配置文件示例：**
```conf
# PHC作为主要参考源
refclock PHC /dev/ptp0 poll 0 dpoll 0 offset 0 prefer refid PTP

# GNSS作为监控参考源
refclock SHM 0 offset 0.0 delay 0.2 refid GPS noselect

# Stratum 1服务配置
local stratum 1
stratumweight 0

# 网络服务配置
port 123
bindaddress 0.0.0.0
allow ***********/24
allow 10.0.0.0/8
deny all

# 客户端限制
clientloglimit 1000
ratelimit interval 1 burst 16 leak 2

# 性能优化
lock_all
sched_priority 50
maxupdateskew 100.0
makestep 1.0 3

# 日志配置
logdir /var/log/chrony
log tracking measurements statistics
logchange 0.5
```

### 6.2 PTP4L特级主时钟配置

**关键配置参数：**

| 参数 | 值 | 说明 |
|------|----|----- |
| `clockClass` | 6 (GPS锁定) / 7 (守时模式) | 动态调整，反映当前时间源状态 |
| `clockAccuracy` | 0x20 (25ns) | 声明的时间精度等级 |
| `offsetScaledLogVariance` | 0x4E5D | 基于实际测量的时间变化率 |
| `priority1` | 128 | 特级主时钟优先级 |
| `priority2` | 128 | 次级优先级 |
| `domainNumber` | 0 | 默认PTP域 |
| `slaveOnly` | 0 | 配置为主时钟模式 |
| `masterOnly` | 1 | 仅作为主时钟运行 |

**动态配置生成逻辑：**
```cpp
class PtpConfigGenerator {
public:
    PtpConfig generateConfig(ClockState state, TimeSource activeSource) {
        PtpConfig config;
        
        // 根据系统状态动态设置clockClass
        switch(state) {
            case ClockState::LOCKED:
                config.clockClass = (activeSource == TimeSource::GNSS) ? 6 : 7;
                break;
            case ClockState::HOLDOVER:
                config.clockClass = 7;
                break;
            default:
                config.clockClass = 248; // 默认值
        }
        
        // 根据实际精度设置clockAccuracy
        config.clockAccuracy = calculateAccuracy(state, activeSource);
        
        return config;
    }
};
```

### 6.3 TS2PHC时间戳同步配置

**核心配置示例：**
```conf
[global]
use_syslog 1
verbose 1
logging_level 6
ts2phc.pulsewidth 500000000

# GNSS 1PPS输入配置
[/dev/pps0]
ts2phc.extts_polarity rising
ts2phc.extts_correction 0

# PHC输出配置  
[eth0]
ts2phc.pin_index 0
```

**10MHz频率基准配置：**
```conf
# Intel E810网卡DPLL配置
[eth0]
ts2phc.dpll_pin 1
ts2phc.dpll_state automatic
```

### 6.4 PTP4L特级主时钟详细配置示例

**完整ptp4l.conf配置文件：**

| 参数 | 值 | 理由 |
|------|----|----- |
| `[global]` | 全局配置段 | - |
| `time_stamping` | `hardware` | 启用硬件时间戳，这是实现高精度的关键 |
| `priority1` | `1` | 设置极高的主时钟优先级，确保设备成为网络中的特级主时钟 |
| `priority2` | `1` | 在priority1相同时，作为次级比较条件，同样设置为最高优先级 |
| `clockClass` | `6` | 当被GNSS锁定时，设置为6，表示时间可追溯至PRC（Primary Reference Clock） |
| `clockAccuracy` | `0x21` | 表示纳秒级精度（25ns以内），向从设备宣告自身的高精度 |
| `masterOnly` | `1` | 强制设备只作为主时钟运行，不参与从时钟选举，增加系统确定性 |
| `[ethX]` | 特定网络接口配置段 | - |
| `network_transport` | `UDPv4` | 使用UDP/IPv4作为PTP报文的传输协议，兼容性最广 |
| `delay_mechanism` | `E2E` | 使用端到端（End-to-End）延迟测量机制，适用于大多数网络环境 |

**完整配置文件示例：**
```conf
[global]
# 硬件时间戳配置
time_stamping hardware
tx_timestamp_timeout 50
check_fup_sync 0

# 特级主时钟优先级配置
priority1 1
priority2 1
clockClass 6
clockAccuracy 0x21
offsetScaledLogVariance 0x4E5D

# 主时钟模式配置
masterOnly 1
slaveOnly 0
twoStepFlag 1

# 域和身份配置
domainNumber 0
clockIdentity 000000.FFFE.000001

# 报文间隔配置（log2秒）
logAnnounceInterval 1    # 2秒
logSyncInterval 0        # 1秒
logMinDelayReqInterval 0 # 1秒

# 超时配置
announceReceiptTimeout 3
syncReceiptTimeout 0

# 性能优化
assume_two_step 0
check_fup_sync 0
clock_servo pi
pi_proportional_const 0.0
pi_integral_const 0.0

# 日志配置
logging_level 6
verbose 1
use_syslog 1

# 网络接口配置
[eth0]
network_transport UDPv4
delay_mechanism E2E
```

### 6.5 动态配置管理策略

**状态驱动的配置更新：**
```cpp
class ConfigurationManager {
public:
    void updatePtpConfig(ClockState state, TimeSource source) {
        PtpConfig config = loadBaseConfig();
        
        // 根据系统状态动态调整关键参数
        switch(state) {
            case ClockState::LOCKED:
                if(source == TimeSource::GNSS) {
                    config.clockClass = 6;      // GPS锁定
                    config.clockAccuracy = 0x21; // 25ns
                } else {
                    config.clockClass = 7;      // 其他高质量源
                    config.clockAccuracy = 0x22; // 100ns
                }
                break;
                
            case ClockState::HOLDOVER:
                config.clockClass = 7;          // 守时模式
                config.clockAccuracy = 0x25;   // 1μs
                break;
                
            case ClockState::DISCIPLINING:
                config.clockClass = 52;         // 驯服中
                config.clockAccuracy = 0x31;   // 未知精度
                break;
                
            default:
                config.clockClass = 248;        // 默认值
                config.clockAccuracy = 0xFE;   // 未知
        }
        
        // 应用配置并重启服务
        applyConfiguration(config);
        restartPtp4lService();
    }
    
private:
    void applyConfiguration(const PtpConfig& config) {
        std::ofstream configFile("/etc/ptp4l.conf");
        configFile << generateConfigContent(config);
        configFile.close();
    }
};
```

### 6.6 网络接口特定优化

**Intel E810系列网卡优化：**
```conf
# E810特定优化配置
[eth0]
# 启用硬件时间戳
time_stamping hardware

# 优化时间戳精度
tx_timestamp_timeout 50
rx_timestamp_timeout 50

# DPLL配置（如果支持）
# 这些参数通过ts2phc配置，而非ptp4l
```

**通用网卡兼容配置：**
```conf
[eth0]
# 基础硬件时间戳
time_stamping hardware

# 保守的超时设置
tx_timestamp_timeout 100
rx_timestamp_timeout 100

# 标准传输配置
network_transport UDPv4
delay_mechanism E2E
```