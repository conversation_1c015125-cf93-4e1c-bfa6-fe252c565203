#!/bin/bash

# 平台兼容性验证脚本
# 用于验证不同平台的构建和运行兼容性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测当前平台
detect_platform() {
    local os_name=$(uname -s)
    local arch=$(uname -m)
    
    case "$os_name" in
        Linux)
            case "$arch" in
                x86_64)
                    PLATFORM="linux-x86_64"
                    PLATFORM_DESC="Linux x86_64"
                    ;;
                loongarch64)
                    PLATFORM="linux-loongarch64"
                    PLATFORM_DESC="Linux 龙芯LoongArch64"
                    ;;
                *)
                    log_error "不支持的Linux架构: $arch"
                    exit 1
                    ;;
            esac
            ;;
        Darwin)
            case "$arch" in
                x86_64)
                    PLATFORM="macos-x86_64"
                    PLATFORM_DESC="macOS x86_64"
                    ;;
                arm64)
                    PLATFORM="macos-arm64"
                    PLATFORM_DESC="macOS ARM64"
                    ;;
                *)
                    log_error "不支持的macOS架构: $arch"
                    exit 1
                    ;;
            esac
            ;;
        *)
            log_error "不支持的操作系统: $os_name"
            exit 1
            ;;
    esac
    
    log_info "检测到平台: $PLATFORM_DESC"
}

# 检查构建依赖
check_build_dependencies() {
    log_info "检查构建依赖..."
    
    local missing_deps=()
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        missing_deps+=("cmake")
    else
        local cmake_version=$(cmake --version | head -n1 | cut -d' ' -f3)
        log_info "CMake版本: $cmake_version"
    fi
    
    # 检查编译器
    if [[ "$PLATFORM" == linux-* ]]; then
        if ! command -v gcc &> /dev/null; then
            missing_deps+=("gcc")
        else
            local gcc_version=$(gcc --version | head -n1)
            log_info "GCC版本: $gcc_version"
        fi
        
        if ! command -v g++ &> /dev/null; then
            missing_deps+=("g++")
        fi
    elif [[ "$PLATFORM" == macos-* ]]; then
        if ! command -v clang &> /dev/null; then
            missing_deps+=("clang")
        else
            local clang_version=$(clang --version | head -n1)
            log_info "Clang版本: $clang_version"
        fi
    fi
    
    # 检查pkg-config
    if ! command -v pkg-config &> /dev/null; then
        missing_deps+=("pkg-config")
    fi
    
    # 检查平台特定依赖
    if [[ "$PLATFORM" == linux-* ]]; then
        # 检查SQLite3开发库
        if ! pkg-config --exists sqlite3; then
            missing_deps+=("libsqlite3-dev")
        fi
        
        # 检查线程库
        if ! ldconfig -p | grep -q libpthread; then
            missing_deps+=("libpthread-dev")
        fi
    elif [[ "$PLATFORM" == macos-* ]]; then
        # 检查Homebrew
        if ! command -v brew &> /dev/null; then
            log_warning "建议安装Homebrew以管理依赖"
        fi
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下构建依赖:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        
        # 提供安装建议
        if [[ "$PLATFORM" == linux-* ]]; then
            log_info "Ubuntu/Debian安装命令:"
            echo "  sudo apt-get update"
            echo "  sudo apt-get install ${missing_deps[*]}"
        elif [[ "$PLATFORM" == macos-* ]]; then
            log_info "macOS安装命令:"
            echo "  brew install ${missing_deps[*]}"
        fi
        
        return 1
    fi
    
    log_success "所有构建依赖已满足"
    return 0
}

# 检查交叉编译工具链（仅适用于龙芯平台）
check_cross_compilation() {
    if [[ "$PLATFORM" != "linux-loongarch64" ]]; then
        return 0
    fi
    
    log_info "检查龙芯交叉编译工具链..."
    
    local toolchain_prefix="loongarch64-linux-gnu"
    local missing_tools=()
    
    for tool in gcc g++ ld ar strip objdump; do
        if ! command -v "${toolchain_prefix}-${tool}" &> /dev/null; then
            missing_tools+=("${toolchain_prefix}-${tool}")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        log_warning "缺少龙芯交叉编译工具:"
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        log_info "请安装龙芯交叉编译工具链"
        return 1
    fi
    
    log_success "龙芯交叉编译工具链检查通过"
    return 0
}

# 执行构建测试
run_build_test() {
    log_info "执行构建测试..."
    
    local build_dir="build-platform-test"
    local build_type="Release"
    
    # 清理之前的构建
    if [ -d "$build_dir" ]; then
        rm -rf "$build_dir"
    fi
    
    mkdir -p "$build_dir"
    cd "$build_dir"
    
    # 配置CMake
    log_info "配置CMake..."
    if [[ "$PLATFORM" == "linux-loongarch64" ]]; then
        # 使用交叉编译工具链
        cmake .. \
            -DCMAKE_BUILD_TYPE="$build_type" \
            -DCMAKE_TOOLCHAIN_FILE="../build_scripts/toolchains/loongarch64-linux-gnu.cmake" \
            -DCMAKE_VERBOSE_MAKEFILE=ON
    else
        cmake .. \
            -DCMAKE_BUILD_TYPE="$build_type" \
            -DCMAKE_VERBOSE_MAKEFILE=ON
    fi
    
    if [ $? -ne 0 ]; then
        log_error "CMake配置失败"
        cd ..
        return 1
    fi
    
    # 执行构建
    log_info "执行构建..."
    local cpu_count=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
    make -j"$cpu_count"
    
    if [ $? -ne 0 ]; then
        log_error "构建失败"
        cd ..
        return 1
    fi
    
    log_success "构建成功"
    cd ..
    return 0
}

# 运行平台兼容性测试
run_compatibility_tests() {
    log_info "运行平台兼容性测试..."
    
    local build_dir="build-platform-test"
    
    if [ ! -d "$build_dir" ]; then
        log_error "构建目录不存在，请先运行构建测试"
        return 1
    fi
    
    cd "$build_dir"
    
    # 运行平台基准测试工具
    if [ -f "./platform-benchmark" ]; then
        log_info "运行平台基准测试..."
        ./platform-benchmark
        
        if [ $? -eq 0 ]; then
            log_success "平台基准测试通过"
        else
            log_warning "平台基准测试存在问题"
        fi
    else
        log_warning "平台基准测试工具未构建"
    fi
    
    # 运行单元测试（如果可用）
    if command -v ctest &> /dev/null; then
        log_info "运行单元测试..."
        ctest --output-on-failure --parallel 4
        
        if [ $? -eq 0 ]; then
            log_success "单元测试通过"
        else
            log_warning "部分单元测试失败"
        fi
    else
        log_warning "CTest不可用，跳过单元测试"
    fi
    
    # 运行平台特定的兼容性测试
    if [ -f "./test_platform_compatibility" ]; then
        log_info "运行平台兼容性测试..."
        ./test_platform_compatibility
        
        if [ $? -eq 0 ]; then
            log_success "平台兼容性测试通过"
        else
            log_warning "平台兼容性测试存在问题"
        fi
    else
        log_warning "平台兼容性测试未构建"
    fi
    
    cd ..
}

# 生成兼容性报告
generate_compatibility_report() {
    log_info "生成兼容性报告..."
    
    local report_file="platform_compatibility_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "=== 平台兼容性验证报告 ==="
        echo "生成时间: $(date)"
        echo "平台: $PLATFORM_DESC"
        echo "操作系统: $(uname -s)"
        echo "架构: $(uname -m)"
        echo "内核版本: $(uname -r)"
        echo ""
        
        echo "=== 系统信息 ==="
        if [[ "$PLATFORM" == linux-* ]]; then
            echo "发行版信息:"
            if [ -f /etc/os-release ]; then
                cat /etc/os-release
            fi
            echo ""
            
            echo "CPU信息:"
            grep "model name\|processor" /proc/cpuinfo | head -5
            echo ""
            
            echo "内存信息:"
            grep "MemTotal\|MemAvailable" /proc/meminfo
            echo ""
        elif [[ "$PLATFORM" == macos-* ]]; then
            echo "macOS版本:"
            sw_vers
            echo ""
            
            echo "CPU信息:"
            sysctl -n machdep.cpu.brand_string
            echo ""
            
            echo "内存信息:"
            echo "总内存: $(( $(sysctl -n hw.memsize) / 1024 / 1024 / 1024 ))GB"
            echo ""
        fi
        
        echo "=== 构建工具版本 ==="
        if command -v cmake &> /dev/null; then
            echo "CMake: $(cmake --version | head -n1)"
        fi
        
        if command -v gcc &> /dev/null; then
            echo "GCC: $(gcc --version | head -n1)"
        fi
        
        if command -v clang &> /dev/null; then
            echo "Clang: $(clang --version | head -n1)"
        fi
        
        echo ""
        
        echo "=== 依赖库检查 ==="
        if command -v pkg-config &> /dev/null; then
            echo "SQLite3: $(pkg-config --modversion sqlite3 2>/dev/null || echo '未找到')"
            echo "OpenSSL: $(pkg-config --modversion openssl 2>/dev/null || echo '未找到')"
        fi
        echo ""
        
        echo "=== 构建测试结果 ==="
        if [ -d "build-platform-test" ]; then
            echo "构建状态: 成功"
            echo "构建目录: build-platform-test"
            
            echo ""
            echo "构建产物:"
            find build-platform-test -name "timing-server" -o -name "platform-benchmark" -o -name "test_*" | head -10
        else
            echo "构建状态: 失败或未执行"
        fi
        
        echo ""
        echo "=== 平台特定信息 ==="
        case "$PLATFORM" in
            linux-x86_64)
                echo "Linux x86_64平台特性:"
                echo "- 支持SSE/AVX指令集优化"
                echo "- 支持实时内核(PREEMPT_RT)"
                echo "- 支持高精度定时器"
                ;;
            linux-loongarch64)
                echo "龙芯LoongArch64平台特性:"
                echo "- 支持龙芯指令集优化"
                echo "- 支持交叉编译构建"
                if command -v loongarch64-linux-gnu-gcc &> /dev/null; then
                    echo "- 交叉编译工具链: 可用"
                else
                    echo "- 交叉编译工具链: 不可用"
                fi
                ;;
            macos-*)
                echo "macOS开发环境特性:"
                echo "- 使用Mock HAL实现"
                echo "- 支持完整开发测试流程"
                echo "- Xcode工具链集成"
                ;;
        esac
        
    } > "$report_file"
    
    log_success "兼容性报告已生成: $report_file"
}

# 清理函数
cleanup() {
    if [ -d "build-platform-test" ]; then
        log_info "清理构建目录..."
        rm -rf "build-platform-test"
    fi
}

# 主函数
main() {
    echo "=== 平台兼容性验证工具 ==="
    echo ""
    
    # 检测平台
    detect_platform
    
    # 检查构建依赖
    if ! check_build_dependencies; then
        log_error "依赖检查失败，请安装缺少的依赖后重试"
        exit 1
    fi
    
    # 检查交叉编译工具链（如果需要）
    if ! check_cross_compilation; then
        log_warning "交叉编译工具链检查失败，但将继续验证"
    fi
    
    # 执行构建测试
    if ! run_build_test; then
        log_error "构建测试失败"
        cleanup
        exit 1
    fi
    
    # 运行兼容性测试
    run_compatibility_tests
    
    # 生成报告
    generate_compatibility_report
    
    log_success "平台兼容性验证完成"
    
    # 询问是否清理
    read -p "是否清理构建目录? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup
    fi
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --help, -h     显示此帮助信息"
        echo "  --clean        仅清理构建目录"
        echo "  --deps-only    仅检查依赖"
        echo "  --build-only   仅执行构建测试"
        echo "  --test-only    仅运行测试（需要先构建）"
        echo ""
        echo "此脚本用于验证不同平台的构建和运行兼容性。"
        echo "支持的平台:"
        echo "  - Linux x86_64"
        echo "  - Linux 龙芯LoongArch64"
        echo "  - macOS x86_64"
        echo "  - macOS ARM64"
        exit 0
        ;;
    --clean)
        cleanup
        exit 0
        ;;
    --deps-only)
        detect_platform
        check_build_dependencies
        check_cross_compilation
        exit $?
        ;;
    --build-only)
        detect_platform
        check_build_dependencies
        run_build_test
        exit $?
        ;;
    --test-only)
        detect_platform
        run_compatibility_tests
        exit $?
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        echo "使用 --help 查看帮助信息"
        exit 1
        ;;
esac