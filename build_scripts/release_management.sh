#!/bin/bash

# 高精度授时服务器系统版本管理和发布脚本
# 支持配置迁移和回滚机制

set -e

# 脚本参数
ACTION=${1:-"help"}
VERSION=${2:-""}
PLATFORM=${3:-"all"}
RELEASE_TYPE=${4:-"stable"}

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

show_usage() {
    echo "用法: $0 [操作] [版本] [平台] [发布类型]"
    echo ""
    echo "操作:"
    echo "  help            - 显示帮助信息"
    echo "  version         - 显示当前版本信息"
    echo "  bump            - 升级版本号"
    echo "  build           - 构建发布版本"
    echo "  package         - 创建发布包"
    echo "  release         - 创建正式发布"
    echo "  rollback        - 回滚到指定版本"
    echo "  migrate-config  - 迁移配置文件"
    echo "  validate        - 验证发布包"
    echo ""
    echo "版本格式: major.minor.patch (例如: 1.2.3)"
    echo ""
    echo "平台选项:"
    echo "  all             - 所有支持的平台（默认）"
    echo "  linux           - Linux x86_64"
    echo "  loongarch64     - 龙芯LoongArch64"
    echo "  darwin          - macOS开发环境"
    echo ""
    echo "发布类型:"
    echo "  stable          - 稳定版本（默认）"
    echo "  beta            - 测试版本"
    echo "  alpha           - 开发版本"
    echo "  rc              - 候选版本"
    echo ""
    echo "示例:"
    echo "  $0 version                          # 显示当前版本"
    echo "  $0 bump 1.1.0                      # 升级到版本1.1.0"
    echo "  $0 build 1.1.0 linux stable        # 构建Linux稳定版"
    echo "  $0 release 1.1.0                   # 创建1.1.0正式发布"
    echo "  $0 rollback 1.0.0                  # 回滚到1.0.0版本"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 版本文件路径
VERSION_FILE="$PROJECT_ROOT/VERSION"
CMAKE_FILE="$PROJECT_ROOT/CMakeLists.txt"
PACKAGE_JSON="$PROJECT_ROOT/frontend/package.json"

# 发布目录
RELEASE_DIR="$PROJECT_ROOT/releases"
BACKUP_DIR="$PROJECT_ROOT/backups"

print_info "项目根目录: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

# 获取当前版本
get_current_version() {
    if [[ -f "$VERSION_FILE" ]]; then
        cat "$VERSION_FILE"
    else
        # 从CMakeLists.txt提取版本
        grep "project.*VERSION" CMakeLists.txt | sed -n 's/.*VERSION \([0-9.]*\).*/\1/p' || echo "1.0.0"
    fi
}

# 验证版本格式
validate_version() {
    local version=$1
    if [[ ! $version =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        print_error "无效的版本格式: $version"
        print_info "版本格式应为: major.minor.patch (例如: 1.2.3)"
        return 1
    fi
    return 0
}

# 比较版本
compare_versions() {
    local version1=$1
    local version2=$2
    
    # 将版本号分解为数组
    IFS='.' read -ra V1 <<< "$version1"
    IFS='.' read -ra V2 <<< "$version2"
    
    # 比较主版本号
    if [[ ${V1[0]} -gt ${V2[0]} ]]; then
        return 1  # version1 > version2
    elif [[ ${V1[0]} -lt ${V2[0]} ]]; then
        return 2  # version1 < version2
    fi
    
    # 比较次版本号
    if [[ ${V1[1]} -gt ${V2[1]} ]]; then
        return 1
    elif [[ ${V1[1]} -lt ${V2[1]} ]]; then
        return 2
    fi
    
    # 比较补丁版本号
    if [[ ${V1[2]} -gt ${V2[2]} ]]; then
        return 1
    elif [[ ${V1[2]} -lt ${V2[2]} ]]; then
        return 2
    fi
    
    return 0  # version1 == version2
}

# 更新版本号
update_version() {
    local new_version=$1
    
    print_info "更新版本号到: $new_version"
    
    # 更新VERSION文件
    echo "$new_version" > "$VERSION_FILE"
    
    # 更新CMakeLists.txt
    if [[ -f "$CMAKE_FILE" ]]; then
        sed -i.bak "s/project(timing-server-system VERSION [0-9.]*/project(timing-server-system VERSION $new_version/" "$CMAKE_FILE"
        rm -f "$CMAKE_FILE.bak"
        print_success "已更新CMakeLists.txt"
    fi
    
    # 更新前端package.json
    if [[ -f "$PACKAGE_JSON" ]]; then
        if command -v jq >/dev/null 2>&1; then
            jq ".version = \"$new_version\"" "$PACKAGE_JSON" > "$PACKAGE_JSON.tmp" && mv "$PACKAGE_JSON.tmp" "$PACKAGE_JSON"
            print_success "已更新package.json"
        else
            print_warning "未安装jq，跳过package.json更新"
        fi
    fi
    
    # 创建版本标签
    if git rev-parse --git-dir >/dev/null 2>&1; then
        git add VERSION CMakeLists.txt frontend/package.json 2>/dev/null || true
        git commit -m "Bump version to $new_version" 2>/dev/null || true
        git tag -a "v$new_version" -m "Release version $new_version" 2>/dev/null || true
        print_success "已创建Git标签: v$new_version"
    fi
}

# 构建发布版本
build_release() {
    local version=$1
    local platform=$2
    local release_type=$3
    
    print_info "构建发布版本: $version ($platform, $release_type)"
    
    # 创建构建目录
    local build_timestamp=$(date +%Y%m%d_%H%M%S)
    local build_dir="build-release-$version-$build_timestamp"
    
    mkdir -p "$build_dir"
    cd "$build_dir"
    
    # 设置构建参数
    local cmake_args="-DCMAKE_BUILD_TYPE=Release"
    local platforms=()
    
    if [[ "$platform" == "all" ]]; then
        platforms=("linux" "loongarch64" "darwin")
    else
        platforms=("$platform")
    fi
    
    # 构建各个平台
    for target_platform in "${platforms[@]}"; do
        print_info "构建平台: $target_platform"
        
        local platform_build_dir="$target_platform"
        mkdir -p "$platform_build_dir"
        cd "$platform_build_dir"
        
        # 设置平台特定的CMake参数
        case "$target_platform" in
            "loongarch64")
                cmake_args="$cmake_args -DCMAKE_TOOLCHAIN_FILE=../../build_scripts/toolchains/loongarch64-linux-gnu.cmake"
                ;;
            "linux"|"darwin")
                cmake_args="-DCMAKE_BUILD_TYPE=Release"
                ;;
        esac
        
        # 配置和构建
        cmake ../../ $cmake_args
        make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)
        
        # 运行测试（仅在本地平台）
        if [[ "$target_platform" == "linux" && "$OSTYPE" == "linux-gnu" ]] || \
           [[ "$target_platform" == "darwin" && "$OSTYPE" == "darwin"* ]]; then
            print_info "运行测试..."
            ctest --output-on-failure || print_warning "部分测试失败"
        fi
        
        cd ..
    done
    
    cd "$PROJECT_ROOT"
    print_success "构建完成: $build_dir"
}

# 创建发布包
create_release_packages() {
    local version=$1
    local platform=$2
    local release_type=$3
    
    print_info "创建发布包: $version ($platform, $release_type)"
    
    # 创建发布目录
    mkdir -p "$RELEASE_DIR/$version"
    local release_output="$RELEASE_DIR/$version"
    
    # 设置平台列表
    local platforms=()
    if [[ "$platform" == "all" ]]; then
        platforms=("linux" "loongarch64" "darwin")
    else
        platforms=("$platform")
    fi
    
    # 为每个平台创建包
    for target_platform in "${platforms[@]}"; do
        print_info "创建$target_platform平台发布包..."
        
        # 构建项目
        ./build_scripts/build.sh "$target_platform" Release
        
        # 创建部署包
        ./build_scripts/create_deployment_package.sh "$target_platform" Release all "$release_output"
        
        print_success "$target_platform平台发布包创建完成"
    done
    
    # 创建发布说明
    create_release_notes "$version" "$release_type" "$release_output"
    
    # 生成校验和
    cd "$release_output"
    find . -name "*.tar.gz" -o -name "*.deb" -o -name "*.rpm" -o -name "*-installer.sh" | \
        xargs sha256sum > "timing-server-system-$version.sha256"
    
    print_success "发布包创建完成: $release_output"
    ls -la "$release_output"
}

# 创建发布说明
create_release_notes() {
    local version=$1
    local release_type=$2
    local output_dir=$3
    
    local release_notes="$output_dir/RELEASE_NOTES.md"
    
    cat > "$release_notes" << EOF
# 高精度授时服务器系统 v$version 发布说明

## 版本信息
- **版本号**: $version
- **发布类型**: $release_type
- **发布日期**: $(date -u '+%Y-%m-%d')
- **构建时间**: $(date -u '+%Y-%m-%d %H:%M:%S UTC')

## 支持的平台
- Linux x86_64
- LoongArch64 (龙芯)
- macOS (开发环境)

## 新增功能
- 完整的CI/CD流水线支持
- 自动化部署包创建
- 跨平台构建和测试
- 版本管理和回滚机制
- 配置迁移工具

## 改进和优化
- 优化了构建脚本的错误处理
- 改进了部署文档的完整性
- 增强了测试覆盖率
- 优化了性能监控功能

## 修复的问题
- 修复了跨平台编译的兼容性问题
- 解决了配置文件验证的边界情况
- 修复了内存泄漏检测的误报
- 改进了错误日志的可读性

## 安装说明

### 快速安装
\`\`\`bash
# 下载并解压
tar -xzf timing-server-system-$version-linux-x86_64.tar.gz
cd timing-server-system-$version-linux-x86_64

# 运行安装脚本
sudo ./install.sh
\`\`\`

### 包管理器安装
\`\`\`bash
# Ubuntu/Debian
sudo dpkg -i timing-server-system-$version-linux-x86_64.deb

# CentOS/RHEL
sudo rpm -ivh timing-server-system-$version-linux-x86_64.rpm
\`\`\`

### 自解压安装
\`\`\`bash
sudo bash timing-server-system-$version-linux-x86_64-installer.sh
\`\`\`

## 升级说明

### 从v1.0.x升级
1. 停止当前服务: \`sudo systemctl stop timing-server\`
2. 备份配置和数据: \`sudo tar -czf backup.tar.gz /etc/timing-server /var/lib/timing-server\`
3. 安装新版本（使用上述安装方法之一）
4. 迁移配置: \`timing-server --migrate-config /etc/timing-server/config.json\`
5. 启动服务: \`sudo systemctl start timing-server\`

### 配置变更
- 新增了\`monitoring.alarms\`配置节
- 优化了\`timing.disciplining\`参数结构
- 增加了\`security.access_control\`选项

## 已知问题
- 在某些龙芯平台上，交叉编译工具链可能需要手动配置
- macOS上的Mock HAL实现仅用于开发测试

## 技术支持
- 文档: https://github.com/timing-server/timing-server-system/docs
- 问题报告: https://github.com/timing-server/timing-server-system/issues
- 邮件支持: <EMAIL>

## 校验和
请使用提供的SHA256校验和文件验证下载的完整性：
\`\`\`bash
sha256sum -c timing-server-system-$version.sha256
\`\`\`

---
**发布团队**: Timing Server Development Team  
**发布日期**: $(date -u '+%Y-%m-%d')
EOF

    print_success "发布说明已创建: $release_notes"
}

# 创建正式发布
create_official_release() {
    local version=$1
    
    print_info "创建正式发布: $version"
    
    # 验证版本
    if ! validate_version "$version"; then
        return 1
    fi
    
    # 检查是否为新版本
    local current_version=$(get_current_version)
    compare_versions "$version" "$current_version"
    local result=$?
    
    if [[ $result -eq 0 ]]; then
        print_error "版本 $version 已存在"
        return 1
    elif [[ $result -eq 2 ]]; then
        print_error "版本 $version 低于当前版本 $current_version"
        return 1
    fi
    
    # 创建备份
    create_backup "$current_version"
    
    # 更新版本号
    update_version "$version"
    
    # 构建发布版本
    build_release "$version" "all" "stable"
    
    # 创建发布包
    create_release_packages "$version" "all" "stable"
    
    # 验证发布包
    validate_release_packages "$version"
    
    print_success "正式发布创建完成: v$version"
    print_info "发布文件位置: $RELEASE_DIR/$version"
}

# 回滚到指定版本
rollback_version() {
    local target_version=$1
    
    print_info "回滚到版本: $target_version"
    
    # 验证目标版本
    if ! validate_version "$target_version"; then
        return 1
    fi
    
    # 检查备份是否存在
    local backup_file="$BACKUP_DIR/backup-$target_version.tar.gz"
    if [[ ! -f "$backup_file" ]]; then
        print_error "未找到版本 $target_version 的备份文件"
        print_info "可用备份:"
        ls -la "$BACKUP_DIR"/backup-*.tar.gz 2>/dev/null || echo "无可用备份"
        return 1
    fi
    
    # 创建当前状态备份
    local current_version=$(get_current_version)
    create_backup "$current_version-rollback-$(date +%Y%m%d_%H%M%S)"
    
    # 恢复备份
    print_info "恢复备份文件: $backup_file"
    tar -xzf "$backup_file" -C "$PROJECT_ROOT"
    
    # 验证回滚
    local restored_version=$(get_current_version)
    if [[ "$restored_version" == "$target_version" ]]; then
        print_success "成功回滚到版本: $target_version"
    else
        print_error "回滚失败，当前版本: $restored_version"
        return 1
    fi
}

# 创建备份
create_backup() {
    local version=$1
    
    print_info "创建备份: $version"
    
    mkdir -p "$BACKUP_DIR"
    local backup_file="$BACKUP_DIR/backup-$version.tar.gz"
    
    # 备份关键文件
    tar -czf "$backup_file" \
        VERSION \
        CMakeLists.txt \
        frontend/package.json \
        --exclude-vcs \
        --exclude='build-*' \
        --exclude='node_modules' \
        --exclude='*.log' \
        . 2>/dev/null || true
    
    print_success "备份已创建: $backup_file"
}

# 迁移配置文件
migrate_config() {
    local config_file=${1:-"/etc/timing-server/config.json"}
    
    print_info "迁移配置文件: $config_file"
    
    if [[ ! -f "$config_file" ]]; then
        print_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 备份原配置
    local backup_config="${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$config_file" "$backup_config"
    print_info "原配置已备份: $backup_config"
    
    # 检查配置版本
    local config_version=""
    if command -v jq >/dev/null 2>&1; then
        config_version=$(jq -r '.version // "unknown"' "$config_file" 2>/dev/null || echo "unknown")
    fi
    
    print_info "配置文件版本: $config_version"
    
    # 执行迁移（这里可以添加具体的迁移逻辑）
    case "$config_version" in
        "1.0.0"|"unknown")
            migrate_config_1_0_to_1_1 "$config_file"
            ;;
        "1.1.0")
            print_info "配置文件已是最新版本"
            ;;
        *)
            print_warning "未知的配置版本: $config_version"
            ;;
    esac
    
    # 验证迁移后的配置
    if command -v timing-server >/dev/null 2>&1; then
        if timing-server --config "$config_file" --validate; then
            print_success "配置迁移完成并验证通过"
        else
            print_error "配置迁移后验证失败"
            print_info "恢复备份配置: cp $backup_config $config_file"
            return 1
        fi
    else
        print_warning "无法验证配置，timing-server程序不可用"
    fi
}

# 配置迁移：1.0.x -> 1.1.x
migrate_config_1_0_to_1_1() {
    local config_file=$1
    
    print_info "执行配置迁移: 1.0.x -> 1.1.x"
    
    if command -v jq >/dev/null 2>&1; then
        # 使用jq进行配置迁移
        jq '
        # 添加新的监控配置节
        .monitoring = {
            "metrics": {
                "enabled": true,
                "collection_interval_seconds": 1,
                "retention_days": 30
            },
            "alarms": {
                "enabled": true,
                "thresholds": {
                    "phase_offset_warning_ns": 100.0,
                    "phase_offset_critical_ns": 500.0,
                    "frequency_offset_warning_ppm": 0.01,
                    "frequency_offset_critical_ppm": 0.1
                }
            }
        } |
        # 更新版本号
        .version = "1.1.0"
        ' "$config_file" > "${config_file}.tmp" && mv "${config_file}.tmp" "$config_file"
        
        print_success "配置迁移完成"
    else
        print_error "需要安装jq工具进行配置迁移"
        return 1
    fi
}

# 验证发布包
validate_release_packages() {
    local version=$1
    
    print_info "验证发布包: $version"
    
    local release_dir="$RELEASE_DIR/$version"
    if [[ ! -d "$release_dir" ]]; then
        print_error "发布目录不存在: $release_dir"
        return 1
    fi
    
    cd "$release_dir"
    
    # 验证校验和
    if [[ -f "timing-server-system-$version.sha256" ]]; then
        if sha256sum -c "timing-server-system-$version.sha256"; then
            print_success "校验和验证通过"
        else
            print_error "校验和验证失败"
            return 1
        fi
    else
        print_warning "未找到校验和文件"
    fi
    
    # 验证包文件
    local packages_found=0
    for package in *.tar.gz *.deb *.rpm *-installer.sh; do
        if [[ -f "$package" ]]; then
            print_info "发现包文件: $package"
            packages_found=$((packages_found + 1))
        fi
    done
    
    if [[ $packages_found -eq 0 ]]; then
        print_error "未找到任何发布包"
        return 1
    fi
    
    print_success "发布包验证完成，共找到 $packages_found 个包文件"
    cd "$PROJECT_ROOT"
}

# 显示版本信息
show_version_info() {
    local current_version=$(get_current_version)
    
    echo "高精度授时服务器系统版本信息"
    echo "============================"
    echo "当前版本: $current_version"
    echo "项目根目录: $PROJECT_ROOT"
    echo "版本文件: $VERSION_FILE"
    echo ""
    
    # Git信息
    if git rev-parse --git-dir >/dev/null 2>&1; then
        echo "Git信息:"
        echo "--------"
        echo "当前分支: $(git branch --show-current 2>/dev/null || echo 'unknown')"
        echo "最新提交: $(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
        echo "提交时间: $(git log -1 --format=%cd --date=iso 2>/dev/null || echo 'unknown')"
        echo ""
        
        # 显示标签
        echo "版本标签:"
        git tag -l "v*" | sort -V | tail -10 || echo "无版本标签"
        echo ""
    fi
    
    # 构建信息
    echo "构建信息:"
    echo "--------"
    if [[ -d "build-linux-Release" ]]; then
        echo "Linux构建: 存在"
    else
        echo "Linux构建: 不存在"
    fi
    
    if [[ -d "build-loongarch64-Release" ]]; then
        echo "LoongArch64构建: 存在"
    else
        echo "LoongArch64构建: 不存在"
    fi
    
    if [[ -d "build-darwin-Release" ]]; then
        echo "macOS构建: 存在"
    else
        echo "macOS构建: 不存在"
    fi
    echo ""
    
    # 发布信息
    echo "发布信息:"
    echo "--------"
    if [[ -d "$RELEASE_DIR" ]]; then
        echo "发布目录: $RELEASE_DIR"
        echo "可用发布:"
        ls -1 "$RELEASE_DIR" 2>/dev/null | head -10 || echo "无可用发布"
    else
        echo "发布目录: 不存在"
    fi
    echo ""
    
    # 备份信息
    echo "备份信息:"
    echo "--------"
    if [[ -d "$BACKUP_DIR" ]]; then
        echo "备份目录: $BACKUP_DIR"
        echo "可用备份:"
        ls -1 "$BACKUP_DIR"/backup-*.tar.gz 2>/dev/null | head -5 || echo "无可用备份"
    else
        echo "备份目录: 不存在"
    fi
}

# 主函数
main() {
    case "$ACTION" in
        "help"|"-h"|"--help")
            show_usage
            ;;
        "version")
            show_version_info
            ;;
        "bump")
            if [[ -z "$VERSION" ]]; then
                print_error "请指定版本号"
                show_usage
                exit 1
            fi
            if validate_version "$VERSION"; then
                update_version "$VERSION"
            fi
            ;;
        "build")
            if [[ -z "$VERSION" ]]; then
                VERSION=$(get_current_version)
            fi
            build_release "$VERSION" "$PLATFORM" "$RELEASE_TYPE"
            ;;
        "package")
            if [[ -z "$VERSION" ]]; then
                VERSION=$(get_current_version)
            fi
            create_release_packages "$VERSION" "$PLATFORM" "$RELEASE_TYPE"
            ;;
        "release")
            if [[ -z "$VERSION" ]]; then
                print_error "请指定版本号"
                show_usage
                exit 1
            fi
            create_official_release "$VERSION"
            ;;
        "rollback")
            if [[ -z "$VERSION" ]]; then
                print_error "请指定回滚目标版本号"
                show_usage
                exit 1
            fi
            rollback_version "$VERSION"
            ;;
        "migrate-config")
            migrate_config "$VERSION"
            ;;
        "validate")
            if [[ -z "$VERSION" ]]; then
                VERSION=$(get_current_version)
            fi
            validate_release_packages "$VERSION"
            ;;
        *)
            print_error "未知操作: $ACTION"
            show_usage
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"