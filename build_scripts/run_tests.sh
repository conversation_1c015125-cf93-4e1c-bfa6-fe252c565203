优先修复 test_precision_monitoring.cpp、test_end_to_end_integration.cpp、test_web_interface_functionality.cpp #!/bin/bash

# 高精度授时服务器系统自动化测试脚本
# 包含单元测试、集成测试和性能测试

set -e

# 脚本参数
TEST_TYPE=${1:-"all"}
PLATFORM=${2:-"auto"}
BUILD_TYPE=${3:-"Debug"}
VERBOSE=${4:-"false"}

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

show_usage() {
    echo "用法: $0 [测试类型] [平台] [构建类型] [详细输出]"
    echo ""
    echo "测试类型:"
    echo "  all         - 运行所有测试（默认）"
    echo "  unit        - 仅运行单元测试"
    echo "  integration - 仅运行集成测试"
    echo "  performance - 仅运行性能测试"
    echo "  coverage    - 运行测试并生成覆盖率报告"
    echo ""
    echo "平台选项:"
    echo "  auto        - 自动检测平台（默认）"
    echo "  linux       - Linux x86_64平台"
    echo "  loongarch64 - 龙芯LoongArch64平台"
    echo "  darwin      - macOS平台"
    echo ""
    echo "构建类型:"
    echo "  Debug       - 调试版本（默认，包含测试）"
    echo "  Release     - 发布版本"
    echo ""
    echo "详细输出:"
    echo "  true        - 显示详细测试输出"
    echo "  false       - 简洁输出（默认）"
    echo ""
    echo "示例:"
    echo "  $0 unit linux Debug true"
    echo "  $0 coverage auto Debug"
    echo "  $0 performance linux Release"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "项目根目录: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

# 自动检测平台
if [[ "$PLATFORM" == "auto" ]]; then
    case "$OSTYPE" in
        linux*)
            if [[ "$(uname -m)" == "loongarch64" ]]; then
                PLATFORM="loongarch64"
            else
                PLATFORM="linux"
            fi
            ;;
        darwin*)
            PLATFORM="darwin"
            ;;
        *)
            print_error "不支持的操作系统: $OSTYPE"
            exit 1
            ;;
    esac
fi

print_info "测试类型: $TEST_TYPE"
print_info "目标平台: $PLATFORM"
print_info "构建类型: $BUILD_TYPE"

# 设置构建目录
BUILD_DIR="build-${PLATFORM}-${BUILD_TYPE}"

# 检查构建目录是否存在
if [[ ! -d "$BUILD_DIR" ]]; then
    print_error "构建目录不存在: $BUILD_DIR"
    print_info "请先运行构建脚本: ./build_scripts/build.sh $PLATFORM $BUILD_TYPE"
    exit 1
fi

# 进入构建目录
cd "$BUILD_DIR"

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# 测试结果记录
TEST_RESULTS_FILE="test_results_$(date +%Y%m%d_%H%M%S).xml"
COVERAGE_DIR="coverage_report"

# 运行单元测试
run_unit_tests() {
    print_info "运行单元测试..."
    
    if [[ ! -f "Makefile" ]]; then
        print_error "未找到Makefile，请先构建项目"
        return 1
    fi
    
    # 检查是否有测试目标
    if ! make help | grep -q "test"; then
        print_warning "未找到测试目标，跳过单元测试"
        return 0
    fi
    
    # 设置测试环境变量
    export CTEST_OUTPUT_ON_FAILURE=1
    export CTEST_PARALLEL_LEVEL=${CTEST_PARALLEL_LEVEL:-4}
    
    # 运行CTest
    local ctest_args=""
    if [[ "$VERBOSE" == "true" ]]; then
        ctest_args="--verbose"
    fi
    
    if [[ "$TEST_TYPE" == "coverage" ]]; then
        ctest_args="$ctest_args --coverage"
    fi
    
    print_info "执行命令: ctest $ctest_args --output-junit $TEST_RESULTS_FILE"
    
    if ctest $ctest_args --output-junit "$TEST_RESULTS_FILE"; then
        print_success "单元测试通过"
        
        # 解析测试结果
        if [[ -f "$TEST_RESULTS_FILE" ]]; then
            local tests=$(grep -o 'tests="[0-9]*"' "$TEST_RESULTS_FILE" | cut -d'"' -f2 || echo "0")
            local failures=$(grep -o 'failures="[0-9]*"' "$TEST_RESULTS_FILE" | cut -d'"' -f2 || echo "0")
            local errors=$(grep -o 'errors="[0-9]*"' "$TEST_RESULTS_FILE" | cut -d'"' -f2 || echo "0")
            
            TOTAL_TESTS=$((TOTAL_TESTS + tests))
            FAILED_TESTS=$((FAILED_TESTS + failures + errors))
            PASSED_TESTS=$((PASSED_TESTS + tests - failures - errors))
            
            print_info "单元测试统计: 总计 $tests, 通过 $((tests - failures - errors)), 失败 $((failures + errors))"
        fi
        
        return 0
    else
        print_error "单元测试失败"
        return 1
    fi
}

# 运行集成测试
run_integration_tests() {
    print_info "运行集成测试..."
    
    # 检查是否有集成测试可执行文件
    local integration_tests=()
    for test_file in test_*_integration; do
        if [[ -x "$test_file" ]]; then
            integration_tests+=("$test_file")
        fi
    done
    
    if [[ ${#integration_tests[@]} -eq 0 ]]; then
        print_warning "未找到集成测试，跳过"
        return 0
    fi
    
    local integration_passed=0
    local integration_failed=0
    
    for test in "${integration_tests[@]}"; do
        print_info "运行集成测试: $test"
        
        if [[ "$VERBOSE" == "true" ]]; then
            if "./$test"; then
                print_success "集成测试通过: $test"
                integration_passed=$((integration_passed + 1))
            else
                print_error "集成测试失败: $test"
                integration_failed=$((integration_failed + 1))
            fi
        else
            if "./$test" >/dev/null 2>&1; then
                print_success "集成测试通过: $test"
                integration_passed=$((integration_passed + 1))
            else
                print_error "集成测试失败: $test"
                integration_failed=$((integration_failed + 1))
            fi
        fi
    done
    
    TOTAL_TESTS=$((TOTAL_TESTS + integration_passed + integration_failed))
    PASSED_TESTS=$((PASSED_TESTS + integration_passed))
    FAILED_TESTS=$((FAILED_TESTS + integration_failed))
    
    print_info "集成测试统计: 总计 $((integration_passed + integration_failed)), 通过 $integration_passed, 失败 $integration_failed"
    
    if [[ $integration_failed -eq 0 ]]; then
        return 0
    else
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    print_info "运行性能测试..."
    
    # 检查是否有性能测试可执行文件
    local perf_tests=()
    for test_file in test_*_performance benchmark_*; do
        if [[ -x "$test_file" ]]; then
            perf_tests+=("$test_file")
        fi
    done
    
    if [[ ${#perf_tests[@]} -eq 0 ]]; then
        print_warning "未找到性能测试，跳过"
        return 0
    fi
    
    # 创建性能测试报告目录
    local perf_report_dir="performance_report_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$perf_report_dir"
    
    local perf_passed=0
    local perf_failed=0
    
    for test in "${perf_tests[@]}"; do
        print_info "运行性能测试: $test"
        
        local test_output="$perf_report_dir/${test}_output.txt"
        local test_result="$perf_report_dir/${test}_result.json"
        
        if "./$test" > "$test_output" 2>&1; then
            print_success "性能测试完成: $test"
            perf_passed=$((perf_passed + 1))
            
            # 尝试解析性能指标
            if grep -q "Performance:" "$test_output"; then
                grep "Performance:" "$test_output" > "$test_result"
                print_info "性能指标已保存: $test_result"
            fi
        else
            print_error "性能测试失败: $test"
            perf_failed=$((perf_failed + 1))
        fi
    done
    
    print_info "性能测试统计: 总计 $((perf_passed + perf_failed)), 完成 $perf_passed, 失败 $perf_failed"
    print_info "性能测试报告目录: $perf_report_dir"
    
    if [[ $perf_failed -eq 0 ]]; then
        return 0
    else
        return 1
    fi
}

# 生成覆盖率报告
generate_coverage_report() {
    print_info "生成测试覆盖率报告..."
    
    # 检查是否安装了lcov
    if ! command -v lcov >/dev/null 2>&1; then
        print_warning "未安装lcov，跳过覆盖率报告生成"
        print_info "安装命令: sudo apt-get install lcov (Ubuntu/Debian)"
        return 0
    fi
    
    # 创建覆盖率报告目录
    mkdir -p "$COVERAGE_DIR"
    
    # 收集覆盖率数据
    print_info "收集覆盖率数据..."
    lcov --capture --directory . --output-file "$COVERAGE_DIR/coverage.info"
    
    # 过滤系统文件和测试文件
    lcov --remove "$COVERAGE_DIR/coverage.info" \
         '/usr/*' \
         '*/tests/*' \
         '*/test_*' \
         '*/mock/*' \
         --output-file "$COVERAGE_DIR/coverage_filtered.info"
    
    # 生成HTML报告
    print_info "生成HTML覆盖率报告..."
    genhtml "$COVERAGE_DIR/coverage_filtered.info" \
            --output-directory "$COVERAGE_DIR/html" \
            --title "高精度授时服务器系统测试覆盖率报告" \
            --show-details \
            --legend
    
    # 显示覆盖率摘要
    lcov --list "$COVERAGE_DIR/coverage_filtered.info"
    
    print_success "覆盖率报告生成完成"
    print_info "HTML报告位置: $COVERAGE_DIR/html/index.html"
    
    # 如果在macOS上，尝试打开报告
    if [[ "$OSTYPE" == "darwin"* ]] && command -v open >/dev/null 2>&1; then
        print_info "正在打开覆盖率报告..."
        open "$COVERAGE_DIR/html/index.html"
    fi
}

# 运行内存泄漏检测
run_memory_leak_detection() {
    print_info "运行内存泄漏检测..."
    
    # 检查是否安装了valgrind
    if ! command -v valgrind >/dev/null 2>&1; then
        print_warning "未安装valgrind，跳过内存泄漏检测"
        print_info "安装命令: sudo apt-get install valgrind (Ubuntu/Debian)"
        return 0
    fi
    
    # 查找测试可执行文件
    local test_executables=()
    for test_file in test_*; do
        if [[ -x "$test_file" && ! "$test_file" =~ .*\.(sh|py)$ ]]; then
            test_executables+=("$test_file")
        fi
    done
    
    if [[ ${#test_executables[@]} -eq 0 ]]; then
        print_warning "未找到测试可执行文件，跳过内存泄漏检测"
        return 0
    fi
    
    # 创建内存检测报告目录
    local memory_report_dir="memory_report_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$memory_report_dir"
    
    local memory_issues=0
    
    for test in "${test_executables[@]}"; do
        print_info "检测内存泄漏: $test"
        
        local valgrind_output="$memory_report_dir/${test}_valgrind.txt"
        
        # 运行valgrind检测
        valgrind --tool=memcheck \
                 --leak-check=full \
                 --show-leak-kinds=all \
                 --track-origins=yes \
                 --verbose \
                 --log-file="$valgrind_output" \
                 "./$test" >/dev/null 2>&1 || true
        
        # 检查是否有内存问题
        if grep -q "ERROR SUMMARY: 0 errors" "$valgrind_output" && \
           grep -q "definitely lost: 0 bytes" "$valgrind_output"; then
            print_success "内存检测通过: $test"
        else
            print_warning "发现内存问题: $test"
            memory_issues=$((memory_issues + 1))
            
            # 显示内存问题摘要
            if [[ "$VERBOSE" == "true" ]]; then
                grep -A 5 "ERROR SUMMARY" "$valgrind_output" || true
                grep -A 5 "LEAK SUMMARY" "$valgrind_output" || true
            fi
        fi
    done
    
    print_info "内存检测报告目录: $memory_report_dir"
    
    if [[ $memory_issues -eq 0 ]]; then
        print_success "未发现内存泄漏"
        return 0
    else
        print_warning "发现 $memory_issues 个程序存在内存问题"
        return 1
    fi
}

# 运行静态代码分析
run_static_analysis() {
    print_info "运行静态代码分析..."
    
    # 检查是否安装了cppcheck
    if ! command -v cppcheck >/dev/null 2>&1; then
        print_warning "未安装cppcheck，跳过静态代码分析"
        print_info "安装命令: sudo apt-get install cppcheck (Ubuntu/Debian)"
        return 0
    fi
    
    # 创建静态分析报告目录
    local analysis_report_dir="static_analysis_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$analysis_report_dir"
    
    # 运行cppcheck
    print_info "运行cppcheck静态分析..."
    cppcheck --enable=all \
             --inconclusive \
             --xml \
             --xml-version=2 \
             --output-file="$analysis_report_dir/cppcheck.xml" \
             "$PROJECT_ROOT/backend/src" \
             "$PROJECT_ROOT/backend/include" \
             2>/dev/null || true
    
    # 生成HTML报告
    if command -v cppcheck-htmlreport >/dev/null 2>&1; then
        cppcheck-htmlreport --file="$analysis_report_dir/cppcheck.xml" \
                           --report-dir="$analysis_report_dir/html" \
                           --source-dir="$PROJECT_ROOT"
        
        print_info "静态分析HTML报告: $analysis_report_dir/html/index.html"
    fi
    
    # 显示问题摘要
    local issues=$(grep -c "<error" "$analysis_report_dir/cppcheck.xml" 2>/dev/null || echo "0")
    print_info "静态分析发现 $issues 个问题"
    
    if [[ $issues -eq 0 ]]; then
        print_success "静态代码分析通过"
        return 0
    else
        print_warning "静态代码分析发现问题"
        return 1
    fi
}

# 主测试流程
main() {
    local test_result=0
    
    print_info "开始运行测试..."
    print_info "测试时间: $(date)"
    
    # 根据测试类型运行相应测试
    case "$TEST_TYPE" in
        "unit")
            run_unit_tests || test_result=1
            ;;
        "integration")
            run_integration_tests || test_result=1
            ;;
        "performance")
            run_performance_tests || test_result=1
            ;;
        "coverage")
            run_unit_tests || test_result=1
            generate_coverage_report
            ;;
        "memory")
            run_memory_leak_detection || test_result=1
            ;;
        "static")
            run_static_analysis || test_result=1
            ;;
        "all")
            run_unit_tests || test_result=1
            run_integration_tests || test_result=1
            run_performance_tests || test_result=1
            
            # 如果是Debug构建，运行额外的检测
            if [[ "$BUILD_TYPE" == "Debug" ]]; then
                run_memory_leak_detection || true  # 不影响整体结果
                run_static_analysis || true        # 不影响整体结果
            fi
            ;;
        *)
            print_error "不支持的测试类型: $TEST_TYPE"
            show_usage
            exit 1
            ;;
    esac
    
    # 显示测试摘要
    print_info ""
    print_info "测试摘要:"
    print_info "========================================="
    print_info "总测试数: $TOTAL_TESTS"
    print_info "通过测试: $PASSED_TESTS"
    print_info "失败测试: $FAILED_TESTS"
    print_info "跳过测试: $SKIPPED_TESTS"
    print_info "测试平台: $PLATFORM"
    print_info "构建类型: $BUILD_TYPE"
    print_info "测试时间: $(date)"
    
    if [[ $test_result -eq 0 ]]; then
        print_success "所有测试通过！"
    else
        print_error "部分测试失败！"
    fi
    
    return $test_result
}

# 运行主函数
main "$@"