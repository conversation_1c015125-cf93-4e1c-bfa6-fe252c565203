#!/bin/bash

# 高精度授时服务器系统端到端集成测试脚本
# 执行完整的系统集成测试，包括时钟驯服流程、故障恢复、Web界面和性能测试

set -e

# 脚本参数
PLATFORM=${1:-"auto"}
BUILD_TYPE=${2:-"Debug"}
VERBOSE=${3:-"false"}
GENERATE_REPORT=${4:-"true"}

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

show_usage() {
    echo "用法: $0 [平台] [构建类型] [详细输出] [生成报告]"
    echo ""
    echo "平台选项:"
    echo "  auto        - 自动检测平台（默认）"
    echo "  linux       - Linux x86_64平台"
    echo "  loongarch64 - 龙芯LoongArch64平台"
    echo "  darwin      - macOS平台"
    echo ""
    echo "构建类型:"
    echo "  Debug       - 调试版本（默认）"
    echo "  Release     - 发布版本"
    echo ""
    echo "详细输出:"
    echo "  true        - 显示详细测试输出"
    echo "  false       - 简洁输出（默认）"
    echo ""
    echo "生成报告:"
    echo "  true        - 生成测试报告（默认）"
    echo "  false       - 不生成报告"
    echo ""
    echo "示例:"
    echo "  $0 linux Debug true true"
    echo "  $0 auto Release false false"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "项目根目录: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

# 自动检测平台
if [[ "$PLATFORM" == "auto" ]]; then
    case "$OSTYPE" in
        linux*)
            if [[ "$(uname -m)" == "loongarch64" ]]; then
                PLATFORM="loongarch64"
            else
                PLATFORM="linux"
            fi
            ;;
        darwin*)
            PLATFORM="darwin"
            ;;
        *)
            print_error "不支持的操作系统: $OSTYPE"
            exit 1
            ;;
    esac
fi

print_info "集成测试配置:"
print_info "  目标平台: $PLATFORM"
print_info "  构建类型: $BUILD_TYPE"
print_info "  详细输出: $VERBOSE"
print_info "  生成报告: $GENERATE_REPORT"

# 设置构建目录
BUILD_DIR="build-${PLATFORM}-${BUILD_TYPE}"

# 检查构建目录是否存在
if [[ ! -d "$BUILD_DIR" ]]; then
    print_error "构建目录不存在: $BUILD_DIR"
    print_info "请先运行构建脚本: ./build_scripts/build.sh $PLATFORM $BUILD_TYPE"
    exit 1
fi

# 进入构建目录
cd "$BUILD_DIR"

# 创建测试报告目录
REPORT_DIR="integration_test_report_$(date +%Y%m%d_%H%M%S)"
if [[ "$GENERATE_REPORT" == "true" ]]; then
    mkdir -p "$REPORT_DIR"
    print_info "测试报告目录: $REPORT_DIR"
fi

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

# 执行单个测试的函数
run_single_test() {
    local test_name="$1"
    local test_executable="$2"
    local test_description="$3"
    
    print_info "执行测试: $test_description"
    
    if [[ ! -x "$test_executable" ]]; then
        print_warning "测试可执行文件不存在: $test_executable"
        return 1
    fi
    
    local test_output_file=""
    if [[ "$GENERATE_REPORT" == "true" ]]; then
        test_output_file="$REPORT_DIR/${test_name}_output.txt"
    fi
    
    local test_start_time=$(date +%s)
    local test_result=0
    
    if [[ "$VERBOSE" == "true" ]]; then
        if [[ -n "$test_output_file" ]]; then
            "./$test_executable" 2>&1 | tee "$test_output_file"
            test_result=${PIPESTATUS[0]}
        else
            "./$test_executable"
            test_result=$?
        fi
    else
        if [[ -n "$test_output_file" ]]; then
            "./$test_executable" > "$test_output_file" 2>&1
            test_result=$?
        else
            "./$test_executable" > /dev/null 2>&1
            test_result=$?
        fi
    fi
    
    local test_end_time=$(date +%s)
    local test_duration=$((test_end_time - test_start_time))
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ $test_result -eq 0 ]]; then
        print_success "$test_description 通过 (耗时: ${test_duration}秒)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("PASS:$test_name:$test_duration")
    else
        print_error "$test_description 失败 (耗时: ${test_duration}秒)"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("FAIL:$test_name:$test_duration")
        
        # 如果详细输出关闭，显示失败测试的输出
        if [[ "$VERBOSE" == "false" && -n "$test_output_file" && -f "$test_output_file" ]]; then
            print_error "测试失败输出:"
            tail -20 "$test_output_file"
        fi
    fi
    
    return $test_result
}

# 检查系统依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查必要的库和工具
    if ! command -v make >/dev/null 2>&1; then
        missing_deps+=("make")
    fi
    
    if [[ "$PLATFORM" == "linux" || "$PLATFORM" == "loongarch64" ]]; then
        # 检查Linux特定依赖
        if ! ldconfig -p | grep -q libpthread; then
            missing_deps+=("libpthread")
        fi
        
        if ! ldconfig -p | grep -q librt; then
            missing_deps+=("librt")
        fi
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "缺少以下依赖:"
        for dep in "${missing_deps[@]}"; do
            print_error "  - $dep"
        done
        return 1
    fi
    
    print_success "系统依赖检查通过"
    return 0
}

# 预热系统
warmup_system() {
    print_info "系统预热..."
    
    # 运行一些基本操作来预热系统
    if [[ -x "./timing-server" ]]; then
        timeout 5s ./timing-server --test-mode >/dev/null 2>&1 || true
    fi
    
    # 清理可能的残留进程
    pkill -f "timing-server" >/dev/null 2>&1 || true
    pkill -f "test_" >/dev/null 2>&1 || true
    
    # 等待系统稳定
    sleep 2
    
    print_success "系统预热完成"
}

# 生成测试报告
generate_test_report() {
    if [[ "$GENERATE_REPORT" != "true" ]]; then
        return 0
    fi
    
    print_info "生成测试报告..."
    
    local report_file="$REPORT_DIR/integration_test_report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度授时服务器系统 - 端到端集成测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .pass { background-color: #d4edda; border-left: 4px solid #28a745; }
        .fail { background-color: #f8d7da; border-left: 4px solid #dc3545; }
        .details { margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>高精度授时服务器系统 - 端到端集成测试报告</h1>
        <p><strong>测试时间:</strong> $(date)</p>
        <p><strong>测试平台:</strong> $PLATFORM</p>
        <p><strong>构建类型:</strong> $BUILD_TYPE</p>
    </div>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <table>
            <tr><th>指标</th><th>数值</th></tr>
            <tr><td>总测试数</td><td>$TOTAL_TESTS</td></tr>
            <tr><td>通过测试</td><td>$PASSED_TESTS</td></tr>
            <tr><td>失败测试</td><td>$FAILED_TESTS</td></tr>
            <tr><td>成功率</td><td>$(( PASSED_TESTS * 100 / TOTAL_TESTS ))%</td></tr>
        </table>
    </div>
    
    <div class="details">
        <h2>测试详情</h2>
EOF

    # 添加测试结果详情
    for result in "${TEST_RESULTS[@]}"; do
        IFS=':' read -r status test_name duration <<< "$result"
        
        if [[ "$status" == "PASS" ]]; then
            cat >> "$report_file" << EOF
        <div class="test-result pass">
            <strong>✓ $test_name</strong> - 通过 (耗时: ${duration}秒)
        </div>
EOF
        else
            cat >> "$report_file" << EOF
        <div class="test-result fail">
            <strong>✗ $test_name</strong> - 失败 (耗时: ${duration}秒)
        </div>
EOF
        fi
    done
    
    cat >> "$report_file" << EOF
    </div>
</body>
</html>
EOF
    
    print_success "测试报告已生成: $report_file"
    
    # 如果在macOS上，尝试打开报告
    if [[ "$OSTYPE" == "darwin"* ]] && command -v open >/dev/null 2>&1; then
        open "$report_file"
    fi
}

# 主测试流程
main() {
    print_info "开始端到端集成测试..."
    print_info "测试开始时间: $(date)"
    
    # 检查依赖
    if ! check_dependencies; then
        print_error "依赖检查失败"
        exit 1
    fi
    
    # 系统预热
    warmup_system
    
    # 定义测试列表
    declare -A TESTS=(
        ["end_to_end_integration"]="test_end_to_end_integration:端到端集成测试 - 完整时钟驯服流程和状态转换"
        ["system_performance"]="test_system_performance:系统性能测试 - 资源使用优化和性能指标验证"
        ["web_interface_functionality"]="test_web_interface_functionality:Web界面功能测试 - API和界面完整功能验证"
        ["api_integration"]="test_api_integration:API集成测试 - REST API端点功能验证"
        ["websocket_integration"]="test_websocket_integration:WebSocket集成测试 - 实时通信功能验证"
    )
    
    # 按顺序执行测试
    local test_order=(
        "end_to_end_integration"
        "system_performance"
        "web_interface_functionality"
        "api_integration"
        "websocket_integration"
    )
    
    local overall_result=0
    
    for test_key in "${test_order[@]}"; do
        if [[ -n "${TESTS[$test_key]}" ]]; then
            IFS=':' read -r executable description <<< "${TESTS[$test_key]}"
            
            if ! run_single_test "$test_key" "$executable" "$description"; then
                overall_result=1
                
                # 如果是关键测试失败，可以选择停止后续测试
                if [[ "$test_key" == "end_to_end_integration" ]]; then
                    print_warning "核心集成测试失败，但继续执行其他测试..."
                fi
            fi
        fi
    done
    
    # 生成测试报告
    generate_test_report
    
    # 显示测试摘要
    print_info ""
    print_info "端到端集成测试摘要:"
    print_info "========================================="
    print_info "总测试数: $TOTAL_TESTS"
    print_info "通过测试: $PASSED_TESTS"
    print_info "失败测试: $FAILED_TESTS"
    
    if [[ $TOTAL_TESTS -gt 0 ]]; then
        local success_rate=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
        print_info "成功率: ${success_rate}%"
    fi
    
    print_info "测试平台: $PLATFORM"
    print_info "构建类型: $BUILD_TYPE"
    print_info "测试结束时间: $(date)"
    
    if [[ "$GENERATE_REPORT" == "true" ]]; then
        print_info "详细报告: $REPORT_DIR/integration_test_report.html"
    fi
    
    # 根据需求验证测试结果
    print_info ""
    print_info "需求验证结果:"
    print_info "========================================="
    
    # 需求1.1, 1.2, 1.3 - 完整的时钟驯服流程和状态转换
    if [[ " ${TEST_RESULTS[*]} " =~ " PASS:end_to_end_integration:" ]]; then
        print_success "✓ 需求1.1-1.3: 完整的时钟驯服流程和状态转换 - 通过"
    else
        print_error "✗ 需求1.1-1.3: 完整的时钟驯服流程和状态转换 - 失败"
    fi
    
    # 需求4.1 - 多时间源切换和故障恢复机制
    if [[ " ${TEST_RESULTS[*]} " =~ " PASS:end_to_end_integration:" ]]; then
        print_success "✓ 需求4.1: 多时间源切换和故障恢复机制 - 通过"
    else
        print_error "✗ 需求4.1: 多时间源切换和故障恢复机制 - 失败"
    fi
    
    # Web界面和API完整功能
    if [[ " ${TEST_RESULTS[*]} " =~ " PASS:web_interface_functionality:" ]] && \
       [[ " ${TEST_RESULTS[*]} " =~ " PASS:api_integration:" ]]; then
        print_success "✓ Web界面和API完整功能 - 通过"
    else
        print_error "✗ Web界面和API完整功能 - 失败"
    fi
    
    # 性能测试和资源使用优化
    if [[ " ${TEST_RESULTS[*]} " =~ " PASS:system_performance:" ]]; then
        print_success "✓ 性能测试和资源使用优化 - 通过"
    else
        print_error "✗ 性能测试和资源使用优化 - 失败"
    fi
    
    if [[ $overall_result -eq 0 ]]; then
        print_success "所有端到端集成测试通过！"
        print_success "系统已准备好进行长期稳定性测试"
    else
        print_error "部分端到端集成测试失败！"
        print_error "请检查失败的测试并修复问题"
    fi
    
    return $overall_result
}

# 运行主函数
main "$@"