#!/bin/bash

# 高精度授时服务器系统部署包创建脚本
# 支持自动硬件检测和配置生成

set -e

# 脚本参数
PLATFORM=${1:-"auto"}
BUILD_TYPE=${2:-"Release"}
PACKAGE_TYPE=${3:-"all"}
OUTPUT_DIR=${4:-"./packages"}

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

show_usage() {
    echo "用法: $0 [平台] [构建类型] [包类型] [输出目录]"
    echo ""
    echo "平台选项:"
    echo "  auto        - 自动检测平台（默认）"
    echo "  linux       - Linux x86_64平台"
    echo "  loongarch64 - 龙芯LoongArch64平台"
    echo "  darwin      - macOS平台"
    echo ""
    echo "构建类型:"
    echo "  Debug       - 调试版本"
    echo "  Release     - 发布版本（默认）"
    echo ""
    echo "包类型:"
    echo "  all         - 创建所有类型的包（默认）"
    echo "  tar         - 仅创建tar.gz包"
    echo "  deb         - 仅创建deb包"
    echo "  rpm         - 仅创建rpm包"
    echo "  installer   - 仅创建安装脚本包"
    echo ""
    echo "示例:"
    echo "  $0 linux Release all ./dist"
    echo "  $0 loongarch64 Release deb"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "项目根目录: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

# 自动检测平台
if [[ "$PLATFORM" == "auto" ]]; then
    case "$OSTYPE" in
        linux*)
            if [[ "$(uname -m)" == "loongarch64" ]]; then
                PLATFORM="loongarch64"
            else
                PLATFORM="linux"
            fi
            ;;
        darwin*)
            PLATFORM="darwin"
            ;;
        *)
            print_error "不支持的操作系统: $OSTYPE"
            exit 1
            ;;
    esac
fi

print_info "目标平台: $PLATFORM"
print_info "构建类型: $BUILD_TYPE"
print_info "包类型: $PACKAGE_TYPE"

# 设置构建目录
BUILD_DIR="build-${PLATFORM}-${BUILD_TYPE}"

# 检查构建目录是否存在
if [[ ! -d "$BUILD_DIR" ]]; then
    print_error "构建目录不存在: $BUILD_DIR"
    print_info "请先运行构建脚本: ./build_scripts/build.sh $PLATFORM $BUILD_TYPE"
    exit 1
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"
OUTPUT_DIR="$(cd "$OUTPUT_DIR" && pwd)"

print_info "输出目录: $OUTPUT_DIR"

# 获取版本信息
VERSION=$(grep "project.*VERSION" CMakeLists.txt | sed -n 's/.*VERSION \([0-9.]*\).*/\1/p')
if [[ -z "$VERSION" ]]; then
    VERSION="1.0.0"
fi

GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_DATE=$(date -u '+%Y%m%d-%H%M%S')

print_info "版本信息: $VERSION"
print_info "Git提交: $GIT_COMMIT"
print_info "构建时间: $BUILD_DATE"

# 设置包名
PACKAGE_NAME="timing-server-system"
PACKAGE_VERSION="${VERSION}-${BUILD_DATE}-${GIT_COMMIT}"
PACKAGE_FULL_NAME="${PACKAGE_NAME}-${PACKAGE_VERSION}-${PLATFORM}"

print_info "包名: $PACKAGE_FULL_NAME"

# 创建临时打包目录
TEMP_DIR=$(mktemp -d)
PACKAGE_ROOT="$TEMP_DIR/$PACKAGE_FULL_NAME"
mkdir -p "$PACKAGE_ROOT"

print_info "临时目录: $TEMP_DIR"

# 清理函数
cleanup() {
    print_info "清理临时文件..."
    rm -rf "$TEMP_DIR"
}
trap cleanup EXIT

# 复制二进制文件
print_info "复制二进制文件..."
mkdir -p "$PACKAGE_ROOT/bin"
cp "$BUILD_DIR/timing-server" "$PACKAGE_ROOT/bin/" 2>/dev/null || print_warning "主程序不存在"
cp "$BUILD_DIR/hal-demo" "$PACKAGE_ROOT/bin/" 2>/dev/null || print_warning "HAL演示程序不存在"

# 复制示例程序
if ls "$BUILD_DIR"/*-example >/dev/null 2>&1; then
    mkdir -p "$PACKAGE_ROOT/bin/examples"
    cp "$BUILD_DIR"/*-example "$PACKAGE_ROOT/bin/examples/" 2>/dev/null || true
fi

# 复制配置文件
print_info "复制配置文件..."
mkdir -p "$PACKAGE_ROOT/etc/timing-server"
cp -r platform/config/* "$PACKAGE_ROOT/etc/timing-server/" 2>/dev/null || true

# 生成硬件检测配置
print_info "生成硬件检测配置..."
cat > "$PACKAGE_ROOT/etc/timing-server/hardware-detection.json" << EOF
{
  "platform": "$PLATFORM",
  "build_type": "$BUILD_TYPE",
  "version": "$VERSION",
  "build_date": "$BUILD_DATE",
  "git_commit": "$GIT_COMMIT",
  "auto_detection": {
    "gnss_devices": [
      "/dev/ttyS0",
      "/dev/ttyS1",
      "/dev/ttyUSB0",
      "/dev/ttyUSB1"
    ],
    "pps_devices": [
      "/dev/pps0",
      "/dev/pps1"
    ],
    "rtc_devices": [
      "/dev/rtc0",
      "/dev/rtc1"
    ],
    "network_interfaces": [
      "eth0",
      "eth1",
      "enp0s3",
      "enp0s8"
    ]
  },
  "platform_specific": {
    "loongarch64": {
      "cpu_features": ["loongarch64", "lp64d"],
      "optimization_flags": ["-march=loongarch64", "-mabi=lp64d"]
    },
    "linux": {
      "cpu_features": ["x86_64", "sse4.2"],
      "optimization_flags": ["-march=native"]
    },
    "darwin": {
      "cpu_features": ["x86_64", "mock_hal"],
      "optimization_flags": ["-march=native"]
    }
  }
}
EOF

# 复制systemd服务文件
print_info "复制systemd服务文件..."
mkdir -p "$PACKAGE_ROOT/lib/systemd/system"
cp platform/systemd/timing-server.service "$PACKAGE_ROOT/lib/systemd/system/" 2>/dev/null || true

# 复制脚本文件
print_info "复制脚本文件..."
mkdir -p "$PACKAGE_ROOT/share/timing-server/scripts"
cp platform/*.sh "$PACKAGE_ROOT/share/timing-server/scripts/" 2>/dev/null || true
chmod +x "$PACKAGE_ROOT/share/timing-server/scripts"/*.sh 2>/dev/null || true

# 复制前端文件
print_info "复制前端文件..."
if [[ -d "frontend/dist" ]]; then
    mkdir -p "$PACKAGE_ROOT/share/timing-server/web"
    cp -r frontend/dist/* "$PACKAGE_ROOT/share/timing-server/web/" 2>/dev/null || true
else
    print_warning "前端构建文件不存在，跳过前端文件复制"
fi

# 复制文档
print_info "复制文档..."
mkdir -p "$PACKAGE_ROOT/share/doc/timing-server"
cp README.md "$PACKAGE_ROOT/share/doc/timing-server/" 2>/dev/null || true
cp -r docs/* "$PACKAGE_ROOT/share/doc/timing-server/" 2>/dev/null || true

# 创建目录结构
print_info "创建运行时目录结构..."
mkdir -p "$PACKAGE_ROOT/var/lib/timing-server"
mkdir -p "$PACKAGE_ROOT/var/log/timing-server"
mkdir -p "$PACKAGE_ROOT/etc/timing-server/ssl"

# 生成安装脚本
print_info "生成安装脚本..."
cat > "$PACKAGE_ROOT/install.sh" << 'EOF'
#!/bin/bash

# 高精度授时服务器系统安装脚本
# 支持自动硬件检测和配置生成

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[信息]${NC} $1"; }
print_success() { echo -e "${GREEN}[成功]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[警告]${NC} $1"; }
print_error() { echo -e "${RED}[错误]${NC} $1"; }

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    print_error "此脚本需要root权限运行"
    print_info "请使用: sudo $0"
    exit 1
fi

print_info "开始安装高精度授时服务器系统..."

# 获取安装包目录
INSTALL_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
print_info "安装包目录: $INSTALL_DIR"

# 检测系统平台
detect_platform() {
    local platform=""
    case "$OSTYPE" in
        linux*)
            if [[ "$(uname -m)" == "loongarch64" ]]; then
                platform="loongarch64"
            else
                platform="linux"
            fi
            ;;
        *)
            print_error "不支持的操作系统: $OSTYPE"
            exit 1
            ;;
    esac
    echo "$platform"
}

PLATFORM=$(detect_platform)
print_info "检测到平台: $PLATFORM"

# 硬件检测
detect_hardware() {
    print_info "检测硬件设备..."
    
    # 检测GNSS设备
    local gnss_devices=()
    for device in /dev/ttyS* /dev/ttyUSB*; do
        if [[ -e "$device" ]]; then
            gnss_devices+=("$device")
        fi
    done
    
    # 检测PPS设备
    local pps_devices=()
    for device in /dev/pps*; do
        if [[ -e "$device" ]]; then
            pps_devices+=("$device")
        fi
    done
    
    # 检测RTC设备
    local rtc_devices=()
    for device in /dev/rtc*; do
        if [[ -e "$device" ]]; then
            rtc_devices+=("$device")
        fi
    done
    
    # 检测网络接口
    local network_interfaces=()
    for interface in $(ls /sys/class/net/ 2>/dev/null); do
        if [[ "$interface" != "lo" ]]; then
            network_interfaces+=("$interface")
        fi
    done
    
    print_info "发现GNSS设备: ${gnss_devices[*]:-无}"
    print_info "发现PPS设备: ${pps_devices[*]:-无}"
    print_info "发现RTC设备: ${rtc_devices[*]:-无}"
    print_info "发现网络接口: ${network_interfaces[*]:-无}"
    
    # 生成硬件配置
    cat > /tmp/detected_hardware.json << EOL
{
  "gnss_devices": [$(printf '"%s",' "${gnss_devices[@]}" | sed 's/,$//')]
  "pps_devices": [$(printf '"%s",' "${pps_devices[@]}" | sed 's/,$//')]
  "rtc_devices": [$(printf '"%s",' "${rtc_devices[@]}" | sed 's/,$//')]
  "network_interfaces": [$(printf '"%s",' "${network_interfaces[@]}" | sed 's/,$//')]
}
EOL
}

# 安装系统依赖
install_dependencies() {
    print_info "安装系统依赖..."
    
    if command -v apt-get >/dev/null 2>&1; then
        apt-get update
        apt-get install -y \
            sqlite3 \
            libsqlite3-0 \
            openssl \
            libssl3 \
            chrony \
            ptp4l \
            linuxptp \
            gpsd \
            gpsd-clients
    elif command -v yum >/dev/null 2>&1; then
        yum install -y \
            sqlite \
            openssl \
            chrony \
            linuxptp \
            gpsd
    else
        print_warning "未知的包管理器，请手动安装依赖"
    fi
}

# 创建用户和组
create_user() {
    print_info "创建系统用户..."
    
    if ! getent group timing-server >/dev/null; then
        groupadd -r timing-server
        print_success "创建组: timing-server"
    fi
    
    if ! getent passwd timing-server >/dev/null; then
        useradd -r -g timing-server -d /var/lib/timing-server \
                -s /sbin/nologin -c "Timing Server System" timing-server
        print_success "创建用户: timing-server"
    fi
}

# 安装文件
install_files() {
    print_info "安装程序文件..."
    
    # 复制二进制文件
    cp -r "$INSTALL_DIR/bin"/* /usr/local/bin/ 2>/dev/null || true
    chmod +x /usr/local/bin/timing-server 2>/dev/null || true
    
    # 复制配置文件
    cp -r "$INSTALL_DIR/etc"/* /etc/ 2>/dev/null || true
    
    # 复制systemd服务文件
    cp -r "$INSTALL_DIR/lib"/* /lib/ 2>/dev/null || true
    
    # 复制共享文件
    cp -r "$INSTALL_DIR/share"/* /usr/share/ 2>/dev/null || true
    
    # 创建运行时目录
    mkdir -p /var/lib/timing-server
    mkdir -p /var/log/timing-server
    mkdir -p /etc/timing-server/ssl
    
    # 设置权限
    chown -R timing-server:timing-server /var/lib/timing-server
    chown -R timing-server:timing-server /var/log/timing-server
    chown -R timing-server:timing-server /etc/timing-server
    
    chmod 755 /var/lib/timing-server
    chmod 755 /var/log/timing-server
    chmod 750 /etc/timing-server
    chmod 700 /etc/timing-server/ssl
}

# 生成配置文件
generate_config() {
    print_info "生成配置文件..."
    
    # 检测硬件
    detect_hardware
    
    # 合并硬件检测结果到配置文件
    if [[ -f "/tmp/detected_hardware.json" && -f "/etc/timing-server/config.json" ]]; then
        # 这里可以使用jq工具合并JSON配置
        print_info "硬件检测完成，配置文件已更新"
    fi
}

# 启用服务
enable_service() {
    print_info "启用系统服务..."
    
    systemctl daemon-reload
    systemctl enable timing-server.service
    
    print_success "服务已启用，可以使用以下命令管理:"
    print_info "启动服务: systemctl start timing-server"
    print_info "停止服务: systemctl stop timing-server"
    print_info "查看状态: systemctl status timing-server"
    print_info "查看日志: journalctl -u timing-server -f"
}

# 主安装流程
main() {
    detect_hardware
    install_dependencies
    create_user
    install_files
    generate_config
    enable_service
    
    print_success "高精度授时服务器系统安装完成！"
    print_info "配置文件位置: /etc/timing-server/"
    print_info "日志文件位置: /var/log/timing-server/"
    print_info "数据文件位置: /var/lib/timing-server/"
    print_info ""
    print_info "请根据实际硬件配置修改 /etc/timing-server/config.json"
    print_info "然后运行: systemctl start timing-server"
}

main "$@"
EOF

chmod +x "$PACKAGE_ROOT/install.sh"

# 生成卸载脚本
print_info "生成卸载脚本..."
cat > "$PACKAGE_ROOT/uninstall.sh" << 'EOF'
#!/bin/bash

# 高精度授时服务器系统卸载脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[信息]${NC} $1"; }
print_success() { echo -e "${GREEN}[成功]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[警告]${NC} $1"; }
print_error() { echo -e "${RED}[错误]${NC} $1"; }

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    print_error "此脚本需要root权限运行"
    print_info "请使用: sudo $0"
    exit 1
fi

print_info "开始卸载高精度授时服务器系统..."

# 停止服务
print_info "停止服务..."
systemctl stop timing-server.service 2>/dev/null || true
systemctl disable timing-server.service 2>/dev/null || true

# 删除服务文件
print_info "删除服务文件..."
rm -f /lib/systemd/system/timing-server.service
systemctl daemon-reload

# 删除程序文件
print_info "删除程序文件..."
rm -f /usr/local/bin/timing-server
rm -f /usr/local/bin/hal-demo
rm -rf /usr/local/bin/examples
rm -rf /usr/share/timing-server

# 询问是否删除配置和数据
read -p "是否删除配置文件和数据? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "删除配置和数据文件..."
    rm -rf /etc/timing-server
    rm -rf /var/lib/timing-server
    rm -rf /var/log/timing-server
    
    # 删除用户和组
    userdel timing-server 2>/dev/null || true
    groupdel timing-server 2>/dev/null || true
    
    print_success "配置和数据已删除"
else
    print_info "保留配置和数据文件"
fi

print_success "高精度授时服务器系统卸载完成！"
EOF

chmod +x "$PACKAGE_ROOT/uninstall.sh"

# 生成版本信息文件
print_info "生成版本信息文件..."
cat > "$PACKAGE_ROOT/VERSION" << EOF
Package: $PACKAGE_NAME
Version: $VERSION
Platform: $PLATFORM
Build Type: $BUILD_TYPE
Build Date: $BUILD_DATE
Git Commit: $GIT_COMMIT
EOF

# 生成README文件
print_info "生成README文件..."
cat > "$PACKAGE_ROOT/README.txt" << EOF
高精度授时服务器系统部署包
============================

版本: $VERSION
平台: $PLATFORM
构建类型: $BUILD_TYPE
构建时间: $BUILD_DATE

安装说明:
1. 解压部署包到目标目录
2. 运行安装脚本: sudo ./install.sh
3. 根据硬件配置修改 /etc/timing-server/config.json
4. 启动服务: sudo systemctl start timing-server

卸载说明:
运行卸载脚本: sudo ./uninstall.sh

目录结构:
- bin/                  可执行文件
- etc/                  配置文件
- lib/                  系统服务文件
- share/                共享文件和脚本
- var/                  运行时目录结构
- install.sh            安装脚本
- uninstall.sh          卸载脚本
- VERSION               版本信息
- README.txt            说明文件

更多信息请参考: share/doc/timing-server/
EOF

# 创建不同类型的包
create_packages() {
    print_info "创建部署包..."
    
    # 创建tar.gz包
    if [[ "$PACKAGE_TYPE" == "all" || "$PACKAGE_TYPE" == "tar" ]]; then
        print_info "创建tar.gz包..."
        cd "$TEMP_DIR"
        tar -czf "$OUTPUT_DIR/${PACKAGE_FULL_NAME}.tar.gz" "$PACKAGE_FULL_NAME"
        print_success "创建完成: ${PACKAGE_FULL_NAME}.tar.gz"
    fi
    
    # 创建deb包（仅Linux平台）
    if [[ ("$PACKAGE_TYPE" == "all" || "$PACKAGE_TYPE" == "deb") && ("$PLATFORM" == "linux" || "$PLATFORM" == "loongarch64") ]]; then
        if command -v dpkg-deb >/dev/null 2>&1; then
            print_info "创建deb包..."
            
            # 创建DEBIAN控制目录
            mkdir -p "$PACKAGE_ROOT/DEBIAN"
            
            # 生成control文件
            cat > "$PACKAGE_ROOT/DEBIAN/control" << EOF
Package: timing-server-system
Version: $PACKAGE_VERSION
Section: net
Priority: optional
Architecture: $(dpkg --print-architecture 2>/dev/null || echo "amd64")
Depends: sqlite3, libsqlite3-0, openssl, libssl3, chrony, linuxptp, gpsd
Maintainer: Timing Server Team <<EMAIL>>
Description: 高精度授时服务器系统
 提供高精度时间同步服务，集成多种时间源包括GNSS接收机、
 铷原子钟、高精度RTC、外部1PPS和10MHz信号。
 .
 支持PTP特级主时钟和NTP Stratum 1服务。
EOF
            
            # 生成postinst脚本
            cat > "$PACKAGE_ROOT/DEBIAN/postinst" << 'EOF'
#!/bin/bash
set -e

# 创建用户和组
if ! getent group timing-server >/dev/null; then
    groupadd -r timing-server
fi

if ! getent passwd timing-server >/dev/null; then
    useradd -r -g timing-server -d /var/lib/timing-server \
            -s /sbin/nologin -c "Timing Server System" timing-server
fi

# 设置权限
chown -R timing-server:timing-server /var/lib/timing-server
chown -R timing-server:timing-server /var/log/timing-server
chown -R timing-server:timing-server /etc/timing-server

# 启用服务
systemctl daemon-reload
systemctl enable timing-server.service

echo "高精度授时服务器系统安装完成"
echo "请配置 /etc/timing-server/config.json 后启动服务"
echo "启动命令: systemctl start timing-server"
EOF
            
            chmod +x "$PACKAGE_ROOT/DEBIAN/postinst"
            
            # 生成prerm脚本
            cat > "$PACKAGE_ROOT/DEBIAN/prerm" << 'EOF'
#!/bin/bash
set -e

# 停止服务
systemctl stop timing-server.service 2>/dev/null || true
systemctl disable timing-server.service 2>/dev/null || true
EOF
            
            chmod +x "$PACKAGE_ROOT/DEBIAN/prerm"
            
            # 构建deb包
            dpkg-deb --build "$PACKAGE_ROOT" "$OUTPUT_DIR/${PACKAGE_FULL_NAME}.deb"
            print_success "创建完成: ${PACKAGE_FULL_NAME}.deb"
        else
            print_warning "dpkg-deb未找到，跳过deb包创建"
        fi
    fi
    
    # 创建rpm包（仅Linux平台）
    if [[ ("$PACKAGE_TYPE" == "all" || "$PACKAGE_TYPE" == "rpm") && ("$PLATFORM" == "linux" || "$PLATFORM" == "loongarch64") ]]; then
        if command -v rpmbuild >/dev/null 2>&1; then
            print_info "创建rpm包..."
            
            # 创建RPM构建目录结构
            RPM_BUILD_DIR="$TEMP_DIR/rpmbuild"
            mkdir -p "$RPM_BUILD_DIR"/{BUILD,RPMS,SOURCES,SPECS,SRPMS}
            
            # 创建源码包
            cd "$TEMP_DIR"
            tar -czf "$RPM_BUILD_DIR/SOURCES/${PACKAGE_FULL_NAME}.tar.gz" "$PACKAGE_FULL_NAME"
            
            # 生成spec文件
            cat > "$RPM_BUILD_DIR/SPECS/timing-server-system.spec" << EOF
Name:           timing-server-system
Version:        $VERSION
Release:        1%{?dist}
Summary:        高精度授时服务器系统

License:        MIT
URL:            https://github.com/timing-server/timing-server-system
Source0:        %{name}-%{version}.tar.gz

BuildRequires:  cmake >= 3.16
BuildRequires:  gcc-c++
BuildRequires:  sqlite-devel
BuildRequires:  openssl-devel

Requires:       sqlite
Requires:       openssl
Requires:       chrony
Requires:       linuxptp
Requires:       gpsd

%description
高精度授时服务器系统，提供高精度时间同步服务，集成多种时间源
包括GNSS接收机、铷原子钟、高精度RTC、外部1PPS和10MHz信号。
支持PTP特级主时钟和NTP Stratum 1服务。

%prep
%setup -q

%build
# 已预构建

%install
rm -rf %{buildroot}
mkdir -p %{buildroot}

# 复制文件
cp -r bin %{buildroot}/usr/local/
cp -r etc %{buildroot}/
cp -r lib %{buildroot}/
cp -r share %{buildroot}/usr/
cp -r var %{buildroot}/

%files
/usr/local/bin/*
/etc/timing-server/*
/lib/systemd/system/timing-server.service
/usr/share/timing-server/*
%dir /var/lib/timing-server
%dir /var/log/timing-server

%post
# 创建用户和组
getent group timing-server >/dev/null || groupadd -r timing-server
getent passwd timing-server >/dev/null || \
    useradd -r -g timing-server -d /var/lib/timing-server \
            -s /sbin/nologin -c "Timing Server System" timing-server

# 设置权限
chown -R timing-server:timing-server /var/lib/timing-server
chown -R timing-server:timing-server /var/log/timing-server
chown -R timing-server:timing-server /etc/timing-server

# 启用服务
systemctl daemon-reload
systemctl enable timing-server.service

%preun
systemctl stop timing-server.service 2>/dev/null || true
systemctl disable timing-server.service 2>/dev/null || true

%changelog
* $(date +'%a %b %d %Y') Timing Server Team <<EMAIL>> - $VERSION-1
- Initial package
EOF
            
            # 构建RPM包
            rpmbuild --define "_topdir $RPM_BUILD_DIR" -ba "$RPM_BUILD_DIR/SPECS/timing-server-system.spec"
            
            # 复制生成的RPM包
            find "$RPM_BUILD_DIR/RPMS" -name "*.rpm" -exec cp {} "$OUTPUT_DIR/" \;
            print_success "创建完成: RPM包"
        else
            print_warning "rpmbuild未找到，跳过rpm包创建"
        fi
    fi
    
    # 创建安装脚本包
    if [[ "$PACKAGE_TYPE" == "all" || "$PACKAGE_TYPE" == "installer" ]]; then
        print_info "创建安装脚本包..."
        
        # 创建自解压安装脚本
        cat > "$OUTPUT_DIR/${PACKAGE_FULL_NAME}-installer.sh" << 'EOF'
#!/bin/bash

# 高精度授时服务器系统自解压安装脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[信息]${NC} $1"; }
print_success() { echo -e "${GREEN}[成功]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[警告]${NC} $1"; }
print_error() { echo -e "${RED}[错误]${NC} $1"; }

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    print_error "此脚本需要root权限运行"
    print_info "请使用: sudo $0"
    exit 1
fi

print_info "高精度授时服务器系统自解压安装程序"
print_info "正在解压安装包..."

# 创建临时目录
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

# 解压嵌入的tar包（从脚本末尾开始）
ARCHIVE_LINE=$(awk '/^__ARCHIVE_BELOW__/ {print NR + 1; exit 0; }' "$0")
tail -n +$ARCHIVE_LINE "$0" | tar -xzf -

# 运行安装脚本
PACKAGE_DIR=$(ls -d */ | head -n 1)
cd "$PACKAGE_DIR"
./install.sh

# 清理临时文件
cd /
rm -rf "$TEMP_DIR"

print_success "安装完成！"
exit 0

__ARCHIVE_BELOW__
EOF
        
        # 将tar包附加到安装脚本
        cat "$OUTPUT_DIR/${PACKAGE_FULL_NAME}.tar.gz" >> "$OUTPUT_DIR/${PACKAGE_FULL_NAME}-installer.sh"
        chmod +x "$OUTPUT_DIR/${PACKAGE_FULL_NAME}-installer.sh"
        
        print_success "创建完成: ${PACKAGE_FULL_NAME}-installer.sh"
    fi
}

# 创建包
create_packages

# 生成校验和文件
print_info "生成校验和文件..."
cd "$OUTPUT_DIR"
for file in *; do
    if [[ -f "$file" ]]; then
        sha256sum "$file" >> "${PACKAGE_FULL_NAME}.sha256"
    fi
done

# 显示结果
print_success "部署包创建完成！"
print_info "输出目录: $OUTPUT_DIR"
print_info "生成的文件:"
ls -la "$OUTPUT_DIR"

print_info ""
print_info "安装说明:"
print_info "1. tar.gz包: tar -xzf <包名> && cd <目录> && sudo ./install.sh"
print_info "2. deb包: sudo dpkg -i <包名>"
print_info "3. rpm包: sudo rpm -ivh <包名>"
print_info "4. 安装脚本: sudo bash <安装脚本名>"