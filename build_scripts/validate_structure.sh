#!/bin/bash

# 高精度授时服务器系统结构验证脚本
# 验证项目结构完整性和构建环境

set -e

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "项目根目录: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 检查函数
check_file() {
    local file="$1"
    local description="$2"
    local required="${3:-true}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [[ -f "$file" ]]; then
        print_success "✓ $description: $file"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [[ "$required" == "true" ]]; then
            print_error "✗ $description: $file (必需文件缺失)"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        else
            print_warning "⚠ $description: $file (可选文件缺失)"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            return 0
        fi
    fi
}

check_directory() {
    local dir="$1"
    local description="$2"
    local required="${3:-true}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [[ -d "$dir" ]]; then
        print_success "✓ $description: $dir"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [[ "$required" == "true" ]]; then
            print_error "✗ $description: $dir (必需目录缺失)"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        else
            print_warning "⚠ $description: $dir (可选目录缺失)"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
            return 0
        fi
    fi
}

check_executable() {
    local file="$1"
    local description="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [[ -f "$file" && -x "$file" ]]; then
        print_success "✓ $description: $file (可执行)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    elif [[ -f "$file" ]]; then
        print_error "✗ $description: $file (文件存在但不可执行)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    else
        print_error "✗ $description: $file (文件不存在)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

print_info "开始验证项目结构..."
echo ""

# 1. 验证根目录文件
print_info "=== 验证根目录文件 ==="
check_file "CMakeLists.txt" "根CMake配置文件"
check_file "README.md" "项目说明文件"
check_file ".gitignore" "Git忽略文件"

echo ""

# 2. 验证后端结构
print_info "=== 验证后端结构 ==="
check_directory "backend" "后端目录"
check_directory "backend/include" "头文件目录"
check_directory "backend/src" "源文件目录"
check_directory "backend/tests" "测试目录"
check_directory "backend/examples" "示例目录"

# 验证核心头文件
check_file "backend/include/core/types.h" "核心类型定义"
check_file "backend/include/core/timing_engine.h" "授时引擎头文件"
check_file "backend/include/core/clock_state_machine.h" "状态机头文件"
check_file "backend/include/hal/interfaces.h" "HAL接口定义"

# 验证核心源文件
check_file "backend/src/core/timing_engine.cpp" "授时引擎实现"
check_file "backend/src/core/clock_state_machine.cpp" "状态机实现"
check_file "backend/src/main.cpp" "主程序入口"

# 验证HAL实现
check_directory "backend/src/hal/linux" "Linux HAL实现"
check_directory "backend/src/hal/mock" "Mock HAL实现"
check_file "backend/src/hal/hal_factory.cpp" "HAL工厂实现"

echo ""

# 3. 验证前端结构
print_info "=== 验证前端结构 ==="
check_directory "frontend" "前端目录"
check_file "frontend/package.json" "前端包配置"
check_file "frontend/vite.config.ts" "Vite配置文件"
check_directory "frontend/src" "前端源码目录"
check_file "frontend/src/main.ts" "前端入口文件"

echo ""

# 4. 验证构建脚本
print_info "=== 验证构建脚本 ==="
check_directory "build_scripts" "构建脚本目录"
check_executable "build_scripts/build.sh" "主构建脚本"
check_executable "build_scripts/validate_structure.sh" "结构验证脚本"
check_directory "build_scripts/toolchains" "工具链目录"
check_file "build_scripts/toolchains/loongarch64-linux-gnu.cmake" "龙芯工具链配置"

echo ""

# 5. 验证平台配置
print_info "=== 验证平台配置 ==="
check_directory "platform" "平台配置目录"
check_directory "platform/config" "配置文件目录"
check_directory "platform/systemd" "systemd服务目录"

check_file "platform/config/config.json" "主配置文件"
check_file "platform/config/security.json" "安全配置文件"
check_file "platform/config/users.json" "用户配置文件"
check_file "platform/systemd/timing-server.service" "systemd服务文件"

check_executable "platform/install.sh" "安装脚本"
check_executable "platform/uninstall.sh" "卸载脚本"
check_executable "platform/timing-server-wrapper.sh" "启动包装脚本"
check_executable "platform/system-monitor.sh" "系统监控脚本"

echo ""

# 6. 验证文档
print_info "=== 验证文档 ==="
check_directory "docs" "文档目录"
check_file "docs/deployment-guide.md" "部署指南"
check_file "docs/hal-architecture.md" "HAL架构文档"
check_file "docs/installation-improvements.md" "安装改进文档"
check_file "docs/README.md" "文档说明" false

echo ""

# 7. 验证构建环境
print_info "=== 验证构建环境 ==="

# 检查必需的命令
commands=("cmake" "make" "gcc" "g++")
for cmd in "${commands[@]}"; do
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if command -v "$cmd" &> /dev/null; then
        version=$($cmd --version | head -n1)
        print_success "✓ 命令可用: $cmd ($version)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        print_error "✗ 命令缺失: $cmd"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
done

# 检查CMake版本
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if command -v cmake &> /dev/null; then
    cmake_version=$(cmake --version | head -n1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+')
    required_version="3.16.0"
    
    if [[ "$(printf '%s\n' "$required_version" "$cmake_version" | sort -V | head -n1)" == "$required_version" ]]; then
        print_success "✓ CMake版本满足要求: $cmake_version (>= $required_version)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        print_error "✗ CMake版本过低: $cmake_version (需要 >= $required_version)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
else
    print_error "✗ CMake未安装"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

echo ""

# 8. 验证可选依赖
print_info "=== 验证可选依赖 ==="

optional_commands=("sqlite3" "openssl" "systemctl" "journalctl")
for cmd in "${optional_commands[@]}"; do
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if command -v "$cmd" &> /dev/null; then
        print_success "✓ 可选命令可用: $cmd"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        print_warning "⚠ 可选命令缺失: $cmd"
        WARNING_CHECKS=$((WARNING_CHECKS + 1))
    fi
done

echo ""

# 9. 验证权限
print_info "=== 验证文件权限 ==="

# 检查脚本执行权限
scripts=("build_scripts/build.sh" "platform/install.sh" "platform/uninstall.sh" 
         "platform/timing-server-wrapper.sh" "platform/system-monitor.sh")

for script in "${scripts[@]}"; do
    if [[ -f "$script" ]]; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if [[ -x "$script" ]]; then
            print_success "✓ 脚本可执行: $script"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            print_error "✗ 脚本不可执行: $script"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        fi
    fi
done

echo ""

# 10. 验证CMake安装配置
print_info "=== 验证CMake安装配置 ==="

# 检查CMakeLists.txt中的安装配置
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -q "install(DIRECTORY platform/config/" CMakeLists.txt; then
    print_success "✓ 配置文件安装规则已配置"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    print_error "✗ 配置文件安装规则缺失"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -q "install(FILES platform/systemd/timing-server.service" CMakeLists.txt; then
    print_success "✓ systemd服务安装规则已配置"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    print_error "✗ systemd服务安装规则缺失"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -q "install(PROGRAMS.*platform.*\.sh" CMakeLists.txt; then
    print_success "✓ 脚本文件安装规则已配置"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    print_error "✗ 脚本文件安装规则缺失"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -q "create_symlink" CMakeLists.txt; then
    print_success "✓ 符号链接创建规则已配置"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    print_warning "⚠ 符号链接创建规则缺失"
    WARNING_CHECKS=$((WARNING_CHECKS + 1))
fi

echo ""

# 11. 生成验证报告
print_info "=== 验证报告 ==="
echo "总检查项: $TOTAL_CHECKS"
echo "通过: $PASSED_CHECKS"
echo "失败: $FAILED_CHECKS"
echo "警告: $WARNING_CHECKS"

echo ""

if [[ $FAILED_CHECKS -eq 0 ]]; then
    print_success "✓ 项目结构验证通过！"
    if [[ $WARNING_CHECKS -gt 0 ]]; then
        print_warning "存在 $WARNING_CHECKS 个警告项，但不影响构建"
    fi
    echo ""
    print_info "可以继续进行构建:"
    echo "  ./build_scripts/build.sh native Release"
    echo "  ./build_scripts/build.sh loongarch64 Release"
    exit 0
else
    print_error "✗ 项目结构验证失败！"
    print_error "请修复上述 $FAILED_CHECKS 个错误后重新验证"
    echo ""
    print_info "常见解决方案:"
    echo "  1. 确保所有必需文件存在"
    echo "  2. 设置脚本执行权限: chmod +x script_name.sh"
    echo "  3. 安装缺失的构建工具"
    echo "  4. 检查CMake版本是否满足要求"
    exit 1
fi