# 龙芯LoongArch64交叉编译工具链文件
# 用于在x86_64主机上交叉编译龙芯平台的二进制文件

# 设置目标系统信息
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR loongarch64)

# 工具链前缀 - 根据实际安装的工具链调整
set(TOOLCHAIN_PREFIX loongarch64-linux-gnu)

# 设置编译器路径
# 假设工具链安装在 /opt/loongarch64-linux-gnu 目录下
# 实际使用时需要根据工具链安装位置调整
set(TOOLCHAIN_ROOT /opt/loongarch64-linux-gnu)

# 检查工具链是否存在
if(NOT EXISTS ${TOOLCHAIN_ROOT})
    message(WARNING "龙芯工具链目录不存在: ${TOOLCHAIN_ROOT}")
    message(WARNING "请确保已正确安装龙芯LoongArch64交叉编译工具链")
    message(WARNING "或修改 TOOLCHAIN_ROOT 变量指向正确的工具链路径")
endif()

# 设置编译器
set(CMAKE_C_COMPILER ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-gcc)
set(CMAKE_CXX_COMPILER ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-g++)
set(CMAKE_ASM_COMPILER ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-gcc)

# 设置其他工具
set(CMAKE_AR ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-ar)
set(CMAKE_LINKER ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-ld)
set(CMAKE_NM ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-nm)
set(CMAKE_OBJCOPY ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-objcopy)
set(CMAKE_OBJDUMP ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-objdump)
set(CMAKE_RANLIB ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-ranlib)
set(CMAKE_SIZE ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-size)
set(CMAKE_STRIP ${TOOLCHAIN_ROOT}/bin/${TOOLCHAIN_PREFIX}-strip)

# 设置系统根目录
set(CMAKE_SYSROOT ${TOOLCHAIN_ROOT}/sysroot)
set(CMAKE_FIND_ROOT_PATH ${TOOLCHAIN_ROOT}/sysroot)

# 设置查找模式
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# 龙芯特定的编译标志
# -march=loongarch64: 指定目标架构为LoongArch64
# -mabi=lp64d: 使用LP64D ABI (Long and Pointer 64-bit, Double precision floating point)
set(LOONGARCH_FLAGS "-march=loongarch64 -mabi=lp64d")

# 设置C编译标志
set(CMAKE_C_FLAGS_INIT "${LOONGARCH_FLAGS}")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${LOONGARCH_FLAGS}")

# 设置C++编译标志
set(CMAKE_CXX_FLAGS_INIT "${LOONGARCH_FLAGS}")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${LOONGARCH_FLAGS}")

# 设置汇编标志
set(CMAKE_ASM_FLAGS "${CMAKE_ASM_FLAGS} ${LOONGARCH_FLAGS}")

# 链接器标志
# 静态链接libgcc和libstdc++以减少目标系统的依赖
set(CMAKE_EXE_LINKER_FLAGS_INIT "-static-libgcc -static-libstdc++")
set(CMAKE_SHARED_LINKER_FLAGS_INIT "-static-libgcc -static-libstdc++")

# 优化标志
set(CMAKE_C_FLAGS_RELEASE "-O3 -DNDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")
set(CMAKE_C_FLAGS_DEBUG "-g -O0")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0")

# 设置交叉编译标志
set(CMAKE_CROSSCOMPILING TRUE)

# 禁用一些可能不兼容的特性
set(CMAKE_CROSSCOMPILING_EMULATOR "")

# 龙芯特定的预处理器定义
add_definitions(-D__loongarch64__)
add_definitions(-DLOONGARCH64)

# 打印配置信息
message(STATUS "配置龙芯LoongArch64交叉编译环境")
message(STATUS "工具链根目录: ${TOOLCHAIN_ROOT}")
message(STATUS "系统根目录: ${CMAKE_SYSROOT}")
message(STATUS "C编译器: ${CMAKE_C_COMPILER}")
message(STATUS "C++编译器: ${CMAKE_CXX_COMPILER}")
message(STATUS "编译标志: ${LOONGARCH_FLAGS}")

# 验证编译器是否存在
if(NOT EXISTS ${CMAKE_C_COMPILER})
    message(FATAL_ERROR "C编译器不存在: ${CMAKE_C_COMPILER}")
endif()

if(NOT EXISTS ${CMAKE_CXX_COMPILER})
    message(FATAL_ERROR "C++编译器不存在: ${CMAKE_CXX_COMPILER}")
endif()