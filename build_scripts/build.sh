#!/bin/bash

# 高精度授时服务器系统自动化构建脚本
# 支持多平台构建：Linux x86_64、龙芯LoongArch64、macOS开发环境

set -e  # 遇到错误立即退出

# 脚本参数
PLATFORM=${1:-"native"}
BUILD_TYPE=${2:-"Release"}
CLEAN_BUILD=${3:-"false"}

# 颜色输出定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [平台] [构建类型] [清理构建]"
    echo ""
    echo "平台选项:"
    echo "  native      - 本地平台构建（自动检测）"
    echo "  linux       - Linux x86_64平台"
    echo "  loongarch64 - 龙芯LoongArch64平台（交叉编译）"
    echo "  darwin      - macOS平台（开发环境）"
    echo ""
    echo "构建类型:"
    echo "  Debug       - 调试版本"
    echo "  Release     - 发布版本（默认）"
    echo ""
    echo "清理构建:"
    echo "  true        - 清理后重新构建"
    echo "  false       - 增量构建（默认）"
    echo ""
    echo "示例:"
    echo "  $0 native Release        # 本地发布版本构建"
    echo "  $0 loongarch64 Debug     # 龙芯调试版本交叉编译"
    echo "  $0 linux Release true    # Linux发布版本清理构建"
}

# 检查参数
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_info "项目根目录: $PROJECT_ROOT"
print_info "构建平台: $PLATFORM"
print_info "构建类型: $BUILD_TYPE"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 设置构建目录
BUILD_DIR="build-${PLATFORM}-${BUILD_TYPE}"

# 清理构建目录（如果需要）
if [[ "$CLEAN_BUILD" == "true" ]]; then
    print_info "清理构建目录: $BUILD_DIR"
    rm -rf "$BUILD_DIR"
fi

# 创建构建目录
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 根据平台设置CMake参数
CMAKE_ARGS=""
case $PLATFORM in
    "loongarch64")
        print_info "配置龙芯LoongArch64交叉编译环境"
        CMAKE_ARGS="-DCMAKE_TOOLCHAIN_FILE=../build_scripts/toolchains/loongarch64-linux-gnu.cmake"
        
        # 检查工具链是否存在
        TOOLCHAIN_ROOT="/opt/loongarch64-linux-gnu"
        if [[ ! -d "$TOOLCHAIN_ROOT" ]]; then
            print_warning "龙芯工具链目录不存在: $TOOLCHAIN_ROOT"
            print_warning "请确保已正确安装龙芯LoongArch64交叉编译工具链"
            print_info "可以从以下地址获取工具链:"
            print_info "https://github.com/loongson/build-tools"
        fi
        ;;
    "linux")
        print_info "配置Linux x86_64本地编译环境"
        CMAKE_ARGS=""
        ;;
    "darwin"|"native")
        print_info "配置本地编译环境"
        CMAKE_ARGS=""
        
        # 在macOS上检查是否安装了必要的工具
        if [[ "$OSTYPE" == "darwin"* ]]; then
            if ! command -v cmake &> /dev/null; then
                print_error "未找到cmake，请先安装: brew install cmake"
                exit 1
            fi
        fi
        ;;
    *)
        print_error "不支持的平台: $PLATFORM"
        print_info "支持的平台: native, linux, loongarch64, darwin"
        exit 1
        ;;
esac

# 检查CMake是否可用
if ! command -v cmake &> /dev/null; then
    print_error "未找到cmake，请先安装cmake"
    exit 1
fi

# 获取CPU核心数用于并行编译
if command -v nproc &> /dev/null; then
    NPROC=$(nproc)
elif command -v sysctl &> /dev/null; then
    NPROC=$(sysctl -n hw.ncpu)
else
    NPROC=4
fi

print_info "使用 $NPROC 个并行编译进程"

# 配置项目
print_info "配置CMake项目..."
cmake .. \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
    $CMAKE_ARGS

if [[ $? -ne 0 ]]; then
    print_error "CMake配置失败"
    exit 1
fi

print_success "CMake配置完成"

# 编译项目
print_info "开始编译项目..."
make -j"$NPROC"

if [[ $? -ne 0 ]]; then
    print_error "编译失败"
    exit 1
fi

print_success "编译完成"

# 运行测试（仅在Debug模式下）
if [[ "$BUILD_TYPE" == "Debug" ]]; then
    print_info "运行单元测试..."
    if command -v ctest &> /dev/null; then
        ctest --output-on-failure --parallel "$NPROC"
        if [[ $? -eq 0 ]]; then
            print_success "所有测试通过"
        else
            print_warning "部分测试失败"
        fi
    else
        print_warning "未找到ctest，跳过测试"
    fi
fi

# 创建部署包
print_info "创建部署包..."
make package

if [[ $? -eq 0 ]]; then
    print_success "部署包创建完成"
    
    # 显示生成的文件
    print_info "生成的文件:"
    ls -la *.tar.gz *.deb *.rpm 2>/dev/null || true
else
    print_warning "部署包创建失败"
fi

# 显示构建摘要
print_success "构建完成！"
print_info "构建目录: $BUILD_DIR"
print_info "可执行文件: $BUILD_DIR/timing-server"

# 如果是本地构建，提供运行建议
if [[ "$PLATFORM" == "native" || "$PLATFORM" == "darwin" ]]; then
    print_info "运行程序: ./$BUILD_DIR/timing-server --help"
fi

# 显示安装说明
if [[ "$PLATFORM" == "linux" || "$PLATFORM" == "loongarch64" ]]; then
    print_info "安装到系统: sudo make install"
    print_info "或使用生成的deb/rpm包进行安装"
fi