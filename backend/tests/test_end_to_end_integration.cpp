/**
 * @file test_end_to_end_integration.cpp
 * @brief 端到端集成测试 - 测试完整的时钟驯服流程和状态转换
 * 
 * 本测试文件实现了完整的系统集成测试，验证：
 * 1. 完整的时钟驯服流程和状态转换
 * 2. 多时间源切换和故障恢复机制
 * 3. Web界面和API的完整功能
 * 4. 性能测试和资源使用优化
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <thread>
#include <chrono>
#include <memory>
#include <vector>
#include <atomic>
#include <future>

// 核心组件头文件
#include "core/timing_engine.h"
#include "core/clock_state_machine.h"
#include "core/config_manager.h"
#include "core/daemon_manager.h"
#include "core/logger.h"
#include "core/error_handler.h"
#include "core/database_manager.h"

// HAL接口头文件
#include "mock/mock_hal_interfaces.h"
#include "hal/interfaces.h"
#include "hal/mock_hal_factory.h"
#include "hal/hal_factory.h"

// API组件头文件
#include "api/timing_service.h"
#include "api/auth_manager.h"

// 前向声明
namespace timing_server {
namespace api {
std::shared_ptr<TimingService> createTimingService(
    std::shared_ptr<core::TimingEngine> timing_engine,
    std::shared_ptr<core::ConfigManager> config_manager,
    std::shared_ptr<core::DatabaseManager> database_manager);
}
}
#include "api/websocket_server.h"

using namespace std::chrono_literals;
using ::testing::_;
using ::testing::Return;
using ::testing::InSequence;
using ::testing::AtLeast;

namespace timing_server {
namespace test {

/**
 * @class EndToEndIntegrationTest
 * @brief 端到端集成测试类
 * 
 * 测试完整的系统工作流程，从系统启动到稳定运行的全过程
 */
class EndToEndIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化单例日志系统
        auto& logger = core::Logger::GetInstance();
        logger.Initialize();
        logger_ = std::shared_ptr<core::Logger>(&logger, [](core::Logger*){});

        // 初始化配置管理器
        config_manager_ = std::make_shared<core::ConfigManager>("/tmp/test_config.json");
        config_manager_->Initialize();

        // 创建测试配置
        CreateTestConfiguration();

        // 初始化HAL工厂（使用Mock实现）
        hal_factory_ = std::make_shared<hal::MockHalFactory>();

        // 初始化单例核心组件
        auto& error_handler = core::ErrorHandler::GetInstance();
        error_handler.Initialize();
        error_handler_ = std::shared_ptr<core::ErrorHandler>(&error_handler, [](core::ErrorHandler*){});

        auto& database_manager = core::DatabaseManager::GetInstance();
        database_manager.Initialize(":memory:");
        database_manager_ = std::shared_ptr<core::DatabaseManager>(&database_manager, [](core::DatabaseManager*){});

        daemon_manager_ = std::make_shared<core::DaemonManager>(logger_, error_handler_);

        // 初始化授时引擎（使用正确的构造函数）
        core::TimingConfig timing_config;
        timing_engine_ = std::make_shared<core::TimingEngine>(timing_config);

        // 初始化API服务
        auth_manager_ = std::make_shared<api::AuthManager>(config_manager_, logger_);
        timing_service_ = std::make_shared<api::TimingServiceImpl>(timing_engine_, config_manager_, database_manager_);
        
        // 设置测试超时
        test_timeout_ = 30s;
    }
    
    void TearDown() override {
        // 停止所有服务
        if (timing_engine_) {
            timing_engine_->Stop();
        }
        
        // 清理资源
        timing_engine_.reset();
        timing_service_.reset();
        auth_manager_.reset();
        daemon_manager_.reset();
        database_manager_.reset();
        error_handler_.reset();
        hal_factory_.reset();
        config_manager_.reset();
        logger_.reset();
    }
    
    /**
     * @brief 创建测试配置
     */
    void CreateTestConfiguration() {
        core::TimingConfig config;
        
        // 时间源配置（注释掉不存在的成员）
        // config.gnss_config.enabled = true;
        // config.gnss_config.device_path = "/dev/ttyS0";
        // config.gnss_config.baud_rate = 9600;
        // config.gnss_config.priority = 1;
        // 
        // config.rubidium_config.enabled = true;
        // config.rubidium_config.device_path = "/dev/spidev0.0";
        // config.rubidium_config.priority = 2;
        // 
        // config.rtc_config.enabled = true;
        // config.rtc_config.device_path = "/dev/rtc0";
        // config.rtc_config.priority = 5;
        
        // 只设置支持的成员
        config.priorities = core::TimeSourcePriority{};
        config.discipline = core::DiscipliningParameters{};
        config.holdover = core::HoldoverParameters{};
        config.alarms = core::AlarmThresholds{};
        config.config_version = "test";
        config.last_modified = 0;
        
        // config.disciplining_params.convergence_threshold_ns = 50.0;
        // config.disciplining_params.holdover_timeout_hours = 24;
        // config.disciplining_params.learning_duration_hours = 72;
        // 
        // config.api_config.http_port = 8080;
        // config.api_config.websocket_port = 8081;
        // config.api_config.max_connections = 100;
        
        // config_manager_->SetConfiguration(config);
    }
    
    /**
     * @brief 设置Mock HAL的行为模式
     */
    void SetupMockHalBehavior() {
        // 获取Mock对象
        auto mock_gnss = std::dynamic_pointer_cast<hal::MockGnssReceiver>(
            hal_factory_->CreateGnssReceiver()
        );
        auto mock_pps = std::dynamic_pointer_cast<hal::MockPpsInput>(
            hal_factory_->CreatePpsInput()
        );
        auto mock_rubidium = std::dynamic_pointer_cast<hal::MockAtomicClock>(
            hal_factory_->CreateAtomicClock()
        );
        
        // 设置GNSS接收机行为
        EXPECT_CALL(*mock_gnss, Initialize())
            .WillOnce(Return(true));
        
        EXPECT_CALL(*mock_gnss, IsSignalValid())
            .WillRepeatedly(Return(true));
        
        EXPECT_CALL(*mock_gnss, ReadNmeaSentence())
            .WillRepeatedly(Return("$GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A"));
        
        // 设置PPS输入行为
        EXPECT_CALL(*mock_pps, Initialize())
            .WillOnce(Return(true));
        
        EXPECT_CALL(*mock_pps, WaitForPpsEdge(_))
            .WillRepeatedly(Return(true));
        
        // 设置铷钟行为
        EXPECT_CALL(*mock_rubidium, Initialize())
            .WillOnce(Return(true));
        
        EXPECT_CALL(*mock_rubidium, GetStatus())
            .WillRepeatedly(Return(hal::ClockState::LOCKED));
        
        EXPECT_CALL(*mock_rubidium, GetFrequencyOffset())
            .WillRepeatedly(Return(0.001)); // 1ppm偏移
    }
    
protected:
    std::shared_ptr<core::Logger> logger_;
    std::shared_ptr<core::ConfigManager> config_manager_;
    std::shared_ptr<hal::MockHalFactory> hal_factory_;
    std::shared_ptr<core::ErrorHandler> error_handler_;
    std::shared_ptr<core::DatabaseManager> database_manager_;
    std::shared_ptr<core::DaemonManager> daemon_manager_;
    std::shared_ptr<core::TimingEngine> timing_engine_;
    std::shared_ptr<api::AuthManager> auth_manager_;
    std::shared_ptr<api::TimingService> timing_service_;
    
    std::chrono::seconds test_timeout_;
};

/**
 * @test 测试完整的时钟驯服流程
 * 验证系统从启动到锁定状态的完整流程
 */
TEST_F(EndToEndIntegrationTest, CompleteClockDiscipliningProcess) {
    // 设置Mock HAL行为
    SetupMockHalBehavior();
    
    // 启动授时引擎
    ASSERT_TRUE(timing_engine_->Start()) << "授时引擎启动失败";
    
    // 等待系统初始化
    std::this_thread::sleep_for(100ms);
    
    // 验证初始状态
    auto initial_status = timing_engine_->GetSystemStatus();
    EXPECT_EQ(initial_status.current_state, core::ClockState::FREE_RUN) 
        << "初始状态应为FREE_RUN";
    
    // 模拟GNSS信号检测
    std::this_thread::sleep_for(500ms);
    
    // 验证状态转换到DISCIPLINING
    auto disciplining_status = timing_engine_->GetSystemStatus();
    EXPECT_EQ(disciplining_status.current_state, core::ClockState::DISCIPLINING)
        << "检测到GNSS信号后应转换到DISCIPLINING状态";
    
    // 等待驯服收敛
    auto start_time = std::chrono::steady_clock::now();
    bool converged = false;
    
    while (std::chrono::steady_clock::now() - start_time < test_timeout_) {
        auto status = timing_engine_->GetSystemStatus();
        if (status.current_state == core::ClockState::LOCKED) {
            converged = true;
            break;
        }
        std::this_thread::sleep_for(100ms);
    }
    
    ASSERT_TRUE(converged) << "驯服过程未在超时时间内收敛";
    
    // 验证锁定状态的性能指标
    auto locked_status = timing_engine_->GetSystemStatus();
    EXPECT_EQ(locked_status.current_state, core::ClockState::LOCKED);
    EXPECT_LT(locked_status.timing_metrics.phase_offset_ns, 100.0)
        << "相位偏移应小于100ns";
    EXPECT_LT(std::abs(locked_status.timing_metrics.frequency_offset_ppm), 0.01)
        << "频率偏移应小于0.01ppm";
}

/**
 * @test 测试多时间源切换和故障恢复机制
 * 验证GNSS信号丢失时的故障切换和恢复过程
 */
TEST_F(EndToEndIntegrationTest, TimeSourceFailoverAndRecovery) {
    SetupMockHalBehavior();
    
    // 启动系统并等待锁定
    ASSERT_TRUE(timing_engine_->Start());
    
    // 等待系统达到锁定状态
    auto start_time = std::chrono::steady_clock::now();
    while (std::chrono::steady_clock::now() - start_time < test_timeout_) {
        auto status = timing_engine_->GetSystemStatus();
        if (status.current_state == core::ClockState::LOCKED) {
            break;
        }
        std::this_thread::sleep_for(100ms);
    }
    
    // 验证系统已锁定
    auto locked_status = timing_engine_->GetSystemStatus();
    ASSERT_EQ(locked_status.current_state, core::ClockState::LOCKED);
    EXPECT_EQ(locked_status.active_source, core::TimeSource::GNSS);
    
    // 模拟GNSS信号丢失
    auto mock_gnss = std::dynamic_pointer_cast<hal::MockGnssReceiver>(
        hal_factory_->CreateGnssReceiver()
    );
    EXPECT_CALL(*mock_gnss, IsSignalValid())
        .WillRepeatedly(Return(false));
    
    // 等待故障检测和切换
    start_time = std::chrono::steady_clock::now();
    bool switched_to_holdover = false;
    
    while (std::chrono::steady_clock::now() - start_time < test_timeout_) {
        auto status = timing_engine_->GetSystemStatus();
        if (status.current_state == core::ClockState::HOLDOVER) {
            switched_to_holdover = true;
            break;
        }
        std::this_thread::sleep_for(100ms);
    }
    
    ASSERT_TRUE(switched_to_holdover) << "系统未能切换到守时模式";
    
    // 验证守时状态
    auto holdover_status = timing_engine_->GetSystemStatus();
    EXPECT_EQ(holdover_status.current_state, core::ClockState::HOLDOVER);
    EXPECT_EQ(holdover_status.active_source, core::TimeSource::RUBIDIUM);
    
    // 模拟GNSS信号恢复
    EXPECT_CALL(*mock_gnss, IsSignalValid())
        .WillRepeatedly(Return(true));
    
    // 等待信号恢复和重新锁定
    start_time = std::chrono::steady_clock::now();
    bool recovered_to_locked = false;
    
    while (std::chrono::steady_clock::now() - start_time < test_timeout_) {
        auto status = timing_engine_->GetSystemStatus();
        if (status.current_state == core::ClockState::LOCKED && 
            status.active_source == core::TimeSource::GNSS) {
            recovered_to_locked = true;
            break;
        }
        std::this_thread::sleep_for(100ms);
    }
    
    ASSERT_TRUE(recovered_to_locked) << "系统未能从守时模式恢复到锁定状态";
}

/**
 * @test 测试API服务的完整功能
 * 验证REST API和WebSocket的基本功能
 */
TEST_F(EndToEndIntegrationTest, APIServiceFunctionality) {
    SetupMockHalBehavior();
    
    // 启动授时引擎
    ASSERT_TRUE(timing_engine_->Start());
    
    // 测试系统状态查询API
    auto status_response = timing_service_->getSystemStatus();
    EXPECT_EQ(status_response.health, core::SystemHealth::HEALTHY) << "系统状态查询失败";
    EXPECT_GT(status_response.uptime_seconds, 0) << "系统运行时间应大于0";

    // 测试健康状态查询API
    auto health_response = timing_service_->getHealthStatus();
    EXPECT_EQ(health_response.status, "HEALTHY") << "健康状态查询失败";
    
    // 测试认证功能
    api::LoginRequest login_req;
    login_req.username = "admin";
    login_req.password = "admin123";
    
    auto auth_response = auth_manager_->Authenticate(login_req);
    EXPECT_TRUE(auth_response.success) << "认证失败";
    EXPECT_FALSE(auth_response.token.empty()) << "认证令牌为空";
    
    // 测试令牌验证
    auto token_validation = auth_manager_->ValidateToken(auth_response.token);
    EXPECT_TRUE(token_validation.valid) << "令牌验证失败";
    EXPECT_EQ(token_validation.username, "admin") << "令牌用户名不匹配";
}

/**
 * @test 测试系统性能指标
 * 验证系统资源使用和响应时间符合要求
 */
TEST_F(EndToEndIntegrationTest, SystemPerformanceMetrics) {
    SetupMockHalBehavior();
    
    // 启动系统
    ASSERT_TRUE(timing_engine_->Start());
    
    // 等待系统稳定
    std::this_thread::sleep_for(1s);
    
    // 测试API响应时间
    const int num_requests = 100;
    std::vector<std::chrono::microseconds> response_times;
    
    for (int i = 0; i < num_requests; ++i) {
        auto start = std::chrono::high_resolution_clock::now();
        auto response = timing_service_->GetSystemStatus();
        auto end = std::chrono::high_resolution_clock::now();
        
        EXPECT_TRUE(response.success) << "API请求失败";
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        response_times.push_back(duration);
    }
    
    // 计算平均响应时间
    auto total_time = std::accumulate(response_times.begin(), response_times.end(), 
                                     std::chrono::microseconds(0));
    auto avg_response_time = total_time / num_requests;
    
    // 验证响应时间要求（<10ms）
    EXPECT_LT(avg_response_time.count(), 10000) 
        << "平均API响应时间超过10ms要求: " << avg_response_time.count() << "μs";
    
    // 测试并发性能
    const int num_threads = 10;
    const int requests_per_thread = 50;
    std::vector<std::future<bool>> futures;
    std::atomic<int> successful_requests(0);
    
    auto concurrent_test = [&]() {
        for (int i = 0; i < requests_per_thread; ++i) {
            auto response = timing_service_->GetSystemStatus();
            if (response.success) {
                successful_requests++;
            }
        }
        return true;
    };
    
    // 启动并发测试
    auto concurrent_start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_threads; ++i) {
        futures.push_back(std::async(std::launch::async, concurrent_test));
    }
    
    // 等待所有线程完成
    for (auto& future : futures) {
        EXPECT_TRUE(future.get()) << "并发测试线程失败";
    }
    
    auto concurrent_end = std::chrono::high_resolution_clock::now();
    auto concurrent_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        concurrent_end - concurrent_start);
    
    // 验证并发性能
    int total_requests = num_threads * requests_per_thread;
    EXPECT_EQ(successful_requests.load(), total_requests) 
        << "并发请求成功率不足100%";
    
    double requests_per_second = (double)total_requests / concurrent_duration.count() * 1000;
    EXPECT_GT(requests_per_second, 100) 
        << "并发处理能力不足: " << requests_per_second << " req/s";
}

/**
 * @test 测试错误处理和恢复机制
 * 验证系统在各种异常情况下的处理能力
 */
TEST_F(EndToEndIntegrationTest, ErrorHandlingAndRecovery) {
    SetupMockHalBehavior();
    
    // 启动系统
    ASSERT_TRUE(timing_engine_->Start());
    
    // 模拟硬件故障
    auto mock_gnss = std::dynamic_pointer_cast<hal::MockGnssReceiver>(
        hal_factory_->CreateGnssReceiver()
    );
    
    // 模拟GNSS初始化失败
    EXPECT_CALL(*mock_gnss, Initialize())
        .WillOnce(Return(false));
    
    // 重新初始化系统以触发错误
    timing_engine_->Stop();
    
    // 系统应该能够处理硬件初始化失败
    bool restart_success = timing_engine_->Start();
    
    // 验证错误处理
    auto error_logs = logger_->GetRecentErrors();
    EXPECT_FALSE(error_logs.empty()) << "应该记录硬件初始化错误";
    
    // 验证系统降级运行
    auto status = timing_engine_->GetSystemStatus();
    EXPECT_NE(status.current_state, core::ClockState::FAULT) 
        << "系统不应进入故障状态";
    
    // 测试配置错误处理
    core::TimingConfig invalid_config;
    invalid_config.gnss_config.baud_rate = -1; // 无效波特率
    
    bool config_result = config_manager_->SetConfiguration(invalid_config);
    EXPECT_FALSE(config_result) << "应该拒绝无效配置";
    
    // 验证配置验证错误记录
    auto config_errors = logger_->GetRecentErrors();
    bool found_config_error = false;
    for (const auto& error : config_errors) {
        if (error.find("配置") != std::string::npos || 
            error.find("波特率") != std::string::npos) {
            found_config_error = true;
            break;
        }
    }
    EXPECT_TRUE(found_config_error) << "应该记录配置验证错误";
}

/**
 * @test 测试长期运行稳定性（简化版）
 * 验证系统在较长时间运行中的稳定性
 */
TEST_F(EndToEndIntegrationTest, LongTermStabilityTest) {
    SetupMockHalBehavior();
    
    // 启动系统
    ASSERT_TRUE(timing_engine_->Start());
    
    // 运行较长时间的稳定性测试（简化为30秒）
    const auto test_duration = 30s;
    const auto check_interval = 1s;
    
    auto start_time = std::chrono::steady_clock::now();
    int stability_checks = 0;
    int stable_checks = 0;
    
    while (std::chrono::steady_clock::now() - start_time < test_duration) {
        auto status = timing_engine_->GetSystemStatus();
        
        stability_checks++;
        
        // 检查系统状态稳定性
        if (status.current_state == core::ClockState::LOCKED || 
            status.current_state == core::ClockState::DISCIPLINING) {
            stable_checks++;
        }
        
        // 检查性能指标
        if (status.current_state == core::ClockState::LOCKED) {
            EXPECT_LT(status.timing_metrics.phase_offset_ns, 200.0)
                << "长期运行中相位偏移过大";
            EXPECT_LT(std::abs(status.timing_metrics.frequency_offset_ppm), 0.02)
                << "长期运行中频率偏移过大";
        }
        
        std::this_thread::sleep_for(check_interval);
    }
    
    // 验证稳定性指标
    double stability_ratio = (double)stable_checks / stability_checks;
    EXPECT_GT(stability_ratio, 0.95) 
        << "系统稳定性不足: " << stability_ratio * 100 << "%";
    
    // 检查内存使用情况（简化检查）
    auto final_status = timing_engine_->GetSystemStatus();
    EXPECT_LT(final_status.system_metrics.memory_usage_mb, 100.0)
        << "内存使用超过100MB限制";
    
    EXPECT_LT(final_status.system_metrics.cpu_usage_percent, 10.0)
        << "CPU使用率超过10%（测试环境放宽限制）";
}

} // namespace test
} // namespace timing_server