#pragma once

#include <gmock/gmock.h>
#include <hal/interfaces.h>
#include <hal/hal_factory.h>
#include <core/types.h>
#include <ctime>

namespace timing_server {
namespace hal {

/**
 * @brief Mock GNSS接收机实现
 * 用于测试环境中模拟GNSS接收机功能
 */
class MockGnssReceiver : public I_GnssReceiver {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(std::string, ReadNmeaSentence, (), (override));
    MOCK_METHOD(bool, IsSignalValid, (), (override));
    MOCK_METHOD(SatelliteInfo, GetSatelliteInfo, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock PPS输入实现
 * 用于测试环境中模拟PPS信号输入功能
 */
class MockPpsInput : public I_PpsInput {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(bool, WaitForPpsEdge, (int timeout_ms), (override));
    MOCK_METHOD(uint64_t, GetLastPpsTimestamp, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock 原子钟实现
 * 用于测试环境中模拟原子钟功能
 */
class MockAtomicClock : public I_AtomicClock {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(ClockHealth, GetStatus, (), (override));
    MOCK_METHOD(bool, SetFrequencyCorrection, (double ppm), (override));
    MOCK_METHOD(double, GetFrequencyOffset, (), (override));
    MOCK_METHOD(ClockHealth, GetHealth, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock 频率输入实现
 * 用于测试环境中模拟频率输入功能
 */
class MockFrequencyInput : public I_FrequencyInput {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(double, MeasureFrequency, (), (override));
    MOCK_METHOD(bool, IsSignalPresent, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock RTC实现
 * 用于测试环境中模拟实时时钟功能
 */
class MockHighPrecisionRtc : public I_HighPrecisionRtc {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(timespec, GetTime, (), (override));
    MOCK_METHOD(bool, SetTime, (const timespec& ts), (override));
    MOCK_METHOD(bool, IsValid, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock 网络接口实现
 * 用于测试环境中模拟网络接口功能
 */
class MockNetworkInterface : public I_NetworkInterface {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(timespec, GetPHCTime, (), (override));
    MOCK_METHOD(bool, SetPHCTime, (const timespec& ts), (override));
    MOCK_METHOD(bool, ConfigurePTP, (const PTPConfig& config), (override));
    MOCK_METHOD(PHCStatus, GetPHCStatus, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock HAL工厂实现
 * 用于测试环境中创建Mock HAL设备实例
 */
class MockHalFactory : public I_HalFactory {
public:
    MOCK_METHOD(std::unique_ptr<I_GnssReceiver>, CreateGnssReceiver, (), (override));
    MOCK_METHOD(std::unique_ptr<I_PpsInput>, CreatePpsInput, (), (override));
    MOCK_METHOD(std::unique_ptr<I_AtomicClock>, CreateAtomicClock, (), (override));
    MOCK_METHOD(std::unique_ptr<I_FrequencyInput>, CreateFrequencyInput, (), (override));
    MOCK_METHOD(std::unique_ptr<I_HighPrecisionRtc>, CreateRtc, (), (override));
    MOCK_METHOD(std::unique_ptr<I_NetworkInterface>, CreateNetworkInterface, (), (override));
};

} // namespace hal
} // namespace timing_server
