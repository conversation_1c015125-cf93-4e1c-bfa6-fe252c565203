#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <api/websocket_server.h>
#include <api/websocket_event_listener.h>
#include <api/timing_service.h>
#include <thread>
#include <chrono>
#include <vector>
#include <atomic>
#include <future>

using namespace timing_server::api;
using namespace timing_server::core;

/**
 * @brief Mock授时服务实现，用于测试
 */
class MockTimingService : public TimingService {
public:
    MOCK_METHOD(SystemStatus, getSystemStatus, (), (override));
    MOCK_METHOD(std::string, getPtpConfig, (), (override));
    MOCK_METHOD(bool, updatePtpConfig, (const std::string&), (override));
    MOCK_METHOD(std::string, getNtpConfig, (), (override));
    MOCK_METHOD(bool, updateNtpConfig, (const std::string&), (override));
    MOCK_METHOD(LogQueryResult, queryLogs, (const LogQueryParams&), (override));
    MOCK_METHOD(HealthStatus, getHealthStatus, (), (override));
    MOCK_METHOD(bool, restartSystem, (), (override));
    MOCK_METHOD(PerformanceMetrics, getMetrics, (const std::string&, const std::string&), (override));
    MOCK_METHOD(std::string, validateConfig, (const std::string&, const std::string&), (override));
    MOCK_METHOD(std::string, getConfigSchema, (const std::string&), (override));
};

/**
 * @brief WebSocket集成测试类
 */
class WebSocketIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建Mock授时服务
        mock_timing_service = std::make_shared<MockTimingService>();
        
        // 配置WebSocket服务器
        config.port = 18081; // 使用测试端口
        config.max_connections = 50;
        config.heartbeat_interval_ms = 5000;
        config.heartbeat_timeout_ms = 10000;
        config.message_queue_size = 500;
        config.require_authentication = true;
        
        // 创建WebSocket服务器
        websocket_server = std::make_unique<WebSocketServer>(config, mock_timing_service);
        
        // 创建事件监听器
        event_listener = std::make_shared<WebSocketEventListener>();
        websocket_server->setEventListener(event_listener);
        
        // 设置Mock期望
        setupMockExpectations();
    }
    
    void TearDown() override {
        if (websocket_server && websocket_server->isRunning()) {
            websocket_server->stop();
        }
    }
    
    void setupMockExpectations() {
        // 设置系统状态Mock
        SystemStatus mock_status;
        mock_status.current_state = ClockState::LOCKED;
        mock_status.active_source = TimeSource::GNSS;
        mock_status.uptime_seconds = 3600;
        mock_status.health = SystemHealth::HEALTHY;
        
        EXPECT_CALL(*mock_timing_service, getSystemStatus())
            .WillRepeatedly(::testing::Return(mock_status));
    }
    
    WebSocketServerConfig config;
    std::unique_ptr<WebSocketServer> websocket_server;
    std::shared_ptr<MockTimingService> mock_timing_service;
    std::shared_ptr<WebSocketEventListener> event_listener;
};

/**
 * @brief 测试WebSocket服务器基本启动和停止
 */
TEST_F(WebSocketIntegrationTest, BasicStartStop) {
    // 测试启动
    EXPECT_TRUE(websocket_server->start());
    EXPECT_TRUE(websocket_server->isRunning());
    
    // 等待服务器完全启动
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 测试停止
    websocket_server->stop();
    EXPECT_FALSE(websocket_server->isRunning());
}

/**
 * @brief 测试重复启动
 */
TEST_F(WebSocketIntegrationTest, RepeatedStart) {
    EXPECT_TRUE(websocket_server->start());
    EXPECT_TRUE(websocket_server->isRunning());
    
    // 重复启动应该返回true但不会重新启动
    EXPECT_TRUE(websocket_server->start());
    EXPECT_TRUE(websocket_server->isRunning());
    
    websocket_server->stop();
}

/**
 * @brief 测试消息广播功能
 */
TEST_F(WebSocketIntegrationTest, MessageBroadcast) {
    EXPECT_TRUE(websocket_server->start());
    
    // 模拟添加一些连接（在实际实现中需要真实的WebSocket连接）
    // 这里测试广播接口的基本功能
    
    WebSocketMessage test_message(WebSocketMessageType::STATUS_UPDATE, 
                                 R"({"state": "LOCKED", "accuracy": 50.0})");
    
    // 广播消息（由于没有真实连接，返回0是正常的）
    uint32_t sent_count = websocket_server->broadcastMessage(test_message, "status_update");
    EXPECT_EQ(sent_count, 0); // 没有连接时应该返回0
    
    websocket_server->stop();
}

/**
 * @brief 测试状态推送功能
 */
TEST_F(WebSocketIntegrationTest, StatusPushing) {
    EXPECT_TRUE(websocket_server->start());
    
    // 启动状态推送
    websocket_server->startStatusPushing();
    
    // 等待几次状态推送
    std::this_thread::sleep_for(std::chrono::milliseconds(2500));
    
    // 停止状态推送
    websocket_server->stopStatusPushing();
    
    websocket_server->stop();
}

/**
 * @brief 测试服务器统计信息
 */
TEST_F(WebSocketIntegrationTest, ServerStats) {
    EXPECT_TRUE(websocket_server->start());
    
    auto stats = websocket_server->getStats();
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_EQ(stats.total_connections, 0);
    EXPECT_EQ(stats.messages_sent, 0);
    EXPECT_EQ(stats.messages_received, 0);
    
    websocket_server->stop();
}

/**
 * @brief 测试事件监听器统计
 */
TEST_F(WebSocketIntegrationTest, EventListenerStats) {
    auto stats = event_listener->getConnectionStats();
    EXPECT_EQ(stats.total_connections, 0);
    EXPECT_EQ(stats.successful_authentications, 0);
    EXPECT_EQ(stats.failed_authentications, 0);
    EXPECT_EQ(stats.messages_processed, 0);
}

/**
 * @brief 测试并发连接处理能力
 */
TEST_F(WebSocketIntegrationTest, ConcurrentConnectionHandling) {
    EXPECT_TRUE(websocket_server->start());
    
    const int num_threads = 10;
    const int operations_per_thread = 100;
    std::vector<std::future<void>> futures;
    std::atomic<int> total_operations{0};
    
    // 启动多个线程模拟并发操作
    for (int i = 0; i < num_threads; ++i) {
        futures.push_back(std::async(std::launch::async, [&, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                // 模拟消息广播
                WebSocketMessage message(WebSocketMessageType::HEARTBEAT, 
                                       R"({"thread": )" + std::to_string(i) + 
                                       R"(, "operation": )" + std::to_string(j) + "}");
                
                websocket_server->broadcastMessage(message, "heartbeat");
                total_operations.fetch_add(1);
                
                // 短暂延迟模拟真实负载
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        }));
    }
    
    // 等待所有线程完成
    for (auto& future : futures) {
        future.wait();
    }
    
    EXPECT_EQ(total_operations.load(), num_threads * operations_per_thread);
    
    websocket_server->stop();
}

/**
 * @brief 测试消息序列化和反序列化
 */
TEST_F(WebSocketIntegrationTest, MessageSerialization) {
    // 创建测试消息
    WebSocketMessage original_message(WebSocketMessageType::STATUS_UPDATE, 
                                    R"({"state": "LOCKED", "accuracy": 50.0, "timestamp": 1234567890})");
    
    // 序列化
    std::string serialized = websocket_server->serializeMessage(original_message);
    EXPECT_FALSE(serialized.empty());
    
    // 验证序列化结果包含预期内容
    EXPECT_NE(serialized.find("status_update"), std::string::npos);
    EXPECT_NE(serialized.find("LOCKED"), std::string::npos);
    EXPECT_NE(serialized.find("50.0"), std::string::npos);
    
    // 反序列化
    WebSocketMessage deserialized = websocket_server->deserializeMessage(serialized);
    EXPECT_EQ(deserialized.type, WebSocketMessageType::STATUS_UPDATE);
    EXPECT_FALSE(deserialized.payload.empty());
}

/**
 * @brief 测试心跳机制
 */
TEST_F(WebSocketIntegrationTest, HeartbeatMechanism) {
    EXPECT_TRUE(websocket_server->start());
    
    // 创建心跳消息
    auto heartbeat_message = websocket_server->createHeartbeatMessage();
    EXPECT_EQ(heartbeat_message.type, WebSocketMessageType::HEARTBEAT);
    EXPECT_FALSE(heartbeat_message.payload.empty());
    
    // 验证心跳消息格式
    std::string serialized = websocket_server->serializeMessage(heartbeat_message);
    EXPECT_NE(serialized.find("heartbeat"), std::string::npos);
    EXPECT_NE(serialized.find("timestamp"), std::string::npos);
    
    websocket_server->stop();
}

/**
 * @brief 测试告警消息创建
 */
TEST_F(WebSocketIntegrationTest, AlarmMessageCreation) {
    auto alarm_message = websocket_server->createAlarmMessage("WARNING", "GNSS", "信号质量下降");
    
    EXPECT_EQ(alarm_message.type, WebSocketMessageType::ALARM);
    
    std::string serialized = websocket_server->serializeMessage(alarm_message);
    EXPECT_NE(serialized.find("alarm"), std::string::npos);
    EXPECT_NE(serialized.find("WARNING"), std::string::npos);
    EXPECT_NE(serialized.find("GNSS"), std::string::npos);
    EXPECT_NE(serialized.find("信号质量下降"), std::string::npos);
}

/**
 * @brief 测试连接管理
 */
TEST_F(WebSocketIntegrationTest, ConnectionManagement) {
    EXPECT_TRUE(websocket_server->start());
    
    // 获取活跃连接列表（应该为空）
    auto connections = websocket_server->getActiveConnections();
    EXPECT_TRUE(connections.empty());
    
    // 测试断开不存在的连接
    EXPECT_FALSE(websocket_server->disconnectClient(999, "test"));
    
    websocket_server->stop();
}

/**
 * @brief 性能基准测试
 */
TEST_F(WebSocketIntegrationTest, PerformanceBenchmark) {
    EXPECT_TRUE(websocket_server->start());
    
    const int num_messages = 1000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 发送大量消息测试性能
    for (int i = 0; i < num_messages; ++i) {
        WebSocketMessage message(WebSocketMessageType::STATUS_UPDATE, 
                               R"({"message_id": )" + std::to_string(i) + "}");
        websocket_server->broadcastMessage(message, "performance_test");
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // 验证性能要求：1000条消息应该在合理时间内处理完成
    EXPECT_LT(duration.count(), 1000); // 应该在1秒内完成
    
    double messages_per_second = (num_messages * 1000.0) / duration.count();
    std::cout << "消息处理性能: " << messages_per_second << " 消息/秒" << std::endl;
    
    websocket_server->stop();
}

/**
 * @brief 测试内存使用情况
 */
TEST_F(WebSocketIntegrationTest, MemoryUsage) {
    EXPECT_TRUE(websocket_server->start());
    
    // 创建大量消息测试内存使用
    const int num_messages = 10000;
    std::vector<WebSocketMessage> messages;
    messages.reserve(num_messages);
    
    for (int i = 0; i < num_messages; ++i) {
        messages.emplace_back(WebSocketMessageType::STATUS_UPDATE, 
                            R"({"large_payload": ")" + std::string(100, 'A' + (i % 26)) + R"("})");
    }
    
    // 广播所有消息
    for (const auto& message : messages) {
        websocket_server->broadcastMessage(message, "memory_test");
    }
    
    // 验证服务器仍然正常运行
    EXPECT_TRUE(websocket_server->isRunning());
    
    websocket_server->stop();
}

/**
 * @brief 测试错误处理
 */
TEST_F(WebSocketIntegrationTest, ErrorHandling) {
    EXPECT_TRUE(websocket_server->start());
    
    // 测试无效JSON消息的处理
    try {
        websocket_server->deserializeMessage("invalid json {");
        FAIL() << "应该抛出异常";
    } catch (const std::exception& e) {
        // 预期的异常
        EXPECT_TRUE(true);
    }
    
    // 测试空消息处理
    WebSocketMessage empty_message(WebSocketMessageType::ERROR, "");
    std::string serialized = websocket_server->serializeMessage(empty_message);
    EXPECT_FALSE(serialized.empty());
    
    websocket_server->stop();
}