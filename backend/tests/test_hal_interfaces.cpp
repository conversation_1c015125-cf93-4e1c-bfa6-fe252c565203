#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include "hal/platform_detector.h"
#include "core/types.h"

using namespace timing_server::hal;
using namespace timing_server::core;
using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;

namespace timing_server {
namespace hal {
namespace test {

/**
 * @brief Mock GNSS接收机类
 * 用于单元测试的GNSS接收机模拟实现
 */
class MockGnssReceiver : public I_GnssReceiver {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(std::string, ReadNmeaSentence, (), (override));
    MOCK_METHOD(bool, IsSignalValid, (), (override));
    MOCK_METHOD(SatelliteInfo, GetSatelliteInfo, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock PPS输入类
 * 用于单元测试的PPS输入模拟实现
 */
class MockPpsInput : public I_PpsInput {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(bool, WaitForPpsEdge, (int timeout_ms), (override));
    MOCK_METHOD(uint64_t, GetLastPpsTimestamp, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock 原子钟类
 * 用于单元测试的原子钟模拟实现
 */
class MockAtomicClock : public I_AtomicClock {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(ClockHealth, GetStatus, (), (override));
    MOCK_METHOD(bool, SetFrequencyCorrection, (double ppm), (override));
    MOCK_METHOD(double, GetFrequencyOffset, (), (override));
    MOCK_METHOD(ClockHealth, GetHealth, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock 频率输入类
 * 用于单元测试的频率输入模拟实现
 */
class MockFrequencyInput : public I_FrequencyInput {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(double, MeasureFrequency, (), (override));
    MOCK_METHOD(bool, IsSignalPresent, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock 高精度RTC类
 * 用于单元测试的RTC模拟实现
 */
class MockHighPrecisionRtc : public I_HighPrecisionRtc {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(timespec, GetTime, (), (override));
    MOCK_METHOD(bool, SetTime, (const timespec& ts), (override));
    MOCK_METHOD(bool, IsValid, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock 网络接口类
 * 用于单元测试的网络接口模拟实现
 */
class MockNetworkInterface : public I_NetworkInterface {
public:
    MOCK_METHOD(bool, Initialize, (), (override));
    MOCK_METHOD(timespec, GetPHCTime, (), (override));
    MOCK_METHOD(bool, SetPHCTime, (const timespec& ts), (override));
    MOCK_METHOD(bool, ConfigurePTP, (const PTPConfig& config), (override));
    MOCK_METHOD(PHCStatus, GetPHCStatus, (), (override));
    MOCK_METHOD(void, Close, (), (override));
};

/**
 * @brief Mock HAL工厂类
 * 用于单元测试的HAL工厂模拟实现
 */
class MockHalFactory : public I_HalFactory {
public:
    MOCK_METHOD(std::unique_ptr<I_GnssReceiver>, CreateGnssReceiver, (), (override));
    MOCK_METHOD(std::unique_ptr<I_PpsInput>, CreatePpsInput, (), (override));
    MOCK_METHOD(std::unique_ptr<I_AtomicClock>, CreateAtomicClock, (), (override));
    MOCK_METHOD(std::unique_ptr<I_FrequencyInput>, CreateFrequencyInput, (), (override));
    MOCK_METHOD(std::unique_ptr<I_HighPrecisionRtc>, CreateRtc, (), (override));
    MOCK_METHOD(std::unique_ptr<I_NetworkInterface>, CreateNetworkInterface, (), (override));
};

} // namespace test
} // namespace hal
} // namespace timing_server

/**
 * @brief 平台检测器测试套件
 * 测试平台检测功能的正确性
 */
class PlatformDetectorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 测试前的设置
    }
    
    void TearDown() override {
        // 测试后的清理
    }
};

/**
 * @brief 测试平台检测功能
 */
TEST_F(PlatformDetectorTest, DetectCurrentPlatform) {
    // 检测当前平台
    auto platform_info = PlatformDetector::DetectPlatform();
    
    // 验证平台信息的有效性
    EXPECT_FALSE(platform_info.os_name.empty()) << "操作系统名称不应为空";
    EXPECT_FALSE(platform_info.architecture.empty()) << "处理器架构不应为空";
    EXPECT_FALSE(platform_info.description.empty()) << "平台描述不应为空";
    EXPECT_NE(platform_info.type, PlatformType::UNKNOWN) << "应该能够识别当前平台";
    
    // 输出检测结果用于调试
    std::cout << "检测到的平台信息:" << std::endl;
    std::cout << "  类型: " << PlatformDetector::PlatformTypeToString(platform_info.type) << std::endl;
    std::cout << "  操作系统: " << platform_info.os_name << std::endl;
    std::cout << "  架构: " << platform_info.architecture << std::endl;
    std::cout << "  内核版本: " << platform_info.kernel_version << std::endl;
    std::cout << "  开发环境: " << (platform_info.is_development_env ? "是" : "否") << std::endl;
    std::cout << "  描述: " << platform_info.description << std::endl;
}

/**
 * @brief 测试平台类型字符串转换
 */
TEST_F(PlatformDetectorTest, PlatformTypeToString) {
    EXPECT_EQ(PlatformDetector::PlatformTypeToString(PlatformType::LINUX_X86_64), "linux-x86_64");
    EXPECT_EQ(PlatformDetector::PlatformTypeToString(PlatformType::LINUX_LOONGARCH64), "linux-loongarch64");
    EXPECT_EQ(PlatformDetector::PlatformTypeToString(PlatformType::MACOS_X86_64), "macos-x86_64");
    EXPECT_EQ(PlatformDetector::PlatformTypeToString(PlatformType::MACOS_ARM64), "macos-arm64");
    EXPECT_EQ(PlatformDetector::PlatformTypeToString(PlatformType::UNKNOWN), "unknown");
}

/**
 * @brief 测试平台类型判断函数
 */
TEST_F(PlatformDetectorTest, PlatformTypeCheckers) {
    // 测试Linux平台判断
    EXPECT_TRUE(PlatformDetector::IsLinuxPlatform(PlatformType::LINUX_X86_64));
    EXPECT_TRUE(PlatformDetector::IsLinuxPlatform(PlatformType::LINUX_LOONGARCH64));
    EXPECT_FALSE(PlatformDetector::IsLinuxPlatform(PlatformType::MACOS_X86_64));
    EXPECT_FALSE(PlatformDetector::IsLinuxPlatform(PlatformType::MACOS_ARM64));
    
    // 测试macOS平台判断
    EXPECT_FALSE(PlatformDetector::IsMacOSPlatform(PlatformType::LINUX_X86_64));
    EXPECT_FALSE(PlatformDetector::IsMacOSPlatform(PlatformType::LINUX_LOONGARCH64));
    EXPECT_TRUE(PlatformDetector::IsMacOSPlatform(PlatformType::MACOS_X86_64));
    EXPECT_TRUE(PlatformDetector::IsMacOSPlatform(PlatformType::MACOS_ARM64));
    
    // 测试Mock HAL需求判断
    EXPECT_FALSE(PlatformDetector::RequiresMockHal(PlatformType::LINUX_X86_64));
    EXPECT_FALSE(PlatformDetector::RequiresMockHal(PlatformType::LINUX_LOONGARCH64));
    EXPECT_TRUE(PlatformDetector::RequiresMockHal(PlatformType::MACOS_X86_64));
    EXPECT_TRUE(PlatformDetector::RequiresMockHal(PlatformType::MACOS_ARM64));
}

/**
 * @brief HAL异常测试套件
 * 测试HAL异常处理机制
 */
class HalExceptionTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

/**
 * @brief 测试HAL异常创建和信息获取
 */
TEST_F(HalExceptionTest, ExceptionCreationAndInfo) {
    const std::string message = "测试错误消息";
    const std::string details = "详细错误信息";
    
    HalException exception(HalErrorType::DEVICE_NOT_FOUND, message, details);
    
    EXPECT_EQ(exception.GetErrorType(), HalErrorType::DEVICE_NOT_FOUND);
    EXPECT_EQ(exception.GetDetails(), details);
    EXPECT_EQ(std::string(exception.what()), message);
}

/**
 * @brief 测试错误类型描述
 */
TEST_F(HalExceptionTest, ErrorTypeDescriptions) {
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::PLATFORM_NOT_SUPPORTED), "平台不支持");
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::FACTORY_CREATION_FAILED), "工厂创建失败");
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::DEVICE_INITIALIZATION_FAILED), "设备初始化失败");
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::DEVICE_NOT_FOUND), "设备未找到");
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::PERMISSION_DENIED), "权限不足");
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::RESOURCE_BUSY), "资源忙碌");
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::CONFIGURATION_ERROR), "配置错误");
    EXPECT_EQ(HalException::GetErrorTypeDescription(HalErrorType::UNKNOWN_ERROR), "未知错误");
}

/**
 * @brief HAL工厂管理器测试套件
 * 测试HAL工厂管理器的功能
 */
class HalFactoryManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 每个测试前清理管理器状态
        HalFactoryManager::GetInstance().Cleanup();
    }
    
    void TearDown() override {
        // 每个测试后清理管理器状态
        HalFactoryManager::GetInstance().Cleanup();
    }
};

/**
 * @brief 测试HAL工厂管理器单例模式
 */
TEST_F(HalFactoryManagerTest, SingletonPattern) {
    auto& manager1 = HalFactoryManager::GetInstance();
    auto& manager2 = HalFactoryManager::GetInstance();
    
    // 验证单例模式：两个引用应该指向同一个对象
    EXPECT_EQ(&manager1, &manager2) << "HAL工厂管理器应该是单例";
}

/**
 * @brief 测试HAL工厂管理器初始化
 */
TEST_F(HalFactoryManagerTest, Initialization) {
    auto& manager = HalFactoryManager::GetInstance();
    
    // 初始状态应该是未初始化
    EXPECT_FALSE(manager.IsInitialized()) << "初始状态应该是未初始化";
    
    // 注意：由于Mock HAL尚未实现，这个测试可能会失败
    // 这是预期的，将在后续任务中修复
    // bool init_result = manager.Initialize();
    // 暂时跳过初始化测试，直到Mock HAL实现完成
    
    std::cout << "注意：HAL工厂初始化测试跳过，等待Mock HAL实现完成" << std::endl;
}

/**
 * @brief Mock设备接口测试套件
 * 测试Mock设备接口的基本功能
 */
class MockDeviceInterfaceTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

/**
 * @brief 测试Mock GNSS接收机接口
 */
TEST_F(MockDeviceInterfaceTest, MockGnssReceiverInterface) {
    using namespace timing_server::hal::test;
    
    StrictMock<MockGnssReceiver> mock_gnss;
    
    // 设置期望的调用和返回值
    EXPECT_CALL(mock_gnss, Initialize())
        .WillOnce(Return(true));
    
    EXPECT_CALL(mock_gnss, IsSignalValid())
        .WillOnce(Return(true));
    
    SatelliteInfo expected_info;
    expected_info.satellite_count = 12;
    expected_info.signal_strength_db = -142.0;
    expected_info.is_locked = true;
    expected_info.fix_type = "3D";
    
    EXPECT_CALL(mock_gnss, GetSatelliteInfo())
        .WillOnce(Return(expected_info));
    
    EXPECT_CALL(mock_gnss, ReadNmeaSentence())
        .WillOnce(Return("$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47"));
    
    EXPECT_CALL(mock_gnss, Close())
        .Times(1);
    
    // 执行测试
    EXPECT_TRUE(mock_gnss.Initialize());
    EXPECT_TRUE(mock_gnss.IsSignalValid());
    
    auto info = mock_gnss.GetSatelliteInfo();
    EXPECT_EQ(info.satellite_count, 12);
    EXPECT_DOUBLE_EQ(info.signal_strength_db, -142.0);
    EXPECT_TRUE(info.is_locked);
    EXPECT_EQ(info.fix_type, "3D");
    
    auto nmea = mock_gnss.ReadNmeaSentence();
    EXPECT_FALSE(nmea.empty());
    EXPECT_TRUE(nmea.find("$GPGGA") != std::string::npos);
    
    mock_gnss.Close();
}

/**
 * @brief 测试Mock PPS输入接口
 */
TEST_F(MockDeviceInterfaceTest, MockPpsInputInterface) {
    using namespace timing_server::hal::test;
    
    StrictMock<MockPpsInput> mock_pps;
    
    // 设置期望的调用和返回值
    EXPECT_CALL(mock_pps, Initialize())
        .WillOnce(Return(true));
    
    EXPECT_CALL(mock_pps, WaitForPpsEdge(1000))
        .WillOnce(Return(true));
    
    const uint64_t expected_timestamp = 1640995200000000000ULL; // 2022-01-01 00:00:00 UTC in nanoseconds
    EXPECT_CALL(mock_pps, GetLastPpsTimestamp())
        .WillOnce(Return(expected_timestamp));
    
    EXPECT_CALL(mock_pps, Close())
        .Times(1);
    
    // 执行测试
    EXPECT_TRUE(mock_pps.Initialize());
    EXPECT_TRUE(mock_pps.WaitForPpsEdge(1000));
    
    auto timestamp = mock_pps.GetLastPpsTimestamp();
    EXPECT_EQ(timestamp, expected_timestamp);
    
    mock_pps.Close();
}

/**
 * @brief 测试Mock原子钟接口
 */
TEST_F(MockDeviceInterfaceTest, MockAtomicClockInterface) {
    using namespace timing_server::hal::test;
    
    StrictMock<MockAtomicClock> mock_clock;
    
    // 设置期望的调用和返回值
    EXPECT_CALL(mock_clock, Initialize())
        .WillOnce(Return(true));
    
    ClockHealth expected_health;
    expected_health.temperature = 65.2;
    expected_health.frequency_offset = 0.0001;
    expected_health.is_healthy = true;
    expected_health.status_message = "正常运行";
    
    EXPECT_CALL(mock_clock, GetHealth())
        .WillOnce(Return(expected_health));
    
    EXPECT_CALL(mock_clock, SetFrequencyCorrection(0.001))
        .WillOnce(Return(true));
    
    EXPECT_CALL(mock_clock, GetFrequencyOffset())
        .WillOnce(Return(0.001));
    
    EXPECT_CALL(mock_clock, Close())
        .Times(1);
    
    // 执行测试
    EXPECT_TRUE(mock_clock.Initialize());
    
    auto health = mock_clock.GetHealth();
    EXPECT_DOUBLE_EQ(health.temperature, 65.2);
    EXPECT_DOUBLE_EQ(health.frequency_offset, 0.0001);
    EXPECT_TRUE(health.is_healthy);
    EXPECT_EQ(health.status_message, "正常运行");
    
    EXPECT_TRUE(mock_clock.SetFrequencyCorrection(0.001));
    EXPECT_DOUBLE_EQ(mock_clock.GetFrequencyOffset(), 0.001);
    
    mock_clock.Close();
}

/**
 * @brief 数据结构测试套件
 * 测试HAL层数据结构的正确性
 */
class DataStructureTest : public ::testing::Test {
protected:
    void SetUp() override {}
    void TearDown() override {}
};

/**
 * @brief 测试时间数据结构
 */
TEST_F(DataStructureTest, TimeDataStructure) {
    timing_server::hal::TimeData time_data;
    time_data.timestamp_ns = 1640995200000000000ULL;
    time_data.frequency_offset = 0.001;
    time_data.phase_offset = 12.5;
    time_data.quality = 95;
    time_data.is_valid = true;
    
    EXPECT_EQ(time_data.timestamp_ns, 1640995200000000000ULL);
    EXPECT_DOUBLE_EQ(time_data.frequency_offset, 0.001);
    EXPECT_DOUBLE_EQ(time_data.phase_offset, 12.5);
    EXPECT_EQ(time_data.quality, 95);
    EXPECT_TRUE(time_data.is_valid);
}

/**
 * @brief 测试卫星信息结构
 */
TEST_F(DataStructureTest, SatelliteInfoStructure) {
    SatelliteInfo sat_info;
    sat_info.satellite_count = 12;
    sat_info.signal_strength_db = -142.0;
    sat_info.is_locked = true;
    sat_info.fix_type = "3D";
    
    EXPECT_EQ(sat_info.satellite_count, 12);
    EXPECT_DOUBLE_EQ(sat_info.signal_strength_db, -142.0);
    EXPECT_TRUE(sat_info.is_locked);
    EXPECT_EQ(sat_info.fix_type, "3D");
}

/**
 * @brief 测试PTP配置结构
 */
TEST_F(DataStructureTest, PTPConfigStructure) {
    PTPConfig ptp_config;
    ptp_config.domain = 0;
    ptp_config.priority1 = 128;
    ptp_config.priority2 = 128;
    ptp_config.clock_class = 6;
    ptp_config.clock_accuracy = 0x21;
    ptp_config.interface = "eth0";
    
    EXPECT_EQ(ptp_config.domain, 0);
    EXPECT_EQ(ptp_config.priority1, 128);
    EXPECT_EQ(ptp_config.priority2, 128);
    EXPECT_EQ(ptp_config.clock_class, 6);
    EXPECT_EQ(ptp_config.clock_accuracy, 0x21);
    EXPECT_EQ(ptp_config.interface, "eth0");
}

/**
 * @brief 主测试函数
 * 运行所有HAL接口相关的单元测试
 */
int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    std::cout << "开始执行HAL接口单元测试..." << std::endl;
    std::cout << "测试范围包括：" << std::endl;
    std::cout << "  - 平台检测功能" << std::endl;
    std::cout << "  - HAL异常处理" << std::endl;
    std::cout << "  - HAL工厂管理器" << std::endl;
    std::cout << "  - Mock设备接口" << std::endl;
    std::cout << "  - 数据结构验证" << std::endl;
    std::cout << std::endl;
    
    int result = RUN_ALL_TESTS();
    
    if (result == 0) {
        std::cout << std::endl << "所有HAL接口测试通过！" << std::endl;
    } else {
        std::cout << std::endl << "部分HAL接口测试失败，请检查实现。" << std::endl;
    }
    
    return result;
}