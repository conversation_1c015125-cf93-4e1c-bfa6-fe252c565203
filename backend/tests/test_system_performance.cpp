/**
 * @file test_system_performance.cpp
 * @brief 系统性能测试 - 专注于资源使用优化和性能指标验证
 * 
 * 本测试文件实现了详细的系统性能测试，包括：
 * 1. CPU使用率监控和优化验证
 * 2. 内存使用监控和泄漏检测
 * 3. 网络性能和延迟测试
 * 4. 磁盘I/O性能测试
 * 5. 并发处理能力测试
 */

#include <gtest/gtest.h>
#include <limits>
#include <cstddef>
#include <gmock/gmock.h>
#include <thread>
#include <chrono>
#include <memory>
#include <vector>
#include <atomic>
#include <future>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <string>

// 系统监控相关头文件
#include <sys/resource.h>

// Mock类定义
#include "mock/mock_hal_interfaces.h"
#include <sys/time.h>
#include <unistd.h>

// 核心组件头文件
#include "core/timing_engine.h"
#include "core/logger.h"
#include "core/database_manager.h"
#include "core/config_manager.h"
#include "core/error_handler.h"
#include "api/timing_service.h"
#include "hal/interfaces.h"

using namespace std::chrono_literals;
using namespace timing_server;
using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;

namespace timing_server {
namespace test {

// 使用统一的Mock类定义
using MockGnssReceiver = hal::MockGnssReceiver;
using MockPpsInput = hal::MockPpsInput;
using MockAtomicClock = hal::MockAtomicClock;
using MockHalFactory = hal::MockHalFactory;

/**
 * @struct SystemMetrics
 * @brief 系统性能指标结构
 */
struct SystemMetrics {
    double cpu_usage_percent = 0.0;
    size_t memory_usage_kb = 0;
    size_t memory_peak_kb = 0;
    double disk_io_rate_mbps = 0.0;
    size_t network_bytes_sent = 0;
    size_t network_bytes_received = 0;
    std::chrono::microseconds avg_response_time{0};
    size_t active_threads = 0;
    size_t open_file_descriptors = 0;
};

/**
 * @class SystemPerformanceTest
 * @brief 系统性能测试类
 */
class SystemPerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化单例组件
        auto& logger = core::Logger::GetInstance();
        logger.Initialize("performance_test.log");
        logger_ = std::shared_ptr<core::Logger>(&logger, [](core::Logger*){});

        auto& database_manager = core::DatabaseManager::GetInstance();
        database_manager.Initialize(":memory:");
        database_manager_ = std::shared_ptr<core::DatabaseManager>(&database_manager, [](core::DatabaseManager*){});

        auto& error_handler = core::ErrorHandler::GetInstance();
        error_handler.Initialize();
        error_handler_ = std::shared_ptr<core::ErrorHandler>(&error_handler, [](core::ErrorHandler*){});

        // 创建非单例组件
        config_manager_ = std::make_shared<core::ConfigManager>("test_config.json");
        config_manager_->Initialize();

        hal_factory_ = std::make_shared<MockHalFactory>();

        // 创建默认配置
        core::TimingConfig timing_config;
        timing_engine_ = std::make_shared<core::TimingEngine>(timing_config);

        // 使用工厂函数创建授时服务实例
        timing_service_ = api::createTimingService(timing_engine_, config_manager_, database_manager_);
        
        // 设置基准性能指标
        baseline_metrics_ = GetCurrentSystemMetrics();
        
        // 启动系统
        SetupMockHalBehavior();
        ASSERT_TRUE(timing_engine_->Start()) << "系统启动失败";
        
        // 等待系统稳定
        std::this_thread::sleep_for(1s);
    }
    
    void TearDown() override {
        if (timing_engine_) {
            timing_engine_->Stop();
        }
        
        // 清理资源
        timing_service_.reset();
        timing_engine_.reset();
        database_manager_.reset();
        error_handler_.reset();
        hal_factory_.reset();
        config_manager_.reset();
        logger_.reset();
    }
    
    /**
     * @brief 设置Mock HAL行为
     */
    void SetupMockHalBehavior() {
        // 先创建HAL对象以获取Mock指针
        auto gnss_receiver = hal_factory_->CreateGnssReceiver();
        auto pps_input = hal_factory_->CreatePpsInput();
        auto atomic_clock = hal_factory_->CreateAtomicClock();
        
        // 简化的Mock设置 - 由于我们使用的是Google Mock的MOCK_METHOD
        // 这些Mock对象会自动返回默认值，我们不需要显式设置期望
        // 在实际的性能测试中，我们主要关注系统的资源使用情况
        std::cout << "Mock HAL行为已设置" << std::endl;
    }
    
    /**
     * @brief 获取当前系统性能指标
     */
    SystemMetrics GetCurrentSystemMetrics() {
        SystemMetrics metrics;
        
        // 获取内存使用情况
        struct rusage usage;
        if (getrusage(RUSAGE_SELF, &usage) == 0) {
            metrics.memory_usage_kb = usage.ru_maxrss;
            metrics.memory_peak_kb = usage.ru_maxrss;
        }
        
        // 获取CPU使用情况（简化实现）
        metrics.cpu_usage_percent = GetCpuUsagePercent();
        
        // 获取线程数量
        metrics.active_threads = GetActiveThreadCount();
        
        // 获取文件描述符数量
        metrics.open_file_descriptors = GetOpenFileDescriptorCount();
        
        return metrics;
    }
    
    /**
     * @brief 获取CPU使用率
     */
    double GetCpuUsagePercent() {
        static auto last_time = std::chrono::steady_clock::now();
        static struct rusage last_usage;
        static bool first_call = true;
        
        auto current_time = std::chrono::steady_clock::now();
        struct rusage current_usage;
        
        if (getrusage(RUSAGE_SELF, &current_usage) != 0) {
            return 0.0;
        }
        
        if (first_call) {
            last_time = current_time;
            last_usage = current_usage;
            first_call = false;
            return 0.0;
        }
        
        auto time_diff = std::chrono::duration_cast<std::chrono::microseconds>(
            current_time - last_time).count();
        
        auto user_time_diff = (current_usage.ru_utime.tv_sec - last_usage.ru_utime.tv_sec) * 1000000 +
                             (current_usage.ru_utime.tv_usec - last_usage.ru_utime.tv_usec);
        auto sys_time_diff = (current_usage.ru_stime.tv_sec - last_usage.ru_stime.tv_sec) * 1000000 +
                            (current_usage.ru_stime.tv_usec - last_usage.ru_stime.tv_usec);
        
        double cpu_percent = ((double)(user_time_diff + sys_time_diff) / time_diff) * 100.0;
        
        last_time = current_time;
        last_usage = current_usage;
        
        return cpu_percent;
    }
    
    /**
     * @brief 获取活跃线程数量
     */
    size_t GetActiveThreadCount() {
        std::ifstream status_file("/proc/self/status");
        std::string line;
        
        while (std::getline(status_file, line)) {
            if (line.find("Threads:") == 0) {
                std::istringstream iss(line);
                std::string label;
                size_t count;
                iss >> label >> count;
                return count;
            }
        }
        
        return 0;
    }
    
    /**
     * @brief 获取打开的文件描述符数量
     */
    size_t GetOpenFileDescriptorCount() {
        std::ifstream status_file("/proc/self/status");
        std::string line;
        
        while (std::getline(status_file, line)) {
            if (line.find("FDSize:") == 0) {
                std::istringstream iss(line);
                std::string label;
                size_t count;
                iss >> label >> count;
                return count;
            }
        }
        
        return 0;
    }
    
    /**
     * @brief 执行负载测试
     */
    void ExecuteLoadTest(int duration_seconds, int concurrent_requests) {
        std::atomic<int> completed_requests(0);
        std::atomic<int> failed_requests(0);
        std::vector<std::chrono::microseconds> response_times;
        std::mutex response_times_mutex;
        
        auto test_function = [&]() {
            auto start_time = std::chrono::steady_clock::now();
            auto end_time = start_time + std::chrono::seconds(duration_seconds);
            
            while (std::chrono::steady_clock::now() < end_time) {
                auto request_start = std::chrono::high_resolution_clock::now();
                auto response = timing_service_->getSystemStatus();
                auto request_end = std::chrono::high_resolution_clock::now();
                
                auto response_time = std::chrono::duration_cast<std::chrono::microseconds>(
                    request_end - request_start);
                
                {
                    std::lock_guard<std::mutex> lock(response_times_mutex);
                    response_times.push_back(response_time);
                }
                
                // 检查响应是否有效（SystemStatus没有success字段，检查其他字段）
                if (response.health != core::SystemHealth::CRITICAL) {
                    completed_requests++;
                } else {
                    failed_requests++;
                }
                
                // 短暂休息以避免过度负载
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        };
        
        // 启动并发测试线程
        std::vector<std::thread> test_threads;
        for (int i = 0; i < concurrent_requests; ++i) {
            test_threads.emplace_back(test_function);
        }
        
        // 等待所有线程完成
        for (auto& thread : test_threads) {
            thread.join();
        }
        
        // 计算统计信息
        int total_requests = completed_requests + failed_requests;
        double success_rate = (double)completed_requests / total_requests * 100.0;
        
        auto avg_response_time = std::accumulate(response_times.begin(), response_times.end(),
                                               std::chrono::microseconds(0)) / response_times.size();
        
        // 记录测试结果
        LOG_INFO(core::LogComponent::SYSTEM, "  负载测试结果:");
        LOG_INFO(core::LogComponent::SYSTEM, "  总请求数: " + std::to_string(total_requests));
        LOG_INFO(core::LogComponent::SYSTEM, "  成功请求数: " + std::to_string(completed_requests.load()));
        LOG_INFO(core::LogComponent::SYSTEM, "  失败请求数: " + std::to_string(failed_requests.load()));
        LOG_INFO(core::LogComponent::SYSTEM, "  成功率: " + std::to_string(success_rate) + "%");
        LOG_INFO(core::LogComponent::SYSTEM, "  平均响应时间: " + std::to_string(avg_response_time.count()) + "μs");
        
        // 验证性能要求
        EXPECT_GT(success_rate, 99.0) << "请求成功率应大于99%";
        EXPECT_LT(avg_response_time.count(), 10000) << "平均响应时间应小于10ms";
    }
    
protected:
    std::shared_ptr<core::Logger> logger_;
    std::shared_ptr<core::ConfigManager> config_manager_;
    std::shared_ptr<MockHalFactory> hal_factory_;
    std::shared_ptr<core::ErrorHandler> error_handler_;
    std::shared_ptr<core::DatabaseManager> database_manager_;
    std::shared_ptr<core::TimingEngine> timing_engine_;
    std::shared_ptr<api::TimingService> timing_service_;
    
    SystemMetrics baseline_metrics_;
};

/**
 * @test 测试CPU使用率优化
 * 验证系统CPU使用率符合<5%的要求
 */
TEST_F(SystemPerformanceTest, CpuUsageOptimization) {
    // 运行一段时间以获得稳定的CPU使用率
    const int monitoring_duration = 10; // 秒
    const int sample_interval = 1; // 秒
    
    std::vector<double> cpu_samples;
    
    for (int i = 0; i < monitoring_duration; ++i) {
        std::this_thread::sleep_for(std::chrono::seconds(sample_interval));
        double cpu_usage = GetCpuUsagePercent();
        cpu_samples.push_back(cpu_usage);
        
        LOG_INFO(core::LogComponent::SYSTEM, "CPU使用率采样 " + std::to_string(i+1) + ": " + 
                        std::to_string(cpu_usage) + "%");
    }
    
    // 计算平均CPU使用率
    double avg_cpu_usage = std::accumulate(cpu_samples.begin(), cpu_samples.end(), 0.0) / 
                          cpu_samples.size();
    
    // 计算最大CPU使用率
    double max_cpu_usage = *std::max_element(cpu_samples.begin(), cpu_samples.end());
    
    LOG_INFO(core::LogComponent::SYSTEM, "CPU使用率统计:");
    LOG_INFO(core::LogComponent::SYSTEM, "  平均CPU使用率: " + std::to_string(avg_cpu_usage) + "%");
    LOG_INFO(core::LogComponent::SYSTEM, "  最大CPU使用率: " + std::to_string(max_cpu_usage) + "%");
    
    // 验证CPU使用率要求
    EXPECT_LT(avg_cpu_usage, 5.0) << "平均CPU使用率应小于5%";
    EXPECT_LT(max_cpu_usage, 10.0) << "峰值CPU使用率应小于10%";
}

/**
 * @test 测试内存使用优化
 * 验证系统内存使用符合<100MB的要求
 */
TEST_F(SystemPerformanceTest, MemoryUsageOptimization) {
    // 监控内存使用情况
    const int monitoring_duration = 10; // 秒
    const int sample_interval = 1; // 秒
    
    std::vector<size_t> memory_samples;
    size_t initial_memory = GetCurrentSystemMetrics().memory_usage_kb;
    
    for (int i = 0; i < monitoring_duration; ++i) {
        std::this_thread::sleep_for(std::chrono::seconds(sample_interval));
        
        auto metrics = GetCurrentSystemMetrics();
        memory_samples.push_back(metrics.memory_usage_kb);
        
        LOG_INFO(core::LogComponent::SYSTEM, "内存使用采样 " + std::to_string(i+1) + ": " + 
                        std::to_string(metrics.memory_usage_kb / 1024.0) + " MB");
    }
    
    // 计算内存使用统计
    size_t avg_memory_kb = std::accumulate(memory_samples.begin(), memory_samples.end(), 0UL) / 
                          memory_samples.size();
    size_t max_memory_kb = *std::max_element(memory_samples.begin(), memory_samples.end());
    
    double avg_memory_mb = avg_memory_kb / 1024.0;
    double max_memory_mb = max_memory_kb / 1024.0;
    
    LOG_INFO(core::LogComponent::SYSTEM, "内存使用统计:");
    LOG_INFO(core::LogComponent::SYSTEM, "  初始内存使用: " + std::to_string(initial_memory / 1024.0) + " MB");
    LOG_INFO(core::LogComponent::SYSTEM, "  平均内存使用: " + std::to_string(avg_memory_mb) + " MB");
    LOG_INFO(core::LogComponent::SYSTEM, "  最大内存使用: " + std::to_string(max_memory_mb) + " MB");
    
    // 验证内存使用要求
    EXPECT_LT(avg_memory_mb, 100.0) << "平均内存使用应小于100MB";
    EXPECT_LT(max_memory_mb, 120.0) << "峰值内存使用应小于120MB";
    
    // 检查内存泄漏
    size_t memory_growth = max_memory_kb - initial_memory;
    double growth_percentage = (double)memory_growth / initial_memory * 100.0;
    
    EXPECT_LT(growth_percentage, 20.0) << "内存增长应小于20%，可能存在内存泄漏";
}

/**
 * @test 测试API响应时间性能
 * 验证API响应时间符合<10ms的要求
 */
TEST_F(SystemPerformanceTest, ApiResponseTimePerformance) {
    const int num_requests = 1000;
    std::vector<std::chrono::microseconds> response_times;
    
    // 预热
    for (int i = 0; i < 10; ++i) {
        timing_service_->getSystemStatus();
    }
    
    // 执行性能测试
    for (int i = 0; i < num_requests; ++i) {
        auto start = std::chrono::high_resolution_clock::now();
        auto response = timing_service_->getSystemStatus();
        auto end = std::chrono::high_resolution_clock::now();
        
        // 检查响应是否有效（SystemStatus没有success字段，检查其他字段）
        EXPECT_NE(response.health, core::SystemHealth::CRITICAL) << "API请求失败";
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        response_times.push_back(duration);
    }
    
    // 计算统计信息
    auto total_time = std::accumulate(response_times.begin(), response_times.end(),
                                     std::chrono::microseconds(0));
    auto avg_time = total_time / num_requests;
    
    auto min_time = *std::min_element(response_times.begin(), response_times.end());
    auto max_time = *std::max_element(response_times.begin(), response_times.end());
    
    // 计算95百分位数
    std::sort(response_times.begin(), response_times.end());
    auto p95_time = response_times[static_cast<size_t>(num_requests * 0.95)];
    
    LOG_INFO(core::LogComponent::SYSTEM, "API响应时间统计:");
    LOG_INFO(core::LogComponent::SYSTEM, "  平均响应时间: " + std::to_string(avg_time.count()) + "μs");
    LOG_INFO(core::LogComponent::SYSTEM, "  最小响应时间: " + std::to_string(min_time.count()) + "μs");
    LOG_INFO(core::LogComponent::SYSTEM, "  最大响应时间: " + std::to_string(max_time.count()) + "μs");
    LOG_INFO(core::LogComponent::SYSTEM, "  95%响应时间: " + std::to_string(p95_time.count()) + "μs");
    
    // 验证响应时间要求
    EXPECT_LT(avg_time.count(), 10000) << "平均响应时间应小于10ms";
    EXPECT_LT(p95_time.count(), 20000) << "95%响应时间应小于20ms";
    EXPECT_LT(max_time.count(), 50000) << "最大响应时间应小于50ms";
}

/**
 * @test 测试并发处理能力
 * 验证系统能够处理多个并发请求
 */
TEST_F(SystemPerformanceTest, ConcurrentProcessingCapability) {
    const int num_threads = 20;
    const int requests_per_thread = 100;
    const int total_requests = num_threads * requests_per_thread;
    
    std::atomic<int> successful_requests(0);
    std::atomic<int> failed_requests(0);
    std::vector<std::chrono::microseconds> all_response_times;
    std::mutex response_times_mutex;
    
    auto concurrent_test = [&]() {
        std::vector<std::chrono::microseconds> thread_response_times;
        
        for (int i = 0; i < requests_per_thread; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            auto response = timing_service_->getSystemStatus();
            auto end = std::chrono::high_resolution_clock::now();
            
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            thread_response_times.push_back(duration);
            
            // 检查响应是否有效（SystemStatus没有success字段，检查其他字段）
            if (response.health != core::SystemHealth::CRITICAL) {
                successful_requests++;
            } else {
                failed_requests++;
            }
        }
        
        // 合并响应时间数据
        {
            std::lock_guard<std::mutex> lock(response_times_mutex);
            all_response_times.insert(all_response_times.end(),
                                    thread_response_times.begin(),
                                    thread_response_times.end());
        }
    };
    
    // 启动并发测试
    auto start_time = std::chrono::high_resolution_clock::now();
    
    std::vector<std::thread> threads;
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back(concurrent_test);
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        end_time - start_time);
    
    // 计算性能指标
    double success_rate = (double)successful_requests / total_requests * 100.0;
    double requests_per_second = (double)total_requests / total_duration.count() * 1000.0;
    
    auto avg_response_time = std::accumulate(all_response_times.begin(), all_response_times.end(),
                                           std::chrono::microseconds(0)) / all_response_times.size();
    
    LOG_INFO(core::LogComponent::SYSTEM, "并发处理能力测试结果:");
    LOG_INFO(core::LogComponent::SYSTEM, "  并发线程数: " + std::to_string(num_threads));
    LOG_INFO(core::LogComponent::SYSTEM, "  总请求数: " + std::to_string(total_requests));
    LOG_INFO(core::LogComponent::SYSTEM, "  成功请求数: " + std::to_string(successful_requests.load()));
    LOG_INFO(core::LogComponent::SYSTEM, "  失败请求数: " + std::to_string(failed_requests.load()));
    LOG_INFO(core::LogComponent::SYSTEM, "  成功率: " + std::to_string(success_rate) + "%");
    LOG_INFO(core::LogComponent::SYSTEM, "  处理速度: " + std::to_string(requests_per_second) + " req/s");
    LOG_INFO(core::LogComponent::SYSTEM, "  平均响应时间: " + std::to_string(avg_response_time.count()) + "μs");
    
    // 验证并发处理能力
    EXPECT_GT(success_rate, 99.0) << "并发请求成功率应大于99%";
    EXPECT_GT(requests_per_second, 500.0) << "并发处理速度应大于500 req/s";
    EXPECT_LT(avg_response_time.count(), 20000) << "并发情况下平均响应时间应小于20ms";
}

/**
 * @test 测试资源清理和垃圾回收
 * 验证系统能够正确清理资源，避免资源泄漏
 */
TEST_F(SystemPerformanceTest, ResourceCleanupAndGarbageCollection) {
    // 记录初始资源状态
    auto initial_metrics = GetCurrentSystemMetrics();
    
    // 执行大量操作以产生临时资源
    const int num_operations = 1000;
    
    for (int i = 0; i < num_operations; ++i) {
        // 创建临时对象和执行操作
        auto response = timing_service_->getSystemStatus();
        // 检查响应是否有效（SystemStatus没有success字段，检查其他字段）
        EXPECT_NE(response.health, core::SystemHealth::CRITICAL);
        
        // 模拟一些内存分配和释放
        std::vector<char> temp_buffer(1024 * 1024); // 1MB临时缓冲区
        std::fill(temp_buffer.begin(), temp_buffer.end(), static_cast<char>(i % 256));
        
        // 短暂休息
        if (i % 100 == 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
    
    // 等待资源清理
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 检查最终资源状态
    auto final_metrics = GetCurrentSystemMetrics();
    
    // 计算资源变化
    long memory_diff = static_cast<long>(final_metrics.memory_usage_kb) - 
                      static_cast<long>(initial_metrics.memory_usage_kb);
    long fd_diff = static_cast<long>(final_metrics.open_file_descriptors) - 
                  static_cast<long>(initial_metrics.open_file_descriptors);
    
    LOG_INFO(core::LogComponent::SYSTEM, "资源清理测试结果:");
    LOG_INFO(core::LogComponent::SYSTEM, "  初始内存使用: " + std::to_string(initial_metrics.memory_usage_kb / 1024.0) + " MB");
    LOG_INFO(core::LogComponent::SYSTEM, "  最终内存使用: " + std::to_string(final_metrics.memory_usage_kb / 1024.0) + " MB");
    LOG_INFO(core::LogComponent::SYSTEM, "  内存变化: " + std::to_string(memory_diff / 1024.0) + " MB");
    LOG_INFO(core::LogComponent::SYSTEM, "  初始文件描述符: " + std::to_string(initial_metrics.open_file_descriptors));
    LOG_INFO(core::LogComponent::SYSTEM, "  最终文件描述符: " + std::to_string(final_metrics.open_file_descriptors));
    LOG_INFO(core::LogComponent::SYSTEM, "  文件描述符变化: " + std::to_string(fd_diff));
    
    // 验证资源清理效果
    EXPECT_LT(std::abs(memory_diff), 10 * 1024) << "内存使用变化应小于10MB";
    EXPECT_EQ(fd_diff, 0) << "文件描述符应该完全清理";
    
    // 验证最终资源使用仍在限制范围内
    EXPECT_LT(final_metrics.memory_usage_kb / 1024.0, 100.0) << "最终内存使用应小于100MB";
}

/**
 * @test 测试长期运行性能稳定性
 * 验证系统在长期运行中的性能稳定性
 */
TEST_F(SystemPerformanceTest, LongTermPerformanceStability) {
    const int test_duration_minutes = 5; // 简化为5分钟测试
    const int sample_interval_seconds = 30;
    const int total_samples = (test_duration_minutes * 60) / sample_interval_seconds;
    
    std::vector<SystemMetrics> performance_samples;
    
    LOG_INFO(core::LogComponent::SYSTEM, "开始长期性能稳定性测试，持续时间: " + std::to_string(test_duration_minutes) + " 分钟");
    
    for (int i = 0; i < total_samples; ++i) {
        // 执行一些负载操作
        for (int j = 0; j < 10; ++j) {
            auto response = timing_service_->getSystemStatus();
            // 检查响应是否有效（SystemStatus没有success字段，检查其他字段）
            EXPECT_NE(response.health, core::SystemHealth::CRITICAL);
        }
        
        // 采集性能指标
        auto metrics = GetCurrentSystemMetrics();
        performance_samples.push_back(metrics);
        
        LOG_INFO(core::LogComponent::SYSTEM,
                 "性能采样 " + std::to_string(i+1) + "/" + std::to_string(total_samples) +
                 " - CPU: " + std::to_string(metrics.cpu_usage_percent) + "%" +
                 ", 内存: " + std::to_string(metrics.memory_usage_kb / 1024.0) + "MB");
        
        // 等待下一个采样间隔
        std::this_thread::sleep_for(std::chrono::seconds(sample_interval_seconds));
    }
    
    // 分析性能稳定性
    std::vector<double> cpu_values;
    std::vector<double> memory_values;
    
    for (const auto& sample : performance_samples) {
        cpu_values.push_back(sample.cpu_usage_percent);
        memory_values.push_back(sample.memory_usage_kb / 1024.0);
    }
    
    // 计算统计信息
    double avg_cpu = std::accumulate(cpu_values.begin(), cpu_values.end(), 0.0) / cpu_values.size();
    double avg_memory = std::accumulate(memory_values.begin(), memory_values.end(), 0.0) / memory_values.size();
    
    double max_cpu = *std::max_element(cpu_values.begin(), cpu_values.end());
    double max_memory = *std::max_element(memory_values.begin(), memory_values.end());
    
    // 计算标准差（稳定性指标）
    double cpu_variance = 0.0;
    double memory_variance = 0.0;
    
    for (size_t i = 0; i < cpu_values.size(); ++i) {
        cpu_variance += std::pow(cpu_values[i] - avg_cpu, 2);
        memory_variance += std::pow(memory_values[i] - avg_memory, 2);
    }
    
    double cpu_stddev = std::sqrt(cpu_variance / cpu_values.size());
    double memory_stddev = std::sqrt(memory_variance / memory_values.size());
    
    LOG_INFO(core::LogComponent::SYSTEM, "长期性能稳定性测试结果:");
    LOG_INFO(core::LogComponent::SYSTEM, "  平均CPU使用率: " + std::to_string(avg_cpu) + "% (标准差: " + std::to_string(cpu_stddev) + ")");
    LOG_INFO(core::LogComponent::SYSTEM, "  最大CPU使用率: " + std::to_string(max_cpu) + "%");
    LOG_INFO(core::LogComponent::SYSTEM, "  平均内存使用: " + std::to_string(avg_memory) + "MB (标准差: " + std::to_string(memory_stddev) + ")");
    LOG_INFO(core::LogComponent::SYSTEM, "  最大内存使用: " + std::to_string(max_memory) + "MB");
    
    // 验证长期稳定性
    EXPECT_LT(avg_cpu, 5.0) << "长期平均CPU使用率应小于5%";
    EXPECT_LT(max_cpu, 15.0) << "长期最大CPU使用率应小于15%";
    EXPECT_LT(avg_memory, 100.0) << "长期平均内存使用应小于100MB";
    EXPECT_LT(max_memory, 120.0) << "长期最大内存使用应小于120MB";
    
    // 验证性能稳定性（标准差应该较小）
    EXPECT_LT(cpu_stddev, 2.0) << "CPU使用率标准差应小于2%，表示性能稳定";
    EXPECT_LT(memory_stddev, 10.0) << "内存使用标准差应小于10MB，表示内存稳定";
}

} // namespace test
} // namespace timing_server