#include <gtest/gtest.h>
#include "core/types.h"

using namespace timing_server::core;

/**
 * @brief 核心类型测试
 * 测试核心数据类型和枚举的功能
 */
class CoreTypesTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 测试初始化代码
    }
    
    void TearDown() override {
        // 测试清理代码
    }
};

/**
 * @brief 测试时钟状态枚举
 */
TEST_F(CoreTypesTest, ClockStateEnum) {
    EXPECT_EQ(ClockStateToString(ClockState::FREE_RUN), "FREE_RUN");
    EXPECT_EQ(ClockStateToString(ClockState::DISCIPLINING), "DISCIPLINING");
    EXPECT_EQ(ClockStateToString(ClockState::LOCKED), "LOCKED");
    EXPECT_EQ(ClockStateToString(ClockState::HOLDOVER), "HOLDOVER");
}

/**
 * @brief 测试时间源枚举
 */
TEST_F(CoreTypesTest, TimeSourceEnum) {
    EXPECT_EQ(TimeSourceToString(TimeSource::GNSS), "GNSS");
    EXPECT_EQ(TimeSourceToString(TimeSource::RUBIDIUM), "RUBIDIUM");
    EXPECT_EQ(TimeSourceToString(TimeSource::RTC), "RTC");
    EXPECT_EQ(TimeSourceToString(TimeSource::EXTERNAL_PPS), "EXTERNAL_PPS");
    EXPECT_EQ(TimeSourceToString(TimeSource::EXTERNAL_10MHZ), "EXTERNAL_10MHZ");
    EXPECT_EQ(TimeSourceToString(TimeSource::PHC), "PHC");
    EXPECT_EQ(TimeSourceToString(TimeSource::SYSTEM_CLOCK), "SYSTEM_CLOCK");
}

/**
 * @brief 测试时间质量结构
 */
TEST_F(CoreTypesTest, TimeQualityStructure) {
    TimeQuality quality;
    quality.accuracy_ns = 50.0;
    quality.stability_ppm = 1e-12;
    quality.confidence = 95;
    quality.is_traceable = true;
    quality.reference = "GPS";
    
    EXPECT_DOUBLE_EQ(quality.accuracy_ns, 50.0);
    EXPECT_DOUBLE_EQ(quality.stability_ppm, 1e-12);
    EXPECT_EQ(quality.confidence, 95);
    EXPECT_TRUE(quality.is_traceable);
    EXPECT_EQ(quality.reference, "GPS");
}

/**
 * @brief 测试系统状态结构
 */
TEST_F(CoreTypesTest, SystemStatusStructure) {
    SystemStatus status;
    status.current_state = ClockState::LOCKED;
    status.active_source = TimeSource::GNSS;
    status.health = SystemHealth::HEALTHY;
    status.uptime_seconds = 3600; // 1小时
    status.cpu_usage_percent = 2.5;
    status.memory_usage_mb = 64;
    status.version = "1.0.0";
    status.platform = "linux-x86_64";
    
    EXPECT_EQ(status.current_state, ClockState::LOCKED);
    EXPECT_EQ(status.active_source, TimeSource::GNSS);
    EXPECT_EQ(status.health, SystemHealth::HEALTHY);
    EXPECT_EQ(status.uptime_seconds, 3600);
    EXPECT_DOUBLE_EQ(status.cpu_usage_percent, 2.5);
    EXPECT_EQ(status.memory_usage_mb, 64);
    EXPECT_EQ(status.version, "1.0.0");
    EXPECT_EQ(status.platform, "linux-x86_64");
}