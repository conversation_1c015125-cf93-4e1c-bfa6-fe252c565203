#include <iostream>
#include <chrono>
#include <thread>
#include <cassert>
#include <iomanip>

// 包含HAL接口和工厂
#include "hal/interfaces.h"
#include "hal/hal_factory.h"

using namespace timing_server::hal;

/**
 * @brief 测试Mock HAL实现的功能
 * 
 * 这个测试程序验证所有Mock HAL实现是否正常工作：
 * 1. 测试HAL工厂创建
 * 2. 测试各种Mock设备的基本功能
 * 3. 验证设备间的协调工作
 * 4. 测试异常处理
 */

void TestMockGnssReceiver(std::shared_ptr<I_HalFactory> factory) {
    std::cout << "\n=== 测试Mock GNSS接收机 ===" << std::endl;
    
    auto gnss = factory->CreateGnssReceiver();
    assert(gnss != nullptr);
    
    // 初始化
    bool init_result = gnss->Initialize();
    assert(init_result);
    std::cout << "✓ GNSS接收机初始化成功" << std::endl;
    
    // 测试信号有效性
    bool signal_valid = gnss->IsSignalValid();
    std::cout << "✓ GNSS信号状态: " << (signal_valid ? "有效" : "无效") << std::endl;
    
    // 读取几个NMEA语句
    for (int i = 0; i < 5; i++) {
        std::string nmea = gnss->ReadNmeaSentence();
        if (!nmea.empty()) {
            std::cout << "✓ NMEA数据: " << nmea.substr(0, 50) << "..." << std::endl;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // 获取卫星信息
    SatelliteInfo sat_info = gnss->GetSatelliteInfo();
    std::cout << "✓ 卫星信息: " << sat_info.satellite_count << "颗卫星, "
              << "信号强度: " << sat_info.signal_strength_db << "dB" << std::endl;
    
    gnss->Close();
    std::cout << "✓ GNSS接收机测试完成" << std::endl;
}

void TestMockPpsInput(std::shared_ptr<I_HalFactory> factory) {
    std::cout << "\n=== 测试Mock PPS输入 ===" << std::endl;
    
    auto pps = factory->CreatePpsInput();
    assert(pps != nullptr);
    
    // 初始化
    bool init_result = pps->Initialize();
    assert(init_result);
    std::cout << "✓ PPS输入初始化成功" << std::endl;
    
    // 等待几个PPS信号
    for (int i = 0; i < 3; i++) {
        std::cout << "等待PPS信号 #" << (i + 1) << "..." << std::endl;
        bool pps_detected = pps->WaitForPpsEdge(2000);  // 2秒超时
        
        if (pps_detected) {
            uint64_t timestamp = pps->GetLastPpsTimestamp();
            std::cout << "✓ 检测到PPS信号, 时间戳: " << timestamp << " ns" << std::endl;
        } else {
            std::cout << "⚠ PPS信号等待超时" << std::endl;
        }
    }
    
    pps->Close();
    std::cout << "✓ PPS输入测试完成" << std::endl;
}

void TestMockAtomicClock(std::shared_ptr<I_HalFactory> factory) {
    std::cout << "\n=== 测试Mock 原子钟 ===" << std::endl;
    
    auto clock = factory->CreateAtomicClock();
    assert(clock != nullptr);
    
    // 初始化
    bool init_result = clock->Initialize();
    assert(init_result);
    std::cout << "✓ 原子钟初始化成功" << std::endl;
    
    // 获取初始状态
    ClockHealth health = clock->GetStatus();
    std::cout << "✓ 原子钟状态: 温度=" << health.temperature << "°C, "
              << "频率偏移=" << health.frequency_offset << ", "
              << "健康=" << (health.is_healthy ? "是" : "否") << std::endl;
    
    // 测试频率校正
    double correction = 1e-7;  // 0.1ppm校正
    bool correction_result = clock->SetFrequencyCorrection(correction);
    assert(correction_result);
    std::cout << "✓ 频率校正设置成功: " << correction << " ppm" << std::endl;
    
    // 等待一段时间观察变化
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 再次获取状态
    health = clock->GetHealth();
    std::cout << "✓ 更新后状态: 温度=" << health.temperature << "°C, "
              << "频率偏移=" << clock->GetFrequencyOffset() << std::endl;
    
    clock->Close();
    std::cout << "✓ 原子钟测试完成" << std::endl;
}

void TestMockFrequencyInput(std::shared_ptr<I_HalFactory> factory) {
    std::cout << "\n=== 测试Mock 频率输入 ===" << std::endl;
    
    auto freq = factory->CreateFrequencyInput();
    assert(freq != nullptr);
    
    // 初始化
    bool init_result = freq->Initialize();
    assert(init_result);
    std::cout << "✓ 频率输入初始化成功" << std::endl;
    
    // 检查信号存在
    bool signal_present = freq->IsSignalPresent();
    std::cout << "✓ 频率信号状态: " << (signal_present ? "存在" : "不存在") << std::endl;
    
    if (signal_present) {
        // 进行几次频率测量
        for (int i = 0; i < 5; i++) {
            double frequency = freq->MeasureFrequency();
            double error_ppm = (frequency - 10000000.0) / 10000000.0 * 1e6;
            std::cout << "✓ 频率测量 #" << (i + 1) << ": " 
                      << std::fixed << std::setprecision(6) << frequency 
                      << " Hz (误差: " << std::scientific << error_ppm << " ppm)" << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
    
    freq->Close();
    std::cout << "✓ 频率输入测试完成" << std::endl;
}

void TestMockRtc(std::shared_ptr<I_HalFactory> factory) {
    std::cout << "\n=== 测试Mock 高精度RTC ===" << std::endl;
    
    auto rtc = factory->CreateRtc();
    assert(rtc != nullptr);
    
    // 初始化
    bool init_result = rtc->Initialize();
    assert(init_result);
    std::cout << "✓ RTC初始化成功" << std::endl;
    
    // 检查有效性
    bool is_valid = rtc->IsValid();
    std::cout << "✓ RTC有效性: " << (is_valid ? "有效" : "无效") << std::endl;
    
    if (is_valid) {
        // 读取当前时间
        timespec current_time = rtc->GetTime();
        std::cout << "✓ RTC时间: " << current_time.tv_sec << "." 
                  << std::setfill('0') << std::setw(9) << current_time.tv_nsec << std::endl;
        
        // 设置新时间（当前时间+1秒）
        timespec new_time = current_time;
        new_time.tv_sec += 1;
        
        bool set_result = rtc->SetTime(new_time);
        assert(set_result);
        std::cout << "✓ RTC时间设置成功" << std::endl;
        
        // 验证时间设置
        timespec verify_time = rtc->GetTime();
        std::cout << "✓ 验证时间: " << verify_time.tv_sec << "." 
                  << std::setfill('0') << std::setw(9) << verify_time.tv_nsec << std::endl;
    }
    
    rtc->Close();
    std::cout << "✓ RTC测试完成" << std::endl;
}

void TestMockNetworkInterface(std::shared_ptr<I_HalFactory> factory) {
    std::cout << "\n=== 测试Mock 网络接口 ===" << std::endl;
    
    auto net = factory->CreateNetworkInterface();
    assert(net != nullptr);
    
    // 初始化
    bool init_result = net->Initialize();
    assert(init_result);
    std::cout << "✓ 网络接口初始化成功" << std::endl;
    
    // 获取PHC状态
    PHCStatus status = net->GetPHCStatus();
    std::cout << "✓ PHC状态: 同步=" << (status.is_synchronized ? "是" : "否")
              << ", 偏移=" << status.offset_ns << "ns"
              << ", 时钟等级=" << status.clock_class
              << ", 接口=" << status.interface_name << std::endl;
    
    // 读取PHC时间
    timespec phc_time = net->GetPHCTime();
    std::cout << "✓ PHC时间: " << phc_time.tv_sec << "." 
              << std::setfill('0') << std::setw(9) << phc_time.tv_nsec << std::endl;
    
    // 配置PTP参数
    PTPConfig ptp_config;
    ptp_config.domain = 0;
    ptp_config.priority1 = 128;
    ptp_config.priority2 = 128;
    ptp_config.clock_class = 6;  // GPS锁定
    ptp_config.clock_accuracy = 0x21;  // 25ns
    ptp_config.interface = "eth0";
    
    bool config_result = net->ConfigurePTP(ptp_config);
    assert(config_result);
    std::cout << "✓ PTP配置设置成功" << std::endl;
    
    // 验证配置更新
    PHCStatus updated_status = net->GetPHCStatus();
    std::cout << "✓ 更新后状态: 时钟等级=" << updated_status.clock_class << std::endl;
    
    net->Close();
    std::cout << "✓ 网络接口测试完成" << std::endl;
}

// Convert to Google Test format
#include <gtest/gtest.h>

class MockHalTest : public ::testing::Test {
protected:
    void SetUp() override {
        factory = CreateHalFactory();
        ASSERT_NE(factory, nullptr);
    }
    
    std::shared_ptr<I_HalFactory> factory;
};

TEST_F(MockHalTest, GnssReceiverTest) {
    EXPECT_NO_THROW(TestMockGnssReceiver(factory));
}

TEST_F(MockHalTest, PpsInputTest) {
    EXPECT_NO_THROW(TestMockPpsInput(factory));
}

TEST_F(MockHalTest, AtomicClockTest) {
    EXPECT_NO_THROW(TestMockAtomicClock(factory));
}

TEST_F(MockHalTest, FrequencyInputTest) {
    EXPECT_NO_THROW(TestMockFrequencyInput(factory));
}

TEST_F(MockHalTest, RtcTest) {
    EXPECT_NO_THROW(TestMockRtc(factory));
}

TEST_F(MockHalTest, NetworkInterfaceTest) {
    EXPECT_NO_THROW(TestMockNetworkInterface(factory));
}