/**
 * @file test_simple_compilation.cpp
 * @brief 简化的编译测试 - 验证核心组件是否能正确编译和链接
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <iostream>

// 核心组件头文件
#include "core/timing_engine.h"
#include "core/config_manager.h"
#include "core/logger.h"
#include "core/error_handler.h"
#include "core/database_manager.h"
#include "core/types.h"

// HAL接口头文件
#include "hal/interfaces.h"
#include "hal/hal_factory.h"

// Mock类定义
#include "mock/mock_hal_interfaces.h"

using namespace timing_server;
using ::testing::Return;
using ::testing::_;

namespace timing_server {
namespace test {

/**
 * @brief 简化编译测试类
 * 测试核心组件的基本功能和编译链接
 */
class SimpleCompilationTest : public ::testing::Test {
protected:
    void SetUp() override {
        std::cout << "设置简化编译测试环境..." << std::endl;
        
        // 初始化日志系统
        auto& logger = core::Logger::GetInstance();
        logger.Initialize();
        logger.SetLogLevel(core::LogLevel::INFO);
        
        // 创建配置管理器
        config_manager_ = std::make_shared<core::ConfigManager>("test_config.json");
        bool config_init_result = config_manager_->Initialize();
        if (!config_init_result) {
            std::cout << "警告: 配置管理器初始化失败，使用默认配置" << std::endl;
        }
        
        // 创建默认配置
        core::TimingConfig timing_config;
        timing_engine_ = std::make_shared<core::TimingEngine>(timing_config);
        
        std::cout << "简化编译测试环境设置完成" << std::endl;
    }
    
    void TearDown() override {
        std::cout << "清理简化编译测试环境..." << std::endl;
        
        if (timing_engine_) {
            timing_engine_->Stop();
        }
        
        std::cout << "简化编译测试环境清理完成" << std::endl;
    }

protected:
    std::shared_ptr<core::ConfigManager> config_manager_;
    std::shared_ptr<core::TimingEngine> timing_engine_;
};

/**
 * @brief 测试基本组件创建
 */
TEST_F(SimpleCompilationTest, TestBasicComponentCreation) {
    EXPECT_NE(config_manager_, nullptr) << "配置管理器创建失败";
    EXPECT_NE(timing_engine_, nullptr) << "授时引擎创建失败";
    
    // 测试配置管理器基本功能 - 通过获取配置来验证初始化
    auto config = config_manager_->GetConfig();
    // 如果配置为空，尝试使用默认配置
    if (config.priorities.priorities.empty()) {
        std::cout << "使用默认配置进行测试..." << std::endl;
        config = core::ConfigManager::GetDefaultConfig();
    }
    EXPECT_FALSE(config.priorities.priorities.empty()) << "配置管理器未正确初始化";

    // 测试授时引擎基本状态
    auto current_state = timing_engine_->GetCurrentState();
    // 验证状态是有效的枚举值之一
    bool valid_state = (current_state == core::ClockState::FREE_RUN ||
                       current_state == core::ClockState::DISCIPLINING ||
                       current_state == core::ClockState::LOCKED ||
                       current_state == core::ClockState::HOLDOVER);
    EXPECT_TRUE(valid_state) << "授时引擎状态无效";
    
    std::cout << "✓ 基本组件创建测试通过" << std::endl;
}

/**
 * @brief 测试系统状态获取
 */
TEST_F(SimpleCompilationTest, TestSystemStatusRetrieval) {
    try {
        auto system_status = timing_engine_->GetSystemStatus();

        // 验证系统状态是有效的枚举值之一
        bool valid_state = (system_status.current_state == core::ClockState::FREE_RUN ||
                           system_status.current_state == core::ClockState::DISCIPLINING ||
                           system_status.current_state == core::ClockState::LOCKED ||
                           system_status.current_state == core::ClockState::HOLDOVER);
        EXPECT_TRUE(valid_state) << "系统状态无效";
        EXPECT_GE(system_status.uptime_seconds, 0) << "系统运行时间应为非负数";
        EXPECT_GE(system_status.cpu_usage_percent, 0.0) << "CPU使用率应为非负数";
        EXPECT_GE(system_status.memory_usage_mb, 0) << "内存使用量应为非负数";
        EXPECT_FALSE(system_status.version.empty()) << "版本信息不应为空";

        std::cout << "✓ 系统状态获取测试通过" << std::endl;
        std::cout << "  - 当前状态: " << static_cast<int>(system_status.current_state) << std::endl;
        std::cout << "  - 运行时间: " << system_status.uptime_seconds << "秒" << std::endl;
        std::cout << "  - 版本信息: " << system_status.version << std::endl;
    } catch (const std::exception& e) {
        FAIL() << "系统状态获取异常: " << e.what();
    }
}

/**
 * @brief 测试Mock HAL工厂创建
 */
TEST_F(SimpleCompilationTest, TestMockHalFactoryCreation) {
    auto mock_factory = std::make_shared<hal::MockHalFactory>();
    EXPECT_NE(mock_factory, nullptr) << "Mock HAL工厂创建失败";
    
    // 测试创建各种Mock设备
    auto gnss_receiver = mock_factory->CreateGnssReceiver();
    auto pps_input = mock_factory->CreatePpsInput();
    auto atomic_clock = mock_factory->CreateAtomicClock();
    auto frequency_input = mock_factory->CreateFrequencyInput();
    auto rtc = mock_factory->CreateRtc();
    auto network_interface = mock_factory->CreateNetworkInterface();
    
    EXPECT_NE(gnss_receiver, nullptr) << "Mock GNSS接收机创建失败";
    EXPECT_NE(pps_input, nullptr) << "Mock PPS输入创建失败";
    EXPECT_NE(atomic_clock, nullptr) << "Mock 原子钟创建失败";
    EXPECT_NE(frequency_input, nullptr) << "Mock 频率输入创建失败";
    EXPECT_NE(rtc, nullptr) << "Mock RTC创建失败";
    EXPECT_NE(network_interface, nullptr) << "Mock 网络接口创建失败";
    
    std::cout << "✓ Mock HAL工厂创建测试通过" << std::endl;
}

/**
 * @brief 测试日志系统
 */
TEST_F(SimpleCompilationTest, TestLoggingSystem) {
    auto& logger = core::Logger::GetInstance();
    
    // 测试不同级别的日志
    LOG_INFO(core::LogComponent::SYSTEM, "这是一条信息日志");
    LOG_WARNING(core::LogComponent::SYSTEM, "这是一条警告日志");
    LOG_ERROR(core::LogComponent::SYSTEM, "这是一条错误日志");
    
    std::cout << "✓ 日志系统测试通过" << std::endl;
}

/**
 * @brief 测试配置系统
 */
TEST_F(SimpleCompilationTest, TestConfigurationSystem) {
    // 测试配置系统基本功能
    auto config = config_manager_->GetConfig();
    EXPECT_FALSE(config.priorities.priorities.empty()) << "配置应包含时间源优先级";

    // 测试默认配置加载
    EXPECT_TRUE(config.priorities.auto_failover) << "默认配置应启用自动故障切换";
    
    std::cout << "✓ 配置系统测试通过" << std::endl;
    std::cout << "  - 时间源数量: " << config.priorities.priorities.size() << std::endl;
}

} // namespace test
} // namespace timing_server

/**
 * @brief 主函数
 */
int main(int argc, char** argv) {
    std::cout << "开始简化编译测试..." << std::endl;
    
    ::testing::InitGoogleTest(&argc, argv);
    
    int result = RUN_ALL_TESTS();
    
    std::cout << "简化编译测试完成，结果: " << (result == 0 ? "成功" : "失败") << std::endl;
    
    return result;
}
