#include <gtest/gtest.h>
#include "core/timing_engine.h"
#include "hal/hal_factory.h"
#include <thread>
#include <chrono>

using namespace timing_server::core;
using namespace timing_server::hal;

class TimingEngineTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化HAL工厂
        auto& hal_manager = HalFactoryManager::GetInstance();
        ASSERT_TRUE(hal_manager.Initialize());
        
        // 创建默认配置
        config_.priorities.priorities[TimeSource::GNSS] = 1;
        config_.priorities.priorities[TimeSource::RUBIDIUM] = 2;
        config_.priorities.priorities[TimeSource::RTC] = 5;
        config_.priorities.auto_failover = true;
        config_.priorities.failover_delay_ms = 1000;
        
        config_.discipline.convergence_threshold_ns = 50.0;
        config_.discipline.convergence_time_s = 30;
        config_.discipline.phase_gain = 0.1;
        config_.discipline.frequency_gain = 0.01;
        config_.discipline.measurement_interval_ms = 100;
        
        config_.holdover.max_holdover_hours = 24;
        config_.holdover.frequency_drift_limit_ppm = 1e-9;
        config_.holdover.learning_duration_hours = 72;
        config_.holdover.enable_temperature_compensation = true;
        
        config_.alarms.phase_offset_warning_ns = 100.0;
        config_.alarms.phase_offset_critical_ns = 500.0;
        config_.alarms.frequency_offset_warning_ppm = 0.01;
        config_.alarms.frequency_offset_critical_ppm = 0.1;
    }
    
    void TearDown() override {
        auto& hal_manager = HalFactoryManager::GetInstance();
        hal_manager.Cleanup();
    }
    
    TimingConfig config_;
};

TEST_F(TimingEngineTest, ConstructorAndBasicProperties) {
    TimingEngine engine(config_);
    
    // 检查初始状态
    EXPECT_EQ(engine.GetCurrentState(), ClockState::FREE_RUN);
    EXPECT_EQ(engine.GetActiveTimeSource(), TimeSource::RTC); // 默认使用RTC
    
    // 检查配置
    const auto& retrieved_config = engine.GetConfiguration();
    EXPECT_EQ(retrieved_config.priorities.priorities.at(TimeSource::GNSS), 1);
    EXPECT_EQ(retrieved_config.discipline.convergence_threshold_ns, 50.0);
}

TEST_F(TimingEngineTest, StartAndStop) {
    TimingEngine engine(config_);
    
    // 测试启动
    EXPECT_TRUE(engine.Start());
    
    // 等待一段时间让系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 检查系统状态
    SystemStatus status = engine.GetSystemStatus();
    EXPECT_GT(status.uptime_seconds, 0);
    EXPECT_EQ(status.current_state, ClockState::FREE_RUN);
    EXPECT_FALSE(status.sources.empty());
    
    // 测试停止
    EXPECT_TRUE(engine.Stop());
    
    // 重复启动应该成功
    EXPECT_TRUE(engine.Start());
    EXPECT_TRUE(engine.Stop());
}

TEST_F(TimingEngineTest, TimeSourceManagement) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 等待时间源管理器初始化
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 获取所有时间源信息
    auto sources = engine.GetAllTimeSourceInfo();
    EXPECT_FALSE(sources.empty());
    
    // 检查特定时间源
    TimeSourceInfo gnss_info = engine.GetTimeSourceInfo(TimeSource::GNSS);
    EXPECT_EQ(gnss_info.type, TimeSource::GNSS);
    
    TimeSourceInfo rtc_info = engine.GetTimeSourceInfo(TimeSource::RTC);
    EXPECT_EQ(rtc_info.type, TimeSource::RTC);
    
    engine.Stop();
}

TEST_F(TimingEngineTest, ConfigurationUpdate) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 修改配置
    TimeSourcePriority new_priorities;
    new_priorities.priorities[TimeSource::RUBIDIUM] = 1; // 提高铷钟优先级
    new_priorities.priorities[TimeSource::GNSS] = 2;
    new_priorities.priorities[TimeSource::RTC] = 5;
    new_priorities.auto_failover = true;
    new_priorities.failover_delay_ms = 500;
    
    // 应用新配置
    EXPECT_TRUE(engine.ConfigureTimeSource(new_priorities));
    
    // 验证配置已更新
    const auto& updated_config = engine.GetConfiguration();
    EXPECT_EQ(updated_config.priorities.priorities.at(TimeSource::RUBIDIUM), 1);
    EXPECT_EQ(updated_config.priorities.failover_delay_ms, 500);
    
    engine.Stop();
}

TEST_F(TimingEngineTest, SystemHealthCheck) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    
    // 检查系统健康状况
    SystemHealth health = engine.CheckSystemHealth();
    EXPECT_NE(health, SystemHealth::CRITICAL); // 不应该是严重状态
    
    // 获取系统状态
    SystemStatus status = engine.GetSystemStatus();
    EXPECT_GT(status.uptime_seconds, 0);
    EXPECT_GE(status.memory_usage_mb, 0);
    EXPECT_GE(status.cpu_usage_percent, 0.0);
    EXPECT_FALSE(status.version.empty());
    EXPECT_FALSE(status.platform.empty());
    
    engine.Stop();
}

TEST_F(TimingEngineTest, PerformanceMetrics) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 等待一段时间收集指标
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 获取性能指标
    PerformanceMetrics metrics = engine.GetPerformanceMetrics();
    EXPECT_GE(metrics.current_accuracy_ns, 0.0);
    EXPECT_GE(metrics.average_response_time_ms, 0.0);
    
    engine.Stop();
}

TEST_F(TimingEngineTest, EventTriggering) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 触发GNSS信号获取事件
    EXPECT_TRUE(engine.TriggerEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    
    // 等待状态机处理事件
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 检查状态是否可能发生变化（取决于具体实现）
    ClockState current_state = engine.GetCurrentState();
    EXPECT_TRUE(current_state == ClockState::FREE_RUN || 
                current_state == ClockState::DISCIPLINING);
    
    engine.Stop();
}

TEST_F(TimingEngineTest, TimeSourceSwitching) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 等待系统稳定
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    TimeSource original_source = engine.GetActiveTimeSource();
    
    // 尝试强制切换到不同的时间源
    TimeSource target_source = (original_source == TimeSource::RTC) ? 
                              TimeSource::RUBIDIUM : TimeSource::RTC;
    
    // 注意：切换可能失败如果目标时间源不可用
    bool switch_result = engine.ForceTimeSourceSwitch(target_source);
    
    if (switch_result) {
        EXPECT_EQ(engine.GetActiveTimeSource(), target_source);
    } else {
        // 如果切换失败，应该保持原来的时间源
        EXPECT_EQ(engine.GetActiveTimeSource(), original_source);
    }
    
    engine.Stop();
}

TEST_F(TimingEngineTest, UptimeTracking) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 记录初始运行时间
    uint64_t initial_uptime = engine.GetUptime();
    
    // 等待一段时间
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // 检查运行时间是否增加
    uint64_t current_uptime = engine.GetUptime();
    EXPECT_GT(current_uptime, initial_uptime);
    EXPECT_GE(current_uptime, 1); // 至少1秒
    
    engine.Stop();
}

// 性能测试：验证控制循环不会消耗过多CPU
TEST_F(TimingEngineTest, ControlLoopPerformance) {
    TimingEngine engine(config_);
    ASSERT_TRUE(engine.Start());
    
    // 运行较长时间
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 检查CPU使用率（这是一个简化的检查）
    SystemStatus status = engine.GetSystemStatus();
    EXPECT_LT(status.cpu_usage_percent, 50.0); // CPU使用率应该低于50%
    
    engine.Stop();
}