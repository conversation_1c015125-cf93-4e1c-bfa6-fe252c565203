/**
 * @file test_minimal_compilation.cpp
 * @brief 最小化编译测试 - 验证核心组件是否能正确编译和链接
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <iostream>

// 核心组件头文件
#include "core/types.h"
#include "core/logger.h"

// HAL接口头文件
#include "hal/interfaces.h"

// Mock类定义
#include "mock/mock_hal_interfaces.h"

using namespace timing_server;

namespace timing_server {
namespace test {

/**
 * @brief 最小化编译测试类
 * 测试核心组件的基本编译和链接
 */
class MinimalCompilationTest : public ::testing::Test {
protected:
    void SetUp() override {
        std::cout << "设置最小化编译测试环境..." << std::endl;
    }
    
    void TearDown() override {
        std::cout << "清理最小化编译测试环境..." << std::endl;
    }
};

/**
 * @brief 测试基本类型定义
 */
TEST_F(MinimalCompilationTest, TestBasicTypes) {
    // 测试枚举类型
    core::ClockState state = core::ClockState::FREE_RUN;
    EXPECT_EQ(state, core::ClockState::FREE_RUN);
    
    core::TimeSource source = core::TimeSource::GNSS;
    EXPECT_EQ(source, core::TimeSource::GNSS);
    
    core::SystemHealth health = core::SystemHealth::HEALTHY;
    EXPECT_EQ(health, core::SystemHealth::HEALTHY);
    
    std::cout << "✓ 基本类型定义测试通过" << std::endl;
}

/**
 * @brief 测试日志系统基本功能
 */
TEST_F(MinimalCompilationTest, TestLoggingBasics) {
    // 测试日志系统单例获取
    auto& logger = core::Logger::GetInstance();
    
    // 测试基本日志输出（不会崩溃）
    try {
        LOG_INFO(core::LogComponent::SYSTEM, "这是一条测试日志");
        std::cout << "✓ 日志系统基本功能测试通过" << std::endl;
    } catch (const std::exception& e) {
        FAIL() << "日志系统异常: " << e.what();
    }
}

/**
 * @brief 测试Mock HAL接口创建
 */
TEST_F(MinimalCompilationTest, TestMockHalInterfaces) {
    // 测试创建各种Mock接口
    auto mock_gnss = std::make_shared<hal::MockGnssReceiver>();
    auto mock_pps = std::make_shared<hal::MockPpsInput>();
    auto mock_clock = std::make_shared<hal::MockAtomicClock>();
    auto mock_freq = std::make_shared<hal::MockFrequencyInput>();
    auto mock_rtc = std::make_shared<hal::MockHighPrecisionRtc>();
    auto mock_net = std::make_shared<hal::MockNetworkInterface>();
    
    EXPECT_NE(mock_gnss, nullptr) << "Mock GNSS接收机创建失败";
    EXPECT_NE(mock_pps, nullptr) << "Mock PPS输入创建失败";
    EXPECT_NE(mock_clock, nullptr) << "Mock 原子钟创建失败";
    EXPECT_NE(mock_freq, nullptr) << "Mock 频率输入创建失败";
    EXPECT_NE(mock_rtc, nullptr) << "Mock RTC创建失败";
    EXPECT_NE(mock_net, nullptr) << "Mock 网络接口创建失败";
    
    std::cout << "✓ Mock HAL接口创建测试通过" << std::endl;
}

/**
 * @brief 测试字符串转换函数
 */
TEST_F(MinimalCompilationTest, TestStringConversions) {
    // 测试时钟状态转换
    std::string state_str = core::ClockStateToString(core::ClockState::LOCKED);
    EXPECT_EQ(state_str, "LOCKED");
    
    state_str = core::ClockStateToString(core::ClockState::FREE_RUN);
    EXPECT_EQ(state_str, "FREE_RUN");
    
    state_str = core::ClockStateToString(core::ClockState::DISCIPLINING);
    EXPECT_EQ(state_str, "DISCIPLINING");
    
    state_str = core::ClockStateToString(core::ClockState::HOLDOVER);
    EXPECT_EQ(state_str, "HOLDOVER");
    
    std::cout << "✓ 字符串转换函数测试通过" << std::endl;
}

/**
 * @brief 测试基本结构体
 */
TEST_F(MinimalCompilationTest, TestBasicStructures) {
    // 测试系统状态结构
    core::SystemStatus status;
    status.current_state = core::ClockState::LOCKED;
    status.active_source = core::TimeSource::GNSS;
    status.health = core::SystemHealth::HEALTHY;
    status.uptime_seconds = 3600;
    status.cpu_usage_percent = 2.5;
    status.memory_usage_mb = 64;
    status.version = "1.0.0-test";
    status.platform = "test-platform";
    
    EXPECT_EQ(status.current_state, core::ClockState::LOCKED);
    EXPECT_EQ(status.active_source, core::TimeSource::GNSS);
    EXPECT_EQ(status.health, core::SystemHealth::HEALTHY);
    EXPECT_EQ(status.uptime_seconds, 3600);
    EXPECT_EQ(status.version, "1.0.0-test");
    
    std::cout << "✓ 基本结构体测试通过" << std::endl;
}

} // namespace test
} // namespace timing_server

/**
 * @brief 主函数
 */
int main(int argc, char** argv) {
    std::cout << "开始最小化编译测试..." << std::endl;
    
    ::testing::InitGoogleTest(&argc, argv);
    
    int result = RUN_ALL_TESTS();
    
    std::cout << "最小化编译测试完成，结果: " << (result == 0 ? "成功" : "失败") << std::endl;
    
    return result;
}
