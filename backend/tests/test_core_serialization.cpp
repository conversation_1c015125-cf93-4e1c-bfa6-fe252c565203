#include <gtest/gtest.h>
#include "core/serialization.h"
#include "core/types.h"
#include <thread>
#include <chrono>

using namespace timing_server::core;

class SerializationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试数据
        test_quality_.accuracy_ns = 50.0;
        test_quality_.stability_ppm = 1e-12;
        test_quality_.confidence = 95;
        test_quality_.is_traceable = true;
        test_quality_.reference = "GPS";
        
        test_time_data_.timestamp_ns = 1640995200000000000ULL; // 2022-01-01 00:00:00 UTC
        test_time_data_.frequency_offset_ppm = 0.001;
        test_time_data_.phase_offset_ns = 12.5;
        test_time_data_.quality = test_quality_;
        test_time_data_.source = TimeSource::GNSS;
        test_time_data_.measurement_time_ns = test_time_data_.timestamp_ns;
        test_time_data_.is_valid = true;
        
        test_source_info_.type = TimeSource::GNSS;
        test_source_info_.status = TimeSourceStatus::ACTIVE;
        test_source_info_.quality = test_quality_;
        test_source_info_.priority = 1;
        test_source_info_.last_update_ns = test_time_data_.timestamp_ns;
        test_source_info_.properties["satellites"] = "12";
        test_source_info_.properties["signal_strength"] = "-142";
        
        test_system_status_.current_state = ClockState::LOCKED;
        test_system_status_.active_source = TimeSource::GNSS;
        test_system_status_.sources.push_back(test_source_info_);
        test_system_status_.health = SystemHealth::HEALTHY;
        test_system_status_.uptime_seconds = 86400;
        test_system_status_.cpu_usage_percent = 2.5;
        test_system_status_.memory_usage_mb = 45;
        test_system_status_.version = "1.0.0";
        test_system_status_.platform = "linux-x86_64";
    }
    
    TimeQuality test_quality_;
    TimeData test_time_data_;
    TimeSourceInfo test_source_info_;
    SystemStatus test_system_status_;
};

// JSON序列化测试

TEST_F(SerializationTest, TimeQualityJsonSerialization) {
    // 序列化
    std::string json = JsonSerializer::SerializeTimeQuality(test_quality_);
    EXPECT_FALSE(json.empty());
    EXPECT_NE(json.find("\"accuracy_ns\":50.000"), std::string::npos);
    EXPECT_NE(json.find("\"stability_ppm\":1.000000e-12"), std::string::npos);
    EXPECT_NE(json.find("\"confidence\":95"), std::string::npos);
    EXPECT_NE(json.find("\"is_traceable\":true"), std::string::npos);
    EXPECT_NE(json.find("\"reference\":\"GPS\""), std::string::npos);
    
    // 反序列化
    TimeQuality deserialized = JsonSerializer::DeserializeTimeQuality(json);
    EXPECT_DOUBLE_EQ(deserialized.accuracy_ns, test_quality_.accuracy_ns);
    EXPECT_DOUBLE_EQ(deserialized.stability_ppm, test_quality_.stability_ppm);
    EXPECT_EQ(deserialized.confidence, test_quality_.confidence);
    EXPECT_EQ(deserialized.is_traceable, test_quality_.is_traceable);
    EXPECT_EQ(deserialized.reference, test_quality_.reference);
}

TEST_F(SerializationTest, TimeDataJsonSerialization) {
    // 序列化
    std::string json = JsonSerializer::SerializeTimeData(test_time_data_);
    EXPECT_FALSE(json.empty());
    EXPECT_NE(json.find("\"timestamp_ns\":1640995200000000000"), std::string::npos);
    EXPECT_NE(json.find("\"frequency_offset_ppm\":0.001000"), std::string::npos);
    EXPECT_NE(json.find("\"phase_offset_ns\":12.500"), std::string::npos);
    EXPECT_NE(json.find("\"source\":\"GNSS\""), std::string::npos);
    EXPECT_NE(json.find("\"is_valid\":true"), std::string::npos);
    EXPECT_NE(json.find("\"timestamp_iso\":\"2022-01-01T00:00:00.000000000Z\""), std::string::npos);
    
    // 反序列化
    TimeData deserialized = JsonSerializer::DeserializeTimeData(json);
    EXPECT_EQ(deserialized.timestamp_ns, test_time_data_.timestamp_ns);
    EXPECT_DOUBLE_EQ(deserialized.frequency_offset_ppm, test_time_data_.frequency_offset_ppm);
    EXPECT_DOUBLE_EQ(deserialized.phase_offset_ns, test_time_data_.phase_offset_ns);
    EXPECT_EQ(deserialized.source, test_time_data_.source);
    EXPECT_EQ(deserialized.is_valid, test_time_data_.is_valid);
}

TEST_F(SerializationTest, TimeSourceInfoJsonSerialization) {
    // 序列化
    std::string json = JsonSerializer::SerializeTimeSourceInfo(test_source_info_);
    EXPECT_FALSE(json.empty());
    EXPECT_NE(json.find("\"type\":\"GNSS\""), std::string::npos);
    EXPECT_NE(json.find("\"status\":\"ACTIVE\""), std::string::npos);
    EXPECT_NE(json.find("\"priority\":1"), std::string::npos);
    EXPECT_NE(json.find("\"satellites\":\"12\""), std::string::npos);
    EXPECT_NE(json.find("\"signal_strength\":\"-142\""), std::string::npos);
    
    // 反序列化
    TimeSourceInfo deserialized = JsonSerializer::DeserializeTimeSourceInfo(json);
    EXPECT_EQ(deserialized.type, test_source_info_.type);
    EXPECT_EQ(deserialized.status, test_source_info_.status);
    EXPECT_EQ(deserialized.priority, test_source_info_.priority);
    EXPECT_EQ(deserialized.last_update_ns, test_source_info_.last_update_ns);
    EXPECT_EQ(deserialized.properties.at("satellites"), "12");
    EXPECT_EQ(deserialized.properties.at("signal_strength"), "-142");
}

TEST_F(SerializationTest, SystemStatusJsonSerialization) {
    // 序列化
    std::string json = JsonSerializer::SerializeSystemStatus(test_system_status_);
    EXPECT_FALSE(json.empty());
    EXPECT_NE(json.find("\"current_state\":\"LOCKED\""), std::string::npos);
    EXPECT_NE(json.find("\"active_source\":\"GNSS\""), std::string::npos);
    EXPECT_NE(json.find("\"health\":\"HEALTHY\""), std::string::npos);
    EXPECT_NE(json.find("\"uptime_seconds\":86400"), std::string::npos);
    EXPECT_NE(json.find("\"cpu_usage_percent\":2.50"), std::string::npos);
    EXPECT_NE(json.find("\"memory_usage_mb\":45"), std::string::npos);
    EXPECT_NE(json.find("\"version\":\"1.0.0\""), std::string::npos);
    EXPECT_NE(json.find("\"platform\":\"linux-x86_64\""), std::string::npos);
    
    // 反序列化（部分测试，因为数组解析较复杂）
    SystemStatus deserialized = JsonSerializer::DeserializeSystemStatus(json);
    EXPECT_EQ(deserialized.active_source, test_system_status_.active_source);
    EXPECT_EQ(deserialized.health, test_system_status_.health);
    EXPECT_EQ(deserialized.uptime_seconds, test_system_status_.uptime_seconds);
    EXPECT_DOUBLE_EQ(deserialized.cpu_usage_percent, test_system_status_.cpu_usage_percent);
    EXPECT_EQ(deserialized.memory_usage_mb, test_system_status_.memory_usage_mb);
    EXPECT_EQ(deserialized.version, test_system_status_.version);
    EXPECT_EQ(deserialized.platform, test_system_status_.platform);
}

// 二进制序列化测试

TEST_F(SerializationTest, TimeDataBinarySerialization) {
    uint8_t buffer[1024];
    
    // 序列化
    size_t written = BinarySerializer::SerializeTimeData(test_time_data_, buffer, sizeof(buffer));
    EXPECT_GT(written, 0);
    EXPECT_LT(written, sizeof(buffer));
    
    // 反序列化
    TimeData deserialized;
    size_t read = BinarySerializer::DeserializeTimeData(buffer, written, deserialized);
    EXPECT_EQ(read, written);
    
    // 验证数据
    EXPECT_EQ(deserialized.timestamp_ns, test_time_data_.timestamp_ns);
    EXPECT_DOUBLE_EQ(deserialized.frequency_offset_ppm, test_time_data_.frequency_offset_ppm);
    EXPECT_DOUBLE_EQ(deserialized.phase_offset_ns, test_time_data_.phase_offset_ns);
    EXPECT_EQ(deserialized.source, test_time_data_.source);
    EXPECT_EQ(deserialized.is_valid, test_time_data_.is_valid);
    EXPECT_DOUBLE_EQ(deserialized.quality.accuracy_ns, test_time_data_.quality.accuracy_ns);
    EXPECT_EQ(deserialized.quality.reference, test_time_data_.quality.reference);
}

TEST_F(SerializationTest, SystemStatusBinarySerialization) {
    uint8_t buffer[2048];
    
    // 序列化
    size_t written = BinarySerializer::SerializeSystemStatus(test_system_status_, buffer, sizeof(buffer));
    EXPECT_GT(written, 0);
    EXPECT_LT(written, sizeof(buffer));
    
    // 反序列化
    SystemStatus deserialized;
    size_t read = BinarySerializer::DeserializeSystemStatus(buffer, written, deserialized);
    EXPECT_EQ(read, written);
    
    // 验证数据
    EXPECT_EQ(deserialized.current_state, test_system_status_.current_state);
    EXPECT_EQ(deserialized.active_source, test_system_status_.active_source);
    EXPECT_EQ(deserialized.health, test_system_status_.health);
    EXPECT_EQ(deserialized.uptime_seconds, test_system_status_.uptime_seconds);
    EXPECT_DOUBLE_EQ(deserialized.cpu_usage_percent, test_system_status_.cpu_usage_percent);
    EXPECT_EQ(deserialized.memory_usage_mb, test_system_status_.memory_usage_mb);
    EXPECT_EQ(deserialized.version, test_system_status_.version);
    EXPECT_EQ(deserialized.platform, test_system_status_.platform);
    EXPECT_EQ(deserialized.sources.size(), test_system_status_.sources.size());
}

// 边界条件测试

TEST_F(SerializationTest, JsonStringEscaping) {
    TimeQuality quality;
    quality.reference = "Test\"String\\With\nSpecial\tChars";
    
    std::string json = JsonSerializer::SerializeTimeQuality(quality);
    EXPECT_NE(json.find("\\\""), std::string::npos); // 转义的双引号
    EXPECT_NE(json.find("\\\\"), std::string::npos); // 转义的反斜杠
    EXPECT_NE(json.find("\\n"), std::string::npos);  // 转义的换行符
    EXPECT_NE(json.find("\\t"), std::string::npos);  // 转义的制表符
    
    TimeQuality deserialized = JsonSerializer::DeserializeTimeQuality(json);
    EXPECT_EQ(deserialized.reference, quality.reference);
}

TEST_F(SerializationTest, BinarySerializationBufferTooSmall) {
    uint8_t small_buffer[10]; // 故意设置很小的缓冲区
    
    size_t written = BinarySerializer::SerializeTimeData(test_time_data_, small_buffer, sizeof(small_buffer));
    EXPECT_EQ(written, 0); // 应该返回0表示失败
}

TEST_F(SerializationTest, EmptyStringHandling) {
    TimeQuality quality;
    quality.reference = ""; // 空字符串
    
    std::string json = JsonSerializer::SerializeTimeQuality(quality);
    EXPECT_NE(json.find("\"reference\":\"\""), std::string::npos);
    
    TimeQuality deserialized = JsonSerializer::DeserializeTimeQuality(json);
    EXPECT_EQ(deserialized.reference, "");
}

// 辅助函数测试

TEST_F(SerializationTest, UtilityFunctions) {
    // 测试时间戳转换
    uint64_t timestamp = 1640995200000000000ULL; // 2022-01-01 00:00:00 UTC
    std::string iso_string = TimestampToIsoString(timestamp);
    EXPECT_EQ(iso_string, "2022-01-01T00:00:00.000000000Z");
    
    // 测试枚举转换
    EXPECT_EQ(TimeSourceToString(TimeSource::GNSS), "GNSS");
    EXPECT_EQ(StringToTimeSource("GNSS"), TimeSource::GNSS);
    EXPECT_EQ(StringToTimeSource("INVALID"), TimeSource::SYSTEM_CLOCK); // 默认值
    
    EXPECT_EQ(ClockStateToString(ClockState::LOCKED), "LOCKED");
    
    EXPECT_EQ(TimeSourceStatusToString(TimeSourceStatus::ACTIVE), "ACTIVE");
    EXPECT_EQ(StringToTimeSourceStatus("ACTIVE"), TimeSourceStatus::ACTIVE);
    
    EXPECT_EQ(SystemHealthToString(SystemHealth::HEALTHY), "HEALTHY");
    EXPECT_EQ(StringToSystemHealth("HEALTHY"), SystemHealth::HEALTHY);
}

TEST_F(SerializationTest, CurrentTimestamp) {
    uint64_t timestamp1 = GetCurrentTimestampNs();
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    uint64_t timestamp2 = GetCurrentTimestampNs();
    
    EXPECT_GT(timestamp2, timestamp1);
    EXPECT_LT(timestamp2 - timestamp1, 10000000ULL); // 应该小于10ms
}