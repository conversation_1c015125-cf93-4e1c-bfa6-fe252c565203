#include <gtest/gtest.h>
#include "core/error_handler.h"
#include "core/logger.h"
#include <thread>
#include <chrono>

using namespace timing_server::core;

class ErrorHandlerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志系统
        Logger::GetInstance().Initialize();
        Logger::GetInstance().SetLogLevel(LogLevel::DEBUG);
        Logger::GetInstance().AddOutput(std::make_unique<ConsoleLogOutput>(false));
        
        // 初始化错误处理器
        ErrorHandler::GetInstance().Initialize();
    }
    
    void TearDown() override {
        ErrorHandler::GetInstance().Shutdown();
        Logger::GetInstance().Shutdown();
    }
};

TEST_F(ErrorHandlerTest, ReportAndResolveError) {
    auto& error_handler = ErrorHandler::GetInstance();
    
    // 报告一个错误
    uint64_t error_id = error_handler.ReportError(
        ErrorType::GNSS_SIGNAL_LOST,
        ErrorSeverity::HIGH,
        LogComponent::HAL_GNSS,
        "GNSS信号丢失",
        "卫星数量不足"
    );
    
    EXPECT_GT(error_id, 0);
    
    // 获取错误信息
    auto error_info = error_handler.GetError(error_id);
    ASSERT_NE(error_info, nullptr);
    EXPECT_EQ(error_info->type, ErrorType::GNSS_SIGNAL_LOST);
    EXPECT_EQ(error_info->severity, ErrorSeverity::HIGH);
    EXPECT_EQ(error_info->component, LogComponent::HAL_GNSS);
    EXPECT_EQ(error_info->message, "GNSS信号丢失");
    EXPECT_EQ(error_info->status, ErrorStatus::NEW);
    
    // 确认错误
    bool acknowledged = error_handler.AcknowledgeError(error_id, "test_user");
    EXPECT_TRUE(acknowledged);
    
    error_info = error_handler.GetError(error_id);
    EXPECT_EQ(error_info->status, ErrorStatus::ACKNOWLEDGED);
    
    // 解决错误
    bool resolved = error_handler.ResolveError(error_id, "手动重启GNSS模块");
    EXPECT_TRUE(resolved);
    
    // 错误应该从活跃列表中移除
    auto active_errors = error_handler.GetActiveErrors();
    bool found = false;
    for (const auto& active_error : active_errors) {
        if (active_error->error_id == error_id) {
            found = true;
            break;
        }
    }
    EXPECT_FALSE(found);
}

TEST_F(ErrorHandlerTest, AutoRecovery) {
    auto& error_handler = ErrorHandler::GetInstance();
    
    // 启用自动恢复
    error_handler.SetAutoRecoveryEnabled(true);
    
    // 报告一个支持自动恢复的错误
    uint64_t error_id = error_handler.ReportError(
        ErrorType::NETWORK_TIMEOUT,
        ErrorSeverity::MEDIUM,
        LogComponent::HAL_NETWORK,
        "网络连接超时"
    );
    
    // 等待自动恢复处理
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 检查错误是否被处理
    auto error_info = error_handler.GetError(error_id);
    // 注意：由于是异步处理，可能需要更长的等待时间或更复杂的同步机制
}

TEST_F(ErrorHandlerTest, ErrorStatistics) {
    auto& error_handler = ErrorHandler::GetInstance();
    
    // 报告多个不同类型的错误
    error_handler.ReportError(ErrorType::GNSS_SIGNAL_LOST, ErrorSeverity::HIGH, 
                             LogComponent::HAL_GNSS, "错误1");
    error_handler.ReportError(ErrorType::GNSS_SIGNAL_LOST, ErrorSeverity::MEDIUM, 
                             LogComponent::HAL_GNSS, "错误2");
    error_handler.ReportError(ErrorType::NETWORK_TIMEOUT, ErrorSeverity::LOW, 
                             LogComponent::HAL_NETWORK, "错误3");
    
    auto stats = error_handler.GetErrorStatistics();
    
    EXPECT_EQ(stats["total_errors"], 3);
    EXPECT_EQ(stats["GNSS_SIGNAL_LOST"], 2);
    EXPECT_EQ(stats["NETWORK_TIMEOUT"], 1);
}

TEST_F(ErrorHandlerTest, HealthMonitor) {
    auto& error_handler = ErrorHandler::GetInstance();
    auto& health_monitor = error_handler.GetHealthMonitor();
    
    // 获取系统健康状态
    SystemHealth health = health_monitor.GetSystemHealth();
    EXPECT_NE(health, SystemHealth::CRITICAL); // 测试环境应该不是严重状态
    
    // 获取健康报告
    auto health_report = health_monitor.GetHealthReport();
    EXPECT_FALSE(health_report.empty());
    
    // 检查是否包含基本的健康指标
    EXPECT_TRUE(health_report.find("cpu_usage") != health_report.end());
    EXPECT_TRUE(health_report.find("memory_usage") != health_report.end());
}

TEST_F(ErrorHandlerTest, LoggerIntegration) {
    auto& logger = Logger::GetInstance();
    
    // 测试不同级别的日志记录
    LOG_INFO(LogComponent::SYSTEM, "测试信息日志");
    LOG_WARNING(LogComponent::TIMING_ENGINE, "测试警告日志");
    LOG_ERROR(LogComponent::HAL_GNSS, "测试错误日志");
    
    // 测试带上下文的日志记录
    std::map<std::string, std::string> context = {
        {"error_code", "E001"},
        {"component", "GNSS"},
        {"retry_count", "3"}
    };
    
    LOG_WITH_CONTEXT(LogLevel::ERROR, LogComponent::HAL_GNSS, 
                     "GNSS初始化失败", context);
    
    // 测试日志搜索功能
    LogFilter filter;
    filter.min_level = LogLevel::WARNING;
    filter.components = {LogComponent::TIMING_ENGINE, LogComponent::HAL_GNSS};
    filter.max_results = 10;
    
    auto search_results = logger.SearchLogs(filter);
    EXPECT_GE(search_results.size(), 2); // 至少应该有警告和错误日志
    
    // 测试获取最近日志
    auto recent_logs = logger.GetRecentLogs(5);
    EXPECT_LE(recent_logs.size(), 5);
}

TEST_F(ErrorHandlerTest, LogRotation) {
    // 创建一个临时日志文件进行轮转测试
    std::string temp_log_file = "/tmp/test_rotation.log";
    
    LogRotationConfig rotation_config;
    rotation_config.max_file_size_mb = 1; // 1MB
    rotation_config.max_files = 3;
    rotation_config.enable_compression = false; // 简化测试
    rotation_config.rotation_check_interval_s = 1;
    
    auto file_output = std::make_unique<FileLogOutput>(temp_log_file, rotation_config);
    
    // 写入大量日志以触发轮转
    LogEntry entry(LogLevel::INFO, LogComponent::SYSTEM, "测试日志轮转");
    for (int i = 0; i < 1000; ++i) {
        entry.message = "测试日志轮转 - 条目 " + std::to_string(i) + 
                       " - 这是一条比较长的日志消息，用于快速填满日志文件以测试轮转功能";
        file_output->WriteLog(entry);
    }
    
    // 等待轮转检查
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 检查是否生成了轮转文件
    // 注意：这个测试可能需要根据实际文件系统情况调整
    
    file_output.reset(); // 关闭文件输出
    
    // 清理测试文件
    std::remove(temp_log_file.c_str());
    std::remove((temp_log_file + ".1").c_str());
    std::remove((temp_log_file + ".2").c_str());
}

// 测试工具函数
TEST_F(ErrorHandlerTest, UtilityFunctions) {
    // 测试错误类型转换
    EXPECT_EQ(ErrorTypeToString(ErrorType::GNSS_SIGNAL_LOST), "GNSS_SIGNAL_LOST");
    EXPECT_EQ(StringToErrorType("GNSS_SIGNAL_LOST"), ErrorType::GNSS_SIGNAL_LOST);
    
    // 测试错误严重程度转换
    EXPECT_EQ(ErrorSeverityToString(ErrorSeverity::HIGH), "HIGH");
    EXPECT_EQ(StringToErrorSeverity("HIGH"), ErrorSeverity::HIGH);
    
    // 测试日志级别转换
    EXPECT_EQ(LogLevelToString(LogLevel::ERROR), "ERROR");
    EXPECT_EQ(StringToLogLevel("ERROR"), LogLevel::ERROR);
    
    // 测试日志组件转换
    EXPECT_EQ(LogComponentToString(LogComponent::HAL_GNSS), "HAL_GNSS");
    EXPECT_EQ(StringToLogComponent("HAL_GNSS"), LogComponent::HAL_GNSS);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}