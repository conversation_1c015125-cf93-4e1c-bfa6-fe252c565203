#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/precision_monitor.h"
#include "core/alarm_system.h"
#include "core/notification_system.h"
#include "core/timing_engine.h"
#include <thread>
#include <chrono>
#include <memory>

using namespace timing_server::core;
using namespace testing;

// Mock TimingEngine for testing
class MockTimingEngine : public ITimingEngine {
public:
    MOCK_METHOD(bool, Start, (), (override));
    MOCK_METHOD(bool, Stop, (), (override));
    MOCK_METHOD(SystemStatus, GetSystemStatus, (), (override));
    MOCK_METHOD(TimeSourceInfo, GetTimeSourceInfo, (TimeSource source), (override));
    MOCK_METHOD(bool, SetTimeSourcePriority, (const TimeSourcePriority& priorities), (override));
    MOCK_METHOD(bool, ConfigureDisciplining, (const DiscipliningParameters& params), (override));
    MOCK_METHOD(bool, ConfigureHoldover, (const HoldoverParameters& params), (override));
    MOCK_METHOD(TimeData, GetCurrentTimeData, (), (override));
    MOCK_METHOD(std::vector<TimeData>, GetHistoricalTimeData, (uint64_t start_time, uint64_t end_time), (override));
};

class PrecisionMonitoringIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建Mock授时引擎
        mock_timing_engine_ = std::make_shared<MockTimingEngine>();
        
        // 设置默认的Mock行为
        SetupDefaultMockBehavior();
        
        // 创建告警阈值配置
        alarm_thresholds_.phase_offset_warning_ns = 100.0;
        alarm_thresholds_.phase_offset_critical_ns = 500.0;
        alarm_thresholds_.frequency_offset_warning_ppm = 0.01;
        alarm_thresholds_.frequency_offset_critical_ppm = 0.1;
        alarm_thresholds_.gnss_satellites_warning = 6;
        alarm_thresholds_.gnss_satellites_critical = 4;
        alarm_thresholds_.gnss_snr_warning_db = -145.0;
        alarm_thresholds_.gnss_snr_critical_db = -150.0;
        alarm_thresholds_.cpu_usage_warning = 80.0;
        alarm_thresholds_.memory_usage_warning = 80.0;
        alarm_thresholds_.temperature_warning = 70.0;
        alarm_thresholds_.temperature_critical = 75.0;
        
        // 创建精度监控器
        precision_monitor_ = std::make_shared<PrecisionMonitor>(mock_timing_engine_, alarm_thresholds_);
        
        // 创建告警系统
        alarm_system_ = std::make_shared<SmartAlarmSystem>(precision_monitor_);
    }
    
    void TearDown() override {
        if (alarm_system_) {
            alarm_system_->Stop();
        }
        if (precision_monitor_) {
            precision_monitor_->Stop();
        }
    }
    
    void SetupDefaultMockBehavior() {
        // 设置默认系统状态
        SystemStatus default_status;
        default_status.current_state = ClockState::LOCKED;
        default_status.active_source = TimeSource::GNSS;
        default_status.health = SystemHealth::HEALTHY;
        default_status.uptime_seconds = 3600;
        default_status.cpu_usage_percent = 5.0;
        default_status.memory_usage_mb = 50;
        default_status.version = "1.0.0";
        default_status.platform = "test-platform";
        
        ON_CALL(*mock_timing_engine_, GetSystemStatus())
            .WillByDefault(Return(default_status));
        
        // 设置默认时间源信息
        TimeSourceInfo gnss_info;
        gnss_info.type = TimeSource::GNSS;
        gnss_info.status = TimeSourceStatus::ACTIVE;
        gnss_info.quality.accuracy_ns = 25.0;
        gnss_info.quality.stability_ppm = 1e-12;
        gnss_info.quality.confidence = 95;
        gnss_info.quality.is_traceable = true;
        gnss_info.priority = 1;
        gnss_info.last_update_ns = GetCurrentTimestampNs();
        gnss_info.properties["satellites"] = "12";
        gnss_info.properties["snr_db"] = "-140.0";
        
        ON_CALL(*mock_timing_engine_, GetTimeSourceInfo(TimeSource::GNSS))
            .WillByDefault(Return(gnss_info));
        
        TimeSourceInfo rubidium_info;
        rubidium_info.type = TimeSource::RUBIDIUM;
        rubidium_info.status = TimeSourceStatus::STANDBY;
        rubidium_info.quality.accuracy_ns = 100.0;
        rubidium_info.quality.stability_ppm = 1e-11;
        rubidium_info.quality.confidence = 90;
        rubidium_info.priority = 2;
        rubidium_info.last_update_ns = GetCurrentTimestampNs();
        rubidium_info.properties["temperature"] = "65.0";
        
        ON_CALL(*mock_timing_engine_, GetTimeSourceInfo(TimeSource::RUBIDIUM))
            .WillByDefault(Return(rubidium_info));
    }

protected:
    std::shared_ptr<MockTimingEngine> mock_timing_engine_;
    std::shared_ptr<PrecisionMonitor> precision_monitor_;
    std::shared_ptr<SmartAlarmSystem> alarm_system_;
    AlarmThresholds alarm_thresholds_;
};

// 测试精度监控器基本功能
TEST_F(PrecisionMonitoringIntegrationTest, BasicPrecisionMonitoring) {
    // 启动精度监控器
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 等待一些测量数据
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 获取当前测量
    auto measurement = precision_monitor_->GetCurrentMeasurement();
    
    // 验证测量数据
    EXPECT_GT(measurement.timestamp_ns, 0);
    EXPECT_EQ(measurement.source, TimeSource::GNSS);
    EXPECT_EQ(measurement.system_state, ClockState::LOCKED);
    EXPECT_GT(measurement.overall_quality_score, 0);
    EXPECT_TRUE(measurement.meets_spec_requirements);
    
    // 停止监控器
    EXPECT_TRUE(precision_monitor_->Stop());
}

// 测试精度监控级别设置
TEST_F(PrecisionMonitoringIntegrationTest, MonitoringLevelConfiguration) {
    // 测试不同监控级别
    precision_monitor_->SetMonitoringLevel(MonitoringLevel::BASIC);
    precision_monitor_->SetMonitoringLevel(MonitoringLevel::STANDARD);
    precision_monitor_->SetMonitoringLevel(MonitoringLevel::ADVANCED);
    precision_monitor_->SetMonitoringLevel(MonitoringLevel::DIAGNOSTIC);
    
    // 启动监控器
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 验证监控器正常工作
    auto measurement = precision_monitor_->GetCurrentMeasurement();
    EXPECT_GT(measurement.timestamp_ns, 0);
    
    precision_monitor_->Stop();
}

// 测试历史数据管理
TEST_F(PrecisionMonitoringIntegrationTest, HistoricalDataManagement) {
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 等待收集一些历史数据
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    uint64_t current_time = GetCurrentTimestampNs();
    uint64_t start_time = current_time - 1000000000ULL; // 1秒前
    
    // 获取历史数据
    auto historical_data = precision_monitor_->GetHistoricalMeasurements(start_time, current_time, 100);
    
    // 验证历史数据
    EXPECT_GT(historical_data.size(), 0);
    
    // 测试数据导出
    EXPECT_TRUE(precision_monitor_->ExportMeasurementData("/tmp/test_measurements.csv", "csv"));
    EXPECT_TRUE(precision_monitor_->ExportMeasurementData("/tmp/test_measurements.json", "json"));
    
    // 清理历史数据
    precision_monitor_->ClearHistoricalData(0); // 清除所有数据
    
    precision_monitor_->Stop();
}

// 测试精度趋势分析
TEST_F(PrecisionMonitoringIntegrationTest, PrecisionTrendAnalysis) {
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 等待收集足够的数据进行趋势分析
    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    
    // 获取趋势分析
    auto trend = precision_monitor_->GetPrecisionTrend(1); // 1小时趋势
    
    // 验证趋势分析结果
    EXPECT_EQ(trend.analysis_period_hours, 1);
    EXPECT_GE(trend.mean_accuracy_ns, 0.0);
    EXPECT_GE(trend.confidence_level, 0.0);
    EXPECT_LE(trend.confidence_level, 1.0);
    
    precision_monitor_->Stop();
}

// 测试系统健康评分
TEST_F(PrecisionMonitoringIntegrationTest, SystemHealthScoring) {
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 获取系统健康评分
    auto health_score = precision_monitor_->GetSystemHealthScore();
    
    // 验证健康评分
    EXPECT_LE(health_score.overall_score, 100);
    EXPECT_LE(health_score.timing_accuracy_score, 100);
    EXPECT_LE(health_score.signal_quality_score, 100);
    EXPECT_LE(health_score.system_stability_score, 100);
    EXPECT_LE(health_score.hardware_health_score, 100);
    EXPECT_LE(health_score.performance_score, 100);
    
    // 验证健康状态
    EXPECT_NE(health_score.health_status, SystemHealth::CRITICAL); // 默认情况下不应该是严重状态
    
    precision_monitor_->Stop();
}

// 测试预测性维护建议
TEST_F(PrecisionMonitoringIntegrationTest, PredictiveMaintenanceAdvice) {
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 等待收集数据
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // 获取维护建议
    auto advice = precision_monitor_->GetMaintenanceAdvice();
    
    // 验证维护建议
    EXPECT_GT(advice.analysis_timestamp_ns, 0);
    EXPECT_GT(advice.estimated_days_to_failure, 0);
    EXPECT_GE(advice.failure_probability, 0.0);
    EXPECT_LE(advice.failure_probability, 1.0);
    EXPECT_FALSE(advice.primary_concern.empty());
    
    // 验证组件预测
    EXPECT_GT(advice.component_predictions.size(), 0);
    for (const auto& prediction : advice.component_predictions) {
        EXPECT_FALSE(prediction.component_name.empty());
        EXPECT_LE(prediction.health_percentage, 100);
        EXPECT_GT(prediction.estimated_lifetime_days, 0);
    }
    
    precision_monitor_->Stop();
}

// 测试告警系统基本功能
TEST_F(PrecisionMonitoringIntegrationTest, BasicAlarmSystem) {
    // 启动告警系统
    EXPECT_TRUE(alarm_system_->Start());
    
    // 创建测试告警
    AlarmEvent test_alarm(AlarmType::ACCURACY_DEGRADED, AlarmLevel::WARNING, "测试告警");
    test_alarm.description = "这是一个测试告警";
    test_alarm.source_component = "TestComponent";
    test_alarm.threshold_value = 100.0;
    test_alarm.current_value = 150.0;
    test_alarm.unit = "ns";
    
    // 触发告警
    uint64_t alarm_id = alarm_system_->TriggerAlarm(test_alarm);
    EXPECT_GT(alarm_id, 0);
    
    // 获取活跃告警
    auto active_alarms = alarm_system_->GetActiveAlarms();
    EXPECT_EQ(active_alarms.size(), 1);
    EXPECT_EQ(active_alarms[0].id, alarm_id);
    EXPECT_EQ(active_alarms[0].title, "测试告警");
    
    // 确认告警
    EXPECT_TRUE(alarm_system_->AcknowledgeAlarm(alarm_id, "test_user", "已确认"));
    
    // 解决告警
    EXPECT_TRUE(alarm_system_->ResolveAlarm(alarm_id, "问题已解决"));
    
    // 验证告警已解决
    active_alarms = alarm_system_->GetActiveAlarms();
    EXPECT_EQ(active_alarms.size(), 0);
    
    alarm_system_->Stop();
}

// 测试告警规则管理
TEST_F(PrecisionMonitoringIntegrationTest, AlarmRuleManagement) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 创建自定义告警规则
    AlarmRule custom_rule;
    custom_rule.rule_name = "自定义精度告警";
    custom_rule.alarm_type = AlarmType::ACCURACY_DEGRADED;
    custom_rule.alarm_level = AlarmLevel::ERROR;
    custom_rule.metric_name = "absolute_accuracy_ns";
    custom_rule.condition = ">";
    custom_rule.threshold_value = 200.0;
    custom_rule.duration_seconds = 60;
    custom_rule.enabled = true;
    custom_rule.notification_methods = {NotificationMethod::LOG_ONLY, NotificationMethod::CONSOLE};
    
    // 添加规则
    uint64_t rule_id = alarm_system_->AddAlarmRule(custom_rule);
    EXPECT_GT(rule_id, 0);
    
    // 获取所有规则
    auto rules = alarm_system_->GetAlarmRules();
    EXPECT_GT(rules.size(), 0);
    
    // 查找添加的规则
    bool found = false;
    for (const auto& rule : rules) {
        if (rule.rule_id == rule_id) {
            EXPECT_EQ(rule.rule_name, "自定义精度告警");
            EXPECT_EQ(rule.threshold_value, 200.0);
            found = true;
            break;
        }
    }
    EXPECT_TRUE(found);
    
    // 更新规则
    custom_rule.rule_id = rule_id;
    custom_rule.threshold_value = 250.0;
    EXPECT_TRUE(alarm_system_->UpdateAlarmRule(custom_rule));
    
    // 删除规则
    EXPECT_TRUE(alarm_system_->RemoveAlarmRule(rule_id));
    
    alarm_system_->Stop();
}

// 测试通知系统集成
TEST_F(PrecisionMonitoringIntegrationTest, NotificationSystemIntegration) {
    // 创建通知器
    auto log_notifier = NotificationFactory::CreateLogNotifier();
    auto console_notifier = NotificationFactory::CreateConsoleNotifier();
    auto syslog_notifier = NotificationFactory::CreateSyslogNotifier();
    
    // 验证通知器可用性
    EXPECT_TRUE(log_notifier->IsAvailable());
    EXPECT_TRUE(console_notifier->IsAvailable());
    // syslog可用性取决于平台
    
    // 注册通知器到告警系统
    alarm_system_->RegisterNotifier(log_notifier);
    alarm_system_->RegisterNotifier(console_notifier);
    if (syslog_notifier->IsAvailable()) {
        alarm_system_->RegisterNotifier(syslog_notifier);
    }
    
    EXPECT_TRUE(alarm_system_->Start());
    
    // 创建测试告警
    AlarmEvent test_alarm(AlarmType::GNSS_SIGNAL_WEAK, AlarmLevel::WARNING, "GNSS信号弱");
    test_alarm.description = "GNSS信号强度低于阈值";
    test_alarm.source_component = "GnssReceiver";
    test_alarm.threshold_value = -145.0;
    test_alarm.current_value = -148.0;
    test_alarm.unit = "dB";
    
    // 触发告警（应该触发通知）
    uint64_t alarm_id = alarm_system_->TriggerAlarm(test_alarm);
    EXPECT_GT(alarm_id, 0);
    
    // 等待通知处理
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 清理
    alarm_system_->ResolveAlarm(alarm_id, "测试完成");
    alarm_system_->Stop();
}

// 测试精度监控和告警系统的完整集成
TEST_F(PrecisionMonitoringIntegrationTest, FullSystemIntegration) {
    // 设置测量回调来触发告警评估
    bool callback_called = false;
    precision_monitor_->SetMeasurementCallback([&callback_called](const PrecisionMeasurement& measurement) {
        callback_called = true;
    });
    
    // 创建默认通知器
    auto notifiers = NotificationFactory::CreateDefaultNotifiers();
    for (auto& notifier : notifiers) {
        alarm_system_->RegisterNotifier(notifier);
    }
    
    // 启动系统
    EXPECT_TRUE(precision_monitor_->Start());
    EXPECT_TRUE(alarm_system_->Start());
    
    // 等待系统运行
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 验证回调被调用
    EXPECT_TRUE(callback_called);
    
    // 获取统计信息
    auto alarm_stats = alarm_system_->GetAlarmStatistics();
    EXPECT_GE(alarm_stats.total_alarms, 0);
    
    // 测试精度要求检查
    EXPECT_TRUE(precision_monitor_->MeetsAccuracyRequirement(100.0)); // 100ns要求
    
    // 强制执行一次测量
    auto forced_measurement = precision_monitor_->ForceMeasurement();
    EXPECT_GT(forced_measurement.timestamp_ns, 0);
    
    // 停止系统
    alarm_system_->Stop();
    precision_monitor_->Stop();
}

// 测试异常情况处理
TEST_F(PrecisionMonitoringIntegrationTest, ExceptionHandling) {
    // 测试无效的授时引擎
    auto invalid_monitor = std::make_shared<PrecisionMonitor>(nullptr, alarm_thresholds_);
    EXPECT_FALSE(invalid_monitor->Start());
    
    // 测试无效的精度监控器
    auto invalid_alarm_system = std::make_shared<SmartAlarmSystem>(nullptr);
    EXPECT_FALSE(invalid_alarm_system->Start());
    
    // 测试重复启动
    EXPECT_TRUE(precision_monitor_->Start());
    EXPECT_TRUE(precision_monitor_->Start()); // 应该返回true但不重复启动
    precision_monitor_->Stop();
    
    // 测试重复停止
    EXPECT_TRUE(precision_monitor_->Stop());
    EXPECT_TRUE(precision_monitor_->Stop()); // 应该返回true
}

// 性能测试
TEST_F(PrecisionMonitoringIntegrationTest, PerformanceTest) {
    EXPECT_TRUE(precision_monitor_->Start());
    EXPECT_TRUE(alarm_system_->Start());
    
    // 测量多次获取当前测量的性能
    auto start_time = std::chrono::high_resolution_clock::now();
    const int num_measurements = 100;
    
    for (int i = 0; i < num_measurements; ++i) {
        auto measurement = precision_monitor_->GetCurrentMeasurement();
        EXPECT_GT(measurement.timestamp_ns, 0);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // 验证性能（每次测量应该在合理时间内完成）
    EXPECT_LT(duration.count(), 1000); // 100次测量应该在1秒内完成
    
    std::cout << "性能测试结果: " << num_measurements << " 次测量耗时 " 
              << duration.count() << " 毫秒" << std::endl;
    
    alarm_system_->Stop();
    precision_monitor_->Stop();
}

} // namespace