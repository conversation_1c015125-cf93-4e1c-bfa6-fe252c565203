#include <gtest/gtest.h>
#include "core/clock_state_machine.h"
#include <memory>
#include <thread>
#include <chrono>

using namespace timing_server::core;

/**
 * @brief 测试用状态机监听器
 * 用于验证状态机事件通知功能
 */
class TestStateMachineListener : public IStateMachineListener {
public:
    std::vector<std::pair<ClockState, ClockState>> state_entered_events;
    std::vector<std::pair<ClockState, ClockState>> state_exited_events;
    std::vector<std::pair<ClockEvent, ClockState>> event_processed_events;
    std::vector<std::tuple<ClockEvent, ClockState, TransitionResult>> transition_failed_events;
    
    void OnStateEntered(ClockState state, ClockState previous_state) override {
        state_entered_events.emplace_back(state, previous_state);
    }
    
    void OnStateExited(ClockState state, ClockState next_state) override {
        state_exited_events.emplace_back(state, next_state);
    }
    
    void OnEventProcessed(const StateMachineEvent& event, ClockState current_state) override {
        event_processed_events.emplace_back(event.event, current_state);
    }
    
    void OnTransitionFailed(const StateMachineEvent& event, ClockState current_state, TransitionResult result) override {
        transition_failed_events.emplace_back(event.event, current_state, result);
    }
    
    void Clear() {
        state_entered_events.clear();
        state_exited_events.clear();
        event_processed_events.clear();
        transition_failed_events.clear();
    }
};

/**
 * @brief 状态机测试类
 * 测试时钟状态机的完整功能
 */
class StateMachineTest : public ::testing::Test {
protected:
    void SetUp() override {
        state_machine_ = std::make_unique<ClockStateMachine>(ClockState::FREE_RUN);
        listener_ = std::make_shared<TestStateMachineListener>();
        
        ASSERT_TRUE(state_machine_->Initialize());
        state_machine_->AddListener(listener_);
        ASSERT_TRUE(state_machine_->Start());
    }
    
    void TearDown() override {
        if (state_machine_) {
            state_machine_->Stop();
        }
    }
    
    std::unique_ptr<ClockStateMachine> state_machine_;
    std::shared_ptr<TestStateMachineListener> listener_;
};

/**
 * @brief 测试状态机初始化
 */
TEST_F(StateMachineTest, Initialization) {
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    
    // 测试状态信息
    StateInfo info = state_machine_->GetStateInfo(ClockState::FREE_RUN);
    EXPECT_EQ(info.state, ClockState::FREE_RUN);
    EXPECT_EQ(info.enter_count, 1);
    EXPECT_GT(info.enter_time_ns, 0);
}

/**
 * @brief 测试基本状态转换：FREE_RUN -> DISCIPLINING
 */
TEST_F(StateMachineTest, FreeRunToDisciplining) {
    listener_->Clear();
    
    // 发送GNSS信号获取事件
    StateMachineEvent event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "测试GNSS信号获取");
    TransitionResult result = state_machine_->ProcessEvent(event);
    
    EXPECT_EQ(result, TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 验证监听器事件
    ASSERT_EQ(listener_->state_exited_events.size(), 1);
    EXPECT_EQ(listener_->state_exited_events[0].first, ClockState::FREE_RUN);
    EXPECT_EQ(listener_->state_exited_events[0].second, ClockState::DISCIPLINING);
    
    ASSERT_EQ(listener_->state_entered_events.size(), 1);
    EXPECT_EQ(listener_->state_entered_events[0].first, ClockState::DISCIPLINING);
    EXPECT_EQ(listener_->state_entered_events[0].second, ClockState::FREE_RUN);
    
    ASSERT_EQ(listener_->event_processed_events.size(), 1);
    EXPECT_EQ(listener_->event_processed_events[0].first, ClockEvent::GNSS_SIGNAL_ACQUIRED);
    EXPECT_EQ(listener_->event_processed_events[0].second, ClockState::DISCIPLINING);
}

/**
 * @brief 测试完整的驯服流程：FREE_RUN -> DISCIPLINING -> LOCKED
 */
TEST_F(StateMachineTest, CompleteDiscipliningFlow) {
    // 第一步：FREE_RUN -> DISCIPLINING
    StateMachineEvent gnss_acquired(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号获取");
    EXPECT_EQ(state_machine_->ProcessEvent(gnss_acquired), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 等待最小驯服时间（模拟）
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // 第二步：DISCIPLINING -> LOCKED
    StateMachineEvent convergence(ClockEvent::CONVERGENCE_ACHIEVED, "驯服收敛完成");
    EXPECT_EQ(state_machine_->ProcessEvent(convergence), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
    
    // 验证状态统计
    StateInfo disciplining_info = state_machine_->GetStateInfo(ClockState::DISCIPLINING);
    EXPECT_EQ(disciplining_info.enter_count, 1);
    EXPECT_GT(disciplining_info.total_duration_ns, 0);
    
    StateInfo locked_info = state_machine_->GetStateInfo(ClockState::LOCKED);
    EXPECT_EQ(locked_info.enter_count, 1);
    EXPECT_GT(locked_info.enter_time_ns, 0);
}

/**
 * @brief 测试守时流程：LOCKED -> HOLDOVER -> DISCIPLINING
 */
TEST_F(StateMachineTest, HoldoverFlow) {
    // 先到达LOCKED状态
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
    
    // LOCKED -> HOLDOVER (GNSS信号丢失)
    StateMachineEvent signal_lost(ClockEvent::GNSS_SIGNAL_LOST, "GNSS信号丢失");
    EXPECT_EQ(state_machine_->ProcessEvent(signal_lost), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
    
    // HOLDOVER -> DISCIPLINING (GNSS信号恢复)
    StateMachineEvent signal_recovered(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号恢复");
    EXPECT_EQ(state_machine_->ProcessEvent(signal_recovered), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
}

/**
 * @brief 测试守时超时：HOLDOVER -> FREE_RUN
 */
TEST_F(StateMachineTest, HoldoverTimeout) {
    // 先到达HOLDOVER状态
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_LOST));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
    
    // HOLDOVER -> FREE_RUN (守时超时)
    StateMachineEvent timeout(ClockEvent::HOLDOVER_TIMEOUT, "守时超时");
    EXPECT_EQ(state_machine_->ProcessEvent(timeout), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
}

/**
 * @brief 测试硬件故障处理
 */
TEST_F(StateMachineTest, HardwareFaultHandling) {
    // 测试从不同状态的硬件故障处理
    std::vector<ClockState> test_states = {
        ClockState::DISCIPLINING,
        ClockState::LOCKED,
        ClockState::HOLDOVER
    };
    
    for (ClockState test_state : test_states) {
        // 重置到初始状态
        state_machine_->Reset();
        state_machine_->Start();
        
        // 根据测试状态设置相应的状态
        if (test_state == ClockState::DISCIPLINING) {
            state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
        } else if (test_state == ClockState::LOCKED) {
            state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
        } else if (test_state == ClockState::HOLDOVER) {
            state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
            state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_LOST));
        }
        
        EXPECT_EQ(state_machine_->GetCurrentState(), test_state);
        
        // 发送硬件故障事件
        StateMachineEvent fault(ClockEvent::HARDWARE_FAULT, "硬件故障测试");
        EXPECT_EQ(state_machine_->ProcessEvent(fault), TransitionResult::SUCCESS);
        EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    }
}

/**
 * @brief 测试无效状态转换
 */
TEST_F(StateMachineTest, InvalidTransitions) {
    listener_->Clear();
    
    // 在FREE_RUN状态下发送收敛事件（无效）
    StateMachineEvent invalid_event(ClockEvent::CONVERGENCE_ACHIEVED, "无效的收敛事件");
    TransitionResult result = state_machine_->ProcessEvent(invalid_event);
    
    EXPECT_EQ(result, TransitionResult::INVALID);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    
    // 验证转换失败事件
    ASSERT_EQ(listener_->transition_failed_events.size(), 1);
    EXPECT_EQ(std::get<0>(listener_->transition_failed_events[0]), ClockEvent::CONVERGENCE_ACHIEVED);
    EXPECT_EQ(std::get<1>(listener_->transition_failed_events[0]), ClockState::FREE_RUN);
    EXPECT_EQ(std::get<2>(listener_->transition_failed_events[0]), TransitionResult::INVALID);
}

/**
 * @brief 测试强制状态转换
 */
TEST_F(StateMachineTest, ForceTransition) {
    // 强制转换到LOCKED状态
    EXPECT_TRUE(state_machine_->ForceTransition(ClockState::LOCKED, "强制转换测试"));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
    
    // 强制转换到HOLDOVER状态
    EXPECT_TRUE(state_machine_->ForceTransition(ClockState::HOLDOVER, "强制转换到守时"));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
}

/**
 * @brief 测试状态转换有效性检查
 */
TEST_F(StateMachineTest, TransitionValidityCheck) {
    // 有效转换
    EXPECT_TRUE(state_machine_->IsTransitionValid(
        ClockState::FREE_RUN, 
        ClockState::DISCIPLINING, 
        ClockEvent::GNSS_SIGNAL_ACQUIRED
    ));
    
    // 无效转换
    EXPECT_FALSE(state_machine_->IsTransitionValid(
        ClockState::FREE_RUN, 
        ClockState::LOCKED, 
        ClockEvent::GNSS_SIGNAL_ACQUIRED
    ));
    
    EXPECT_FALSE(state_machine_->IsTransitionValid(
        ClockState::DISCIPLINING, 
        ClockState::HOLDOVER, 
        ClockEvent::CONVERGENCE_ACHIEVED
    ));
}

/**
 * @brief 测试状态持续时间计算
 */
TEST_F(StateMachineTest, StateDurationCalculation) {
    uint64_t initial_duration = state_machine_->GetStateDuration();
    EXPECT_GT(initial_duration, 0);
    
    // 等待一段时间
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    uint64_t later_duration = state_machine_->GetStateDuration();
    EXPECT_GT(later_duration, initial_duration);
}

/**
 * @brief 测试状态机重置功能
 */
TEST_F(StateMachineTest, StateMachineReset) {
    // 执行一些状态转换
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 重置状态机
    state_machine_->Reset();
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    
    // 验证统计信息被重置
    StateInfo info = state_machine_->GetStateInfo(ClockState::FREE_RUN);
    EXPECT_EQ(info.enter_count, 1);
    EXPECT_EQ(info.last_transition_reason, "重置到初始状态");
}

/**
 * @brief 测试状态持久化和恢复
 */
TEST_F(StateMachineTest, StatePersistenceAndRestore) {
    // 执行状态转换
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
    
    // 保存状态
    std::string state_file = "/tmp/test_state_machine.json";
    EXPECT_TRUE(state_machine_->SaveState(state_file));
    
    // 创建新的状态机并恢复状态
    auto new_state_machine = std::make_unique<ClockStateMachine>();
    EXPECT_TRUE(new_state_machine->Initialize());
    EXPECT_TRUE(new_state_machine->RestoreState(state_file));
    
    // 验证状态已恢复（简化验证）
    // 注意：完整的状态恢复需要更复杂的JSON解析
    EXPECT_EQ(new_state_machine->GetCurrentState(), ClockState::LOCKED);
    
    // 清理测试文件
    std::remove(state_file.c_str());
}

/**
 * @brief 测试状态机统计信息
 */
TEST_F(StateMachineTest, StatisticsGeneration) {
    // 执行一些状态转换
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    
    // 获取统计信息
    std::string stats = state_machine_->GetStatistics();
    EXPECT_FALSE(stats.empty());
    EXPECT_NE(stats.find("当前状态"), std::string::npos);
    EXPECT_NE(stats.find("总转换次数"), std::string::npos);
    EXPECT_NE(stats.find("LOCKED"), std::string::npos);
}

/**
 * @brief 测试监听器管理
 */
TEST_F(StateMachineTest, ListenerManagement) {
    auto additional_listener = std::make_shared<TestStateMachineListener>();
    
    // 添加额外监听器
    state_machine_->AddListener(additional_listener);
    
    // 执行状态转换
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    
    // 验证两个监听器都收到了事件
    EXPECT_EQ(listener_->state_entered_events.size(), 1);
    EXPECT_EQ(additional_listener->state_entered_events.size(), 1);
    
    // 移除监听器
    state_machine_->RemoveListener(additional_listener);
    
    // 再次执行状态转换
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    
    // 验证只有原始监听器收到事件
    EXPECT_EQ(listener_->state_entered_events.size(), 2);
    EXPECT_EQ(additional_listener->state_entered_events.size(), 1);
}

/**
 * @brief 测试状态转换守护条件和动作
 */
TEST_F(StateMachineTest, TransitionGuardsAndActions) {
    bool guard_called = false;
    bool action_called = false;
    
    // 设置守护条件和动作
    state_machine_->SetTransitionGuard(
        ClockState::FREE_RUN,
        ClockState::DISCIPLINING,
        ClockEvent::GNSS_SIGNAL_ACQUIRED,
        [&guard_called]() -> bool {
            guard_called = true;
            return true;  // 允许转换
        }
    );
    
    state_machine_->SetTransitionAction(
        ClockState::FREE_RUN,
        ClockState::DISCIPLINING,
        ClockEvent::GNSS_SIGNAL_ACQUIRED,
        [&action_called]() {
            action_called = true;
        }
    );
    
    // 执行状态转换
    TransitionResult result = state_machine_->ProcessEvent(
        StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED)
    );
    
    EXPECT_EQ(result, TransitionResult::SUCCESS);
    EXPECT_TRUE(guard_called);
    EXPECT_TRUE(action_called);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
}

/**
 * @brief 测试事件类型和字符串转换
 */
TEST_F(StateMachineTest, EventTypeConversions) {
    // 测试事件类型定义
    ClockEvent event1 = ClockEvent::GNSS_SIGNAL_ACQUIRED;
    ClockEvent event2 = ClockEvent::GNSS_SIGNAL_LOST;
    ClockEvent event3 = ClockEvent::CONVERGENCE_ACHIEVED;
    ClockEvent event4 = ClockEvent::HOLDOVER_TIMEOUT;
    
    EXPECT_EQ(event1, ClockEvent::GNSS_SIGNAL_ACQUIRED);
    EXPECT_EQ(event2, ClockEvent::GNSS_SIGNAL_LOST);
    EXPECT_EQ(event3, ClockEvent::CONVERGENCE_ACHIEVED);
    EXPECT_EQ(event4, ClockEvent::HOLDOVER_TIMEOUT);
    
    // 测试字符串转换
    EXPECT_EQ(ClockEventToString(ClockEvent::GNSS_SIGNAL_ACQUIRED), "GNSS_SIGNAL_ACQUIRED");
    EXPECT_EQ(ClockEventToString(ClockEvent::CONVERGENCE_ACHIEVED), "CONVERGENCE_ACHIEVED");
    EXPECT_EQ(TransitionResultToString(TransitionResult::SUCCESS), "SUCCESS");
    EXPECT_EQ(TransitionResultToString(TransitionResult::INVALID), "INVALID");
}

/**
 * @brief 测试所有状态的完整性
 */
TEST_F(StateMachineTest, StateCompleteness) {
    // 验证所有状态都已定义
    std::vector<ClockState> all_states = {
        ClockState::FREE_RUN,
        ClockState::DISCIPLINING,
        ClockState::LOCKED,
        ClockState::HOLDOVER
    };
    
    EXPECT_EQ(all_states.size(), 4);
    
    // 验证所有状态都有对应的字符串表示
    for (ClockState state : all_states) {
        std::string state_str = ClockStateToString(state);
        EXPECT_FALSE(state_str.empty());
        EXPECT_NE(state_str, "UNKNOWN");
    }
    
    // 验证状态机可以获取所有状态的信息
    auto all_state_info = state_machine_->GetAllStateInfo();
    EXPECT_EQ(all_state_info.size(), 4);
    
    for (ClockState state : all_states) {
        EXPECT_NE(all_state_info.find(state), all_state_info.end());
    }
}