/**
 * @file test_api_integration.cpp
 * @brief REST API集成测试
 * 
 * 测试REST API的各个组件是否正确集成和工作
 */

#include <gtest/gtest.h>
#include <memory>
#include <string>

// 包含API相关头文件
#include <api/timing_service.h>
#include <core/types.h>
#include <core/logger.h>

using namespace timing_server;

/**
 * @brief 模拟授时服务实现
 * 用于测试API功能
 */
class MockTimingServiceForTest : public api::TimingService {
public:
    core::SystemStatus getSystemStatus() override {
        core::SystemStatus status;
        status.current_state = core::ClockState::LOCKED;
        status.active_source = core::TimeSource::GNSS;
        status.health = core::SystemHealth::HEALTHY;
        status.uptime_seconds = 3600;
        status.cpu_usage_percent = 2.5;
        status.memory_usage_mb = 48;
        status.version = "1.0.0-test";
        status.platform = "test-platform";
        
        // 添加GNSS时间源
        core::TimeSourceInfo gnss_source;
        gnss_source.type = core::TimeSource::GNSS;
        gnss_source.status = core::TimeSourceStatus::ACTIVE;
        gnss_source.priority = 1;
        gnss_source.last_update_ns = core::GetCurrentTimestampNs();
        gnss_source.quality.accuracy_ns = 45.0;
        gnss_source.quality.stability_ppm = 1.2e-12;
        gnss_source.quality.confidence = 98;
        gnss_source.quality.is_traceable = true;
        gnss_source.quality.reference = "GPS";
        
        status.sources.push_back(gnss_source);
        return status;
    }
    
    std::string getPtpConfig() override {
        return R"({"domain": 0, "priority1": 128, "clock_class": 6})";
    }
    
    bool updatePtpConfig(const std::string& config_json) override {
        return !config_json.empty();
    }
    
    std::string getNtpConfig() override {
        return R"({"stratum": 1, "reference_id": "GPS"})";
    }
    
    bool updateNtpConfig(const std::string& config_json) override {
        return !config_json.empty();
    }
    
    LogQueryResult queryLogs(const LogQueryParams& params) override {
        LogQueryResult result;
        
        LogEntry entry;
        entry.timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
        entry.level = "INFO";
        entry.component = "TEST";
        entry.message = "测试日志条目";
        entry.context["test"] = "value";
        
        result.logs.push_back(entry);
        result.total_entries = 1;
        result.current_page = params.page;
        result.total_pages = 1;
        
        return result;
    }
    
    HealthStatus getHealthStatus() override {
        HealthStatus status;
        status.status = "HEALTHY";
        status.timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
        status.uptime_seconds = 3600.0;
        status.components["test_component"] = "HEALTHY";
        return status;
    }
    
    bool restartSystem() override {
        return true;
    }
    
    PerformanceMetrics getMetrics(const std::string& from, const std::string& to) override {
        PerformanceMetrics metrics;
        metrics.current_accuracy_ns = 42.5;
        metrics.average_accuracy_ns = 45.2;
        metrics.max_accuracy_ns = 48.7;
        metrics.allan_deviation_1s = 1.1e-12;
        metrics.state_transitions = 3;
        metrics.error_count = 0;
        metrics.packets_processed = 125000;
        metrics.average_response_time_ms = 2.8;
        metrics.start_time = from;
        metrics.end_time = to;
        return metrics;
    }
    
    std::string validateConfig(const std::string& config_type, const std::string& config_json) override {
        if (config_type == "ptp" || config_type == "ntp") {
            return ""; // 验证通过
        }
        return "不支持的配置类型";
    }
    
    std::string getConfigSchema(const std::string& config_type) override {
        if (config_type == "ptp") {
            return R"({"type": "object"})";
        }
        return "";
    }
};

/**
 * @brief API集成测试类
 */
class ApiIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        timing_service = std::make_shared<MockTimingServiceForTest>();
    }
    
    void TearDown() override {
        timing_service.reset();
    }
    
    std::shared_ptr<MockTimingServiceForTest> timing_service;
};

/**
 * @brief 测试TimingService基本功能
 */
TEST_F(ApiIntegrationTest, TestTimingServiceBasicFunctions) {
    // 测试系统状态获取
    auto status = timing_service->getSystemStatus();
    EXPECT_EQ(status.current_state, core::ClockState::LOCKED);
    EXPECT_EQ(status.active_source, core::TimeSource::GNSS);
    EXPECT_EQ(status.health, core::SystemHealth::HEALTHY);
    EXPECT_EQ(status.version, "1.0.0-test");
    EXPECT_FALSE(status.sources.empty());
    
    // 测试第一个时间源
    const auto& first_source = status.sources[0];
    EXPECT_EQ(first_source.type, core::TimeSource::GNSS);
    EXPECT_EQ(first_source.status, core::TimeSourceStatus::ACTIVE);
    EXPECT_EQ(first_source.priority, 1);
    EXPECT_EQ(first_source.quality.accuracy_ns, 45.0);
    EXPECT_TRUE(first_source.quality.is_traceable);
}

/**
 * @brief 测试PTP配置功能
 */
TEST_F(ApiIntegrationTest, TestPtpConfigFunctions) {
    // 测试获取PTP配置
    std::string config = timing_service->getPtpConfig();
    EXPECT_FALSE(config.empty());
    EXPECT_NE(config.find("domain"), std::string::npos);
    EXPECT_NE(config.find("priority1"), std::string::npos);
    
    // 测试更新PTP配置
    std::string new_config = R"({"domain": 1, "priority1": 64})";
    bool result = timing_service->updatePtpConfig(new_config);
    EXPECT_TRUE(result);
    
    // 测试空配置更新
    result = timing_service->updatePtpConfig("");
    EXPECT_FALSE(result);
}

/**
 * @brief 测试NTP配置功能
 */
TEST_F(ApiIntegrationTest, TestNtpConfigFunctions) {
    // 测试获取NTP配置
    std::string config = timing_service->getNtpConfig();
    EXPECT_FALSE(config.empty());
    EXPECT_NE(config.find("stratum"), std::string::npos);
    EXPECT_NE(config.find("reference_id"), std::string::npos);
    
    // 测试更新NTP配置
    std::string new_config = R"({"stratum": 2, "reference_id": "PTP"})";
    bool result = timing_service->updateNtpConfig(new_config);
    EXPECT_TRUE(result);
}

/**
 * @brief 测试日志查询功能
 */
TEST_F(ApiIntegrationTest, TestLogQueryFunctions) {
    api::TimingService::LogQueryParams params;
    params.level = "INFO";
    params.page = 1;
    params.limit = 100;
    params.from = "2024-01-01T00:00:00Z";
    params.to = "2024-01-01T23:59:59Z";
    
    auto result = timing_service->queryLogs(params);
    
    EXPECT_EQ(result.total_entries, 1);
    EXPECT_EQ(result.current_page, 1);
    EXPECT_EQ(result.total_pages, 1);
    EXPECT_FALSE(result.logs.empty());
    
    const auto& log_entry = result.logs[0];
    EXPECT_EQ(log_entry.level, "INFO");
    EXPECT_EQ(log_entry.component, "TEST");
    EXPECT_EQ(log_entry.message, "测试日志条目");
    EXPECT_FALSE(log_entry.timestamp.empty());
    EXPECT_EQ(log_entry.context.at("test"), "value");
}

/**
 * @brief 测试健康状态功能
 */
TEST_F(ApiIntegrationTest, TestHealthStatusFunctions) {
    auto health = timing_service->getHealthStatus();
    
    EXPECT_EQ(health.status, "HEALTHY");
    EXPECT_FALSE(health.timestamp.empty());
    EXPECT_EQ(health.uptime_seconds, 3600.0);
    EXPECT_FALSE(health.components.empty());
    EXPECT_EQ(health.components.at("test_component"), "HEALTHY");
}

/**
 * @brief 测试系统重启功能
 */
TEST_F(ApiIntegrationTest, TestSystemRestartFunction) {
    bool result = timing_service->restartSystem();
    EXPECT_TRUE(result);
}

/**
 * @brief 测试性能指标功能
 */
TEST_F(ApiIntegrationTest, TestMetricsFunctions) {
    std::string from = "2024-01-01T00:00:00Z";
    std::string to = "2024-01-01T23:59:59Z";
    
    auto metrics = timing_service->getMetrics(from, to);
    
    EXPECT_EQ(metrics.current_accuracy_ns, 42.5);
    EXPECT_EQ(metrics.average_accuracy_ns, 45.2);
    EXPECT_EQ(metrics.max_accuracy_ns, 48.7);
    EXPECT_EQ(metrics.allan_deviation_1s, 1.1e-12);
    EXPECT_EQ(metrics.state_transitions, 3);
    EXPECT_EQ(metrics.error_count, 0);
    EXPECT_EQ(metrics.packets_processed, 125000);
    EXPECT_EQ(metrics.average_response_time_ms, 2.8);
    EXPECT_EQ(metrics.start_time, from);
    EXPECT_EQ(metrics.end_time, to);
}

/**
 * @brief 测试配置验证功能
 */
TEST_F(ApiIntegrationTest, TestConfigValidationFunctions) {
    // 测试有效的配置类型
    std::string result = timing_service->validateConfig("ptp", R"({"domain": 0})");
    EXPECT_TRUE(result.empty()); // 空字符串表示验证通过
    
    result = timing_service->validateConfig("ntp", R"({"stratum": 1})");
    EXPECT_TRUE(result.empty());
    
    // 测试无效的配置类型
    result = timing_service->validateConfig("invalid", R"({"test": "value"})");
    EXPECT_FALSE(result.empty());
    EXPECT_NE(result.find("不支持的配置类型"), std::string::npos);
}

/**
 * @brief 测试配置模式功能
 */
TEST_F(ApiIntegrationTest, TestConfigSchemaFunctions) {
    // 测试PTP配置模式
    std::string schema = timing_service->getConfigSchema("ptp");
    EXPECT_FALSE(schema.empty());
    EXPECT_NE(schema.find("object"), std::string::npos);
    
    // 测试不存在的配置模式
    schema = timing_service->getConfigSchema("invalid");
    EXPECT_TRUE(schema.empty());
}

/**
 * @brief 测试数据类型转换功能
 */
TEST_F(ApiIntegrationTest, TestDataTypeConversions) {
    // 测试时钟状态转换
    EXPECT_EQ(core::ClockStateToString(core::ClockState::LOCKED), "LOCKED");
    EXPECT_EQ(core::ClockStateToString(core::ClockState::FREE_RUN), "FREE_RUN");
    EXPECT_EQ(core::ClockStateToString(core::ClockState::DISCIPLINING), "DISCIPLINING");
    EXPECT_EQ(core::ClockStateToString(core::ClockState::HOLDOVER), "HOLDOVER");
    
    // 测试时间源转换
    EXPECT_EQ(core::TimeSourceToString(core::TimeSource::GNSS), "GNSS");
    EXPECT_EQ(core::TimeSourceToString(core::TimeSource::RUBIDIUM), "RUBIDIUM");
    EXPECT_EQ(core::TimeSourceToString(core::TimeSource::RTC), "RTC");
    
    // 测试时间源状态转换
    EXPECT_EQ(core::TimeSourceStatusToString(core::TimeSourceStatus::ACTIVE), "ACTIVE");
    EXPECT_EQ(core::TimeSourceStatusToString(core::TimeSourceStatus::STANDBY), "STANDBY");
    EXPECT_EQ(core::TimeSourceStatusToString(core::TimeSourceStatus::FAULT), "FAULT");
    
    // 测试系统健康状态转换
    EXPECT_EQ(core::SystemHealthToString(core::SystemHealth::HEALTHY), "HEALTHY");
    EXPECT_EQ(core::SystemHealthToString(core::SystemHealth::WARNING), "WARNING");
    EXPECT_EQ(core::SystemHealthToString(core::SystemHealth::ERROR), "ERROR");
    EXPECT_EQ(core::SystemHealthToString(core::SystemHealth::CRITICAL), "CRITICAL");
}

/**
 * @brief 测试时间戳功能
 */
TEST_F(ApiIntegrationTest, TestTimestampFunctions) {
    // 测试获取当前时间戳
    uint64_t timestamp1 = core::GetCurrentTimestampNs();
    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    uint64_t timestamp2 = core::GetCurrentTimestampNs();
    
    EXPECT_GT(timestamp2, timestamp1);
    
    // 测试时间戳转换为ISO字符串
    std::string iso_string = core::TimestampToIsoString(timestamp1);
    EXPECT_FALSE(iso_string.empty());
    EXPECT_NE(iso_string.find("T"), std::string::npos);
    EXPECT_NE(iso_string.find("Z"), std::string::npos);
}

/**
 * @brief 主测试函数
 */
int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    std::cout << "=== REST API集成测试 ===" << std::endl;
    std::cout << "测试平台: " << PLATFORM_NAME << std::endl;
    
    return RUN_ALL_TESTS();
}