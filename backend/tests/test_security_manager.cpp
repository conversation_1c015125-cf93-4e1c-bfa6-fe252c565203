#include <gtest/gtest.h>
#include <api/security_manager.h>
#include <core/logger.h>
#include <thread>
#include <chrono>
#include <fstream>
#include <filesystem>

using namespace timing_server::api;

class SecurityManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建临时目录用于测试
        test_dir = "/tmp/security_manager_test";
        std::filesystem::create_directories(test_dir);
        
        config_file = test_dir + "/security_config.json";
        cert_file = test_dir + "/test.crt";
        key_file = test_dir + "/test.key";
        
        security_manager = std::make_unique<SecurityManager>();
    }
    
    void TearDown() override {
        security_manager.reset();
        
        // 清理测试文件
        std::filesystem::remove_all(test_dir);
    }
    
    void createTestConfig() {
        std::ofstream config(config_file);
        config << R"({
            "rate_limit": {
                "max_requests": 10,
                "time_window_seconds": 60,
                "burst_limit": 5,
                "block_duration_seconds": 300,
                "enable_progressive_delay": true
            },
            "tls": {
                "cert_file": ")" << cert_file << R"(",
                "key_file": ")" << key_file << R"(",
                "ca_file": "/etc/ssl/ca.crt",
                "min_tls_version": "1.2",
                "require_client_cert": false,
                "cipher_suites": [
                    "ECDHE-RSA-AES256-GCM-SHA384",
                    "ECDHE-RSA-AES128-GCM-SHA256"
                ]
            },
            "whitelist": ["127.0.0.1", "***********/24"],
            "blacklist": ["********", "**********/16"]
        })";
        config.close();
    }
    
    std::string test_dir;
    std::string config_file;
    std::string cert_file;
    std::string key_file;
    std::unique_ptr<SecurityManager> security_manager;
};

// 测试基本初始化
TEST_F(SecurityManagerTest, BasicInitialization) {
    EXPECT_TRUE(security_manager->initialize());
    EXPECT_TRUE(security_manager->isSecurityEnabled());
}

// 测试配置文件加载
TEST_F(SecurityManagerTest, ConfigFileLoading) {
    createTestConfig();
    
    EXPECT_TRUE(security_manager->initialize(config_file));
    
    // 验证速率限制配置
    auto rate_config = security_manager->getRateLimitConfig();
    EXPECT_EQ(rate_config.max_requests, 10);
    EXPECT_EQ(rate_config.time_window_seconds, 60);
    EXPECT_EQ(rate_config.burst_limit, 5);
    EXPECT_EQ(rate_config.block_duration_seconds, 300);
    EXPECT_TRUE(rate_config.enable_progressive_delay);
    
    // 验证TLS配置
    auto tls_config = security_manager->getTlsConfig();
    EXPECT_EQ(tls_config.cert_file, cert_file);
    EXPECT_EQ(tls_config.key_file, key_file);
    EXPECT_EQ(tls_config.min_tls_version, "1.2");
    EXPECT_FALSE(tls_config.require_client_cert);
    EXPECT_EQ(tls_config.cipher_suites.size(), 2);
    
    // 验证白名单和黑名单
    auto whitelist = security_manager->getWhitelist();
    auto blacklist = security_manager->getBlacklist();
    
    EXPECT_EQ(whitelist.size(), 2);
    EXPECT_EQ(blacklist.size(), 2);
    
    EXPECT_TRUE(std::find(whitelist.begin(), whitelist.end(), "127.0.0.1") != whitelist.end());
    EXPECT_TRUE(std::find(blacklist.begin(), blacklist.end(), "********") != blacklist.end());
}

// 测试IP白名单功能
TEST_F(SecurityManagerTest, WhitelistFunctionality) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 添加IP到白名单
    EXPECT_TRUE(security_manager->addToWhitelist("*************"));
    EXPECT_FALSE(security_manager->addToWhitelist("*************")); // 重复添加
    
    // 验证白名单
    auto whitelist = security_manager->getWhitelist();
    EXPECT_EQ(whitelist.size(), 1);
    EXPECT_EQ(whitelist[0], "*************");
    
    // 验证IP访问权限
    EXPECT_TRUE(security_manager->isIpAllowed("*************"));
    EXPECT_TRUE(security_manager->isIpAllowed("*************")); // 不在白名单但也不在黑名单
    
    // 从白名单移除
    EXPECT_TRUE(security_manager->removeFromWhitelist("*************"));
    EXPECT_FALSE(security_manager->removeFromWhitelist("*************")); // 重复移除
    
    whitelist = security_manager->getWhitelist();
    EXPECT_EQ(whitelist.size(), 0);
}

// 测试IP黑名单功能
TEST_F(SecurityManagerTest, BlacklistFunctionality) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 添加IP到黑名单
    EXPECT_TRUE(security_manager->addToBlacklist("********00", 0, "测试阻断"));
    
    // 验证黑名单
    auto blacklist = security_manager->getBlacklist();
    EXPECT_EQ(blacklist.size(), 1);
    EXPECT_EQ(blacklist[0], "********00");
    
    // 验证IP访问权限
    EXPECT_FALSE(security_manager->isIpAllowed("********00"));
    EXPECT_TRUE(security_manager->isIpAllowed("**********")); // 不在黑名单
    
    // 从黑名单移除
    EXPECT_TRUE(security_manager->removeFromBlacklist("********00"));
    EXPECT_TRUE(security_manager->isIpAllowed("********00")); // 现在应该允许访问
}

// 测试临时IP阻断
TEST_F(SecurityManagerTest, TemporaryIpBlocking) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 添加临时阻断（1秒）
    EXPECT_TRUE(security_manager->addToBlacklist("********00", 1, "临时测试阻断"));
    
    // 立即检查应该被阻断
    EXPECT_FALSE(security_manager->isIpAllowed("********00"));
    
    // 等待阻断过期
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 现在应该允许访问
    EXPECT_TRUE(security_manager->isIpAllowed("********00"));
}

// 测试CIDR网段匹配
TEST_F(SecurityManagerTest, CidrMatching) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 添加CIDR网段到白名单
    EXPECT_TRUE(security_manager->addToWhitelist("***********/24"));
    
    // 测试网段内的IP
    EXPECT_TRUE(security_manager->isIpAllowed("***********"));
    EXPECT_TRUE(security_manager->isIpAllowed("*************"));
    EXPECT_TRUE(security_manager->isIpAllowed("*************"));
    
    // 测试网段外的IP
    EXPECT_TRUE(security_manager->isIpAllowed("***********")); // 不在白名单但也不在黑名单
    
    // 添加CIDR网段到黑名单
    EXPECT_TRUE(security_manager->addToBlacklist("10.0.0.0/16"));
    
    // 测试黑名单网段
    EXPECT_FALSE(security_manager->isIpAllowed("********"));
    EXPECT_FALSE(security_manager->isIpAllowed("************"));
    EXPECT_TRUE(security_manager->isIpAllowed("********")); // 不在黑名单网段
}

// 测试速率限制
TEST_F(SecurityManagerTest, RateLimiting) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 设置严格的速率限制
    RateLimitConfig config;
    config.max_requests = 3;
    config.time_window_seconds = 60;
    config.burst_limit = 1;
    config.block_duration_seconds = 5;
    config.enable_progressive_delay = true;
    
    security_manager->setRateLimitConfig(config);
    
    std::string test_ip = "*************";
    std::string resource = "/api/v1/test";
    
    // 前3个请求应该通过
    for (int i = 0; i < 3; i++) {
        EXPECT_TRUE(security_manager->checkRateLimit(test_ip, resource));
        security_manager->recordAccess(test_ip, "test-agent", resource, true);
    }
    
    // 第4个请求应该被限制
    EXPECT_FALSE(security_manager->checkRateLimit(test_ip, resource));
    
    // 第5个请求应该触发临时阻断
    EXPECT_FALSE(security_manager->checkRateLimit(test_ip, resource));
    
    // 验证IP被临时阻断
    EXPECT_FALSE(security_manager->isIpAllowed(test_ip));
}

// 测试暴力破解检测
TEST_F(SecurityManagerTest, BruteForceDetection) {
    EXPECT_TRUE(security_manager->initialize());
    
    std::string test_ip = "*************";
    std::string resource = "/api/v1/auth/login";
    std::string user_agent = "test-agent";
    
    // 模拟多次失败的登录尝试
    for (int i = 0; i < 15; i++) {
        security_manager->recordAccess(test_ip, user_agent, resource, false, "testuser");
    }
    
    // 检查是否检测到暴力破解攻击并自动阻断
    EXPECT_FALSE(security_manager->isIpAllowed(test_ip));
    
    // 验证黑名单中包含该IP
    auto blacklist = security_manager->getBlacklist();
    EXPECT_TRUE(std::find(blacklist.begin(), blacklist.end(), test_ip) != blacklist.end());
}

// 测试可疑用户代理检测
TEST_F(SecurityManagerTest, SuspiciousUserAgentDetection) {
    EXPECT_TRUE(security_manager->initialize());
    
    std::string test_ip = "*************";
    std::string resource = "/api/v1/status";
    
    // 测试可疑的用户代理
    std::vector<std::string> suspicious_agents = {
        "",  // 空用户代理
        "bot",
        "crawler",
        "spider",
        "scanner",
        "sqlmap",
        "nikto",
        "nmap",
        "curl/7.68.0",
        "wget/1.20.3",
        "python-requests/2.25.1",
        "x"  // 过短的用户代理
    };
    
    for (const auto& agent : suspicious_agents) {
        // 记录访问
        security_manager->recordAccess(test_ip, agent, resource, true);
        
        // 检查是否检测到可疑活动
        bool is_suspicious = security_manager->detectSuspiciousActivity(test_ip, agent, resource);
        EXPECT_TRUE(is_suspicious) << "Failed to detect suspicious user agent: " << agent;
    }
}

// 测试安全事件记录
TEST_F(SecurityManagerTest, SecurityEventRecording) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 记录安全事件
    SecurityEvent event;
    event.timestamp = 1234567890;
    event.type = SecurityEventType::AUTHENTICATION_FAILED;
    event.ip_address = "*************";
    event.user_agent = "test-agent";
    event.username = "testuser";
    event.resource = "/api/v1/auth/login";
    event.details = "密码错误";
    event.severity = 6;
    
    security_manager->recordSecurityEvent(event);
    
    // 获取安全事件
    auto events = security_manager->getSecurityEvents(10);
    EXPECT_EQ(events.size(), 1);
    
    const auto& recorded_event = events[0];
    EXPECT_EQ(recorded_event.type, SecurityEventType::AUTHENTICATION_FAILED);
    EXPECT_EQ(recorded_event.ip_address, "*************");
    EXPECT_EQ(recorded_event.username, "testuser");
    EXPECT_EQ(recorded_event.details, "密码错误");
    EXPECT_EQ(recorded_event.severity, 6);
}

// 测试IP统计信息
TEST_F(SecurityManagerTest, IpStatistics) {
    EXPECT_TRUE(security_manager->initialize());
    
    std::string test_ip = "*************";
    std::string resource = "/api/v1/test";
    std::string user_agent = "test-agent";
    
    // 记录一些访问
    for (int i = 0; i < 5; i++) {
        security_manager->recordAccess(test_ip, user_agent, resource, i % 2 == 0); // 50%失败率
    }
    
    // 获取IP统计信息
    auto stats = security_manager->getIpStats(test_ip);
    ASSERT_NE(stats, nullptr);
    
    EXPECT_EQ(stats->ip_address, test_ip);
    EXPECT_EQ(stats->failed_attempts, 2); // 2次失败
    EXPECT_GT(stats->last_seen, 0);
    EXPECT_GT(stats->first_seen, 0);
    EXPECT_LE(stats->first_seen, stats->last_seen);
    
    // 获取所有IP统计信息
    auto all_stats = security_manager->getAllIpStats(10);
    EXPECT_EQ(all_stats.size(), 1);
    EXPECT_EQ(all_stats[0].ip_address, test_ip);
}

// 测试TLS证书生成（如果OpenSSL可用）
TEST_F(SecurityManagerTest, TlsCertificateGeneration) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 生成自签名证书
    bool cert_generated = security_manager->generateSelfSignedCertificate(
        cert_file, key_file, "localhost", 365);
    
    if (cert_generated) {
        // 验证证书文件是否存在
        EXPECT_TRUE(std::filesystem::exists(cert_file));
        EXPECT_TRUE(std::filesystem::exists(key_file));
        
        // 验证证书
        EXPECT_TRUE(security_manager->validateTlsCertificate(cert_file, key_file));
    } else {
        // 如果OpenSSL不可用，跳过测试
        GTEST_SKIP() << "OpenSSL not available for certificate generation";
    }
}

// 测试安全防护开关
TEST_F(SecurityManagerTest, SecurityToggle) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 默认应该启用
    EXPECT_TRUE(security_manager->isSecurityEnabled());
    
    // 禁用安全防护
    security_manager->setSecurityEnabled(false);
    EXPECT_FALSE(security_manager->isSecurityEnabled());
    
    // 禁用后所有IP都应该被允许
    EXPECT_TRUE(security_manager->isIpAllowed("********"));
    EXPECT_TRUE(security_manager->checkRateLimit("********"));
    
    // 重新启用
    security_manager->setSecurityEnabled(true);
    EXPECT_TRUE(security_manager->isSecurityEnabled());
}

// 测试数据清理功能
TEST_F(SecurityManagerTest, DataCleanup) {
    EXPECT_TRUE(security_manager->initialize());
    
    // 记录一些访问数据
    std::string test_ip = "*************";
    security_manager->recordAccess(test_ip, "test-agent", "/api/v1/test", true);
    
    // 验证数据存在
    auto stats = security_manager->getIpStats(test_ip);
    ASSERT_NE(stats, nullptr);
    
    // 手动触发清理（正常情况下这会在后台线程中进行）
    security_manager->cleanupExpiredData();
    
    // 由于数据是刚创建的，不应该被清理
    stats = security_manager->getIpStats(test_ip);
    EXPECT_NE(stats, nullptr);
}

// 性能测试
TEST_F(SecurityManagerTest, PerformanceTest) {
    EXPECT_TRUE(security_manager->initialize());
    
    const int num_requests = 1000;
    const int num_ips = 100;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 模拟大量并发请求
    for (int i = 0; i < num_requests; i++) {
        std::string ip = "192.168.1." + std::to_string(i % num_ips + 1);
        security_manager->isIpAllowed(ip);
        security_manager->checkRateLimit(ip);
        security_manager->recordAccess(ip, "test-agent", "/api/v1/test", true);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // 性能要求：1000个请求应该在1秒内完成
    EXPECT_LT(duration.count(), 1000) << "Performance test failed: " << duration.count() << "ms";
    
    // 验证统计数据
    auto all_stats = security_manager->getAllIpStats(num_ips);
    EXPECT_EQ(all_stats.size(), num_ips);
}

// main函数由GTest::gtest_main提供