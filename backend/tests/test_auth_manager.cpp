#include <gtest/gtest.h>
#include <api/auth_manager.h>
#include <core/logger.h>
#include <memory>

using namespace timing_server::api;

class AuthManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志系统
        auto& logger = timing_server::core::Logger::GetInstance();
        logger.Initialize();
        logger.SetLogLevel(timing_server::core::LogLevel::DEBUG);
        
        // 创建认证管理器
        auth_manager = std::make_shared<AuthManager>("test_secret_key", 3600, 86400);
        ASSERT_TRUE(auth_manager->initialize());
    }
    
    void TearDown() override {
        auth_manager.reset();
        timing_server::core::Logger::GetInstance().Shutdown();
    }
    
    std::shared_ptr<AuthManager> auth_manager;
};

TEST_F(AuthManagerTest, DefaultAdminCreation) {
    // 测试默认管理员用户是否创建成功
    auto user_info = auth_manager->getUserInfo("admin");
    ASSERT_NE(user_info, nullptr);
    EXPECT_EQ(user_info->username, "admin");
    EXPECT_EQ(user_info->role, UserRole::ADMINISTRATOR);
    EXPECT_TRUE(user_info->is_active);
}

TEST_F(AuthManagerTest, UserAuthentication) {
    // 测试用户认证
    auto tokens = auth_manager->authenticate("admin", "admin123", "127.0.0.1", "test-agent");
    EXPECT_FALSE(tokens.first.empty());
    EXPECT_FALSE(tokens.second.empty());
    
    // 测试错误密码
    auto invalid_tokens = auth_manager->authenticate("admin", "wrong_password", "127.0.0.1", "test-agent");
    EXPECT_TRUE(invalid_tokens.first.empty());
    EXPECT_TRUE(invalid_tokens.second.empty());
}

TEST_F(AuthManagerTest, TokenValidation) {
    // 获取有效令牌
    auto tokens = auth_manager->authenticate("admin", "admin123", "127.0.0.1", "test-agent");
    ASSERT_FALSE(tokens.first.empty());
    
    // 验证令牌
    auto token_info = auth_manager->validateToken(tokens.first);
    ASSERT_NE(token_info, nullptr);
    EXPECT_EQ(token_info->username, "admin");
    EXPECT_EQ(token_info->role, UserRole::ADMINISTRATOR);
    
    // 测试无效令牌
    auto invalid_token_info = auth_manager->validateToken("invalid_token");
    EXPECT_EQ(invalid_token_info, nullptr);
}

TEST_F(AuthManagerTest, PermissionCheck) {
    // 测试管理员权限
    EXPECT_TRUE(auth_manager->hasPermission("admin", Permission::READ_STATUS));
    EXPECT_TRUE(auth_manager->hasPermission("admin", Permission::WRITE_CONFIG));
    EXPECT_TRUE(auth_manager->hasPermission("admin", Permission::MANAGE_USERS));
    
    // 创建普通用户
    ASSERT_TRUE(auth_manager->createUser("viewer", "password", UserRole::VIEWER, "admin", "127.0.0.1"));
    
    // 测试查看者权限
    EXPECT_TRUE(auth_manager->hasPermission("viewer", Permission::READ_STATUS));
    EXPECT_FALSE(auth_manager->hasPermission("viewer", Permission::WRITE_CONFIG));
    EXPECT_FALSE(auth_manager->hasPermission("viewer", Permission::MANAGE_USERS));
}

TEST_F(AuthManagerTest, UserManagement) {
    // 创建用户
    ASSERT_TRUE(auth_manager->createUser("operator", "password", UserRole::OPERATOR, "admin", "127.0.0.1"));
    
    // 获取用户信息
    auto user_info = auth_manager->getUserInfo("operator");
    ASSERT_NE(user_info, nullptr);
    EXPECT_EQ(user_info->username, "operator");
    EXPECT_EQ(user_info->role, UserRole::OPERATOR);
    
    // 更新用户
    ASSERT_TRUE(auth_manager->updateUser("operator", "new_password", UserRole::ADMINISTRATOR, true, "admin", "127.0.0.1"));
    
    // 验证更新
    user_info = auth_manager->getUserInfo("operator");
    ASSERT_NE(user_info, nullptr);
    EXPECT_EQ(user_info->role, UserRole::ADMINISTRATOR);
    
    // 删除用户
    ASSERT_TRUE(auth_manager->deleteUser("operator", "admin", "127.0.0.1"));
    
    // 验证删除
    user_info = auth_manager->getUserInfo("operator");
    EXPECT_EQ(user_info, nullptr);
}

TEST_F(AuthManagerTest, UserList) {
    // 创建几个用户
    ASSERT_TRUE(auth_manager->createUser("user1", "password", UserRole::VIEWER, "admin", "127.0.0.1"));
    ASSERT_TRUE(auth_manager->createUser("user2", "password", UserRole::OPERATOR, "admin", "127.0.0.1"));
    
    // 获取用户列表
    auto result = auth_manager->getUserList(1, 10);
    EXPECT_GE(result.first.size(), 3); // admin + user1 + user2
    EXPECT_EQ(result.second, result.first.size()); // 总数应该等于返回的数量
}

TEST_F(AuthManagerTest, AuditLog) {
    // 记录审计日志
    auth_manager->logAudit("admin", "TEST_ACTION", "/test/resource", "127.0.0.1", "test-agent", true, "测试详情");
    
    // 获取审计日志
    auto result = auth_manager->getAuditLogs(1, 10);
    EXPECT_GT(result.first.size(), 0);
    
    // 检查最新的审计日志
    bool found_test_action = false;
    for (const auto& log : result.first) {
        if (log.action == "TEST_ACTION") {
            found_test_action = true;
            EXPECT_EQ(log.username, "admin");
            EXPECT_EQ(log.resource, "/test/resource");
            EXPECT_TRUE(log.success);
            break;
        }
    }
    EXPECT_TRUE(found_test_action);
}

TEST_F(AuthManagerTest, TokenRefresh) {
    // 获取初始令牌
    auto tokens = auth_manager->authenticate("admin", "admin123", "127.0.0.1", "test-agent");
    ASSERT_FALSE(tokens.second.empty());
    
    // 刷新令牌
    auto new_tokens = auth_manager->refreshToken(tokens.second, "127.0.0.1");
    EXPECT_FALSE(new_tokens.first.empty());
    EXPECT_FALSE(new_tokens.second.empty());
    
    // 验证新令牌
    auto token_info = auth_manager->validateToken(new_tokens.first);
    ASSERT_NE(token_info, nullptr);
    EXPECT_EQ(token_info->username, "admin");
}

TEST_F(AuthManagerTest, Logout) {
    // 登录获取令牌
    auto tokens = auth_manager->authenticate("admin", "admin123", "127.0.0.1", "test-agent");
    ASSERT_FALSE(tokens.first.empty());
    
    // 验证令牌有效
    auto token_info = auth_manager->validateToken(tokens.first);
    ASSERT_NE(token_info, nullptr);
    
    // 登出
    ASSERT_TRUE(auth_manager->logout(tokens.first, "127.0.0.1"));
    
    // 验证令牌仍然有效（JWT是无状态的，登出只是移除会话）
    token_info = auth_manager->validateToken(tokens.first);
    EXPECT_NE(token_info, nullptr); // JWT令牌本身仍然有效
}

TEST_F(AuthManagerTest, RolePermissions) {
    // 测试角色权限映射
    auto viewer_perms = AuthManager::getRolePermissions(UserRole::VIEWER);
    EXPECT_EQ(viewer_perms.size(), 2); // READ_STATUS, READ_LOGS
    
    auto operator_perms = AuthManager::getRolePermissions(UserRole::OPERATOR);
    EXPECT_EQ(operator_perms.size(), 4); // READ_STATUS, READ_LOGS, READ_CONFIG, WRITE_CONFIG
    
    auto admin_perms = AuthManager::getRolePermissions(UserRole::ADMINISTRATOR);
    EXPECT_EQ(admin_perms.size(), 7); // 所有权限
}

TEST_F(AuthManagerTest, StringConversions) {
    // 测试角色字符串转换
    EXPECT_EQ(AuthManager::roleToString(UserRole::VIEWER), "viewer");
    EXPECT_EQ(AuthManager::roleToString(UserRole::OPERATOR), "operator");
    EXPECT_EQ(AuthManager::roleToString(UserRole::ADMINISTRATOR), "administrator");
    
    EXPECT_EQ(AuthManager::stringToRole("viewer"), UserRole::VIEWER);
    EXPECT_EQ(AuthManager::stringToRole("operator"), UserRole::OPERATOR);
    EXPECT_EQ(AuthManager::stringToRole("administrator"), UserRole::ADMINISTRATOR);
    
    // 测试权限字符串转换
    EXPECT_EQ(AuthManager::permissionToString(Permission::READ_STATUS), "read_status");
    EXPECT_EQ(AuthManager::permissionToString(Permission::MANAGE_USERS), "manage_users");
}

// main函数由GTest::gtest_main提供