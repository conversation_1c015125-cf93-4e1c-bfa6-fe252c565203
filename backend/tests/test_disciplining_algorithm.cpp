#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/disciplining_algorithm.h"
#include <cmath>
#include <random>
#include <thread>
#include <chrono>

using namespace timing_server::core;
using namespace testing;

/**
 * @brief 测试辅助类：生成模拟时间测量数据
 */
class TimeMeasurementGenerator {
public:
    TimeMeasurementGenerator() : rng_(std::random_device{}()), 
                                noise_dist_(0.0, 10.0),  // 10ns噪声
                                freq_noise_dist_(0.0, 0.01) {} // 0.01ppm频率噪声
    
    /**
     * @brief 生成GNSS锁定过程的测量数据
     * @param time_offset_s 时间偏移（秒）
     * @param convergence_rate 收敛速率
     * @return 时间测量数据
     */
    TimeMeasurement GenerateGnssLockingMeasurement(double time_offset_s, double convergence_rate = 0.1) {
        TimeMeasurement measurement;
        measurement.timestamp_ns = GetCurrentTimestampNs() + static_cast<uint64_t>(time_offset_s * 1e9);
        
        // 模拟指数收敛过程
        double phase_error = 1000.0 * std::exp(-convergence_rate * time_offset_s); // 从1000ns开始收敛
        double frequency_error = 1.0 * std::exp(-convergence_rate * time_offset_s); // 从1ppm开始收敛
        
        // 添加噪声
        measurement.phase_offset_ns = phase_error + noise_dist_(rng_);
        measurement.frequency_offset_ppm = frequency_error + freq_noise_dist_(rng_);
        measurement.measurement_noise_ns = std::abs(noise_dist_(rng_));
        measurement.source = TimeSource::GNSS;
        measurement.is_valid = true;
        
        return measurement;
    }
    
    /**
     * @brief 生成铷钟老化数据
     * @param days_elapsed 经过的天数
     * @param aging_rate_ppm_per_day 老化率
     * @param temperature 温度
     * @param temp_coefficient 温度系数
     * @return 时间测量数据
     */
    TimeMeasurement GenerateRubidiumAgingMeasurement(double days_elapsed, 
                                                    double aging_rate_ppm_per_day = 0.001,
                                                    double temperature = 25.0,
                                                    double temp_coefficient = 0.0001) {
        TimeMeasurement measurement;
        measurement.timestamp_ns = GetCurrentTimestampNs() + static_cast<uint64_t>(days_elapsed * 24 * 3600 * 1e9);
        
        // 老化效应：线性漂移
        double aging_drift = aging_rate_ppm_per_day * days_elapsed;
        
        // 温度效应
        double temp_drift = temp_coefficient * (temperature - 25.0);
        
        measurement.frequency_offset_ppm = aging_drift + temp_drift + freq_noise_dist_(rng_);
        measurement.phase_offset_ns = noise_dist_(rng_); // 相位噪声
        measurement.measurement_noise_ns = std::abs(noise_dist_(rng_));
        measurement.source = TimeSource::RUBIDIUM;
        measurement.is_valid = true;
        
        return measurement;
    }
    
    /**
     * @brief 生成守时期间的测量数据
     * @param hours_elapsed 经过的小时数
     * @param initial_offset 初始频率偏移
     * @param drift_rate 漂移率
     * @return 时间测量数据
     */
    TimeMeasurement GenerateHoldoverMeasurement(double hours_elapsed,
                                               double initial_offset = 0.0,
                                               double drift_rate = 0.001) {
        TimeMeasurement measurement;
        measurement.timestamp_ns = GetCurrentTimestampNs() + static_cast<uint64_t>(hours_elapsed * 3600 * 1e9);
        
        // 守时期间的频率漂移
        measurement.frequency_offset_ppm = initial_offset + drift_rate * hours_elapsed + freq_noise_dist_(rng_);
        measurement.phase_offset_ns = noise_dist_(rng_);
        measurement.measurement_noise_ns = std::abs(noise_dist_(rng_));
        measurement.source = TimeSource::RUBIDIUM;
        measurement.is_valid = true;
        
        return measurement;
    }

private:
    std::mt19937 rng_;
    std::normal_distribution<double> noise_dist_;
    std::normal_distribution<double> freq_noise_dist_;
};

/**
 * @brief HybridDiscipliningAlgorithm单元测试类
 */
class HybridDiscipliningAlgorithmTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试配置
        config_.convergence_threshold_ns = 50.0;
        config_.convergence_time_s = 300; // 5分钟
        config_.phase_gain = 1.0;
        config_.frequency_gain = 0.1;
        config_.measurement_interval_ms = 1000;
        
        algorithm_ = std::make_unique<HybridDiscipliningAlgorithm>();
        generator_ = std::make_unique<TimeMeasurementGenerator>();
    }
    
    void TearDown() override {
        algorithm_.reset();
        generator_.reset();
    }

protected:
    DiscipliningParameters config_;
    std::unique_ptr<HybridDiscipliningAlgorithm> algorithm_;
    std::unique_ptr<TimeMeasurementGenerator> generator_;
};

/**
 * @brief 测试驯服算法初始化
 */
TEST_F(HybridDiscipliningAlgorithmTest, InitializationTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    auto status = algorithm_->GetStatus();
    EXPECT_EQ(status.state, DiscipliningState::ACQUIRING);
    EXPECT_EQ(status.algorithm, DiscipliningAlgorithmType::HYBRID);
    EXPECT_FALSE(status.is_converged);
    EXPECT_GT(status.loop_bandwidth_hz, 0.0);
    EXPECT_GT(status.time_constant_s, 0.0);
}

/**
 * @brief 测试PLL收敛过程
 */
TEST_F(HybridDiscipliningAlgorithmTest, PLLConvergenceTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 模拟GNSS锁定过程，生成收敛的测量数据
    const int num_measurements = 500;
    const double time_step = 1.0; // 1秒间隔
    
    for (int i = 0; i < num_measurements; ++i) {
        auto measurement = generator_->GenerateGnssLockingMeasurement(i * time_step, 0.01);
        EXPECT_TRUE(algorithm_->ProcessMeasurement(measurement));
        
        // 检查状态变化
        auto status = algorithm_->GetStatus();
        if (i > 100) { // 给算法一些时间进入跟踪状态
            EXPECT_TRUE(status.state == DiscipliningState::TRACKING || 
                       status.state == DiscipliningState::CONVERGED);
        }
    }
    
    // 验证最终收敛
    auto final_status = algorithm_->GetStatus();
    EXPECT_TRUE(final_status.is_converged);
    EXPECT_EQ(final_status.state, DiscipliningState::CONVERGED);
    EXPECT_LT(std::abs(final_status.current_phase_error_ns), config_.convergence_threshold_ns);
    
    // 验证频率校正值合理
    double freq_correction = algorithm_->GetFrequencyCorrection();
    EXPECT_LT(std::abs(freq_correction), 2.0); // 应该在合理范围内
    
    std::cout << "PLL收敛测试完成:" << std::endl;
    std::cout << "  收敛时间: " << final_status.convergence_time_s << " 秒" << std::endl;
    std::cout << "  最终相位误差: " << final_status.current_phase_error_ns << " ns" << std::endl;
    std::cout << "  最终频率误差: " << final_status.current_frequency_error_ppm << " ppm" << std::endl;
    std::cout << "  频率校正值: " << freq_correction << " ppm" << std::endl;
}

/**
 * @brief 测试FLL快速捕获
 */
TEST_F(HybridDiscipliningAlgorithmTest, FLLAcquisitionTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 设置为FLL模式
    algorithm_->SetAlgorithmType(DiscipliningAlgorithmType::FLL);
    
    // 生成大频率误差的测量数据
    TimeMeasurement measurement;
    measurement.timestamp_ns = GetCurrentTimestampNs();
    measurement.phase_offset_ns = 5000.0; // 5μs相位误差
    measurement.frequency_offset_ppm = 10.0; // 10ppm频率误差
    measurement.measurement_noise_ns = 10.0;
    measurement.source = TimeSource::GNSS;
    measurement.is_valid = true;
    
    // 处理多个测量点
    for (int i = 0; i < 100; ++i) {
        // 模拟频率误差逐渐减小
        measurement.frequency_offset_ppm = 10.0 * std::exp(-0.1 * i);
        measurement.timestamp_ns += 1000000000ULL; // 1秒间隔
        
        EXPECT_TRUE(algorithm_->ProcessMeasurement(measurement));
    }
    
    // 验证FLL能够快速减小频率误差
    double freq_correction = algorithm_->GetFrequencyCorrection();
    EXPECT_LT(std::abs(freq_correction), 1.0); // 频率校正应该合理
    
    auto status = algorithm_->GetStatus();
    EXPECT_LT(std::abs(status.current_frequency_error_ppm), 1.0);
    
    std::cout << "FLL快速捕获测试完成:" << std::endl;
    std::cout << "  最终频率误差: " << status.current_frequency_error_ppm << " ppm" << std::endl;
    std::cout << "  频率校正值: " << freq_correction << " ppm" << std::endl;
}

/**
 * @brief 测试混合算法自动切换
 */
TEST_F(HybridDiscipliningAlgorithmTest, HybridSwitchingTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 第一阶段：大误差，应该使用FLL
    TimeMeasurement large_error_measurement;
    large_error_measurement.timestamp_ns = GetCurrentTimestampNs();
    large_error_measurement.phase_offset_ns = 2000.0; // 2μs
    large_error_measurement.frequency_offset_ppm = 5.0; // 5ppm
    large_error_measurement.measurement_noise_ns = 10.0;
    large_error_measurement.source = TimeSource::GNSS;
    large_error_measurement.is_valid = true;
    
    EXPECT_TRUE(algorithm_->ProcessMeasurement(large_error_measurement));
    auto status = algorithm_->GetStatus();
    EXPECT_EQ(status.algorithm, DiscipliningAlgorithmType::FLL);
    
    // 第二阶段：小误差，应该切换到PLL
    TimeMeasurement small_error_measurement;
    small_error_measurement.timestamp_ns = GetCurrentTimestampNs() + 1000000000ULL;
    small_error_measurement.phase_offset_ns = 100.0; // 100ns
    small_error_measurement.frequency_offset_ppm = 0.1; // 0.1ppm
    small_error_measurement.measurement_noise_ns = 5.0;
    small_error_measurement.source = TimeSource::GNSS;
    small_error_measurement.is_valid = true;
    
    EXPECT_TRUE(algorithm_->ProcessMeasurement(small_error_measurement));
    status = algorithm_->GetStatus();
    EXPECT_EQ(status.algorithm, DiscipliningAlgorithmType::PLL);
    
    std::cout << "混合算法切换测试完成" << std::endl;
}

/**
 * @brief 测试环路带宽调整
 */
TEST_F(HybridDiscipliningAlgorithmTest, LoopBandwidthTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 测试设置环路带宽
    double test_bandwidth = 0.5;
    algorithm_->SetLoopBandwidth(test_bandwidth);
    EXPECT_DOUBLE_EQ(algorithm_->GetLoopBandwidth(), test_bandwidth);
    
    auto status = algorithm_->GetStatus();
    EXPECT_DOUBLE_EQ(status.loop_bandwidth_hz, test_bandwidth);
    EXPECT_GT(status.time_constant_s, 0.0);
    
    // 测试带宽限制
    algorithm_->SetLoopBandwidth(100.0); // 超出上限
    EXPECT_LE(algorithm_->GetLoopBandwidth(), 10.0);
    
    algorithm_->SetLoopBandwidth(0.0001); // 超出下限
    EXPECT_GE(algorithm_->GetLoopBandwidth(), 0.001);
    
    std::cout << "环路带宽测试完成" << std::endl;
}

/**
 * @brief 测试算法重置功能
 */
TEST_F(HybridDiscipliningAlgorithmTest, ResetTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 处理一些测量数据
    auto measurement = generator_->GenerateGnssLockingMeasurement(10.0);
    EXPECT_TRUE(algorithm_->ProcessMeasurement(measurement));
    
    // 验证状态已改变
    auto status_before = algorithm_->GetStatus();
    EXPECT_NE(status_before.current_phase_error_ns, 0.0);
    
    // 重置算法
    algorithm_->Reset();
    
    // 验证状态已重置
    auto status_after = algorithm_->GetStatus();
    EXPECT_EQ(status_after.state, DiscipliningState::INITIALIZING);
    EXPECT_EQ(status_after.current_phase_error_ns, 0.0);
    EXPECT_EQ(status_after.current_frequency_error_ppm, 0.0);
    EXPECT_FALSE(status_after.is_converged);
    EXPECT_EQ(status_after.convergence_time_s, 0);
    
    std::cout << "算法重置测试完成" << std::endl;
}

/**
 * @brief RubidiumLearningAlgorithm单元测试类
 */
class RubidiumLearningAlgorithmTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.max_holdover_hours = 24;
        config_.frequency_drift_limit_ppm = 1.0;
        config_.learning_duration_hours = 72;
        config_.enable_temperature_compensation = true;
        
        algorithm_ = std::make_unique<RubidiumLearningAlgorithm>();
        generator_ = std::make_unique<TimeMeasurementGenerator>();
    }
    
    void TearDown() override {
        algorithm_.reset();
        generator_.reset();
    }

protected:
    HoldoverParameters config_;
    std::unique_ptr<RubidiumLearningAlgorithm> algorithm_;
    std::unique_ptr<TimeMeasurementGenerator> generator_;
};

/**
 * @brief 测试铷钟学习算法初始化
 */
TEST_F(RubidiumLearningAlgorithmTest, InitializationTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    auto learning_data = algorithm_->GetLearningData();
    EXPECT_EQ(learning_data.sample_count, 0);
    EXPECT_EQ(learning_data.learning_duration_hours, 0);
    EXPECT_EQ(learning_data.aging_rate_ppm_per_day, 0.0);
    EXPECT_EQ(learning_data.temperature_coefficient, 0.0);
    EXPECT_EQ(learning_data.confidence_level, 0.0);
}

/**
 * @brief 测试老化特性学习
 */
TEST_F(RubidiumLearningAlgorithmTest, AgingLearningTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 模拟30天的老化数据，老化率为0.001 ppm/天
    const double aging_rate = 0.001;
    const int num_days = 30;
    const double temperature = 25.0;
    
    uint64_t base_time = GetCurrentTimestampNs();
    
    for (int day = 0; day < num_days; ++day) {
        for (int hour = 0; hour < 24; hour += 4) { // 每4小时一个样本
            double days_elapsed = day + hour / 24.0;
            uint64_t timestamp = base_time + static_cast<uint64_t>(days_elapsed * 24 * 3600 * 1e9);
            
            // 生成带有老化趋势的频率偏移
            double frequency_offset = aging_rate * days_elapsed;
            
            EXPECT_TRUE(algorithm_->AddSample(frequency_offset, temperature, timestamp));
        }
    }
    
    // 验证学习结果
    auto learning_data = algorithm_->GetLearningData();
    EXPECT_GT(learning_data.sample_count, 100);
    EXPECT_GT(learning_data.learning_duration_hours, 24);
    
    // 验证老化率学习精度（允许10%误差）
    double learned_aging_rate = learning_data.aging_rate_ppm_per_day;
    double error_percentage = std::abs(learned_aging_rate - aging_rate) / aging_rate * 100.0;
    EXPECT_LT(error_percentage, 10.0);
    
    // 验证置信度
    EXPECT_GT(learning_data.confidence_level, 0.8);
    
    std::cout << "老化特性学习测试完成:" << std::endl;
    std::cout << "  期望老化率: " << aging_rate << " ppm/天" << std::endl;
    std::cout << "  学习老化率: " << learned_aging_rate << " ppm/天" << std::endl;
    std::cout << "  误差百分比: " << error_percentage << "%" << std::endl;
    std::cout << "  置信度: " << learning_data.confidence_level << std::endl;
}

/**
 * @brief 测试温度特性学习
 */
TEST_F(RubidiumLearningAlgorithmTest, TemperatureLearningTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 模拟不同温度下的频率偏移，温度系数为0.0001 ppm/°C
    const double temp_coefficient = 0.0001;
    const double reference_temp = 25.0;
    
    uint64_t base_time = GetCurrentTimestampNs();
    
    // 在20°C到30°C范围内生成数据
    for (int i = 0; i < 100; ++i) {
        double temperature = 20.0 + (i % 11); // 20°C到30°C
        double frequency_offset = temp_coefficient * (temperature - reference_temp);
        uint64_t timestamp = base_time + i * 3600ULL * 1000000000ULL; // 每小时一个样本
        
        EXPECT_TRUE(algorithm_->AddSample(frequency_offset, temperature, timestamp));
    }
    
    // 验证学习结果
    auto learning_data = algorithm_->GetLearningData();
    EXPECT_GT(learning_data.sample_count, 50);
    
    // 验证温度系数学习精度（允许20%误差，因为温度效应较小）
    double learned_temp_coefficient = learning_data.temperature_coefficient;
    double error_percentage = std::abs(learned_temp_coefficient - temp_coefficient) / temp_coefficient * 100.0;
    EXPECT_LT(error_percentage, 20.0);
    
    // 测试温度补偿计算
    double compensation_30c = algorithm_->GetTemperatureCompensation(30.0);
    double expected_compensation = temp_coefficient * (30.0 - reference_temp);
    EXPECT_NEAR(compensation_30c, expected_compensation, 0.0001);
    
    std::cout << "温度特性学习测试完成:" << std::endl;
    std::cout << "  期望温度系数: " << temp_coefficient << " ppm/°C" << std::endl;
    std::cout << "  学习温度系数: " << learned_temp_coefficient << " ppm/°C" << std::endl;
    std::cout << "  误差百分比: " << error_percentage << "%" << std::endl;
}

/**
 * @brief 测试频率偏移预测
 */
TEST_F(RubidiumLearningAlgorithmTest, FrequencyPredictionTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 添加一些学习数据
    const double aging_rate = 0.002;
    const double temp_coefficient = 0.0002;
    const double reference_temp = 25.0;
    
    uint64_t base_time = GetCurrentTimestampNs();
    
    for (int i = 0; i < 200; ++i) {
        double days_elapsed = i / 8.0; // 每天8个样本
        double temperature = 25.0 + 5.0 * std::sin(i * 0.1); // 温度变化
        
        double frequency_offset = aging_rate * days_elapsed + 
                                temp_coefficient * (temperature - reference_temp);
        
        uint64_t timestamp = base_time + static_cast<uint64_t>(days_elapsed * 24 * 3600 * 1e9);
        
        EXPECT_TRUE(algorithm_->AddSample(frequency_offset, temperature, timestamp));
    }
    
    // 测试预测功能
    double test_temperature = 30.0;
    double test_time_hours = 48.0; // 2天后
    
    double predicted_offset = algorithm_->PredictFrequencyOffset(test_temperature, test_time_hours);
    
    // 计算期望值
    double expected_aging = aging_rate * (test_time_hours / 24.0);
    double expected_temp = temp_coefficient * (test_temperature - reference_temp);
    double expected_total = expected_aging + expected_temp;
    
    // 验证预测精度（允许15%误差）
    double prediction_error = std::abs(predicted_offset - expected_total) / expected_total * 100.0;
    EXPECT_LT(prediction_error, 15.0);
    
    std::cout << "频率偏移预测测试完成:" << std::endl;
    std::cout << "  期望偏移: " << expected_total << " ppm" << std::endl;
    std::cout << "  预测偏移: " << predicted_offset << " ppm" << std::endl;
    std::cout << "  预测误差: " << prediction_error << "%" << std::endl;
}

/**
 * @brief 测试学习数据保存和加载
 */
TEST_F(RubidiumLearningAlgorithmTest, SaveLoadTest) {
    EXPECT_TRUE(algorithm_->Initialize(config_));
    
    // 添加一些学习数据
    uint64_t base_time = GetCurrentTimestampNs();
    for (int i = 0; i < 50; ++i) {
        double frequency_offset = 0.001 * i;
        double temperature = 25.0 + i * 0.1;
        uint64_t timestamp = base_time + i * 3600ULL * 1000000000ULL;
        
        EXPECT_TRUE(algorithm_->AddSample(frequency_offset, temperature, timestamp));
    }
    
    // 获取原始学习数据
    auto original_data = algorithm_->GetLearningData();
    
    // 保存数据
    std::string test_filename = "/tmp/test_rubidium_learning.dat";
    EXPECT_TRUE(algorithm_->SaveLearningData(test_filename));
    
    // 创建新的算法实例并加载数据
    auto new_algorithm = std::make_unique<RubidiumLearningAlgorithm>();
    EXPECT_TRUE(new_algorithm->Initialize(config_));
    EXPECT_TRUE(new_algorithm->LoadLearningData(test_filename));
    
    // 验证加载的数据
    auto loaded_data = new_algorithm->GetLearningData();
    EXPECT_EQ(loaded_data.sample_count, original_data.sample_count);
    EXPECT_EQ(loaded_data.learning_duration_hours, original_data.learning_duration_hours);
    EXPECT_DOUBLE_EQ(loaded_data.aging_rate_ppm_per_day, original_data.aging_rate_ppm_per_day);
    EXPECT_DOUBLE_EQ(loaded_data.temperature_coefficient, original_data.temperature_coefficient);
    
    // 清理测试文件
    std::remove(test_filename.c_str());
    
    std::cout << "学习数据保存加载测试完成" << std::endl;
}

/**
 * @brief HoldoverPredictionAlgorithm单元测试类
 */
class HoldoverPredictionAlgorithmTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置学习数据
        learning_data_.aging_rate_ppm_per_day = 0.001;
        learning_data_.temperature_coefficient = 0.0001;
        learning_data_.temperature_reference = 25.0;
        learning_data_.learning_duration_hours = 168; // 7天
        learning_data_.sample_count = 1000;
        learning_data_.confidence_level = 0.9;
        learning_data_.prediction_accuracy = 0.0001;
        
        // 设置守时配置
        config_.max_holdover_hours = 24;
        config_.frequency_drift_limit_ppm = 1.0;
        config_.learning_duration_hours = 72;
        config_.enable_temperature_compensation = true;
        
        algorithm_ = std::make_unique<HoldoverPredictionAlgorithm>();
        generator_ = std::make_unique<TimeMeasurementGenerator>();
    }
    
    void TearDown() override {
        algorithm_.reset();
        generator_.reset();
    }

protected:
    RubidiumLearningData learning_data_;
    HoldoverParameters config_;
    std::unique_ptr<HoldoverPredictionAlgorithm> algorithm_;
    std::unique_ptr<TimeMeasurementGenerator> generator_;
};

/**
 * @brief 测试守时预测算法初始化
 */
TEST_F(HoldoverPredictionAlgorithmTest, InitializationTest) {
    EXPECT_TRUE(algorithm_->Initialize(learning_data_, config_));
    
    // 验证初始状态
    auto prediction = algorithm_->GetCurrentPrediction();
    EXPECT_EQ(prediction.predicted_frequency_drift_ppm, 0.0);
    EXPECT_EQ(prediction.predicted_phase_drift_ns, 0.0);
    EXPECT_EQ(prediction.prediction_horizon_hours, 0);
}

/**
 * @brief 测试守时启动和预测
 */
TEST_F(HoldoverPredictionAlgorithmTest, HoldoverStartTest) {
    EXPECT_TRUE(algorithm_->Initialize(learning_data_, config_));
    
    double initial_freq_offset = 0.1; // 0.1ppm初始偏移
    double initial_temperature = 25.0;
    
    EXPECT_TRUE(algorithm_->StartHoldover(initial_freq_offset, initial_temperature));
    
    // 模拟时间流逝
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // 更新温度
    double new_temperature = 30.0;
    algorithm_->UpdateTemperature(new_temperature);
    
    // 获取预测结果
    auto prediction = algorithm_->GetCurrentPrediction();
    
    // 验证预测结果合理性
    EXPECT_GT(prediction.prediction_timestamp_ns, 0);
    EXPECT_GE(prediction.prediction_horizon_hours, 0);
    
    // 验证温度补偿
    double expected_temp_compensation = learning_data_.temperature_coefficient * 
                                       (new_temperature - learning_data_.temperature_reference);
    EXPECT_NEAR(prediction.temperature_compensation_ppm, expected_temp_compensation, 0.0001);
    
    // 验证频率和相位校正
    double freq_correction = algorithm_->GetFrequencyCorrection();
    double phase_correction = algorithm_->GetPhaseCorrection();
    
    EXPECT_NE(freq_correction, 0.0); // 应该有校正值
    EXPECT_LT(std::abs(freq_correction), 1.0); // 校正值应该合理
    
    std::cout << "守时启动测试完成:" << std::endl;
    std::cout << "  预测频率漂移: " << prediction.predicted_frequency_drift_ppm << " ppm" << std::endl;
    std::cout << "  预测相位漂移: " << prediction.predicted_phase_drift_ns << " ns" << std::endl;
    std::cout << "  温度补偿: " << prediction.temperature_compensation_ppm << " ppm" << std::endl;
    std::cout << "  频率校正: " << freq_correction << " ppm" << std::endl;
}

/**
 * @brief 测试长期守时预测精度
 */
TEST_F(HoldoverPredictionAlgorithmTest, LongTermPredictionTest) {
    EXPECT_TRUE(algorithm_->Initialize(learning_data_, config_));
    EXPECT_TRUE(algorithm_->StartHoldover(0.0, 25.0));
    
    // 测试不同时间点的预测精度
    std::vector<double> test_hours = {1.0, 6.0, 12.0, 24.0};
    
    for (double hours : test_hours) {
        double accuracy = algorithm_->GetPredictionAccuracy(hours);
        
        // 验证精度随时间降低
        EXPECT_GT(accuracy, 0.0);
        
        // 24小时内精度应该在可接受范围内
        if (hours <= 24.0) {
            EXPECT_LT(accuracy, 1000.0); // 1μs以内
        }
        
        std::cout << "  " << hours << "小时预测精度: " << accuracy << " ns" << std::endl;
    }
}

/**
 * @brief 测试守时停止
 */
TEST_F(HoldoverPredictionAlgorithmTest, HoldoverStopTest) {
    EXPECT_TRUE(algorithm_->Initialize(learning_data_, config_));
    EXPECT_TRUE(algorithm_->StartHoldover(0.1, 25.0));
    
    // 运行一段时间
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // 停止守时
    algorithm_->StopHoldover();
    
    // 验证停止后的状态
    double freq_correction = algorithm_->GetFrequencyCorrection();
    double phase_correction = algorithm_->GetPhaseCorrection();
    
    EXPECT_EQ(freq_correction, 0.0);
    EXPECT_EQ(phase_correction, 0.0);
    
    std::cout << "守时停止测试完成" << std::endl;
}

/**
 * @brief ClockDiscipliningManager集成测试类
 */
class ClockDiscipliningManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置完整的授时配置
        config_.discipline.convergence_threshold_ns = 50.0;
        config_.discipline.convergence_time_s = 300;
        config_.discipline.phase_gain = 1.0;
        config_.discipline.frequency_gain = 0.1;
        config_.discipline.measurement_interval_ms = 1000;
        
        config_.holdover.max_holdover_hours = 24;
        config_.holdover.frequency_drift_limit_ppm = 1.0;
        config_.holdover.learning_duration_hours = 72;
        config_.holdover.enable_temperature_compensation = true;
        
        manager_ = std::make_unique<ClockDiscipliningManager>();
        generator_ = std::make_unique<TimeMeasurementGenerator>();
    }
    
    void TearDown() override {
        manager_.reset();
        generator_.reset();
    }

protected:
    TimingConfig config_;
    std::unique_ptr<ClockDiscipliningManager> manager_;
    std::unique_ptr<TimeMeasurementGenerator> generator_;
};

/**
 * @brief 测试驯服管理器初始化
 */
TEST_F(ClockDiscipliningManagerTest, InitializationTest) {
    EXPECT_TRUE(manager_->Initialize(config_));
    
    auto status = manager_->GetDiscipliningStatus();
    EXPECT_EQ(status.state, DiscipliningState::INITIALIZING);
    EXPECT_FALSE(status.is_converged);
    
    auto learning_data = manager_->GetRubidiumLearningData();
    EXPECT_EQ(learning_data.sample_count, 0);
    
    auto prediction = manager_->GetHoldoverPrediction();
    EXPECT_EQ(prediction.prediction_horizon_hours, 0);
}

/**
 * @brief 测试GNSS驯服到守时的完整流程
 */
TEST_F(ClockDiscipliningManagerTest, FullDiscipliningCycleTest) {
    EXPECT_TRUE(manager_->Initialize(config_));
    
    // 设置状态变化回调
    bool state_changed = false;
    DiscipliningState last_state = DiscipliningState::INITIALIZING;
    
    manager_->SetStatusChangeCallback([&](DiscipliningState old_state, DiscipliningState new_state) {
        state_changed = true;
        last_state = new_state;
        std::cout << "状态变化: " << static_cast<int>(old_state) 
                  << " -> " << static_cast<int>(new_state) << std::endl;
    });
    
    // 第一阶段：启动GNSS驯服
    EXPECT_TRUE(manager_->StartGnssDisciplining());
    EXPECT_TRUE(state_changed);
    EXPECT_EQ(last_state, DiscipliningState::ACQUIRING);
    
    // 第二阶段：处理GNSS测量数据直到收敛
    const int num_measurements = 200;
    for (int i = 0; i < num_measurements; ++i) {
        auto measurement = generator_->GenerateGnssLockingMeasurement(i * 1.0, 0.02);
        EXPECT_TRUE(manager_->ProcessTimeMeasurement(measurement));
        
        // 同时更新铷钟温度
        double temperature = 25.0 + 2.0 * std::sin(i * 0.1);
        manager_->UpdateRubidiumTemperature(temperature);
    }
    
    // 验证驯服收敛
    EXPECT_TRUE(manager_->IsDiscipliningConverged());
    
    auto disciplining_status = manager_->GetDiscipliningStatus();
    EXPECT_EQ(disciplining_status.state, DiscipliningState::CONVERGED);
    
    // 验证学习数据积累
    auto learning_data = manager_->GetRubidiumLearningData();
    EXPECT_GT(learning_data.sample_count, 100);
    
    // 第三阶段：切换到守时模式
    EXPECT_TRUE(manager_->StartHoldover());
    EXPECT_EQ(last_state, DiscipliningState::HOLDOVER);
    
    // 验证守时预测
    auto prediction = manager_->GetHoldoverPrediction();
    EXPECT_GT(prediction.prediction_timestamp_ns, 0);
    
    // 验证校正值
    double freq_correction = manager_->GetFrequencyCorrection();
    double phase_correction = manager_->GetPhaseCorrection();
    
    EXPECT_NE(freq_correction, 0.0);
    EXPECT_LT(std::abs(freq_correction), 2.0);
    
    // 第四阶段：停止守时
    manager_->StopHoldover();
    
    std::cout << "完整驯服周期测试完成:" << std::endl;
    std::cout << "  学习样本数: " << learning_data.sample_count << std::endl;
    std::cout << "  老化率: " << learning_data.aging_rate_ppm_per_day << " ppm/天" << std::endl;
    std::cout << "  温度系数: " << learning_data.temperature_coefficient << " ppm/°C" << std::endl;
    std::cout << "  最终频率校正: " << freq_correction << " ppm" << std::endl;
}

/**
 * @brief 性能测试：大量测量数据处理
 */
TEST_F(ClockDiscipliningManagerTest, PerformanceTest) {
    EXPECT_TRUE(manager_->Initialize(config_));
    EXPECT_TRUE(manager_->StartGnssDisciplining());
    
    const int num_measurements = 10000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_measurements; ++i) {
        auto measurement = generator_->GenerateGnssLockingMeasurement(i * 0.1, 0.01);
        EXPECT_TRUE(manager_->ProcessTimeMeasurement(measurement));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    double avg_processing_time = duration.count() / static_cast<double>(num_measurements);
    
    // 验证处理性能（每个测量应该在100μs内完成）
    EXPECT_LT(avg_processing_time, 100.0);
    
    std::cout << "性能测试完成:" << std::endl;
    std::cout << "  处理测量数: " << num_measurements << std::endl;
    std::cout << "  总耗时: " << duration.count() << " μs" << std::endl;
    std::cout << "  平均处理时间: " << avg_processing_time << " μs/测量" << std::endl;
}

// main函数由GTest::gtest_main提供