#include <gtest/gtest.h>
#include "core/config_manager.h"
#include <filesystem>
#include <fstream>
#include <thread>
#include <chrono>

using namespace timing_server::core;

class ConfigManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建临时测试目录
        test_dir_ = std::filesystem::temp_directory_path() / "timing_server_test";
        std::filesystem::create_directories(test_dir_);
        
        // 设置测试配置文件路径
        test_config_file_ = test_dir_ / "test_config.json";
        
        // 创建配置管理器
        config_manager_ = std::make_unique<ConfigManager>(test_config_file_.string());
    }
    
    void TearDown() override {
        // 清理配置管理器
        config_manager_.reset();
        
        // 删除测试目录
        std::filesystem::remove_all(test_dir_);
    }
    
    void CreateTestConfigFile(const std::string& content) {
        std::ofstream file(test_config_file_);
        file << content;
        file.close();
    }
    
    std::string ReadTestConfigFile() {
        std::ifstream file(test_config_file_);
        if (!file.is_open()) return "";
        
        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();
        return content;
    }

protected:
    std::filesystem::path test_dir_;
    std::filesystem::path test_config_file_;
    std::unique_ptr<ConfigManager> config_manager_;
};

// 基本功能测试

TEST_F(ConfigManagerTest, InitializeWithoutConfigFile) {
    // 配置文件不存在时应该创建默认配置
    EXPECT_TRUE(config_manager_->Initialize());
    EXPECT_TRUE(std::filesystem::exists(test_config_file_));
    
    // 验证默认配置
    TimingConfig config = config_manager_->GetConfig();
    EXPECT_EQ(config.priorities.priorities.at(TimeSource::GNSS), 1);
    EXPECT_EQ(config.priorities.priorities.at(TimeSource::RUBIDIUM), 2);
    EXPECT_TRUE(config.priorities.auto_failover);
    EXPECT_DOUBLE_EQ(config.discipline.convergence_threshold_ns, 50.0);
    EXPECT_EQ(config.holdover.max_holdover_hours, 24);
}

TEST_F(ConfigManagerTest, InitializeWithExistingConfigFile) {
    // 创建测试配置文件
    std::string test_config = R"({
        "priorities": {
            "auto_failover": false,
            "failover_delay_ms": 10000,
            "source_priorities": {
                "GNSS": 2,
                "RUBIDIUM": 1
            }
        },
        "discipline": {
            "convergence_threshold_ns": 100.0,
            "convergence_time_s": 600,
            "phase_gain": 0.2,
            "frequency_gain": 0.02,
            "measurement_interval_ms": 2000
        },
        "holdover": {
            "max_holdover_hours": 48,
            "frequency_drift_limit_ppm": 2.0,
            "learning_duration_hours": 144,
            "enable_temperature_compensation": false
        },
        "alarms": {
            "phase_offset_warning_ns": 200.0,
            "phase_offset_critical_ns": 1000.0,
            "frequency_offset_warning_ppm": 0.02,
            "frequency_offset_critical_ppm": 0.2,
            "gnss_satellites_warning": 8,
            "gnss_satellites_critical": 5,
            "gnss_snr_warning_db": -140.0,
            "gnss_snr_critical_db": -145.0,
            "cpu_usage_warning": 70.0,
            "memory_usage_warning": 70.0,
            "temperature_warning": 65.0,
            "temperature_critical": 70.0
        },
        "config_version": "1.1.0",
        "last_modified": 1640995200000000000
    })";
    
    CreateTestConfigFile(test_config);
    
    EXPECT_TRUE(config_manager_->Initialize());
    
    // 验证加载的配置
    TimingConfig config = config_manager_->GetConfig();
    EXPECT_FALSE(config.priorities.auto_failover);
    EXPECT_EQ(config.priorities.failover_delay_ms, 10000);
    EXPECT_DOUBLE_EQ(config.discipline.convergence_threshold_ns, 100.0);
    EXPECT_EQ(config.holdover.max_holdover_hours, 48);
    EXPECT_FALSE(config.holdover.enable_temperature_compensation);
}

TEST_F(ConfigManagerTest, SaveAndLoadConfig) {
    EXPECT_TRUE(config_manager_->Initialize());
    
    // 修改配置
    TimingConfig config = config_manager_->GetConfig();
    config.discipline.convergence_threshold_ns = 75.0;
    config.holdover.max_holdover_hours = 36;
    config.priorities.auto_failover = false;
    
    // 更新配置
    ConfigValidationResult result = config_manager_->UpdateConfig(config);
    EXPECT_TRUE(result.is_valid);
    
    // 创建新的配置管理器实例来验证持久化
    auto new_manager = std::make_unique<ConfigManager>(test_config_file_.string());
    EXPECT_TRUE(new_manager->Initialize());
    
    TimingConfig loaded_config = new_manager->GetConfig();
    EXPECT_DOUBLE_EQ(loaded_config.discipline.convergence_threshold_ns, 75.0);
    EXPECT_EQ(loaded_config.holdover.max_holdover_hours, 36);
    EXPECT_FALSE(loaded_config.priorities.auto_failover);
}

// 配置验证测试

TEST_F(ConfigManagerTest, ValidateDefaultConfig) {
    TimingConfig default_config = ConfigManager::GetDefaultConfig();
    ConfigValidationResult result = ConfigManager::ValidateConfig(default_config);
    
    EXPECT_TRUE(result.is_valid);
    EXPECT_TRUE(result.errors.empty());
}

TEST_F(ConfigManagerTest, ValidateInvalidConfig) {
    TimingConfig invalid_config = ConfigManager::GetDefaultConfig();
    
    // 设置无效值
    invalid_config.discipline.convergence_threshold_ns = -10.0; // 负数
    invalid_config.holdover.max_holdover_hours = 0; // 零值
    invalid_config.alarms.phase_offset_critical_ns = 50.0; // 小于警告阈值
    invalid_config.alarms.phase_offset_warning_ns = 100.0;
    
    ConfigValidationResult result = ConfigManager::ValidateConfig(invalid_config);
    
    EXPECT_FALSE(result.is_valid);
    EXPECT_FALSE(result.errors.empty());
    
    // 检查特定错误
    std::string summary = result.GetSummary();
    EXPECT_NE(summary.find("收敛阈值"), std::string::npos);
    EXPECT_NE(summary.find("最大守时时间"), std::string::npos);
    EXPECT_NE(summary.find("严重阈值必须大于警告阈值"), std::string::npos);
}

TEST_F(ConfigManagerTest, ValidatePriorityConflicts) {
    TimingConfig config = ConfigManager::GetDefaultConfig();
    
    // 设置重复的优先级
    config.priorities.priorities[TimeSource::GNSS] = 1;
    config.priorities.priorities[TimeSource::RUBIDIUM] = 1; // 重复优先级
    
    ConfigValidationResult result = ConfigManager::ValidateConfig(config);
    
    EXPECT_FALSE(result.is_valid);
    EXPECT_NE(result.GetSummary().find("优先级 1 被多个时间源使用"), std::string::npos);
}

// 回调机制测试

TEST_F(ConfigManagerTest, ConfigChangeCallback) {
    EXPECT_TRUE(config_manager_->Initialize());
    
    bool callback_called = false;
    TimingConfig old_config_received;
    TimingConfig new_config_received;
    
    // 注册回调
    uint32_t callback_id = config_manager_->RegisterChangeCallback(
        [&](const TimingConfig& old_config, const TimingConfig& new_config) {
            callback_called = true;
            old_config_received = old_config;
            new_config_received = new_config;
        });
    
    // 更新配置
    TimingConfig config = config_manager_->GetConfig();
    double old_threshold = config.discipline.convergence_threshold_ns;
    config.discipline.convergence_threshold_ns = 123.45;
    
    ConfigValidationResult result = config_manager_->UpdateConfig(config);
    EXPECT_TRUE(result.is_valid);
    
    // 验证回调被调用
    EXPECT_TRUE(callback_called);
    EXPECT_DOUBLE_EQ(old_config_received.discipline.convergence_threshold_ns, old_threshold);
    EXPECT_DOUBLE_EQ(new_config_received.discipline.convergence_threshold_ns, 123.45);
    
    // 取消注册回调
    config_manager_->UnregisterChangeCallback(callback_id);
    
    // 再次更新配置
    callback_called = false;
    config.discipline.convergence_threshold_ns = 67.89;
    result = config_manager_->UpdateConfig(config);
    EXPECT_TRUE(result.is_valid);
    
    // 验证回调未被调用
    EXPECT_FALSE(callback_called);
}

// 热重载测试

TEST_F(ConfigManagerTest, HotReload) {
    EXPECT_TRUE(config_manager_->Initialize());
    
    bool callback_called = false;
    config_manager_->RegisterChangeCallback(
        [&](const TimingConfig& old_config, const TimingConfig& new_config) {
            callback_called = true;
        });
    
    // 启用热重载
    config_manager_->EnableHotReload(true);
    
    // 等待一小段时间确保监控线程启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 修改配置文件
    std::string modified_config = R"({
        "priorities": {
            "auto_failover": true,
            "failover_delay_ms": 5000,
            "source_priorities": {
                "GNSS": 1,
                "RUBIDIUM": 2,
                "RTC": 5,
                "EXTERNAL_PPS": 3,
                "EXTERNAL_10MHZ": 4,
                "PHC": 6,
                "SYSTEM_CLOCK": 7
            }
        },
        "discipline": {
            "convergence_threshold_ns": 999.0,
            "convergence_time_s": 300,
            "phase_gain": 0.1,
            "frequency_gain": 0.01,
            "measurement_interval_ms": 1000
        },
        "holdover": {
            "max_holdover_hours": 24,
            "frequency_drift_limit_ppm": 1.0,
            "learning_duration_hours": 72,
            "enable_temperature_compensation": true
        },
        "alarms": {
            "phase_offset_warning_ns": 100.0,
            "phase_offset_critical_ns": 500.0,
            "frequency_offset_warning_ppm": 0.01,
            "frequency_offset_critical_ppm": 0.1,
            "gnss_satellites_warning": 6,
            "gnss_satellites_critical": 4,
            "gnss_snr_warning_db": -145.0,
            "gnss_snr_critical_db": -150.0,
            "cpu_usage_warning": 80.0,
            "memory_usage_warning": 80.0,
            "temperature_warning": 70.0,
            "temperature_critical": 75.0
        },
        "config_version": "1.0.0",
        "last_modified": 1640995200000000000
    })";
    
    CreateTestConfigFile(modified_config);
    
    // 等待文件监控检测到变化
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 验证配置已更新
    TimingConfig updated_config = config_manager_->GetConfig();
    EXPECT_DOUBLE_EQ(updated_config.discipline.convergence_threshold_ns, 999.0);
    EXPECT_TRUE(callback_called);
    
    // 禁用热重载
    config_manager_->EnableHotReload(false);
}

// JSON导入导出测试

TEST_F(ConfigManagerTest, JsonExportImport) {
    EXPECT_TRUE(config_manager_->Initialize());
    
    // 修改配置
    TimingConfig original_config = config_manager_->GetConfig();
    original_config.discipline.convergence_threshold_ns = 88.88;
    original_config.holdover.max_holdover_hours = 48;
    
    ConfigValidationResult result = config_manager_->UpdateConfig(original_config);
    EXPECT_TRUE(result.is_valid);
    
    // 导出JSON
    std::string json_export = config_manager_->ExportConfigJson(true);
    EXPECT_FALSE(json_export.empty());
    EXPECT_NE(json_export.find("88.88"), std::string::npos);
    EXPECT_NE(json_export.find("48"), std::string::npos);
    
    // 重置为默认配置
    EXPECT_TRUE(config_manager_->ResetToDefault());
    TimingConfig default_config = config_manager_->GetConfig();
    EXPECT_DOUBLE_EQ(default_config.discipline.convergence_threshold_ns, 50.0);
    
    // 导入之前导出的JSON
    result = config_manager_->ImportConfigJson(json_export);
    EXPECT_TRUE(result.is_valid);
    
    // 验证配置已恢复
    TimingConfig imported_config = config_manager_->GetConfig();
    EXPECT_DOUBLE_EQ(imported_config.discipline.convergence_threshold_ns, 88.88);
    EXPECT_EQ(imported_config.holdover.max_holdover_hours, 48);
}

TEST_F(ConfigManagerTest, InvalidJsonImport) {
    EXPECT_TRUE(config_manager_->Initialize());
    
    // 尝试导入无效的JSON
    std::string invalid_json = "{ invalid json content }";
    ConfigValidationResult result = config_manager_->ImportConfigJson(invalid_json);
    
    EXPECT_FALSE(result.is_valid);
    EXPECT_NE(result.GetSummary().find("JSON格式解析失败"), std::string::npos);
}

// 边界条件测试

TEST_F(ConfigManagerTest, FilePermissionError) {
    // 创建只读目录（在某些系统上可能需要特殊权限）
    std::filesystem::path readonly_dir = test_dir_ / "readonly";
    std::filesystem::create_directories(readonly_dir);
    
    // 尝试在只读目录中创建配置文件（这个测试可能在某些系统上失败）
    std::filesystem::path readonly_config = readonly_dir / "config.json";
    auto readonly_manager = std::make_unique<ConfigManager>(readonly_config.string());
    
    // 这个测试的结果取决于文件系统权限，所以我们只是验证它不会崩溃
    readonly_manager->Initialize();
}

TEST_F(ConfigManagerTest, ConfigFileCorruption) {
    // 创建损坏的配置文件
    CreateTestConfigFile("{ corrupted json content without proper closing");
    
    // 初始化应该失败，但不应该崩溃
    bool init_result = config_manager_->Initialize();
    
    // 根据实现，这可能成功（使用默认配置）或失败
    // 重要的是不应该崩溃
    EXPECT_NO_THROW(config_manager_->GetConfig());
}

// 配置验证器详细测试

TEST_F(ConfigManagerTest, DetailedValidation) {
    ConfigValidationResult result;
    
    // 测试时间源优先级验证
    TimeSourcePriority priorities;
    priorities.priorities[TimeSource::GNSS] = 1;
    priorities.priorities[TimeSource::RUBIDIUM] = 1; // 重复优先级
    priorities.failover_delay_ms = 500; // 太小
    
    ConfigValidator::ValidateTimeSourcePriorities(priorities, result);
    EXPECT_FALSE(result.is_valid);
    EXPECT_GE(result.errors.size(), 2);
    
    // 测试驯服参数验证
    result = ConfigValidationResult(); // 重置
    DiscipliningParameters discipline;
    discipline.convergence_threshold_ns = -10.0; // 负数
    discipline.phase_gain = 2.0; // 超出范围
    
    ConfigValidator::ValidateDiscipliningParameters(discipline, result);
    EXPECT_FALSE(result.is_valid);
    
    // 测试守时参数验证
    result = ConfigValidationResult(); // 重置
    HoldoverParameters holdover;
    holdover.max_holdover_hours = 0; // 无效值
    holdover.frequency_drift_limit_ppm = -1.0; // 负数
    
    ConfigValidator::ValidateHoldoverParameters(holdover, result);
    EXPECT_FALSE(result.is_valid);
    
    // 测试告警阈值验证
    result = ConfigValidationResult(); // 重置
    AlarmThresholds alarms;
    alarms.phase_offset_warning_ns = 100.0;
    alarms.phase_offset_critical_ns = 50.0; // 小于警告阈值
    alarms.gnss_satellites_warning = 2; // 太小
    
    ConfigValidator::ValidateAlarmThresholds(alarms, result);
    EXPECT_FALSE(result.is_valid);
}

TEST_F(ConfigManagerTest, ConfigConsistencyValidation) {
    TimingConfig config = ConfigManager::GetDefaultConfig();
    
    // 设置可能导致警告的配置
    config.discipline.convergence_threshold_ns = 200.0; // 大于告警阈值
    config.alarms.phase_offset_warning_ns = 100.0;
    config.holdover.learning_duration_hours = 12; // 小于最大守时时间
    config.holdover.max_holdover_hours = 24;
    
    ConfigValidationResult result;
    ConfigValidator::ValidateConfigConsistency(config, result);
    
    EXPECT_TRUE(result.is_valid); // 应该有效，但有警告
    EXPECT_FALSE(result.warnings.empty());
    
    std::string summary = result.GetSummary();
    EXPECT_NE(summary.find("频繁告警"), std::string::npos);
    EXPECT_NE(summary.find("守时精度"), std::string::npos);
}