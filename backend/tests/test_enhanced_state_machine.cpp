#include <gtest/gtest.h>
#include "core/clock_state_machine.h"
#include "core/signal_quality_evaluator.h"
#include <memory>
#include <thread>
#include <chrono>

using namespace timing_server::core;

/**
 * @brief 增强状态机测试类
 * 测试集成了质量评估器的状态机功能
 */
class EnhancedStateMachineTest : public ::testing::Test {
protected:
    void SetUp() override {
        state_machine_ = std::make_unique<ClockStateMachine>(ClockState::FREE_RUN);
        ASSERT_TRUE(state_machine_->Initialize());
        ASSERT_TRUE(state_machine_->Start());
    }
    
    void TearDown() override {
        if (state_machine_) {
            state_machine_->Stop();
        }
    }
    
    std::unique_ptr<ClockStateMachine> state_machine_;
};

/**
 * @brief 测试GNSS信号质量评估
 */
TEST_F(EnhancedStateMachineTest, GnssSignalQualityEvaluation) {
    // 模拟高质量GNSS数据
    std::string good_gnss_data = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n"
        "$GPGSA,A,3,04,05,09,12,,,,,,,,,1.5,0.9,1.2*39\r\n";
    
    GnssSignalQuality quality = state_machine_->EvaluateGnssSignal(good_gnss_data);
    
    EXPECT_EQ(quality.satellite_count, 8);
    EXPECT_EQ(quality.fix_quality, 2); // DGPS
    EXPECT_LT(quality.position_dilution, 2.0);
    EXPECT_GT(quality.signal_strength_dbm, -150.0);
    
    // 测试低质量GNSS数据
    std::string poor_gnss_data = 
        "$GPGGA,123519,4807.038,N,01131.000,E,0,02,9.9,545.4,M,46.9,M,,*47\r\n";
    
    GnssSignalQuality poor_quality = state_machine_->EvaluateGnssSignal(poor_gnss_data);
    
    EXPECT_EQ(poor_quality.satellite_count, 2);
    EXPECT_EQ(poor_quality.fix_quality, 0); // 无效
    EXPECT_GT(poor_quality.position_dilution, 5.0);
}

/**
 * @brief 测试智能GNSS信号获取转换
 */
TEST_F(EnhancedStateMachineTest, IntelligentGnssSignalTransition) {
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    
    // 测试低质量信号 - 应该被阻止转换
    StateMachineEvent poor_signal_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "低质量GNSS信号");
    poor_signal_event.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,0,02,9.9,545.4,M,46.9,M,,*47\r\n";
    
    TransitionResult result = state_machine_->ProcessEvent(poor_signal_event);
    EXPECT_EQ(result, TransitionResult::BLOCKED);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    
    // 测试高质量信号 - 应该成功转换
    StateMachineEvent good_signal_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "高质量GNSS信号");
    good_signal_event.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n"
        "$GPGSA,A,3,04,05,09,12,,,,,,,,,1.5,0.9,1.2*39\r\n";
    
    result = state_machine_->ProcessEvent(good_signal_event);
    EXPECT_EQ(result, TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
}

/**
 * @brief 测试驯服收敛评估
 */
TEST_F(EnhancedStateMachineTest, DiscipliningConvergenceEvaluation) {
    // 先转换到驯服状态
    StateMachineEvent signal_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号获取");
    signal_event.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n";
    
    EXPECT_EQ(state_machine_->ProcessEvent(signal_event), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 添加驯服样本 - 模拟收敛过程
    for (int i = 0; i < 15; ++i) {
        double phase_error = 100.0 * std::exp(-i * 0.3); // 指数衰减
        double freq_error = 0.1 * std::exp(-i * 0.2);
        
        state_machine_->AddDiscipliningSample(phase_error, freq_error);
        
        // 短暂等待
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 检查是否收敛
    bool converged = state_machine_->IsDiscipliningConverged();
    EXPECT_TRUE(converged);
    
    // 尝试转换到锁定状态
    StateMachineEvent convergence_event(ClockEvent::CONVERGENCE_ACHIEVED, "驯服收敛");
    TransitionResult result = state_machine_->ProcessEvent(convergence_event);
    EXPECT_EQ(result, TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
}

/**
 * @brief 测试驯服未收敛时的阻止转换
 */
TEST_F(EnhancedStateMachineTest, DiscipliningNotConvergedBlocking) {
    // 转换到驯服状态
    StateMachineEvent signal_event(ClockEvent::GNSS_SIGNAL_ACQUIRED);
    EXPECT_EQ(state_machine_->ProcessEvent(signal_event), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 添加少量不稳定的样本
    for (int i = 0; i < 5; ++i) {
        double phase_error = 200.0 + (i % 2) * 100.0; // 不稳定的误差
        double freq_error = 0.5 + (i % 2) * 0.3;
        
        state_machine_->AddDiscipliningSample(phase_error, freq_error);
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
    
    // 检查未收敛
    EXPECT_FALSE(state_machine_->IsDiscipliningConverged());
    
    // 尝试转换到锁定状态应该被阻止
    StateMachineEvent convergence_event(ClockEvent::CONVERGENCE_ACHIEVED, "尝试收敛");
    TransitionResult result = state_machine_->ProcessEvent(convergence_event);
    EXPECT_EQ(result, TransitionResult::BLOCKED);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
}

/**
 * @brief 测试守时质量评估
 */
TEST_F(EnhancedStateMachineTest, HoldoverQualityEvaluation) {
    // 先到达锁定状态
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    // 添加收敛样本
    for (int i = 0; i < 15; ++i) {
        state_machine_->AddDiscipliningSample(30.0, 0.01);
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
    
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
    
    // 进入守时模式
    StateMachineEvent signal_lost_event(ClockEvent::GNSS_SIGNAL_LOST, "GNSS信号丢失");
    signal_lost_event.context["frequency_offset"] = "0.001"; // 1 ppb
    
    EXPECT_EQ(state_machine_->ProcessEvent(signal_lost_event), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
    
    // 更新守时状态
    for (int i = 0; i < 10; ++i) {
        double freq_offset = 0.001 + i * 0.0001; // 模拟频率漂移
        double temperature = 25.0 + i * 0.1;     // 模拟温度变化
        
        state_machine_->UpdateHoldoverStatus(freq_offset, temperature);
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
    
    // 获取守时质量
    HoldoverQuality quality = state_machine_->GetHoldoverQuality();
    EXPECT_GT(quality.quality_score, 0);
    EXPECT_GT(quality.holdover_duration_ns, 0);
    EXPECT_NE(quality.frequency_drift_rate_ppm_per_hour, 0.0);
}

/**
 * @brief 测试守时超时检查
 */
TEST_F(EnhancedStateMachineTest, HoldoverTimeoutCheck) {
    // 到达守时状态
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    for (int i = 0; i < 15; ++i) {
        state_machine_->AddDiscipliningSample(30.0, 0.01);
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
    
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_LOST));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
    
    // 测试短时间内不应该超时
    EXPECT_FALSE(state_machine_->IsHoldoverTimeout(24));
    
    // 测试超时事件但实际未超时应该被阻止
    StateMachineEvent timeout_event(ClockEvent::HOLDOVER_TIMEOUT, "测试超时");
    timeout_event.context["max_holdover_hours"] = "24";
    
    TransitionResult result = state_machine_->ProcessEvent(timeout_event);
    EXPECT_EQ(result, TransitionResult::BLOCKED);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
}

/**
 * @brief 测试守时期间GNSS信号恢复
 */
TEST_F(EnhancedStateMachineTest, HoldoverGnssRecovery) {
    // 到达守时状态
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_ACQUIRED));
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    for (int i = 0; i < 15; ++i) {
        state_machine_->AddDiscipliningSample(30.0, 0.01);
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
    
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED));
    state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::GNSS_SIGNAL_LOST));
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
    
    // 测试低质量信号恢复 - 应该被阻止
    StateMachineEvent poor_recovery_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "低质量信号恢复");
    poor_recovery_event.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,0,02,9.9,545.4,M,46.9,M,,*47\r\n";
    
    TransitionResult result = state_machine_->ProcessEvent(poor_recovery_event);
    EXPECT_EQ(result, TransitionResult::BLOCKED);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
    
    // 测试高质量信号恢复 - 应该成功
    StateMachineEvent good_recovery_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "高质量信号恢复");
    good_recovery_event.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n";
    
    result = state_machine_->ProcessEvent(good_recovery_event);
    EXPECT_EQ(result, TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
}

/**
 * @brief 测试完整的状态机生命周期
 */
TEST_F(EnhancedStateMachineTest, CompleteStateMachineLifecycle) {
    // 1. 开始：FREE_RUN
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    
    // 2. GNSS信号获取 -> DISCIPLINING
    StateMachineEvent signal_acquired(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号获取");
    signal_acquired.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n";
    
    EXPECT_EQ(state_machine_->ProcessEvent(signal_acquired), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 3. 驯服过程
    for (int i = 0; i < 20; ++i) {
        double phase_error = 100.0 * std::exp(-i * 0.2);
        double freq_error = 0.1 * std::exp(-i * 0.15);
        state_machine_->AddDiscipliningSample(phase_error, freq_error);
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
    
    // 4. 驯服收敛 -> LOCKED
    EXPECT_EQ(state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED)), 
              TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
    
    // 5. GNSS信号丢失 -> HOLDOVER
    StateMachineEvent signal_lost(ClockEvent::GNSS_SIGNAL_LOST, "GNSS信号丢失");
    signal_lost.context["frequency_offset"] = "0.001";
    
    EXPECT_EQ(state_machine_->ProcessEvent(signal_lost), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::HOLDOVER);
    
    // 6. 守时过程
    for (int i = 0; i < 10; ++i) {
        state_machine_->UpdateHoldoverStatus(0.001 + i * 0.0001, 25.0 + i * 0.1);
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
    
    // 7. GNSS信号恢复 -> DISCIPLINING
    StateMachineEvent signal_recovered(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号恢复");
    signal_recovered.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,10,0.8,545.4,M,46.9,M,,*47\r\n";
    
    EXPECT_EQ(state_machine_->ProcessEvent(signal_recovered), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 8. 再次收敛 -> LOCKED
    for (int i = 0; i < 15; ++i) {
        state_machine_->AddDiscipliningSample(40.0, 0.01);
        std::this_thread::sleep_for(std::chrono::milliseconds(3));
    }
    
    EXPECT_EQ(state_machine_->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED)), 
              TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::LOCKED);
    
    // 验证统计信息
    std::string stats = state_machine_->GetStatistics();
    EXPECT_FALSE(stats.empty());
    EXPECT_NE(stats.find("LOCKED"), std::string::npos);
    EXPECT_NE(stats.find("总转换次数"), std::string::npos);
}

/**
 * @brief 测试状态机在各种异常情况下的鲁棒性
 */
TEST_F(EnhancedStateMachineTest, StateMachineRobustness) {
    // 测试空GNSS数据
    StateMachineEvent empty_gnss_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "空GNSS数据");
    empty_gnss_event.context["gnss_data"] = "";
    
    // 应该使用默认逻辑成功转换
    EXPECT_EQ(state_machine_->ProcessEvent(empty_gnss_event), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
    
    // 测试无效GNSS数据
    StateMachineEvent invalid_gnss_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "无效GNSS数据");
    invalid_gnss_event.context["gnss_data"] = "invalid nmea data";
    
    // 重置到FREE_RUN状态
    state_machine_->Reset();
    state_machine_->Start();
    
    // 应该被阻止转换
    EXPECT_EQ(state_machine_->ProcessEvent(invalid_gnss_event), TransitionResult::BLOCKED);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::FREE_RUN);
    
    // 测试没有上下文数据的事件
    StateMachineEvent no_context_event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "无上下文数据");
    
    // 应该使用默认逻辑成功转换
    EXPECT_EQ(state_machine_->ProcessEvent(no_context_event), TransitionResult::SUCCESS);
    EXPECT_EQ(state_machine_->GetCurrentState(), ClockState::DISCIPLINING);
}