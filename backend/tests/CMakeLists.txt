# 测试框架配置
cmake_minimum_required(VERSION 3.16)

# 查找Google Test框架
find_package(GTest QUIET)

if(GTest_FOUND)
    message(STATUS "找到Google Test框架")
    
    # 添加Mock头文件目录
    include_directories(${CMAKE_CURRENT_SOURCE_DIR})

    # 创建测试可执行文件
    add_executable(timing-server-tests
        test_core_types.cpp
        test_core_serialization.cpp
        test_config_manager.cpp
        test_state_machine.cpp
        test_timing_engine.cpp
        test_disciplining_algorithm.cpp
        test_daemon_manager.cpp
        test_database_manager.cpp
        test_rubidium_learning.cpp
        test_auth_manager.cpp
        test_security_manager.cpp
        test_precision_monitoring.cpp
    )
    
    # 创建端到端集成测试 - 完整的系统集成验证
    add_executable(test_end_to_end_integration
        test_end_to_end_integration.cpp
    )
    
    # 创建系统性能测试
    add_executable(test_system_performance
        test_system_performance.cpp
    )
    
    # 创建Web界面功能测试
    add_executable(test_web_interface_functionality
        test_web_interface_functionality.cpp
    )
    
    # 创建长期稳定性测试
    add_executable(test_long_term_stability
        test_long_term_stability.cpp
    )

    # 创建简化编译测试
    add_executable(test_simple_compilation
        test_simple_compilation.cpp
    )

    # 创建最小化编译测试
    add_executable(test_minimal_compilation
        test_minimal_compilation.cpp
    )

    # 创建简化长期稳定性测试
    add_executable(test_long_term_stability_simple
        test_long_term_stability_simple.cpp
    )
    
    # 创建API集成测试
    add_executable(test_api_integration
        test_api_integration.cpp
    )
    
    # 创建WebSocket集成测试
    add_executable(test_websocket_integration
        test_websocket_integration.cpp
    )
    
    # 创建平台兼容性测试
    add_executable(test_platform_compatibility
        test_platform_compatibility.cpp
    )
    
    # 创建HAL接口测试（需要Google Mock）
    add_executable(timing-server-hal-tests
        test_hal_interfaces.cpp
        test_mock_hal.cpp
    )
    
    # 链接测试库
    target_link_libraries(timing-server-tests
        timing-core
        timing-hal
        timing-api
        GTest::gtest
        GTest::gtest_main
        Threads::Threads
    )
    
    # 端到端集成测试链接 - 包含所有核心组件和Mock支持
    target_link_libraries(test_end_to_end_integration
        timing-core
        timing-hal
        # timing-api  # 暂时移除API依赖以避免编译问题
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )
    
    target_link_libraries(test_system_performance
        timing-core
        timing-hal
        # timing-api  # 暂时移除API依赖以避免编译问题
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )
    
    target_link_libraries(test_web_interface_functionality
        timing-core
        timing-hal
        timing-api
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )
    
    target_link_libraries(test_long_term_stability
        timing-core
        timing-hal
        # timing-api  # 暂时移除API依赖以避免编译问题
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )
    
    target_link_libraries(timing-server-hal-tests
        timing-core
        timing-hal
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )

    # 链接简化编译测试 - 只依赖核心组件，避免API组件的编译问题
    target_link_libraries(test_simple_compilation
        timing-core
        timing-hal
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )

    # 链接最小化编译测试 - 最基本的依赖
    target_link_libraries(test_minimal_compilation
        timing-core
        timing-hal
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )

    # 链接简化长期稳定性测试
    target_link_libraries(test_long_term_stability_simple
        timing-core
        timing-hal
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )
    
    target_link_libraries(test_api_integration
        timing-core
        timing-api
        GTest::gtest
        GTest::gtest_main
        Threads::Threads
    )
    
    target_link_libraries(test_websocket_integration
        timing-core
        timing-api
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
        OpenSSL::SSL
        OpenSSL::Crypto
    )
    
    target_link_libraries(test_platform_compatibility
        timing-core
        timing-hal
        timing-api
        GTest::gtest
        GTest::gtest_main
        GTest::gmock
        GTest::gmock_main
        Threads::Threads
    )
    
    # 添加测试
    add_test(NAME timing-server-unit-tests COMMAND timing-server-tests)
    add_test(NAME timing-server-hal-tests COMMAND timing-server-hal-tests)
    add_test(NAME api-integration-tests COMMAND test_api_integration)
    add_test(NAME websocket-integration-tests COMMAND test_websocket_integration)
    add_test(NAME end-to-end-integration-tests COMMAND test_end_to_end_integration)
    add_test(NAME system-performance-tests COMMAND test_system_performance)
    add_test(NAME web-interface-functionality-tests COMMAND test_web_interface_functionality)
    add_test(NAME long-term-stability-tests COMMAND test_long_term_stability)
    add_test(NAME platform-compatibility-tests COMMAND test_platform_compatibility)
    
    message(STATUS "测试框架配置完成")
else()
    message(WARNING "未找到Google Test框架，跳过测试构建")
    message(WARNING "要启用测试，请安装Google Test:")
    message(WARNING "  Ubuntu/Debian: sudo apt-get install libgtest-dev")
    message(WARNING "  macOS: brew install googletest")
endif()