#include <gtest/gtest.h>
#include <filesystem>
#include <thread>
#include <chrono>

#include "core/rubidium_learning.h"
#include "core/logger.h"

using namespace timing_server::core;

class RubidiumLearningTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建临时测试目录
        test_storage_path_ = "/tmp/test_rubidium_learning";
        test_device_id_ = "TEST_DEVICE_001";
        test_serial_number_ = "RB001234";
        test_firmware_version_ = "1.0.0";
        
        // 清理可能存在的测试文件
        if (std::filesystem::exists(test_storage_path_)) {
            std::filesystem::remove_all(test_storage_path_);
        }
        
        // 创建测试目录
        std::filesystem::create_directories(test_storage_path_);
        
        // 初始化日志系统
        Logger::GetInstance().Initialize();
        Logger::GetInstance().SetLogLevel(LogLevel::DEBUG);
        Logger::GetInstance().AddOutput(std::make_unique<ConsoleLogOutput>(true));
        
        // 创建存储管理器
        storage_ = std::make_unique<RubidiumLearningStorage>(test_storage_path_, test_device_id_);
    }
    
    void TearDown() override {
        // 关闭存储管理器
        if (storage_) {
            storage_->Shutdown();
            storage_.reset();
        }
        
        // 清理测试文件
        if (std::filesystem::exists(test_storage_path_)) {
            std::filesystem::remove_all(test_storage_path_);
        }
        
        // 关闭日志系统
        Logger::GetInstance().Shutdown();
    }
    
    RubidiumLearningData CreateTestLearningData(double frequency_offset = 0.1, 
                                               double temperature = 25.0,
                                               uint32_t sample_count = 1000) {
        RubidiumLearningData data;
        
        data.timestamp_ns = GetCurrentTimestampNs();
        data.learning_duration_ns = 24ULL * 3600ULL * 1000000000ULL; // 24小时
        data.sample_count = sample_count;
        
        data.frequency_offset_ppm = frequency_offset;
        data.frequency_drift_rate_ppm_per_hour = 0.001;
        data.frequency_stability_ppm = 1e-12;
        
        data.reference_temperature_c = temperature;
        data.temperature_coefficient_ppm_per_c = -0.5;
        data.temperature_coefficient_2nd_ppm_per_c2 = -0.01;
        
        data.aging_rate_ppm_per_day = 0.01;
        data.aging_acceleration_factor = 1.0;
        data.operating_days = 30;
        
        data.allan_deviation_1s = 1e-12;
        data.allan_deviation_10s = 5e-13;
        data.allan_deviation_100s = 2e-13;
        data.allan_deviation_1000s = 1e-13;
        
        data.confidence_level = 0.95;
        data.prediction_accuracy = 0.9;
        data.validation_score = 85;
        
        data.phase_correction_ns = 10.0;
        data.frequency_correction_ppm = -0.05;
        data.correction_enabled = true;
        
        return data;
    }
    
    std::string test_storage_path_;
    std::string test_device_id_;
    std::string test_serial_number_;
    std::string test_firmware_version_;
    std::unique_ptr<RubidiumLearningStorage> storage_;
};

// 测试RubidiumLearningData基本功能
TEST_F(RubidiumLearningTest, LearningDataBasicOperations) {
    RubidiumLearningData data = CreateTestLearningData();
    
    // 测试数据有效性
    EXPECT_TRUE(data.IsValid());
    
    // 测试质量评分
    uint32_t quality_score = data.CalculateQualityScore();
    EXPECT_GT(quality_score, 0);
    EXPECT_LE(quality_score, 100);
    
    // 测试摘要生成
    std::string summary = data.GetSummary();
    EXPECT_FALSE(summary.empty());
    EXPECT_NE(summary.find("铷钟学习数据摘要"), std::string::npos);
    EXPECT_NE(summary.find("频率偏移"), std::string::npos);
    EXPECT_NE(summary.find("质量评分"), std::string::npos);
}

// 测试无效数据检测
TEST_F(RubidiumLearningTest, InvalidDataDetection) {
    RubidiumLearningData data = CreateTestLearningData();
    
    // 测试无效频率偏移
    data.frequency_offset_ppm = 2000.0; // 超出合理范围
    EXPECT_FALSE(data.IsValid());
    
    // 恢复有效值
    data.frequency_offset_ppm = 0.1;
    EXPECT_TRUE(data.IsValid());
    
    // 测试无效温度
    data.reference_temperature_c = -100.0; // 超出合理范围
    EXPECT_FALSE(data.IsValid());
    
    // 恢复有效值
    data.reference_temperature_c = 25.0;
    EXPECT_TRUE(data.IsValid());
    
    // 测试无效Allan偏差
    data.allan_deviation_1s = -1.0; // 负值无效
    EXPECT_FALSE(data.IsValid());
    
    // 恢复有效值
    data.allan_deviation_1s = 1e-12;
    EXPECT_TRUE(data.IsValid());
}

// 测试存储管理器初始化
TEST_F(RubidiumLearningTest, StorageInitialization) {
    // 测试初始化
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 测试重复初始化
    result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 测试关闭
    storage_->Shutdown();
}

// 测试学习数据保存和加载
TEST_F(RubidiumLearningTest, SaveAndLoadLearningData) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 创建测试数据
    RubidiumLearningData test_data = CreateTestLearningData(0.15, 26.5, 1500);
    
    // 保存数据
    result = storage_->SaveLearningData(test_data, true);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 加载最新数据
    auto [load_result, loaded_data] = storage_->GetLatestLearningData();
    EXPECT_EQ(load_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 验证加载的数据
    EXPECT_DOUBLE_EQ(loaded_data.frequency_offset_ppm, 0.15);
    EXPECT_DOUBLE_EQ(loaded_data.reference_temperature_c, 26.5);
    EXPECT_EQ(loaded_data.sample_count, 1500);
    EXPECT_TRUE(loaded_data.IsValid());
}

// 测试学习历史管理
TEST_F(RubidiumLearningTest, LearningHistoryManagement) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 保存多个学习数据
    for (int i = 0; i < 5; ++i) {
        RubidiumLearningData data = CreateTestLearningData(0.1 + i * 0.01, 25.0 + i, 1000 + i * 100);
        
        // 稍微延迟以确保时间戳不同
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        result = storage_->SaveLearningData(data, false);
        EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    }
    
    // 加载学习历史
    auto [load_result, history] = storage_->LoadLearningHistory();
    EXPECT_EQ(load_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 验证历史数据
    EXPECT_EQ(history.device_id, test_device_id_);
    EXPECT_EQ(history.serial_number, test_serial_number_);
    EXPECT_EQ(history.firmware_version, test_firmware_version_);
    EXPECT_EQ(history.learning_records.size(), 5);
    
    // 测试获取最新数据
    const RubidiumLearningData* latest = history.GetLatestData();
    ASSERT_NE(latest, nullptr);
    EXPECT_DOUBLE_EQ(latest->frequency_offset_ppm, 0.14); // 最后一个数据
    
    // 测试时间范围查询
    uint64_t start_time = GetCurrentTimestampNs() - 1000000000ULL; // 1秒前
    uint64_t end_time = GetCurrentTimestampNs();
    
    auto range_data = history.GetDataInRange(start_time, end_time);
    EXPECT_EQ(range_data.size(), 5); // 所有数据都在范围内
}

// 测试趋势分析
TEST_F(RubidiumLearningTest, TrendAnalysis) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 创建有趋势的测试数据
    for (int i = 0; i < 10; ++i) {
        RubidiumLearningData data = CreateTestLearningData();
        
        // 模拟频率漂移趋势
        data.frequency_offset_ppm = 0.1 + i * 0.001; // 递增趋势
        data.reference_temperature_c = 25.0 + i * 0.1; // 温度上升趋势
        data.aging_rate_ppm_per_day = 0.01 + i * 0.0001; // 老化加速
        
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        result = storage_->SaveLearningData(data, false);
        EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    }
    
    // 加载历史并分析趋势
    auto [load_result, history] = storage_->LoadLearningHistory();
    EXPECT_EQ(load_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    auto trends = history.AnalyzeTrends();
    
    // 验证趋势分析结果
    EXPECT_GT(trends.frequency_drift_trend, 0); // 频率应该有上升趋势
    EXPECT_GT(trends.temperature_trend, 0);     // 温度应该有上升趋势
    EXPECT_GT(trends.aging_trend, 0);           // 老化率应该有上升趋势
}

// 测试数据完整性验证
TEST_F(RubidiumLearningTest, DataIntegrityVerification) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 保存测试数据
    RubidiumLearningData data = CreateTestLearningData();
    result = storage_->SaveLearningData(data, true);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 验证数据完整性
    result = storage_->VerifyIntegrity(false);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
}

// 测试备份和恢复功能
TEST_F(RubidiumLearningTest, BackupAndRestore) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 保存测试数据
    RubidiumLearningData original_data = CreateTestLearningData(0.123, 27.5, 2000);
    result = storage_->SaveLearningData(original_data, true);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 创建备份
    std::string backup_path = test_storage_path_ + "/test_backup.dat";
    result = storage_->CreateBackup(backup_path);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    EXPECT_TRUE(std::filesystem::exists(backup_path));
    
    // 保存新数据（修改原始数据）
    RubidiumLearningData new_data = CreateTestLearningData(0.456, 28.0, 2500);
    result = storage_->SaveLearningData(new_data, false);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 验证数据已更改
    auto [load_result, current_data] = storage_->GetLatestLearningData();
    EXPECT_EQ(load_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    EXPECT_DOUBLE_EQ(current_data.frequency_offset_ppm, 0.456);
    
    // 从备份恢复
    result = storage_->RestoreFromBackup(backup_path);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 验证数据已恢复
    auto [restore_result, restored_data] = storage_->GetLatestLearningData();
    EXPECT_EQ(restore_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    EXPECT_DOUBLE_EQ(restored_data.frequency_offset_ppm, 0.123);
    EXPECT_DOUBLE_EQ(restored_data.reference_temperature_c, 27.5);
    EXPECT_EQ(restored_data.sample_count, 2000);
}

// 测试数据清理功能
TEST_F(RubidiumLearningTest, DataCleanup) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 保存多个数据记录
    for (int i = 0; i < 5; ++i) {
        RubidiumLearningData data = CreateTestLearningData();
        result = storage_->SaveLearningData(data, false);
        EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 验证数据存在
    auto [load_result, history] = storage_->LoadLearningHistory();
    EXPECT_EQ(load_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    EXPECT_EQ(history.learning_records.size(), 5);
    
    // 清理过期数据（保留0天，即清理所有数据）
    result = storage_->CleanupExpiredData(0);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 验证数据已被清理
    auto [cleanup_result, cleaned_history] = storage_->LoadLearningHistory();
    // 注意：由于我们的实现会保留至少一个最新记录，所以这里可能不是0
    EXPECT_LE(cleaned_history.learning_records.size(), 1);
}

// 测试JSON导出功能
TEST_F(RubidiumLearningTest, JsonExport) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 保存测试数据
    RubidiumLearningData data = CreateTestLearningData();
    result = storage_->SaveLearningData(data, false);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 导出为JSON
    std::string export_path = test_storage_path_ + "/export.json";
    result = storage_->ExportToJson(export_path, false);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 验证导出文件存在
    EXPECT_TRUE(std::filesystem::exists(export_path));
    
    // 验证JSON内容
    std::ifstream file(export_path);
    ASSERT_TRUE(file.is_open());
    
    std::string json_content((std::istreambuf_iterator<char>(file)),
                            std::istreambuf_iterator<char>());
    file.close();
    
    // 检查JSON内容包含预期字段
    EXPECT_NE(json_content.find("device_info"), std::string::npos);
    EXPECT_NE(json_content.find("learning_history"), std::string::npos);
    EXPECT_NE(json_content.find("learning_records"), std::string::npos);
    EXPECT_NE(json_content.find(test_device_id_), std::string::npos);
    EXPECT_NE(json_content.find(test_serial_number_), std::string::npos);
}

// 测试存储统计信息
TEST_F(RubidiumLearningTest, StorageStatistics) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 保存多个测试数据
    for (int i = 0; i < 3; ++i) {
        RubidiumLearningData data = CreateTestLearningData();
        result = storage_->SaveLearningData(data, true); // 创建备份
        EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 获取统计信息
    auto stats = storage_->GetStatistics();
    
    // 验证统计信息
    EXPECT_EQ(stats.total_records, 3);
    EXPECT_GT(stats.file_size_bytes, 0);
    EXPECT_GT(stats.oldest_record_time, 0);
    EXPECT_GT(stats.newest_record_time, 0);
    EXPECT_GE(stats.newest_record_time, stats.oldest_record_time);
    EXPECT_GT(stats.average_quality_score, 0.0);
    EXPECT_LE(stats.average_quality_score, 100.0);
    EXPECT_GT(stats.backup_count, 0); // 应该有备份文件
}

// 测试配置参数设置
TEST_F(RubidiumLearningTest, ConfigurationSettings) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 测试设置最大记录数
    storage_->SetMaxRecords(500);
    
    // 测试设置自动备份间隔
    storage_->SetAutoBackupInterval(12); // 12小时
    
    // 测试启用压缩
    storage_->SetCompressionEnabled(true);
    
    // 这些设置主要测试是否能正常调用，具体效果需要在实际使用中验证
}

// 测试RubidiumLearningAnalyzer
TEST_F(RubidiumLearningTest, LearningAnalyzer) {
    // 初始化存储
    auto result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 保存测试数据
    RubidiumLearningData data = CreateTestLearningData();
    data.temperature_coefficient_ppm_per_c = -0.5;
    data.frequency_drift_rate_ppm_per_hour = 0.001;
    data.aging_rate_ppm_per_day = 0.01;
    
    result = storage_->SaveLearningData(data, false);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    // 创建分析器
    auto analyzer = std::make_unique<RubidiumLearningAnalyzer>(
        std::shared_ptr<RubidiumLearningStorage>(storage_.get(), [](RubidiumLearningStorage*){}));
    
    // 测试频率偏移预测
    uint64_t future_time = GetCurrentTimestampNs() + 3600ULL * 1000000000ULL; // 1小时后
    double predicted_offset = analyzer->PredictFrequencyOffset(future_time, 26.0);
    
    // 验证预测结果合理性
    EXPECT_NE(predicted_offset, 0.0);
    EXPECT_GT(predicted_offset, -10.0);
    EXPECT_LT(predicted_offset, 10.0);
    
    // 测试温度补偿计算
    double compensation = analyzer->CalculateTemperatureCompensation(30.0, 25.0);
    
    // 验证温度补偿结果
    EXPECT_DOUBLE_EQ(compensation, 2.5); // -(-0.5 * 5) = 2.5
}

// 测试错误处理
TEST_F(RubidiumLearningTest, ErrorHandling) {
    // 测试未初始化的存储操作
    RubidiumLearningData data = CreateTestLearningData();
    auto result = storage_->SaveLearningData(data, false);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::ERROR_INVALID_PARAMETER);
    
    // 测试保存无效数据
    auto init_result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(init_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    RubidiumLearningData invalid_data;
    invalid_data.frequency_offset_ppm = 2000.0; // 无效值
    
    result = storage_->SaveLearningData(invalid_data, false);
    EXPECT_EQ(result, RubidiumLearningStorage::StorageResult::ERROR_INVALID_PARAMETER);
    
    // 测试加载不存在的文件
    storage_->Shutdown();
    
    // 删除存储目录
    std::filesystem::remove_all(test_storage_path_);
    
    // 重新创建存储管理器
    storage_ = std::make_unique<RubidiumLearningStorage>(test_storage_path_, test_device_id_);
    init_result = storage_->Initialize(test_serial_number_, test_firmware_version_);
    ASSERT_EQ(init_result, RubidiumLearningStorage::StorageResult::SUCCESS);
    
    auto [load_result, history] = storage_->LoadLearningHistory();
    EXPECT_EQ(load_result, RubidiumLearningStorage::StorageResult::ERROR_FILE_NOT_FOUND);
}

// 测试StorageResultToString函数
TEST_F(RubidiumLearningTest, StorageResultToString) {
    EXPECT_EQ(StorageResultToString(RubidiumLearningStorage::StorageResult::SUCCESS), "SUCCESS");
    EXPECT_EQ(StorageResultToString(RubidiumLearningStorage::StorageResult::ERROR_FILE_NOT_FOUND), "ERROR_FILE_NOT_FOUND");
    EXPECT_EQ(StorageResultToString(RubidiumLearningStorage::StorageResult::ERROR_WRITE_FAILED), "ERROR_WRITE_FAILED");
}