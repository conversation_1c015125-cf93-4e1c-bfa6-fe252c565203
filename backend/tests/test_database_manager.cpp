#include <gtest/gtest.h>
#include <filesystem>
#include <thread>
#include <chrono>

#include "core/database_manager.h"
#include "core/logger.h"

using namespace timing_server::core;

class DatabaseManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建临时测试数据库
        test_db_path_ = "/tmp/test_timing_server.db";
        
        // 删除可能存在的测试数据库
        if (std::filesystem::exists(test_db_path_)) {
            std::filesystem::remove(test_db_path_);
        }
        
        // 初始化日志系统（用于测试中的日志输出）
        Logger::GetInstance().Initialize();
        Logger::GetInstance().SetLogLevel(LogLevel::DEBUG);
        Logger::GetInstance().AddOutput(std::make_unique<ConsoleLogOutput>(true));
    }
    
    void TearDown() override {
        // 关闭数据库管理器
        DatabaseManager::GetInstance().Shutdown();
        
        // 删除测试数据库文件
        if (std::filesystem::exists(test_db_path_)) {
            std::filesystem::remove(test_db_path_);
        }
        
        // 关闭日志系统
        Logger::GetInstance().Shutdown();
    }
    
    std::string test_db_path_;
};

// 测试数据库管理器初始化
TEST_F(DatabaseManagerTest, InitializeAndShutdown) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 测试初始化
    DatabaseResult result = db_manager.Initialize(test_db_path_, 3);
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 验证数据库文件已创建
    EXPECT_TRUE(std::filesystem::exists(test_db_path_));
    
    // 测试重复初始化
    result = db_manager.Initialize(test_db_path_, 3);
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 测试关闭
    db_manager.Shutdown();
}

// 测试性能指标插入和查询
TEST_F(DatabaseManagerTest, MetricOperations) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 初始化数据库
    DatabaseResult result = db_manager.Initialize(test_db_path_);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 禁用异步写入以便立即测试
    db_manager.SetAsyncWriting(false);
    
    // 创建测试指标记录
    MetricRecord metric1("phase_offset", "GNSS", 25.5, "ns", 95);
    MetricRecord metric2("frequency_offset", "RUBIDIUM", 0.001, "ppm", 98);
    
    // 测试单个指标插入
    result = db_manager.InsertMetric(metric1);
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 测试批量指标插入
    std::vector<MetricRecord> metrics = {metric2};
    result = db_manager.InsertMetrics(metrics);
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 测试指标查询
    DatabaseFilter filter;
    filter.limit = 10;
    
    std::vector<MetricRecord> queried_metrics = db_manager.QueryMetrics(filter);
    EXPECT_EQ(queried_metrics.size(), 2);
    
    // 验证查询结果
    bool found_phase_offset = false;
    bool found_frequency_offset = false;
    
    for (const auto& metric : queried_metrics) {
        if (metric.metric_type == "phase_offset" && metric.source == "GNSS") {
            EXPECT_DOUBLE_EQ(metric.value, 25.5);
            EXPECT_EQ(metric.unit, "ns");
            EXPECT_EQ(metric.quality, 95);
            found_phase_offset = true;
        } else if (metric.metric_type == "frequency_offset" && metric.source == "RUBIDIUM") {
            EXPECT_DOUBLE_EQ(metric.value, 0.001);
            EXPECT_EQ(metric.unit, "ppm");
            EXPECT_EQ(metric.quality, 98);
            found_frequency_offset = true;
        }
    }
    
    EXPECT_TRUE(found_phase_offset);
    EXPECT_TRUE(found_frequency_offset);
}

// 测试事件记录插入和查询
TEST_F(DatabaseManagerTest, EventOperations) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 初始化数据库
    DatabaseResult result = db_manager.Initialize(test_db_path_);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 禁用异步写入
    db_manager.SetAsyncWriting(false);
    
    // 创建测试事件记录
    EventRecord event1("INFO", "TIMING_ENGINE", "STATE_CHANGE", "状态从DISCIPLINING转换到LOCKED", "{\"duration\":\"30s\"}");
    EventRecord event2("WARNING", "HAL_GNSS", "SIGNAL_QUALITY", "GNSS信号质量下降", "{\"satellites\":4}");
    
    // 测试单个事件插入
    result = db_manager.InsertEvent(event1);
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 测试批量事件插入
    std::vector<EventRecord> events = {event2};
    result = db_manager.InsertEvents(events);
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 测试事件查询
    DatabaseFilter filter;
    filter.limit = 10;
    
    std::vector<EventRecord> queried_events = db_manager.QueryEvents(filter);
    EXPECT_EQ(queried_events.size(), 2);
    
    // 验证查询结果
    bool found_state_change = false;
    bool found_signal_quality = false;
    
    for (const auto& event : queried_events) {
        if (event.event_type == "STATE_CHANGE" && event.component == "TIMING_ENGINE") {
            EXPECT_EQ(event.level, "INFO");
            EXPECT_EQ(event.message, "状态从DISCIPLINING转换到LOCKED");
            EXPECT_EQ(event.context, "{\"duration\":\"30s\"}");
            found_state_change = true;
        } else if (event.event_type == "SIGNAL_QUALITY" && event.component == "HAL_GNSS") {
            EXPECT_EQ(event.level, "WARNING");
            EXPECT_EQ(event.message, "GNSS信号质量下降");
            EXPECT_EQ(event.context, "{\"satellites\":4}");
            found_signal_quality = true;
        }
    }
    
    EXPECT_TRUE(found_state_change);
    EXPECT_TRUE(found_signal_quality);
}

// 测试状态转换记录
TEST_F(DatabaseManagerTest, StateTransitionOperations) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 初始化数据库
    DatabaseResult result = db_manager.Initialize(test_db_path_);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 禁用异步写入
    db_manager.SetAsyncWriting(false);
    
    // 创建测试状态转换记录
    StateTransitionRecord transition("DISCIPLINING", "LOCKED", "CONVERGENCE_ACHIEVED", 30000, true);
    
    // 测试状态转换插入
    result = db_manager.InsertStateTransition(transition);
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 测试状态转换查询
    DatabaseFilter filter;
    filter.limit = 10;
    
    std::vector<StateTransitionRecord> queried_transitions = db_manager.QueryStateTransitions(filter);
    EXPECT_EQ(queried_transitions.size(), 1);
    
    // 验证查询结果
    const auto& queried_transition = queried_transitions[0];
    EXPECT_EQ(queried_transition.from_state, "DISCIPLINING");
    EXPECT_EQ(queried_transition.to_state, "LOCKED");
    EXPECT_EQ(queried_transition.trigger_event, "CONVERGENCE_ACHIEVED");
    EXPECT_EQ(queried_transition.duration_ms, 30000);
    EXPECT_TRUE(queried_transition.success);
}

// 测试指标统计功能
TEST_F(DatabaseManagerTest, MetricStatistics) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 初始化数据库
    DatabaseResult result = db_manager.Initialize(test_db_path_);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 禁用异步写入
    db_manager.SetAsyncWriting(false);
    
    // 插入多个测试指标
    std::vector<MetricRecord> metrics;
    for (int i = 0; i < 10; ++i) {
        MetricRecord metric("phase_offset", "GNSS", 20.0 + i * 2.0, "ns", 95);
        metrics.push_back(metric);
    }
    
    result = db_manager.InsertMetrics(metrics);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 获取统计信息
    uint64_t start_time = 0;
    uint64_t end_time = GetCurrentTimestampNs();
    
    auto stats = db_manager.GetMetricStatistics("phase_offset", "GNSS", start_time, end_time);
    
    // 验证统计结果
    EXPECT_EQ(stats.count, 10);
    EXPECT_DOUBLE_EQ(stats.average, 29.0);  // (20+22+24+26+28+30+32+34+36+38)/10 = 29
    EXPECT_DOUBLE_EQ(stats.minimum, 20.0);
    EXPECT_DOUBLE_EQ(stats.maximum, 38.0);
    EXPECT_GT(stats.std_deviation, 0.0);
}

// 测试数据清理功能
TEST_F(DatabaseManagerTest, DataCleanup) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 初始化数据库
    DatabaseResult result = db_manager.Initialize(test_db_path_);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 禁用异步写入
    db_manager.SetAsyncWriting(false);
    
    // 插入一些测试数据
    MetricRecord metric("test_metric", "TEST_SOURCE", 100.0, "unit", 100);
    result = db_manager.InsertMetric(metric);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 验证数据存在
    DatabaseFilter filter;
    auto metrics = db_manager.QueryMetrics(filter);
    EXPECT_EQ(metrics.size(), 1);
    
    // 清理过期数据（保留0小时，即清理所有数据）
    result = db_manager.CleanupExpiredData(0, "metrics");
    EXPECT_EQ(result, DatabaseResult::SUCCESS);
    
    // 验证数据已被清理
    metrics = db_manager.QueryMetrics(filter);
    EXPECT_EQ(metrics.size(), 0);
}

// 测试异步写入功能
TEST_F(DatabaseManagerTest, AsyncWriting) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 初始化数据库
    DatabaseResult result = db_manager.Initialize(test_db_path_);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 启用异步写入
    db_manager.SetAsyncWriting(true);
    db_manager.SetBatchSize(5);
    
    // 插入多个指标（异步）
    for (int i = 0; i < 10; ++i) {
        MetricRecord metric("async_test", "TEST", i * 1.0, "unit", 100);
        result = db_manager.InsertMetric(metric);
        EXPECT_EQ(result, DatabaseResult::SUCCESS);
    }
    
    // 等待异步写入完成
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 刷新待写入数据
    db_manager.FlushPendingWrites();
    
    // 验证数据已写入
    DatabaseFilter filter;
    filter.types = {"async_test"};
    auto metrics = db_manager.QueryMetrics(filter);
    EXPECT_EQ(metrics.size(), 10);
}

// 测试数据库统计信息
TEST_F(DatabaseManagerTest, DatabaseStatistics) {
    DatabaseManager& db_manager = DatabaseManager::GetInstance();
    
    // 初始化数据库
    DatabaseResult result = db_manager.Initialize(test_db_path_);
    ASSERT_EQ(result, DatabaseResult::SUCCESS);
    
    // 禁用异步写入
    db_manager.SetAsyncWriting(false);
    
    // 插入测试数据
    MetricRecord metric("test_metric", "TEST", 1.0, "unit", 100);
    EventRecord event("INFO", "TEST", "TEST_EVENT", "测试事件", "{}");
    StateTransitionRecord transition("STATE_A", "STATE_B", "TEST_TRIGGER", 1000, true);
    
    db_manager.InsertMetric(metric);
    db_manager.InsertEvent(event);
    db_manager.InsertStateTransition(transition);
    
    // 获取统计信息
    auto stats = db_manager.GetDatabaseStatistics();
    
    // 验证统计结果
    EXPECT_EQ(stats.total_metrics, 1);
    EXPECT_EQ(stats.total_events, 1);
    EXPECT_EQ(stats.total_state_transitions, 1);
    EXPECT_GT(stats.oldest_record_timestamp, 0);
    EXPECT_GT(stats.newest_record_timestamp, 0);
}

// 测试LogEntry到EventRecord的转换
TEST_F(DatabaseManagerTest, LogEntryConversion) {
    // 创建测试LogEntry
    LogEntry log_entry(LogLevel::WARNING, LogComponent::HAL_GNSS, 
                      "GNSS信号质量下降", "test_file.cpp", 123, "test_function");
    log_entry.context["satellites"] = "4";
    log_entry.context["snr"] = "-150";
    
    // 转换为EventRecord
    EventRecord event_record(log_entry);
    
    // 验证转换结果
    EXPECT_EQ(event_record.timestamp_ns, log_entry.timestamp_ns);
    EXPECT_EQ(event_record.level, "WARNING");
    EXPECT_EQ(event_record.component, "HAL_GNSS");
    EXPECT_EQ(event_record.event_type, "LOG_EVENT");
    EXPECT_EQ(event_record.message, "GNSS信号质量下降");
    
    // 验证上下文JSON包含正确信息
    EXPECT_NE(event_record.context.find("\"file\":\"test_file.cpp\""), std::string::npos);
    EXPECT_NE(event_record.context.find("\"line\":123"), std::string::npos);
    EXPECT_NE(event_record.context.find("\"function\":\"test_function\""), std::string::npos);
    EXPECT_NE(event_record.context.find("\"satellites\":\"4\""), std::string::npos);
    EXPECT_NE(event_record.context.find("\"snr\":\"-150\""), std::string::npos);
}