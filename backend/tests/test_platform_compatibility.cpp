#include <gtest/gtest.h>
#include "core/platform_validator.h"
#include "hal/hal_factory.h"
#include "hal/platform_detector.h"
#include "core/logger.h"
#include <memory>
#include <thread>
#include <chrono>

using namespace timing_server::core;
using namespace timing_server::hal;

/**
 * @brief 平台兼容性测试套件
 * 验证不同平台的功能完整性和性能指标
 */
class PlatformCompatibilityTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 初始化日志系统
        Logger::GetInstance().Initialize();
        
        // 创建HAL工厂
        try {
            hal_factory_ = CreateHalFactory();
            ASSERT_NE(hal_factory_, nullptr) << "HAL工厂创建失败";
        } catch (const std::exception& e) {
            FAIL() << "HAL工厂创建异常: " << e.what();
        }
        
        // 创建平台验证器
        validator_ = std::make_unique<PlatformValidator>(hal_factory_);
        ASSERT_NE(validator_, nullptr) << "平台验证器创建失败";
        
        // 获取平台信息
        platform_info_ = PlatformDetector::DetectPlatform();
    }
    
    void TearDown() override {
        validator_.reset();
        hal_factory_.reset();
    }
    
    std::shared_ptr<I_HalFactory> hal_factory_;
    std::unique_ptr<PlatformValidator> validator_;
    PlatformInfo platform_info_;
};

/**
 * @brief 测试平台检测功能
 */
TEST_F(PlatformCompatibilityTest, PlatformDetection) {
    EXPECT_NE(platform_info_.type, PlatformType::UNKNOWN) 
        << "平台类型检测失败: " << platform_info_.description;
    
    EXPECT_FALSE(platform_info_.os_name.empty()) 
        << "操作系统名称为空";
    
    EXPECT_FALSE(platform_info_.architecture.empty()) 
        << "处理器架构为空";
    
    EXPECT_FALSE(platform_info_.description.empty()) 
        << "平台描述为空";
    
    // 验证平台类型字符串转换
    std::string platform_str = PlatformDetector::PlatformTypeToString(platform_info_.type);
    EXPECT_FALSE(platform_str.empty()) << "平台类型字符串转换失败";
    EXPECT_NE(platform_str, "unknown") << "平台类型应该被正确识别";
    
    std::cout << "检测到平台: " << platform_info_.description << std::endl;
    std::cout << "平台字符串: " << platform_str << std::endl;
}

/**
 * @brief 测试HAL工厂验证功能
 */
TEST_F(PlatformCompatibilityTest, HalFactoryValidation) {
    auto validation_result = ValidateHalFactory(hal_factory_);
    
    EXPECT_TRUE(validation_result.success) 
        << "HAL工厂验证失败: " << validation_result.summary;
    
    if (!validation_result.success) {
        std::cout << "HAL工厂验证错误:" << std::endl;
        for (const auto& error : validation_result.errors) {
            std::cout << "  - " << error << std::endl;
        }
    }
    
    if (!validation_result.warnings.empty()) {
        std::cout << "HAL工厂验证警告:" << std::endl;
        for (const auto& warning : validation_result.warnings) {
            std::cout << "  - " << warning << std::endl;
        }
    }
}

/**
 * @brief 测试完整的平台验证流程
 */
TEST_F(PlatformCompatibilityTest, FullPlatformValidation) {
    auto report = validator_->ValidatePlatform();
    
    // 验证报告基本信息
    EXPECT_EQ(report.platform_info.type, platform_info_.type);
    EXPECT_FALSE(report.validation_items.empty()) << "验证项目列表为空";
    EXPECT_GT(report.total_execution_time_ms, 0) << "执行时间应该大于0";
    
    // 统计验证结果
    size_t total_items = report.validation_items.size();
    EXPECT_GT(total_items, 0) << "应该有验证项目";
    
    // 检查是否有严重错误
    EXPECT_EQ(report.critical_count, 0) 
        << "存在严重错误，平台可能不兼容: " << report.summary;
    
    // 输出详细结果
    std::cout << "\n=== 平台验证结果 ===" << std::endl;
    std::cout << "平台: " << report.platform_info.description << std::endl;
    std::cout << "总体结果: " << (report.overall_success ? "通过" : "失败") << std::endl;
    std::cout << "统计: 成功=" << report.success_count 
              << ", 警告=" << report.warning_count 
              << ", 错误=" << report.error_count 
              << ", 严重=" << report.critical_count << std::endl;
    std::cout << "执行时间: " << report.total_execution_time_ms << "ms" << std::endl;
    
    // 输出详细验证项目
    for (const auto& item : report.validation_items) {
        std::string status;
        switch (item.result_type) {
            case ValidationResultType::SUCCESS: status = "✓ 成功"; break;
            case ValidationResultType::WARNING: status = "⚠ 警告"; break;
            case ValidationResultType::ERROR: status = "✗ 错误"; break;
            case ValidationResultType::CRITICAL: status = "⚠ 严重"; break;
        }
        std::cout << status << " - " << item.test_name << ": " << item.message << std::endl;
        if (!item.details.empty()) {
            std::cout << "    详情: " << item.details << std::endl;
        }
    }
    
    // 输出优化建议
    if (!report.recommendations.empty()) {
        std::cout << "\n优化建议:" << std::endl;
        for (const auto& recommendation : report.recommendations) {
            std::cout << "  - " << recommendation << std::endl;
        }
    }
}

/**
 * @brief 测试性能基准测试
 */
TEST_F(PlatformCompatibilityTest, PerformanceBenchmarks) {
    auto report = validator_->RunBenchmarks();
    
    // 验证基准测试报告
    EXPECT_EQ(report.platform_info.type, platform_info_.type);
    EXPECT_FALSE(report.benchmarks.empty()) << "基准测试结果为空";
    EXPECT_GE(report.overall_score, 0.0) << "总体评分应该非负";
    EXPECT_LE(report.overall_score, 100.0) << "总体评分应该不超过100";
    EXPECT_FALSE(report.performance_grade.empty()) << "性能等级为空";
    
    // 输出基准测试结果
    std::cout << "\n=== 性能基准测试结果 ===" << std::endl;
    std::cout << "平台: " << report.platform_info.description << std::endl;
    std::cout << "总体评分: " << report.overall_score << " (" << report.performance_grade << ")" << std::endl;
    
    for (const auto& benchmark : report.benchmarks) {
        std::cout << "\n" << benchmark.benchmark_name << ":" << std::endl;
        std::cout << "  平均延迟: " << benchmark.average_latency_ns << " ns" << std::endl;
        std::cout << "  最大延迟: " << benchmark.max_latency_ns << " ns" << std::endl;
        std::cout << "  最小延迟: " << benchmark.min_latency_ns << " ns" << std::endl;
        std::cout << "  吞吐量: " << benchmark.throughput_ops_per_sec << " ops/s" << std::endl;
        std::cout << "  CPU使用率: " << benchmark.cpu_usage_percent << "%" << std::endl;
        std::cout << "  内存使用: " << benchmark.memory_usage_mb << " MB" << std::endl;
    }
    
    // 输出优化建议
    if (!report.optimization_tips.empty()) {
        std::cout << "\n性能优化建议:" << std::endl;
        for (const auto& tip : report.optimization_tips) {
            std::cout << "  - " << tip << std::endl;
        }
    }
}

/**
 * @brief 测试Linux x86_64平台特定功能
 */
TEST_F(PlatformCompatibilityTest, LinuxX86_64Specific) {
    if (platform_info_.type != PlatformType::LINUX_X86_64) {
        GTEST_SKIP() << "跳过Linux x86_64特定测试，当前平台: " << platform_info_.description;
    }
    
    auto linux_tests = validator_->ValidateLinuxX86_64();
    EXPECT_FALSE(linux_tests.empty()) << "Linux x86_64验证项目为空";
    
    // 检查是否有x86_64特定的优化验证
    bool found_x86_64_optimization = false;
    for (const auto& test : linux_tests) {
        if (test.test_name.find("x86_64") != std::string::npos) {
            found_x86_64_optimization = true;
            EXPECT_EQ(test.result_type, ValidationResultType::SUCCESS) 
                << "x86_64优化验证失败: " << test.message;
            break;
        }
    }
    EXPECT_TRUE(found_x86_64_optimization) << "未找到x86_64特定优化验证";
    
    std::cout << "\nLinux x86_64特定验证结果:" << std::endl;
    for (const auto& test : linux_tests) {
        std::cout << "  " << test.test_name << ": " << test.message << std::endl;
    }
}

/**
 * @brief 测试龙芯LoongArch64平台特定功能
 */
TEST_F(PlatformCompatibilityTest, LoongArch64Specific) {
    if (platform_info_.type != PlatformType::LINUX_LOONGARCH64) {
        GTEST_SKIP() << "跳过龙芯LoongArch64特定测试，当前平台: " << platform_info_.description;
    }
    
    auto loongarch_tests = validator_->ValidateLoongArch64();
    EXPECT_FALSE(loongarch_tests.empty()) << "龙芯LoongArch64验证项目为空";
    
    // 检查是否有龙芯特定的优化验证
    bool found_loongarch_optimization = false;
    for (const auto& test : loongarch_tests) {
        if (test.test_name.find("龙芯") != std::string::npos) {
            found_loongarch_optimization = true;
            // 龙芯优化可能是警告状态（如果在模拟环境中）
            EXPECT_TRUE(test.result_type == ValidationResultType::SUCCESS || 
                       test.result_type == ValidationResultType::WARNING) 
                << "龙芯优化验证失败: " << test.message;
            break;
        }
    }
    EXPECT_TRUE(found_loongarch_optimization) << "未找到龙芯特定优化验证";
    
    std::cout << "\n龙芯LoongArch64特定验证结果:" << std::endl;
    for (const auto& test : loongarch_tests) {
        std::cout << "  " << test.test_name << ": " << test.message << std::endl;
    }
}

/**
 * @brief 测试macOS Mock HAL功能
 */
TEST_F(PlatformCompatibilityTest, MacOSMockHalSpecific) {
    if (!PlatformDetector::IsMacOSPlatform(platform_info_.type)) {
        GTEST_SKIP() << "跳过macOS Mock HAL特定测试，当前平台: " << platform_info_.description;
    }
    
    auto macos_tests = validator_->ValidateMacOSMockHal();
    EXPECT_FALSE(macos_tests.empty()) << "macOS Mock HAL验证项目为空";
    
    // 检查Mock数据源验证
    bool found_mock_data_validation = false;
    for (const auto& test : macos_tests) {
        if (test.test_name.find("Mock") != std::string::npos) {
            found_mock_data_validation = true;
            // Mock数据验证可能是警告状态（如果某些文件缺失）
            EXPECT_TRUE(test.result_type == ValidationResultType::SUCCESS || 
                       test.result_type == ValidationResultType::WARNING) 
                << "Mock数据验证失败: " << test.message;
            break;
        }
    }
    EXPECT_TRUE(found_mock_data_validation) << "未找到Mock数据源验证";
    
    std::cout << "\nmacOS Mock HAL特定验证结果:" << std::endl;
    for (const auto& test : macos_tests) {
        std::cout << "  " << test.test_name << ": " << test.message << std::endl;
    }
}

/**
 * @brief 测试优化建议生成
 */
TEST_F(PlatformCompatibilityTest, OptimizationRecommendations) {
    auto recommendations = validator_->GenerateOptimizationRecommendations(platform_info_.type);
    
    EXPECT_FALSE(recommendations.empty()) << "优化建议列表为空";
    
    // 验证建议内容不为空
    for (const auto& recommendation : recommendations) {
        EXPECT_FALSE(recommendation.empty()) << "优化建议内容为空";
    }
    
    std::cout << "\n平台优化建议:" << std::endl;
    for (const auto& recommendation : recommendations) {
        std::cout << "  - " << recommendation << std::endl;
    }
}

/**
 * @brief 测试完整兼容性测试套件
 */
TEST_F(PlatformCompatibilityTest, FullCompatibilityTestSuite) {
    bool result = PlatformCompatibilityTestSuite::RunFullCompatibilityTest();
    
    // 注意：这个测试可能会失败，特别是在缺少某些硬件设备的环境中
    // 但不应该有严重错误导致程序崩溃
    std::cout << "\n完整兼容性测试结果: " << (result ? "通过" : "部分失败") << std::endl;
    
    // 验证报告文件是否生成
    std::ifstream report_file("platform_compatibility_report.txt");
    if (report_file.good()) {
        std::cout << "兼容性报告文件已生成" << std::endl;
        report_file.close();
    } else {
        std::cout << "兼容性报告文件生成失败或不存在" << std::endl;
    }
}

/**
 * @brief 测试交叉编译验证
 */
TEST_F(PlatformCompatibilityTest, CrossCompilationValidation) {
    bool result = PlatformCompatibilityTestSuite::ValidateCrossCompilation();
    
    std::cout << "\n交叉编译验证结果: " << (result ? "通过" : "失败") << std::endl;
    
    // 在非Linux环境或没有交叉编译工具链的环境中，这个测试可能会失败
    // 这是正常的，不应该导致整个测试套件失败
    if (!result) {
        std::cout << "注意: 交叉编译工具链可能未安装或不可用" << std::endl;
    }
}

/**
 * @brief 测试运行时稳定性（短时间版本）
 */
TEST_F(PlatformCompatibilityTest, RuntimeStabilityShort) {
    // 运行1分钟的稳定性测试
    bool result = PlatformCompatibilityTestSuite::ValidateRuntimeStability(1);
    
    EXPECT_TRUE(result) << "运行时稳定性测试失败";
    
    std::cout << "\n运行时稳定性测试结果: " << (result ? "通过" : "失败") << std::endl;
}

/**
 * @brief 性能回归测试
 * 验证性能指标是否在可接受范围内
 */
TEST_F(PlatformCompatibilityTest, PerformanceRegression) {
    auto report = validator_->RunBenchmarks();
    
    // 定义性能阈值（根据平台类型调整）
    double min_acceptable_score = 50.0; // 最低可接受评分
    
    if (PlatformDetector::IsMacOSPlatform(platform_info_.type)) {
        // macOS开发环境可以接受较低的性能
        min_acceptable_score = 30.0;
    }
    
    EXPECT_GE(report.overall_score, min_acceptable_score) 
        << "性能评分过低: " << report.overall_score << " < " << min_acceptable_score;
    
    // 检查关键性能指标
    for (const auto& benchmark : report.benchmarks) {
        if (benchmark.benchmark_name == "时间戳精度测试") {
            // 时间戳精度应该在合理范围内
            EXPECT_LT(benchmark.average_latency_ns, 10000.0) 
                << "时间戳精度过低: " << benchmark.average_latency_ns << "ns";
        }
        
        if (benchmark.benchmark_name == "内存使用测试") {
            // 内存使用应该在合理范围内
            EXPECT_LT(benchmark.memory_usage_mb, 200.0) 
                << "内存使用过高: " << benchmark.memory_usage_mb << "MB";
        }
    }
    
    std::cout << "\n性能回归测试通过，总体评分: " << report.overall_score << std::endl;
}

/**
 * @brief 并发安全性测试
 * 验证平台验证器在并发环境下的稳定性
 */
TEST_F(PlatformCompatibilityTest, ConcurrentSafety) {
    const int num_threads = 4;
    const int iterations_per_thread = 10;
    
    std::vector<std::thread> threads;
    std::atomic<int> success_count(0);
    std::atomic<int> error_count(0);
    
    // 启动多个线程并发执行验证
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < iterations_per_thread; ++j) {
                try {
                    auto thread_hal_factory = CreateHalFactory();
                    PlatformValidator thread_validator(thread_hal_factory);
                    
                    auto report = thread_validator.ValidatePlatform();
                    if (report.critical_count == 0) {
                        success_count++;
                    } else {
                        error_count++;
                    }
                    
                } catch (const std::exception& e) {
                    error_count++;
                    std::cerr << "线程 " << i << " 迭代 " << j << " 异常: " << e.what() << std::endl;
                }
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    int total_operations = num_threads * iterations_per_thread;
    double success_rate = static_cast<double>(success_count) / total_operations * 100.0;
    
    std::cout << "\n并发安全性测试结果:" << std::endl;
    std::cout << "  总操作数: " << total_operations << std::endl;
    std::cout << "  成功数: " << success_count << std::endl;
    std::cout << "  错误数: " << error_count << std::endl;
    std::cout << "  成功率: " << success_rate << "%" << std::endl;
    
    // 要求成功率至少90%
    EXPECT_GE(success_rate, 90.0) << "并发安全性测试成功率过低";
}

/**
 * @brief 主函数 - 运行所有平台兼容性测试
 */
int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    std::cout << "=== 平台兼容性测试套件 ===" << std::endl;
    std::cout << "开始执行多平台兼容性验证测试..." << std::endl;
    
    int result = RUN_ALL_TESTS();
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    std::cout << "测试结果: " << (result == 0 ? "全部通过" : "部分失败") << std::endl;
    
    return result;
}