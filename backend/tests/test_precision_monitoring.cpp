#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "core/precision_monitor.h"
#include "core/alarm_system.h"
#include "core/timing_engine.h"
#include <memory>
#include <thread>
#include <chrono>

using namespace timing_server::core;
using namespace testing;

// Test constants
namespace {
    constexpr double DEFAULT_ACCURACY_NS = 25.0;
    constexpr double DEFAULT_AVERAGE_ACCURACY_NS = 30.0;
    constexpr double DEFAULT_MAX_ACCURACY_NS = 45.0;
    constexpr double DEFAULT_ALLAN_DEVIATION_1S = 1e-12;
    constexpr double DEFAULT_ALLAN_DEVIATION_10S = 5e-13;
    constexpr double DEFAULT_ALLAN_DEVIATION_100S = 2e-13;
    constexpr uint32_t DEFAULT_GNSS_SATELLITES = 12;
    constexpr double DEFAULT_GNSS_SNR_DB = -140.0;
    constexpr uint32_t DEFAULT_UPTIME_SECONDS = 3600;
    constexpr double DEFAULT_CPU_USAGE_PERCENT = 5.0;
    constexpr uint64_t DEFAULT_MEMORY_USAGE_MB = 50;
    constexpr uint32_t DEFAULT_CONFIDENCE = 95;
    constexpr double SPEC_ACCURACY_REQUIREMENT_NS = 50.0;
    constexpr double TIGHT_ACCURACY_REQUIREMENT_NS = 10.0;
    constexpr uint32_t EXPECTED_HEALTH_SCORE_THRESHOLD = 70;
}

/**
 * @brief Mock授时引擎类
 * 用于测试精度监控功能
 */
class MockTimingEngine : public ITimingEngine {
public:
    MOCK_METHOD(bool, Start, (), (override));
    MOCK_METHOD(bool, Stop, (), (override));
    MOCK_METHOD(ClockState, GetCurrentState, (), (override));
    MOCK_METHOD(SystemStatus, GetSystemStatus, (), (override));
    MOCK_METHOD(bool, ConfigureTimeSource, (const TimeSourcePriority& config), (override));
    MOCK_METHOD(TimeSource, GetActiveTimeSource, (), (override));
    MOCK_METHOD(TimeSourceInfo, GetTimeSourceInfo, (TimeSource source), (override));
    MOCK_METHOD(std::vector<TimeSourceInfo>, GetAllTimeSourceInfo, (), (override));
    MOCK_METHOD(PerformanceMetrics, GetPerformanceMetrics, (), (const));
};

namespace timing_server {
namespace core {
namespace test {

/**
 * @brief 精度监控器基础功能测试类
 */
class PrecisionMonitorBasicTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_timing_engine_ = std::make_shared<MockTimingEngine>();
        SetupDefaultMockBehavior();
        precision_monitor_ = CreatePrecisionMonitor();
    }

private:
    void SetupDefaultMockBehavior() {
        ON_CALL(*mock_timing_engine_, GetCurrentState())
            .WillByDefault(Return(ClockState::LOCKED));
        ON_CALL(*mock_timing_engine_, GetActiveTimeSource())
            .WillByDefault(Return(TimeSource::GNSS));
        ON_CALL(*mock_timing_engine_, GetSystemStatus())
            .WillByDefault(Return(CreateDefaultSystemStatus()));
        ON_CALL(*mock_timing_engine_, GetPerformanceMetrics())
            .WillByDefault(Return(CreateDefaultPerformanceMetrics()));
        ON_CALL(*mock_timing_engine_, GetTimeSourceInfo(TimeSource::GNSS))
            .WillByDefault(Return(CreateDefaultGnssInfo()));
    }

    SystemStatus CreateDefaultSystemStatus() {
        SystemStatus status;
        status.current_state = ClockState::LOCKED;
        status.active_source = TimeSource::GNSS;
        status.health = SystemHealth::HEALTHY;
        status.uptime_seconds = 3600;
        status.cpu_usage_percent = 5.0;
        status.memory_usage_mb = 50;
        return status;
    }

    PerformanceMetrics CreateDefaultPerformanceMetrics() {
        PerformanceMetrics metrics;
        metrics.current_accuracy_ns = 25.0;
        metrics.average_accuracy_ns = 30.0;
        metrics.max_accuracy_ns = 45.0;
        metrics.allan_deviation_1s = 1e-12;
        metrics.allan_deviation_10s = 5e-13;
        metrics.allan_deviation_100s = 2e-13;
        return metrics;
    }

    TimeSourceInfo CreateDefaultGnssInfo() {
        TimeSourceInfo gnss_info;
        gnss_info.type = TimeSource::GNSS;
        gnss_info.status = TimeSourceStatus::ACTIVE;
        gnss_info.quality.accuracy_ns = 25.0;
        gnss_info.quality.stability_ppm = 1e-12;
        gnss_info.quality.confidence = 95;
        gnss_info.properties["satellites"] = "12";
        gnss_info.properties["snr_db"] = "-140";
        return gnss_info;
    }

    std::unique_ptr<PrecisionMonitor> CreatePrecisionMonitor() {
        return std::make_unique<PrecisionMonitor>(mock_timing_engine_, CreateDefaultAlarmThresholds());
    }

    AlarmThresholds CreateDefaultAlarmThresholds() {
        AlarmThresholds thresholds;
        thresholds.phase_offset_warning_ns = 100.0;
        thresholds.phase_offset_critical_ns = 500.0;
        thresholds.frequency_offset_warning_ppm = 0.01;
        thresholds.frequency_offset_critical_ppm = 0.1;
        thresholds.gnss_satellites_warning = 6;
        thresholds.gnss_satellites_critical = 4;
        thresholds.gnss_snr_warning_db = -145.0;
        thresholds.gnss_snr_critical_db = -150.0;
        thresholds.cpu_usage_warning = 80.0;
        thresholds.memory_usage_warning = 80.0;
        thresholds.temperature_warning = 70.0;
        thresholds.temperature_critical = 75.0;
        return thresholds;
    }
    
    void TearDown() override {
        if (precision_monitor_) {
            precision_monitor_->Stop();
        }
    }

protected:
    std::shared_ptr<MockTimingEngine> mock_timing_engine_;
    std::unique_ptr<PrecisionMonitor> precision_monitor_;
};

/**
 * @brief 测试精度监控器的基本功能
 */
TEST_F(PrecisionMonitorBasicTest, BasicFunctionality) {
    // 测试启动和停止
    EXPECT_TRUE(precision_monitor_->Start());
    EXPECT_TRUE(precision_monitor_->Stop());
    
    // 测试重复启动
    EXPECT_TRUE(precision_monitor_->Start());
    EXPECT_TRUE(precision_monitor_->Start()); // 应该返回true但不重复启动
    EXPECT_TRUE(precision_monitor_->Stop());
}

/**
 * @brief 测试精度测量功能
 */
TEST_F(PrecisionMonitorBasicTest, PrecisionMeasurement) {
    // 获取当前测量
    auto measurement = precision_monitor_->GetCurrentMeasurement();
    
    // 验证测量数据
    EXPECT_GT(measurement.timestamp_ns, 0);
    EXPECT_EQ(measurement.system_state, ClockState::LOCKED);
    EXPECT_EQ(measurement.source, TimeSource::GNSS);
    EXPECT_EQ(measurement.absolute_accuracy_ns, 25.0);
    EXPECT_EQ(measurement.gnss_satellites, 12);
    EXPECT_EQ(measurement.gnss_snr_db, -140.0);
    EXPECT_GT(measurement.overall_quality_score, 0);
    EXPECT_TRUE(measurement.meets_spec_requirements);
}

/**
 * @brief 测试历史数据管理
 */
TEST_F(PrecisionMonitorBasicTest, HistoricalData) {
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 强制执行几次测量而不是等待
    precision_monitor_->ForceMeasurement();
    precision_monitor_->ForceMeasurement();
    precision_monitor_->ForceMeasurement();
    
    uint64_t current_time = GetCurrentTimestampNs();
    uint64_t start_time = current_time - 5000000000ULL; // 5秒前
    
    // 获取历史数据
    auto historical_data = precision_monitor_->GetHistoricalMeasurements(start_time, current_time, 100);
    
    // 应该有一些历史数据
    EXPECT_GT(historical_data.size(), 0);
    
    // 验证数据按时间排序
    for (size_t i = 1; i < historical_data.size(); ++i) {
        EXPECT_GE(historical_data[i].timestamp_ns, historical_data[i-1].timestamp_ns);
    }
    
    EXPECT_TRUE(precision_monitor_->Stop());
}

/**
 * @brief 测试精度趋势分析
 */
TEST_F(PrecisionMonitorBasicTest, PrecisionTrend) {
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 等待一些测量数据
    std::this_thread::sleep_for(std::chrono::milliseconds(2100));
    
    // 获取趋势分析
    auto trend = precision_monitor_->GetPrecisionTrend(1); // 1小时趋势
    
    // 验证趋势数据
    EXPECT_EQ(trend.analysis_period_hours, 1);
    EXPECT_GE(trend.mean_accuracy_ns, 0);
    EXPECT_GE(trend.std_deviation_accuracy_ns, 0);
    EXPECT_GE(trend.confidence_level, 0.0);
    EXPECT_LE(trend.confidence_level, 1.0);
    
    EXPECT_TRUE(precision_monitor_->Stop());
}

/**
 * @brief 测试系统健康评分
 */
TEST_F(PrecisionMonitorBasicTest, SystemHealthScore) {
    auto health_score = precision_monitor_->GetSystemHealthScore();
    
    // 验证健康评分
    EXPECT_GE(health_score.overall_score, 0);
    EXPECT_LE(health_score.overall_score, 100);
    EXPECT_GE(health_score.timing_accuracy_score, 0);
    EXPECT_LE(health_score.timing_accuracy_score, 100);
    EXPECT_GE(health_score.signal_quality_score, 0);
    EXPECT_LE(health_score.signal_quality_score, 100);
    
    // 在LOCKED状态下，健康评分应该较高
    EXPECT_GT(health_score.overall_score, 70);
    EXPECT_EQ(health_score.health_status, SystemHealth::HEALTHY);
}

/**
 * @brief 测试预测性维护建议
 */
TEST_F(PrecisionMonitorBasicTest, MaintenanceAdvice) {
    auto advice = precision_monitor_->GetMaintenanceAdvice();
    
    // 验证维护建议
    EXPECT_GT(advice.analysis_timestamp_ns, 0);
    EXPECT_GT(advice.estimated_days_to_failure, 0);
    EXPECT_GE(advice.failure_probability, 0.0);
    EXPECT_LE(advice.failure_probability, 1.0);
    EXPECT_FALSE(advice.primary_concern.empty());
    
    // 应该有组件预测
    EXPECT_GT(advice.component_predictions.size(), 0);
    
    for (const auto& prediction : advice.component_predictions) {
        EXPECT_FALSE(prediction.component_name.empty());
        EXPECT_GE(prediction.health_percentage, 0);
        EXPECT_LE(prediction.health_percentage, 100);
        EXPECT_GT(prediction.estimated_lifetime_days, 0);
    }
}

/**
 * @brief 测试精度要求检查
 */
TEST_F(PrecisionMonitorBasicTest, AccuracyRequirement) {
    // 测试满足50ns精度要求
    EXPECT_TRUE(precision_monitor_->MeetsAccuracyRequirement(SPEC_ACCURACY_REQUIREMENT_NS));
    
    // 测试不满足10ns精度要求
    EXPECT_FALSE(precision_monitor_->MeetsAccuracyRequirement(TIGHT_ACCURACY_REQUIREMENT_NS));
    
    // 测试边界条件
    EXPECT_TRUE(precision_monitor_->MeetsAccuracyRequirement(DEFAULT_ACCURACY_NS));
    EXPECT_FALSE(precision_monitor_->MeetsAccuracyRequirement(0.0));
    
    // 测试异常输入
    EXPECT_FALSE(precision_monitor_->MeetsAccuracyRequirement(-1.0));
}

/**
 * @brief 测试测量回调功能
 */
TEST_F(PrecisionMonitorBasicTest, MeasurementCallback) {
    bool callback_called = false;
    PrecisionMeasurement received_measurement;
    
    // 设置回调函数
    precision_monitor_->SetMeasurementCallback([&](const PrecisionMeasurement& measurement) {
        callback_called = true;
        received_measurement = measurement;
    });
    
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 等待回调被调用
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));
    
    EXPECT_TRUE(callback_called);
    EXPECT_GT(received_measurement.timestamp_ns, 0);
    EXPECT_EQ(received_measurement.system_state, ClockState::LOCKED);
    
    EXPECT_TRUE(precision_monitor_->Stop());
}

/**
 * @brief 测试数据导出功能
 */
TEST_F(PrecisionMonitorBasicTest, DataExport) {
    EXPECT_TRUE(precision_monitor_->Start());
    
    // 等待一些测量数据
    std::this_thread::sleep_for(std::chrono::milliseconds(2100));
    
    // 测试CSV导出
    std::string csv_filename = "/tmp/test_measurements.csv";
    EXPECT_TRUE(precision_monitor_->ExportMeasurementData(csv_filename, "csv"));
    
    // 测试JSON导出
    std::string json_filename = "/tmp/test_measurements.json";
    EXPECT_TRUE(precision_monitor_->ExportMeasurementData(json_filename, "json"));
    
    // 测试不支持的格式
    EXPECT_FALSE(precision_monitor_->ExportMeasurementData("/tmp/test.xml", "xml"));
    
    EXPECT_TRUE(precision_monitor_->Stop());
}

/**
 * @brief 告警系统测试类
 */
class AlarmSystemTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建Mock精度监控器
        mock_precision_monitor_ = std::make_shared<MockPrecisionMonitor>();
        
        // 创建告警系统
        alarm_system_ = std::make_unique<SmartAlarmSystem>(mock_precision_monitor_);
    }
    
    void TearDown() override {
        if (alarm_system_) {
            alarm_system_->Stop();
        }
    }
    
    /**
     * @brief Mock精度监控器类
     */
    class MockPrecisionMonitor : public IPrecisionMonitor {
    public:
        MOCK_METHOD(bool, Start, (), (override));
        MOCK_METHOD(bool, Stop, (), (override));
        MOCK_METHOD(void, SetMonitoringLevel, (MonitoringLevel level), (override));
        MOCK_METHOD(PrecisionMeasurement, GetCurrentMeasurement, (), (override));
        MOCK_METHOD(std::vector<PrecisionMeasurement>, GetHistoricalMeasurements, 
                   (uint64_t start_time, uint64_t end_time, uint32_t max_samples), (override));
        MOCK_METHOD(PrecisionTrend, GetPrecisionTrend, (uint32_t period_hours), (override));
        MOCK_METHOD(SystemHealthScore, GetSystemHealthScore, (), (override));
        MOCK_METHOD(PredictiveMaintenanceAdvice, GetMaintenanceAdvice, (), (override));
        MOCK_METHOD(bool, MeetsAccuracyRequirement, (double required_accuracy_ns), (override));
        MOCK_METHOD(void, SetMeasurementCallback, 
                   (std::function<void(const PrecisionMeasurement&)> callback), (override));
    };

protected:
    std::shared_ptr<MockPrecisionMonitor> mock_precision_monitor_;
    std::unique_ptr<SmartAlarmSystem> alarm_system_;
};

/**
 * @brief 测试告警系统的基本功能
 */
TEST_F(AlarmSystemTest, BasicFunctionality) {
    // 测试启动和停止
    EXPECT_TRUE(alarm_system_->Start());
    EXPECT_TRUE(alarm_system_->Stop());
    
    // 测试重复启动
    EXPECT_TRUE(alarm_system_->Start());
    EXPECT_TRUE(alarm_system_->Start()); // 应该返回true但不重复启动
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警触发功能
 */
TEST_F(AlarmSystemTest, AlarmTriggering) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 创建测试告警
    AlarmEvent test_alarm(AlarmType::ACCURACY_DEGRADED, AlarmLevel::ERROR, "测试精度告警");
    test_alarm.description = "系统精度超出允许范围";
    test_alarm.source_component = "PrecisionMonitor";
    test_alarm.threshold_value = 50.0;
    test_alarm.current_value = 120.0;
    test_alarm.unit = "ns";
    
    // 触发告警
    uint64_t alarm_id = alarm_system_->TriggerAlarm(test_alarm);
    EXPECT_GT(alarm_id, 0);
    
    // 获取活跃告警
    auto active_alarms = alarm_system_->GetActiveAlarms();
    EXPECT_EQ(active_alarms.size(), 1);
    EXPECT_EQ(active_alarms[0].id, alarm_id);
    EXPECT_EQ(active_alarms[0].type, AlarmType::ACCURACY_DEGRADED);
    EXPECT_EQ(active_alarms[0].level, AlarmLevel::ERROR);
    EXPECT_EQ(active_alarms[0].status, AlarmStatus::ACTIVE);
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警确认功能
 */
TEST_F(AlarmSystemTest, AlarmAcknowledgment) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 触发告警
    AlarmEvent test_alarm(AlarmType::GNSS_SATELLITES_LOW, AlarmLevel::WARNING, "GNSS卫星数量不足");
    uint64_t alarm_id = alarm_system_->TriggerAlarm(test_alarm);
    
    // 确认告警
    EXPECT_TRUE(alarm_system_->AcknowledgeAlarm(alarm_id, "test_user", "已知问题，正在处理"));
    
    // 验证告警状态
    auto active_alarms = alarm_system_->GetActiveAlarms();
    EXPECT_EQ(active_alarms.size(), 1);
    EXPECT_EQ(active_alarms[0].status, AlarmStatus::ACKNOWLEDGED);
    EXPECT_EQ(active_alarms[0].acknowledged_by, "test_user");
    EXPECT_GT(active_alarms[0].acknowledged_time_ns, 0);
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警解决功能
 */
TEST_F(AlarmSystemTest, AlarmResolution) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 触发告警
    AlarmEvent test_alarm(AlarmType::CPU_USAGE_HIGH, AlarmLevel::WARNING, "CPU使用率过高");
    uint64_t alarm_id = alarm_system_->TriggerAlarm(test_alarm);
    
    // 解决告警
    EXPECT_TRUE(alarm_system_->ResolveAlarm(alarm_id, "优化了算法，CPU使用率已降低"));
    
    // 验证活跃告警列表为空
    auto active_alarms = alarm_system_->GetActiveAlarms();
    EXPECT_EQ(active_alarms.size(), 0);
    
    // 验证历史告警
    uint64_t current_time = GetCurrentTimestampNs();
    auto historical_alarms = alarm_system_->GetHistoricalAlarms(
        current_time - 60000000000ULL, current_time, 100);
    EXPECT_EQ(historical_alarms.size(), 1);
    EXPECT_EQ(historical_alarms[0].status, AlarmStatus::RESOLVED);
    EXPECT_EQ(historical_alarms[0].resolution_notes, "优化了算法，CPU使用率已降低");
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警抑制功能
 */
TEST_F(AlarmSystemTest, AlarmSuppression) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 触发告警
    AlarmEvent test_alarm(AlarmType::RUBIDIUM_TEMPERATURE_HIGH, AlarmLevel::ERROR, "铷钟温度过高");
    uint64_t alarm_id = alarm_system_->TriggerAlarm(test_alarm);
    
    // 抑制告警
    EXPECT_TRUE(alarm_system_->SuppressAlarm(alarm_id, 300)); // 抑制5分钟
    
    // 验证告警状态
    auto active_alarms = alarm_system_->GetActiveAlarms();
    EXPECT_EQ(active_alarms.size(), 1);
    EXPECT_EQ(active_alarms[0].status, AlarmStatus::SUPPRESSED);
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警规则管理
 */
TEST_F(AlarmSystemTest, AlarmRuleManagement) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 创建告警规则
    AlarmRule test_rule;
    test_rule.rule_name = "测试精度规则";
    test_rule.alarm_type = AlarmType::ACCURACY_DEGRADED;
    test_rule.alarm_level = AlarmLevel::ERROR;
    test_rule.metric_name = "absolute_accuracy_ns";
    test_rule.condition = ">";
    test_rule.threshold_value = 100.0;
    test_rule.duration_seconds = 60;
    test_rule.enabled = true;
    
    // 添加规则
    uint64_t rule_id = alarm_system_->AddAlarmRule(test_rule);
    EXPECT_GT(rule_id, 0);
    
    // 获取规则列表
    auto rules = alarm_system_->GetAlarmRules();
    EXPECT_GT(rules.size(), 0);
    
    // 查找添加的规则
    bool rule_found = false;
    for (const auto& rule : rules) {
        if (rule.rule_id == rule_id) {
            EXPECT_EQ(rule.rule_name, "测试精度规则");
            EXPECT_EQ(rule.alarm_type, AlarmType::ACCURACY_DEGRADED);
            EXPECT_EQ(rule.threshold_value, 100.0);
            rule_found = true;
            break;
        }
    }
    EXPECT_TRUE(rule_found);
    
    // 更新规则
    test_rule.rule_id = rule_id;
    test_rule.threshold_value = 150.0;
    EXPECT_TRUE(alarm_system_->UpdateAlarmRule(test_rule));
    
    // 删除规则
    EXPECT_TRUE(alarm_system_->RemoveAlarmRule(rule_id));
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警统计功能
 */
TEST_F(AlarmSystemTest, AlarmStatistics) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 触发多个不同类型的告警
    AlarmEvent alarm1(AlarmType::ACCURACY_DEGRADED, AlarmLevel::ERROR, "精度告警");
    AlarmEvent alarm2(AlarmType::GNSS_SATELLITES_LOW, AlarmLevel::WARNING, "卫星告警");
    AlarmEvent alarm3(AlarmType::CPU_USAGE_HIGH, AlarmLevel::WARNING, "CPU告警");
    
    uint64_t id1 = alarm_system_->TriggerAlarm(alarm1);
    uint64_t id2 = alarm_system_->TriggerAlarm(alarm2);
    uint64_t id3 = alarm_system_->TriggerAlarm(alarm3);
    
    // 确认一个告警
    alarm_system_->AcknowledgeAlarm(id1, "admin");
    
    // 解决一个告警
    alarm_system_->ResolveAlarm(id2);
    
    // 获取统计信息
    auto stats = alarm_system_->GetAlarmStatistics();
    
    EXPECT_EQ(stats.active_alarms, 1); // id3还是活跃的
    EXPECT_EQ(stats.acknowledged_alarms, 1); // id1被确认
    EXPECT_EQ(stats.resolved_alarms, 1); // id2被解决
    EXPECT_GE(stats.total_alarms, 3);
    
    // 验证按级别统计
    EXPECT_GE(stats.alarms_by_level[AlarmLevel::ERROR], 1);
    EXPECT_GE(stats.alarms_by_level[AlarmLevel::WARNING], 2);
    
    // 验证按类型统计
    EXPECT_GE(stats.alarms_by_type[AlarmType::ACCURACY_DEGRADED], 1);
    EXPECT_GE(stats.alarms_by_type[AlarmType::GNSS_SATELLITES_LOW], 1);
    EXPECT_GE(stats.alarms_by_type[AlarmType::CPU_USAGE_HIGH], 1);
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警回调功能
 */
TEST_F(AlarmSystemTest, AlarmCallback) {
    bool callback_called = false;
    AlarmEvent received_alarm;
    
    // 设置回调函数
    alarm_system_->SetAlarmCallback([&](const AlarmEvent& alarm) {
        callback_called = true;
        received_alarm = alarm;
    });
    
    EXPECT_TRUE(alarm_system_->Start());
    
    // 触发告警
    AlarmEvent test_alarm(AlarmType::MEMORY_USAGE_HIGH, AlarmLevel::WARNING, "内存使用率过高");
    uint64_t alarm_id = alarm_system_->TriggerAlarm(test_alarm);
    
    // 验证回调被调用
    EXPECT_TRUE(callback_called);
    EXPECT_EQ(received_alarm.id, alarm_id);
    EXPECT_EQ(received_alarm.type, AlarmType::MEMORY_USAGE_HIGH);
    EXPECT_EQ(received_alarm.level, AlarmLevel::WARNING);
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 测试告警数据导出功能
 */
TEST_F(AlarmSystemTest, AlarmDataExport) {
    EXPECT_TRUE(alarm_system_->Start());
    
    // 触发一些告警
    AlarmEvent alarm1(AlarmType::ACCURACY_DEGRADED, AlarmLevel::ERROR, "精度告警");
    AlarmEvent alarm2(AlarmType::GNSS_SATELLITES_LOW, AlarmLevel::WARNING, "卫星告警");
    
    alarm_system_->TriggerAlarm(alarm1);
    uint64_t id2 = alarm_system_->TriggerAlarm(alarm2);
    alarm_system_->ResolveAlarm(id2, "问题已解决");
    
    // 测试CSV导出
    std::string csv_filename = "/tmp/test_alarms.csv";
    EXPECT_TRUE(alarm_system_->ExportAlarmData(csv_filename, "csv"));
    
    // 测试JSON导出
    std::string json_filename = "/tmp/test_alarms.json";
    EXPECT_TRUE(alarm_system_->ExportAlarmData(json_filename, "json"));
    
    // 测试不支持的格式
    EXPECT_FALSE(alarm_system_->ExportAlarmData("/tmp/test.xml", "xml"));
    
    EXPECT_TRUE(alarm_system_->Stop());
}

/**
 * @brief 主函数
 */
int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

} // namespace test
} // namespace core
} // namespace timing_server