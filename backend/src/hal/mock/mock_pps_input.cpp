#include "hal/interfaces.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <random>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <cstring>

namespace timing_server {
namespace hal {

/**
 * @brief Mock PPS输入实现类
 * 
 * 这个类模拟真实的1PPS（每秒脉冲）信号输入，主要用于macOS开发环境和测试场景。
 * 它生成精确的每秒脉冲信号，并提供可配置的信号质量和抖动模拟。
 * 
 * 主要功能：
 * 1. 生成精确的1Hz脉冲信号，与系统时钟同步
 * 2. 模拟信号抖动和质量变化
 * 3. 支持信号丢失和恢复的模拟
 * 4. 提供高精度时间戳（纳秒级）
 * 5. 支持可配置的信号参数
 * 
 * 工作原理：
 * - 使用独立线程生成周期性的PPS信号
 * - 基于系统高精度时钟计算精确的秒边界
 * - 添加可配置的抖动来模拟真实硬件特性
 * - 支持信号质量评估和异常检测
 */
class MockPpsInput : public I_PpsInput {
public:
    /**
     * @brief 构造函数
     * 初始化Mock PPS输入的默认参数
     */
    MockPpsInput()
        : initialized_(false)
        , signal_present_(true)
        , last_pps_timestamp_(0)
        , pps_thread_running_(false)
        , jitter_ns_(100)  // 默认100ns抖动
        , signal_quality_(95)  // 默认95%信号质量
        , random_engine_(std::chrono::steady_clock::now().time_since_epoch().count()) {
        
        std::cout << "Mock PPS输入已创建" << std::endl;
    }
    
    /**
     * @brief 析构函数
     * 停止PPS线程并清理资源
     */
    ~MockPpsInput() {
        Close();
    }
    
    /**
     * @brief 初始化PPS输入
     * 
     * 初始化过程包括：
     * 1. 启动PPS信号生成线程
     * 2. 初始化随机数生成器用于抖动模拟
     * 3. 设置信号质量参数
     * 4. 同步到下一个秒边界
     * 
     * @return 初始化是否成功
     */
    bool Initialize() override {
        std::cout << "正在初始化Mock PPS输入..." << std::endl;
        
        if (initialized_) {
            std::cout << "Mock PPS输入已经初始化" << std::endl;
            return true;
        }
        
        // 初始化随机数分布
        jitter_distribution_ = std::normal_distribution<double>(0.0, jitter_ns_);
        quality_distribution_ = std::uniform_real_distribution<double>(0.0, 1.0);
        
        // 启动PPS信号生成线程
        pps_thread_running_ = true;
        pps_thread_ = std::thread(&MockPpsInput::PpsGeneratorThread, this);
        
        // 等待第一个PPS信号
        std::unique_lock<std::mutex> lock(pps_mutex_);
        if (pps_condition_.wait_for(lock, std::chrono::seconds(2), [this] { return last_pps_timestamp_ > 0; })) {
            initialized_ = true;
            std::cout << "Mock PPS输入初始化完成" << std::endl;
            std::cout << "  抖动范围: ±" << jitter_ns_ << " ns" << std::endl;
            std::cout << "  信号质量: " << signal_quality_ << "%" << std::endl;
            return true;
        } else {
            std::cerr << "Mock PPS输入初始化超时" << std::endl;
            pps_thread_running_ = false;
            if (pps_thread_.joinable()) {
                pps_thread_.join();
            }
            return false;
        }
    }
    
    /**
     * @brief 等待PPS信号边沿
     * 
     * 阻塞等待下一个PPS信号的到来。这个函数模拟真实硬件的PPS等待行为：
     * 1. 如果在超时时间内检测到PPS边沿，返回true
     * 2. 如果超时或信号丢失，返回false
     * 3. 支持可配置的超时时间
     * 
     * @param timeout_ms 超时时间（毫秒）
     * @return 是否检测到PPS边沿
     */
    bool WaitForPpsEdge(int timeout_ms) override {
        if (!initialized_) {
            std::cerr << "PPS输入未初始化" << std::endl;
            return false;
        }
        
        if (!signal_present_) {
            std::cout << "PPS信号不存在，等待超时" << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(timeout_ms));
            return false;
        }
        
        std::unique_lock<std::mutex> lock(pps_mutex_);
        uint64_t current_timestamp = last_pps_timestamp_;
        
        // 等待新的PPS信号
        bool result = pps_condition_.wait_for(lock, std::chrono::milliseconds(timeout_ms), 
            [this, current_timestamp] { 
                return last_pps_timestamp_ > current_timestamp; 
            });
        
        if (result) {
            std::cout << "检测到PPS边沿，时间戳: " << last_pps_timestamp_ << " ns" << std::endl;
        } else {
            std::cout << "PPS边沿等待超时 (" << timeout_ms << " ms)" << std::endl;
        }
        
        return result;
    }
    
    /**
     * @brief 获取最后一次PPS时间戳
     * 
     * 返回最近一次PPS信号的高精度时间戳。时间戳基于系统单调时钟，
     * 提供纳秒级精度，并包含模拟的抖动。
     * 
     * @return 纳秒级时间戳
     */
    uint64_t GetLastPpsTimestamp() override {
        std::lock_guard<std::mutex> lock(pps_mutex_);
        return last_pps_timestamp_;
    }
    
    /**
     * @brief 关闭PPS输入
     * 
     * 清理所有资源：
     * 1. 停止PPS生成线程
     * 2. 重置状态标志
     * 3. 清理同步对象
     */
    void Close() override {
        if (initialized_) {
            std::cout << "正在关闭Mock PPS输入..." << std::endl;
            
            // 停止PPS线程
            pps_thread_running_ = false;
            pps_condition_.notify_all();
            
            if (pps_thread_.joinable()) {
                pps_thread_.join();
            }
            
            initialized_ = false;
            std::cout << "Mock PPS输入已关闭" << std::endl;
        }
    }
    
    /**
     * @brief 设置信号质量参数
     * 用于测试不同的信号条件
     * @param jitter_ns 抖动范围（纳秒）
     * @param quality 信号质量（0-100）
     * @param present 信号是否存在
     */
    void SetSignalParameters(double jitter_ns, int quality, bool present) {
        jitter_ns_ = jitter_ns;
        signal_quality_ = quality;
        signal_present_ = present;
        
        // 更新抖动分布
        jitter_distribution_ = std::normal_distribution<double>(0.0, jitter_ns);
        
        std::cout << "PPS信号参数已更新:" << std::endl;
        std::cout << "  抖动: ±" << jitter_ns << " ns" << std::endl;
        std::cout << "  质量: " << quality << "%" << std::endl;
        std::cout << "  存在: " << (present ? "是" : "否") << std::endl;
    }
    
    /**
     * @brief 获取信号质量统计
     * @return 信号质量百分比
     */
    int GetSignalQuality() const {
        return signal_quality_;
    }
    
    /**
     * @brief 获取当前抖动设置
     * @return 抖动范围（纳秒）
     */
    double GetJitterRange() const {
        return jitter_ns_;
    }

private:
    /**
     * @brief PPS信号生成线程
     * 
     * 这个线程负责生成周期性的PPS信号：
     * 1. 计算精确的秒边界时间
     * 2. 添加配置的抖动
     * 3. 模拟信号质量变化
     * 4. 通知等待的线程
     * 
     * 线程以高优先级运行以确保时间精度。
     */
    void PpsGeneratorThread() {
        std::cout << "PPS生成线程已启动" << std::endl;
        
        // 同步到下一个秒边界
        auto now = std::chrono::steady_clock::now();
        auto next_second = std::chrono::time_point_cast<std::chrono::seconds>(now) + std::chrono::seconds(1);
        std::this_thread::sleep_until(next_second);
        
        while (pps_thread_running_) {
            if (signal_present_) {
                // 生成PPS信号
                GeneratePpsSignal();
                
                // 模拟信号质量变化
                SimulateSignalQuality();
            }
            
            // 等待到下一秒
            next_second += std::chrono::seconds(1);
            std::this_thread::sleep_until(next_second);
        }
        
        std::cout << "PPS生成线程已停止" << std::endl;
    }
    
    /**
     * @brief 生成PPS信号
     * 
     * 生成一个PPS脉冲信号：
     * 1. 获取当前高精度时间戳
     * 2. 添加随机抖动模拟真实硬件
     * 3. 更新最后PPS时间戳
     * 4. 通知等待的线程
     */
    void GeneratePpsSignal() {
        // 获取当前时间戳（纳秒）
        auto now = std::chrono::steady_clock::now();
        auto timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
        
        // 添加抖动
        double jitter = jitter_distribution_(random_engine_);
        timestamp_ns += static_cast<uint64_t>(jitter);
        
        // 更新时间戳并通知等待者
        {
            std::lock_guard<std::mutex> lock(pps_mutex_);
            last_pps_timestamp_ = timestamp_ns;
        }
        
        pps_condition_.notify_all();
        
        // 输出调试信息（每10秒输出一次）
        static int counter = 0;
        if (++counter % 10 == 0) {
            std::cout << "PPS信号生成 #" << counter << ", 抖动: " << jitter << " ns" << std::endl;
        }
    }
    
    /**
     * @brief 模拟信号质量变化
     * 
     * 定期改变信号质量参数：
     * 1. 随机调整抖动范围
     * 2. 模拟信号丢失和恢复
     * 3. 改变信号质量评分
     */
    void SimulateSignalQuality() {
        static auto last_quality_change = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        
        // 每30秒评估一次信号质量变化
        if (std::chrono::duration_cast<std::chrono::seconds>(now - last_quality_change).count() >= 30) {
            double random_value = quality_distribution_(random_engine_);
            
            // 5%概率模拟信号丢失
            if (random_value < 0.05 && signal_present_) {
                signal_present_ = false;
                std::cout << "模拟PPS信号丢失" << std::endl;
            }
            // 如果信号丢失，80%概率恢复
            else if (!signal_present_ && random_value < 0.8) {
                signal_present_ = true;
                std::cout << "模拟PPS信号恢复" << std::endl;
            }
            // 20%概率调整抖动
            else if (random_value < 0.2) {
                double old_jitter = jitter_ns_;
                jitter_ns_ = 50 + (random_value * 200);  // 50-250ns范围
                jitter_distribution_ = std::normal_distribution<double>(0.0, jitter_ns_);
                std::cout << "PPS抖动调整: " << old_jitter << " -> " << jitter_ns_ << " ns" << std::endl;
            }
            
            last_quality_change = now;
        }
    }
    
    /**
     * @brief 计算信号质量评分
     * 基于抖动、稳定性等因素计算信号质量
     * @return 质量评分（0-100）
     */
    int CalculateSignalQuality() const {
        if (!signal_present_) {
            return 0;
        }
        
        // 基于抖动计算质量评分
        int quality = 100;
        if (jitter_ns_ > 100) {
            quality -= static_cast<int>((jitter_ns_ - 100) / 10);
        }
        
        return std::max(0, std::min(100, quality));
    }

private:
    bool initialized_;                          // 初始化状态
    std::atomic<bool> signal_present_;          // 信号存在标志
    std::atomic<uint64_t> last_pps_timestamp_;  // 最后PPS时间戳
    
    // PPS生成线程
    std::thread pps_thread_;
    std::atomic<bool> pps_thread_running_;
    std::mutex pps_mutex_;
    std::condition_variable pps_condition_;
    
    // 信号质量参数
    double jitter_ns_;                          // 抖动范围（纳秒）
    int signal_quality_;                        // 信号质量（0-100）
    
    // 随机数生成器
    std::mt19937 random_engine_;
    std::normal_distribution<double> jitter_distribution_;
    std::uniform_real_distribution<double> quality_distribution_;
};

} // namespace hal
} // namespace timing_server