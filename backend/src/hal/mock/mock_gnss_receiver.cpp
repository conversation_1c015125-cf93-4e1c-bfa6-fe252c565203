#include "hal/interfaces.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <thread>
#include <chrono>
#include <random>
#include <ctime>
#include <iomanip>
#include <vector>
#include <string>
#include <cstring>

namespace timing_server {
namespace hal {

/**
 * @brief Mock GNSS接收机实现类
 * 
 * 这个类模拟真实的GNSS接收机行为，主要用于macOS开发环境和测试场景。
 * 它可以从预设的NMEA数据文件中读取模拟数据，或者动态生成NMEA语句。
 * 
 * 主要功能：
 * 1. 从文件读取预录制的NMEA数据，支持循环播放
 * 2. 动态生成NMEA语句，模拟不同的卫星状态
 * 3. 模拟信号质量变化，包括卫星数量和信号强度
 * 4. 支持信号丢失和恢复的模拟
 * 5. 提供可配置的模拟参数
 */
class MockGnssReceiver : public I_GnssReceiver {
public:
    /**
     * @brief 构造函数
     * 初始化Mock GNSS接收机的默认参数
     */
    MockGnssReceiver() 
        : initialized_(false)
        , signal_valid_(true)
        , current_satellite_count_(12)
        , signal_strength_db_(-142.0)
        , fix_type_("3D")
        , data_file_path_("mock_data/nmea_sample.txt")
        , use_file_data_(false)
        , file_position_(0)
        , random_engine_(std::chrono::steady_clock::now().time_since_epoch().count()) {
        
        std::cout << "Mock GNSS接收机已创建" << std::endl;
    }
    
    /**
     * @brief 析构函数
     * 清理资源并关闭文件
     */
    ~MockGnssReceiver() {
        Close();
    }
    
    /**
     * @brief 初始化GNSS接收机
     * 
     * 初始化过程包括：
     * 1. 检查是否存在NMEA数据文件
     * 2. 如果文件存在，加载文件数据
     * 3. 如果文件不存在，使用动态生成模式
     * 4. 初始化随机数生成器用于模拟信号变化
     * 
     * @return 初始化是否成功
     */
    bool Initialize() override {
        std::cout << "正在初始化Mock GNSS接收机..." << std::endl;
        
        // 尝试加载NMEA数据文件
        if (LoadNmeaDataFile()) {
            std::cout << "已加载NMEA数据文件: " << data_file_path_ << std::endl;
            std::cout << "文件包含 " << nmea_lines_.size() << " 行NMEA数据" << std::endl;
            use_file_data_ = true;
        } else {
            std::cout << "未找到NMEA数据文件，使用动态生成模式" << std::endl;
            use_file_data_ = false;
        }
        
        // 初始化模拟参数
        InitializeSimulationParameters();
        
        initialized_ = true;
        std::cout << "Mock GNSS接收机初始化完成" << std::endl;
        return true;
    }
    
    /**
     * @brief 读取NMEA语句
     * 
     * 根据当前模式返回NMEA数据：
     * 1. 文件模式：从预加载的文件数据中循环读取
     * 2. 动态模式：实时生成NMEA语句
     * 
     * 支持的NMEA语句类型：
     * - $GPRMC: 推荐最小定位信息
     * - $GPGGA: 全球定位系统定位数据
     * - $GPGSV: 可见卫星信息
     * 
     * @return NMEA语句字符串，如果没有数据则返回空字符串
     */
    std::string ReadNmeaSentence() override {
        if (!initialized_) {
            return "";
        }
        
        // 模拟信号质量变化
        SimulateSignalVariation();
        
        if (use_file_data_ && !nmea_lines_.empty()) {
            // 从文件数据中读取
            std::string sentence = nmea_lines_[file_position_];
            file_position_ = (file_position_ + 1) % nmea_lines_.size();
            
            // 添加时间延迟模拟真实接收机的数据率
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            return sentence;
        } else {
            // 动态生成NMEA语句
            return GenerateNmeaSentence();
        }
    }
    
    /**
     * @brief 检查GNSS信号是否有效
     * 
     * 信号有效性基于以下条件：
     * 1. 卫星数量 >= 4（最少定位要求）
     * 2. 信号强度 > -150dB（可用信号阈值）
     * 3. 模拟的信号状态
     * 
     * @return 信号有效性
     */
    bool IsSignalValid() override {
        return signal_valid_ && current_satellite_count_ >= 4 && signal_strength_db_ > -150.0;
    }
    
    /**
     * @brief 获取卫星信息
     * 
     * 返回当前模拟的卫星状态信息，包括：
     * 1. 可见卫星数量（4-16颗）
     * 2. 平均信号强度（-140dB到-160dB）
     * 3. 定位锁定状态
     * 4. 定位类型（2D/3D）
     * 
     * @return 卫星信息结构
     */
    SatelliteInfo GetSatelliteInfo() override {
        SatelliteInfo info;
        info.satellite_count = current_satellite_count_;
        info.signal_strength_db = signal_strength_db_;
        info.is_locked = IsSignalValid();
        info.fix_type = fix_type_;
        
        return info;
    }
    
    /**
     * @brief 关闭GNSS接收机
     * 
     * 清理所有资源：
     * 1. 清空NMEA数据缓存
     * 2. 重置状态标志
     * 3. 输出关闭信息
     */
    void Close() override {
        if (initialized_) {
            std::cout << "正在关闭Mock GNSS接收机..." << std::endl;
            nmea_lines_.clear();
            initialized_ = false;
            std::cout << "Mock GNSS接收机已关闭" << std::endl;
        }
    }
    
    /**
     * @brief 设置NMEA数据文件路径
     * 允许外部指定不同的NMEA数据文件用于测试
     * @param file_path 文件路径
     */
    void SetDataFilePath(const std::string& file_path) {
        data_file_path_ = file_path;
    }
    
    /**
     * @brief 设置信号模拟参数
     * 用于测试不同的信号条件
     * @param satellite_count 卫星数量
     * @param signal_strength 信号强度(dB)
     * @param is_valid 信号有效性
     */
    void SetSimulationParameters(uint32_t satellite_count, double signal_strength, bool is_valid) {
        current_satellite_count_ = satellite_count;
        signal_strength_db_ = signal_strength;
        signal_valid_ = is_valid;
    }

private:
    /**
     * @brief 加载NMEA数据文件
     * 
     * 尝试从指定路径加载NMEA数据文件，支持以下格式：
     * 1. 每行一个完整的NMEA语句
     * 2. 自动过滤空行和注释行
     * 3. 验证NMEA语句格式的基本正确性
     * 
     * @return 加载是否成功
     */
    bool LoadNmeaDataFile() {
        std::ifstream file(data_file_path_);
        if (!file.is_open()) {
            return false;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            // 跳过空行和注释行
            if (line.empty() || line[0] == '#') {
                continue;
            }
            
            // 基本的NMEA格式验证
            if (line.length() > 6 && line[0] == '$' && line.find(',') != std::string::npos) {
                nmea_lines_.push_back(line);
            }
        }
        
        file.close();
        return !nmea_lines_.empty();
    }
    
    /**
     * @brief 初始化模拟参数
     * 设置默认的模拟参数，包括卫星数量、信号强度等
     */
    void InitializeSimulationParameters() {
        // 设置随机数分布
        satellite_distribution_ = std::uniform_int_distribution<int>(8, 16);
        signal_strength_distribution_ = std::uniform_real_distribution<double>(-140.0, -160.0);
        variation_distribution_ = std::uniform_real_distribution<double>(0.0, 1.0);
        
        std::cout << "模拟参数已初始化:" << std::endl;
        std::cout << "  卫星数量: " << current_satellite_count_ << std::endl;
        std::cout << "  信号强度: " << signal_strength_db_ << " dB" << std::endl;
        std::cout << "  定位类型: " << fix_type_ << std::endl;
    }
    
    /**
     * @brief 模拟信号质量变化
     * 
     * 定期随机改变信号参数以模拟真实环境：
     * 1. 卫星数量在8-16之间变化
     * 2. 信号强度在-140dB到-160dB之间波动
     * 3. 偶尔模拟信号丢失情况
     */
    void SimulateSignalVariation() {
        static auto last_variation = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        
        // 每5秒更新一次信号参数
        if (std::chrono::duration_cast<std::chrono::seconds>(now - last_variation).count() >= 5) {
            // 随机改变卫星数量
            if (variation_distribution_(random_engine_) < 0.3) {  // 30%概率改变
                current_satellite_count_ = satellite_distribution_(random_engine_);
            }
            
            // 随机改变信号强度
            if (variation_distribution_(random_engine_) < 0.5) {  // 50%概率改变
                signal_strength_db_ = signal_strength_distribution_(random_engine_);
            }
            
            // 偶尔模拟信号丢失
            if (variation_distribution_(random_engine_) < 0.05) {  // 5%概率信号丢失
                signal_valid_ = false;
                std::cout << "模拟GNSS信号丢失" << std::endl;
            } else if (!signal_valid_ && variation_distribution_(random_engine_) < 0.8) {  // 80%概率恢复
                signal_valid_ = true;
                std::cout << "模拟GNSS信号恢复" << std::endl;
            }
            
            last_variation = now;
        }
    }
    
    /**
     * @brief 生成NMEA语句
     * 
     * 动态生成标准的NMEA语句，包括：
     * 1. $GPRMC - 推荐最小定位信息
     * 2. $GPGGA - 全球定位系统定位数据
     * 3. $GPGSV - 可见卫星信息
     * 
     * @return 生成的NMEA语句
     */
    std::string GenerateNmeaSentence() {
        static int sentence_counter = 0;
        sentence_counter++;
        
        // 循环生成不同类型的NMEA语句
        switch (sentence_counter % 3) {
            case 0:
                return GenerateGPRMC();
            case 1:
                return GenerateGPGGA();
            case 2:
                return GenerateGPGSV();
            default:
                return GenerateGPRMC();
        }
    }
    
    /**
     * @brief 生成GPRMC语句
     * $GPRMC,时间,状态,纬度,纬度半球,经度,经度半球,速度,航向,日期,磁偏角,磁偏角方向*校验和
     */
    std::string GenerateGPRMC() {
        auto now = std::time(nullptr);
        auto* tm = std::gmtime(&now);
        
        std::ostringstream oss;
        oss << "$GPRMC,";
        oss << std::setfill('0') << std::setw(2) << tm->tm_hour;
        oss << std::setfill('0') << std::setw(2) << tm->tm_min;
        oss << std::setfill('0') << std::setw(2) << tm->tm_sec << ".00,";
        oss << (signal_valid_ ? "A" : "V") << ",";  // A=有效, V=无效
        oss << "3958.3953,N,11629.4039,W,0.0,0.0,";
        oss << std::setfill('0') << std::setw(2) << tm->tm_mday;
        oss << std::setfill('0') << std::setw(2) << (tm->tm_mon + 1);
        oss << std::setfill('0') << std::setw(2) << (tm->tm_year % 100);
        oss << ",0.0,E*";
        
        // 简化的校验和计算（实际应该是XOR校验）
        oss << "6A";
        
        return oss.str();
    }
    
    /**
     * @brief 生成GPGGA语句
     * $GPGGA,时间,纬度,纬度半球,经度,经度半球,定位质量,卫星数,HDOP,海拔,海拔单位,大地水准面高度,单位,差分时间,差分站ID*校验和
     */
    std::string GenerateGPGGA() {
        auto now = std::time(nullptr);
        auto* tm = std::gmtime(&now);
        
        std::ostringstream oss;
        oss << "$GPGGA,";
        oss << std::setfill('0') << std::setw(2) << tm->tm_hour;
        oss << std::setfill('0') << std::setw(2) << tm->tm_min;
        oss << std::setfill('0') << std::setw(2) << tm->tm_sec << ".00,";
        oss << "3958.3953,N,11629.4039,W,";
        oss << (signal_valid_ ? "1" : "0") << ",";  // 定位质量：1=GPS定位，0=无定位
        oss << std::setfill('0') << std::setw(2) << current_satellite_count_ << ",";
        oss << "1.2,100.0,M,0.0,M,,*";
        
        // 简化的校验和
        oss << "5C";
        
        return oss.str();
    }
    
    /**
     * @brief 生成GPGSV语句
     * $GPGSV,总句数,当前句数,可见卫星总数,卫星1PRN,卫星1仰角,卫星1方位角,卫星1信噪比,...*校验和
     */
    std::string GenerateGPGSV() {
        std::ostringstream oss;
        oss << "$GPGSV,3,1," << std::setfill('0') << std::setw(2) << current_satellite_count_;
        
        // 添加4颗卫星的信息（第一句）
        for (uint32_t i = 1; i <= 4 && i <= current_satellite_count_; i++) {
            oss << "," << std::setfill('0') << std::setw(2) << i;      // PRN
            oss << "," << (30 + i * 10);                               // 仰角
            oss << "," << (i * 90);                                    // 方位角
            oss << "," << std::setfill('0') << std::setw(2) << (40 + i); // 信噪比
        }
        
        oss << "*4F";  // 简化的校验和
        
        return oss.str();
    }
    
    /**
     * @brief 计算NMEA校验和
     * NMEA校验和是从$后到*前所有字符的XOR结果
     * @param sentence NMEA语句（不包含校验和）
     * @return 十六进制校验和字符串
     */
    std::string CalculateChecksum(const std::string& sentence) {
        uint8_t checksum = 0;
        bool start_calc = false;
        
        for (char c : sentence) {
            if (c == '$') {
                start_calc = true;
                continue;
            }
            if (c == '*') {
                break;
            }
            if (start_calc) {
                checksum ^= static_cast<uint8_t>(c);
            }
        }
        
        std::ostringstream oss;
        oss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << static_cast<int>(checksum);
        return oss.str();
    }

private:
    bool initialized_;                          // 初始化状态
    bool signal_valid_;                         // 信号有效性
    uint32_t current_satellite_count_;          // 当前卫星数量
    double signal_strength_db_;                 // 信号强度(dB)
    std::string fix_type_;                      // 定位类型
    
    std::string data_file_path_;                // NMEA数据文件路径
    bool use_file_data_;                        // 是否使用文件数据
    std::vector<std::string> nmea_lines_;       // 预加载的NMEA数据
    size_t file_position_;                      // 文件读取位置
    
    // 随机数生成器和分布
    std::mt19937 random_engine_;
    std::uniform_int_distribution<int> satellite_distribution_;
    std::uniform_real_distribution<double> signal_strength_distribution_;
    std::uniform_real_distribution<double> variation_distribution_;
};

} // namespace hal
} // namespace timing_server