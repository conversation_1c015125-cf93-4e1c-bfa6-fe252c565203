#include "hal/interfaces.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <random>
#include <cmath>
#include <sstream>
#include <iomanip>
#include <cstring>

namespace timing_server {
namespace hal {

/**
 * @brief Mock 原子钟实现类
 * 
 * 这个类模拟铷原子钟的行为，主要用于macOS开发环境和测试场景。
 * 它模拟真实铷原子钟的各种特性，包括温度敏感性、频率漂移、老化效应等。
 * 
 * 主要功能：
 * 1. 模拟铷原子钟的温度特性和控制
 * 2. 模拟频率漂移和老化效应
 * 3. 支持频率校正和学习算法
 * 4. 模拟各种工作状态和故障模式
 * 5. 提供详细的健康状态监控
 * 
 * 铷原子钟特性模拟：
 * - 工作温度：通常在65-75°C
 * - 频率稳定度：1×10⁻¹¹ 到 1×10⁻¹²
 * - 温度系数：约1×10⁻¹⁰/°C
 * - 老化率：<5×10⁻¹¹/月
 * - 预热时间：约15分钟达到稳定
 */
class MockAtomicClock : public I_AtomicClock {
public:
    /**
     * @brief 构造函数
     * 初始化Mock原子钟的默认参数
     */
    MockAtomicClock()
        : initialized_(false)
        , is_healthy_(true)
        , current_temperature_(25.0)  // 室温开始
        , target_temperature_(70.0)   // 目标工作温度
        , frequency_offset_(0.0)
        , frequency_correction_(0.0)
        , aging_rate_(1e-12)          // 默认老化率
        , temperature_coefficient_(1e-10)  // 温度系数
        , startup_time_(std::chrono::steady_clock::now())
        , random_engine_(std::chrono::steady_clock::now().time_since_epoch().count()) {
        
        std::cout << "Mock 原子钟已创建" << std::endl;
    }
    
    /**
     * @brief 析构函数
     * 清理资源
     */
    ~MockAtomicClock() {
        Close();
    }
    
    /**
     * @brief 初始化原子钟
     * 
     * 初始化过程包括：
     * 1. 启动温度控制模拟
     * 2. 初始化频率参数
     * 3. 开始预热过程
     * 4. 设置健康监控
     * 
     * @return 初始化是否成功
     */
    bool Initialize() override {
        std::cout << "正在初始化Mock 原子钟..." << std::endl;
        
        if (initialized_) {
            std::cout << "Mock 原子钟已经初始化" << std::endl;
            return true;
        }
        
        // 初始化随机数分布
        temperature_noise_dist_ = std::normal_distribution<double>(0.0, 0.1);  // ±0.1°C噪声
        frequency_noise_dist_ = std::normal_distribution<double>(0.0, 1e-13);  // 频率噪声
        
        // 记录启动时间
        startup_time_ = std::chrono::steady_clock::now();
        
        // 初始化状态
        current_temperature_ = 25.0;  // 从室温开始
        frequency_offset_ = 1e-9;     // 初始频率偏移较大
        is_healthy_ = true;
        
        initialized_ = true;
        
        std::cout << "Mock 原子钟初始化完成" << std::endl;
        std::cout << "  目标温度: " << target_temperature_ << "°C" << std::endl;
        std::cout << "  温度系数: " << temperature_coefficient_ << "/°C" << std::endl;
        std::cout << "  老化率: " << aging_rate_ << "/秒" << std::endl;
        std::cout << "  开始预热过程..." << std::endl;
        
        return true;
    }
    
    /**
     * @brief 获取原子钟状态
     * 
     * 返回当前的时钟健康状态，包括：
     * 1. 当前温度和温度稳定性
     * 2. 频率偏移和稳定度
     * 3. 预热状态和工作模式
     * 4. 健康状况评估
     * 
     * @return 时钟健康状态
     */
    ClockHealth GetStatus() override {
        if (!initialized_) {
            ClockHealth health;
            health.temperature = 0.0;
            health.frequency_offset = 0.0;
            health.is_healthy = false;
            health.status_message = "原子钟未初始化";
            return health;
        }
        
        // 更新模拟状态
        UpdateSimulation();
        
        ClockHealth health;
        health.temperature = current_temperature_;
        health.frequency_offset = frequency_offset_;
        health.is_healthy = is_healthy_;
        health.status_message = GenerateStatusMessage();
        
        return health;
    }
    
    /**
     * @brief 设置频率校正
     * 
     * 应用频率校正值来补偿原子钟的漂移：
     * 1. 验证校正值的合理范围
     * 2. 应用校正并更新内部状态
     * 3. 模拟校正的响应时间
     * 
     * @param ppm 频率校正值（ppm）
     * @return 设置是否成功
     */
    bool SetFrequencyCorrection(double ppm) override {
        if (!initialized_) {
            std::cerr << "原子钟未初始化，无法设置频率校正" << std::endl;
            return false;
        }
        
        // 验证校正值范围（铷钟通常支持±1ppm的校正范围）
        if (std::abs(ppm) > 1.0) {
            std::cerr << "频率校正值超出范围: " << ppm << " ppm (最大±1.0 ppm)" << std::endl;
            return false;
        }
        
        frequency_correction_ = ppm;
        
        std::cout << "频率校正已设置: " << ppm << " ppm" << std::endl;
        
        // 模拟校正的响应时间（铷钟通常需要几秒到几分钟）
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        return true;
    }
    
    /**
     * @brief 获取频率偏移
     * 
     * 返回当前的频率偏移，包括：
     * 1. 基础漂移
     * 2. 温度影响
     * 3. 老化效应
     * 4. 已应用的校正
     * 
     * @return 当前频率偏移（ppm）
     */
    double GetFrequencyOffset() override {
        if (!initialized_) {
            return 0.0;
        }
        
        UpdateSimulation();
        return frequency_offset_;
    }
    
    /**
     * @brief 获取时钟健康状况
     * 
     * 返回详细的健康状况信息，与GetStatus()相同
     * 
     * @return 健康状况结构
     */
    ClockHealth GetHealth() override {
        return GetStatus();
    }
    
    /**
     * @brief 关闭原子钟
     * 
     * 清理所有资源：
     * 1. 停止温度控制
     * 2. 重置状态标志
     * 3. 清理模拟参数
     */
    void Close() override {
        if (initialized_) {
            std::cout << "正在关闭Mock 原子钟..." << std::endl;
            initialized_ = false;
            is_healthy_ = false;
            std::cout << "Mock 原子钟已关闭" << std::endl;
        }
    }
    
    /**
     * @brief 设置模拟参数
     * 用于测试不同的原子钟特性
     * @param target_temp 目标温度
     * @param temp_coeff 温度系数
     * @param aging_rate 老化率
     */
    void SetSimulationParameters(double target_temp, double temp_coeff, double aging_rate) {
        target_temperature_ = target_temp;
        temperature_coefficient_ = temp_coeff;
        aging_rate_ = aging_rate;
        
        std::cout << "原子钟模拟参数已更新:" << std::endl;
        std::cout << "  目标温度: " << target_temp << "°C" << std::endl;
        std::cout << "  温度系数: " << temp_coeff << "/°C" << std::endl;
        std::cout << "  老化率: " << aging_rate << "/秒" << std::endl;
    }
    
    /**
     * @brief 模拟故障
     * 用于测试故障处理逻辑
     * @param fault_type 故障类型
     */
    void SimulateFault(const std::string& fault_type) {
        if (fault_type == "overheat") {
            current_temperature_ = 85.0;  // 过热
            is_healthy_ = false;
            std::cout << "模拟原子钟过热故障" << std::endl;
        } else if (fault_type == "frequency_drift") {
            frequency_offset_ = 1e-6;  // 大幅频率漂移
            is_healthy_ = false;
            std::cout << "模拟原子钟频率漂移故障" << std::endl;
        } else if (fault_type == "temperature_control") {
            target_temperature_ = current_temperature_;  // 温控失效
            is_healthy_ = false;
            std::cout << "模拟原子钟温控故障" << std::endl;
        }
    }
    
    /**
     * @brief 获取预热进度
     * @return 预热进度百分比（0-100）
     */
    int GetWarmupProgress() const {
        if (!initialized_) {
            return 0;
        }
        
        auto elapsed = std::chrono::steady_clock::now() - startup_time_;
        auto elapsed_minutes = std::chrono::duration_cast<std::chrono::minutes>(elapsed).count();
        
        // 假设15分钟完全预热
        int progress = std::min(100, static_cast<int>((elapsed_minutes * 100) / 15));
        return progress;
    }

private:
    /**
     * @brief 更新模拟状态
     * 
     * 定期更新原子钟的各种状态：
     * 1. 温度控制模拟
     * 2. 频率漂移计算
     * 3. 老化效应模拟
     * 4. 健康状态评估
     */
    void UpdateSimulation() {
        auto now = std::chrono::steady_clock::now();
        static auto last_update = now;
        
        auto dt = std::chrono::duration_cast<std::chrono::seconds>(now - last_update).count();
        if (dt < 1) {
            return;  // 每秒更新一次
        }
        last_update = now;
        
        // 更新温度（模拟温控系统）
        UpdateTemperature(dt);
        
        // 更新频率偏移
        UpdateFrequencyOffset(dt);
        
        // 更新健康状态
        UpdateHealthStatus();
    }
    
    /**
     * @brief 更新温度模拟
     * 
     * 模拟原子钟的温度控制系统：
     * 1. 预热阶段：从室温逐渐升温到工作温度
     * 2. 稳定阶段：维持目标温度±0.1°C
     * 3. 添加温度噪声模拟真实环境
     * 
     * @param dt 时间间隔（秒）
     */
    void UpdateTemperature(double dt) {
        double temperature_error = target_temperature_ - current_temperature_;
        
        // 简单的PID温控模拟
        double heating_rate = 0.1;  // 每秒升温0.1°C
        if (std::abs(temperature_error) > 0.1) {
            current_temperature_ += std::copysign(heating_rate * dt, temperature_error);
        }
        
        // 添加温度噪声
        current_temperature_ += temperature_noise_dist_(random_engine_) * dt;
        
        // 限制温度范围
        current_temperature_ = std::max(20.0, std::min(80.0, current_temperature_));
    }
    
    /**
     * @brief 更新频率偏移
     * 
     * 计算当前的频率偏移，包括：
     * 1. 温度影响：基于温度系数
     * 2. 老化效应：随时间的缓慢漂移
     * 3. 随机噪声：短期稳定度
     * 4. 频率校正：已应用的校正值
     * 
     * @param dt 时间间隔（秒）
     */
    void UpdateFrequencyOffset(double dt) {
        // 基础频率偏移（随预热时间减小）
        int warmup_progress = GetWarmupProgress();
        double base_offset = 1e-9 * (100 - warmup_progress) / 100.0;
        
        // 温度影响
        double temp_error = current_temperature_ - target_temperature_;
        double temp_offset = temperature_coefficient_ * temp_error;
        
        // 老化效应（累积）
        static double aging_accumulator = 0.0;
        aging_accumulator += aging_rate_ * dt;
        
        // 随机噪声
        double noise = frequency_noise_dist_(random_engine_);
        
        // 总频率偏移
        frequency_offset_ = base_offset + temp_offset + aging_accumulator + noise - frequency_correction_;
    }
    
    /**
     * @brief 更新健康状态
     * 
     * 评估原子钟的健康状况：
     * 1. 温度是否在正常范围
     * 2. 频率偏移是否合理
     * 3. 预热是否完成
     * 4. 是否有异常状态
     */
    void UpdateHealthStatus() {
        bool temp_ok = (current_temperature_ >= 65.0 && current_temperature_ <= 75.0);
        bool freq_ok = (std::abs(frequency_offset_) < 1e-8);
        bool warmup_ok = (GetWarmupProgress() > 80);
        
        is_healthy_ = temp_ok && freq_ok && warmup_ok;
        
        // 输出状态变化（每分钟一次）
        static auto last_status_output = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::minutes>(now - last_status_output).count() >= 1) {
            std::cout << "原子钟状态更新: 温度=" << std::fixed << std::setprecision(1) 
                      << current_temperature_ << "°C, 频率偏移=" << std::scientific 
                      << frequency_offset_ << ", 预热=" << GetWarmupProgress() << "%" << std::endl;
            last_status_output = now;
        }
    }
    
    /**
     * @brief 生成状态消息
     * 
     * 根据当前状态生成详细的中文状态描述
     * 
     * @return 状态消息字符串
     */
    std::string GenerateStatusMessage() {
        std::ostringstream oss;
        
        int warmup_progress = GetWarmupProgress();
        
        if (warmup_progress < 50) {
            oss << "预热中 (" << warmup_progress << "%)";
        } else if (warmup_progress < 90) {
            oss << "接近稳定 (" << warmup_progress << "%)";
        } else {
            oss << "工作稳定";
        }
        
        if (!is_healthy_) {
            oss << " - 异常状态";
            
            if (current_temperature_ > 75.0) {
                oss << " (过热)";
            } else if (current_temperature_ < 65.0) {
                oss << " (温度过低)";
            }
            
            if (std::abs(frequency_offset_) > 1e-8) {
                oss << " (频率漂移)";
            }
        }
        
        return oss.str();
    }

private:
    bool initialized_;                          // 初始化状态
    bool is_healthy_;                           // 健康状态
    
    // 温度相关
    double current_temperature_;                // 当前温度(°C)
    double target_temperature_;                 // 目标温度(°C)
    
    // 频率相关
    double frequency_offset_;                   // 当前频率偏移(ppm)
    double frequency_correction_;               // 应用的频率校正(ppm)
    double aging_rate_;                         // 老化率(ppm/秒)
    double temperature_coefficient_;            // 温度系数(ppm/°C)
    
    // 时间相关
    std::chrono::steady_clock::time_point startup_time_;  // 启动时间
    
    // 随机数生成器
    std::mt19937 random_engine_;
    std::normal_distribution<double> temperature_noise_dist_;
    std::normal_distribution<double> frequency_noise_dist_;
};

} // namespace hal
} // namespace timing_server