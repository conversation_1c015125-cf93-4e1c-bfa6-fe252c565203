#include "hal/interfaces.h"
#include <iostream>
#include <memory>

// 前向声明所有Mock实现类
namespace timing_server {
namespace hal {
    class MockGnssReceiver;
    class MockPpsInput;
    class MockAtomicClock;
    class MockFrequencyInput;
    class MockHighPrecisionRtc;
    class MockNetworkInterface;
}
}

// 包含所有Mock实现的声明（这里简化处理，实际项目中应该有独立的头文件）
#include "mock_gnss_receiver.cpp"
#include "mock_pps_input.cpp"
#include "mock_atomic_clock.cpp"
#include "mock_frequency_input.cpp"
#include "mock_rtc.cpp"
#include "mock_network_interface.cpp"

namespace timing_server {
namespace hal {

/**
 * @brief Mock HAL工厂实现类
 * 
 * 这个工厂类负责创建所有Mock硬件抽象层实现，主要用于macOS开发环境和测试场景。
 * 它提供完整的硬件模拟功能，支持开发和测试流程中的各种需求。
 * 
 * 主要功能：
 * 1. 创建所有类型的Mock硬件实现
 * 2. 提供统一的工厂接口
 * 3. 支持参数配置和定制
 * 4. 确保Mock实现的一致性
 * 
 * 支持的Mock设备：
 * - MockGnssReceiver: 模拟GNSS接收机
 * - MockPpsInput: 模拟1PPS信号输入
 * - MockAtomicClock: 模拟铷原子钟
 * - MockFrequencyInput: 模拟10MHz频率输入
 * - MockHighPrecisionRtc: 模拟高精度RTC
 * - MockNetworkInterface: 模拟网卡PHC
 */
class MockHalFactory : public I_HalFactory {
public:
    /**
     * @brief 构造函数声明
     */
    MockHalFactory();
    
    /**
     * @brief 析构函数声明
     */
    ~MockHalFactory() override;
    
    /**
     * @brief 创建GNSS接收机实例
     * 
     * 创建Mock GNSS接收机，支持：
     * 1. 从文件读取NMEA数据
     * 2. 动态生成NMEA语句
     * 3. 多种卫星状态模拟
     * 4. 信号质量变化模拟
     * 
     * @return GNSS接收机智能指针
     */
    std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() override {
        std::cout << "创建Mock GNSS接收机实例" << std::endl;
        
        try {
            auto gnss = std::make_unique<MockGnssReceiver>();
            
            // 可以在这里设置特定的模拟参数
            // gnss->SetDataFilePath("custom_nmea_data.txt");
            // gnss->SetSimulationParameters(12, -142.0, true);
            
            std::cout << "Mock GNSS接收机创建成功" << std::endl;
            return std::move(gnss);
            
        } catch (const std::exception& e) {
            std::cerr << "创建Mock GNSS接收机失败: " << e.what() << std::endl;
            return nullptr;
        }
    }
    
    /**
     * @brief 创建PPS输入实例
     * 
     * 创建Mock PPS输入，支持：
     * 1. 精确的1Hz脉冲信号生成
     * 2. 可配置的信号抖动
     * 3. 信号质量模拟
     * 4. 信号丢失和恢复模拟
     * 
     * @return PPS输入智能指针
     */
    std::unique_ptr<I_PpsInput> CreatePpsInput() override {
        std::cout << "创建Mock PPS输入实例" << std::endl;
        
        try {
            auto pps = std::make_unique<MockPpsInput>();
            
            // 可以在这里设置特定的模拟参数
            // pps->SetSignalParameters(100.0, 95, true);  // 100ns抖动，95%质量
            
            std::cout << "Mock PPS输入创建成功" << std::endl;
            return std::move(pps);
            
        } catch (const std::exception& e) {
            std::cerr << "创建Mock PPS输入失败: " << e.what() << std::endl;
            return nullptr;
        }
    }
    
    /**
     * @brief 创建原子钟实例
     * 
     * 创建Mock原子钟，支持：
     * 1. 铷原子钟特性模拟
     * 2. 温度控制和漂移模拟
     * 3. 频率校正和学习
     * 4. 预热过程模拟
     * 
     * @return 原子钟智能指针
     */
    std::unique_ptr<I_AtomicClock> CreateAtomicClock() override {
        std::cout << "创建Mock 原子钟实例" << std::endl;
        
        try {
            auto clock = std::make_unique<MockAtomicClock>();
            
            // 可以在这里设置特定的模拟参数
            // clock->SetSimulationParameters(70.0, 1e-10, 1e-12);  // 70°C, 温度系数, 老化率
            
            std::cout << "Mock 原子钟创建成功" << std::endl;
            return std::move(clock);
            
        } catch (const std::exception& e) {
            std::cerr << "创建Mock 原子钟失败: " << e.what() << std::endl;
            return nullptr;
        }
    }
    
    /**
     * @brief 创建频率输入实例
     * 
     * 创建Mock频率输入，支持：
     * 1. 10MHz频率基准模拟
     * 2. 频率精度和稳定度模拟
     * 3. 信号质量评估
     * 4. 频率测量统计
     * 
     * @return 频率输入智能指针
     */
    std::unique_ptr<I_FrequencyInput> CreateFrequencyInput() override {
        std::cout << "创建Mock 频率输入实例" << std::endl;
        
        try {
            auto freq = std::make_unique<MockFrequencyInput>();
            
            // 可以在这里设置特定的模拟参数
            // freq->SetSimulationParameters(1e-9, 1e-11, true);  // 1ppb精度, 1e-11稳定度
            
            std::cout << "Mock 频率输入创建成功" << std::endl;
            return std::move(freq);
            
        } catch (const std::exception& e) {
            std::cerr << "创建Mock 频率输入失败: " << e.what() << std::endl;
            return nullptr;
        }
    }
    
    /**
     * @brief 创建高精度RTC实例
     * 
     * 创建Mock高精度RTC，支持：
     * 1. 高精度时间保持
     * 2. 电池供电模拟
     * 3. 温度漂移模拟
     * 4. 时间校准功能
     * 
     * @return RTC智能指针
     */
    std::unique_ptr<I_HighPrecisionRtc> CreateRtc() override {
        std::cout << "创建Mock 高精度RTC实例" << std::endl;
        
        try {
            auto rtc = std::make_unique<MockHighPrecisionRtc>();
            
            // 可以在这里设置特定的模拟参数
            // rtc->SetSimulationParameters(20.0, 0.04, true);  // 20ppm精度, 温度系数, 有电池
            
            std::cout << "Mock 高精度RTC创建成功" << std::endl;
            return std::move(rtc);
            
        } catch (const std::exception& e) {
            std::cerr << "创建Mock 高精度RTC失败: " << e.what() << std::endl;
            return nullptr;
        }
    }
    
    /**
     * @brief 创建网络接口实例
     * 
     * 创建Mock网络接口，支持：
     * 1. PHC硬件时钟模拟
     * 2. PTP协议配置
     * 3. 硬件时间戳模拟
     * 4. 网络同步状态模拟
     * 
     * @return 网络接口智能指针
     */
    std::unique_ptr<I_NetworkInterface> CreateNetworkInterface() override {
        std::cout << "创建Mock 网络接口实例" << std::endl;
        
        try {
            auto net = std::make_unique<MockNetworkInterface>();
            
            // 可以在这里设置特定的模拟参数
            // net->SetSimulationParameters("eth0", 10.0, 50.0);  // eth0, 10ns时间戳精度, 50ns同步精度
            
            std::cout << "Mock 网络接口创建成功" << std::endl;
            return std::move(net);
            
        } catch (const std::exception& e) {
            std::cerr << "创建Mock 网络接口失败: " << e.what() << std::endl;
            return nullptr;
        }
    }
    
    /**
     * @brief 获取工厂信息
     * @return 工厂描述信息
     */
    std::string GetFactoryInfo() const {
        return "Mock HAL工厂 - 用于macOS开发环境和测试场景的硬件模拟";
    }
    
    /**
     * @brief 验证工厂功能
     * 测试所有设备创建功能
     * @return 验证是否成功
     */
    bool ValidateFactory() {
        std::cout << "验证Mock HAL工厂功能..." << std::endl;
        
        bool all_success = true;
        
        // 测试每种设备的创建
        std::vector<std::pair<std::string, std::function<bool()>>> tests = {
            {"GNSS接收机", [this]() -> bool { 
                auto device = CreateGnssReceiver();
                return device != nullptr;
            }},
            {"PPS输入", [this]() -> bool { 
                auto device = CreatePpsInput();
                return device != nullptr;
            }},
            {"原子钟", [this]() -> bool { 
                auto device = CreateAtomicClock();
                return device != nullptr;
            }},
            {"频率输入", [this]() -> bool { 
                auto device = CreateFrequencyInput();
                return device != nullptr;
            }},
            {"高精度RTC", [this]() -> bool { 
                auto device = CreateRtc();
                return device != nullptr;
            }},
            {"网络接口", [this]() -> bool { 
                auto device = CreateNetworkInterface();
                return device != nullptr;
            }}
        };
        
        for (const auto& test : tests) {
            try {
                bool success = test.second();
                if (success) {
                    std::cout << "✓ " << test.first << " 创建测试通过" << std::endl;
                } else {
                    std::cout << "✗ " << test.first << " 创建测试失败" << std::endl;
                    all_success = false;
                }
            } catch (const std::exception& e) {
                std::cout << "✗ " << test.first << " 创建测试异常: " << e.what() << std::endl;
                all_success = false;
            }
        }
        
        if (all_success) {
            std::cout << "Mock HAL工厂验证成功" << std::endl;
        } else {
            std::cout << "Mock HAL工厂验证失败" << std::endl;
        }
        
        return all_success;
    }
};

// MockHalFactory实现
MockHalFactory::MockHalFactory() {
    std::cout << "Mock HAL工厂已创建" << std::endl;
    std::cout << "支持的Mock设备类型:" << std::endl;
    std::cout << "  - GNSS接收机 (支持NMEA数据模拟)" << std::endl;
    std::cout << "  - PPS输入 (支持1Hz脉冲信号模拟)" << std::endl;
    std::cout << "  - 原子钟 (支持铷钟特性模拟)" << std::endl;
    std::cout << "  - 频率输入 (支持10MHz基准模拟)" << std::endl;
    std::cout << "  - 高精度RTC (支持时间保持模拟)" << std::endl;
    std::cout << "  - 网络接口 (支持PHC和PTP模拟)" << std::endl;
}

MockHalFactory::~MockHalFactory() {
    std::cout << "Mock HAL工厂已销毁" << std::endl;
}

} // namespace hal
} // namespace timing_server