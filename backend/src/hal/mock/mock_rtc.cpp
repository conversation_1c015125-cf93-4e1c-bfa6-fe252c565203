#include "hal/interfaces.h"
#include <iostream>
#include <chrono>
#include <random>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <cstring>

namespace timing_server {
namespace hal {

/**
 * @brief Mock 高精度RTC实现类
 * 
 * 这个类模拟高精度实时时钟(RTC)的行为，主要用于macOS开发环境和测试场景。
 * 它模拟电池供电的RTC芯片特性，包括时间保持、精度漂移、温度影响等。
 * 
 * 主要功能：
 * 1. 模拟高精度RTC时间读取和设置
 * 2. 提供可配置的时间精度和漂移
 * 3. 模拟电池状态和掉电保护
 * 4. 支持时间校准和偏移补偿
 * 5. 模拟各种RTC异常和恢复
 * 
 * RTC特性模拟：
 * - 时间精度：±20ppm (约±1.7秒/天)
 * - 温度系数：±0.04ppm/°C
 * - 电池寿命：>10年
 * - 掉电保持：支持
 * - 分辨率：1秒或更高
 */
class MockHighPrecisionRtc : public I_HighPrecisionRtc {
public:
    /**
     * @brief 构造函数
     * 初始化Mock RTC的默认参数
     */
    MockHighPrecisionRtc()
        : initialized_(false)
        , is_valid_(true)
        , has_battery_power_(true)
        , time_accuracy_ppm_(20.0)        // ±20ppm精度
        , temperature_coefficient_(0.04)   // ±0.04ppm/°C
        , current_temperature_(25.0)       // 室温
        , time_offset_seconds_(0.0)        // 时间偏移
        , last_calibration_time_(std::chrono::steady_clock::now())
        , random_engine_(std::chrono::steady_clock::now().time_since_epoch().count()) {
        
        std::cout << "Mock 高精度RTC已创建" << std::endl;
    }
    
    /**
     * @brief 析构函数
     * 清理资源
     */
    ~MockHighPrecisionRtc() {
        Close();
    }
    
    /**
     * @brief 初始化RTC
     * 
     * 初始化过程包括：
     * 1. 检查电池状态
     * 2. 验证时间有效性
     * 3. 初始化精度参数
     * 4. 设置温度补偿
     * 
     * @return 初始化是否成功
     */
    bool Initialize() override {
        std::cout << "正在初始化Mock 高精度RTC..." << std::endl;
        
        if (initialized_) {
            std::cout << "Mock RTC已经初始化" << std::endl;
            return true;
        }
        
        // 初始化随机数分布
        drift_distribution_ = std::normal_distribution<double>(0.0, time_accuracy_ppm_ * 1e-6);
        temperature_distribution_ = std::uniform_real_distribution<double>(20.0, 30.0);
        battery_distribution_ = std::uniform_real_distribution<double>(0.0, 1.0);
        
        // 检查电池状态
        CheckBatteryStatus();
        
        // 初始化时间偏移（模拟RTC的初始误差）
        time_offset_seconds_ = drift_distribution_(random_engine_) * 86400.0;  // 一天的误差
        
        // 记录初始化时间
        initialization_time_ = std::chrono::system_clock::now();
        last_calibration_time_ = std::chrono::steady_clock::now();
        
        initialized_ = true;
        
        std::cout << "Mock 高精度RTC初始化完成" << std::endl;
        std::cout << "  时间精度: ±" << time_accuracy_ppm_ << " ppm" << std::endl;
        std::cout << "  温度系数: ±" << temperature_coefficient_ << " ppm/°C" << std::endl;
        std::cout << "  电池状态: " << (has_battery_power_ ? "正常" : "低电量") << std::endl;
        std::cout << "  初始时间偏移: " << std::fixed << std::setprecision(3) 
                  << time_offset_seconds_ << " 秒" << std::endl;
        
        return true;
    }
    
    /**
     * @brief 获取RTC时间
     * 
     * 读取当前RTC时间：
     * 1. 获取系统时间作为基准
     * 2. 应用累积的时间漂移
     * 3. 考虑温度补偿
     * 4. 添加随机噪声
     * 
     * @return 时间结构（秒和纳秒）
     */
    timespec GetTime() override {
        timespec ts = {0, 0};
        
        if (!initialized_) {
            std::cerr << "RTC未初始化" << std::endl;
            return ts;
        }
        
        if (!is_valid_) {
            std::cerr << "RTC时间无效" << std::endl;
            return ts;
        }
        
        // 更新RTC模拟状态
        UpdateRtcSimulation();
        
        // 获取当前系统时间
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        
        // 计算总的时间偏移
        double total_offset = CalculateTotalTimeOffset();
        
        // 应用偏移
        auto adjusted_duration = duration + std::chrono::nanoseconds(
            static_cast<int64_t>(total_offset * 1e9));
        
        // 转换为timespec
        auto seconds = std::chrono::duration_cast<std::chrono::seconds>(adjusted_duration);
        auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(
            adjusted_duration - seconds);
        
        ts.tv_sec = seconds.count();
        ts.tv_nsec = nanoseconds.count();
        
        // 输出调试信息（每小时一次）
        static auto last_debug_output = std::chrono::steady_clock::now();
        auto debug_now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::hours>(debug_now - last_debug_output).count() >= 1) {
            std::cout << "RTC时间读取: 偏移=" << std::fixed << std::setprecision(3) 
                      << total_offset << "秒, 温度=" << current_temperature_ << "°C" << std::endl;
            last_debug_output = debug_now;
        }
        
        return ts;
    }
    
    /**
     * @brief 设置RTC时间
     * 
     * 设置RTC时间并校准：
     * 1. 验证时间的合理性
     * 2. 计算与系统时间的差异
     * 3. 更新时间偏移
     * 4. 记录校准时间
     * 
     * @param ts 要设置的时间
     * @return 设置是否成功
     */
    bool SetTime(const timespec& ts) override {
        if (!initialized_) {
            std::cerr << "RTC未初始化，无法设置时间" << std::endl;
            return false;
        }
        
        if (!has_battery_power_) {
            std::cerr << "RTC电池电量不足，无法设置时间" << std::endl;
            return false;
        }
        
        // 验证时间合理性
        if (ts.tv_sec < 0 || ts.tv_nsec < 0 || ts.tv_nsec >= 1000000000) {
            std::cerr << "无效的时间值" << std::endl;
            return false;
        }
        
        // 获取当前系统时间
        auto now = std::chrono::system_clock::now();
        auto system_duration = now.time_since_epoch();
        auto system_seconds = std::chrono::duration_cast<std::chrono::seconds>(system_duration);
        
        // 计算新的时间偏移
        double new_offset = ts.tv_sec - system_seconds.count();
        double offset_change = new_offset - time_offset_seconds_;
        
        time_offset_seconds_ = new_offset;
        last_calibration_time_ = std::chrono::steady_clock::now();
        
        std::cout << "RTC时间已设置" << std::endl;
        std::cout << "  设置时间: " << FormatTime(ts) << std::endl;
        std::cout << "  偏移变化: " << std::fixed << std::setprecision(3) 
                  << offset_change << " 秒" << std::endl;
        std::cout << "  新偏移: " << time_offset_seconds_ << " 秒" << std::endl;
        
        return true;
    }
    
    /**
     * @brief 检查RTC是否有效
     * 
     * 检查RTC的有效性：
     * 1. 电池状态
     * 2. 时间合理性
     * 3. 硬件状态
     * 
     * @return RTC有效性
     */
    bool IsValid() override {
        if (!initialized_) {
            return false;
        }
        
        // 更新状态
        UpdateRtcSimulation();
        
        return is_valid_ && has_battery_power_;
    }
    
    /**
     * @brief 关闭RTC
     * 
     * 清理所有资源：
     * 1. 保存当前状态
     * 2. 重置标志
     * 3. 输出统计信息
     */
    void Close() override {
        if (initialized_) {
            std::cout << "正在关闭Mock 高精度RTC..." << std::endl;
            
            // 输出运行统计
            if (initialization_time_.time_since_epoch().count() > 0) {
                auto now = std::chrono::system_clock::now();
                auto runtime = std::chrono::duration_cast<std::chrono::hours>(
                    now - initialization_time_).count();
                
                std::cout << "RTC运行统计:" << std::endl;
                std::cout << "  运行时间: " << runtime << " 小时" << std::endl;
                std::cout << "  最终偏移: " << std::fixed << std::setprecision(3) 
                          << time_offset_seconds_ << " 秒" << std::endl;
                std::cout << "  平均温度: " << current_temperature_ << "°C" << std::endl;
            }
            
            initialized_ = false;
            is_valid_ = false;
            
            std::cout << "Mock 高精度RTC已关闭" << std::endl;
        }
    }
    
    /**
     * @brief 设置模拟参数
     * 用于测试不同的RTC特性
     * @param accuracy_ppm 时间精度(ppm)
     * @param temp_coeff 温度系数(ppm/°C)
     * @param has_battery 是否有电池供电
     */
    void SetSimulationParameters(double accuracy_ppm, double temp_coeff, bool has_battery) {
        time_accuracy_ppm_ = accuracy_ppm;
        temperature_coefficient_ = temp_coeff;
        has_battery_power_ = has_battery;
        
        // 更新随机数分布
        drift_distribution_ = std::normal_distribution<double>(0.0, accuracy_ppm * 1e-6);
        
        std::cout << "RTC模拟参数已更新:" << std::endl;
        std::cout << "  时间精度: ±" << accuracy_ppm << " ppm" << std::endl;
        std::cout << "  温度系数: ±" << temp_coeff << " ppm/°C" << std::endl;
        std::cout << "  电池状态: " << (has_battery ? "正常" : "异常") << std::endl;
    }
    
    /**
     * @brief 获取电池状态
     * @return 电池是否正常
     */
    bool GetBatteryStatus() const {
        return has_battery_power_;
    }
    
    /**
     * @brief 获取当前温度
     * @return 当前温度(°C)
     */
    double GetCurrentTemperature() const {
        return current_temperature_;
    }
    
    /**
     * @brief 获取时间偏移
     * @return 当前时间偏移(秒)
     */
    double GetTimeOffset() const {
        return time_offset_seconds_;
    }
    
    /**
     * @brief 模拟RTC异常
     * 用于测试异常处理逻辑
     * @param fault_type 故障类型
     */
    void SimulateFault(const std::string& fault_type) {
        if (fault_type == "battery_low") {
            has_battery_power_ = false;
            is_valid_ = false;
            std::cout << "模拟RTC电池电量不足" << std::endl;
        } else if (fault_type == "time_invalid") {
            is_valid_ = false;
            std::cout << "模拟RTC时间无效" << std::endl;
        } else if (fault_type == "large_drift") {
            time_offset_seconds_ += 3600.0;  // 增加1小时偏移
            std::cout << "模拟RTC大幅时间漂移" << std::endl;
        }
    }

private:
    /**
     * @brief 更新RTC模拟状态
     * 
     * 定期更新RTC的各种状态：
     * 1. 时间漂移累积
     * 2. 温度变化模拟
     * 3. 电池状态检查
     * 4. 有效性评估
     */
    void UpdateRtcSimulation() {
        static auto last_update = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        
        auto dt = std::chrono::duration_cast<std::chrono::seconds>(now - last_update).count();
        if (dt < 60) {  // 每分钟更新一次
            return;
        }
        last_update = now;
        
        // 更新温度
        UpdateTemperature();
        
        // 更新时间漂移
        UpdateTimeDrift(dt);
        
        // 检查电池状态
        CheckBatteryStatus();
        
        // 更新有效性
        UpdateValidity();
    }
    
    /**
     * @brief 更新温度模拟
     * 模拟环境温度的缓慢变化
     */
    void UpdateTemperature() {
        // 模拟温度的缓慢变化（正弦波 + 随机噪声）
        static double temperature_phase = 0.0;
        temperature_phase += 0.001;  // 缓慢变化
        
        double base_temp = 25.0 + 5.0 * std::sin(temperature_phase);  // 20-30°C范围
        double noise = temperature_distribution_(random_engine_) - 25.0;  // ±5°C噪声
        
        current_temperature_ = base_temp + noise * 0.1;  // 小幅噪声
    }
    
    /**
     * @brief 更新时间漂移
     * 计算累积的时间漂移
     * @param dt 时间间隔(秒)
     */
    void UpdateTimeDrift(double dt) {
        // 基础漂移（基于精度规格）
        double base_drift = drift_distribution_(random_engine_) * dt;
        
        // 温度影响
        double temp_error = current_temperature_ - 25.0;  // 相对于25°C
        double temp_drift = temperature_coefficient_ * 1e-6 * temp_error * dt;
        
        // 累积漂移
        time_offset_seconds_ += base_drift + temp_drift;
    }
    
    /**
     * @brief 检查电池状态
     * 模拟电池电量检查
     */
    void CheckBatteryStatus() {
        // 模拟电池电量检查（很小概率电量不足）
        if (battery_distribution_(random_engine_) < 0.001) {  // 0.1%概率
            if (has_battery_power_) {
                has_battery_power_ = false;
                std::cout << "警告: RTC电池电量不足" << std::endl;
            }
        }
    }
    
    /**
     * @brief 更新有效性状态
     * 基于各种条件评估RTC有效性
     */
    void UpdateValidity() {
        // RTC有效性基于电池状态和时间合理性
        bool was_valid = is_valid_;
        is_valid_ = has_battery_power_ && (std::abs(time_offset_seconds_) < 86400.0);  // 偏移<1天
        
        if (was_valid && !is_valid_) {
            std::cout << "警告: RTC时间变为无效状态" << std::endl;
        } else if (!was_valid && is_valid_) {
            std::cout << "信息: RTC时间恢复有效状态" << std::endl;
        }
    }
    
    /**
     * @brief 计算总时间偏移
     * 综合各种因素计算当前的总时间偏移
     * @return 总偏移(秒)
     */
    double CalculateTotalTimeOffset() {
        // 基础偏移
        double total_offset = time_offset_seconds_;
        
        // 自上次校准以来的漂移
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            now - last_calibration_time_).count();
        
        if (elapsed > 0) {
            // 添加持续漂移
            double drift_rate = time_accuracy_ppm_ * 1e-6;  // ppm转换为比例
            total_offset += drift_rate * elapsed;
        }
        
        return total_offset;
    }
    
    /**
     * @brief 格式化时间显示
     * @param ts 时间结构
     * @return 格式化的时间字符串
     */
    std::string FormatTime(const timespec& ts) {
        std::time_t time = ts.tv_sec;
        std::tm* tm = std::gmtime(&time);
        
        std::ostringstream oss;
        oss << std::put_time(tm, "%Y-%m-%d %H:%M:%S");
        oss << "." << std::setfill('0') << std::setw(9) << ts.tv_nsec;
        oss << " UTC";
        
        return oss.str();
    }

private:
    bool initialized_;                          // 初始化状态
    bool is_valid_;                             // 时间有效性
    bool has_battery_power_;                    // 电池状态
    
    // 精度参数
    double time_accuracy_ppm_;                  // 时间精度(ppm)
    double temperature_coefficient_;            // 温度系数(ppm/°C)
    double current_temperature_;                // 当前温度(°C)
    
    // 时间相关
    double time_offset_seconds_;                // 时间偏移(秒)
    std::chrono::system_clock::time_point initialization_time_;  // 初始化时间
    std::chrono::steady_clock::time_point last_calibration_time_;  // 上次校准时间
    
    // 随机数生成器
    std::mt19937 random_engine_;
    std::normal_distribution<double> drift_distribution_;
    std::uniform_real_distribution<double> temperature_distribution_;
    std::uniform_real_distribution<double> battery_distribution_;
};

} // namespace hal
} // namespace timing_server