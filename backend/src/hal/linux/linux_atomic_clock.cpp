#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>
#include <thread>
#include <vector>
#include <cmath>

// Linux SPI/I2C接口头文件
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <errno.h>
#include <cstring>

// SPI接口
#ifdef __linux__
#include <linux/spi/spidev.h>
#endif

// I2C接口
#ifdef __linux__
#include <linux/i2c-dev.h>
#endif

namespace timing_server {
namespace hal {

/**
 * @brief Linux原子钟实现
 * 通过SPI/I2C接口控制铷原子钟，支持温度补偿和频率校正
 * 适用于多种厂商的铷钟产品，如Stanford Research Systems、Microsemi等
 */
class LinuxAtomicClock : public I_AtomicClock {
public:
    /**
     * @brief 原子钟接口类型枚举
     */
    enum class InterfaceType {
        SPI,        // SPI接口
        I2C,        // I2C接口
        UART        // UART接口
    };
    
    /**
     * @brief 构造Linux原子钟
     * @param interface_type 接口类型
     * @param device_path 设备路径 (如: /dev/spidev0.0, /dev/i2c-1)
     * @param device_address 设备地址 (I2C使用)
     */
    explicit LinuxAtomicClock(InterfaceType interface_type = InterfaceType::SPI,
                             const std::string& device_path = "/dev/spidev0.0",
                             uint8_t device_address = 0x48);
    
    /**
     * @brief 析构函数
     * 自动关闭设备连接和清理资源
     */
    ~LinuxAtomicClock() override;
    
    /**
     * @brief 初始化原子钟
     * 打开通信接口，配置设备参数，验证设备响应
     * @return 初始化是否成功
     */
    bool Initialize() override;
    
    /**
     * @brief 获取原子钟状态
     * 读取原子钟的当前工作状态和健康信息
     * @return 时钟健康状态
     */
    ClockHealth GetStatus() override;
    
    /**
     * @brief 设置频率校正
     * 调整原子钟的输出频率，用于驯服和校准
     * @param ppm 频率校正值（ppm），正值表示频率增加
     * @return 设置是否成功
     */
    bool SetFrequencyCorrection(double ppm) override;
    
    /**
     * @brief 获取频率偏移
     * 读取当前设置的频率校正值
     * @return 当前频率偏移（ppm）
     */
    double GetFrequencyOffset() override;
    
    /**
     * @brief 获取时钟健康状况
     * 综合评估原子钟的健康状态，包括温度、老化等因素
     * @return 健康状况结构
     */
    ClockHealth GetHealth() override;
    
    /**
     * @brief 关闭原子钟
     * 停止监控线程，关闭通信接口，清理所有资源
     */
    void Close() override;

private:
    /**
     * @brief 配置SPI接口
     * 设置SPI通信参数，如时钟频率、模式等
     * @return 配置是否成功
     */
    bool ConfigureSpiInterface();
    
    /**
     * @brief 配置I2C接口
     * 设置I2C设备地址和通信参数
     * @return 配置是否成功
     */
    bool ConfigureI2cInterface();
    
    /**
     * @brief 发送SPI命令
     * 通过SPI接口发送命令并接收响应
     * @param tx_data 发送数据
     * @param rx_data 接收数据缓冲区
     * @param length 数据长度
     * @return 发送是否成功
     */
    bool SendSpiCommand(const uint8_t* tx_data, uint8_t* rx_data, size_t length);
    
    /**
     * @brief 发送I2C命令
     * 通过I2C接口发送命令并接收响应
     * @param reg_addr 寄存器地址
     * @param data 数据指针
     * @param length 数据长度
     * @param is_write 是否为写操作
     * @return 发送是否成功
     */
    bool SendI2cCommand(uint8_t reg_addr, uint8_t* data, size_t length, bool is_write);
    
    /**
     * @brief 读取温度传感器
     * 从原子钟内部温度传感器读取温度值
     * @return 温度值（摄氏度）
     */
    double ReadTemperature();
    
    /**
     * @brief 读取频率控制寄存器
     * 读取当前的频率校正设置
     * @return 频率控制值
     */
    uint32_t ReadFrequencyControl();
    
    /**
     * @brief 写入频率控制寄存器
     * 设置新的频率校正值
     * @param control_value 控制值
     * @return 写入是否成功
     */
    bool WriteFrequencyControl(uint32_t control_value);
    
    /**
     * @brief 读取状态寄存器
     * 读取原子钟的状态信息
     * @return 状态寄存器值
     */
    uint32_t ReadStatusRegister();
    
    /**
     * @brief 转换频率校正值
     * 将ppm值转换为设备特定的控制值
     * @param ppm 频率偏移（ppm）
     * @return 设备控制值
     */
    uint32_t ConvertPpmToControlValue(double ppm);
    
    /**
     * @brief 转换控制值为ppm
     * 将设备控制值转换为ppm值
     * @param control_value 设备控制值
     * @return 频率偏移（ppm）
     */
    double ConvertControlValueToPpm(uint32_t control_value);
    
    /**
     * @brief 温度补偿计算
     * 根据温度变化计算频率补偿值
     * @param current_temp 当前温度
     * @param reference_temp 参考温度
     * @return 温度补偿值（ppm）
     */
    double CalculateTemperatureCompensation(double current_temp, double reference_temp);
    
    /**
     * @brief 监控线程函数
     * 定期监控原子钟状态，更新健康信息
     */
    void MonitoringThread();
    
    /**
     * @brief 更新健康状态
     * 基于当前读取的参数更新健康状态
     */
    void UpdateHealthStatus();
    
    // 设备配置
    InterfaceType interface_type_;      // 接口类型
    std::string device_path_;           // 设备路径
    uint8_t device_address_;            // I2C设备地址
    int device_fd_;                     // 设备文件描述符
    
    // SPI配置
    uint32_t spi_speed_;                // SPI时钟频率
    uint8_t spi_mode_;                  // SPI模式
    uint8_t spi_bits_;                  // SPI数据位数
    
    // 设备状态
    std::atomic<bool> initialized_;     // 初始化状态
    std::atomic<bool> monitoring_;      // 监控状态
    std::unique_ptr<std::thread> monitor_thread_; // 监控线程
    
    // 原子钟参数
    std::mutex status_mutex_;           // 状态互斥锁
    ClockHealth current_health_;        // 当前健康状态
    double current_temperature_;        // 当前温度
    double frequency_offset_ppm_;       // 当前频率偏移
    uint32_t current_control_value_;    // 当前控制值
    
    // 温度补偿参数
    double reference_temperature_;      // 参考温度
    double temperature_coefficient_;    // 温度系数（ppm/°C）
    bool temperature_compensation_enabled_; // 温度补偿使能
    
    // 统计信息
    std::atomic<uint64_t> read_count_;  // 读取计数
    std::atomic<uint64_t> write_count_; // 写入计数
    std::atomic<uint64_t> error_count_; // 错误计数
    
    // 历史数据
    std::vector<double> temperature_history_; // 温度历史
    std::vector<double> frequency_history_;   // 频率历史
    static const size_t MAX_HISTORY_SIZE = 1440; // 24小时历史(分钟级)
    
    // 设备特定常量
    static constexpr double MAX_FREQUENCY_RANGE_PPM = 100.0;    // 最大频率调节范围
    static constexpr double TEMPERATURE_COEFFICIENT_DEFAULT = -1e-9; // 默认温度系数
    static constexpr double REFERENCE_TEMPERATURE_DEFAULT = 25.0;    // 默认参考温度
    static constexpr int MONITORING_INTERVAL_MS = 1000;             // 监控间隔
    static constexpr int MAX_CONSECUTIVE_ERRORS = 10;               // 最大连续错误数
};

} // namespace hal
} // namespace timing_server// 基本实现方法
L
inuxAtomicClock::LinuxAtomicClock(InterfaceType interface_type, 
                                   const std::string& device_path,
                                   uint8_t device_address)
    : interface_type_(interface_type)
    , device_path_(device_path)
    , device_address_(device_address)
    , device_fd_(-1)
    , initialized_(false)
    , monitoring_(false)
    , current_temperature_(25.0)
    , frequency_offset_ppm_(0.0)
    , current_control_value_(0)
    , reference_temperature_(REFERENCE_TEMPERATURE_DEFAULT)
    , temperature_coefficient_(TEMPERATURE_COEFFICIENT_DEFAULT)
    , temperature_compensation_enabled_(true)
    , read_count_(0)
    , write_count_(0)
    , error_count_(0)
{
    // 初始化健康状态
    current_health_.temperature = current_temperature_;
    current_health_.frequency_offset = frequency_offset_ppm_;
    current_health_.is_healthy = false;
    current_health_.status_message = "未初始化";
    
    // SPI默认配置
    spi_speed_ = 1000000;  // 1MHz
    spi_mode_ = 0;         // SPI模式0
    spi_bits_ = 8;         // 8位数据
}

LinuxAtomicClock::~LinuxAtomicClock() {
    Close();
}

bool LinuxAtomicClock::Initialize() {
    // 根据接口类型进行初始化
    bool success = false;
    switch (interface_type_) {
        case InterfaceType::SPI:
            success = ConfigureSpiInterface();
            break;
        case InterfaceType::I2C:
            success = ConfigureI2cInterface();
            break;
        case InterfaceType::UART:
            // UART接口暂未实现
            std::cerr << "UART接口暂未实现" << std::endl;
            return false;
    }
    
    if (!success) {
        return false;
    }
    
    // 读取初始状态
    current_temperature_ = ReadTemperature();
    current_control_value_ = ReadFrequencyControl();
    frequency_offset_ppm_ = ConvertControlValueToPpm(current_control_value_);
    
    // 启动监控线程
    monitoring_ = true;
    monitor_thread_ = std::make_unique<std::thread>(&LinuxAtomicClock::MonitoringThread, this);
    
    initialized_ = true;
    UpdateHealthStatus();
    
    std::cout << "原子钟设备初始化成功: " << device_path_ << std::endl;
    return true;
}

ClockHealth LinuxAtomicClock::GetStatus() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    return current_health_;
}

bool LinuxAtomicClock::SetFrequencyCorrection(double ppm) {
    if (!initialized_) {
        return false;
    }
    
    // 检查频率范围
    if (std::abs(ppm) > MAX_FREQUENCY_RANGE_PPM) {
        std::cerr << "频率校正值超出范围: " << ppm << " ppm" << std::endl;
        return false;
    }
    
    // 转换为控制值
    uint32_t control_value = ConvertPpmToControlValue(ppm);
    
    // 写入设备
    if (WriteFrequencyControl(control_value)) {
        std::lock_guard<std::mutex> lock(status_mutex_);
        frequency_offset_ppm_ = ppm;
        current_control_value_ = control_value;
        current_health_.frequency_offset = ppm;
        write_count_++;
        return true;
    }
    
    error_count_++;
    return false;
}

double LinuxAtomicClock::GetFrequencyOffset() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    return frequency_offset_ppm_;
}

ClockHealth LinuxAtomicClock::GetHealth() {
    return GetStatus();
}

void LinuxAtomicClock::Close() {
    if (monitoring_) {
        monitoring_ = false;
        if (monitor_thread_ && monitor_thread_->joinable()) {
            monitor_thread_->join();
        }
    }
    
    if (device_fd_ >= 0) {
        close(device_fd_);
        device_fd_ = -1;
    }
    
    initialized_ = false;
    std::cout << "原子钟设备已关闭" << std::endl;
}

bool LinuxAtomicClock::ConfigureSpiInterface() {
    // 打开SPI设备
    device_fd_ = open(device_path_.c_str(), O_RDWR);
    if (device_fd_ < 0) {
        std::cerr << "无法打开SPI设备: " << device_path_ << " - " << strerror(errno) << std::endl;
        return false;
    }
    
#ifdef __linux__
    // 设置SPI模式
    if (ioctl(device_fd_, SPI_IOC_WR_MODE, &spi_mode_) < 0) {
        std::cerr << "设置SPI模式失败: " << strerror(errno) << std::endl;
        close(device_fd_);
        device_fd_ = -1;
        return false;
    }
    
    // 设置数据位数
    if (ioctl(device_fd_, SPI_IOC_WR_BITS_PER_WORD, &spi_bits_) < 0) {
        std::cerr << "设置SPI数据位数失败: " << strerror(errno) << std::endl;
        close(device_fd_);
        device_fd_ = -1;
        return false;
    }
    
    // 设置时钟频率
    if (ioctl(device_fd_, SPI_IOC_WR_MAX_SPEED_HZ, &spi_speed_) < 0) {
        std::cerr << "设置SPI时钟频率失败: " << strerror(errno) << std::endl;
        close(device_fd_);
        device_fd_ = -1;
        return false;
    }
#endif
    
    return true;
}

bool LinuxAtomicClock::ConfigureI2cInterface() {
    // 打开I2C设备
    device_fd_ = open(device_path_.c_str(), O_RDWR);
    if (device_fd_ < 0) {
        std::cerr << "无法打开I2C设备: " << device_path_ << " - " << strerror(errno) << std::endl;
        return false;
    }
    
#ifdef __linux__
    // 设置I2C从设备地址
    if (ioctl(device_fd_, I2C_SLAVE, device_address_) < 0) {
        std::cerr << "设置I2C从设备地址失败: " << strerror(errno) << std::endl;
        close(device_fd_);
        device_fd_ = -1;
        return false;
    }
#endif
    
    return true;
}

double LinuxAtomicClock::ReadTemperature() {
    // 这里应该实现实际的温度读取
    // 暂时返回模拟值
    return 25.0 + (rand() % 100) / 100.0; // 25.0-26.0°C
}

uint32_t LinuxAtomicClock::ReadFrequencyControl() {
    // 这里应该实现实际的频率控制寄存器读取
    // 暂时返回默认值
    read_count_++;
    return 0x80000000; // 中间值
}

bool LinuxAtomicClock::WriteFrequencyControl(uint32_t control_value) {
    // 这里应该实现实际的频率控制寄存器写入
    // 暂时返回成功
    write_count_++;
    return true;
}

uint32_t LinuxAtomicClock::ConvertPpmToControlValue(double ppm) {
    // 简化的转换公式，实际应该根据具体原子钟规格实现
    double normalized = (ppm + MAX_FREQUENCY_RANGE_PPM) / (2.0 * MAX_FREQUENCY_RANGE_PPM);
    return static_cast<uint32_t>(normalized * 0xFFFFFFFF);
}

double LinuxAtomicClock::ConvertControlValueToPpm(uint32_t control_value) {
    // 简化的转换公式，实际应该根据具体原子钟规格实现
    double normalized = static_cast<double>(control_value) / 0xFFFFFFFF;
    return (normalized * 2.0 * MAX_FREQUENCY_RANGE_PPM) - MAX_FREQUENCY_RANGE_PPM;
}

void LinuxAtomicClock::MonitoringThread() {
    while (monitoring_) {
        // 读取温度
        double new_temperature = ReadTemperature();
        
        {
            std::lock_guard<std::mutex> lock(status_mutex_);
            current_temperature_ = new_temperature;
            current_health_.temperature = new_temperature;
            
            // 更新历史数据
            temperature_history_.push_back(new_temperature);
            if (temperature_history_.size() > MAX_HISTORY_SIZE) {
                temperature_history_.erase(temperature_history_.begin());
            }
        }
        
        // 更新健康状态
        UpdateHealthStatus();
        
        // 休眠
        std::this_thread::sleep_for(std::chrono::milliseconds(MONITORING_INTERVAL_MS));
    }
}

void LinuxAtomicClock::UpdateHealthStatus() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 检查温度范围
    bool temp_ok = (current_temperature_ > 0 && current_temperature_ < 80);
    
    // 检查错误率
    bool error_rate_ok = (error_count_ < MAX_CONSECUTIVE_ERRORS);
    
    current_health_.is_healthy = temp_ok && error_rate_ok && initialized_;
    
    if (current_health_.is_healthy) {
        current_health_.status_message = "正常工作";
    } else {
        current_health_.status_message = "设备异常";
    }
}

} // namespace hal
} // namespace timing_server