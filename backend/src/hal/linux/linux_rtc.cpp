#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include <memory>
#include <mutex>
#include <chrono>
#include <ctime>

// Linux RTC接口头文件
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <errno.h>
#include <cstring>

// Linux RTC内核接口
#ifdef __linux__
#include <linux/rtc.h>
#endif

namespace timing_server {
namespace hal {

/**
 * @brief Linux高精度RTC实现
 * 访问系统高精度实时时钟，提供非易失性时间存储
 * 支持多种RTC设备，包括板载RTC和外部高精度RTC模块
 */
class LinuxHighPrecisionRtc : public I_HighPrecisionRtc {
public:
    /**
     * @brief 构造Linux高精度RTC
     * @param device_path RTC设备路径 (如: /dev/rtc0, /dev/rtc1)
     */
    explicit LinuxHighPrecisionRtc(const std::string& device_path = "/dev/rtc0");
    
    /**
     * @brief 析构函数
     * 自动关闭RTC设备和清理资源
     */
    ~LinuxHighPrecisionRtc() override;
    
    /**
     * @brief 初始化RTC
     * 打开RTC设备，验证设备功能，检查时钟有效性
     * @return 初始化是否成功
     */
    bool Initialize() override;
    
    /**
     * @brief 获取RTC时间
     * 从RTC设备读取当前时间，转换为高精度时间戳
     * @return 时间结构（秒和纳秒）
     */
    timespec GetTime() override;
    
    /**
     * @brief 设置RTC时间
     * 将指定时间写入RTC设备，更新硬件时钟
     * @param ts 要设置的时间
     * @return 设置是否成功
     */
    bool SetTime(const timespec& ts) override;
    
    /**
     * @brief 检查RTC是否有效
     * 验证RTC设备是否正常工作，时间是否合理
     * @return RTC有效性
     */
    bool IsValid() override;
    
    /**
     * @brief 关闭RTC
     * 关闭RTC设备文件，清理所有资源
     */
    void Close() override;

private:
    /**
     * @brief 读取RTC寄存器
     * 从RTC设备读取时间寄存器值
     * @param rtc_time 输出的RTC时间结构
     * @return 读取是否成功
     */
    bool ReadRtcRegisters(struct rtc_time* rtc_time);
    
    /**
     * @brief 写入RTC寄存器
     * 向RTC设备写入时间寄存器值
     * @param rtc_time 要写入的RTC时间结构
     * @return 写入是否成功
     */
    bool WriteRtcRegisters(const struct rtc_time* rtc_time);
    
    /**
     * @brief 转换时间格式
     * 将RTC时间结构转换为timespec格式
     * @param rtc_time RTC时间结构
     * @return timespec时间结构
     */
    timespec ConvertRtcTimeToTimespec(const struct rtc_time& rtc_time);
    
    /**
     * @brief 转换时间格式
     * 将timespec格式转换为RTC时间结构
     * @param ts timespec时间结构
     * @return RTC时间结构
     */
    struct rtc_time ConvertTimespecToRtcTime(const timespec& ts);
    
    /**
     * @brief 验证时间合理性
     * 检查时间值是否在合理范围内
     * @param ts 时间结构
     * @return 时间是否合理
     */
    bool ValidateTimeRange(const timespec& ts);
    
    /**
     * @brief 检查RTC设备能力
     * 查询RTC设备支持的功能
     * @return 查询是否成功
     */
    bool QueryRtcCapabilities();
    
    /**
     * @brief 校准RTC精度
     * 如果支持，校准RTC的时钟精度
     * @return 校准是否成功
     */
    bool CalibrateRtcAccuracy();
    
    /**
     * @brief 检测时钟漂移
     * 通过与系统时钟比较检测RTC漂移
     * @return 漂移率（ppm）
     */
    double DetectClockDrift();
    
    /**
     * @brief 更新漂移统计
     * 更新RTC漂移的统计信息
     * @param drift_ppm 漂移率
     */
    void UpdateDriftStatistics(double drift_ppm);
    
    // 设备配置
    std::string device_path_;           // RTC设备路径
    int rtc_fd_;                        // RTC设备文件描述符
    bool device_initialized_;           // 设备初始化状态
    
    // RTC设备信息
    struct rtc_wkalrm alarm_info_;      // 闹钟信息
    unsigned long rtc_features_;        // RTC功能标志
    bool supports_alarm_;               // 是否支持闹钟
    bool supports_update_irq_;          // 是否支持更新中断
    bool supports_periodic_irq_;        // 是否支持周期中断
    
    // 时间同步
    std::mutex time_mutex_;             // 时间访问互斥锁
    timespec last_read_time_;           // 最后读取时间
    std::chrono::steady_clock::time_point last_read_timestamp_; // 最后读取时间戳
    
    // 精度和漂移监控
    std::vector<double> drift_history_; // 漂移历史记录
    static const size_t MAX_DRIFT_HISTORY = 24; // 最大漂移历史数量
    double average_drift_ppm_;          // 平均漂移率
    double drift_variance_;             // 漂移方差
    
    // 统计信息
    std::atomic<uint64_t> read_count_;  // 读取计数
    std::atomic<uint64_t> write_count_; // 写入计数
    std::atomic<uint64_t> error_count_; // 错误计数
    
    // 有效性检查
    bool time_valid_;                   // 时间有效性
    timespec min_valid_time_;           // 最小有效时间
    timespec max_valid_time_;           // 最大有效时间
    
    // 配置常量
    static constexpr time_t MIN_VALID_TIMESTAMP = 1577836800;  // 2020-01-01 00:00:00 UTC
    static constexpr time_t MAX_VALID_TIMESTAMP = 4102444800;  // 2100-01-01 00:00:00 UTC
    static constexpr double MAX_ACCEPTABLE_DRIFT_PPM = 100.0;  // 最大可接受漂移率
    static constexpr int DRIFT_MEASUREMENT_INTERVAL_S = 3600;  // 漂移测量间隔(1小时)
    static constexpr int MAX_CONSECUTIVE_ERRORS = 5;           // 最大连续错误数
    
    // 错误处理
    int consecutive_errors_;            // 连续错误计数
    std::chrono::steady_clock::time_point last_error_time_; // 最后错误时间
};

} // namespace hal
} // namespace timing_server//
 基本实现方法
LinuxHighPrecisionRtc::LinuxHighPrecisionRtc(const std::string& device_path)
    : device_path_(device_path)
    , rtc_fd_(-1)
    , device_initialized_(false)
    , rtc_features_(0)
    , supports_alarm_(false)
    , supports_update_irq_(false)
    , supports_periodic_irq_(false)
    , average_drift_ppm_(0.0)
    , drift_variance_(0.0)
    , read_count_(0)
    , write_count_(0)
    , error_count_(0)
    , time_valid_(false)
    , consecutive_errors_(0)
{
    // 初始化时间范围
    min_valid_time_.tv_sec = MIN_VALID_TIMESTAMP;
    min_valid_time_.tv_nsec = 0;
    max_valid_time_.tv_sec = MAX_VALID_TIMESTAMP;
    max_valid_time_.tv_nsec = 0;
    
    // 初始化最后读取时间
    last_read_time_.tv_sec = 0;
    last_read_time_.tv_nsec = 0;
    
    // 初始化闹钟信息
    memset(&alarm_info_, 0, sizeof(alarm_info_));
}

LinuxHighPrecisionRtc::~LinuxHighPrecisionRtc() {
    Close();
}

bool LinuxHighPrecisionRtc::Initialize() {
    // 打开RTC设备
    rtc_fd_ = open(device_path_.c_str(), O_RDWR);
    if (rtc_fd_ < 0) {
        std::cerr << "无法打开RTC设备: " << device_path_ << " - " << strerror(errno) << std::endl;
        return false;
    }
    
    // 查询RTC设备能力
    if (!QueryRtcCapabilities()) {
        close(rtc_fd_);
        rtc_fd_ = -1;
        return false;
    }
    
    // 验证RTC时间有效性
    timespec current_time = GetTime();
    time_valid_ = ValidateTimeRange(current_time);
    
    device_initialized_ = true;
    std::cout << "RTC设备初始化成功: " << device_path_ << std::endl;
    
    return true;
}

timespec LinuxHighPrecisionRtc::GetTime() {
    timespec ts = {0, 0};
    
    if (!device_initialized_) {
        return ts;
    }
    
#ifdef __linux__
    struct rtc_time rtc_tm;
    if (ReadRtcRegisters(&rtc_tm)) {
        ts = ConvertRtcTimeToTimespec(rtc_tm);
        
        {
            std::lock_guard<std::mutex> lock(time_mutex_);
            last_read_time_ = ts;
            last_read_timestamp_ = std::chrono::steady_clock::now();
        }
        
        read_count_++;
        consecutive_errors_ = 0;
    } else {
        error_count_++;
        consecutive_errors_++;
    }
#endif
    
    return ts;
}

bool LinuxHighPrecisionRtc::SetTime(const timespec& ts) {
    if (!device_initialized_) {
        return false;
    }
    
    if (!ValidateTimeRange(ts)) {
        std::cerr << "RTC时间超出有效范围" << std::endl;
        return false;
    }
    
#ifdef __linux__
    struct rtc_time rtc_tm = ConvertTimespecToRtcTime(ts);
    if (WriteRtcRegisters(&rtc_tm)) {
        write_count_++;
        consecutive_errors_ = 0;
        time_valid_ = true;
        return true;
    } else {
        error_count_++;
        consecutive_errors_++;
        return false;
    }
#else
    return false;
#endif
}

bool LinuxHighPrecisionRtc::IsValid() {
    if (!device_initialized_) {
        return false;
    }
    
    // 检查连续错误数
    if (consecutive_errors_ >= MAX_CONSECUTIVE_ERRORS) {
        return false;
    }
    
    // 检查时间有效性
    return time_valid_;
}

void LinuxHighPrecisionRtc::Close() {
    if (rtc_fd_ >= 0) {
        close(rtc_fd_);
        rtc_fd_ = -1;
    }
    
    device_initialized_ = false;
    std::cout << "RTC设备已关闭" << std::endl;
}

bool LinuxHighPrecisionRtc::ReadRtcRegisters(struct rtc_time* rtc_time) {
#ifdef __linux__
    if (ioctl(rtc_fd_, RTC_RD_TIME, rtc_time) < 0) {
        std::cerr << "读取RTC时间失败: " << strerror(errno) << std::endl;
        return false;
    }
    return true;
#else
    return false;
#endif
}

bool LinuxHighPrecisionRtc::WriteRtcRegisters(const struct rtc_time* rtc_time) {
#ifdef __linux__
    if (ioctl(rtc_fd_, RTC_SET_TIME, rtc_time) < 0) {
        std::cerr << "设置RTC时间失败: " << strerror(errno) << std::endl;
        return false;
    }
    return true;
#else
    return false;
#endif
}

timespec LinuxHighPrecisionRtc::ConvertRtcTimeToTimespec(const struct rtc_time& rtc_time) {
    struct tm tm_time;
    memset(&tm_time, 0, sizeof(tm_time));
    
    tm_time.tm_sec = rtc_time.tm_sec;
    tm_time.tm_min = rtc_time.tm_min;
    tm_time.tm_hour = rtc_time.tm_hour;
    tm_time.tm_mday = rtc_time.tm_mday;
    tm_time.tm_mon = rtc_time.tm_mon;
    tm_time.tm_year = rtc_time.tm_year;
    tm_time.tm_wday = rtc_time.tm_wday;
    tm_time.tm_yday = rtc_time.tm_yday;
    tm_time.tm_isdst = rtc_time.tm_isdst;
    
    timespec ts;
    ts.tv_sec = mktime(&tm_time);
    ts.tv_nsec = 0; // RTC通常只有秒级精度
    
    return ts;
}

struct rtc_time LinuxHighPrecisionRtc::ConvertTimespecToRtcTime(const timespec& ts) {
    struct rtc_time rtc_time;
    memset(&rtc_time, 0, sizeof(rtc_time));
    
    struct tm* tm_time = gmtime(&ts.tv_sec);
    if (tm_time) {
        rtc_time.tm_sec = tm_time->tm_sec;
        rtc_time.tm_min = tm_time->tm_min;
        rtc_time.tm_hour = tm_time->tm_hour;
        rtc_time.tm_mday = tm_time->tm_mday;
        rtc_time.tm_mon = tm_time->tm_mon;
        rtc_time.tm_year = tm_time->tm_year;
        rtc_time.tm_wday = tm_time->tm_wday;
        rtc_time.tm_yday = tm_time->tm_yday;
        rtc_time.tm_isdst = tm_time->tm_isdst;
    }
    
    return rtc_time;
}

bool LinuxHighPrecisionRtc::ValidateTimeRange(const timespec& ts) {
    return (ts.tv_sec >= min_valid_time_.tv_sec) && 
           (ts.tv_sec <= max_valid_time_.tv_sec);
}

bool LinuxHighPrecisionRtc::QueryRtcCapabilities() {
    // 这里应该查询RTC设备的具体能力
    // 暂时设置默认值
    supports_alarm_ = true;
    supports_update_irq_ = false;
    supports_periodic_irq_ = false;
    
    std::cout << "RTC设备能力: 支持闹钟=" << supports_alarm_ << std::endl;
    return true;
}

double LinuxHighPrecisionRtc::DetectClockDrift() {
    // 简化的漂移检测实现
    // 实际应该与系统时钟或其他高精度时钟源比较
    return 0.0; // 暂时返回无漂移
}

void LinuxHighPrecisionRtc::UpdateDriftStatistics(double drift_ppm) {
    drift_history_.push_back(drift_ppm);
    if (drift_history_.size() > MAX_DRIFT_HISTORY) {
        drift_history_.erase(drift_history_.begin());
    }
    
    // 计算平均漂移和方差
    if (!drift_history_.empty()) {
        double sum = 0;
        for (double drift : drift_history_) {
            sum += drift;
        }
        average_drift_ppm_ = sum / drift_history_.size();
        
        double variance = 0;
        for (double drift : drift_history_) {
            double diff = drift - average_drift_ppm_;
            variance += diff * diff;
        }
        drift_variance_ = variance / drift_history_.size();
    }
}

} // namespace hal
} // namespace timing_server