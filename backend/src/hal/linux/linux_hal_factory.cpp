#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include "hal/linux_hal_implementations.h"
#include <memory>
#include <iostream>
#include <stdexcept>

// Linux系统头文件
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>

namespace timing_server {
namespace hal {

/**
 * @brief Linux HAL工厂实现
 * 用于Linux x86_64和龙芯LoongArch64平台，提供真实硬件接口访问
 * 支持多种GNSS接收机、PPS设备、原子钟和高端网卡的硬件抽象
 */
class LinuxHalFactory : public I_HalFactory {
public:
    /**
     * @brief 构造Linux HAL工厂
     * 初始化工厂并检测可用的硬件设备
     */
    LinuxHalFactory();
    
    /**
     * @brief 析构函数
     * 清理工厂资源
     */
    ~LinuxHalFactory() override = default;
    
    /**
     * @brief 创建GNSS接收机实例
     * 支持多种厂商的GNSS接收机，通过串口设备访问NMEA数据
     * @return GNSS接收机智能指针
     * @throws HalException 如果设备创建失败
     */
    std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() override;
    
    /**
     * @brief 创建PPS输入实例
     * 使用Linux PPS API捕获高精度1PPS时间戳，支持多种PPS设备
     * @return PPS输入智能指针
     * @throws HalException 如果设备创建失败
     */
    std::unique_ptr<I_PpsInput> CreatePpsInput() override;
    
    /**
     * @brief 创建原子钟实例
     * 通过SPI/I2C接口控制铷原子钟，支持温度补偿和频率校正
     * @return 原子钟智能指针
     * @throws HalException 如果设备创建失败
     */
    std::unique_ptr<I_AtomicClock> CreateAtomicClock() override;
    
    /**
     * @brief 创建频率输入实例
     * 测量外部10MHz频率基准信号，用于高精度频率校准
     * @return 频率输入智能指针
     * @throws HalException 如果设备创建失败
     */
    std::unique_ptr<I_FrequencyInput> CreateFrequencyInput() override;
    
    /**
     * @brief 创建高精度RTC实例
     * 访问系统高精度实时时钟，提供非易失性时间存储
     * @return RTC智能指针
     * @throws HalException 如果设备创建失败
     */
    std::unique_ptr<I_HighPrecisionRtc> CreateRtc() override;
    
    /**
     * @brief 创建网络接口实例
     * 操作网卡PTP硬件时钟(PHC)，支持Intel E810等高端网卡
     * @return 网络接口智能指针
     * @throws HalException 如果设备创建失败
     */
    std::unique_ptr<I_NetworkInterface> CreateNetworkInterface() override;

private:
    /**
     * @brief 检测可用的硬件设备
     * 扫描系统中可用的时间同步相关硬件设备
     */
    void DetectAvailableDevices();
    
    /**
     * @brief 验证设备权限
     * 检查当前用户是否有足够权限访问硬件设备
     * @param device_path 设备路径
     * @return 是否有访问权限
     */
    bool CheckDevicePermissions(const std::string& device_path);
    
    // 设备路径配置
    std::string gnss_device_path_;      // GNSS设备路径 (默认: /dev/ttyS0)
    std::string pps_device_path_;       // PPS设备路径 (默认: /dev/pps0)
    std::string rtc_device_path_;       // RTC设备路径 (默认: /dev/rtc0)
    std::string network_interface_;     // 网络接口名称 (默认: eth0)
    std::string spi_device_path_;       // SPI设备路径 (默认: /dev/spidev0.0)
    
    // 设备可用性标志
    bool gnss_available_;
    bool pps_available_;
    bool atomic_clock_available_;
    bool frequency_input_available_;
    bool rtc_available_;
    bool network_interface_available_;
};

// 实现Linux HAL工厂方法
LinuxHalFactory::LinuxHalFactory() 
    : gnss_device_path_("/dev/ttyS0")
    , pps_device_path_("/dev/pps0")
    , rtc_device_path_("/dev/rtc0")
    , network_interface_("eth0")
    , spi_device_path_("/dev/spidev0.0")
    , gnss_available_(false)
    , pps_available_(false)
    , atomic_clock_available_(false)
    , frequency_input_available_(false)
    , rtc_available_(false)
    , network_interface_available_(false)
{
    std::cout << "Linux HAL工厂已创建，开始检测硬件设备..." << std::endl;
    DetectAvailableDevices();
}

std::unique_ptr<I_GnssReceiver> LinuxHalFactory::CreateGnssReceiver() {
    if (!gnss_available_) {
        throw HalException(HalErrorType::DEVICE_NOT_FOUND, 
                          "GNSS设备不可用", 
                          "设备路径: " + gnss_device_path_);
    }
    
    try {
        return std::make_unique<LinuxGnssReceiver>(gnss_device_path_);
    } catch (const std::exception& e) {
        throw HalException(HalErrorType::DEVICE_INITIALIZATION_FAILED,
                          "GNSS接收机创建失败",
                          e.what());
    }
}

std::unique_ptr<I_PpsInput> LinuxHalFactory::CreatePpsInput() {
    if (!pps_available_) {
        throw HalException(HalErrorType::DEVICE_NOT_FOUND,
                          "PPS设备不可用",
                          "设备路径: " + pps_device_path_);
    }
    
    try {
        return std::make_unique<LinuxPpsInput>(pps_device_path_);
    } catch (const std::exception& e) {
        throw HalException(HalErrorType::DEVICE_INITIALIZATION_FAILED,
                          "PPS输入设备创建失败",
                          e.what());
    }
}

std::unique_ptr<I_AtomicClock> LinuxHalFactory::CreateAtomicClock() {
    if (!atomic_clock_available_) {
        throw HalException(HalErrorType::DEVICE_NOT_FOUND,
                          "原子钟设备不可用",
                          "设备路径: " + spi_device_path_);
    }
    
    try {
        return std::make_unique<LinuxAtomicClock>(
            LinuxAtomicClock::InterfaceType::SPI, 
            spi_device_path_);
    } catch (const std::exception& e) {
        throw HalException(HalErrorType::DEVICE_INITIALIZATION_FAILED,
                          "原子钟设备创建失败",
                          e.what());
    }
}

std::unique_ptr<I_FrequencyInput> LinuxHalFactory::CreateFrequencyInput() {
    if (!frequency_input_available_) {
        throw HalException(HalErrorType::DEVICE_NOT_FOUND,
                          "频率输入设备不可用",
                          "GPIO引脚或计数器设备不可用");
    }
    
    try {
        return std::make_unique<LinuxFrequencyInput>();
    } catch (const std::exception& e) {
        throw HalException(HalErrorType::DEVICE_INITIALIZATION_FAILED,
                          "频率输入设备创建失败",
                          e.what());
    }
}

std::unique_ptr<I_HighPrecisionRtc> LinuxHalFactory::CreateRtc() {
    if (!rtc_available_) {
        throw HalException(HalErrorType::DEVICE_NOT_FOUND,
                          "RTC设备不可用",
                          "设备路径: " + rtc_device_path_);
    }
    
    try {
        return std::make_unique<LinuxHighPrecisionRtc>(rtc_device_path_);
    } catch (const std::exception& e) {
        throw HalException(HalErrorType::DEVICE_INITIALIZATION_FAILED,
                          "RTC设备创建失败",
                          e.what());
    }
}

std::unique_ptr<I_NetworkInterface> LinuxHalFactory::CreateNetworkInterface() {
    if (!network_interface_available_) {
        throw HalException(HalErrorType::DEVICE_NOT_FOUND,
                          "网络接口不可用",
                          "接口名称: " + network_interface_);
    }
    
    try {
        return std::make_unique<LinuxNetworkInterface>(network_interface_);
    } catch (const std::exception& e) {
        throw HalException(HalErrorType::DEVICE_INITIALIZATION_FAILED,
                          "网络接口创建失败",
                          e.what());
    }
}

void LinuxHalFactory::DetectAvailableDevices() {
    // 检测GNSS设备
    gnss_available_ = CheckDevicePermissions(gnss_device_path_);
    if (gnss_available_) {
        std::cout << "检测到GNSS设备: " << gnss_device_path_ << std::endl;
    }
    
    // 检测PPS设备
    pps_available_ = CheckDevicePermissions(pps_device_path_);
    if (pps_available_) {
        std::cout << "检测到PPS设备: " << pps_device_path_ << std::endl;
    }
    
    // 检测原子钟设备(SPI)
    atomic_clock_available_ = CheckDevicePermissions(spi_device_path_);
    if (atomic_clock_available_) {
        std::cout << "检测到原子钟设备: " << spi_device_path_ << std::endl;
    }
    
    // 检测RTC设备
    rtc_available_ = CheckDevicePermissions(rtc_device_path_);
    if (rtc_available_) {
        std::cout << "检测到RTC设备: " << rtc_device_path_ << std::endl;
    }
    
    // 检测网络接口
    // 这里简化处理，实际应该检查接口是否存在且支持PTP
    network_interface_available_ = true;
    std::cout << "检测到网络接口: " << network_interface_ << std::endl;
    
    // 检测频率输入(GPIO)
    // 这里简化处理，实际应该检查GPIO是否可用
    frequency_input_available_ = true;
    std::cout << "检测到频率输入设备(GPIO)" << std::endl;
}

bool LinuxHalFactory::CheckDevicePermissions(const std::string& device_path) {
    // 检查设备文件是否存在且可访问
    int fd = open(device_path.c_str(), O_RDWR);
    if (fd >= 0) {
        close(fd);
        return true;
    }
    
    // 如果无法打开，检查是否是权限问题
    if (errno == EACCES) {
        std::cout << "警告: 设备 " << device_path << " 存在但权限不足" << std::endl;
        return false;
    }
    
    return false;
}

// 导出工厂创建函数供外部使用
extern "C" {
    std::unique_ptr<I_HalFactory> CreateLinuxHalFactory() {
        return std::make_unique<LinuxHalFactory>();
    }
}

} // namespace hal
} // namespace timing_server