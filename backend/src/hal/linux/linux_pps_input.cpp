#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>
#include <thread>

// Linux PPS API头文件
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/time.h>
#include <errno.h>
#include <cstring>

// Linux PPS内核接口
#ifdef __linux__
#include <linux/pps.h>
#include <sys/ioctl.h>
#endif

namespace timing_server {
namespace hal {

/**
 * @brief Linux PPS输入实现
 * 使用Linux PPS API捕获高精度1PPS时间戳，支持多种PPS设备
 * 包括GNSS接收机输出的PPS信号、原子钟PPS输出等
 */
class LinuxPpsInput : public I_PpsInput {
public:
    /**
     * @brief 构造Linux PPS输入
     * @param device_path PPS设备路径 (如: /dev/pps0, /dev/pps1)
     */
    explicit LinuxPpsInput(const std::string& device_path = "/dev/pps0");
    
    /**
     * @brief 析构函数
     * 自动关闭PPS设备和清理资源
     */
    ~LinuxPpsInput() override;
    
    /**
     * @brief 初始化PPS输入
     * 打开PPS设备，配置PPS参数，验证设备功能
     * @return 初始化是否成功
     */
    bool Initialize() override;
    
    /**
     * @brief 等待PPS信号边沿
     * 阻塞等待下一个PPS信号边沿，支持超时控制
     * @param timeout_ms 超时时间（毫秒），0表示无限等待
     * @return 是否检测到PPS边沿
     */
    bool WaitForPpsEdge(int timeout_ms) override;
    
    /**
     * @brief 获取最后一次PPS时间戳
     * 返回最近一次PPS事件的高精度时间戳
     * @return 纳秒级时间戳
     */
    uint64_t GetLastPpsTimestamp() override;
    
    /**
     * @brief 关闭PPS输入
     * 停止PPS监听，关闭设备文件，清理所有资源
     */
    void Close() override;

private:
    /**
     * @brief 配置PPS设备参数
     * 设置PPS捕获模式、边沿类型等参数
     * @return 配置是否成功
     */
    bool ConfigurePpsDevice();
    
    /**
     * @brief 获取PPS设备能力
     * 查询PPS设备支持的功能和参数
     * @return 查询是否成功
     */
    bool QueryPpsCapabilities();
    
    /**
     * @brief 执行PPS时间戳捕获
     * 使用PPS API获取精确的时间戳信息
     * @param pps_info 输出的PPS信息结构
     * @return 捕获是否成功
     */
    bool CapturePpsTimestamp(struct pps_fdata* pps_info);
    
    /**
     * @brief 转换时间戳格式
     * 将PPS API的时间戳转换为纳秒级时间戳
     * @param pps_time PPS时间结构
     * @return 纳秒级时间戳
     */
    uint64_t ConvertPpsTimestamp(const struct pps_ktime& pps_time);
    
    /**
     * @brief 验证PPS信号质量
     * 检查PPS信号的稳定性和精度
     * @return 信号质量是否合格
     */
    bool ValidatePpsSignalQuality();
    
    /**
     * @brief 更新统计信息
     * 更新PPS事件计数和质量统计
     */
    void UpdateStatistics();
    
    // 设备配置
    std::string device_path_;           // PPS设备路径
    int pps_fd_;                        // PPS设备文件描述符
    
    // PPS设备信息
    struct pps_params pps_params_;      // PPS参数
    int pps_mode_;                      // PPS模式
    bool device_initialized_;           // 设备初始化状态
    
    // 时间戳信息
    std::mutex timestamp_mutex_;        // 时间戳互斥锁
    uint64_t last_timestamp_ns_;        // 最后一次PPS时间戳
    std::chrono::steady_clock::time_point last_pps_time_; // 最后PPS事件时间
    
    // 信号质量统计
    std::atomic<uint64_t> pps_event_count_;     // PPS事件计数
    std::atomic<uint64_t> missed_pulses_;       // 丢失脉冲计数
    std::atomic<uint64_t> error_count_;         // 错误计数
    
    // 质量监控
    std::vector<uint64_t> interval_history_;    // 间隔历史记录
    static const size_t MAX_HISTORY_SIZE = 60;  // 最大历史记录数量
    double average_interval_ns_;                 // 平均间隔
    double interval_jitter_ns_;                  // 间隔抖动
    
    // 配置常量
    static constexpr int DEFAULT_PPS_MODE = PPS_CAPTUREASSERT; // 默认PPS模式
    static constexpr double EXPECTED_INTERVAL_NS = 1000000000.0; // 期望间隔(1秒)
    static constexpr double MAX_JITTER_NS = 1000.0;             // 最大允许抖动
    static constexpr int MAX_CONSECUTIVE_ERRORS = 5;            // 最大连续错误数
};

} // namespace hal
} // namespace timing_server// 基本实现方法

LinuxPpsInput::LinuxPpsInput(const std::string& device_path)
    : device_path_(device_path)
    , pps_fd_(-1)
    , device_initialized_(false)
    , last_timestamp_ns_(0)
    , pps_event_count_(0)
    , missed_pulses_(0)
    , error_count_(0)
    , average_interval_ns_(EXPECTED_INTERVAL_NS)
    , interval_jitter_ns_(0.0)
{
    // 初始化PPS参数
    memset(&pps_params_, 0, sizeof(pps_params_));
    pps_mode_ = DEFAULT_PPS_MODE;
}

LinuxPpsInput::~LinuxPpsInput() {
    Close();
}

bool LinuxPpsInput::Initialize() {
    // 打开PPS设备
    pps_fd_ = open(device_path_.c_str(), O_RDWR);
    if (pps_fd_ < 0) {
        std::cerr << "无法打开PPS设备: " << device_path_ << " - " << strerror(errno) << std::endl;
        return false;
    }
    
    // 查询PPS设备能力
    if (!QueryPpsCapabilities()) {
        close(pps_fd_);
        pps_fd_ = -1;
        return false;
    }
    
    // 配置PPS设备
    if (!ConfigurePpsDevice()) {
        close(pps_fd_);
        pps_fd_ = -1;
        return false;
    }
    
    device_initialized_ = true;
    std::cout << "PPS输入设备初始化成功: " << device_path_ << std::endl;
    return true;
}

bool LinuxPpsInput::WaitForPpsEdge(int timeout_ms) {
    if (!device_initialized_) {
        return false;
    }
    
#ifdef __linux__
    struct pps_fdata pps_data;
    
    // 设置超时
    struct timespec timeout;
    if (timeout_ms > 0) {
        timeout.tv_sec = timeout_ms / 1000;
        timeout.tv_nsec = (timeout_ms % 1000) * 1000000;
    }
    
    // 等待PPS事件
    if (CapturePpsTimestamp(&pps_data)) {
        // 更新时间戳
        {
            std::lock_guard<std::mutex> lock(timestamp_mutex_);
            last_timestamp_ns_ = ConvertPpsTimestamp(pps_data.info.assert_tu);
            last_pps_time_ = std::chrono::steady_clock::now();
        }
        
        pps_event_count_++;
        UpdateStatistics();
        return true;
    }
#endif
    
    return false;
}

uint64_t LinuxPpsInput::GetLastPpsTimestamp() {
    std::lock_guard<std::mutex> lock(timestamp_mutex_);
    return last_timestamp_ns_;
}

void LinuxPpsInput::Close() {
    if (pps_fd_ >= 0) {
        close(pps_fd_);
        pps_fd_ = -1;
    }
    
    device_initialized_ = false;
    std::cout << "PPS输入设备已关闭" << std::endl;
}

bool LinuxPpsInput::ConfigurePpsDevice() {
#ifdef __linux__
    // 设置PPS参数
    pps_params_.mode = pps_mode_;
    
    if (ioctl(pps_fd_, PPS_SETPARAMS, &pps_params_) < 0) {
        std::cerr << "设置PPS参数失败: " << strerror(errno) << std::endl;
        return false;
    }
    
    return true;
#else
    return false;
#endif
}

bool LinuxPpsInput::QueryPpsCapabilities() {
#ifdef __linux__
    int mode;
    if (ioctl(pps_fd_, PPS_GETCAP, &mode) < 0) {
        std::cerr << "查询PPS能力失败: " << strerror(errno) << std::endl;
        return false;
    }
    
    std::cout << "PPS设备能力: 0x" << std::hex << mode << std::dec << std::endl;
    
    // 检查是否支持所需的捕获模式
    if (!(mode & PPS_CAPTUREASSERT)) {
        std::cerr << "PPS设备不支持断言捕获模式" << std::endl;
        return false;
    }
    
    return true;
#else
    return false;
#endif
}

bool LinuxPpsInput::CapturePpsTimestamp(struct pps_fdata* pps_info) {
#ifdef __linux__
    if (ioctl(pps_fd_, PPS_FETCH, pps_info) < 0) {
        if (errno != EINTR) {
            error_count_++;
        }
        return false;
    }
    
    return true;
#else
    return false;
#endif
}

uint64_t LinuxPpsInput::ConvertPpsTimestamp(const struct pps_ktime& pps_time) {
    return static_cast<uint64_t>(pps_time.sec) * 1000000000ULL + 
           static_cast<uint64_t>(pps_time.nsec);
}

bool LinuxPpsInput::ValidatePpsSignalQuality() {
    // 检查间隔抖动是否在可接受范围内
    return interval_jitter_ns_ < MAX_JITTER_NS;
}

void LinuxPpsInput::UpdateStatistics() {
    // 计算间隔统计
    auto now = std::chrono::steady_clock::now();
    if (pps_event_count_ > 1) {
        auto interval_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            now - last_pps_time_).count();
        
        interval_history_.push_back(interval_ns);
        if (interval_history_.size() > MAX_HISTORY_SIZE) {
            interval_history_.erase(interval_history_.begin());
        }
        
        // 计算平均间隔和抖动
        if (interval_history_.size() > 1) {
            double sum = 0;
            for (auto interval : interval_history_) {
                sum += interval;
            }
            average_interval_ns_ = sum / interval_history_.size();
            
            // 计算抖动(标准差)
            double variance = 0;
            for (auto interval : interval_history_) {
                double diff = interval - average_interval_ns_;
                variance += diff * diff;
            }
            interval_jitter_ns_ = std::sqrt(variance / interval_history_.size());
        }
    }
}

} // namespace hal
} // namespace timing_server