#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>
#include <thread>
#include <vector>
#include <cmath>
#include <algorithm>

// Linux GPIO和计数器接口
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <errno.h>
#include <cstring>
#include <poll.h>

namespace timing_server {
namespace hal {

/**
 * @brief Linux频率输入实现
 * 测量外部10MHz频率基准信号，用于高精度频率校准
 * 支持GPIO边沿计数和专用频率计数器硬件
 */
class LinuxFrequencyInput : public I_FrequencyInput {
public:
    /**
     * @brief 频率测量方法枚举
     */
    enum class MeasurementMethod {
        GPIO_EDGE_COUNTING,     // GPIO边沿计数法
        HARDWARE_COUNTER,       // 硬件计数器法
        TIMER_CAPTURE          // 定时器捕获法
    };
    
    /**
     * @brief 构造Linux频率输入
     * @param method 测量方法
     * @param device_path 设备路径 (GPIO: /sys/class/gpio/gpioN, 计数器: /dev/counterN)
     * @param gpio_pin GPIO引脚号 (GPIO方法使用)
     */
    explicit LinuxFrequencyInput(MeasurementMethod method = MeasurementMethod::GPIO_EDGE_COUNTING,
                                const std::string& device_path = "/sys/class/gpio/gpio18",
                                int gpio_pin = 18);
    
    /**
     * @brief 析构函数
     * 自动停止测量和清理资源
     */
    ~LinuxFrequencyInput() override;
    
    /**
     * @brief 初始化频率输入
     * 配置GPIO或硬件计数器，启动测量线程
     * @return 初始化是否成功
     */
    bool Initialize() override;
    
    /**
     * @brief 测量频率
     * 返回最近测量的频率值，基于配置的测量窗口
     * @return 测量到的频率值（Hz）
     */
    double MeasureFrequency() override;
    
    /**
     * @brief 检查信号是否存在
     * 检测输入信号是否存在且在合理范围内
     * @return 信号存在性
     */
    bool IsSignalPresent() override;
    
    /**
     * @brief 关闭频率输入
     * 停止测量线程，释放GPIO资源，清理所有资源
     */
    void Close() override;

private:
    /**
     * @brief 初始化GPIO边沿计数
     * 配置GPIO引脚为输入模式，设置边沿触发
     * @return 初始化是否成功
     */
    bool InitializeGpioEdgeCounting();
    
    /**
     * @brief 初始化硬件计数器
     * 配置专用硬件计数器设备
     * @return 初始化是否成功
     */
    bool InitializeHardwareCounter();
    
    /**
     * @brief 配置GPIO引脚
     * 导出GPIO引脚，设置方向和边沿触发模式
     * @return 配置是否成功
     */
    bool ConfigureGpioPin();
    
    /**
     * @brief 清理GPIO配置
     * 取消导出GPIO引脚，清理sysfs资源
     */
    void CleanupGpioPin();
    
    /**
     * @brief GPIO边沿计数线程
     * 监听GPIO边沿事件，统计脉冲数量
     */
    void GpioCountingThread();
    
    /**
     * @brief 硬件计数器读取线程
     * 定期读取硬件计数器值
     */
    void HardwareCounterThread();
    
    /**
     * @brief 计算频率
     * 基于脉冲计数和时间窗口计算频率
     * @param pulse_count 脉冲计数
     * @param time_window_ns 时间窗口（纳秒）
     * @return 计算的频率（Hz）
     */
    double CalculateFrequency(uint64_t pulse_count, uint64_t time_window_ns);
    
    /**
     * @brief 更新频率统计
     * 更新频率测量的统计信息，包括平均值、抖动等
     * @param new_frequency 新的频率测量值
     */
    void UpdateFrequencyStatistics(double new_frequency);
    
    /**
     * @brief 验证频率范围
     * 检查测量的频率是否在合理范围内
     * @param frequency 频率值
     * @return 频率是否有效
     */
    bool ValidateFrequencyRange(double frequency);
    
    /**
     * @brief 滤波频率测量
     * 对频率测量值进行滤波处理，减少噪声影响
     * @param raw_frequency 原始频率值
     * @return 滤波后的频率值
     */
    double FilterFrequencyMeasurement(double raw_frequency);
    
    /**
     * @brief 检测信号丢失
     * 检测输入信号是否丢失或异常
     * @return 是否检测到信号丢失
     */
    bool DetectSignalLoss();
    
    // 设备配置
    MeasurementMethod method_;          // 测量方法
    std::string device_path_;           // 设备路径
    int gpio_pin_;                      // GPIO引脚号
    int device_fd_;                     // 设备文件描述符
    
    // GPIO配置
    std::string gpio_export_path_;      // GPIO导出路径
    std::string gpio_direction_path_;   // GPIO方向路径
    std::string gpio_edge_path_;        // GPIO边沿路径
    std::string gpio_value_path_;       // GPIO值路径
    bool gpio_exported_;                // GPIO是否已导出
    
    // 测量线程控制
    std::atomic<bool> measuring_;       // 测量状态
    std::unique_ptr<std::thread> measurement_thread_; // 测量线程
    
    // 频率测量数据
    std::mutex frequency_mutex_;        // 频率数据互斥锁
    double current_frequency_;          // 当前频率值
    std::chrono::steady_clock::time_point last_measurement_time_; // 最后测量时间
    
    // 脉冲计数
    std::atomic<uint64_t> pulse_count_; // 脉冲计数
    std::chrono::steady_clock::time_point count_start_time_; // 计数开始时间
    
    // 统计信息
    std::vector<double> frequency_history_; // 频率历史记录
    static const size_t MAX_HISTORY_SIZE = 60; // 最大历史记录数量
    double average_frequency_;          // 平均频率
    double frequency_jitter_;           // 频率抖动
    double min_frequency_;              // 最小频率
    double max_frequency_;              // 最大频率
    
    // 信号检测
    std::atomic<bool> signal_present_;  // 信号存在标志
    std::chrono::steady_clock::time_point last_pulse_time_; // 最后脉冲时间
    
    // 滤波器参数
    double filter_alpha_;               // 低通滤波器系数
    double filtered_frequency_;         // 滤波后频率
    
    // 配置常量
    static constexpr double EXPECTED_FREQUENCY_HZ = 10000000.0;     // 期望频率(10MHz)
    static constexpr double FREQUENCY_TOLERANCE_PERCENT = 0.01;     // 频率容差(1%)
    static constexpr int MEASUREMENT_WINDOW_MS = 1000;              // 测量窗口(1秒)
    static constexpr int SIGNAL_TIMEOUT_MS = 5000;                  // 信号超时(5秒)
    static constexpr double FILTER_ALPHA_DEFAULT = 0.1;             // 默认滤波系数
    static constexpr int MAX_CONSECUTIVE_ERRORS = 5;                // 最大连续错误数
    
    // 错误统计
    std::atomic<uint64_t> measurement_count_;   // 测量次数
    std::atomic<uint64_t> error_count_;         // 错误次数
    std::atomic<uint64_t> timeout_count_;       // 超时次数
};

} // namespace hal
} // namespace timing_server// 基
本实现方法
LinuxFrequencyInput::LinuxFrequencyInput(MeasurementMethod method,
                                         const std::string& device_path,
                                         int gpio_pin)
    : method_(method)
    , device_path_(device_path)
    , gpio_pin_(gpio_pin)
    , device_fd_(-1)
    , gpio_exported_(false)
    , measuring_(false)
    , current_frequency_(EXPECTED_FREQUENCY_HZ)
    , pulse_count_(0)
    , signal_present_(false)
    , average_frequency_(EXPECTED_FREQUENCY_HZ)
    , frequency_jitter_(0.0)
    , min_frequency_(EXPECTED_FREQUENCY_HZ)
    , max_frequency_(EXPECTED_FREQUENCY_HZ)
    , filter_alpha_(FILTER_ALPHA_DEFAULT)
    , filtered_frequency_(EXPECTED_FREQUENCY_HZ)
    , measurement_count_(0)
    , error_count_(0)
    , timeout_count_(0)
{
    // 构建GPIO路径
    gpio_export_path_ = "/sys/class/gpio/export";
    gpio_direction_path_ = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/direction";
    gpio_edge_path_ = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/edge";
    gpio_value_path_ = "/sys/class/gpio/gpio" + std::to_string(gpio_pin_) + "/value";
}

LinuxFrequencyInput::~LinuxFrequencyInput() {
    Close();
}

bool LinuxFrequencyInput::Initialize() {
    bool success = false;
    
    switch (method_) {
        case MeasurementMethod::GPIO_EDGE_COUNTING:
            success = InitializeGpioEdgeCounting();
            break;
        case MeasurementMethod::HARDWARE_COUNTER:
            success = InitializeHardwareCounter();
            break;
        case MeasurementMethod::TIMER_CAPTURE:
            // 定时器捕获方法暂未实现
            std::cerr << "定时器捕获方法暂未实现" << std::endl;
            return false;
    }
    
    if (success) {
        // 启动测量线程
        measuring_ = true;
        if (method_ == MeasurementMethod::GPIO_EDGE_COUNTING) {
            measurement_thread_ = std::make_unique<std::thread>(&LinuxFrequencyInput::GpioCountingThread, this);
        } else {
            measurement_thread_ = std::make_unique<std::thread>(&LinuxFrequencyInput::HardwareCounterThread, this);
        }
        
        std::cout << "频率输入设备初始化成功" << std::endl;
    }
    
    return success;
}

double LinuxFrequencyInput::MeasureFrequency() {
    std::lock_guard<std::mutex> lock(frequency_mutex_);
    return filtered_frequency_;
}

bool LinuxFrequencyInput::IsSignalPresent() {
    // 检查信号超时
    auto now = std::chrono::steady_clock::now();
    auto time_since_last_pulse = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - last_pulse_time_).count();
    
    return signal_present_ && (time_since_last_pulse < SIGNAL_TIMEOUT_MS);
}

void LinuxFrequencyInput::Close() {
    if (measuring_) {
        measuring_ = false;
        if (measurement_thread_ && measurement_thread_->joinable()) {
            measurement_thread_->join();
        }
    }
    
    if (method_ == MeasurementMethod::GPIO_EDGE_COUNTING) {
        CleanupGpioPin();
    }
    
    if (device_fd_ >= 0) {
        close(device_fd_);
        device_fd_ = -1;
    }
    
    std::cout << "频率输入设备已关闭" << std::endl;
}

bool LinuxFrequencyInput::InitializeGpioEdgeCounting() {
    // 配置GPIO引脚
    if (!ConfigureGpioPin()) {
        return false;
    }
    
    // 打开GPIO值文件
    device_fd_ = open(gpio_value_path_.c_str(), O_RDONLY);
    if (device_fd_ < 0) {
        std::cerr << "无法打开GPIO值文件: " << gpio_value_path_ << " - " << strerror(errno) << std::endl;
        CleanupGpioPin();
        return false;
    }
    
    return true;
}

bool LinuxFrequencyInput::InitializeHardwareCounter() {
    // 打开硬件计数器设备
    device_fd_ = open(device_path_.c_str(), O_RDWR);
    if (device_fd_ < 0) {
        std::cerr << "无法打开硬件计数器: " << device_path_ << " - " << strerror(errno) << std::endl;
        return false;
    }
    
    return true;
}

bool LinuxFrequencyInput::ConfigureGpioPin() {
    // 导出GPIO引脚
    int export_fd = open(gpio_export_path_.c_str(), O_WRONLY);
    if (export_fd < 0) {
        std::cerr << "无法打开GPIO导出文件: " << strerror(errno) << std::endl;
        return false;
    }
    
    std::string pin_str = std::to_string(gpio_pin_);
    if (write(export_fd, pin_str.c_str(), pin_str.length()) < 0) {
        if (errno != EBUSY) { // EBUSY表示已经导出
            std::cerr << "导出GPIO引脚失败: " << strerror(errno) << std::endl;
            close(export_fd);
            return false;
        }
    }
    close(export_fd);
    gpio_exported_ = true;
    
    // 等待sysfs文件创建
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 设置为输入模式
    int direction_fd = open(gpio_direction_path_.c_str(), O_WRONLY);
    if (direction_fd < 0) {
        std::cerr << "无法打开GPIO方向文件: " << strerror(errno) << std::endl;
        return false;
    }
    
    if (write(direction_fd, "in", 2) < 0) {
        std::cerr << "设置GPIO方向失败: " << strerror(errno) << std::endl;
        close(direction_fd);
        return false;
    }
    close(direction_fd);
    
    // 设置边沿触发
    int edge_fd = open(gpio_edge_path_.c_str(), O_WRONLY);
    if (edge_fd < 0) {
        std::cerr << "无法打开GPIO边沿文件: " << strerror(errno) << std::endl;
        return false;
    }
    
    if (write(edge_fd, "rising", 6) < 0) {
        std::cerr << "设置GPIO边沿触发失败: " << strerror(errno) << std::endl;
        close(edge_fd);
        return false;
    }
    close(edge_fd);
    
    return true;
}

void LinuxFrequencyInput::CleanupGpioPin() {
    if (gpio_exported_) {
        int unexport_fd = open("/sys/class/gpio/unexport", O_WRONLY);
        if (unexport_fd >= 0) {
            std::string pin_str = std::to_string(gpio_pin_);
            write(unexport_fd, pin_str.c_str(), pin_str.length());
            close(unexport_fd);
        }
        gpio_exported_ = false;
    }
}

void LinuxFrequencyInput::GpioCountingThread() {
    struct pollfd pfd;
    pfd.fd = device_fd_;
    pfd.events = POLLPRI | POLLERR;
    
    auto start_time = std::chrono::steady_clock::now();
    uint64_t pulse_count = 0;
    
    while (measuring_) {
        int ret = poll(&pfd, 1, MEASUREMENT_WINDOW_MS);
        if (ret > 0) {
            if (pfd.revents & POLLPRI) {
                // 检测到边沿
                pulse_count++;
                last_pulse_time_ = std::chrono::steady_clock::now();
                signal_present_ = true;
                
                // 清除缓冲区
                char dummy;
                lseek(device_fd_, 0, SEEK_SET);
                read(device_fd_, &dummy, 1);
            }
        } else if (ret == 0) {
            // 超时，计算频率
            auto end_time = std::chrono::steady_clock::now();
            auto duration_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
                end_time - start_time).count();
            
            if (duration_ns > 0) {
                double frequency = CalculateFrequency(pulse_count, duration_ns);
                UpdateFrequencyStatistics(frequency);
            }
            
            // 重置计数
            start_time = end_time;
            pulse_count = 0;
            measurement_count_++;
        } else {
            // 错误
            error_count_++;
            if (errno != EINTR) {
                std::cerr << "GPIO轮询错误: " << strerror(errno) << std::endl;
            }
        }
    }
}

void LinuxFrequencyInput::HardwareCounterThread() {
    // 硬件计数器实现（简化版本）
    while (measuring_) {
        // 这里应该读取硬件计数器值
        // 暂时使用模拟数据
        double simulated_frequency = EXPECTED_FREQUENCY_HZ + (rand() % 1000 - 500);
        UpdateFrequencyStatistics(simulated_frequency);
        
        std::this_thread::sleep_for(std::chrono::milliseconds(MEASUREMENT_WINDOW_MS));
        measurement_count_++;
    }
}

double LinuxFrequencyInput::CalculateFrequency(uint64_t pulse_count, uint64_t time_window_ns) {
    if (time_window_ns == 0) return 0.0;
    
    // 频率 = 脉冲数 / 时间窗口(秒)
    double time_window_s = static_cast<double>(time_window_ns) / 1e9;
    return static_cast<double>(pulse_count) / time_window_s;
}

void LinuxFrequencyInput::UpdateFrequencyStatistics(double new_frequency) {
    if (!ValidateFrequencyRange(new_frequency)) {
        error_count_++;
        return;
    }
    
    // 应用滤波
    double filtered = FilterFrequencyMeasurement(new_frequency);
    
    {
        std::lock_guard<std::mutex> lock(frequency_mutex_);
        current_frequency_ = new_frequency;
        filtered_frequency_ = filtered;
        
        // 更新历史记录
        frequency_history_.push_back(new_frequency);
        if (frequency_history_.size() > MAX_HISTORY_SIZE) {
            frequency_history_.erase(frequency_history_.begin());
        }
        
        // 更新统计信息
        if (!frequency_history_.empty()) {
            double sum = 0;
            min_frequency_ = frequency_history_[0];
            max_frequency_ = frequency_history_[0];
            
            for (double freq : frequency_history_) {
                sum += freq;
                min_frequency_ = std::min(min_frequency_, freq);
                max_frequency_ = std::max(max_frequency_, freq);
            }
            
            average_frequency_ = sum / frequency_history_.size();
            
            // 计算抖动
            double variance = 0;
            for (double freq : frequency_history_) {
                double diff = freq - average_frequency_;
                variance += diff * diff;
            }
            frequency_jitter_ = std::sqrt(variance / frequency_history_.size());
        }
    }
}

bool LinuxFrequencyInput::ValidateFrequencyRange(double frequency) {
    double tolerance = EXPECTED_FREQUENCY_HZ * FREQUENCY_TOLERANCE_PERCENT;
    return (frequency >= EXPECTED_FREQUENCY_HZ - tolerance) && 
           (frequency <= EXPECTED_FREQUENCY_HZ + tolerance);
}

double LinuxFrequencyInput::FilterFrequencyMeasurement(double raw_frequency) {
    // 简单的低通滤波器
    filtered_frequency_ = filter_alpha_ * raw_frequency + (1.0 - filter_alpha_) * filtered_frequency_;
    return filtered_frequency_;
}

} // namespace hal
} // namespace timing_server