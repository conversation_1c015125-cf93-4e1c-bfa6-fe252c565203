#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include <memory>
#include <mutex>
#include <chrono>
#include <string>
#include <vector>

// Linux网络和PHC接口头文件
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <linux/sockios.h>
#include <linux/ethtool.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <cstring>

// PTP硬件时钟接口
#ifdef __linux__
#include <linux/ptp_clock.h>
#endif

namespace timing_server {
namespace hal {

/**
 * @brief Linux网络接口实现
 * 操作网卡PTP硬件时钟(PHC)，支持Intel E810等高端网卡
 * 提供PHC时间读写、PTP配置和时钟同步功能
 */
class LinuxNetworkInterface : public I_NetworkInterface {
public:
    /**
     * @brief 构造Linux网络接口
     * @param interface_name 网络接口名称 (如: eth0, enp1s0f0)
     */
    explicit LinuxNetworkInterface(const std::string& interface_name = "eth0");
    
    /**
     * @brief 析构函数
     * 自动关闭PHC设备和网络套接字
     */
    ~LinuxNetworkInterface() override;
    
    /**
     * @brief 初始化网络接口
     * 打开网络接口，检测PHC设备，验证PTP功能
     * @return 初始化是否成功
     */
    bool Initialize() override;
    
    /**
     * @brief 获取PHC时间
     * 从网卡PTP硬件时钟读取当前时间
     * @return PHC时间戳
     */
    timespec GetPHCTime() override;
    
    /**
     * @brief 设置PHC时间
     * 向网卡PTP硬件时钟写入指定时间
     * @param ts 要设置的时间
     * @return 设置是否成功
     */
    bool SetPHCTime(const timespec& ts) override;
    
    /**
     * @brief 配置PTP参数
     * 设置PTP协议相关参数，如域、优先级等
     * @param config PTP配置参数
     * @return 配置是否成功
     */
    bool ConfigurePTP(const PTPConfig& config) override;
    
    /**
     * @brief 获取PHC状态
     * 读取PHC的当前状态和同步信息
     * @return PHC状态信息
     */
    PHCStatus GetPHCStatus() override;
    
    /**
     * @brief 关闭网络接口
     * 关闭PHC设备和网络套接字，清理所有资源
     */
    void Close() override;

private:
    /**
     * @brief 检测PHC设备
     * 查找与网络接口关联的PHC设备
     * @return 检测是否成功
     */
    bool DetectPhcDevice();
    
    /**
     * @brief 打开PHC设备
     * 打开PTP硬件时钟设备文件
     * @return 打开是否成功
     */
    bool OpenPhcDevice();
    
    /**
     * @brief 查询PHC能力
     * 查询PHC设备支持的功能和精度
     * @return 查询是否成功
     */
    bool QueryPhcCapabilities();
    
    /**
     * @brief 获取网络接口索引
     * 通过接口名称获取网络接口索引
     * @return 接口索引，失败返回-1
     */
    int GetInterfaceIndex();
    
    /**
     * @brief 检查PTP硬件时间戳支持
     * 验证网卡是否支持PTP硬件时间戳
     * @return 是否支持硬件时间戳
     */
    bool CheckPtpHardwareTimestamp();
    
    /**
     * @brief 配置硬件时间戳
     * 启用网卡的PTP硬件时间戳功能
     * @return 配置是否成功
     */
    bool ConfigureHardwareTimestamp();
    
    /**
     * @brief 读取PHC寄存器
     * 直接读取PHC设备的时间寄存器
     * @param ts 输出的时间戳
     * @return 读取是否成功
     */
    bool ReadPhcRegister(timespec* ts);
    
    /**
     * @brief 写入PHC寄存器
     * 直接写入PHC设备的时间寄存器
     * @param ts 要写入的时间戳
     * @return 写入是否成功
     */
    bool WritePhcRegister(const timespec& ts);
    
    /**
     * @brief 调整PHC频率
     * 微调PHC的时钟频率，用于精确同步
     * @param ppb 频率调整值（ppb）
     * @return 调整是否成功
     */
    bool AdjustPhcFrequency(int64_t ppb);
    
    /**
     * @brief 获取PHC频率调整值
     * 读取当前的PHC频率调整设置
     * @return 频率调整值（ppb）
     */
    int64_t GetPhcFrequencyAdjustment();
    
    /**
     * @brief 执行PHC时间步进
     * 对PHC时间进行步进调整
     * @param offset_ns 时间偏移（纳秒）
     * @return 步进是否成功
     */
    bool StepPhcTime(int64_t offset_ns);
    
    /**
     * @brief 检查网卡驱动支持
     * 验证网卡驱动是否支持PTP功能
     * @return 驱动支持情况
     */
    bool CheckDriverSupport();
    
    /**
     * @brief 获取网卡信息
     * 读取网卡的详细信息，如型号、驱动版本等
     */
    void GetNetworkCardInfo();
    
    /**
     * @brief 监控PHC状态
     * 定期监控PHC的工作状态和同步精度
     */
    void MonitorPhcStatus();
    
    /**
     * @brief 更新同步统计
     * 更新PHC同步的统计信息
     */
    void UpdateSyncStatistics();
    
    // 网络接口配置
    std::string interface_name_;        // 网络接口名称
    int interface_index_;               // 网络接口索引
    int socket_fd_;                     // 网络套接字文件描述符
    
    // PHC设备配置
    std::string phc_device_path_;       // PHC设备路径 (如: /dev/ptp0)
    int phc_fd_;                        // PHC设备文件描述符
    int phc_index_;                     // PHC设备索引
    
    // 设备状态
    bool device_initialized_;           // 设备初始化状态
    bool phc_available_;                // PHC是否可用
    bool hardware_timestamp_enabled_;   // 硬件时间戳是否启用
    
    // PHC能力信息
    struct ptp_clock_caps phc_caps_;    // PHC设备能力
    int max_adj_ppb_;                   // 最大频率调整范围
    bool supports_pps_;                 // 是否支持PPS输出
    bool supports_external_ts_;         // 是否支持外部时间戳
    
    // 网卡信息
    std::string driver_name_;           // 驱动名称
    std::string driver_version_;        // 驱动版本
    std::string firmware_version_;      // 固件版本
    std::string bus_info_;              // 总线信息
    
    // PTP配置
    std::mutex config_mutex_;           // 配置互斥锁
    PTPConfig current_config_;          // 当前PTP配置
    bool ptp_configured_;               // PTP是否已配置
    
    // 同步状态
    std::mutex status_mutex_;           // 状态互斥锁
    PHCStatus current_status_;          // 当前PHC状态
    timespec last_sync_time_;           // 最后同步时间
    double sync_offset_ns_;             // 同步偏移量
    
    // 统计信息
    std::atomic<uint64_t> read_count_;  // 读取计数
    std::atomic<uint64_t> write_count_; // 写入计数
    std::atomic<uint64_t> error_count_; // 错误计数
    std::atomic<uint64_t> sync_count_;  // 同步计数
    
    // 性能监控
    std::vector<double> offset_history_; // 偏移历史记录
    static const size_t MAX_OFFSET_HISTORY = 100; // 最大偏移历史数量
    double average_offset_ns_;          // 平均偏移量
    double offset_jitter_ns_;           // 偏移抖动
    
    // 配置常量
    static constexpr int MAX_PHC_DEVICES = 16;          // 最大PHC设备数量
    static constexpr int64_t MAX_FREQUENCY_ADJ_PPB = 1000000000; // 最大频率调整范围
    static constexpr double MAX_ACCEPTABLE_OFFSET_NS = 1000.0;   // 最大可接受偏移
    static constexpr int SYNC_MONITORING_INTERVAL_MS = 1000;     // 同步监控间隔
    static constexpr int MAX_CONSECUTIVE_ERRORS = 10;            // 最大连续错误数
    
    // Intel E810特定支持
    bool is_intel_e810_;                // 是否为Intel E810网卡
    bool dpll_supported_;               // 是否支持DPLL
    int dpll_pin_index_;                // DPLL引脚索引
    
    // 错误处理
    int consecutive_errors_;            // 连续错误计数
    std::chrono::steady_clock::time_point last_error_time_; // 最后错误时间
};

} // namespace hal
} // namespace timing_server// 基本
实现方法
LinuxNetworkInterface::LinuxNetworkInterface(const std::string& interface_name)
    : interface_name_(interface_name)
    , interface_index_(-1)
    , socket_fd_(-1)
    , phc_fd_(-1)
    , phc_index_(-1)
    , device_initialized_(false)
    , phc_available_(false)
    , hardware_timestamp_enabled_(false)
    , max_adj_ppb_(0)
    , supports_pps_(false)
    , supports_external_ts_(false)
    , ptp_configured_(false)
    , sync_offset_ns_(0.0)
    , read_count_(0)
    , write_count_(0)
    , error_count_(0)
    , sync_count_(0)
    , average_offset_ns_(0.0)
    , offset_jitter_ns_(0.0)
    , is_intel_e810_(false)
    , dpll_supported_(false)
    , dpll_pin_index_(-1)
    , consecutive_errors_(0)
{
    // 初始化PHC能力结构
    memset(&phc_caps_, 0, sizeof(phc_caps_));
    
    // 初始化PTP配置
    current_config_.domain = 0;
    current_config_.priority1 = 128;
    current_config_.priority2 = 128;
    current_config_.clock_class = 248;
    current_config_.clock_accuracy = 0xFE;
    current_config_.interface = interface_name_;
    
    // 初始化PHC状态
    current_status_.is_synchronized = false;
    current_status_.offset_ns = 0.0;
    current_status_.clock_class = 248;
    current_status_.interface_name = interface_name_;
    
    // 初始化同步时间
    last_sync_time_.tv_sec = 0;
    last_sync_time_.tv_nsec = 0;
}

LinuxNetworkInterface::~LinuxNetworkInterface() {
    Close();
}

bool LinuxNetworkInterface::Initialize() {
    // 创建网络套接字
    socket_fd_ = socket(AF_INET, SOCK_DGRAM, 0);
    if (socket_fd_ < 0) {
        std::cerr << "创建网络套接字失败: " << strerror(errno) << std::endl;
        return false;
    }
    
    // 获取网络接口索引
    interface_index_ = GetInterfaceIndex();
    if (interface_index_ < 0) {
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }
    
    // 检测PHC设备
    if (!DetectPhcDevice()) {
        std::cerr << "未检测到PHC设备" << std::endl;
        // 继续初始化，但标记PHC不可用
    }
    
    // 获取网卡信息
    GetNetworkCardInfo();
    
    // 检查PTP硬件时间戳支持
    if (phc_available_) {
        hardware_timestamp_enabled_ = CheckPtpHardwareTimestamp();
        if (hardware_timestamp_enabled_) {
            ConfigureHardwareTimestamp();
        }
    }
    
    device_initialized_ = true;
    std::cout << "网络接口初始化成功: " << interface_name_ << std::endl;
    
    return true;
}

timespec LinuxNetworkInterface::GetPHCTime() {
    timespec ts = {0, 0};
    
    if (!phc_available_ || phc_fd_ < 0) {
        return ts;
    }
    
    if (ReadPhcRegister(&ts)) {
        read_count_++;
        consecutive_errors_ = 0;
    } else {
        error_count_++;
        consecutive_errors_++;
    }
    
    return ts;
}

bool LinuxNetworkInterface::SetPHCTime(const timespec& ts) {
    if (!phc_available_ || phc_fd_ < 0) {
        return false;
    }
    
    if (WritePhcRegister(ts)) {
        write_count_++;
        consecutive_errors_ = 0;
        
        {
            std::lock_guard<std::mutex> lock(status_mutex_);
            last_sync_time_ = ts;
            current_status_.is_synchronized = true;
        }
        
        return true;
    } else {
        error_count_++;
        consecutive_errors_++;
        return false;
    }
}

bool LinuxNetworkInterface::ConfigurePTP(const PTPConfig& config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    // 验证配置参数
    if (config.domain > 255 || config.priority1 > 255 || config.priority2 > 255) {
        std::cerr << "PTP配置参数超出范围" << std::endl;
        return false;
    }
    
    // 保存配置
    current_config_ = config;
    ptp_configured_ = true;
    
    std::cout << "PTP配置已更新: 域=" << config.domain 
              << ", 优先级1=" << config.priority1 
              << ", 时钟等级=" << config.clock_class << std::endl;
    
    return true;
}

PHCStatus LinuxNetworkInterface::GetPHCStatus() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 更新状态信息
    if (phc_available_) {
        current_status_.is_synchronized = (consecutive_errors_ < MAX_CONSECUTIVE_ERRORS);
        current_status_.offset_ns = sync_offset_ns_;
        current_status_.clock_class = current_config_.clock_class;
    } else {
        current_status_.is_synchronized = false;
        current_status_.offset_ns = 0.0;
        current_status_.clock_class = 248; // 默认值
    }
    
    return current_status_;
}

void LinuxNetworkInterface::Close() {
    if (phc_fd_ >= 0) {
        close(phc_fd_);
        phc_fd_ = -1;
    }
    
    if (socket_fd_ >= 0) {
        close(socket_fd_);
        socket_fd_ = -1;
    }
    
    device_initialized_ = false;
    phc_available_ = false;
    
    std::cout << "网络接口已关闭: " << interface_name_ << std::endl;
}

bool LinuxNetworkInterface::DetectPhcDevice() {
    // 尝试查找与网络接口关联的PHC设备
    for (int i = 0; i < MAX_PHC_DEVICES; i++) {
        std::string phc_path = "/dev/ptp" + std::to_string(i);
        
        int fd = open(phc_path.c_str(), O_RDWR);
        if (fd >= 0) {
            // 检查是否与当前网络接口关联
            // 这里简化处理，实际应该通过ethtool或其他方法验证关联性
            phc_device_path_ = phc_path;
            phc_index_ = i;
            close(fd);
            
            // 重新打开用于后续操作
            if (OpenPhcDevice()) {
                phc_available_ = true;
                std::cout << "检测到PHC设备: " << phc_device_path_ << std::endl;
                return true;
            }
        }
    }
    
    return false;
}

bool LinuxNetworkInterface::OpenPhcDevice() {
    phc_fd_ = open(phc_device_path_.c_str(), O_RDWR);
    if (phc_fd_ < 0) {
        std::cerr << "无法打开PHC设备: " << phc_device_path_ << " - " << strerror(errno) << std::endl;
        return false;
    }
    
    // 查询PHC能力
    return QueryPhcCapabilities();
}

bool LinuxNetworkInterface::QueryPhcCapabilities() {
#ifdef __linux__
    if (ioctl(phc_fd_, PTP_CLOCK_GETCAPS, &phc_caps_) < 0) {
        std::cerr << "查询PHC能力失败: " << strerror(errno) << std::endl;
        return false;
    }
    
    max_adj_ppb_ = phc_caps_.max_adj;
    supports_pps_ = (phc_caps_.pps > 0);
    supports_external_ts_ = (phc_caps_.n_ext_ts > 0);
    
    std::cout << "PHC设备能力: 最大调整=" << max_adj_ppb_ << "ppb, "
              << "PPS支持=" << supports_pps_ << ", "
              << "外部时间戳=" << supports_external_ts_ << std::endl;
    
    return true;
#else
    return false;
#endif
}

int LinuxNetworkInterface::GetInterfaceIndex() {
    struct ifreq ifr;
    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, interface_name_.c_str(), IFNAMSIZ - 1);
    
    if (ioctl(socket_fd_, SIOCGIFINDEX, &ifr) < 0) {
        std::cerr << "获取网络接口索引失败: " << interface_name_ << " - " << strerror(errno) << std::endl;
        return -1;
    }
    
    return ifr.ifr_ifindex;
}

bool LinuxNetworkInterface::CheckPtpHardwareTimestamp() {
    // 检查网卡是否支持PTP硬件时间戳
    // 这里简化处理，实际应该通过ethtool检查
    return phc_available_;
}

bool LinuxNetworkInterface::ConfigureHardwareTimestamp() {
    // 配置硬件时间戳
    // 这里简化处理，实际应该配置网卡的时间戳功能
    std::cout << "硬件时间戳已配置" << std::endl;
    return true;
}

bool LinuxNetworkInterface::ReadPhcRegister(timespec* ts) {
#ifdef __linux__
    if (ioctl(phc_fd_, PTP_SYS_OFFSET, ts) < 0) {
        return false;
    }
    return true;
#else
    return false;
#endif
}

bool LinuxNetworkInterface::WritePhcRegister(const timespec& ts) {
#ifdef __linux__
    if (ioctl(phc_fd_, PTP_SYS_OFFSET, &ts) < 0) {
        return false;
    }
    return true;
#else
    return false;
#endif
}

void LinuxNetworkInterface::GetNetworkCardInfo() {
    // 获取网卡驱动信息
    struct ifreq ifr;
    struct ethtool_drvinfo drvinfo;
    
    memset(&ifr, 0, sizeof(ifr));
    memset(&drvinfo, 0, sizeof(drvinfo));
    
    strncpy(ifr.ifr_name, interface_name_.c_str(), IFNAMSIZ - 1);
    drvinfo.cmd = ETHTOOL_GDRVINFO;
    ifr.ifr_data = reinterpret_cast<char*>(&drvinfo);
    
    if (ioctl(socket_fd_, SIOCETHTOOL, &ifr) == 0) {
        driver_name_ = drvinfo.driver;
        driver_version_ = drvinfo.version;
        firmware_version_ = drvinfo.fw_version;
        bus_info_ = drvinfo.bus_info;
        
        // 检查是否为Intel E810网卡
        is_intel_e810_ = (driver_name_.find("ice") != std::string::npos);
        
        std::cout << "网卡信息: 驱动=" << driver_name_ 
                  << ", 版本=" << driver_version_
                  << ", Intel E810=" << is_intel_e810_ << std::endl;
    }
}

} // namespace hal
} // namespace timing_server