#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <sstream>
#include <algorithm>

// Linux系统头文件
#include <fcntl.h>
#include <unistd.h>
#include <termios.h>
#include <sys/select.h>
#include <sys/time.h>
#include <errno.h>
#include <cstring>

namespace timing_server {
namespace hal {

/**
 * @brief Linux GNSS接收机实现
 * 通过串口设备读取NMEA数据，支持多种厂商的GNSS接收机
 * 包括GPS、北斗、GLONASS、Galileo等多系统接收机
 */
class LinuxGnssReceiver : public I_GnssReceiver {
public:
    /**
     * @brief 构造Linux GNSS接收机
     * @param device_path 串口设备路径 (如: /dev/ttyS0, /dev/ttyUSB0)
     * @param baudrate 波特率 (默认: 9600)
     */
    explicit LinuxGnssReceiver(const std::string& device_path = "/dev/ttyS0", 
                              int baudrate = 9600);
    
    /**
     * @brief 析构函数
     * 自动关闭串口连接和清理资源
     */
    ~LinuxGnssReceiver() override;
    
    /**
     * @brief 初始化GNSS接收机
     * 打开串口设备，配置通信参数，启动数据接收线程
     * @return 初始化是否成功
     */
    bool Initialize() override;
    
    /**
     * @brief 读取NMEA语句
     * 从内部缓冲区读取完整的NMEA语句，支持多种NMEA格式
     * @return NMEA语句字符串，如果没有数据则返回空字符串
     */
    std::string ReadNmeaSentence() override;
    
    /**
     * @brief 检查GNSS信号是否有效
     * 基于最近接收的NMEA数据判断信号质量和定位状态
     * @return 信号有效性
     */
    bool IsSignalValid() override;
    
    /**
     * @brief 获取卫星信息
     * 解析GSV和GGA等NMEA语句，提供卫星数量和信号强度信息
     * @return 卫星信息结构
     */
    SatelliteInfo GetSatelliteInfo() override;
    
    /**
     * @brief 关闭GNSS接收机
     * 停止数据接收线程，关闭串口设备，清理所有资源
     */
    void Close() override;

private:
    /**
     * @brief 配置串口参数
     * 设置波特率、数据位、停止位、奇偶校验等串口通信参数
     * @return 配置是否成功
     */
    bool ConfigureSerialPort();
    
    /**
     * @brief 数据接收线程函数
     * 持续从串口读取数据，解析NMEA语句并更新内部状态
     */
    void ReceiveDataThread();
    
    /**
     * @brief 解析NMEA语句
     * 解析各种类型的NMEA语句(GGA, RMC, GSV等)，更新卫星信息
     * @param nmea_sentence NMEA语句字符串
     */
    void ParseNmeaSentence(const std::string& nmea_sentence);
    
    /**
     * @brief 解析GGA语句
     * 解析GPS定位数据，包含时间、位置、定位质量等信息
     * @param fields NMEA字段数组
     */
    void ParseGGA(const std::vector<std::string>& fields);
    
    /**
     * @brief 解析RMC语句
     * 解析推荐最小定位信息，包含时间、日期、速度等
     * @param fields NMEA字段数组
     */
    void ParseRMC(const std::vector<std::string>& fields);
    
    /**
     * @brief 解析GSV语句
     * 解析卫星可见信息，包含卫星数量、信号强度等
     * @param fields NMEA字段数组
     */
    void ParseGSV(const std::vector<std::string>& fields);
    
    /**
     * @brief 分割NMEA字符串
     * 按逗号分割NMEA语句为字段数组
     * @param nmea_sentence NMEA语句
     * @return 字段数组
     */
    std::vector<std::string> SplitNmeaFields(const std::string& nmea_sentence);
    
    /**
     * @brief 验证NMEA校验和
     * 验证NMEA语句的校验和是否正确
     * @param nmea_sentence NMEA语句
     * @return 校验和是否正确
     */
    bool ValidateNmeaChecksum(const std::string& nmea_sentence);
    
    /**
     * @brief 计算NMEA校验和
     * 计算NMEA语句的XOR校验和
     * @param data 数据部分(不包含$和*)
     * @return 校验和值
     */
    uint8_t CalculateNmeaChecksum(const std::string& data);
    
    // 设备配置
    std::string device_path_;           // 串口设备路径
    int baudrate_;                      // 波特率
    int serial_fd_;                     // 串口文件描述符
    
    // 线程控制
    std::atomic<bool> running_;         // 运行标志
    std::unique_ptr<std::thread> receive_thread_; // 接收线程
    
    // 数据缓冲和同步
    std::mutex data_mutex_;             // 数据互斥锁
    std::vector<std::string> nmea_buffer_; // NMEA语句缓冲区
    static const size_t MAX_BUFFER_SIZE = 100; // 最大缓冲区大小
    
    // GNSS状态信息
    std::mutex status_mutex_;           // 状态互斥锁
    SatelliteInfo satellite_info_;     // 卫星信息
    bool signal_valid_;                 // 信号有效性
    std::chrono::steady_clock::time_point last_valid_data_; // 最后有效数据时间
    
    // NMEA解析状态
    std::string current_sentence_;      // 当前正在构建的NMEA语句
    bool sentence_complete_;            // 语句是否完整
    
    // 统计信息
    uint64_t total_sentences_received_; // 接收的NMEA语句总数
    uint64_t valid_sentences_count_;    // 有效NMEA语句数量
    uint64_t checksum_errors_;          // 校验和错误数量
    
    // 超时配置
    static constexpr int SIGNAL_TIMEOUT_SECONDS = 10; // 信号超时时间(秒)
    static constexpr int READ_TIMEOUT_MS = 1000;       // 读取超时时间(毫秒)
};

} // namespace hal
} // namespace timing_server
// 基本实现方法

LinuxGnssReceiver::LinuxGnssReceiver(const std::string& device_path, int baudrate)
    : device_path_(device_path)
    , baudrate_(baudrate)
    , serial_fd_(-1)
    , running_(false)
    , signal_valid_(false)
{
    // 初始化卫星信息
    satellite_info_.satellite_count = 0;
    satellite_info_.signal_strength_db = -999.0;
    satellite_info_.is_locked = false;
    satellite_info_.fix_type = "无定位";
}

LinuxGnssReceiver::~LinuxGnssReceiver() {
    Close();
}

bool LinuxGnssReceiver::Initialize() {
    // 打开串口设备
    serial_fd_ = open(device_path_.c_str(), O_RDWR | O_NOCTTY | O_NONBLOCK);
    if (serial_fd_ < 0) {
        std::cerr << "无法打开GNSS设备: " << device_path_ << " - " << strerror(errno) << std::endl;
        return false;
    }
    
    // 配置串口参数
    if (!ConfigureSerialPort()) {
        close(serial_fd_);
        serial_fd_ = -1;
        return false;
    }
    
    // 启动接收线程
    running_ = true;
    receive_thread_ = std::make_unique<std::thread>(&LinuxGnssReceiver::ReceiveDataThread, this);
    
    std::cout << "GNSS接收机初始化成功: " << device_path_ << std::endl;
    return true;
}

std::string LinuxGnssReceiver::ReadNmeaSentence() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    if (nmea_buffer_.empty()) {
        return "";
    }
    
    std::string sentence = nmea_buffer_.front();
    nmea_buffer_.erase(nmea_buffer_.begin());
    return sentence;
}

bool LinuxGnssReceiver::IsSignalValid() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 检查信号超时
    auto now = std::chrono::steady_clock::now();
    auto time_since_last_data = std::chrono::duration_cast<std::chrono::seconds>(
        now - last_valid_data_).count();
    
    return signal_valid_ && (time_since_last_data < SIGNAL_TIMEOUT_SECONDS);
}

SatelliteInfo LinuxGnssReceiver::GetSatelliteInfo() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    return satellite_info_;
}

void LinuxGnssReceiver::Close() {
    if (running_) {
        running_ = false;
        if (receive_thread_ && receive_thread_->joinable()) {
            receive_thread_->join();
        }
    }
    
    if (serial_fd_ >= 0) {
        close(serial_fd_);
        serial_fd_ = -1;
    }
    
    std::cout << "GNSS接收机已关闭" << std::endl;
}

bool LinuxGnssReceiver::ConfigureSerialPort() {
    struct termios tty;
    if (tcgetattr(serial_fd_, &tty) != 0) {
        std::cerr << "获取串口属性失败: " << strerror(errno) << std::endl;
        return false;
    }
    
    // 设置波特率
    speed_t speed;
    switch (baudrate_) {
        case 9600: speed = B9600; break;
        case 19200: speed = B19200; break;
        case 38400: speed = B38400; break;
        case 57600: speed = B57600; break;
        case 115200: speed = B115200; break;
        default:
            std::cerr << "不支持的波特率: " << baudrate_ << std::endl;
            return false;
    }
    
    cfsetospeed(&tty, speed);
    cfsetispeed(&tty, speed);
    
    // 配置串口参数: 8N1, 无流控
    tty.c_cflag &= ~PARENB;        // 无奇偶校验
    tty.c_cflag &= ~CSTOPB;        // 1个停止位
    tty.c_cflag &= ~CSIZE;         // 清除数据位设置
    tty.c_cflag |= CS8;            // 8个数据位
    tty.c_cflag &= ~CRTSCTS;       // 无硬件流控
    tty.c_cflag |= CREAD | CLOCAL; // 启用接收，忽略调制解调器控制线
    
    // 配置输入模式
    tty.c_iflag &= ~(IXON | IXOFF | IXANY); // 关闭软件流控
    tty.c_iflag &= ~(ICANON | ECHO | ECHOE | ISIG); // 原始模式
    
    // 配置输出模式
    tty.c_oflag &= ~OPOST; // 原始输出
    
    // 设置超时
    tty.c_cc[VMIN] = 0;    // 非阻塞读取
    tty.c_cc[VTIME] = 10;  // 1秒超时
    
    if (tcsetattr(serial_fd_, TCSANOW, &tty) != 0) {
        std::cerr << "设置串口属性失败: " << strerror(errno) << std::endl;
        return false;
    }
    
    return true;
}

void LinuxGnssReceiver::ReceiveDataThread() {
    char buffer[256];
    std::string current_line;
    
    while (running_) {
        ssize_t bytes_read = read(serial_fd_, buffer, sizeof(buffer) - 1);
        if (bytes_read > 0) {
            buffer[bytes_read] = '\0';
            current_line += buffer;
            
            // 查找完整的NMEA语句
            size_t pos = 0;
            while ((pos = current_line.find('\n', pos)) != std::string::npos) {
                std::string sentence = current_line.substr(0, pos);
                if (!sentence.empty() && sentence.back() == '\r') {
                    sentence.pop_back(); // 移除\r
                }
                
                if (!sentence.empty() && sentence[0] == '$') {
                    // 验证NMEA校验和
                    if (ValidateNmeaChecksum(sentence)) {
                        // 添加到缓冲区
                        {
                            std::lock_guard<std::mutex> lock(data_mutex_);
                            nmea_buffer_.push_back(sentence);
                            if (nmea_buffer_.size() > MAX_BUFFER_SIZE) {
                                nmea_buffer_.erase(nmea_buffer_.begin());
                            }
                        }
                        
                        // 解析NMEA语句
                        ParseNmeaSentence(sentence);
                        total_sentences_received_++;
                        valid_sentences_count_++;
                    } else {
                        checksum_errors_++;
                    }
                }
                
                current_line.erase(0, pos + 1);
                pos = 0;
            }
        } else if (bytes_read < 0 && errno != EAGAIN && errno != EWOULDBLOCK) {
            std::cerr << "GNSS数据读取错误: " << strerror(errno) << std::endl;
            break;
        }
        
        // 短暂休眠避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void LinuxGnssReceiver::ParseNmeaSentence(const std::string& nmea_sentence) {
    auto fields = SplitNmeaFields(nmea_sentence);
    if (fields.empty()) return;
    
    std::string sentence_type = fields[0];
    
    if (sentence_type.find("GGA") != std::string::npos) {
        ParseGGA(fields);
    } else if (sentence_type.find("RMC") != std::string::npos) {
        ParseRMC(fields);
    } else if (sentence_type.find("GSV") != std::string::npos) {
        ParseGSV(fields);
    }
}

void LinuxGnssReceiver::ParseGGA(const std::vector<std::string>& fields) {
    if (fields.size() < 15) return;
    
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 解析定位质量指示符 (字段6)
    if (fields.size() > 6 && !fields[6].empty()) {
        int quality = std::stoi(fields[6]);
        signal_valid_ = (quality > 0);
        
        if (signal_valid_) {
            last_valid_data_ = std::chrono::steady_clock::now();
            
            // 解析卫星数量 (字段7)
            if (fields.size() > 7 && !fields[7].empty()) {
                satellite_info_.satellite_count = std::stoi(fields[7]);
            }
            
            // 设置定位类型
            switch (quality) {
                case 1: satellite_info_.fix_type = "GPS定位"; break;
                case 2: satellite_info_.fix_type = "差分GPS"; break;
                case 3: satellite_info_.fix_type = "PPS定位"; break;
                case 4: satellite_info_.fix_type = "RTK固定解"; break;
                case 5: satellite_info_.fix_type = "RTK浮点解"; break;
                default: satellite_info_.fix_type = "未知定位"; break;
            }
            
            satellite_info_.is_locked = true;
        }
    }
}

void LinuxGnssReceiver::ParseRMC(const std::vector<std::string>& fields) {
    if (fields.size() < 12) return;
    
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 解析状态字段 (字段2): A=有效, V=无效
    if (fields.size() > 2) {
        signal_valid_ = (fields[2] == "A");
        if (signal_valid_) {
            last_valid_data_ = std::chrono::steady_clock::now();
        }
    }
}

void LinuxGnssReceiver::ParseGSV(const std::vector<std::string>& fields) {
    if (fields.size() < 4) return;
    
    // GSV语句包含卫星信息，这里简化处理
    // 实际实现应该解析所有卫星的信号强度信息
}

std::vector<std::string> LinuxGnssReceiver::SplitNmeaFields(const std::string& nmea_sentence) {
    std::vector<std::string> fields;
    std::stringstream ss(nmea_sentence);
    std::string field;
    
    while (std::getline(ss, field, ',')) {
        fields.push_back(field);
    }
    
    return fields;
}

bool LinuxGnssReceiver::ValidateNmeaChecksum(const std::string& nmea_sentence) {
    // 查找校验和分隔符 '*'
    size_t asterisk_pos = nmea_sentence.find('*');
    if (asterisk_pos == std::string::npos || asterisk_pos + 3 != nmea_sentence.length()) {
        return false; // 没有校验和或格式错误
    }
    
    // 提取数据部分 (去掉$和*XX)
    std::string data = nmea_sentence.substr(1, asterisk_pos - 1);
    
    // 计算校验和
    uint8_t calculated_checksum = CalculateNmeaChecksum(data);
    
    // 提取期望的校验和
    std::string checksum_str = nmea_sentence.substr(asterisk_pos + 1);
    uint8_t expected_checksum = static_cast<uint8_t>(std::stoi(checksum_str, nullptr, 16));
    
    return calculated_checksum == expected_checksum;
}

uint8_t LinuxGnssReceiver::CalculateNmeaChecksum(const std::string& data) {
    uint8_t checksum = 0;
    for (char c : data) {
        checksum ^= static_cast<uint8_t>(c);
    }
    return checksum;
}

} // namespace hal
} // namespace timing_server