#include "hal/platform_detector.h"
#include <sys/utsname.h>
#include <cstdlib>
#include <fstream>
#include <sstream>

namespace timing_server {
namespace hal {

PlatformInfo PlatformDetector::DetectPlatform() {
    PlatformInfo info;
    
    // 检测基本系统信息
    info.os_name = DetectOperatingSystem();
    info.architecture = DetectArchitecture();
    info.kernel_version = DetectKernelVersion();
    info.is_development_env = IsDevEnvironment();
    
    // 根据操作系统和架构确定平台类型
    if (info.os_name == "Linux") {
        if (info.architecture == "x86_64") {
            info.type = PlatformType::LINUX_X86_64;
            info.description = "Linux x86_64 生产环境";
        } else if (info.architecture == "loongarch64") {
            info.type = PlatformType::LINUX_LOONGARCH64;
            info.description = "Linux 龙芯LoongArch64 生产环境";
        } else {
            info.type = PlatformType::UNKNOWN;
            info.description = "未知Linux架构: " + info.architecture;
        }
    } else if (info.os_name == "Darwin") {
        if (info.architecture == "x86_64") {
            info.type = PlatformType::MACOS_X86_64;
            info.description = "macOS x86_64 开发环境";
        } else if (info.architecture == "arm64") {
            info.type = PlatformType::MACOS_ARM64;
            info.description = "macOS ARM64 开发环境";
        } else {
            info.type = PlatformType::UNKNOWN;
            info.description = "未知macOS架构: " + info.architecture;
        }
        info.is_development_env = true; // macOS总是被视为开发环境
    } else {
        info.type = PlatformType::UNKNOWN;
        info.description = "不支持的操作系统: " + info.os_name;
    }
    
    return info;
}

std::string PlatformDetector::PlatformTypeToString(PlatformType type) {
    switch (type) {
        case PlatformType::LINUX_X86_64:
            return "linux-x86_64";
        case PlatformType::LINUX_LOONGARCH64:
            return "linux-loongarch64";
        case PlatformType::MACOS_X86_64:
            return "macos-x86_64";
        case PlatformType::MACOS_ARM64:
            return "macos-arm64";
        case PlatformType::UNKNOWN:
        default:
            return "unknown";
    }
}

bool PlatformDetector::IsLinuxPlatform(PlatformType type) {
    return type == PlatformType::LINUX_X86_64 || 
           type == PlatformType::LINUX_LOONGARCH64;
}

bool PlatformDetector::IsMacOSPlatform(PlatformType type) {
    return type == PlatformType::MACOS_X86_64 || 
           type == PlatformType::MACOS_ARM64;
}

bool PlatformDetector::RequiresMockHal(PlatformType type) {
    // macOS平台和开发环境使用Mock HAL
    return IsMacOSPlatform(type);
}

std::string PlatformDetector::DetectOperatingSystem() {
    struct utsname system_info;
    if (uname(&system_info) == 0) {
        return std::string(system_info.sysname);
    }
    return "Unknown";
}

std::string PlatformDetector::DetectArchitecture() {
    struct utsname system_info;
    if (uname(&system_info) == 0) {
        std::string machine = system_info.machine;
        
        // 标准化架构名称
        if (machine == "x86_64" || machine == "amd64") {
            return "x86_64";
        } else if (machine == "loongarch64") {
            return "loongarch64";
        } else if (machine == "arm64" || machine == "aarch64") {
            return "arm64";
        }
        
        return machine;
    }
    return "unknown";
}

std::string PlatformDetector::DetectKernelVersion() {
    struct utsname system_info;
    if (uname(&system_info) == 0) {
        return std::string(system_info.release);
    }
    return "unknown";
}

bool PlatformDetector::IsDevEnvironment() {
    // 检查常见的开发环境标识
    const char* dev_indicators[] = {
        "DEVELOPMENT",
        "DEV_MODE",
        "DEBUG",
        "CMAKE_BUILD_TYPE"
    };
    
    for (const char* indicator : dev_indicators) {
        if (std::getenv(indicator) != nullptr) {
            return true;
        }
    }
    
    // 检查是否存在开发相关的目录或文件
    std::ifstream cmake_cache("CMakeCache.txt");
    if (cmake_cache.good()) {
        return true;
    }
    
    std::ifstream build_dir("build");
    if (build_dir.good()) {
        return true;
    }
    
    return false;
}

} // namespace hal
} // namespace timing_server