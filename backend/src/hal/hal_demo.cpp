/**
 * @file hal_demo.cpp
 * @brief HAL工厂模式演示程序
 * 
 * 这个程序演示了如何使用HAL工厂模式来创建和使用硬件抽象层组件。
 * 它展示了平台检测、工厂创建、设备实例化和基本操作的完整流程。
 */

#include "hal/hal_factory.h"
#include "hal/platform_detector.h"
#include <iostream>
#include <iomanip>
#include <memory>

using namespace timing_server::hal;

/**
 * @brief 演示平台检测功能
 */
void DemonstratePlatformDetection() {
    std::cout << "=== 平台检测演示 ===" << std::endl;
    
    try {
        // 检测当前平台
        auto platform_info = PlatformDetector::DetectPlatform();
        
        std::cout << "平台检测结果：" << std::endl;
        std::cout << "  平台类型: " << PlatformDetector::PlatformTypeToString(platform_info.type) << std::endl;
        std::cout << "  操作系统: " << platform_info.os_name << std::endl;
        std::cout << "  处理器架构: " << platform_info.architecture << std::endl;
        std::cout << "  内核版本: " << platform_info.kernel_version << std::endl;
        std::cout << "  开发环境: " << (platform_info.is_development_env ? "是" : "否") << std::endl;
        std::cout << "  平台描述: " << platform_info.description << std::endl;
        
        // 显示平台特性
        std::cout << "\n平台特性分析：" << std::endl;
        std::cout << "  是否为Linux平台: " << (PlatformDetector::IsLinuxPlatform(platform_info.type) ? "是" : "否") << std::endl;
        std::cout << "  是否为macOS平台: " << (PlatformDetector::IsMacOSPlatform(platform_info.type) ? "是" : "否") << std::endl;
        std::cout << "  需要Mock HAL: " << (PlatformDetector::RequiresMockHal(platform_info.type) ? "是" : "否") << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "平台检测失败: " << e.what() << std::endl;
    }
    
    std::cout << std::endl;
}

/**
 * @brief 演示HAL工厂管理器功能
 */
void DemonstrateHalFactoryManager() {
    std::cout << "=== HAL工厂管理器演示 ===" << std::endl;
    
    try {
        // 获取HAL工厂管理器实例
        auto& manager = HalFactoryManager::GetInstance();
        
        std::cout << "初始化状态: " << (manager.IsInitialized() ? "已初始化" : "未初始化") << std::endl;
        
        // 尝试初始化HAL工厂
        std::cout << "正在初始化HAL工厂..." << std::endl;
        bool init_success = manager.Initialize();
        
        if (init_success) {
            std::cout << "HAL工厂初始化成功！" << std::endl;
            
            // 获取平台信息
            const auto& platform_info = manager.GetPlatformInfo();
            std::cout << "使用的平台: " << platform_info.description << std::endl;
            
            // 尝试获取工厂实例
            try {
                auto factory = manager.GetFactory();
                std::cout << "成功获取HAL工厂实例" << std::endl;
                
                // 验证工厂功能
                std::cout << "正在验证HAL工厂功能..." << std::endl;
                auto validation_result = ValidateHalFactory(factory);
                
                std::cout << "验证结果: " << validation_result.summary << std::endl;
                
                if (!validation_result.errors.empty()) {
                    std::cout << "发现的错误:" << std::endl;
                    for (const auto& error : validation_result.errors) {
                        std::cout << "  - " << error << std::endl;
                    }
                }
                
                if (!validation_result.warnings.empty()) {
                    std::cout << "发现的警告:" << std::endl;
                    for (const auto& warning : validation_result.warnings) {
                        std::cout << "  - " << warning << std::endl;
                    }
                }
                
            } catch (const HalException& e) {
                std::cerr << "获取HAL工厂失败: " << e.what() << std::endl;
                std::cerr << "错误类型: " << HalException::GetErrorTypeDescription(e.GetErrorType()) << std::endl;
                if (!e.GetDetails().empty()) {
                    std::cerr << "详细信息: " << e.GetDetails() << std::endl;
                }
            }
            
        } else {
            std::cerr << "HAL工厂初始化失败" << std::endl;
        }
        
        // 清理资源
        manager.Cleanup();
        std::cout << "HAL工厂资源已清理" << std::endl;
        
    } catch (const HalException& e) {
        std::cerr << "HAL工厂管理器操作失败: " << e.what() << std::endl;
        std::cerr << "错误类型: " << HalException::GetErrorTypeDescription(e.GetErrorType()) << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "未知异常: " << e.what() << std::endl;
    }
    
    std::cout << std::endl;
}

/**
 * @brief 演示HAL异常处理机制
 */
void DemonstrateHalExceptionHandling() {
    std::cout << "=== HAL异常处理演示 ===" << std::endl;
    
    // 演示各种错误类型的异常
    std::vector<std::pair<HalErrorType, std::string>> error_examples = {
        {HalErrorType::PLATFORM_NOT_SUPPORTED, "不支持的平台类型"},
        {HalErrorType::FACTORY_CREATION_FAILED, "工厂创建过程中发生错误"},
        {HalErrorType::DEVICE_INITIALIZATION_FAILED, "设备初始化失败"},
        {HalErrorType::DEVICE_NOT_FOUND, "指定的硬件设备未找到"},
        {HalErrorType::PERMISSION_DENIED, "访问硬件设备权限不足"},
        {HalErrorType::RESOURCE_BUSY, "硬件资源正在被其他进程使用"},
        {HalErrorType::CONFIGURATION_ERROR, "设备配置参数错误"},
        {HalErrorType::UNKNOWN_ERROR, "发生了未知的错误"}
    };
    
    std::cout << "HAL异常类型及其中文描述：" << std::endl;
    for (const auto& example : error_examples) {
        std::cout << "  " << std::setw(30) << std::left 
                  << HalException::GetErrorTypeDescription(example.first) 
                  << " - " << example.second << std::endl;
        
        // 创建并捕获异常示例
        try {
            throw HalException(example.first, example.second, "这是详细错误信息");
        } catch (const HalException& e) {
            // 演示异常信息获取
            // std::cout << "    捕获异常: " << e.what() << std::endl;
            // std::cout << "    错误类型: " << HalException::GetErrorTypeDescription(e.GetErrorType()) << std::endl;
            // std::cout << "    详细信息: " << e.GetDetails() << std::endl;
        }
    }
    
    std::cout << std::endl;
}

/**
 * @brief 演示便捷函数的使用
 */
void DemonstrateConvenienceFunctions() {
    std::cout << "=== 便捷函数演示 ===" << std::endl;
    
    try {
        std::cout << "使用便捷函数创建HAL工厂..." << std::endl;
        
        // 使用便捷函数创建HAL工厂
        auto factory = CreateHalFactory();
        std::cout << "成功通过便捷函数创建HAL工厂" << std::endl;
        
        // 这里可以使用工厂创建各种设备实例
        // 但由于Mock HAL尚未实现，暂时跳过
        
    } catch (const HalException& e) {
        std::cout << "便捷函数调用失败（这是预期的，因为Mock HAL尚未实现）" << std::endl;
        std::cout << "错误信息: " << e.what() << std::endl;
        std::cout << "错误类型: " << HalException::GetErrorTypeDescription(e.GetErrorType()) << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "未知异常: " << e.what() << std::endl;
    }
    
    std::cout << std::endl;
}

/**
 * @brief 主函数 - 运行所有演示
 */
int main() {
    std::cout << "HAL工厂模式和基础架构演示程序" << std::endl;
    std::cout << "======================================" << std::endl;
    std::cout << std::endl;
    
    // 运行各个演示
    DemonstratePlatformDetection();
    DemonstrateHalFactoryManager();
    DemonstrateHalExceptionHandling();
    DemonstrateConvenienceFunctions();
    
    std::cout << "演示程序完成！" << std::endl;
    std::cout << std::endl;
    std::cout << "注意事项：" << std::endl;
    std::cout << "1. 当前Mock HAL实现尚未完成，部分功能会显示错误，这是正常的" << std::endl;
    std::cout << "2. Linux HAL实现将在后续任务中完成" << std::endl;
    std::cout << "3. 完整的设备操作演示将在HAL实现完成后提供" << std::endl;
    
    return 0;
}