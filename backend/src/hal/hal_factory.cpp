#include "hal/hal_factory.h"
#include "hal/mock_hal_factory.h"
#include <sstream>
#include <iostream>

// 前向声明平台特定的工厂类
namespace timing_server {
namespace hal {
    class LinuxHalFactory;
    class MockHalFactory;
}
}

namespace timing_server {
namespace hal {

// HAL异常实现
HalException::HalException(HalErrorType error_type, const std::string& message, 
                          const std::string& details)
    : std::runtime_error(message), error_type_(error_type), details_(details) {
}

std::string HalException::GetErrorTypeDescription(HalErrorType error_type) {
    switch (error_type) {
        case HalErrorType::PLATFORM_NOT_SUPPORTED:
            return "平台不支持";
        case HalErrorType::FACTORY_CREATION_FAILED:
            return "工厂创建失败";
        case HalErrorType::DEVICE_INITIALIZATION_FAILED:
            return "设备初始化失败";
        case HalErrorType::DEVICE_NOT_FOUND:
            return "设备未找到";
        case HalErrorType::PERMISSION_DENIED:
            return "权限不足";
        case HalErrorType::RESOURCE_BUSY:
            return "资源忙碌";
        case HalErrorType::CONFIGURATION_ERROR:
            return "配置错误";
        case HalErrorType::UNKNOWN_ERROR:
        default:
            return "未知错误";
    }
}

// HAL工厂管理器实现
HalFactoryManager& HalFactoryManager::GetInstance() {
    static HalFactoryManager instance;
    return instance;
}

bool HalFactoryManager::Initialize() {
    try {
        // 检测当前平台
        platform_info_ = PlatformDetector::DetectPlatform();
        
        std::cout << "检测到平台: " << platform_info_.description << std::endl;
        std::cout << "操作系统: " << platform_info_.os_name << std::endl;
        std::cout << "处理器架构: " << platform_info_.architecture << std::endl;
        std::cout << "内核版本: " << platform_info_.kernel_version << std::endl;
        std::cout << "开发环境: " << (platform_info_.is_development_env ? "是" : "否") << std::endl;
        
        // 根据平台类型创建相应的HAL工厂
        if (PlatformDetector::RequiresMockHal(platform_info_.type)) {
            std::cout << "使用Mock HAL实现（开发/测试环境）" << std::endl;
            factory_ = CreateMockFactory();
        } else if (PlatformDetector::IsLinuxPlatform(platform_info_.type)) {
            std::cout << "使用Linux HAL实现（生产环境）" << std::endl;
            factory_ = CreateLinuxFactory();
        } else {
            throw HalException(HalErrorType::PLATFORM_NOT_SUPPORTED,
                             "不支持的平台类型: " + PlatformDetector::PlatformTypeToString(platform_info_.type));
        }
        
        if (!factory_) {
            throw HalException(HalErrorType::FACTORY_CREATION_FAILED,
                             "HAL工厂创建失败");
        }
        
        initialized_ = true;
        std::cout << "HAL工厂初始化成功" << std::endl;
        return true;
        
    } catch (const HalException& e) {
        std::cerr << "HAL初始化失败: " << e.what() << std::endl;
        if (!e.GetDetails().empty()) {
            std::cerr << "详细信息: " << e.GetDetails() << std::endl;
        }
        return false;
    } catch (const std::exception& e) {
        std::cerr << "HAL初始化异常: " << e.what() << std::endl;
        return false;
    }
}

std::shared_ptr<I_HalFactory> HalFactoryManager::GetFactory() {
    if (!initialized_) {
        throw HalException(HalErrorType::FACTORY_CREATION_FAILED,
                         "HAL工厂未初始化，请先调用Initialize()");
    }
    
    if (!factory_) {
        throw HalException(HalErrorType::FACTORY_CREATION_FAILED,
                         "HAL工厂实例为空");
    }
    
    return factory_;
}

void HalFactoryManager::Cleanup() {
    if (factory_) {
        std::cout << "清理HAL工厂资源" << std::endl;
        factory_.reset();
    }
    initialized_ = false;
}

std::shared_ptr<I_HalFactory> HalFactoryManager::CreateLinuxFactory() {
    // 注意：这里需要在实现Linux HAL时取消注释
    // return std::make_shared<LinuxHalFactory>();
    
    // 临时实现：如果Linux HAL还未实现，使用Mock HAL
    std::cout << "警告: Linux HAL实现尚未完成，临时使用Mock HAL" << std::endl;
    return CreateMockFactory();
}

std::shared_ptr<I_HalFactory> HalFactoryManager::CreateMockFactory() {
    try {
        // 创建Mock HAL工厂实例
        auto factory = std::make_shared<MockHalFactory>();
        
        // 验证工厂功能
        if (factory) {
            std::cout << "Mock HAL工厂创建成功" << std::endl;
            return factory;
        } else {
            throw HalException(HalErrorType::FACTORY_CREATION_FAILED,
                             "Mock HAL工厂创建失败");
        }
        
    } catch (const std::exception& e) {
        throw HalException(HalErrorType::FACTORY_CREATION_FAILED,
                         "创建Mock HAL工厂时发生异常: " + std::string(e.what()));
    }
}

// 便捷函数实现
std::shared_ptr<I_HalFactory> CreateHalFactory() {
    auto& manager = HalFactoryManager::GetInstance();
    if (!manager.IsInitialized()) {
        if (!manager.Initialize()) {
            throw HalException(HalErrorType::FACTORY_CREATION_FAILED,
                             "HAL工厂初始化失败");
        }
    }
    return manager.GetFactory();
}

// HAL验证函数实现
HalValidationResult ValidateHalFactory(std::shared_ptr<I_HalFactory> factory) {
    HalValidationResult result;
    result.success = true;
    
    if (!factory) {
        result.success = false;
        result.errors.push_back("HAL工厂实例为空");
        result.summary = "验证失败：工厂实例无效";
        return result;
    }
    
    // 测试各种设备创建功能
    std::vector<std::pair<std::string, std::function<void()>>> tests = {
        {"GNSS接收机", [&]() {
            auto gnss = factory->CreateGnssReceiver();
            if (!gnss) {
                result.errors.push_back("无法创建GNSS接收机实例");
                result.success = false;
            }
        }},
        {"PPS输入", [&]() {
            auto pps = factory->CreatePpsInput();
            if (!pps) {
                result.errors.push_back("无法创建PPS输入实例");
                result.success = false;
            }
        }},
        {"原子钟", [&]() {
            auto clock = factory->CreateAtomicClock();
            if (!clock) {
                result.errors.push_back("无法创建原子钟实例");
                result.success = false;
            }
        }},
        {"频率输入", [&]() {
            auto freq = factory->CreateFrequencyInput();
            if (!freq) {
                result.errors.push_back("无法创建频率输入实例");
                result.success = false;
            }
        }},
        {"高精度RTC", [&]() {
            auto rtc = factory->CreateRtc();
            if (!rtc) {
                result.errors.push_back("无法创建RTC实例");
                result.success = false;
            }
        }},
        {"网络接口", [&]() {
            auto net = factory->CreateNetworkInterface();
            if (!net) {
                result.errors.push_back("无法创建网络接口实例");
                result.success = false;
            }
        }}
    };
    
    // 执行所有测试
    for (const auto& test : tests) {
        try {
            test.second();
            std::cout << "✓ " << test.first << " 创建测试通过" << std::endl;
        } catch (const HalException& e) {
            result.errors.push_back(test.first + " 创建失败: " + e.what());
            result.success = false;
            std::cerr << "✗ " << test.first << " 创建测试失败: " << e.what() << std::endl;
        } catch (const std::exception& e) {
            result.errors.push_back(test.first + " 创建异常: " + e.what());
            result.success = false;
            std::cerr << "✗ " << test.first << " 创建测试异常: " << e.what() << std::endl;
        }
    }
    
    // 生成验证摘要
    std::stringstream summary;
    if (result.success) {
        summary << "HAL工厂验证成功：所有 " << tests.size() << " 个设备类型均可正常创建";
    } else {
        summary << "HAL工厂验证失败：" << result.errors.size() << " 个错误";
        if (!result.warnings.empty()) {
            summary << "，" << result.warnings.size() << " 个警告";
        }
    }
    result.summary = summary.str();
    
    return result;
}

} // namespace hal
} // namespace timing_server