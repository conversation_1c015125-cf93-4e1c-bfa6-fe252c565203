#include "core/notification_system.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <ctime>

#ifdef __linux__
#include <syslog.h>
#include <curl/curl.h>
#endif

#ifdef __APPLE__
#include <syslog.h>
#endif

// 解决syslog.h中的宏定义冲突
#ifdef LOG_DEBUG
#undef LOG_DEBUG
#endif
#ifdef LOG_INFO
#undef LOG_INFO
#endif
#ifdef LOG_WARNING
#undef LOG_WARNING
#endif
#ifdef LOG_ERROR
#undef LOG_ERROR
#endif

// 在解决冲突后包含我们的日志头文件
#include "core/logger.h"

namespace timing_server {
namespace core {

// ============================================================================
// LogNotifier Implementation
// ============================================================================

LogNotifier::LogNotifier() {
    LOG_DEBUG(LogComponent::SYSTEM, "日志通知器已创建");
}

bool LogNotifier::SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) {
    try {
        // 构建告警消息
        std::ostringstream oss;
        oss << "[告警] " << AlarmLevelToString(alarm.level) << " - " << alarm.title;
        if (!alarm.description.empty()) {
            oss << ": " << alarm.description;
        }
        
        // 添加上下文信息
        if (!alarm.source_component.empty()) {
            oss << " (组件: " << alarm.source_component << ")";
        }
        
        if (alarm.threshold_value != 0.0 || alarm.current_value != 0.0) {
            oss << " [当前值: " << alarm.current_value;
            if (!alarm.unit.empty()) {
                oss << alarm.unit;
            }
            if (alarm.threshold_value != 0.0) {
                oss << ", 阈值: " << alarm.threshold_value;
                if (!alarm.unit.empty()) {
                    oss << alarm.unit;
                }
            }
            oss << "]";
        }
        
        // 根据告警级别选择日志级别
        switch (alarm.level) {
            case AlarmLevel::INFO:
                LOG_INFO(LogComponent::SYSTEM, oss.str());
                break;
            case AlarmLevel::WARNING:
                LOG_WARNING(LogComponent::SYSTEM, oss.str());
                break;
            case AlarmLevel::ERROR:
                LOG_ERROR(LogComponent::SYSTEM, oss.str());
                break;
            case AlarmLevel::CRITICAL:
                LOG_CRITICAL(LogComponent::SYSTEM, oss.str());
                break;
        }
        
        LOG_DEBUG(LogComponent::SYSTEM, "告警通知已记录到日志: " + alarm.title);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "日志通知发送失败: " + std::string(e.what()));
        return false;
    }
}

NotificationMethod LogNotifier::GetNotificationMethod() const {
    return NotificationMethod::LOG_ONLY;
}

bool LogNotifier::IsAvailable() const {
    return true; // 日志通知器总是可用的
}

} // namespace core
} // namespace timing_server

// ============================================================================
// ConsoleNotifier Implementation
// ============================================================================

namespace timing_server {
namespace core {

ConsoleNotifier::ConsoleNotifier() {
    LOG_DEBUG(LogComponent::SYSTEM, "控制台通知器已创建");
}

bool ConsoleNotifier::SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) {
    try {
        // 获取当前时间
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto tm = *std::localtime(&time_t);
        
        // 构建彩色输出消息
        std::ostringstream oss;
        oss << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] ";
        oss << GetLevelColor(alarm.level);
        oss << "[" << AlarmLevelToString(alarm.level) << "]";
        oss << GetResetColor();
        oss << " " << alarm.title;
        
        if (!alarm.description.empty()) {
            oss << ": " << alarm.description;
        }
        
        // 添加详细信息
        if (!alarm.source_component.empty()) {
            oss << " (组件: " << alarm.source_component << ")";
        }
        
        if (alarm.threshold_value != 0.0 || alarm.current_value != 0.0) {
            oss << "\n  └─ 当前值: " << alarm.current_value;
            if (!alarm.unit.empty()) {
                oss << alarm.unit;
            }
            if (alarm.threshold_value != 0.0) {
                oss << ", 阈值: " << alarm.threshold_value;
                if (!alarm.unit.empty()) {
                    oss << alarm.unit;
                }
            }
        }
        
        // 输出到控制台
        if (alarm.level >= AlarmLevel::ERROR) {
            std::cerr << oss.str() << std::endl;
        } else {
            std::cout << oss.str() << std::endl;
        }
        
        LOG_DEBUG(LogComponent::SYSTEM, "告警通知已输出到控制台: " + alarm.title);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "控制台通知发送失败: " + std::string(e.what()));
        return false;
    }
}

NotificationMethod ConsoleNotifier::GetNotificationMethod() const {
    return NotificationMethod::CONSOLE;
}

bool ConsoleNotifier::IsAvailable() const {
    return true; // 控制台通知器总是可用的
}

std::string ConsoleNotifier::GetLevelColor(AlarmLevel level) const {
    switch (level) {
        case AlarmLevel::INFO:
            return "\033[36m"; // 青色
        case AlarmLevel::WARNING:
            return "\033[33m"; // 黄色
        case AlarmLevel::ERROR:
            return "\033[31m"; // 红色
        case AlarmLevel::CRITICAL:
            return "\033[35m"; // 紫色
        default:
            return "";
    }
}

std::string ConsoleNotifier::GetResetColor() const {
    return "\033[0m"; // 重置颜色
}

} // namespace core
} // namespace timing_server

// ============================================================================
// SyslogNotifier Implementation
// ============================================================================

namespace timing_server {
namespace core {

SyslogNotifier::SyslogNotifier(const std::string& ident) 
    : ident_(ident), syslog_opened_(false) {
#if defined(__linux__) || defined(__APPLE__)
    openlog(ident_.c_str(), LOG_PID | LOG_NDELAY, LOG_DAEMON);
    syslog_opened_ = true;
    LOG_DEBUG(LogComponent::SYSTEM, "系统日志通知器已创建，标识符: " + ident_);
#else
    LOG_WARNING(LogComponent::SYSTEM, "当前平台不支持syslog，系统日志通知器不可用");
#endif
}

SyslogNotifier::~SyslogNotifier() {
#if defined(__linux__) || defined(__APPLE__)
    if (syslog_opened_) {
        closelog();
        syslog_opened_ = false;
    }
#endif
}

bool SyslogNotifier::SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) {
#if defined(__linux__) || defined(__APPLE__)
    if (!syslog_opened_) {
        LOG_ERROR(LogComponent::SYSTEM, "系统日志未打开，无法发送通知");
        return false;
    }
    
    try {
        // 构建syslog消息
        std::ostringstream oss;
        oss << "[告警] " << alarm.title;
        if (!alarm.description.empty()) {
            oss << ": " << alarm.description;
        }
        
        if (!alarm.source_component.empty()) {
            oss << " (组件: " << alarm.source_component << ")";
        }
        
        if (alarm.threshold_value != 0.0 || alarm.current_value != 0.0) {
            oss << " [当前值: " << alarm.current_value;
            if (!alarm.unit.empty()) {
                oss << alarm.unit;
            }
            if (alarm.threshold_value != 0.0) {
                oss << ", 阈值: " << alarm.threshold_value;
                if (!alarm.unit.empty()) {
                    oss << alarm.unit;
                }
            }
            oss << "]";
        }
        
        // 发送到syslog
        int priority = GetSyslogPriority(alarm.level);
        syslog(priority, "%s", oss.str().c_str());
        
        LOG_DEBUG(LogComponent::SYSTEM, "告警通知已发送到系统日志: " + alarm.title);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "系统日志通知发送失败: " + std::string(e.what()));
        return false;
    }
#else
    LOG_ERROR(LogComponent::SYSTEM, "当前平台不支持syslog通知");
    return false;
#endif
}

NotificationMethod SyslogNotifier::GetNotificationMethod() const {
    return NotificationMethod::SYSLOG;
}

bool SyslogNotifier::IsAvailable() const {
#if defined(__linux__) || defined(__APPLE__)
    return syslog_opened_;
#else
    return false;
#endif
}

int SyslogNotifier::GetSyslogPriority(AlarmLevel level) const {
#if defined(__linux__) || defined(__APPLE__)
    switch (level) {
        case AlarmLevel::INFO:
            return 6; // LOG_INFO
        case AlarmLevel::WARNING:
            return 4; // LOG_WARNING
        case AlarmLevel::ERROR:
            return 3; // LOG_ERR
        case AlarmLevel::CRITICAL:
            return 2; // LOG_CRIT
        default:
            return 6; // LOG_INFO
    }
#else
    return 0;
#endif
}

} // namespace core
} // namespace timing_server

// ============================================================================
// NotificationFactory Implementation
// ============================================================================

namespace timing_server {
namespace core {

std::shared_ptr<IAlarmNotifier> NotificationFactory::CreateLogNotifier() {
    return std::make_shared<LogNotifier>();
}

std::shared_ptr<IAlarmNotifier> NotificationFactory::CreateConsoleNotifier() {
    return std::make_shared<ConsoleNotifier>();
}

std::shared_ptr<IAlarmNotifier> NotificationFactory::CreateSyslogNotifier(const std::string& ident) {
    return std::make_shared<SyslogNotifier>(ident);
}

std::shared_ptr<IAlarmNotifier> NotificationFactory::CreateWebhookNotifier(const std::string& webhook_url, 
                                                                          uint32_t timeout_seconds) {
    // 简化实现，返回日志通知器作为替代
    LOG_WARNING(LogComponent::SYSTEM, "Webhook通知器功能尚未完全实现，使用日志通知器替代");
    return CreateLogNotifier();
}

std::shared_ptr<IAlarmNotifier> NotificationFactory::CreateEmailNotifier(const EmailNotifier::SmtpConfig& config) {
    // 简化实现，返回日志通知器作为替代
    LOG_WARNING(LogComponent::SYSTEM, "邮件通知器功能尚未完全实现，使用日志通知器替代");
    return CreateLogNotifier();
}

std::vector<std::shared_ptr<IAlarmNotifier>> NotificationFactory::CreateDefaultNotifiers() {
    std::vector<std::shared_ptr<IAlarmNotifier>> notifiers;
    
    // 添加日志通知器（总是可用）
    notifiers.push_back(CreateLogNotifier());
    
    // 添加控制台通知器（总是可用）
    notifiers.push_back(CreateConsoleNotifier());
    
    // 添加系统日志通知器（在支持的平台上）
    auto syslog_notifier = CreateSyslogNotifier();
    if (syslog_notifier->IsAvailable()) {
        notifiers.push_back(syslog_notifier);
    }
    
    LOG_INFO(LogComponent::SYSTEM, "已创建 " + std::to_string(notifiers.size()) + " 个默认通知器");
    return notifiers;
}
} 
// namespace core
} // namespace timing_server