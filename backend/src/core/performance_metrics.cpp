#include "core/performance_metrics.h"
#include "core/logger.h"
#include "core/timing_engine.h"
#include "core/json_fallback.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <numeric>
#include <limits>
#include <sys/resource.h>
#include <sys/statvfs.h>
#ifdef __linux__
#include <sys/sysinfo.h>
#endif

namespace timing_server {
namespace core {

// PerformanceMetric实现
PerformanceMetric::PerformanceMetric(const MetricDefinition& definition) 
    : definition_(definition) {
    
    // 为直方图类型初始化桶
    if (definition_.type == MetricType::HISTOGRAM && !definition_.histogram_buckets.empty()) {
        std::vector<HistogramBucket> buckets;
        for (double bound : definition_.histogram_buckets) {
            buckets.emplace_back(bound);
        }
        // 添加无穷大桶
        buckets.emplace_back(std::numeric_limits<double>::infinity());
        
        histogram_buckets_[""] = buckets; // 默认标签组合
    }
}

void PerformanceMetric::Set(double value, const std::map<std::string, std::string>& labels) {
    if (definition_.type != MetricType::GAUGE) {
        LOG_WARNING(LogComponent::SYSTEM, "尝试在非仪表类型指标上调用Set: " + definition_.name);
        return;
    }
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::string label_key = GenerateLabelKey(labels);
    gauge_values_[label_key] = value;
    
    // 添加到历史数据
    historical_data_.emplace(value, labels);
    if (historical_data_.size() > MAX_HISTORICAL_SIZE) {
        historical_data_.pop();
    }
}

void PerformanceMetric::Add(double increment, const std::map<std::string, std::string>& labels) {
    if (definition_.type != MetricType::COUNTER) {
        LOG_WARNING(LogComponent::SYSTEM, "尝试在非计数器类型指标上调用Add: " + definition_.name);
        return;
    }
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::string label_key = GenerateLabelKey(labels);
    counter_values_[label_key] += increment;
    
    // 添加到历史数据
    historical_data_.emplace(counter_values_[label_key], labels);
    if (historical_data_.size() > MAX_HISTORICAL_SIZE) {
        historical_data_.pop();
    }
}

void PerformanceMetric::Observe(double value, const std::map<std::string, std::string>& labels) {
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::string label_key = GenerateLabelKey(labels);
    
    if (definition_.type == MetricType::HISTOGRAM) {
        // 更新直方图桶
        auto& buckets = histogram_buckets_[label_key];
        if (buckets.empty()) {
            // 初始化桶
            for (double bound : definition_.histogram_buckets) {
                buckets.emplace_back(bound);
            }
            buckets.emplace_back(std::numeric_limits<double>::infinity());
        }
        
        for (auto& bucket : buckets) {
            if (value <= bucket.upper_bound) {
                bucket.count++;
            }
        }
    } else if (definition_.type == MetricType::SUMMARY) {
        // 添加到摘要值
        summary_values_[label_key].push_back(value);
        
        // 限制摘要值数量
        if (summary_values_[label_key].size() > 1000) {
            summary_values_[label_key].erase(summary_values_[label_key].begin());
        }
    }
    
    // 添加到历史数据
    historical_data_.emplace(value, labels);
    if (historical_data_.size() > MAX_HISTORICAL_SIZE) {
        historical_data_.pop();
    }
}

double PerformanceMetric::GetValue(const std::map<std::string, std::string>& labels) const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::string label_key = GenerateLabelKey(labels);
    
    switch (definition_.type) {
        case MetricType::GAUGE: {
            auto it = gauge_values_.find(label_key);
            return (it != gauge_values_.end()) ? it->second : 0.0;
        }
        case MetricType::COUNTER: {
            auto it = counter_values_.find(label_key);
            return (it != counter_values_.end()) ? it->second : 0.0;
        }
        case MetricType::SUMMARY: {
            auto it = summary_values_.find(label_key);
            if (it != summary_values_.end() && !it->second.empty()) {
                // 返回平均值
                double sum = std::accumulate(it->second.begin(), it->second.end(), 0.0);
                return sum / it->second.size();
            }
            return 0.0;
        }
        case MetricType::HISTOGRAM: {
            auto it = histogram_buckets_.find(label_key);
            if (it != histogram_buckets_.end() && !it->second.empty()) {
                // 返回总计数
                return it->second.back().count;
            }
            return 0.0;
        }
    }
    
    return 0.0;
}

std::vector<MetricDataPoint> PerformanceMetric::GetAllDataPoints() const {
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    std::vector<MetricDataPoint> result;
    std::queue<MetricDataPoint> temp_queue = historical_data_;
    
    while (!temp_queue.empty()) {
        result.push_back(temp_queue.front());
        temp_queue.pop();
    }
    
    return result;
}

std::vector<HistogramBucket> PerformanceMetric::GetHistogramBuckets(const std::map<std::string, std::string>& labels) const {
    if (definition_.type != MetricType::HISTOGRAM) {
        return {};
    }
    
    std::lock_guard<std::mutex> lock(data_mutex_);
    std::string label_key = GenerateLabelKey(labels);
    
    auto it = histogram_buckets_.find(label_key);
    return (it != histogram_buckets_.end()) ? it->second : std::vector<HistogramBucket>();
}

void PerformanceMetric::Reset() {
    std::lock_guard<std::mutex> lock(data_mutex_);
    
    gauge_values_.clear();
    counter_values_.clear();
    histogram_buckets_.clear();
    summary_values_.clear();
    
    // 清空历史数据
    std::queue<MetricDataPoint> empty_queue;
    historical_data_.swap(empty_queue);
}

std::string PerformanceMetric::GenerateLabelKey(const std::map<std::string, std::string>& labels) const {
    if (labels.empty()) {
        return "";
    }
    
    std::ostringstream oss;
    bool first = true;
    for (const auto& [key, value] : labels) {
        if (!first) oss << ",";
        oss << key << "=" << value;
        first = false;
    }
    
    return oss.str();
}

// PerformanceMetricsCollector实现
PerformanceMetricsCollector::PerformanceMetricsCollector() 
    : running_(false), collection_interval_(std::chrono::milliseconds(DEFAULT_COLLECTION_INTERVAL_MS)) {
    
    CreateDefaultMetrics();
    LOG_INFO(LogComponent::SYSTEM, "性能指标收集器已创建");
}

PerformanceMetricsCollector::~PerformanceMetricsCollector() {
    Stop();
    LOG_INFO(LogComponent::SYSTEM, "性能指标收集器已销毁");
}

bool PerformanceMetricsCollector::Start() {
    std::lock_guard<std::mutex> lock(collection_mutex_);
    
    if (running_) {
        LOG_WARNING(LogComponent::SYSTEM, "性能指标收集器已在运行中");
        return true;
    }
    
    running_ = true;
    
    // 启动收集线程
    collection_thread_ = std::thread(&PerformanceMetricsCollector::CollectionThread, this);
    
    LOG_INFO(LogComponent::SYSTEM, "性能指标收集器已启动");
    return true;
}

bool PerformanceMetricsCollector::Stop() {
    std::unique_lock<std::mutex> lock(collection_mutex_);
    
    if (!running_) {
        return true;
    }
    
    running_ = false;
    collection_cv_.notify_all();
    lock.unlock();
    
    // 等待收集线程结束
    if (collection_thread_.joinable()) {
        collection_thread_.join();
    }
    
    LOG_INFO(LogComponent::SYSTEM, "性能指标收集器已停止");
    return true;
}

std::shared_ptr<PerformanceMetric> PerformanceMetricsCollector::RegisterMetric(const MetricDefinition& definition) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    auto it = metrics_.find(definition.name);
    if (it != metrics_.end()) {
        LOG_WARNING(LogComponent::SYSTEM, "指标已存在: " + definition.name);
        return it->second;
    }
    
    auto metric = std::make_shared<PerformanceMetric>(definition);
    metrics_[definition.name] = metric;
    
    LOG_INFO(LogComponent::SYSTEM, "注册性能指标: " + definition.name + 
             " (类型: " + std::to_string(static_cast<int>(definition.type)) + ")");
    
    return metric;
}

std::shared_ptr<PerformanceMetric> PerformanceMetricsCollector::GetMetric(const std::string& name) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    auto it = metrics_.find(name);
    return (it != metrics_.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<PerformanceMetric>> PerformanceMetricsCollector::GetAllMetrics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    std::vector<std::shared_ptr<PerformanceMetric>> result;
    for (const auto& [name, metric] : metrics_) {
        result.push_back(metric);
    }
    
    return result;
}

void PerformanceMetricsCollector::CollectSystemMetrics() {
    try {
        // CPU使用率
        auto cpu_metric = GetMetric("system_cpu_usage_percent");
        if (cpu_metric) {
            // 读取 /proc/stat 计算CPU使用率
            static uint64_t last_total = 0, last_idle = 0;
            
            std::ifstream stat_file("/proc/stat");
            if (stat_file.is_open()) {
                std::string line;
                if (std::getline(stat_file, line) && line.substr(0, 3) == "cpu") {
                    std::istringstream iss(line);
                    std::string cpu_label;
                    uint64_t user, nice, system, idle, iowait, irq, softirq, steal;
                    
                    if (iss >> cpu_label >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal) {
                        uint64_t total = user + nice + system + idle + iowait + irq + softirq + steal;
                        uint64_t idle_time = idle + iowait;
                        
                        if (last_total > 0) {
                            uint64_t total_diff = total - last_total;
                            uint64_t idle_diff = idle_time - last_idle;
                            
                            if (total_diff > 0) {
                                double cpu_usage = 100.0 * (1.0 - static_cast<double>(idle_diff) / total_diff);
                                cpu_metric->Set(cpu_usage);
                            }
                        }
                        
                        last_total = total;
                        last_idle = idle_time;
                    }
                }
            }
        }
        
        // 内存使用率
        auto memory_metric = GetMetric("system_memory_usage_percent");
        if (memory_metric) {
            std::ifstream meminfo_file("/proc/meminfo");
            if (meminfo_file.is_open()) {
                std::string line;
                uint64_t mem_total = 0, mem_available = 0;
                
                while (std::getline(meminfo_file, line)) {
                    if (line.substr(0, 9) == "MemTotal:") {
                        std::istringstream iss(line);
                        std::string label, unit;
                        iss >> label >> mem_total >> unit;
                    } else if (line.substr(0, 13) == "MemAvailable:") {
                        std::istringstream iss(line);
                        std::string label, unit;
                        iss >> label >> mem_available >> unit;
                        break;
                    }
                }
                
                if (mem_total > 0 && mem_available > 0) {
                    double memory_usage = 100.0 * (1.0 - static_cast<double>(mem_available) / mem_total);
                    memory_metric->Set(memory_usage);
                }
            }
        }
        
        // 磁盘使用率
        auto disk_metric = GetMetric("system_disk_usage_percent");
        if (disk_metric) {
            struct statvfs stat;
            if (statvfs("/", &stat) == 0) {
                uint64_t total_space = stat.f_blocks * stat.f_frsize;
                uint64_t free_space = stat.f_bavail * stat.f_frsize;
                
                if (total_space > 0) {
                    double disk_usage = 100.0 * (1.0 - static_cast<double>(free_space) / total_space);
                    disk_metric->Set(disk_usage);
                }
            }
        }
        
        // 进程内存使用量
        auto process_memory_metric = GetMetric("process_memory_usage_mb");
        if (process_memory_metric) {
            struct rusage usage;
            if (getrusage(RUSAGE_SELF, &usage) == 0) {
#ifdef __linux__
                double memory_mb = static_cast<double>(usage.ru_maxrss) / 1024.0; // Linux下是KB
#else
                double memory_mb = static_cast<double>(usage.ru_maxrss) / (1024.0 * 1024.0); // macOS下是字节
#endif
                process_memory_metric->Set(memory_mb);
            }
        }
        
        // 系统负载
        auto load_metric = GetMetric("system_load_average");
        if (load_metric) {
            double loadavg[3];
            if (getloadavg(loadavg, 3) != -1) {
                load_metric->Set(loadavg[0], {{"period", "1m"}});
                load_metric->Set(loadavg[1], {{"period", "5m"}});
                load_metric->Set(loadavg[2], {{"period", "15m"}});
            }
        }
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "收集系统指标失败: " + std::string(e.what()));
    }
}

// 移除了CollectTimingMetrics方法，因为需要ITimingEngine的完整定义

std::string PerformanceMetricsCollector::ExportMetrics(const std::string& format) {
    if (format == "prometheus") {
        return ExportPrometheusFormat();
    } else if (format == "json") {
        return ExportJsonFormat();
    } else if (format == "csv") {
        return ExportCsvFormat();
    } else {
        LOG_WARNING(LogComponent::SYSTEM, "不支持的导出格式: " + format);
        return "";
    }
}

void PerformanceMetricsCollector::SetCollectionInterval(uint32_t interval_ms) {
    collection_interval_ = std::chrono::milliseconds(interval_ms);
    LOG_INFO(LogComponent::SYSTEM, "设置收集间隔: " + std::to_string(interval_ms) + "ms");
}

// 移除了SetTimingEngine方法

void PerformanceMetricsCollector::CollectionThread() {
    LOG_INFO(LogComponent::SYSTEM, "性能指标收集线程已启动");
    
    while (running_) {
        try {
            // 收集系统指标
            CollectSystemMetrics();
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "指标收集线程异常: " + std::string(e.what()));
        }
        
        // 等待下次收集
        std::unique_lock<std::mutex> lock(collection_mutex_);
        collection_cv_.wait_for(lock, collection_interval_, [this] { return !running_; });
    }
    
    LOG_INFO(LogComponent::SYSTEM, "性能指标收集线程已结束");
}

void PerformanceMetricsCollector::CreateDefaultMetrics() {
    // 系统指标
    RegisterMetric(MetricDefinition("system_cpu_usage_percent", "系统CPU使用率", MetricType::GAUGE, "percent"));
    RegisterMetric(MetricDefinition("system_memory_usage_percent", "系统内存使用率", MetricType::GAUGE, "percent"));
    RegisterMetric(MetricDefinition("system_disk_usage_percent", "系统磁盘使用率", MetricType::GAUGE, "percent"));
    RegisterMetric(MetricDefinition("system_load_average", "系统负载平均值", MetricType::GAUGE, ""));
    RegisterMetric(MetricDefinition("process_memory_usage_mb", "进程内存使用量", MetricType::GAUGE, "MB"));
    
    // 授时系统指标
    RegisterMetric(MetricDefinition("timing_system_state", "授时系统状态", MetricType::GAUGE, ""));
    RegisterMetric(MetricDefinition("timing_active_source", "活跃时间源", MetricType::GAUGE, ""));
    RegisterMetric(MetricDefinition("timing_uptime_seconds", "系统运行时间", MetricType::COUNTER, "seconds"));
    
    // 时间精度指标
    MetricDefinition accuracy_def("timing_accuracy_ns", "时间精度", MetricType::HISTOGRAM, "nanoseconds");
    accuracy_def.histogram_buckets = {10.0, 25.0, 50.0, 100.0, 250.0, 500.0, 1000.0, 2500.0, 5000.0};
    RegisterMetric(accuracy_def);
    
    RegisterMetric(MetricDefinition("timing_allan_deviation_1s", "1秒Allan偏差", MetricType::GAUGE, ""));
    RegisterMetric(MetricDefinition("timing_allan_deviation_10s", "10秒Allan偏差", MetricType::GAUGE, ""));
    
    // 时间源指标
    RegisterMetric(MetricDefinition("timing_source_quality", "时间源质量", MetricType::GAUGE, "percent"));
    RegisterMetric(MetricDefinition("timing_source_accuracy_ns", "时间源精度", MetricType::GAUGE, "nanoseconds"));
    
    LOG_INFO(LogComponent::SYSTEM, "已创建默认性能指标");
}

std::string PerformanceMetricsCollector::ExportPrometheusFormat() {
    std::ostringstream oss;
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    for (const auto& [name, metric] : metrics_) {
        const auto& definition = metric->GetDefinition();
        
        // 输出HELP和TYPE注释
        oss << "# HELP " << name << " " << definition.description << "\n";
        oss << "# TYPE " << name << " ";
        
        switch (definition.type) {
            case MetricType::COUNTER:
                oss << "counter\n";
                break;
            case MetricType::GAUGE:
                oss << "gauge\n";
                break;
            case MetricType::HISTOGRAM:
                oss << "histogram\n";
                break;
            case MetricType::SUMMARY:
                oss << "summary\n";
                break;
        }
        
        // 输出指标值
        if (definition.type == MetricType::HISTOGRAM) {
            auto buckets = metric->GetHistogramBuckets();
            for (const auto& bucket : buckets) {
                oss << name << "_bucket{le=\"";
                if (std::isinf(bucket.upper_bound)) {
                    oss << "+Inf";
                } else {
                    oss << bucket.upper_bound;
                }
                oss << "\"} " << bucket.count << "\n";
            }
            oss << name << "_count " << metric->GetValue() << "\n";
        } else {
            oss << name << " " << metric->GetValue() << "\n";
        }
        
        oss << "\n";
    }
    
    return oss.str();
}

std::string PerformanceMetricsCollector::ExportJsonFormat() {
    Json::Value root;
    Json::Value metrics_array(Json::arrayValue);
    
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    for (const auto& [name, metric] : metrics_) {
        Json::Value metric_obj;
        const auto& definition = metric->GetDefinition();
        
        metric_obj["name"] = name;
        metric_obj["description"] = definition.description;
        metric_obj["type"] = static_cast<int>(definition.type);
        metric_obj["unit"] = definition.unit;
        metric_obj["value"] = metric->GetValue();
        metric_obj["timestamp"] = GetCurrentTimestampNs();
        
        if (definition.type == MetricType::HISTOGRAM) {
            Json::Value buckets_array(Json::arrayValue);
            auto buckets = metric->GetHistogramBuckets();
            
            for (const auto& bucket : buckets) {
                Json::Value bucket_obj;
                bucket_obj["upper_bound"] = bucket.upper_bound;
                bucket_obj["count"] = bucket.count;
                buckets_array.append(bucket_obj);
            }
            
            metric_obj["buckets"] = buckets_array;
        }
        
        metrics_array.append(metric_obj);
    }
    
    root["metrics"] = metrics_array;
    root["timestamp"] = GetCurrentTimestampNs();
    root["version"] = "1.0.0";
    
    Json::StreamWriterBuilder builder;
    return Json::writeString(builder, root);
}

std::string PerformanceMetricsCollector::ExportCsvFormat() {
    std::ostringstream oss;
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    // CSV头部
    oss << "timestamp,metric_name,metric_type,value,unit,description\n";
    
    uint64_t timestamp = GetCurrentTimestampNs();
    
    for (const auto& [name, metric] : metrics_) {
        const auto& definition = metric->GetDefinition();
        
        oss << timestamp << ","
            << name << ","
            << static_cast<int>(definition.type) << ","
            << metric->GetValue() << ","
            << definition.unit << ","
            << "\"" << definition.description << "\"\n";
    }
    
    return oss.str();
}

// MetricsManager实现
MetricsManager& MetricsManager::GetInstance() {
    static MetricsManager instance;
    return instance;
}

bool MetricsManager::Initialize() {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (initialized_) {
        LOG_WARNING(LogComponent::SYSTEM, "指标管理器已初始化");
        return true;
    }
    
    collector_ = std::make_shared<PerformanceMetricsCollector>();
    
    if (!collector_->Start()) {
        LOG_ERROR(LogComponent::SYSTEM, "启动性能指标收集器失败");
        return false;
    }
    
    initialized_ = true;
    LOG_INFO(LogComponent::SYSTEM, "指标管理器已初始化");
    return true;
}

void MetricsManager::Shutdown() {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (!initialized_) {
        return;
    }
    
    if (collector_) {
        collector_->Stop();
        collector_.reset();
    }
    
    initialized_ = false;
    LOG_INFO(LogComponent::SYSTEM, "指标管理器已关闭");
}

} // namespace core
} // namespace timing_server