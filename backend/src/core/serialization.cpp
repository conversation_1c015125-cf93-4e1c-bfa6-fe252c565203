#include "core/serialization.h"
#include <sstream>
#include <regex>
#include <cstring>

namespace timing_server {
namespace core {

// JSON序列化实现

std::string JsonSerializer::SerializeTimeQuality(const TimeQuality& quality) {
    std::ostringstream oss;
    oss << "{"
        << "\"accuracy_ns\":" << std::fixed << std::setprecision(3) << quality.accuracy_ns << ","
        << "\"stability_ppm\":" << std::scientific << std::setprecision(6) << quality.stability_ppm << ","
        << "\"confidence\":" << quality.confidence << ","
        << "\"is_traceable\":" << (quality.is_traceable ? "true" : "false") << ","
        << "\"reference\":\"" << EscapeJsonString(quality.reference) << "\""
        << "}";
    return oss.str();
}

TimeQuality JsonSerializer::DeserializeTimeQuality(const std::string& json) {
    TimeQuality quality;
    quality.accuracy_ns = ParseJsonNumber(json, "accuracy_ns");
    quality.stability_ppm = ParseJsonNumber(json, "stability_ppm");
    quality.confidence = static_cast<uint32_t>(ParseJsonNumber(json, "confidence"));
    quality.is_traceable = ParseJsonBool(json, "is_traceable");
    quality.reference = ParseJsonString(json, "reference");
    return quality;
}

std::string JsonSerializer::SerializeTimeData(const TimeData& data) {
    std::ostringstream oss;
    oss << "{"
        << "\"timestamp_ns\":" << data.timestamp_ns << ","
        << "\"frequency_offset_ppm\":" << std::fixed << std::setprecision(6) << data.frequency_offset_ppm << ","
        << "\"phase_offset_ns\":" << std::fixed << std::setprecision(3) << data.phase_offset_ns << ","
        << "\"quality\":" << SerializeTimeQuality(data.quality) << ","
        << "\"source\":\"" << TimeSourceToString(data.source) << "\","
        << "\"measurement_time_ns\":" << data.measurement_time_ns << ","
        << "\"is_valid\":" << (data.is_valid ? "true" : "false") << ","
        << "\"timestamp_iso\":\"" << TimestampToIsoString(data.timestamp_ns) << "\""
        << "}";
    return oss.str();
}

TimeData JsonSerializer::DeserializeTimeData(const std::string& json) {
    TimeData data;
    data.timestamp_ns = ParseJsonUint64(json, "timestamp_ns");
    data.frequency_offset_ppm = ParseJsonNumber(json, "frequency_offset_ppm");
    data.phase_offset_ns = ParseJsonNumber(json, "phase_offset_ns");
    
    // 解析嵌套的quality对象
    std::regex quality_regex(R"("quality"\s*:\s*(\{[^}]*\}))");
    std::smatch match;
    if (std::regex_search(json, match, quality_regex)) {
        data.quality = DeserializeTimeQuality(match[1].str());
    }
    
    data.source = StringToTimeSource(ParseJsonString(json, "source"));
    data.measurement_time_ns = ParseJsonUint64(json, "measurement_time_ns");
    data.is_valid = ParseJsonBool(json, "is_valid");
    
    return data;
}

std::string JsonSerializer::SerializeTimeSourceInfo(const TimeSourceInfo& info) {
    std::ostringstream oss;
    oss << "{"
        << "\"type\":\"" << TimeSourceToString(info.type) << "\","
        << "\"status\":\"" << TimeSourceStatusToString(info.status) << "\","
        << "\"quality\":" << SerializeTimeQuality(info.quality) << ","
        << "\"priority\":" << info.priority << ","
        << "\"last_update_ns\":" << info.last_update_ns << ","
        << "\"last_update_iso\":\"" << TimestampToIsoString(info.last_update_ns) << "\","
        << "\"properties\":{";
    
    bool first = true;
    for (const auto& prop : info.properties) {
        if (!first) oss << ",";
        oss << "\"" << EscapeJsonString(prop.first) << "\":\"" << EscapeJsonString(prop.second) << "\"";
        first = false;
    }
    
    oss << "}}";
    return oss.str();
}

TimeSourceInfo JsonSerializer::DeserializeTimeSourceInfo(const std::string& json) {
    TimeSourceInfo info;
    info.type = StringToTimeSource(ParseJsonString(json, "type"));
    info.status = StringToTimeSourceStatus(ParseJsonString(json, "status"));
    
    // 解析嵌套的quality对象
    std::regex quality_regex(R"("quality"\s*:\s*(\{[^}]*\}))");
    std::smatch match;
    if (std::regex_search(json, match, quality_regex)) {
        info.quality = DeserializeTimeQuality(match[1].str());
    }
    
    info.priority = static_cast<uint32_t>(ParseJsonNumber(json, "priority"));
    info.last_update_ns = ParseJsonUint64(json, "last_update_ns");
    
    // 解析properties对象（简化实现）
    std::regex props_regex(R"("properties"\s*:\s*\{([^}]*)\})");
    if (std::regex_search(json, match, props_regex)) {
        std::string props_str = match[1].str();
        std::regex prop_regex("\"([^\"]+)\"\\s*:\\s*\"([^\"]*)\"");
        std::sregex_iterator iter(props_str.begin(), props_str.end(), prop_regex);
        std::sregex_iterator end;
        
        for (; iter != end; ++iter) {
            info.properties[(*iter)[1].str()] = (*iter)[2].str();
        }
    }
    
    return info;
}

std::string JsonSerializer::SerializeSystemStatus(const SystemStatus& status) {
    std::ostringstream oss;
    oss << "{"
        << "\"current_state\":\"" << ClockStateToString(status.current_state) << "\","
        << "\"active_source\":\"" << TimeSourceToString(status.active_source) << "\","
        << "\"health\":\"" << SystemHealthToString(status.health) << "\","
        << "\"uptime_seconds\":" << status.uptime_seconds << ","
        << "\"cpu_usage_percent\":" << std::fixed << std::setprecision(2) << status.cpu_usage_percent << ","
        << "\"memory_usage_mb\":" << status.memory_usage_mb << ","
        << "\"version\":\"" << EscapeJsonString(status.version) << "\","
        << "\"platform\":\"" << EscapeJsonString(status.platform) << "\","
        << "\"sources\":[";
    
    for (size_t i = 0; i < status.sources.size(); ++i) {
        if (i > 0) oss << ",";
        oss << SerializeTimeSourceInfo(status.sources[i]);
    }
    
    oss << "]}";
    return oss.str();
}

SystemStatus JsonSerializer::DeserializeSystemStatus(const std::string& json) {
    SystemStatus status;
    status.current_state = static_cast<ClockState>(ParseJsonNumber(json, "current_state"));
    status.active_source = StringToTimeSource(ParseJsonString(json, "active_source"));
    status.health = StringToSystemHealth(ParseJsonString(json, "health"));
    status.uptime_seconds = ParseJsonUint64(json, "uptime_seconds");
    status.cpu_usage_percent = ParseJsonNumber(json, "cpu_usage_percent");
    status.memory_usage_mb = ParseJsonUint64(json, "memory_usage_mb");
    status.version = ParseJsonString(json, "version");
    status.platform = ParseJsonString(json, "platform");
    
    // 解析sources数组（简化实现）
    std::regex sources_regex(R"("sources"\s*:\s*\[([^\]]*)\])");
    std::smatch match;
    if (std::regex_search(json, match, sources_regex)) {
        // 这里需要更复杂的JSON数组解析，暂时简化处理
        // 在实际项目中建议使用专业的JSON库如nlohmann/json
    }
    
    return status;
}

std::string JsonSerializer::SerializeTimingConfig(const TimingConfig& config) {
    std::ostringstream oss;
    oss << "{"
        << "\"priorities\":{"
        << "\"auto_failover\":" << (config.priorities.auto_failover ? "true" : "false") << ","
        << "\"failover_delay_ms\":" << config.priorities.failover_delay_ms << ","
        << "\"source_priorities\":{";
    
    bool first = true;
    for (const auto& priority : config.priorities.priorities) {
        if (!first) oss << ",";
        oss << "\"" << TimeSourceToString(priority.first) << "\":" << priority.second;
        first = false;
    }
    
    oss << "}},"
        << "\"discipline\":{"
        << "\"convergence_threshold_ns\":" << std::fixed << std::setprecision(3) << config.discipline.convergence_threshold_ns << ","
        << "\"convergence_time_s\":" << config.discipline.convergence_time_s << ","
        << "\"phase_gain\":" << std::fixed << std::setprecision(6) << config.discipline.phase_gain << ","
        << "\"frequency_gain\":" << std::fixed << std::setprecision(6) << config.discipline.frequency_gain << ","
        << "\"measurement_interval_ms\":" << config.discipline.measurement_interval_ms
        << "},"
        << "\"holdover\":{"
        << "\"max_holdover_hours\":" << config.holdover.max_holdover_hours << ","
        << "\"frequency_drift_limit_ppm\":" << std::fixed << std::setprecision(6) << config.holdover.frequency_drift_limit_ppm << ","
        << "\"learning_duration_hours\":" << config.holdover.learning_duration_hours << ","
        << "\"enable_temperature_compensation\":" << (config.holdover.enable_temperature_compensation ? "true" : "false")
        << "},"
        << "\"alarms\":{"
        << "\"phase_offset_warning_ns\":" << std::fixed << std::setprecision(3) << config.alarms.phase_offset_warning_ns << ","
        << "\"phase_offset_critical_ns\":" << std::fixed << std::setprecision(3) << config.alarms.phase_offset_critical_ns << ","
        << "\"frequency_offset_warning_ppm\":" << std::fixed << std::setprecision(6) << config.alarms.frequency_offset_warning_ppm << ","
        << "\"frequency_offset_critical_ppm\":" << std::fixed << std::setprecision(6) << config.alarms.frequency_offset_critical_ppm << ","
        << "\"gnss_satellites_warning\":" << config.alarms.gnss_satellites_warning << ","
        << "\"gnss_satellites_critical\":" << config.alarms.gnss_satellites_critical << ","
        << "\"gnss_snr_warning_db\":" << std::fixed << std::setprecision(2) << config.alarms.gnss_snr_warning_db << ","
        << "\"gnss_snr_critical_db\":" << std::fixed << std::setprecision(2) << config.alarms.gnss_snr_critical_db << ","
        << "\"cpu_usage_warning\":" << std::fixed << std::setprecision(2) << config.alarms.cpu_usage_warning << ","
        << "\"memory_usage_warning\":" << std::fixed << std::setprecision(2) << config.alarms.memory_usage_warning << ","
        << "\"temperature_warning\":" << std::fixed << std::setprecision(2) << config.alarms.temperature_warning << ","
        << "\"temperature_critical\":" << std::fixed << std::setprecision(2) << config.alarms.temperature_critical
        << "},"
        << "\"config_version\":\"" << EscapeJsonString(config.config_version) << "\","
        << "\"last_modified\":" << config.last_modified
        << "}";
    
    return oss.str();
}

TimingConfig JsonSerializer::DeserializeTimingConfig(const std::string& json) {
    TimingConfig config;
    
    // 解析priorities部分
    config.priorities.auto_failover = ParseJsonBool(json, "auto_failover");
    config.priorities.failover_delay_ms = static_cast<uint32_t>(ParseJsonNumber(json, "failover_delay_ms"));
    
    // 解析discipline部分
    config.discipline.convergence_threshold_ns = ParseJsonNumber(json, "convergence_threshold_ns");
    config.discipline.convergence_time_s = static_cast<uint32_t>(ParseJsonNumber(json, "convergence_time_s"));
    config.discipline.phase_gain = ParseJsonNumber(json, "phase_gain");
    config.discipline.frequency_gain = ParseJsonNumber(json, "frequency_gain");
    config.discipline.measurement_interval_ms = static_cast<uint32_t>(ParseJsonNumber(json, "measurement_interval_ms"));
    
    // 解析holdover部分
    config.holdover.max_holdover_hours = static_cast<uint32_t>(ParseJsonNumber(json, "max_holdover_hours"));
    config.holdover.frequency_drift_limit_ppm = ParseJsonNumber(json, "frequency_drift_limit_ppm");
    config.holdover.learning_duration_hours = static_cast<uint32_t>(ParseJsonNumber(json, "learning_duration_hours"));
    config.holdover.enable_temperature_compensation = ParseJsonBool(json, "enable_temperature_compensation");
    
    // 解析alarms部分
    config.alarms.phase_offset_warning_ns = ParseJsonNumber(json, "phase_offset_warning_ns");
    config.alarms.phase_offset_critical_ns = ParseJsonNumber(json, "phase_offset_critical_ns");
    config.alarms.frequency_offset_warning_ppm = ParseJsonNumber(json, "frequency_offset_warning_ppm");
    config.alarms.frequency_offset_critical_ppm = ParseJsonNumber(json, "frequency_offset_critical_ppm");
    config.alarms.gnss_satellites_warning = static_cast<uint32_t>(ParseJsonNumber(json, "gnss_satellites_warning"));
    config.alarms.gnss_satellites_critical = static_cast<uint32_t>(ParseJsonNumber(json, "gnss_satellites_critical"));
    config.alarms.gnss_snr_warning_db = ParseJsonNumber(json, "gnss_snr_warning_db");
    config.alarms.gnss_snr_critical_db = ParseJsonNumber(json, "gnss_snr_critical_db");
    config.alarms.cpu_usage_warning = ParseJsonNumber(json, "cpu_usage_warning");
    config.alarms.memory_usage_warning = ParseJsonNumber(json, "memory_usage_warning");
    config.alarms.temperature_warning = ParseJsonNumber(json, "temperature_warning");
    config.alarms.temperature_critical = ParseJsonNumber(json, "temperature_critical");
    
    config.config_version = ParseJsonString(json, "config_version");
    config.last_modified = ParseJsonUint64(json, "last_modified");
    
    return config;
}

// 私有辅助函数实现

std::string JsonSerializer::EscapeJsonString(const std::string& str) {
    std::string escaped;
    escaped.reserve(str.length() + 10); // 预留一些空间给转义字符
    
    for (char c : str) {
        switch (c) {
            case '"': escaped += "\\\""; break;
            case '\\': escaped += "\\\\"; break;
            case '\b': escaped += "\\b"; break;
            case '\f': escaped += "\\f"; break;
            case '\n': escaped += "\\n"; break;
            case '\r': escaped += "\\r"; break;
            case '\t': escaped += "\\t"; break;
            default:
                if (c < 0x20) {
                    // 控制字符转义为\uXXXX格式
                    char hex_buffer[5];
                    std::snprintf(hex_buffer, sizeof(hex_buffer), "\\u%02x", static_cast<unsigned char>(c));
                    escaped += hex_buffer;
                } else {
                    escaped += c;
                }
                break;
        }
    }
    
    return escaped;
}

std::string JsonSerializer::ParseJsonString(const std::string& json, const std::string& key) {
    std::string pattern = "\"" + key + "\"\\s*:\\s*\"([^\"]*?)\"";
    std::regex regex(pattern);
    std::smatch match;
    
    if (std::regex_search(json, match, regex)) {
        return match[1].str();
    }
    
    return "";
}

double JsonSerializer::ParseJsonNumber(const std::string& json, const std::string& key) {
    std::string pattern = "\"" + key + "\"\\s*:\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)";
    std::regex regex(pattern);
    std::smatch match;
    
    if (std::regex_search(json, match, regex)) {
        return std::stod(match[1].str());
    }
    
    return 0.0;
}

uint64_t JsonSerializer::ParseJsonUint64(const std::string& json, const std::string& key) {
    std::string pattern = "\"" + key + "\"\\s*:\\s*(\\d+)";
    std::regex regex(pattern);
    std::smatch match;
    
    if (std::regex_search(json, match, regex)) {
        return std::stoull(match[1].str());
    }
    
    return 0;
}

bool JsonSerializer::ParseJsonBool(const std::string& json, const std::string& key) {
    std::string pattern = "\"" + key + "\"\\s*:\\s*(true|false)";
    std::regex regex(pattern);
    std::smatch match;
    
    if (std::regex_search(json, match, regex)) {
        return match[1].str() == "true";
    }
    
    return false;
}

// 二进制序列化实现

size_t BinarySerializer::SerializeTimeData(const TimeData& data, uint8_t* buffer, size_t buffer_size) {
    if (buffer_size < sizeof(TimeData)) {
        return 0; // 缓冲区太小
    }
    
    uint8_t* ptr = buffer;
    size_t written = 0;
    
    // 写入基本数据类型
    written += WriteValue(ptr, data.timestamp_ns);
    written += WriteValue(ptr, data.frequency_offset_ppm);
    written += WriteValue(ptr, data.phase_offset_ns);
    written += WriteValue(ptr, data.source);
    written += WriteValue(ptr, data.measurement_time_ns);
    written += WriteValue(ptr, data.is_valid);
    
    // 写入TimeQuality结构
    written += WriteValue(ptr, data.quality.accuracy_ns);
    written += WriteValue(ptr, data.quality.stability_ppm);
    written += WriteValue(ptr, data.quality.confidence);
    written += WriteValue(ptr, data.quality.is_traceable);
    written += WriteString(ptr, data.quality.reference);
    
    return written;
}

size_t BinarySerializer::DeserializeTimeData(const uint8_t* buffer, size_t buffer_size, TimeData& data) {
    if (buffer_size < sizeof(uint64_t)) {
        return 0; // 缓冲区太小
    }
    
    const uint8_t* ptr = buffer;
    size_t read = 0;
    
    // 读取基本数据类型
    read += ReadValue(ptr, data.timestamp_ns);
    read += ReadValue(ptr, data.frequency_offset_ppm);
    read += ReadValue(ptr, data.phase_offset_ns);
    read += ReadValue(ptr, data.source);
    read += ReadValue(ptr, data.measurement_time_ns);
    read += ReadValue(ptr, data.is_valid);
    
    // 读取TimeQuality结构
    read += ReadValue(ptr, data.quality.accuracy_ns);
    read += ReadValue(ptr, data.quality.stability_ppm);
    read += ReadValue(ptr, data.quality.confidence);
    read += ReadValue(ptr, data.quality.is_traceable);
    read += ReadString(ptr, data.quality.reference);
    
    return read;
}

size_t BinarySerializer::SerializeSystemStatus(const SystemStatus& status, uint8_t* buffer, size_t /* buffer_size */) {
    uint8_t* ptr = buffer;
    size_t written = 0;
    
    // 写入基本字段
    written += WriteValue(ptr, status.current_state);
    written += WriteValue(ptr, status.active_source);
    written += WriteValue(ptr, status.health);
    written += WriteValue(ptr, status.uptime_seconds);
    written += WriteValue(ptr, status.cpu_usage_percent);
    written += WriteValue(ptr, status.memory_usage_mb);
    written += WriteString(ptr, status.version);
    written += WriteString(ptr, status.platform);
    
    // 写入sources数组大小
    uint32_t sources_count = static_cast<uint32_t>(status.sources.size());
    written += WriteValue(ptr, sources_count);
    
    // 写入每个TimeSourceInfo（简化实现）
    for (const auto& source : status.sources) {
        written += WriteValue(ptr, source.type);
        written += WriteValue(ptr, source.status);
        written += WriteValue(ptr, source.priority);
        written += WriteValue(ptr, source.last_update_ns);
        // 省略quality和properties的序列化以简化实现
    }
    
    return written;
}

size_t BinarySerializer::DeserializeSystemStatus(const uint8_t* buffer, size_t /* buffer_size */, SystemStatus& status) {
    const uint8_t* ptr = buffer;
    size_t read = 0;
    
    // 读取基本字段
    read += ReadValue(ptr, status.current_state);
    read += ReadValue(ptr, status.active_source);
    read += ReadValue(ptr, status.health);
    read += ReadValue(ptr, status.uptime_seconds);
    read += ReadValue(ptr, status.cpu_usage_percent);
    read += ReadValue(ptr, status.memory_usage_mb);
    read += ReadString(ptr, status.version);
    read += ReadString(ptr, status.platform);
    
    // 读取sources数组
    uint32_t sources_count;
    read += ReadValue(ptr, sources_count);
    
    status.sources.clear();
    status.sources.reserve(sources_count);
    
    for (uint32_t i = 0; i < sources_count; ++i) {
        TimeSourceInfo source;
        read += ReadValue(ptr, source.type);
        read += ReadValue(ptr, source.status);
        read += ReadValue(ptr, source.priority);
        read += ReadValue(ptr, source.last_update_ns);
        // 省略quality和properties的反序列化以简化实现
        status.sources.push_back(source);
    }
    
    return read;
}

// 二进制序列化辅助函数

size_t BinarySerializer::WriteString(uint8_t*& buffer, const std::string& str) {
    uint32_t length = static_cast<uint32_t>(str.length());
    size_t written = WriteValue(buffer, length);
    
    if (length > 0) {
        std::memcpy(buffer, str.c_str(), length);
        buffer += length;
        written += length;
    }
    
    return written;
}

size_t BinarySerializer::ReadString(const uint8_t*& buffer, std::string& str) {
    uint32_t length;
    size_t read = ReadValue(buffer, length);
    
    if (length > 0) {
        str.assign(reinterpret_cast<const char*>(buffer), length);
        buffer += length;
        read += length;
    } else {
        str.clear();
    }
    
    return read;
}

template<typename T>
size_t BinarySerializer::WriteValue(uint8_t*& buffer, const T& value) {
    std::memcpy(buffer, &value, sizeof(T));
    buffer += sizeof(T);
    return sizeof(T);
}

template<typename T>
size_t BinarySerializer::ReadValue(const uint8_t*& buffer, T& value) {
    std::memcpy(&value, buffer, sizeof(T));
    buffer += sizeof(T);
    return sizeof(T);
}

} // namespace core
} // namespace timing_server