#include "core/precision_monitor.h"
#include "core/timing_engine.h"
#include "core/logger.h"
#include "core/system_resource_monitor.h"
#include <algorithm>
#include <numeric>
#include <cmath>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <sys/resource.h>
#include <unistd.h>
#include <chrono>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

namespace timing_server {
namespace core {

PrecisionMonitor::PrecisionMonitor(std::shared_ptr<ITimingEngine> timing_engine,
                                 const AlarmThresholds& config)
    : timing_engine_(timing_engine)
    , alarm_thresholds_(config)
    , monitoring_level_(MonitoringLevel::STANDARD)
    , running_(false)
    , monitoring_interval_(std::chrono::milliseconds(DEFAULT_MONITORING_INTERVAL_MS))
    , total_measurements_(0)
    , failed_measurements_(0)
    , start_time_(std::chrono::steady_clock::now())
    , system_resource_monitor_(std::make_unique<CachedSystemResourceMonitor>(CreateSystemResourceMonitor())) {
    
    // 设置默认告警阈值（如果未提供）
    if (alarm_thresholds_.phase_offset_warning_ns == 0.0) {
        alarm_thresholds_.phase_offset_warning_ns = 100.0;
        alarm_thresholds_.phase_offset_critical_ns = 500.0;
        alarm_thresholds_.frequency_offset_warning_ppm = 0.01;
        alarm_thresholds_.frequency_offset_critical_ppm = 0.1;
        alarm_thresholds_.gnss_satellites_warning = 6;
        alarm_thresholds_.gnss_satellites_critical = 4;
        alarm_thresholds_.gnss_snr_warning_db = -145.0;
        alarm_thresholds_.gnss_snr_critical_db = -150.0;
        alarm_thresholds_.cpu_usage_warning = 80.0;
        alarm_thresholds_.memory_usage_warning = 80.0;
        alarm_thresholds_.temperature_warning = 70.0;
        alarm_thresholds_.temperature_critical = 75.0;
    }
    
    LOG_INFO(LogComponent::SYSTEM, "精度监控器已创建，监控级别: " + 
             std::to_string(static_cast<int>(monitoring_level_)));
}

PrecisionMonitor::~PrecisionMonitor() {
    Stop();
    LOG_INFO(LogComponent::SYSTEM, "精度监控器已销毁");
}

bool PrecisionMonitor::Start() {
    std::lock_guard<std::mutex> lock(monitoring_mutex_);
    
    if (running_) {
        LOG_WARNING(LogComponent::SYSTEM, "精度监控器已在运行中");
        return true;
    }
    
    if (!timing_engine_) {
        LOG_ERROR(LogComponent::SYSTEM, "授时引擎实例无效，无法启动精度监控");
        return false;
    }
    
    running_ = true;
    start_time_ = std::chrono::steady_clock::now();
    total_measurements_ = 0;
    failed_measurements_ = 0;
    
    // 启动监控线程
    monitoring_thread_ = std::thread(&PrecisionMonitor::MonitoringThread, this);
    
    LOG_INFO(LogComponent::SYSTEM, "精度监控器已启动");
    return true;
}

bool PrecisionMonitor::Stop() {
    std::unique_lock<std::mutex> lock(monitoring_mutex_);
    
    if (!running_) {
        return true;
    }
    
    running_ = false;
    monitoring_cv_.notify_all();
    lock.unlock();
    
    // 等待监控线程结束
    if (monitoring_thread_.joinable()) {
        monitoring_thread_.join();
    }
    
    LOG_INFO(LogComponent::SYSTEM, "精度监控器已停止，总测量次数: " + 
             std::to_string(total_measurements_) + 
             "，失败次数: " + std::to_string(failed_measurements_));
    return true;
}

void PrecisionMonitor::SetMonitoringLevel(MonitoringLevel level) {
    monitoring_level_ = level;
    
    // 根据监控级别调整监控间隔
    switch (level) {
        case MonitoringLevel::BASIC:
            monitoring_interval_ = std::chrono::milliseconds(5000); // 5秒
            break;
        case MonitoringLevel::STANDARD:
            monitoring_interval_ = std::chrono::milliseconds(1000); // 1秒
            break;
        case MonitoringLevel::ADVANCED:
            monitoring_interval_ = std::chrono::milliseconds(500);  // 0.5秒
            break;
        case MonitoringLevel::DIAGNOSTIC:
            monitoring_interval_ = std::chrono::milliseconds(100);  // 0.1秒
            break;
    }
    
    LOG_INFO(LogComponent::SYSTEM, "精度监控级别已设置为: " + 
             std::to_string(static_cast<int>(level)) + 
             "，监控间隔: " + std::to_string(monitoring_interval_.count()) + "ms");
}

PrecisionMeasurement PrecisionMonitor::GetCurrentMeasurement() {
    return PerformMeasurement();
}

std::vector<PrecisionMeasurement> PrecisionMonitor::GetHistoricalMeasurements(
    uint64_t start_time, uint64_t end_time, uint32_t max_samples) {
    
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    std::vector<PrecisionMeasurement> result;
    
    // 从历史数据中筛选指定时间范围的数据
    std::queue<PrecisionMeasurement> temp_queue = measurement_history_;
    std::vector<PrecisionMeasurement> all_measurements;
    
    while (!temp_queue.empty()) {
        all_measurements.push_back(temp_queue.front());
        temp_queue.pop();
    }
    
    // 按时间戳排序
    std::sort(all_measurements.begin(), all_measurements.end(),
              [](const PrecisionMeasurement& a, const PrecisionMeasurement& b) {
                  return a.timestamp_ns < b.timestamp_ns;
              });
    
    // 筛选时间范围
    for (const auto& measurement : all_measurements) {
        if (measurement.timestamp_ns >= start_time && measurement.timestamp_ns <= end_time) {
            result.push_back(measurement);
        }
    }
    
    // 限制样本数量
    if (result.size() > max_samples) {
        // 均匀采样
        std::vector<PrecisionMeasurement> sampled_result;
        double step = static_cast<double>(result.size()) / max_samples;
        for (uint32_t i = 0; i < max_samples; ++i) {
            size_t index = static_cast<size_t>(i * step);
            if (index < result.size()) {
                sampled_result.push_back(result[index]);
            }
        }
        result = std::move(sampled_result);
    }
    
    LOG_DEBUG(LogComponent::SYSTEM, "返回历史测量数据: " + std::to_string(result.size()) + " 个样本");
    return result;
}

PrecisionTrend PrecisionMonitor::GetPrecisionTrend(uint32_t period_hours) {
    uint64_t current_time = GetCurrentTimestampNs();
    uint64_t start_time = current_time - (static_cast<uint64_t>(period_hours) * 3600ULL * 1000000000ULL);
    
    auto measurements = GetHistoricalMeasurements(start_time, current_time, 10000);
    return AnalyzePrecisionTrend(measurements, period_hours);
}

SystemHealthScore PrecisionMonitor::GetSystemHealthScore() {
    auto current_measurement = GetCurrentMeasurement();
    return CalculateHealthScore(current_measurement);
}

PredictiveMaintenanceAdvice PrecisionMonitor::GetMaintenanceAdvice() {
    auto trend = GetPrecisionTrend(168); // 7天趋势
    auto health_score = GetSystemHealthScore();
    return GenerateMaintenanceAdvice(trend, health_score);
}

bool PrecisionMonitor::MeetsAccuracyRequirement(double required_accuracy_ns) {
    auto current_measurement = GetCurrentMeasurement();
    return std::abs(current_measurement.absolute_accuracy_ns) <= required_accuracy_ns;
}

void PrecisionMonitor::SetMeasurementCallback(std::function<void(const PrecisionMeasurement&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    measurement_callback_ = callback;
    LOG_INFO(LogComponent::SYSTEM, "精度测量回调函数已设置");
}

void PrecisionMonitor::SetAlarmThresholds(const AlarmThresholds& config) {
    alarm_thresholds_ = config;
    LOG_INFO(LogComponent::SYSTEM, "告警阈值配置已更新");
}

const AlarmThresholds& PrecisionMonitor::GetAlarmThresholds() const {
    return alarm_thresholds_;
}

PrecisionMeasurement PrecisionMonitor::ForceMeasurement() {
    return PerformMeasurement();
}

void PrecisionMonitor::ClearHistoricalData(uint32_t older_than_hours) {
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    uint64_t cutoff_time = GetCurrentTimestampNs() - 
                          (static_cast<uint64_t>(older_than_hours) * 3600ULL * 1000000000ULL);
    
    std::queue<PrecisionMeasurement> new_queue;
    size_t removed_count = 0;
    
    while (!measurement_history_.empty()) {
        auto measurement = measurement_history_.front();
        measurement_history_.pop();
        
        if (measurement.timestamp_ns >= cutoff_time) {
            new_queue.push(measurement);
        } else {
            removed_count++;
        }
    }
    
    measurement_history_ = std::move(new_queue);
    
    LOG_INFO(LogComponent::SYSTEM, "已清除 " + std::to_string(removed_count) + 
             " 个历史测量数据（超过 " + std::to_string(older_than_hours) + " 小时）");
}

bool PrecisionMonitor::ExportMeasurementData(const std::string& filename, const std::string& format) {
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            LOG_ERROR(LogComponent::SYSTEM, "无法打开导出文件: " + filename);
            return false;
        }
        
        if (format == "csv") {
            // CSV格式导出
            file << "timestamp,source,state,accuracy_ns,phase_offset_ns,frequency_offset_ppm,"
                 << "allan_1s,allan_10s,allan_100s,gnss_satellites,gnss_snr_db,"
                 << "rubidium_temp,system_temp,cpu_usage,memory_usage,quality_score\n";
            
            std::queue<PrecisionMeasurement> temp_queue = measurement_history_;
            while (!temp_queue.empty()) {
                const auto& m = temp_queue.front();
                file << m.timestamp_ns << ","
                     << TimeSourceToString(m.source) << ","
                     << ClockStateToString(m.system_state) << ","
                     << m.absolute_accuracy_ns << ","
                     << m.phase_offset_ns << ","
                     << m.frequency_offset_ppm << ","
                     << m.allan_deviation_1s << ","
                     << m.allan_deviation_10s << ","
                     << m.allan_deviation_100s << ","
                     << m.gnss_satellites << ","
                     << m.gnss_snr_db << ","
                     << m.rubidium_temperature << ","
                     << m.system_temperature << ","
                     << m.cpu_usage_percent << ","
                     << m.memory_usage_mb << ","
                     << m.overall_quality_score << "\n";
                temp_queue.pop();
            }
        } else if (format == "json") {
            // JSON格式导出
            file << "{\n  \"measurements\": [\n";
            
            std::queue<PrecisionMeasurement> temp_queue = measurement_history_;
            bool first = true;
            while (!temp_queue.empty()) {
                const auto& m = temp_queue.front();
                if (!first) file << ",\n";
                first = false;
                
                file << "    {\n"
                     << "      \"timestamp\": " << m.timestamp_ns << ",\n"
                     << "      \"source\": \"" << TimeSourceToString(m.source) << "\",\n"
                     << "      \"state\": \"" << ClockStateToString(m.system_state) << "\",\n"
                     << "      \"accuracy_ns\": " << m.absolute_accuracy_ns << ",\n"
                     << "      \"phase_offset_ns\": " << m.phase_offset_ns << ",\n"
                     << "      \"frequency_offset_ppm\": " << m.frequency_offset_ppm << ",\n"
                     << "      \"quality_score\": " << m.overall_quality_score << "\n"
                     << "    }";
                temp_queue.pop();
            }
            file << "\n  ]\n}";
        } else {
            LOG_ERROR(LogComponent::SYSTEM, "不支持的导出格式: " + format);
            return false;
        }
        
        file.close();
        LOG_INFO(LogComponent::SYSTEM, "测量数据已导出到: " + filename);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "导出测量数据失败: " + std::string(e.what()));
        return false;
    }
}

void PrecisionMonitor::MonitoringThread() {
    LOG_INFO(LogComponent::SYSTEM, "精度监控线程已启动");
    
    while (running_) {
        try {
            // 执行精度测量
            auto measurement = PerformMeasurement();
            
            // 存储测量数据
            StoreMeasurement(measurement);
            
            // 通知回调函数
            NotifyMeasurementCallback(measurement);
            
            total_measurements_++;
            
        } catch (const std::exception& e) {
            failed_measurements_++;
            LOG_ERROR(LogComponent::SYSTEM, "精度测量失败: " + std::string(e.what()));
        }
        
        // 等待下次测量
        std::unique_lock<std::mutex> lock(monitoring_mutex_);
        monitoring_cv_.wait_for(lock, monitoring_interval_, [this] { return !running_; });
    }
    
    LOG_INFO(LogComponent::SYSTEM, "精度监控线程已结束");
}

PrecisionMeasurement PrecisionMonitor::PerformMeasurement() {
    PrecisionMeasurement measurement;
    measurement.timestamp_ns = GetCurrentTimestampNs();
    
    try {
        // 获取系统状态
        auto system_status = timing_engine_->GetSystemStatus();
        measurement.system_state = system_status.current_state;
        measurement.source = system_status.active_source;
        
        // 设置默认精度值（实际实现中应该从具体的时间源获取）
        measurement.absolute_accuracy_ns = 25.0; // 默认精度值
        
        // 获取时间源信息
        auto time_source_info = timing_engine_->GetTimeSourceInfo(system_status.active_source);
        measurement.phase_offset_ns = 0.0; // 需要从具体实现中获取
        measurement.frequency_offset_ppm = 0.0; // 需要从具体实现中获取
        
        // 计算Allan偏差（需要历史数据）
        auto recent_measurements = GetHistoricalMeasurements(
            measurement.timestamp_ns - 100000000000ULL, // 100秒前
            measurement.timestamp_ns, 100);
        
        if (recent_measurements.size() >= 10) {
            measurement.allan_deviation_1s = CalculateAllanDeviation(recent_measurements, 1);
            measurement.allan_deviation_10s = CalculateAllanDeviation(recent_measurements, 10);
            measurement.allan_deviation_100s = CalculateAllanDeviation(recent_measurements, 100);
        }
        
        // 获取GNSS信息
        if (system_status.active_source == TimeSource::GNSS) {
            // 从时间源属性中获取GNSS信息
            auto gnss_props = time_source_info.properties;
            if (gnss_props.find("satellites") != gnss_props.end()) {
                measurement.gnss_satellites = std::stoul(gnss_props["satellites"]);
            }
            if (gnss_props.find("snr_db") != gnss_props.end()) {
                measurement.gnss_snr_db = std::stod(gnss_props["snr_db"]);
            }
        }
        
        // 获取铷钟温度
        if (system_status.active_source == TimeSource::RUBIDIUM) {
            auto rb_props = time_source_info.properties;
            if (rb_props.find("temperature") != rb_props.end()) {
                measurement.rubidium_temperature = std::stod(rb_props["temperature"]);
            }
        }
        
        // 获取系统资源使用情况
        GetSystemResourceUsage(measurement.cpu_usage_percent, measurement.memory_usage_mb);
        
        // 获取网络延迟
        measurement.network_latency_us = GetNetworkLatency();
        
        // 计算质量评分
        measurement.overall_quality_score = 0;
        
        // 基于精度的评分（40%权重）
        double accuracy_score = 0;
        if (measurement.system_state == ClockState::LOCKED) {
            accuracy_score = std::max(0.0, 100.0 - (std::abs(measurement.absolute_accuracy_ns) / SPEC_ACCURACY_LOCKED_NS) * 100.0);
        } else if (measurement.system_state == ClockState::HOLDOVER) {
            accuracy_score = std::max(0.0, 100.0 - (std::abs(measurement.absolute_accuracy_ns) / SPEC_ACCURACY_HOLDOVER_NS) * 100.0);
        } else {
            accuracy_score = 50.0; // 中等评分
        }
        
        // 基于信号质量的评分（30%权重）
        double signal_score = 50.0; // 默认中等评分
        if (measurement.gnss_satellites > 0) {
            signal_score = std::min(100.0, (measurement.gnss_satellites / 12.0) * 100.0);
            if (measurement.gnss_snr_db > alarm_thresholds_.gnss_snr_warning_db) {
                signal_score *= 1.2; // 信号强度好时加分
            }
        }
        
        // 基于系统性能的评分（20%权重）
        double performance_score = 100.0 - measurement.cpu_usage_percent;
        
        // 基于温度的评分（10%权重）
        double temperature_score = 100.0;
        if (measurement.rubidium_temperature > alarm_thresholds_.temperature_warning) {
            temperature_score = std::max(0.0, 100.0 - ((measurement.rubidium_temperature - alarm_thresholds_.temperature_warning) / 10.0) * 100.0);
        }
        
        // 综合评分
        measurement.overall_quality_score = static_cast<uint32_t>(
            accuracy_score * 0.4 + signal_score * 0.3 + performance_score * 0.2 + temperature_score * 0.1);
        
        // 检查是否满足规格要求
        measurement.meets_spec_requirements = 
            (measurement.system_state == ClockState::LOCKED && 
             std::abs(measurement.absolute_accuracy_ns) <= SPEC_ACCURACY_LOCKED_NS) ||
            (measurement.system_state == ClockState::HOLDOVER && 
             std::abs(measurement.absolute_accuracy_ns) <= SPEC_ACCURACY_HOLDOVER_NS);
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "执行精度测量时发生错误: " + std::string(e.what()));
        measurement.overall_quality_score = 0;
        measurement.meets_spec_requirements = false;
    }
    
    return measurement;
}

double PrecisionMonitor::CalculateAllanDeviation(const std::vector<PrecisionMeasurement>& measurements, 
                                                uint32_t tau_seconds) {
    if (measurements.size() < 3) {
        return 0.0;
    }
    
    // 简化的Allan偏差计算
    std::vector<double> frequency_data;
    for (const auto& m : measurements) {
        frequency_data.push_back(m.frequency_offset_ppm);
    }
    
    if (frequency_data.size() < tau_seconds + 1) {
        return 0.0;
    }
    
    double sum_squared_diff = 0.0;
    size_t count = 0;
    
    for (size_t i = 0; i < frequency_data.size() - tau_seconds; ++i) {
        double diff = frequency_data[i + tau_seconds] - frequency_data[i];
        sum_squared_diff += diff * diff;
        count++;
    }
    
    if (count == 0) {
        return 0.0;
    }
    
    return std::sqrt(sum_squared_diff / (2.0 * count));
}

PrecisionTrend PrecisionMonitor::AnalyzePrecisionTrend(const std::vector<PrecisionMeasurement>& measurements,
                                                      uint32_t period_hours) {
    PrecisionTrend trend;
    trend.analysis_period_hours = period_hours;
    
    if (measurements.empty()) {
        return trend;
    }
    
    // 计算统计数据
    std::vector<double> accuracy_values;
    std::vector<double> frequency_values;
    
    for (const auto& m : measurements) {
        accuracy_values.push_back(std::abs(m.absolute_accuracy_ns));
        frequency_values.push_back(m.frequency_offset_ppm);
    }
    
    // 平均值
    trend.mean_accuracy_ns = std::accumulate(accuracy_values.begin(), accuracy_values.end(), 0.0) / accuracy_values.size();
    
    // 标准差
    double variance = 0.0;
    for (double val : accuracy_values) {
        variance += (val - trend.mean_accuracy_ns) * (val - trend.mean_accuracy_ns);
    }
    trend.std_deviation_accuracy_ns = std::sqrt(variance / accuracy_values.size());
    
    // 最小值和最大值
    trend.min_accuracy_ns = *std::min_element(accuracy_values.begin(), accuracy_values.end());
    trend.max_accuracy_ns = *std::max_element(accuracy_values.begin(), accuracy_values.end());
    
    // 简单的线性趋势分析
    if (measurements.size() >= 2) {
        double time_span_hours = static_cast<double>(measurements.back().timestamp_ns - measurements.front().timestamp_ns) / 3600000000000.0;
        if (time_span_hours > 0) {
            trend.accuracy_trend_ns_per_hour = (accuracy_values.back() - accuracy_values.front()) / time_span_hours;
            trend.frequency_drift_ppm_per_hour = (frequency_values.back() - frequency_values.front()) / time_span_hours;
        }
    }
    
    // 稳定性评估
    trend.stability_trend_coefficient = trend.std_deviation_accuracy_ns / trend.mean_accuracy_ns;
    trend.trend_is_stable = trend.stability_trend_coefficient < 0.1; // 10%以内认为稳定
    
    // 规格符合性检查
    trend.within_specification = trend.mean_accuracy_ns <= SPEC_ACCURACY_LOCKED_NS;
    
    // 预测
    trend.predicted_accuracy_1h_ns = trend.mean_accuracy_ns + trend.accuracy_trend_ns_per_hour * 1.0;
    trend.predicted_accuracy_24h_ns = trend.mean_accuracy_ns + trend.accuracy_trend_ns_per_hour * 24.0;
    trend.confidence_level = std::max(0.0, 1.0 - trend.stability_trend_coefficient);
    
    // 生成建议
    if (!trend.trend_is_stable) {
        trend.recommendations.push_back("系统精度不稳定，建议检查时间源质量");
    }
    if (!trend.within_specification) {
        trend.recommendations.push_back("精度超出规格要求，建议重新校准系统");
    }
    if (std::abs(trend.frequency_drift_ppm_per_hour) > 0.001) {
        trend.recommendations.push_back("频率漂移较大，建议检查铷钟状态");
    }
    
    return trend;
}

SystemHealthScore PrecisionMonitor::CalculateHealthScore(const PrecisionMeasurement& current_measurement) {
    SystemHealthScore score;
    
    // 时间精度评分
    if (current_measurement.system_state == ClockState::LOCKED) {
        if (std::abs(current_measurement.absolute_accuracy_ns) <= SPEC_ACCURACY_LOCKED_NS) {
            score.timing_accuracy_score = 100;
        } else {
            score.timing_accuracy_score = static_cast<uint32_t>(
                std::max(0.0, 100.0 - (std::abs(current_measurement.absolute_accuracy_ns) / SPEC_ACCURACY_LOCKED_NS - 1.0) * 100.0));
        }
    } else if (current_measurement.system_state == ClockState::HOLDOVER) {
        if (std::abs(current_measurement.absolute_accuracy_ns) <= SPEC_ACCURACY_HOLDOVER_NS) {
            score.timing_accuracy_score = 80; // 守时状态最高80分
        } else {
            score.timing_accuracy_score = static_cast<uint32_t>(
                std::max(0.0, 80.0 - (std::abs(current_measurement.absolute_accuracy_ns) / SPEC_ACCURACY_HOLDOVER_NS - 1.0) * 80.0));
        }
    } else {
        score.timing_accuracy_score = 50; // 其他状态中等评分
    }
    
    // 信号质量评分
    if (current_measurement.gnss_satellites >= alarm_thresholds_.gnss_satellites_warning) {
        score.signal_quality_score = 100;
    } else if (current_measurement.gnss_satellites >= alarm_thresholds_.gnss_satellites_critical) {
        score.signal_quality_score = 70;
    } else if (current_measurement.gnss_satellites > 0) {
        score.signal_quality_score = 40;
    } else {
        score.signal_quality_score = 0;
    }
    
    // 系统稳定性评分（基于Allan偏差）
    if (current_measurement.allan_deviation_1s > 0) {
        if (current_measurement.allan_deviation_1s < 1e-12) {
            score.system_stability_score = 100;
        } else if (current_measurement.allan_deviation_1s < 1e-11) {
            score.system_stability_score = 80;
        } else if (current_measurement.allan_deviation_1s < 1e-10) {
            score.system_stability_score = 60;
        } else {
            score.system_stability_score = 40;
        }
    } else {
        score.system_stability_score = 50; // 无数据时中等评分
    }
    
    // 硬件健康评分
    score.hardware_health_score = 100;
    if (current_measurement.rubidium_temperature > alarm_thresholds_.temperature_critical) {
        score.hardware_health_score = 20;
        score.issues.push_back("铷钟温度过高");
    } else if (current_measurement.rubidium_temperature > alarm_thresholds_.temperature_warning) {
        score.hardware_health_score = 60;
        score.warnings.push_back("铷钟温度偏高");
    }
    
    // 性能评分
    score.performance_score = static_cast<uint32_t>(
        std::max(0.0, 100.0 - current_measurement.cpu_usage_percent));
    
    if (current_measurement.memory_usage_mb > 100) { // 超过100MB内存使用
        score.performance_score = static_cast<uint32_t>(score.performance_score * 0.8);
        score.warnings.push_back("内存使用量较高");
    }
    
    // 计算总体评分
    score.overall_score = static_cast<uint32_t>(
        (score.timing_accuracy_score * 0.4 +
         score.signal_quality_score * 0.25 +
         score.system_stability_score * 0.2 +
         score.hardware_health_score * 0.1 +
         score.performance_score * 0.05));
    
    // 确定健康状态
    if (score.overall_score >= 90) {
        score.health_status = SystemHealth::HEALTHY;
    } else if (score.overall_score >= 70) {
        score.health_status = SystemHealth::WARNING;
    } else if (score.overall_score >= 50) {
        score.health_status = SystemHealth::ERROR;
    } else {
        score.health_status = SystemHealth::CRITICAL;
    }
    
    // 维护建议
    score.needs_calibration = !current_measurement.meets_spec_requirements;
    score.needs_maintenance = score.overall_score < 70;
    
    if (score.needs_maintenance) {
        score.days_until_maintenance = 7; // 建议一周内维护
    } else {
        score.days_until_maintenance = 30; // 正常维护周期
    }
    
    // 生成改进建议
    if (score.timing_accuracy_score < 80) {
        score.suggestions.push_back("检查时间源配置和信号质量");
    }
    if (score.signal_quality_score < 70) {
        score.suggestions.push_back("检查GNSS天线和接收机状态");
    }
    if (score.system_stability_score < 70) {
        score.suggestions.push_back("检查系统负载和环境温度");
    }
    
    return score;
}

PredictiveMaintenanceAdvice PrecisionMonitor::GenerateMaintenanceAdvice(const PrecisionTrend& trend,
                                                                        const SystemHealthScore& health_score) {
    PredictiveMaintenanceAdvice advice;
    advice.analysis_timestamp_ns = GetCurrentTimestampNs();
    
    // 基于健康评分估算故障概率
    if (health_score.overall_score >= 90) {
        advice.estimated_days_to_failure = 365;
        advice.failure_probability = 0.01;
        advice.primary_concern = "系统运行良好";
    } else if (health_score.overall_score >= 70) {
        advice.estimated_days_to_failure = 180;
        advice.failure_probability = 0.05;
        advice.primary_concern = "精度稳定性需要关注";
    } else if (health_score.overall_score >= 50) {
        advice.estimated_days_to_failure = 90;
        advice.failure_probability = 0.15;
        advice.primary_concern = "系统性能下降";
    } else {
        advice.estimated_days_to_failure = 30;
        advice.failure_probability = 0.30;
        advice.primary_concern = "系统存在严重问题";
    }
    
    // 组件预测
    PredictiveMaintenanceAdvice::ComponentPrediction gnss_pred;
    gnss_pred.component_name = "GNSS接收机";
    gnss_pred.health_percentage = health_score.signal_quality_score;
    gnss_pred.estimated_lifetime_days = health_score.signal_quality_score * 10; // 简化计算
    if (health_score.signal_quality_score < 50) {
        gnss_pred.maintenance_action = "检查天线连接和位置";
        gnss_pred.urgent = true;
    } else {
        gnss_pred.maintenance_action = "定期检查信号质量";
        gnss_pred.urgent = false;
    }
    advice.component_predictions.push_back(gnss_pred);
    
    PredictiveMaintenanceAdvice::ComponentPrediction rb_pred;
    rb_pred.component_name = "铷原子钟";
    rb_pred.health_percentage = health_score.hardware_health_score;
    rb_pred.estimated_lifetime_days = health_score.hardware_health_score * 20; // 简化计算
    if (health_score.hardware_health_score < 60) {
        rb_pred.maintenance_action = "检查温度控制和老化补偿";
        rb_pred.urgent = true;
    } else {
        rb_pred.maintenance_action = "监控温度和频率稳定性";
        rb_pred.urgent = false;
    }
    advice.component_predictions.push_back(rb_pred);
    
    // 立即行动建议
    if (health_score.overall_score < 50) {
        advice.immediate_actions.push_back("立即检查系统状态和错误日志");
        advice.immediate_actions.push_back("验证所有硬件连接");
    }
    
    // 计划行动建议
    if (!trend.within_specification) {
        advice.scheduled_actions.push_back("安排系统重新校准");
    }
    if (!trend.trend_is_stable) {
        advice.scheduled_actions.push_back("分析精度波动原因");
    }
    
    // 预防性行动建议
    advice.preventive_actions.push_back("定期备份配置和学习数据");
    advice.preventive_actions.push_back("建立性能基线和监控阈值");
    advice.preventive_actions.push_back("制定应急响应计划");
    
    // 性能优化建议
    if (health_score.performance_score < 80) {
        advice.performance_optimizations.push_back("优化系统资源使用");
        advice.performance_optimizations.push_back("调整监控频率和日志级别");
    }
    
    // 配置更改建议
    if (trend.accuracy_trend_ns_per_hour > 1.0) {
        advice.configuration_changes.push_back("调整驯服算法参数");
    }
    if (health_score.signal_quality_score < 70) {
        advice.configuration_changes.push_back("优化时间源优先级配置");
    }
    
    return advice;
}

void PrecisionMonitor::GetSystemResourceUsage(double& cpu_usage, uint64_t& memory_usage) {
    if (!system_resource_monitor_) {
        LOG_ERROR(LogComponent::SYSTEM, "系统资源监控器未初始化");
        cpu_usage = 0.0;
        memory_usage = 0;
        return;
    }
    
    try {
        cpu_usage = system_resource_monitor_->GetCpuUsage();
        memory_usage = system_resource_monitor_->GetMemoryUsage();
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "获取系统资源使用情况失败: " + std::string(e.what()));
        cpu_usage = 0.0;
        memory_usage = 0;
    }
}

uint32_t PrecisionMonitor::GetNetworkLatency() {
    if (!system_resource_monitor_) {
        LOG_WARNING(LogComponent::SYSTEM, "系统资源监控器未初始化");
        return 100; // 返回默认值
    }
    return system_resource_monitor_->GetNetworkLatency();
}

void PrecisionMonitor::StoreMeasurement(const PrecisionMeasurement& measurement) {
    std::lock_guard<std::mutex> lock(measurements_mutex_);
    
    measurement_history_.push(measurement);
    
    // 限制历史数据大小
    while (measurement_history_.size() > MAX_HISTORY_SIZE) {
        measurement_history_.pop();
    }
}

double PrecisionMonitor::GetCpuUsage() {
    if (!system_resource_monitor_) {
        LOG_WARNING(LogComponent::SYSTEM, "系统资源监控器未初始化");
        return 0.0;
    }
    return system_resource_monitor_->GetCpuUsage();
}

uint64_t PrecisionMonitor::GetMemoryUsage() {
    if (!system_resource_monitor_) {
        LOG_WARNING(LogComponent::SYSTEM, "系统资源监控器未初始化");
        return 0;
    }
    return system_resource_monitor_->GetMemoryUsage();
}

void PrecisionMonitor::NotifyMeasurementCallback(const PrecisionMeasurement& measurement) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    
    if (measurement_callback_) {
        try {
            measurement_callback_(measurement);
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "测量回调函数执行失败: " + std::string(e.what()));
        }
    }
}

} // namespace core
} // namespace timing_server