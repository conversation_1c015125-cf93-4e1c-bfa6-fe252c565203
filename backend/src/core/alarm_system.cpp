#include "core/alarm_system.h"
#include "core/logger.h"
#include <algorithm>
#include <fstream>
#include <sstream>
#include <iomanip>

namespace timing_server {
namespace core {

SmartAlarmSystem::SmartAlarmSystem(std::shared_ptr<IPrecisionMonitor> precision_monitor)
    : precision_monitor_(precision_monitor)
    , running_(false)
    , next_alarm_id_(1)
    , next_rule_id_(1) {
    
    LOG_INFO(LogComponent::SYSTEM, "智能告警系统已创建");
}

SmartAlarmSystem::~SmartAlarmSystem() {
    Stop();
    LOG_INFO(LogComponent::SYSTEM, "智能告警系统已销毁");
}

bool SmartAlarmSystem::Start() {
    std::lock_guard<std::mutex> lock(monitoring_mutex_);
    
    if (running_) {
        LOG_WARNING(LogComponent::SYSTEM, "告警系统已在运行中");
        return true;
    }
    
    if (!precision_monitor_) {
        LOG_ERROR(LogComponent::SYSTEM, "精度监控器实例无效，无法启动告警系统");
        return false;
    }
    
    running_ = true;
    
    // 设置精度测量回调
    precision_monitor_->SetMeasurementCallback(
        std::bind(&SmartAlarmSystem::OnPrecisionMeasurement, this, std::placeholders::_1));
    
    // 启动监控线程
    monitoring_thread_ = std::thread(&SmartAlarmSystem::MonitoringThread, this);
    
    // 创建默认告警规则
    CreateDefaultAlarmRules();
    
    LOG_INFO(LogComponent::SYSTEM, "智能告警系统已启动");
    return true;
}

bool SmartAlarmSystem::Stop() {
    std::unique_lock<std::mutex> lock(monitoring_mutex_);
    
    if (!running_) {
        return true;
    }
    
    running_ = false;
    monitoring_cv_.notify_all();
    lock.unlock();
    
    // 等待监控线程结束
    if (monitoring_thread_.joinable()) {
        monitoring_thread_.join();
    }
    
    LOG_INFO(LogComponent::SYSTEM, "智能告警系统已停止");
    return true;
}

uint64_t SmartAlarmSystem::TriggerAlarm(const AlarmEvent& alarm) {
    AlarmEvent new_alarm = alarm;
    new_alarm.id = GenerateAlarmId();
    new_alarm.timestamp_ns = GetCurrentTimestampNs();
    new_alarm.status = AlarmStatus::ACTIVE;
    new_alarm.first_occurrence_ns = new_alarm.timestamp_ns;
    new_alarm.last_occurrence_ns = new_alarm.timestamp_ns;
    
    // 检查是否为重复告警
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    bool is_duplicate = false;
    for (auto& [id, existing_alarm] : active_alarms_) {
        if (existing_alarm.type == new_alarm.type && 
            existing_alarm.source_component == new_alarm.source_component &&
            existing_alarm.status == AlarmStatus::ACTIVE) {
            
            // 更新重复告警信息
            existing_alarm.occurrence_count++;
            existing_alarm.last_occurrence_ns = new_alarm.timestamp_ns;
            existing_alarm.current_value = new_alarm.current_value;
            
            LOG_DEBUG(LogComponent::SYSTEM, "更新重复告警: " + new_alarm.title + 
                     " (发生次数: " + std::to_string(existing_alarm.occurrence_count) + ")");
            
            is_duplicate = true;
            new_alarm.id = existing_alarm.id;
            break;
        }
    }
    
    if (!is_duplicate) {
        // 检查告警抑制
        if (IsAlarmSuppressed(new_alarm)) {
            LOG_DEBUG(LogComponent::SYSTEM, "告警被抑制: " + new_alarm.title);
            return 0;
        }
        
        // 添加新告警
        active_alarms_[new_alarm.id] = new_alarm;
        
        LOG_INFO(LogComponent::SYSTEM, "触发新告警: [" + AlarmLevelToString(new_alarm.level) + "] " + 
                new_alarm.title + " - " + new_alarm.description);
        
        // 处理告警事件
        ProcessAlarmEvent(new_alarm);
    }
    
    return new_alarm.id;
}

bool SmartAlarmSystem::AcknowledgeAlarm(uint64_t alarm_id, const std::string& acknowledged_by, 
                                       const std::string& notes) {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    auto it = active_alarms_.find(alarm_id);
    if (it == active_alarms_.end()) {
        LOG_WARNING(LogComponent::SYSTEM, "尝试确认不存在的告警: " + std::to_string(alarm_id));
        return false;
    }
    
    if (it->second.status != AlarmStatus::ACTIVE) {
        LOG_WARNING(LogComponent::SYSTEM, "告警状态不是活跃状态，无法确认: " + std::to_string(alarm_id));
        return false;
    }
    
    it->second.status = AlarmStatus::ACKNOWLEDGED;
    it->second.acknowledged_time_ns = GetCurrentTimestampNs();
    it->second.acknowledged_by = acknowledged_by;
    if (!notes.empty()) {
        it->second.context["acknowledgment_notes"] = notes;
    }
    
    LOG_INFO(LogComponent::SYSTEM, "告警已确认: " + it->second.title + " (确认人: " + acknowledged_by + ")");
    
    // 更新统计信息
    UpdateAlarmStatistics(it->second);
    
    return true;
}

bool SmartAlarmSystem::ResolveAlarm(uint64_t alarm_id, const std::string& resolution_notes) {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    auto it = active_alarms_.find(alarm_id);
    if (it == active_alarms_.end()) {
        LOG_WARNING(LogComponent::SYSTEM, "尝试解决不存在的告警: " + std::to_string(alarm_id));
        return false;
    }
    
    it->second.status = AlarmStatus::RESOLVED;
    it->second.resolved_time_ns = GetCurrentTimestampNs();
    it->second.resolution_notes = resolution_notes;
    
    LOG_INFO(LogComponent::SYSTEM, "告警已解决: " + it->second.title);
    
    // 移动到历史记录
    alarm_history_.push(it->second);
    if (alarm_history_.size() > MAX_HISTORY_SIZE) {
        alarm_history_.pop();
    }
    
    // 从活跃告警中移除
    active_alarms_.erase(it);
    
    // 更新统计信息
    UpdateAlarmStatistics(it->second);
    
    return true;
}

bool SmartAlarmSystem::SuppressAlarm(uint64_t alarm_id, uint32_t duration_seconds) {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    auto it = active_alarms_.find(alarm_id);
    if (it == active_alarms_.end()) {
        LOG_WARNING(LogComponent::SYSTEM, "尝试抑制不存在的告警: " + std::to_string(alarm_id));
        return false;
    }
    
    it->second.status = AlarmStatus::SUPPRESSED;
    it->second.context["suppression_duration"] = std::to_string(duration_seconds);
    it->second.context["suppression_end_time"] = std::to_string(
        GetCurrentTimestampNs() + static_cast<uint64_t>(duration_seconds) * 1000000000ULL);
    
    LOG_INFO(LogComponent::SYSTEM, "告警已抑制: " + it->second.title + 
             " (持续时间: " + std::to_string(duration_seconds) + "秒)");
    
    return true;
}

std::vector<AlarmEvent> SmartAlarmSystem::GetActiveAlarms() {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    std::vector<AlarmEvent> result;
    for (const auto& [id, alarm] : active_alarms_) {
        result.push_back(alarm);
    }
    
    // 按时间戳排序（最新的在前）
    std::sort(result.begin(), result.end(),
              [](const AlarmEvent& a, const AlarmEvent& b) {
                  return a.timestamp_ns > b.timestamp_ns;
              });
    
    return result;
}

std::vector<AlarmEvent> SmartAlarmSystem::GetHistoricalAlarms(uint64_t start_time, uint64_t end_time, 
                                                             uint32_t max_count) {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    std::vector<AlarmEvent> result;
    
    // 从历史记录中筛选
    std::queue<AlarmEvent> temp_queue = alarm_history_;
    while (!temp_queue.empty()) {
        const auto& alarm = temp_queue.front();
        if (alarm.timestamp_ns >= start_time && alarm.timestamp_ns <= end_time) {
            result.push_back(alarm);
        }
        temp_queue.pop();
    }
    
    // 按时间戳排序（最新的在前）
    std::sort(result.begin(), result.end(),
              [](const AlarmEvent& a, const AlarmEvent& b) {
                  return a.timestamp_ns > b.timestamp_ns;
              });
    
    // 限制数量
    if (result.size() > max_count) {
        result.resize(max_count);
    }
    
    return result;
}

AlarmStatistics SmartAlarmSystem::GetAlarmStatistics() {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    
    // 更新当前统计信息
    AlarmStatistics current_stats = statistics_;
    
    std::lock_guard<std::mutex> alarms_lock(alarms_mutex_);
    current_stats.active_alarms = 0;
    current_stats.acknowledged_alarms = 0;
    current_stats.suppressed_alarms = 0;
    
    for (const auto& [id, alarm] : active_alarms_) {
        switch (alarm.status) {
            case AlarmStatus::ACTIVE:
                current_stats.active_alarms++;
                break;
            case AlarmStatus::ACKNOWLEDGED:
                current_stats.acknowledged_alarms++;
                break;
            case AlarmStatus::SUPPRESSED:
                current_stats.suppressed_alarms++;
                break;
            default:
                break;
        }
        
        current_stats.alarms_by_level[alarm.level]++;
        current_stats.alarms_by_type[alarm.type]++;
    }
    
    return current_stats;
}

uint64_t SmartAlarmSystem::AddAlarmRule(const AlarmRule& rule) {
    std::lock_guard<std::mutex> lock(rules_mutex_);
    
    AlarmRule new_rule = rule;
    new_rule.rule_id = next_rule_id_++;
    
    alarm_rules_[new_rule.rule_id] = new_rule;
    
    LOG_INFO(LogComponent::SYSTEM, "添加告警规则: " + new_rule.rule_name + 
             " (ID: " + std::to_string(new_rule.rule_id) + ")");
    
    return new_rule.rule_id;
}

bool SmartAlarmSystem::RemoveAlarmRule(uint64_t rule_id) {
    std::lock_guard<std::mutex> lock(rules_mutex_);
    
    auto it = alarm_rules_.find(rule_id);
    if (it == alarm_rules_.end()) {
        LOG_WARNING(LogComponent::SYSTEM, "尝试删除不存在的告警规则: " + std::to_string(rule_id));
        return false;
    }
    
    LOG_INFO(LogComponent::SYSTEM, "删除告警规则: " + it->second.rule_name);
    alarm_rules_.erase(it);
    
    return true;
}

bool SmartAlarmSystem::UpdateAlarmRule(const AlarmRule& rule) {
    std::lock_guard<std::mutex> lock(rules_mutex_);
    
    auto it = alarm_rules_.find(rule.rule_id);
    if (it == alarm_rules_.end()) {
        LOG_WARNING(LogComponent::SYSTEM, "尝试更新不存在的告警规则: " + std::to_string(rule.rule_id));
        return false;
    }
    
    it->second = rule;
    LOG_INFO(LogComponent::SYSTEM, "更新告警规则: " + rule.rule_name);
    
    return true;
}

std::vector<AlarmRule> SmartAlarmSystem::GetAlarmRules() {
    std::lock_guard<std::mutex> lock(rules_mutex_);
    
    std::vector<AlarmRule> result;
    for (const auto& [id, rule] : alarm_rules_) {
        result.push_back(rule);
    }
    
    return result;
}

void SmartAlarmSystem::RegisterNotifier(std::shared_ptr<IAlarmNotifier> notifier) {
    std::lock_guard<std::mutex> lock(notifiers_mutex_);
    
    notifiers_.push_back(notifier);
    
    LOG_INFO(LogComponent::SYSTEM, "注册告警通知器: " + 
             std::to_string(static_cast<int>(notifier->GetNotificationMethod())));
}

void SmartAlarmSystem::SetAlarmCallback(std::function<void(const AlarmEvent&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    alarm_callback_ = callback;
    LOG_INFO(LogComponent::SYSTEM, "告警回调函数已设置");
}

void SmartAlarmSystem::ClearHistoricalAlarms(uint32_t older_than_hours) {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    uint64_t cutoff_time = GetCurrentTimestampNs() - 
                          (static_cast<uint64_t>(older_than_hours) * 3600ULL * 1000000000ULL);
    
    std::queue<AlarmEvent> new_queue;
    size_t removed_count = 0;
    
    while (!alarm_history_.empty()) {
        auto alarm = alarm_history_.front();
        alarm_history_.pop();
        
        if (alarm.timestamp_ns >= cutoff_time) {
            new_queue.push(alarm);
        } else {
            removed_count++;
        }
    }
    
    alarm_history_ = std::move(new_queue);
    
    LOG_INFO(LogComponent::SYSTEM, "已清除 " + std::to_string(removed_count) + 
             " 个历史告警（超过 " + std::to_string(older_than_hours) + " 小时）");
}

bool SmartAlarmSystem::ExportAlarmData(const std::string& filename, const std::string& format) {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            LOG_ERROR(LogComponent::SYSTEM, "无法打开导出文件: " + filename);
            return false;
        }
        
        if (format == "csv") {
            // CSV格式导出
            file << "id,timestamp,type,level,status,title,description,source_component,"
                 << "threshold_value,current_value,unit,occurrence_count,acknowledged_by,resolution_notes\n";
            
            // 导出活跃告警
            for (const auto& [id, alarm] : active_alarms_) {
                file << alarm.id << ","
                     << alarm.timestamp_ns << ","
                     << AlarmTypeToString(alarm.type) << ","
                     << AlarmLevelToString(alarm.level) << ","
                     << AlarmStatusToString(alarm.status) << ","
                     << "\"" << alarm.title << "\","
                     << "\"" << alarm.description << "\","
                     << alarm.source_component << ","
                     << alarm.threshold_value << ","
                     << alarm.current_value << ","
                     << alarm.unit << ","
                     << alarm.occurrence_count << ","
                     << alarm.acknowledged_by << ","
                     << "\"" << alarm.resolution_notes << "\"\n";
            }
            
            // 导出历史告警
            std::queue<AlarmEvent> temp_queue = alarm_history_;
            while (!temp_queue.empty()) {
                const auto& alarm = temp_queue.front();
                file << alarm.id << ","
                     << alarm.timestamp_ns << ","
                     << AlarmTypeToString(alarm.type) << ","
                     << AlarmLevelToString(alarm.level) << ","
                     << AlarmStatusToString(alarm.status) << ","
                     << "\"" << alarm.title << "\","
                     << "\"" << alarm.description << "\","
                     << alarm.source_component << ","
                     << alarm.threshold_value << ","
                     << alarm.current_value << ","
                     << alarm.unit << ","
                     << alarm.occurrence_count << ","
                     << alarm.acknowledged_by << ","
                     << "\"" << alarm.resolution_notes << "\"\n";
                temp_queue.pop();
            }
        } else if (format == "json") {
            // JSON格式导出
            file << "{\n  \"active_alarms\": [\n";
            
            bool first = true;
            for (const auto& [id, alarm] : active_alarms_) {
                if (!first) file << ",\n";
                first = false;
                
                file << "    {\n"
                     << "      \"id\": " << alarm.id << ",\n"
                     << "      \"timestamp\": " << alarm.timestamp_ns << ",\n"
                     << "      \"type\": \"" << AlarmTypeToString(alarm.type) << "\",\n"
                     << "      \"level\": \"" << AlarmLevelToString(alarm.level) << "\",\n"
                     << "      \"status\": \"" << AlarmStatusToString(alarm.status) << "\",\n"
                     << "      \"title\": \"" << alarm.title << "\",\n"
                     << "      \"description\": \"" << alarm.description << "\",\n"
                     << "      \"occurrence_count\": " << alarm.occurrence_count << "\n"
                     << "    }";
            }
            
            file << "\n  ],\n  \"historical_alarms\": [\n";
            
            std::queue<AlarmEvent> temp_queue = alarm_history_;
            first = true;
            while (!temp_queue.empty()) {
                const auto& alarm = temp_queue.front();
                if (!first) file << ",\n";
                first = false;
                
                file << "    {\n"
                     << "      \"id\": " << alarm.id << ",\n"
                     << "      \"timestamp\": " << alarm.timestamp_ns << ",\n"
                     << "      \"type\": \"" << AlarmTypeToString(alarm.type) << "\",\n"
                     << "      \"level\": \"" << AlarmLevelToString(alarm.level) << "\",\n"
                     << "      \"title\": \"" << alarm.title << "\"\n"
                     << "    }";
                temp_queue.pop();
            }
            
            file << "\n  ]\n}";
        } else {
            LOG_ERROR(LogComponent::SYSTEM, "不支持的导出格式: " + format);
            return false;
        }
        
        file.close();
        LOG_INFO(LogComponent::SYSTEM, "告警数据已导出到: " + filename);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "导出告警数据失败: " + std::string(e.what()));
        return false;
    }
}

void SmartAlarmSystem::CreateDefaultAlarmRules() {
    std::vector<AlarmRule> default_rules;
    
    // 精度相关告警规则
    AlarmRule accuracy_rule;
    accuracy_rule.rule_name = "时间精度超限告警";
    accuracy_rule.alarm_type = AlarmType::ACCURACY_DEGRADED;
    accuracy_rule.alarm_level = AlarmLevel::ERROR;
    accuracy_rule.metric_name = "absolute_accuracy_ns";
    accuracy_rule.condition = ">";
    accuracy_rule.threshold_value = 100.0; // 100ns
    accuracy_rule.duration_seconds = 30;
    accuracy_rule.notification_methods = {NotificationMethod::LOG_ONLY, NotificationMethod::CONSOLE};
    default_rules.push_back(accuracy_rule);
    
    // 相位偏移告警规则
    AlarmRule phase_rule;
    phase_rule.rule_name = "相位偏移过大告警";
    phase_rule.alarm_type = AlarmType::PHASE_OFFSET_HIGH;
    phase_rule.alarm_level = AlarmLevel::WARNING;
    phase_rule.metric_name = "phase_offset_ns";
    phase_rule.condition = ">";
    phase_rule.threshold_value = 500.0; // 500ns
    phase_rule.duration_seconds = 60;
    phase_rule.notification_methods = {NotificationMethod::LOG_ONLY};
    default_rules.push_back(phase_rule);
    
    // GNSS卫星数量告警规则
    AlarmRule gnss_rule;
    gnss_rule.rule_name = "GNSS卫星数量不足告警";
    gnss_rule.alarm_type = AlarmType::GNSS_SATELLITES_LOW;
    gnss_rule.alarm_level = AlarmLevel::WARNING;
    gnss_rule.metric_name = "gnss_satellites";
    gnss_rule.condition = "<";
    gnss_rule.threshold_value = 6.0;
    gnss_rule.duration_seconds = 120;
    gnss_rule.notification_methods = {NotificationMethod::LOG_ONLY, NotificationMethod::CONSOLE};
    default_rules.push_back(gnss_rule);
    
    // 铷钟温度告警规则
    AlarmRule temp_rule;
    temp_rule.rule_name = "铷钟温度过高告警";
    temp_rule.alarm_type = AlarmType::RUBIDIUM_TEMPERATURE_HIGH;
    temp_rule.alarm_level = AlarmLevel::ERROR;
    temp_rule.metric_name = "rubidium_temperature";
    temp_rule.condition = ">";
    temp_rule.threshold_value = 70.0; // 70°C
    temp_rule.duration_seconds = 60;
    temp_rule.notification_methods = {NotificationMethod::LOG_ONLY, NotificationMethod::CONSOLE};
    default_rules.push_back(temp_rule);
    
    // CPU使用率告警规则
    AlarmRule cpu_rule;
    cpu_rule.rule_name = "CPU使用率过高告警";
    cpu_rule.alarm_type = AlarmType::CPU_USAGE_HIGH;
    cpu_rule.alarm_level = AlarmLevel::WARNING;
    cpu_rule.metric_name = "cpu_usage_percent";
    cpu_rule.condition = ">";
    cpu_rule.threshold_value = 80.0; // 80%
    cpu_rule.duration_seconds = 300; // 5分钟
    cpu_rule.notification_methods = {NotificationMethod::LOG_ONLY};
    default_rules.push_back(cpu_rule);
    
    // 添加默认规则
    for (const auto& rule : default_rules) {
        AddAlarmRule(rule);
    }
    
    LOG_INFO(LogComponent::SYSTEM, "已创建 " + std::to_string(default_rules.size()) + " 个默认告警规则");
}

void SmartAlarmSystem::MonitoringThread() {
    LOG_INFO(LogComponent::SYSTEM, "告警监控线程已启动");
    
    while (running_) {
        try {
            // 自动解决过期告警
            AutoResolveExpiredAlarms();
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "告警监控线程异常: " + std::string(e.what()));
        }
        
        // 等待下次检查
        std::unique_lock<std::mutex> lock(monitoring_mutex_);
        monitoring_cv_.wait_for(lock, std::chrono::milliseconds(MONITORING_INTERVAL_MS), 
                               [this] { return !running_; });
    }
    
    LOG_INFO(LogComponent::SYSTEM, "告警监控线程已结束");
}

void SmartAlarmSystem::EvaluateAlarmRules(const PrecisionMeasurement& measurement) {
    std::lock_guard<std::mutex> lock(rules_mutex_);
    
    for (const auto& [rule_id, rule] : alarm_rules_) {
        if (!rule.enabled) {
            continue;
        }
        
        if (CheckAlarmRule(rule, measurement)) {
            // 检查规则触发间隔
            auto now = std::chrono::steady_clock::now();
            auto last_triggered_it = rule_last_triggered_.find(rule_id);
            
            if (last_triggered_it != rule_last_triggered_.end()) {
                auto time_since_last = std::chrono::duration_cast<std::chrono::seconds>(
                    now - last_triggered_it->second).count();
                
                if (time_since_last < rule.evaluation_interval_seconds) {
                    continue; // 还未到评估间隔
                }
            }
            
            // 创建告警事件
            AlarmEvent alarm(rule.alarm_type, rule.alarm_level, rule.rule_name);
            alarm.description = "指标 " + rule.metric_name + " " + rule.condition + " " + 
                               std::to_string(rule.threshold_value);
            alarm.source_component = "PrecisionMonitor";
            alarm.threshold_value = rule.threshold_value;
            alarm.current_value = GetMetricValue(rule.metric_name, measurement);
            alarm.unit = (rule.metric_name.find("_ns") != std::string::npos) ? "ns" :
                        (rule.metric_name.find("_ppm") != std::string::npos) ? "ppm" :
                        (rule.metric_name.find("_percent") != std::string::npos) ? "%" : "";
            
            // 添加上下文信息
            alarm.context["rule_id"] = std::to_string(rule_id);
            alarm.context["metric_name"] = rule.metric_name;
            alarm.context["condition"] = rule.condition;
            alarm.context["system_state"] = ClockStateToString(measurement.system_state);
            alarm.context["time_source"] = TimeSourceToString(measurement.source);
            
            // 触发告警
            TriggerAlarm(alarm);
            
            // 更新最后触发时间
            rule_last_triggered_[rule_id] = now;
        }
    }
}

bool SmartAlarmSystem::CheckAlarmRule(const AlarmRule& rule, const PrecisionMeasurement& measurement) {
    double metric_value = GetMetricValue(rule.metric_name, measurement);
    
    if (rule.condition == ">") {
        return metric_value > rule.threshold_value;
    } else if (rule.condition == "<") {
        return metric_value < rule.threshold_value;
    } else if (rule.condition == "==") {
        return std::abs(metric_value - rule.threshold_value) < 1e-9;
    } else if (rule.condition == ">=") {
        return metric_value >= rule.threshold_value;
    } else if (rule.condition == "<=") {
        return metric_value <= rule.threshold_value;
    } else if (rule.condition == "!=") {
        return std::abs(metric_value - rule.threshold_value) >= 1e-9;
    }
    
    return false;
}

void SmartAlarmSystem::ProcessAlarmEvent(AlarmEvent& alarm) {
    // 发送通知
    SendAlarmNotifications(alarm);
    
    // 更新统计信息
    UpdateAlarmStatistics(alarm);
    
    // 调用回调函数
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (alarm_callback_) {
        try {
            alarm_callback_(alarm);
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "告警回调函数执行失败: " + std::string(e.what()));
        }
    }
}

void SmartAlarmSystem::SendAlarmNotifications(const AlarmEvent& alarm) {
    std::lock_guard<std::mutex> lock(notifiers_mutex_);
    
    for (auto& notifier : notifiers_) {
        if (!notifier->IsAvailable()) {
            continue;
        }
        
        try {
            std::vector<std::string> targets; // 可以从告警规则中获取
            bool success = notifier->SendNotification(alarm, targets);
            
            std::lock_guard<std::mutex> stats_lock(statistics_mutex_);
            if (success) {
                statistics_.notifications_sent++;
            } else {
                statistics_.notification_failures++;
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "发送告警通知失败: " + std::string(e.what()));
            std::lock_guard<std::mutex> stats_lock(statistics_mutex_);
            statistics_.notification_failures++;
        }
    }
}

bool SmartAlarmSystem::IsAlarmSuppressed(const AlarmEvent& alarm) {
    // 检查是否有相同类型的告警被抑制
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    for (const auto& [id, existing_alarm] : active_alarms_) {
        if (existing_alarm.type == alarm.type && 
            existing_alarm.status == AlarmStatus::SUPPRESSED) {
            
            // 检查抑制是否过期
            auto suppression_end_it = existing_alarm.context.find("suppression_end_time");
            if (suppression_end_it != existing_alarm.context.end()) {
                uint64_t suppression_end_time = std::stoull(suppression_end_it->second);
                if (GetCurrentTimestampNs() < suppression_end_time) {
                    return true; // 仍在抑制期内
                }
            }
        }
    }
    
    return false;
}

void SmartAlarmSystem::UpdateAlarmStatistics(const AlarmEvent& alarm) {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    
    statistics_.total_alarms++;
    statistics_.alarms_by_level[alarm.level]++;
    statistics_.alarms_by_type[alarm.type]++;
    
    // 计算平均解决时间
    if (alarm.status == AlarmStatus::RESOLVED && alarm.resolved_time_ns > alarm.timestamp_ns) {
        double resolution_time_minutes = static_cast<double>(alarm.resolved_time_ns - alarm.timestamp_ns) / 60000000000.0;
        statistics_.average_resolution_time_minutes = 
            (statistics_.average_resolution_time_minutes * (statistics_.resolved_alarms - 1) + resolution_time_minutes) / 
            statistics_.resolved_alarms;
    }
    
    // 计算平均确认时间
    if (alarm.status == AlarmStatus::ACKNOWLEDGED && alarm.acknowledged_time_ns > alarm.timestamp_ns) {
        double acknowledgment_time_minutes = static_cast<double>(alarm.acknowledged_time_ns - alarm.timestamp_ns) / 60000000000.0;
        statistics_.average_acknowledgment_time_minutes = 
            (statistics_.average_acknowledgment_time_minutes * (statistics_.acknowledged_alarms - 1) + acknowledgment_time_minutes) / 
            statistics_.acknowledged_alarms;
    }
}

void SmartAlarmSystem::AutoResolveExpiredAlarms() {
    std::lock_guard<std::mutex> lock(alarms_mutex_);
    
    std::vector<uint64_t> alarms_to_resolve;
    uint64_t current_time = GetCurrentTimestampNs();
    
    for (const auto& [id, alarm] : active_alarms_) {
        // 检查抑制过期
        if (alarm.status == AlarmStatus::SUPPRESSED) {
            auto suppression_end_it = alarm.context.find("suppression_end_time");
            if (suppression_end_it != alarm.context.end()) {
                uint64_t suppression_end_time = std::stoull(suppression_end_it->second);
                if (current_time >= suppression_end_time) {
                    // 抑制过期，恢复为活跃状态
                    const_cast<AlarmEvent&>(alarm).status = AlarmStatus::ACTIVE;
                    LOG_INFO(LogComponent::SYSTEM, "告警抑制已过期，恢复活跃状态: " + alarm.title);
                }
            }
        }
        
        // 检查自动解决
        // 这里可以添加更复杂的自动解决逻辑
        // 例如：如果精度恢复正常，自动解决精度相关告警
    }
}

uint64_t SmartAlarmSystem::GenerateAlarmId() {
    return next_alarm_id_++;
}

double SmartAlarmSystem::GetMetricValue(const std::string& metric_name, const PrecisionMeasurement& measurement) {
    if (metric_name == "absolute_accuracy_ns") {
        return std::abs(measurement.absolute_accuracy_ns);
    } else if (metric_name == "phase_offset_ns") {
        return std::abs(measurement.phase_offset_ns);
    } else if (metric_name == "frequency_offset_ppm") {
        return std::abs(measurement.frequency_offset_ppm);
    } else if (metric_name == "allan_deviation_1s") {
        return measurement.allan_deviation_1s;
    } else if (metric_name == "gnss_satellites") {
        return static_cast<double>(measurement.gnss_satellites);
    } else if (metric_name == "gnss_snr_db") {
        return measurement.gnss_snr_db;
    } else if (metric_name == "rubidium_temperature") {
        return measurement.rubidium_temperature;
    } else if (metric_name == "system_temperature") {
        return measurement.system_temperature;
    } else if (metric_name == "cpu_usage_percent") {
        return measurement.cpu_usage_percent;
    } else if (metric_name == "memory_usage_mb") {
        return static_cast<double>(measurement.memory_usage_mb);
    } else if (metric_name == "network_latency_us") {
        return static_cast<double>(measurement.network_latency_us);
    } else if (metric_name == "overall_quality_score") {
        return static_cast<double>(measurement.overall_quality_score);
    }
    
    return 0.0;
}

void SmartAlarmSystem::OnPrecisionMeasurement(const PrecisionMeasurement& measurement) {
    try {
        EvaluateAlarmRules(measurement);
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "评估告警规则时发生错误: " + std::string(e.what()));
    }
}

} // namespace core
} // namespace timing_server