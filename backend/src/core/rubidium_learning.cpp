#include "core/rubidium_learning.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <iomanip>
#include <cstring>
#include <zlib.h>

namespace timing_server {
namespace core {

// RubidiumLearningData实现

RubidiumLearningData::RubidiumLearningData() {
    timestamp_ns = GetCurrentTimestampNs();
    learning_duration_ns = 0;
    sample_count = 0;
    
    base_frequency_hz = 10000000.0; // 10MHz
    frequency_offset_ppm = 0.0;
    frequency_drift_rate_ppm_per_hour = 0.0;
    frequency_stability_ppm = 0.0;
    
    reference_temperature_c = 25.0;
    temperature_coefficient_ppm_per_c = 0.0;
    temperature_coefficient_2nd_ppm_per_c2 = 0.0;
    
    aging_rate_ppm_per_day = 0.0;
    aging_acceleration_factor = 1.0;
    operating_days = 0;
    
    humidity_coefficient_ppm_per_percent = 0.0;
    pressure_coefficient_ppm_per_hpa = 0.0;
    vibration_sensitivity_ppm_per_g = 0.0;
    
    allan_deviation_1s = 0.0;
    allan_deviation_10s = 0.0;
    allan_deviation_100s = 0.0;
    allan_deviation_1000s = 0.0;
    
    confidence_level = 0.0;
    prediction_accuracy = 0.0;
    validation_score = 0;
    
    phase_correction_ns = 0.0;
    frequency_correction_ppm = 0.0;
    correction_enabled = false;
}

bool RubidiumLearningData::IsValid() const {
    // 检查基本参数范围
    if (base_frequency_hz <= 0 || base_frequency_hz > 1e12) return false;
    if (std::abs(frequency_offset_ppm) > 1000) return false;
    if (std::abs(frequency_drift_rate_ppm_per_hour) > 100) return false;
    if (frequency_stability_ppm < 0 || frequency_stability_ppm > 1000) return false;
    
    // 检查温度参数
    if (reference_temperature_c < -50 || reference_temperature_c > 100) return false;
    if (std::abs(temperature_coefficient_ppm_per_c) > 10) return false;
    
    // 检查Allan偏差
    if (allan_deviation_1s < 0 || allan_deviation_10s < 0 || 
        allan_deviation_100s < 0 || allan_deviation_1000s < 0) return false;
    
    // 检查置信度和质量评分
    if (confidence_level < 0.0 || confidence_level > 1.0) return false;
    if (validation_score > 100) return false;
    
    // 检查时间戳
    if (timestamp_ns == 0 || learning_duration_ns == 0) return false;
    
    return true;
}

uint32_t RubidiumLearningData::CalculateQualityScore() const {
    if (!IsValid()) return 0;
    
    uint32_t score = 0;
    
    // 基于学习持续时间评分（最多30分）
    uint64_t learning_hours = learning_duration_ns / (3600ULL * 1000000000ULL);
    if (learning_hours >= 72) score += 30;
    else if (learning_hours >= 24) score += 20;
    else if (learning_hours >= 8) score += 10;
    else score += 5;
    
    // 基于采样数量评分（最多20分）
    if (sample_count >= 1000) score += 20;
    else if (sample_count >= 500) score += 15;
    else if (sample_count >= 100) score += 10;
    else score += 5;
    
    // 基于频率稳定度评分（最多25分）
    if (frequency_stability_ppm <= 1e-12) score += 25;
    else if (frequency_stability_ppm <= 1e-11) score += 20;
    else if (frequency_stability_ppm <= 1e-10) score += 15;
    else if (frequency_stability_ppm <= 1e-9) score += 10;
    else score += 5;
    
    // 基于Allan偏差评分（最多15分）
    if (allan_deviation_1s > 0 && allan_deviation_1s <= 1e-12) score += 15;
    else if (allan_deviation_1s <= 1e-11) score += 10;
    else if (allan_deviation_1s <= 1e-10) score += 5;
    
    // 基于置信度评分（最多10分）
    score += static_cast<uint32_t>(confidence_level * 10);
    
    return std::min(score, 100U);
}

std::string RubidiumLearningData::GetSummary() const {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(3);
    
    oss << "铷钟学习数据摘要:\n";
    oss << "  时间戳: " << TimestampToIsoString(timestamp_ns) << "\n";
    oss << "  学习时长: " << (learning_duration_ns / 3600000000000ULL) << " 小时\n";
    oss << "  采样数量: " << sample_count << "\n";
    oss << "  频率偏移: " << frequency_offset_ppm << " ppm\n";
    oss << "  频率稳定度: " << frequency_stability_ppm << " ppm\n";
    oss << "  温度系数: " << temperature_coefficient_ppm_per_c << " ppm/°C\n";
    oss << "  老化率: " << aging_rate_ppm_per_day << " ppm/天\n";
    oss << "  Allan偏差(1s): " << allan_deviation_1s << "\n";
    oss << "  置信度: " << (confidence_level * 100) << "%\n";
    oss << "  质量评分: " << CalculateQualityScore() << "/100\n";
    
    return oss.str();
}

// RubidiumLearningHistory实现

const RubidiumLearningData* RubidiumLearningHistory::GetLatestData() const {
    if (learning_records.empty()) {
        return nullptr;
    }
    
    // 找到时间戳最新的记录
    auto latest_it = std::max_element(learning_records.begin(), learning_records.end(),
        [](const RubidiumLearningData& a, const RubidiumLearningData& b) {
            return a.timestamp_ns < b.timestamp_ns;
        });
    
    return &(*latest_it);
}

std::vector<RubidiumLearningData> RubidiumLearningHistory::GetDataInRange(
    uint64_t start_time, uint64_t end_time) const {
    
    std::vector<RubidiumLearningData> result;
    
    for (const auto& record : learning_records) {
        if (record.timestamp_ns >= start_time && record.timestamp_ns <= end_time) {
            result.push_back(record);
        }
    }
    
    // 按时间戳排序
    std::sort(result.begin(), result.end(),
        [](const RubidiumLearningData& a, const RubidiumLearningData& b) {
            return a.timestamp_ns < b.timestamp_ns;
        });
    
    return result;
}

RubidiumLearningHistory::TrendAnalysis RubidiumLearningHistory::AnalyzeTrends() const {
    TrendAnalysis analysis = {0.0, 0.0, 0.0, 0.0};
    
    if (learning_records.size() < 2) {
        return analysis;
    }
    
    // 准备数据点
    std::vector<std::pair<uint64_t, double>> frequency_points;
    std::vector<std::pair<uint64_t, double>> temperature_points;
    std::vector<std::pair<uint64_t, double>> aging_points;
    std::vector<std::pair<uint64_t, double>> stability_points;
    
    for (const auto& record : learning_records) {
        frequency_points.emplace_back(record.timestamp_ns, record.frequency_offset_ppm);
        temperature_points.emplace_back(record.timestamp_ns, record.reference_temperature_c);
        aging_points.emplace_back(record.timestamp_ns, record.aging_rate_ppm_per_day);
        stability_points.emplace_back(record.timestamp_ns, record.frequency_stability_ppm);
    }
    
    // 计算线性回归斜率作为趋势
    auto calculate_trend = [](const std::vector<std::pair<uint64_t, double>>& points) -> double {
        if (points.size() < 2) return 0.0;
        
        double sum_x = 0, sum_y = 0, sum_xy = 0, sum_x2 = 0;
        size_t n = points.size();
        
        for (const auto& point : points) {
            double x = static_cast<double>(point.first);
            double y = point.second;
            sum_x += x;
            sum_y += y;
            sum_xy += x * y;
            sum_x2 += x * x;
        }
        
        double denominator = n * sum_x2 - sum_x * sum_x;
        if (std::abs(denominator) < 1e-10) return 0.0;
        
        return (n * sum_xy - sum_x * sum_y) / denominator;
    };
    
    analysis.frequency_drift_trend = calculate_trend(frequency_points);
    analysis.temperature_trend = calculate_trend(temperature_points);
    analysis.aging_trend = calculate_trend(aging_points);
    analysis.stability_trend = calculate_trend(stability_points);
    
    return analysis;
}

// RubidiumDataFileHeader实现

uint32_t RubidiumDataFileHeader::CalculateChecksum() const {
    // 简单的CRC32校验和计算
    const uint8_t* data = reinterpret_cast<const uint8_t*>(this);
    size_t size = sizeof(RubidiumDataFileHeader) - sizeof(checksum);
    
    uint32_t crc = 0xFFFFFFFF;
    for (size_t i = 0; i < size; ++i) {
        crc ^= data[i];
        for (int j = 0; j < 8; ++j) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc >>= 1;
            }
        }
    }
    
    return ~crc;
}

bool RubidiumDataFileHeader::IsValid() const {
    if (magic != MAGIC_NUMBER) return false;
    if (version != VERSION) return false;
    if (header_size != sizeof(RubidiumDataFileHeader)) return false;
    if (checksum != CalculateChecksum()) return false;
    
    return true;
}

// RubidiumLearningStorage实现

RubidiumLearningStorage::RubidiumLearningStorage(const std::string& storage_path, 
                                               const std::string& device_id)
    : storage_path_(storage_path), device_id_(device_id), initialized_(false),
      max_records_(1000), auto_backup_interval_hours_(24), compression_enabled_(false),
      cache_last_update_(0) {
}

RubidiumLearningStorage::~RubidiumLearningStorage() {
    Shutdown();
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::Initialize(
    const std::string& serial_number, const std::string& firmware_version) {
    
    std::lock_guard<std::mutex> lock(storage_mutex_);
    
    if (initialized_.load()) {
        return StorageResult::SUCCESS;
    }
    
    serial_number_ = serial_number;
    firmware_version_ = firmware_version;
    
    // 创建存储目录
    StorageResult result = CreateStorageDirectory();
    if (result != StorageResult::SUCCESS) {
        return result;
    }
    
    // 验证现有文件完整性
    std::string main_file = GetMainFilePath();
    if (std::filesystem::exists(main_file)) {
        result = VerifyIntegrity(true); // 如果损坏则尝试修复
        if (result != StorageResult::SUCCESS) {
            LOG_WARNING(LogComponent::HAL_RUBIDIUM, 
                       "主存储文件损坏，尝试从备份恢复");
            
            // 尝试从备份恢复
            for (uint32_t i = 0; i < 3; ++i) {
                std::string backup_file = GetBackupFilePath(i);
                if (std::filesystem::exists(backup_file)) {
                    result = RestoreFromBackup(backup_file);
                    if (result == StorageResult::SUCCESS) {
                        LOG_INFO(LogComponent::HAL_RUBIDIUM, 
                                "从备份文件恢复成功: " + backup_file);
                        break;
                    }
                }
            }
        }
    }
    
    initialized_.store(true);
    LOG_INFO(LogComponent::HAL_RUBIDIUM, 
             "铷钟学习数据存储系统初始化成功: " + storage_path_);
    
    return StorageResult::SUCCESS;
}

void RubidiumLearningStorage::Shutdown() {
    if (!initialized_.load()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(storage_mutex_);
    
    // 清理缓存
    ClearCache();
    
    initialized_.store(false);
    LOG_INFO(LogComponent::HAL_RUBIDIUM, "铷钟学习数据存储系统已关闭");
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::SaveLearningData(
    const RubidiumLearningData& data, bool create_backup) {
    
    if (!initialized_.load()) {
        return StorageResult::ERROR_INVALID_PARAMETER;
    }
    
    if (!data.IsValid()) {
        LOG_ERROR(LogComponent::HAL_RUBIDIUM, "学习数据无效，拒绝保存");
        return StorageResult::ERROR_INVALID_PARAMETER;
    }
    
    std::lock_guard<std::mutex> lock(storage_mutex_);
    
    // 创建备份（如果需要）
    if (create_backup) {
        std::string main_file = GetMainFilePath();
        if (std::filesystem::exists(main_file)) {
            CreateBackup(GetBackupFilePath());
        }
    }
    
    // 加载现有历史数据
    auto [load_result, history] = LoadLearningHistory();
    if (load_result != StorageResult::SUCCESS && load_result != StorageResult::ERROR_FILE_NOT_FOUND) {
        return load_result;
    }
    
    // 添加新数据
    history.device_id = device_id_;
    history.serial_number = serial_number_;
    history.firmware_version = firmware_version_;
    history.last_update_time = GetCurrentTimestampNs();
    
    if (history.first_learning_time == 0) {
        history.first_learning_time = data.timestamp_ns;
    }
    
    history.learning_records.push_back(data);
    
    // 限制记录数量
    if (history.learning_records.size() > max_records_) {
        // 删除最旧的记录
        std::sort(history.learning_records.begin(), history.learning_records.end(),
            [](const RubidiumLearningData& a, const RubidiumLearningData& b) {
                return a.timestamp_ns < b.timestamp_ns;
            });
        
        history.learning_records.erase(history.learning_records.begin(),
            history.learning_records.begin() + (history.learning_records.size() - max_records_));
    }
    
    // 写入文件
    std::string temp_file = GetTempFilePath();
    std::ofstream file(temp_file, std::ios::binary);
    if (!file.is_open()) {
        LOG_ERROR(LogComponent::HAL_RUBIDIUM, "无法创建临时文件: " + temp_file);
        return StorageResult::ERROR_WRITE_FAILED;
    }
    
    // 准备文件头
    RubidiumDataFileHeader header = {};
    header.magic = RubidiumDataFileHeader::MAGIC_NUMBER;
    header.version = RubidiumDataFileHeader::VERSION;
    header.header_size = sizeof(RubidiumDataFileHeader);
    header.record_count = static_cast<uint32_t>(history.learning_records.size());
    header.creation_time = history.first_learning_time;
    header.last_modified_time = history.last_update_time;
    
    strncpy(header.device_id, device_id_.c_str(), sizeof(header.device_id) - 1);
    strncpy(header.serial_number, serial_number_.c_str(), sizeof(header.serial_number) - 1);
    strncpy(header.firmware_version, firmware_version_.c_str(), sizeof(header.firmware_version) - 1);
    
    header.checksum = header.CalculateChecksum();
    
    // 写入头部
    StorageResult result = WriteFileHeader(file, header);
    if (result != StorageResult::SUCCESS) {
        file.close();
        std::filesystem::remove(temp_file);
        return result;
    }
    
    // 写入数据记录
    for (const auto& record : history.learning_records) {
        result = WriteLearningRecord(file, record);
        if (result != StorageResult::SUCCESS) {
            file.close();
            std::filesystem::remove(temp_file);
            return result;
        }
    }
    
    file.close();
    
    // 原子性替换主文件
    std::string main_file = GetMainFilePath();
    std::error_code ec;
    std::filesystem::rename(temp_file, main_file, ec);
    if (ec) {
        LOG_ERROR(LogComponent::HAL_RUBIDIUM, 
                 "替换主文件失败: " + ec.message());
        std::filesystem::remove(temp_file);
        return StorageResult::ERROR_WRITE_FAILED;
    }
    
    // 更新缓存
    UpdateCache(history);
    
    LOG_INFO(LogComponent::HAL_RUBIDIUM, 
             "铷钟学习数据保存成功，记录数: " + std::to_string(history.learning_records.size()));
    
    return StorageResult::SUCCESS;
}

std::pair<RubidiumLearningStorage::StorageResult, RubidiumLearningHistory> 
RubidiumLearningStorage::LoadLearningHistory() {
    
    RubidiumLearningHistory history;
    
    if (!initialized_.load()) {
        return {StorageResult::ERROR_INVALID_PARAMETER, history};
    }
    
    std::lock_guard<std::mutex> lock(storage_mutex_);
    
    // 检查缓存
    if (cached_history_ && 
        (GetCurrentTimestampNs() - cache_last_update_) < 60000000000ULL) { // 1分钟缓存
        return {StorageResult::SUCCESS, *cached_history_};
    }
    
    std::string main_file = GetMainFilePath();
    if (!std::filesystem::exists(main_file)) {
        return {StorageResult::ERROR_FILE_NOT_FOUND, history};
    }
    
    std::ifstream file(main_file, std::ios::binary);
    if (!file.is_open()) {
        LOG_ERROR(LogComponent::HAL_RUBIDIUM, "无法打开存储文件: " + main_file);
        return {StorageResult::ERROR_READ_FAILED, history};
    }
    
    // 读取文件头
    RubidiumDataFileHeader header;
    StorageResult result = ReadFileHeader(file, header);
    if (result != StorageResult::SUCCESS) {
        file.close();
        return {result, history};
    }
    
    // 填充历史信息
    history.device_id = header.device_id;
    history.serial_number = header.serial_number;
    history.firmware_version = header.firmware_version;
    history.first_learning_time = header.creation_time;
    history.last_update_time = header.last_modified_time;
    
    // 读取数据记录
    history.learning_records.reserve(header.record_count);
    for (uint32_t i = 0; i < header.record_count; ++i) {
        RubidiumLearningData data;
        result = ReadLearningRecord(file, data);
        if (result != StorageResult::SUCCESS) {
            LOG_ERROR(LogComponent::HAL_RUBIDIUM, 
                     "读取学习记录失败，记录索引: " + std::to_string(i));
            file.close();
            return {result, history};
        }
        
        if (data.IsValid()) {
            history.learning_records.push_back(data);
        } else {
            LOG_WARNING(LogComponent::HAL_RUBIDIUM, 
                       "跳过无效的学习记录，索引: " + std::to_string(i));
        }
    }
    
    file.close();
    
    // 更新缓存
    UpdateCache(history);
    
    LOG_INFO(LogComponent::HAL_RUBIDIUM, 
             "加载铷钟学习历史成功，记录数: " + std::to_string(history.learning_records.size()));
    
    return {StorageResult::SUCCESS, history};
}

std::pair<RubidiumLearningStorage::StorageResult, RubidiumLearningData> 
RubidiumLearningStorage::GetLatestLearningData() {
    
    auto [result, history] = LoadLearningHistory();
    if (result != StorageResult::SUCCESS) {
        return {result, RubidiumLearningData()};
    }
    
    const RubidiumLearningData* latest = history.GetLatestData();
    if (!latest) {
        return {StorageResult::ERROR_FILE_NOT_FOUND, RubidiumLearningData()};
    }
    
    return {StorageResult::SUCCESS, *latest};
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::CleanupExpiredData(uint32_t retention_days) {
    if (!initialized_.load()) {
        return StorageResult::ERROR_INVALID_PARAMETER;
    }
    
    uint64_t cutoff_time = GetCurrentTimestampNs() - 
                          (static_cast<uint64_t>(retention_days) * 24ULL * 3600ULL * 1000000000ULL);
    
    auto [load_result, history] = LoadLearningHistory();
    if (load_result != StorageResult::SUCCESS) {
        return load_result;
    }
    
    size_t original_count = history.learning_records.size();
    
    // 删除过期记录
    history.learning_records.erase(
        std::remove_if(history.learning_records.begin(), history.learning_records.end(),
            [cutoff_time](const RubidiumLearningData& data) {
                return data.timestamp_ns < cutoff_time;
            }),
        history.learning_records.end()
    );
    
    size_t removed_count = original_count - history.learning_records.size();
    
    if (removed_count > 0) {
        // 重新保存数据
        if (!history.learning_records.empty()) {
            StorageResult save_result = SaveLearningData(history.learning_records.back(), true);
            if (save_result != StorageResult::SUCCESS) {
                return save_result;
            }
        }
        
        LOG_INFO(LogComponent::HAL_RUBIDIUM, 
                 "清理过期学习数据完成，删除记录数: " + std::to_string(removed_count));
    }
    
    return StorageResult::SUCCESS;
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::CompactStorage() {
    // 简单的压缩实现：重新写入文件以去除碎片
    auto [load_result, history] = LoadLearningHistory();
    if (load_result != StorageResult::SUCCESS) {
        return load_result;
    }
    
    if (!history.learning_records.empty()) {
        return SaveLearningData(history.learning_records.back(), true);
    }
    
    return StorageResult::SUCCESS;
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::VerifyIntegrity(bool repair_if_corrupted) {
    std::string main_file = GetMainFilePath();
    if (!std::filesystem::exists(main_file)) {
        return StorageResult::ERROR_FILE_NOT_FOUND;
    }
    
    std::ifstream file(main_file, std::ios::binary);
    if (!file.is_open()) {
        return StorageResult::ERROR_READ_FAILED;
    }
    
    // 验证文件头
    RubidiumDataFileHeader header;
    StorageResult result = ReadFileHeader(file, header);
    if (result != StorageResult::SUCCESS) {
        file.close();
        if (repair_if_corrupted) {
            LOG_WARNING(LogComponent::HAL_RUBIDIUM, "文件头损坏，尝试修复");
            // 这里可以实现修复逻辑
        }
        return result;
    }
    
    // 验证数据记录
    uint32_t valid_records = 0;
    for (uint32_t i = 0; i < header.record_count; ++i) {
        RubidiumLearningData data;
        result = ReadLearningRecord(file, data);
        if (result == StorageResult::SUCCESS && data.IsValid()) {
            valid_records++;
        }
    }
    
    file.close();
    
    if (valid_records != header.record_count) {
        LOG_WARNING(LogComponent::HAL_RUBIDIUM, 
                   "数据完整性检查发现问题，有效记录: " + std::to_string(valid_records) + 
                   "，期望记录: " + std::to_string(header.record_count));
        
        if (repair_if_corrupted) {
            // 重新保存有效数据
            auto [load_result, history] = LoadLearningHistory();
            if (load_result == StorageResult::SUCCESS && !history.learning_records.empty()) {
                return SaveLearningData(history.learning_records.back(), true);
            }
        }
        
        return StorageResult::ERROR_FILE_CORRUPTED;
    }
    
    LOG_INFO(LogComponent::HAL_RUBIDIUM, "数据完整性验证通过");
    return StorageResult::SUCCESS;
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::CreateBackup(const std::string& backup_path) {
    std::string main_file = GetMainFilePath();
    if (!std::filesystem::exists(main_file)) {
        return StorageResult::ERROR_FILE_NOT_FOUND;
    }
    
    try {
        std::filesystem::copy_file(main_file, backup_path, 
                                  std::filesystem::copy_options::overwrite_existing);
        
        LOG_INFO(LogComponent::HAL_RUBIDIUM, "创建备份成功: " + backup_path);
        return StorageResult::SUCCESS;
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::HAL_RUBIDIUM, 
                 "创建备份失败: " + std::string(e.what()));
        return StorageResult::ERROR_BACKUP_FAILED;
    }
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::RestoreFromBackup(const std::string& backup_path) {
    if (!std::filesystem::exists(backup_path)) {
        return StorageResult::ERROR_FILE_NOT_FOUND;
    }
    
    try {
        std::string main_file = GetMainFilePath();
        std::filesystem::copy_file(backup_path, main_file, 
                                  std::filesystem::copy_options::overwrite_existing);
        
        // 验证恢复的文件
        StorageResult verify_result = VerifyIntegrity(false);
        if (verify_result != StorageResult::SUCCESS) {
            LOG_ERROR(LogComponent::HAL_RUBIDIUM, "恢复的备份文件损坏");
            return verify_result;
        }
        
        // 清理缓存以强制重新加载
        ClearCache();
        
        LOG_INFO(LogComponent::HAL_RUBIDIUM, "从备份恢复成功: " + backup_path);
        return StorageResult::SUCCESS;
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::HAL_RUBIDIUM, 
                 "从备份恢复失败: " + std::string(e.what()));
        return StorageResult::ERROR_READ_FAILED;
    }
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::ExportToJson(
    const std::string& export_path, bool include_raw_data) {
    
    auto [load_result, history] = LoadLearningHistory();
    if (load_result != StorageResult::SUCCESS) {
        return load_result;
    }
    
    std::ofstream file(export_path);
    if (!file.is_open()) {
        return StorageResult::ERROR_WRITE_FAILED;
    }
    
    file << "{\n";
    file << "  \"device_info\": {\n";
    file << "    \"device_id\": \"" << history.device_id << "\",\n";
    file << "    \"serial_number\": \"" << history.serial_number << "\",\n";
    file << "    \"firmware_version\": \"" << history.firmware_version << "\"\n";
    file << "  },\n";
    file << "  \"learning_history\": {\n";
    file << "    \"first_learning_time\": " << history.first_learning_time << ",\n";
    file << "    \"last_update_time\": " << history.last_update_time << ",\n";
    file << "    \"record_count\": " << history.learning_records.size() << "\n";
    file << "  },\n";
    file << "  \"learning_records\": [\n";
    
    for (size_t i = 0; i < history.learning_records.size(); ++i) {
        const auto& record = history.learning_records[i];
        
        file << "    {\n";
        file << "      \"timestamp_ns\": " << record.timestamp_ns << ",\n";
        file << "      \"learning_duration_ns\": " << record.learning_duration_ns << ",\n";
        file << "      \"sample_count\": " << record.sample_count << ",\n";
        file << "      \"frequency_offset_ppm\": " << record.frequency_offset_ppm << ",\n";
        file << "      \"frequency_stability_ppm\": " << record.frequency_stability_ppm << ",\n";
        file << "      \"temperature_coefficient_ppm_per_c\": " << record.temperature_coefficient_ppm_per_c << ",\n";
        file << "      \"aging_rate_ppm_per_day\": " << record.aging_rate_ppm_per_day << ",\n";
        file << "      \"allan_deviation_1s\": " << record.allan_deviation_1s << ",\n";
        file << "      \"confidence_level\": " << record.confidence_level << ",\n";
        file << "      \"validation_score\": " << record.validation_score << "\n";
        
        if (include_raw_data) {
            // 可以在这里添加更多详细数据
        }
        
        file << "    }";
        if (i < history.learning_records.size() - 1) {
            file << ",";
        }
        file << "\n";
    }
    
    file << "  ]\n";
    file << "}\n";
    
    file.close();
    
    LOG_INFO(LogComponent::HAL_RUBIDIUM, "导出JSON成功: " + export_path);
    return StorageResult::SUCCESS;
}

RubidiumLearningStorage::StorageStatistics RubidiumLearningStorage::GetStatistics() {
    StorageStatistics stats = {0, 0, 0, 0, 0.0, 0};
    
    auto [load_result, history] = LoadLearningHistory();
    if (load_result != StorageResult::SUCCESS) {
        return stats;
    }
    
    stats.total_records = static_cast<uint32_t>(history.learning_records.size());
    stats.oldest_record_time = history.first_learning_time;
    stats.newest_record_time = history.last_update_time;
    
    // 计算文件大小
    std::string main_file = GetMainFilePath();
    if (std::filesystem::exists(main_file)) {
        stats.file_size_bytes = std::filesystem::file_size(main_file);
    }
    
    // 计算平均质量评分
    if (!history.learning_records.empty()) {
        uint64_t total_score = 0;
        for (const auto& record : history.learning_records) {
            total_score += record.CalculateQualityScore();
        }
        stats.average_quality_score = static_cast<double>(total_score) / history.learning_records.size();
    }
    
    // 统计备份数量
    for (uint32_t i = 0; i < 10; ++i) {
        if (std::filesystem::exists(GetBackupFilePath(i))) {
            stats.backup_count++;
        } else {
            break;
        }
    }
    
    return stats;
}

void RubidiumLearningStorage::SetAutoBackupInterval(uint32_t interval_hours) {
    auto_backup_interval_hours_ = interval_hours;
    LOG_INFO(LogComponent::HAL_RUBIDIUM, 
             "自动备份间隔设置为: " + std::to_string(interval_hours) + " 小时");
}

void RubidiumLearningStorage::SetCompressionEnabled(bool enable) {
    compression_enabled_ = enable;
    LOG_INFO(LogComponent::HAL_RUBIDIUM, 
             "数据压缩" + std::string(enable ? "启用" : "禁用"));
}

void RubidiumLearningStorage::SetMaxRecords(uint32_t max_records) {
    max_records_ = max_records;
    LOG_INFO(LogComponent::HAL_RUBIDIUM, 
             "最大记录数设置为: " + std::to_string(max_records));
}

// 私有方法实现

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::ReadFileHeader(
    std::ifstream& file, RubidiumDataFileHeader& header) {
    
    file.read(reinterpret_cast<char*>(&header), sizeof(header));
    if (file.gcount() != sizeof(header)) {
        return StorageResult::ERROR_READ_FAILED;
    }
    
    if (!header.IsValid()) {
        return StorageResult::ERROR_INVALID_FORMAT;
    }
    
    return StorageResult::SUCCESS;
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::WriteFileHeader(
    std::ofstream& file, const RubidiumDataFileHeader& header) {
    
    file.write(reinterpret_cast<const char*>(&header), sizeof(header));
    if (!file.good()) {
        return StorageResult::ERROR_WRITE_FAILED;
    }
    
    return StorageResult::SUCCESS;
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::ReadLearningRecord(
    std::ifstream& file, RubidiumLearningData& data) {
    
    file.read(reinterpret_cast<char*>(&data), sizeof(data));
    if (file.gcount() != sizeof(data)) {
        return StorageResult::ERROR_READ_FAILED;
    }
    
    return StorageResult::SUCCESS;
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::WriteLearningRecord(
    std::ofstream& file, const RubidiumLearningData& data) {
    
    file.write(reinterpret_cast<const char*>(&data), sizeof(data));
    if (!file.good()) {
        return StorageResult::ERROR_WRITE_FAILED;
    }
    
    return StorageResult::SUCCESS;
}

uint32_t RubidiumLearningStorage::CalculateChecksum(const void* data, size_t size) {
    const uint8_t* bytes = static_cast<const uint8_t*>(data);
    uint32_t crc = 0xFFFFFFFF;
    
    for (size_t i = 0; i < size; ++i) {
        crc ^= bytes[i];
        for (int j = 0; j < 8; ++j) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc >>= 1;
            }
        }
    }
    
    return ~crc;
}

RubidiumLearningStorage::StorageResult RubidiumLearningStorage::CreateStorageDirectory() {
    std::filesystem::path storage_dir = std::filesystem::path(storage_path_).parent_path();
    
    if (!std::filesystem::exists(storage_dir)) {
        std::error_code ec;
        if (!std::filesystem::create_directories(storage_dir, ec)) {
            LOG_ERROR(LogComponent::HAL_RUBIDIUM, 
                     "创建存储目录失败: " + storage_dir.string() + ", 错误: " + ec.message());
            return StorageResult::ERROR_WRITE_FAILED;
        }
    }
    
    return StorageResult::SUCCESS;
}

std::string RubidiumLearningStorage::GetMainFilePath() const {
    return storage_path_ + "/" + device_id_ + "_rubidium_learning.dat";
}

std::string RubidiumLearningStorage::GetBackupFilePath(uint32_t backup_index) const {
    return storage_path_ + "/" + device_id_ + "_rubidium_learning.bak" + 
           (backup_index > 0 ? std::to_string(backup_index) : "");
}

std::string RubidiumLearningStorage::GetTempFilePath() const {
    return storage_path_ + "/" + device_id_ + "_rubidium_learning.tmp";
}

void RubidiumLearningStorage::UpdateCache(const RubidiumLearningHistory& history) {
    cached_history_ = std::make_unique<RubidiumLearningHistory>(history);
    cache_last_update_ = GetCurrentTimestampNs();
}

void RubidiumLearningStorage::ClearCache() {
    cached_history_.reset();
    cache_last_update_ = 0;
}

// RubidiumLearningAnalyzer实现

RubidiumLearningAnalyzer::RubidiumLearningAnalyzer(std::shared_ptr<RubidiumLearningStorage> storage)
    : storage_(storage) {
}

double RubidiumLearningAnalyzer::PredictFrequencyOffset(uint64_t prediction_time_ns, double temperature_c) {
    auto [result, latest_data] = storage_->GetLatestLearningData();
    if (result != RubidiumLearningStorage::StorageResult::SUCCESS) {
        return 0.0;
    }
    
    // 计算时间差（小时）
    double time_diff_hours = static_cast<double>(prediction_time_ns - latest_data.timestamp_ns) / 
                            (3600.0 * 1000000000.0);
    
    // 基础频率偏移
    double predicted_offset = latest_data.frequency_offset_ppm;
    
    // 添加漂移影响
    predicted_offset += latest_data.frequency_drift_rate_ppm_per_hour * time_diff_hours;
    
    // 添加老化影响
    double aging_days = time_diff_hours / 24.0;
    predicted_offset += latest_data.aging_rate_ppm_per_day * aging_days;
    
    // 添加温度影响
    double temp_diff = temperature_c - latest_data.reference_temperature_c;
    predicted_offset += latest_data.temperature_coefficient_ppm_per_c * temp_diff;
    predicted_offset += latest_data.temperature_coefficient_2nd_ppm_per_c2 * temp_diff * temp_diff;
    
    return predicted_offset;
}

double RubidiumLearningAnalyzer::CalculateTemperatureCompensation(
    double current_temperature_c, double reference_temperature_c) {
    
    auto [result, latest_data] = storage_->GetLatestLearningData();
    if (result != RubidiumLearningStorage::StorageResult::SUCCESS) {
        return 0.0;
    }
    
    double temp_diff = current_temperature_c - reference_temperature_c;
    
    // 线性和二次温度补偿
    double compensation = latest_data.temperature_coefficient_ppm_per_c * temp_diff;
    compensation += latest_data.temperature_coefficient_2nd_ppm_per_c2 * temp_diff * temp_diff;
    
    return -compensation; // 返回负值作为补偿
}

// 工具函数实现

std::string StorageResultToString(RubidiumLearningStorage::StorageResult result) {
    switch (result) {
        case RubidiumLearningStorage::StorageResult::SUCCESS: return "SUCCESS";
        case RubidiumLearningStorage::StorageResult::ERROR_FILE_NOT_FOUND: return "ERROR_FILE_NOT_FOUND";
        case RubidiumLearningStorage::StorageResult::ERROR_FILE_CORRUPTED: return "ERROR_FILE_CORRUPTED";
        case RubidiumLearningStorage::StorageResult::ERROR_INVALID_FORMAT: return "ERROR_INVALID_FORMAT";
        case RubidiumLearningStorage::StorageResult::ERROR_CHECKSUM_MISMATCH: return "ERROR_CHECKSUM_MISMATCH";
        case RubidiumLearningStorage::StorageResult::ERROR_VERSION_MISMATCH: return "ERROR_VERSION_MISMATCH";
        case RubidiumLearningStorage::StorageResult::ERROR_WRITE_FAILED: return "ERROR_WRITE_FAILED";
        case RubidiumLearningStorage::StorageResult::ERROR_READ_FAILED: return "ERROR_READ_FAILED";
        case RubidiumLearningStorage::StorageResult::ERROR_INSUFFICIENT_SPACE: return "ERROR_INSUFFICIENT_SPACE";
        case RubidiumLearningStorage::StorageResult::ERROR_PERMISSION_DENIED: return "ERROR_PERMISSION_DENIED";
        case RubidiumLearningStorage::StorageResult::ERROR_BACKUP_FAILED: return "ERROR_BACKUP_FAILED";
        case RubidiumLearningStorage::StorageResult::ERROR_INVALID_PARAMETER: return "ERROR_INVALID_PARAMETER";
        default: return "UNKNOWN";
    }
}

} // namespace core
} // namespace timing_server