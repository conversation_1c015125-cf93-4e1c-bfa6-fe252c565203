#include "core/clock_state_machine.h"
#include "core/signal_quality_evaluator.h"
#include "core/serialization.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <iomanip>

namespace timing_server {
namespace core {

ClockStateMachine::ClockStateMachine(ClockState initial_state)
    : current_state_(initial_state)
    , initial_state_(initial_state)
    , is_running_(false)
    , start_time_ns_(0)
    , total_transitions_(0) {
    
    // 初始化质量评估器
    gnss_evaluator_ = std::make_unique<GnssSignalQualityEvaluator>();
    disciplining_evaluator_ = std::make_unique<DiscipliningConvergenceEvaluator>();
    holdover_evaluator_ = std::make_unique<HoldoverQualityEvaluator>();
    
    // 初始化所有状态信息
    std::vector<ClockState> all_states = {
        ClockState::FREE_RUN,
        ClockState::DISCIPLINING,
        ClockState::LOCKED,
        ClockState::HOLDOVER
    };
    
    uint64_t current_time = GetCurrentTimestampNs();
    for (ClockState state : all_states) {
        StateInfo info;
        info.state = state;
        info.enter_time_ns = (state == initial_state) ? current_time : 0;
        info.duration_ns = 0;
        info.enter_count = (state == initial_state) ? 1 : 0;
        info.total_duration_ns = 0;
        info.last_transition_reason = (state == initial_state) ? "初始状态" : "";
        
        state_info_[state] = info;
    }
}

ClockStateMachine::~ClockStateMachine() {
    Stop();
}

bool ClockStateMachine::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        // 初始化状态转换表
        InitializeTransitionTable();
        
        std::cout << "时钟状态机初始化完成，初始状态: " 
                  << ClockStateToString(current_state_) << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        last_error_ = "状态机初始化失败: " + std::string(e.what());
        std::cerr << last_error_ << std::endl;
        return false;
    }
}

bool ClockStateMachine::Start() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (is_running_) {
        return true;
    }
    
    start_time_ns_ = GetCurrentTimestampNs();
    is_running_ = true;
    
    // 更新当前状态的进入时间
    state_info_[current_state_].enter_time_ns = start_time_ns_;
    
    std::cout << "时钟状态机已启动，当前状态: " 
              << ClockStateToString(current_state_) << std::endl;
    
    return true;
}

bool ClockStateMachine::Stop() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!is_running_) {
        return true;
    }
    
    // 更新当前状态的持续时间
    UpdateStateStatistics(current_state_);
    
    is_running_ = false;
    
    std::cout << "时钟状态机已停止" << std::endl;
    
    return true;
}

TransitionResult ClockStateMachine::ProcessEvent(const StateMachineEvent& event) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!is_running_) {
        return TransitionResult::ERROR;
    }
    
    std::cout << "处理事件: " << ClockEventToString(event.event) 
              << " 当前状态: " << ClockStateToString(current_state_) << std::endl;
    
    // 根据当前状态和事件确定目标状态，使用质量评估器进行智能判断
    ClockState target_state = current_state_;
    TransitionResult result = TransitionResult::INVALID;
    
    switch (current_state_) {
        case ClockState::FREE_RUN:
            if (event.event == ClockEvent::GNSS_SIGNAL_ACQUIRED) {
                // 检查GNSS信号质量是否足够好
                if (event.context.find("gnss_data") != event.context.end()) {
                    GnssSignalQuality quality = gnss_evaluator_->EvaluateGnssQuality(event.context.at("gnss_data"));
                    if (gnss_evaluator_->IsSignalUsable(quality)) {
                        target_state = ClockState::DISCIPLINING;
                        result = TransitionResult::SUCCESS;
                        
                        // 重置驯服评估器
                        disciplining_evaluator_->Reset();
                        
                        std::cout << "GNSS信号质量良好，开始驯服过程 (卫星数: " << quality.satellite_count 
                                  << ", 质量评分: " << gnss_evaluator_->GetQualityScore(quality) << ")" << std::endl;
                    } else {
                        result = TransitionResult::BLOCKED;
                        std::cout << "GNSS信号质量不足，无法开始驯服 (卫星数: " << quality.satellite_count 
                                  << ", 信号强度: " << quality.signal_strength_dbm << " dBm)" << std::endl;
                    }
                } else {
                    // 没有质量数据，使用默认逻辑
                    target_state = ClockState::DISCIPLINING;
                    result = TransitionResult::SUCCESS;
                    disciplining_evaluator_->Reset();
                }
            } else if (event.event == ClockEvent::SYSTEM_RESTART || event.event == ClockEvent::MANUAL_RESET) {
                // 保持在FREE_RUN状态
                result = TransitionResult::SUCCESS;
            }
            break;
            
        case ClockState::DISCIPLINING:
            if (event.event == ClockEvent::CONVERGENCE_ACHIEVED) {
                // 检查驯服是否真正收敛
                if (disciplining_evaluator_->IsConverged()) {
                    target_state = ClockState::LOCKED;
                    result = TransitionResult::SUCCESS;
                    
                    DiscipliningConvergence convergence = disciplining_evaluator_->EvaluateConvergence();
                    std::cout << "驯服收敛完成 (相位误差: " << convergence.phase_error_ns 
                              << " ns, 稳定样本: " << convergence.stable_samples << ")" << std::endl;
                } else {
                    result = TransitionResult::BLOCKED;
                    std::cout << "驯服尚未完全收敛，继续驯服过程" << std::endl;
                }
            } else if (event.event == ClockEvent::GNSS_SIGNAL_LOST) {
                target_state = ClockState::FREE_RUN;
                result = TransitionResult::SUCCESS;
                std::cout << "驯服期间GNSS信号丢失，回到自由运行" << std::endl;
            } else if (event.event == ClockEvent::HARDWARE_FAULT) {
                target_state = ClockState::FREE_RUN;
                result = TransitionResult::SUCCESS;
            }
            break;
            
        case ClockState::LOCKED:
            if (event.event == ClockEvent::GNSS_SIGNAL_LOST) {
                target_state = ClockState::HOLDOVER;
                result = TransitionResult::SUCCESS;
                
                // 开始守时评估
                double initial_offset = 0.0; // 应该从实际测量获取
                if (event.context.find("frequency_offset") != event.context.end()) {
                    try {
                        initial_offset = std::stod(event.context.at("frequency_offset"));
                    } catch (...) {
                        // 使用默认值
                    }
                }
                holdover_evaluator_->StartHoldover(initial_offset);
                
                std::cout << "进入守时模式，初始频率偏移: " << initial_offset << " ppm" << std::endl;
            } else if (event.event == ClockEvent::HARDWARE_FAULT) {
                target_state = ClockState::FREE_RUN;
                result = TransitionResult::SUCCESS;
            } else if (event.event == ClockEvent::SYSTEM_RESTART || event.event == ClockEvent::MANUAL_RESET) {
                target_state = ClockState::FREE_RUN;
                result = TransitionResult::SUCCESS;
            }
            break;
            
        case ClockState::HOLDOVER:
            if (event.event == ClockEvent::GNSS_SIGNAL_ACQUIRED) {
                // 检查GNSS信号质量
                if (event.context.find("gnss_data") != event.context.end()) {
                    GnssSignalQuality quality = gnss_evaluator_->EvaluateGnssQuality(event.context.at("gnss_data"));
                    if (gnss_evaluator_->IsSignalUsable(quality)) {
                        target_state = ClockState::DISCIPLINING;
                        result = TransitionResult::SUCCESS;
                        
                        // 停止守时评估并重置驯服评估器
                        holdover_evaluator_->StopHoldover();
                        disciplining_evaluator_->Reset();
                        
                        std::cout << "守时期间GNSS信号恢复，重新开始驯服" << std::endl;
                    } else {
                        result = TransitionResult::BLOCKED;
                        std::cout << "GNSS信号质量不足，继续守时模式" << std::endl;
                    }
                } else {
                    // 没有质量数据，使用默认逻辑
                    target_state = ClockState::DISCIPLINING;
                    result = TransitionResult::SUCCESS;
                    holdover_evaluator_->StopHoldover();
                    disciplining_evaluator_->Reset();
                }
            } else if (event.event == ClockEvent::HOLDOVER_TIMEOUT) {
                // 检查守时是否真正超时
                uint32_t max_hours = 24; // 默认24小时
                if (event.context.find("max_holdover_hours") != event.context.end()) {
                    try {
                        max_hours = static_cast<uint32_t>(std::stoi(event.context.at("max_holdover_hours")));
                    } catch (...) {
                        // 使用默认值
                    }
                }
                
                if (holdover_evaluator_->IsHoldoverTimeout(max_hours)) {
                    target_state = ClockState::FREE_RUN;
                    result = TransitionResult::SUCCESS;
                    
                    HoldoverQuality quality = holdover_evaluator_->EvaluateHoldover();
                    holdover_evaluator_->StopHoldover();
                    
                    std::cout << "守时超时，回到自由运行 (守时时长: " 
                              << (quality.holdover_duration_ns / (3600.0 * 1e9)) << " 小时)" << std::endl;
                } else {
                    result = TransitionResult::BLOCKED;
                    std::cout << "守时尚未超时，继续守时模式" << std::endl;
                }
            } else if (event.event == ClockEvent::HARDWARE_FAULT) {
                target_state = ClockState::FREE_RUN;
                result = TransitionResult::SUCCESS;
                holdover_evaluator_->StopHoldover();
            } else if (event.event == ClockEvent::SYSTEM_RESTART || event.event == ClockEvent::MANUAL_RESET) {
                target_state = ClockState::FREE_RUN;
                result = TransitionResult::SUCCESS;
                holdover_evaluator_->StopHoldover();
            }
            break;
    }
    
    // 如果需要状态转换
    if (result == TransitionResult::SUCCESS && target_state != current_state_) {
        result = ExecuteTransition(target_state, event, event.description);
    }
    
    // 通知监听器
    if (result == TransitionResult::SUCCESS) {
        NotifyEventProcessed(event);
    } else {
        NotifyTransitionFailed(event, result);
    }
    
    return result;
}

bool ClockStateMachine::ForceTransition(ClockState target_state, const std::string& reason) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!is_running_) {
        return false;
    }
    
    StateMachineEvent event(ClockEvent::MANUAL_RESET, reason.empty() ? "强制转换" : reason);
    TransitionResult result = ExecuteTransition(target_state, event, reason);
    
    return result == TransitionResult::SUCCESS;
}

ClockState ClockStateMachine::GetCurrentState() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return current_state_;
}

StateInfo ClockStateMachine::GetStateInfo(ClockState state) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = state_info_.find(state);
    if (it != state_info_.end()) {
        StateInfo info = it->second;
        
        // 如果是当前状态，更新持续时间
        if (state == current_state_ && is_running_) {
            uint64_t current_time = GetCurrentTimestampNs();
            info.duration_ns = current_time - info.enter_time_ns;
        }
        
        return info;
    }
    
    return StateInfo{};
}

std::map<ClockState, StateInfo> ClockStateMachine::GetAllStateInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::map<ClockState, StateInfo> result = state_info_;
    
    // 更新当前状态的持续时间
    if (is_running_) {
        uint64_t current_time = GetCurrentTimestampNs();
        result[current_state_].duration_ns = current_time - result[current_state_].enter_time_ns;
    }
    
    return result;
}

bool ClockStateMachine::IsTransitionValid(ClockState from_state, ClockState to_state, ClockEvent event) const {
    std::lock_guard<std::mutex> lock(mutex_);
    return IsTransitionValidInternal(from_state, to_state, event);
}

bool ClockStateMachine::IsTransitionValidInternal(ClockState from_state, ClockState to_state, ClockEvent event) const {
    const TransitionCondition* condition = FindTransitionCondition(from_state, to_state, event);
    if (!condition) {
        return false;
    }
    
    // 检查守护条件
    if (condition->guard && !condition->guard()) {
        return false;
    }
    
    // 检查最小持续时间
    if (from_state == current_state_ && is_running_) {
        uint64_t current_time = GetCurrentTimestampNs();
        uint64_t duration_ms = (current_time - state_info_.at(current_state_).enter_time_ns) / 1000000;
        if (duration_ms < condition->min_duration_ms) {
            return false;
        }
    }
    
    return true;
}

uint64_t ClockStateMachine::GetStateDuration(ClockState state) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (state == ClockState::FREE_RUN) {
        state = current_state_;
    }
    
    auto it = state_info_.find(state);
    if (it == state_info_.end()) {
        return 0;
    }
    
    if (state == current_state_ && is_running_) {
        uint64_t current_time = GetCurrentTimestampNs();
        return current_time - it->second.enter_time_ns;
    }
    
    return it->second.duration_ns;
}

void ClockStateMachine::AddListener(std::shared_ptr<IStateMachineListener> listener) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (listener) {
        listeners_.push_back(std::weak_ptr<IStateMachineListener>(listener));
    }
}

void ClockStateMachine::RemoveListener(std::shared_ptr<IStateMachineListener> listener) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    listeners_.erase(
        std::remove_if(listeners_.begin(), listeners_.end(),
            [&listener](const std::weak_ptr<IStateMachineListener>& weak_ptr) {
                return weak_ptr.lock() == listener;
            }),
        listeners_.end()
    );
}

bool ClockStateMachine::SaveState(const std::string& file_path) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        std::ofstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        // 创建状态数据的JSON表示
        std::ostringstream json;
        json << "{\n";
        json << "  \"current_state\": \"" << ClockStateToString(current_state_) << "\",\n";
        json << "  \"is_running\": " << (is_running_ ? "true" : "false") << ",\n";
        json << "  \"start_time_ns\": " << start_time_ns_ << ",\n";
        json << "  \"total_transitions\": " << total_transitions_ << ",\n";
        json << "  \"state_info\": {\n";
        
        bool first = true;
        for (const auto& pair : state_info_) {
            if (!first) json << ",\n";
            first = false;
            
            const StateInfo& info = pair.second;
            json << "    \"" << ClockStateToString(pair.first) << "\": {\n";
            json << "      \"enter_time_ns\": " << info.enter_time_ns << ",\n";
            json << "      \"duration_ns\": " << info.duration_ns << ",\n";
            json << "      \"enter_count\": " << info.enter_count << ",\n";
            json << "      \"total_duration_ns\": " << info.total_duration_ns << ",\n";
            json << "      \"last_transition_reason\": \"" << info.last_transition_reason << "\"\n";
            json << "    }";
        }
        
        json << "\n  }\n";
        json << "}\n";
        
        file << json.str();
        file.close();
        
        return true;
    } catch (const std::exception& e) {
        // 注意：在const方法中不能修改last_error_，这里只是记录到控制台
        std::cerr << "保存状态失败: " << e.what() << std::endl;
        return false;
    }
}

bool ClockStateMachine::RestoreState(const std::string& file_path) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return false;
        }
        
        // 简单的JSON解析（实际项目中应使用专业的JSON库）
        std::string line;
        std::string content;
        while (std::getline(file, line)) {
            content += line;
        }
        file.close();
        
        // 这里应该实现完整的JSON解析
        // 为了简化，只恢复基本状态
        if (content.find("\"FREE_RUN\"") != std::string::npos) {
            current_state_ = ClockState::FREE_RUN;
        } else if (content.find("\"DISCIPLINING\"") != std::string::npos) {
            current_state_ = ClockState::DISCIPLINING;
        } else if (content.find("\"LOCKED\"") != std::string::npos) {
            current_state_ = ClockState::LOCKED;
        } else if (content.find("\"HOLDOVER\"") != std::string::npos) {
            current_state_ = ClockState::HOLDOVER;
        }
        
        std::cout << "状态机状态已从文件恢复: " << ClockStateToString(current_state_) << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        last_error_ = "恢复状态失败: " + std::string(e.what());
        return false;
    }
}

void ClockStateMachine::Reset() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 停止状态机
    is_running_ = false;
    
    // 重置到初始状态
    current_state_ = initial_state_;
    total_transitions_ = 0;
    start_time_ns_ = 0;
    
    // 重置所有状态信息
    uint64_t current_time = GetCurrentTimestampNs();
    for (auto& pair : state_info_) {
        StateInfo& info = pair.second;
        info.enter_time_ns = (pair.first == initial_state_) ? current_time : 0;
        info.duration_ns = 0;
        info.enter_count = (pair.first == initial_state_) ? 1 : 0;
        info.total_duration_ns = 0;
        info.last_transition_reason = (pair.first == initial_state_) ? "重置到初始状态" : "";
    }
    
    std::cout << "状态机已重置到初始状态: " << ClockStateToString(current_state_) << std::endl;
}

std::string ClockStateMachine::GetStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::ostringstream stats;
    stats << "=== 时钟状态机统计信息 ===\n";
    stats << "当前状态: " << ClockStateToString(current_state_) << "\n";
    stats << "运行状态: " << (is_running_ ? "运行中" : "已停止") << "\n";
    stats << "总转换次数: " << total_transitions_ << "\n";
    
    if (start_time_ns_ > 0) {
        uint64_t total_runtime = GetCurrentTimestampNs() - start_time_ns_;
        stats << "总运行时间: " << (total_runtime / 1000000000) << " 秒\n";
    }
    
    stats << "\n各状态统计:\n";
    for (const auto& pair : state_info_) {
        const StateInfo& info = pair.second;
        stats << "  " << ClockStateToString(pair.first) << ":\n";
        stats << "    进入次数: " << info.enter_count << "\n";
        stats << "    总持续时间: " << (info.total_duration_ns / 1000000000) << " 秒\n";
        
        if (pair.first == current_state_ && is_running_) {
            uint64_t current_duration = GetCurrentTimestampNs() - info.enter_time_ns;
            stats << "    当前持续时间: " << (current_duration / 1000000000) << " 秒\n";
        }
        
        if (!info.last_transition_reason.empty()) {
            stats << "    最后转换原因: " << info.last_transition_reason << "\n";
        }
    }
    
    return stats.str();
}

void ClockStateMachine::SetTransitionGuard(ClockState from_state, ClockState to_state, ClockEvent event, std::function<bool()> guard) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& condition : transition_table_) {
        if (condition.from_state == from_state && 
            condition.to_state == to_state && 
            condition.trigger_event == event) {
            condition.guard = guard;
            break;
        }
    }
}

void ClockStateMachine::SetTransitionAction(ClockState from_state, ClockState to_state, ClockEvent event, std::function<void()> action) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    for (auto& condition : transition_table_) {
        if (condition.from_state == from_state && 
            condition.to_state == to_state && 
            condition.trigger_event == event) {
            condition.action = action;
            break;
        }
    }
}

void ClockStateMachine::InitializeTransitionTable() {
    transition_table_.clear();
    
    // FREE_RUN -> DISCIPLINING (GNSS信号获取)
    transition_table_.push_back({
        ClockState::FREE_RUN,
        ClockState::DISCIPLINING,
        ClockEvent::GNSS_SIGNAL_ACQUIRED,
        nullptr,  // 守护条件
        nullptr,  // 转换动作
        0,        // 最小持续时间0秒（可以立即转换）
        "GNSS信号获取，开始驯服"
    });
    
    // DISCIPLINING -> LOCKED (驯服收敛)
    transition_table_.push_back({
        ClockState::DISCIPLINING,
        ClockState::LOCKED,
        ClockEvent::CONVERGENCE_ACHIEVED,
        nullptr,
        nullptr,
        5000,     // 最小驯服时间5秒
        "驯服收敛完成，进入锁定状态"
    });
    
    // DISCIPLINING -> FREE_RUN (GNSS信号丢失)
    transition_table_.push_back({
        ClockState::DISCIPLINING,
        ClockState::FREE_RUN,
        ClockEvent::GNSS_SIGNAL_LOST,
        nullptr,
        nullptr,
        0,
        "驯服期间GNSS信号丢失"
    });
    
    // LOCKED -> HOLDOVER (GNSS信号丢失)
    transition_table_.push_back({
        ClockState::LOCKED,
        ClockState::HOLDOVER,
        ClockEvent::GNSS_SIGNAL_LOST,
        nullptr,
        nullptr,
        0,
        "锁定状态下GNSS信号丢失，进入守时"
    });
    
    // HOLDOVER -> DISCIPLINING (GNSS信号恢复)
    transition_table_.push_back({
        ClockState::HOLDOVER,
        ClockState::DISCIPLINING,
        ClockEvent::GNSS_SIGNAL_ACQUIRED,
        nullptr,
        nullptr,
        0,
        "守时期间GNSS信号恢复"
    });
    
    // HOLDOVER -> FREE_RUN (守时超时)
    transition_table_.push_back({
        ClockState::HOLDOVER,
        ClockState::FREE_RUN,
        ClockEvent::HOLDOVER_TIMEOUT,
        nullptr,
        nullptr,
        0,
        "守时超时，回到自由运行"
    });
    
    // 硬件故障处理 - 所有状态都可以转到FREE_RUN
    std::vector<ClockState> all_states = {
        ClockState::DISCIPLINING,
        ClockState::LOCKED,
        ClockState::HOLDOVER
    };
    
    for (ClockState state : all_states) {
        transition_table_.push_back({
            state,
            ClockState::FREE_RUN,
            ClockEvent::HARDWARE_FAULT,
            nullptr,
            nullptr,
            0,
            "硬件故障，回到自由运行"
        });
        
        transition_table_.push_back({
            state,
            ClockState::FREE_RUN,
            ClockEvent::SYSTEM_RESTART,
            nullptr,
            nullptr,
            0,
            "系统重启"
        });
        
        transition_table_.push_back({
            state,
            ClockState::FREE_RUN,
            ClockEvent::MANUAL_RESET,
            nullptr,
            nullptr,
            0,
            "手动重置"
        });
    }
}

TransitionResult ClockStateMachine::ExecuteTransition(ClockState target_state, const StateMachineEvent& event, const std::string& reason) {
    // 检查转换是否有效（使用内部方法，避免死锁）
    if (!IsTransitionValidInternal(current_state_, target_state, event.event)) {
        return TransitionResult::INVALID;
    }
    
    ClockState previous_state = current_state_;
    
    try {
        // 查找转换条件
        const TransitionCondition* condition = FindTransitionCondition(current_state_, target_state, event.event);
        
        // 执行转换动作
        if (condition && condition->action) {
            condition->action();
        }
        
        // 退出当前状态
        ExitState(current_state_, target_state);
        
        // 更新状态统计
        UpdateStateStatistics(current_state_);
        
        // 切换到新状态
        current_state_ = target_state;
        total_transitions_++;
        
        // 进入新状态
        EnterState(current_state_, previous_state);
        
        // 更新状态信息
        state_info_[current_state_].enter_time_ns = GetCurrentTimestampNs();
        state_info_[current_state_].enter_count++;
        state_info_[current_state_].last_transition_reason = reason.empty() ? 
            (condition ? condition->description : "未知原因") : reason;
        
        std::cout << "状态转换成功: " << ClockStateToString(previous_state) 
                  << " -> " << ClockStateToString(current_state_) 
                  << " (原因: " << state_info_[current_state_].last_transition_reason << ")" << std::endl;
        
        return TransitionResult::SUCCESS;
        
    } catch (const std::exception& e) {
        last_error_ = "状态转换执行失败: " + std::string(e.what());
        std::cerr << last_error_ << std::endl;
        return TransitionResult::ERROR;
    }
}

void ClockStateMachine::EnterState(ClockState state, ClockState previous_state) {
    std::cout << "进入状态: " << ClockStateToString(state) << std::endl;
    
    // 通知监听器
    NotifyStateEntered(state, previous_state);
}

void ClockStateMachine::ExitState(ClockState state, ClockState next_state) {
    std::cout << "退出状态: " << ClockStateToString(state) << std::endl;
    
    // 通知监听器
    NotifyStateExited(state, next_state);
}

void ClockStateMachine::NotifyStateEntered(ClockState state, ClockState previous_state) {
    for (auto& weak_listener : listeners_) {
        if (auto listener = weak_listener.lock()) {
            try {
                listener->OnStateEntered(state, previous_state);
            } catch (const std::exception& e) {
                std::cerr << "监听器状态进入回调异常: " << e.what() << std::endl;
            }
        }
    }
}

void ClockStateMachine::NotifyStateExited(ClockState state, ClockState next_state) {
    for (auto& weak_listener : listeners_) {
        if (auto listener = weak_listener.lock()) {
            try {
                listener->OnStateExited(state, next_state);
            } catch (const std::exception& e) {
                std::cerr << "监听器状态退出回调异常: " << e.what() << std::endl;
            }
        }
    }
}

void ClockStateMachine::NotifyEventProcessed(const StateMachineEvent& event) {
    for (auto& weak_listener : listeners_) {
        if (auto listener = weak_listener.lock()) {
            try {
                listener->OnEventProcessed(event, current_state_);
            } catch (const std::exception& e) {
                std::cerr << "监听器事件处理回调异常: " << e.what() << std::endl;
            }
        }
    }
}

void ClockStateMachine::NotifyTransitionFailed(const StateMachineEvent& event, TransitionResult result) {
    for (auto& weak_listener : listeners_) {
        if (auto listener = weak_listener.lock()) {
            try {
                listener->OnTransitionFailed(event, current_state_, result);
            } catch (const std::exception& e) {
                std::cerr << "监听器转换失败回调异常: " << e.what() << std::endl;
            }
        }
    }
}

const TransitionCondition* ClockStateMachine::FindTransitionCondition(ClockState from_state, ClockState to_state, ClockEvent event) const {
    for (const auto& condition : transition_table_) {
        if (condition.from_state == from_state && 
            condition.to_state == to_state && 
            condition.trigger_event == event) {
            return &condition;
        }
    }
    return nullptr;
}

void ClockStateMachine::UpdateStateStatistics(ClockState state) {
    if (!is_running_) {
        return;
    }
    
    auto& info = state_info_[state];
    uint64_t current_time = GetCurrentTimestampNs();
    
    if (info.enter_time_ns > 0) {
        uint64_t duration = current_time - info.enter_time_ns;
        info.duration_ns = duration;
        info.total_duration_ns += duration;
    }
}

GnssSignalQuality ClockStateMachine::EvaluateGnssSignal(const std::string& gnss_data) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!gnss_evaluator_) {
        return GnssSignalQuality{};
    }
    
    return gnss_evaluator_->EvaluateGnssQuality(gnss_data);
}

void ClockStateMachine::AddDiscipliningSample(double phase_error_ns, double frequency_error_ppm) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (disciplining_evaluator_) {
        disciplining_evaluator_->AddSample(phase_error_ns, frequency_error_ppm);
    }
}

bool ClockStateMachine::IsDiscipliningConverged() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!disciplining_evaluator_) {
        return false;
    }
    
    return disciplining_evaluator_->IsConverged();
}

void ClockStateMachine::StartHoldoverEvaluation(double initial_frequency_offset) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (holdover_evaluator_) {
        holdover_evaluator_->StartHoldover(initial_frequency_offset);
    }
}

void ClockStateMachine::UpdateHoldoverStatus(double current_frequency_offset, double temperature) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (holdover_evaluator_) {
        holdover_evaluator_->UpdateHoldover(current_frequency_offset, temperature);
    }
}

bool ClockStateMachine::IsHoldoverTimeout(uint32_t max_hours) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!holdover_evaluator_) {
        return false;
    }
    
    return holdover_evaluator_->IsHoldoverTimeout(max_hours);
}

HoldoverQuality ClockStateMachine::GetHoldoverQuality() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!holdover_evaluator_) {
        return HoldoverQuality{};
    }
    
    return holdover_evaluator_->EvaluateHoldover();
}

} // namespace core
} // namespace timing_server