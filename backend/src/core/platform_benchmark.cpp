#include "core/platform_validator.h"
#include "hal/hal_factory.h"
#include "core/logger.h"
#include <iostream>
#include <iomanip>
#include <fstream>
#include <chrono>
#include <thread>

using namespace timing_server::core;
using namespace timing_server::hal;

/**
 * @brief 平台基准测试工具
 * 独立的命令行工具，用于执行详细的平台性能基准测试
 */
class PlatformBenchmarkTool {
public:
    /**
     * @brief 运行完整的基准测试套件
     */
    static int RunBenchmarkSuite() {
        try {
            if (!InitializeSystem()) {
                return 1;
            }
            
            auto platform_info = DetectAndDisplayPlatform();
            auto hal_factory = CreateAndValidateHalFactory();
            if (!hal_factory) {
                return 1;
            }
            
            PlatformValidator validator(hal_factory);
            
            auto validation_report = RunValidationPhase(validator);
            auto benchmark_report = RunBenchmarkPhase(validator);
            
            RunPlatformSpecificPhase(platform_info.type, validator);
            GenerateReportPhase(validation_report, benchmark_report, platform_info);
            ProvideOptimizationPhase(platform_info.type, benchmark_report);
            
            std::cout << "\n=== 基准测试完成 ===" << std::endl;
            return validation_report.overall_success ? 0 : 1;
            
        } catch (const std::exception& e) {
            std::cerr << "基准测试异常: " << e.what() << std::endl;
            return 1;
        }
    }
    
private:
    /**
     * @brief 初始化系统组件
     */
    static bool InitializeSystem() {
        std::cout << "=== 平台基准测试工具 ===" << std::endl;
        
        // 初始化日志系统
        Logger::GetInstance().Initialize();
        Logger::GetInstance().SetLogLevel(LogLevel::INFO);
        
        return true;
    }
    
    /**
     * @brief 检测并显示平台信息
     */
    static PlatformInfo DetectAndDisplayPlatform() {
        auto platform_info = PlatformDetector::DetectPlatform();
        std::cout << "检测到平台: " << platform_info.description << std::endl;
        std::cout << "操作系统: " << platform_info.os_name << std::endl;
        std::cout << "处理器架构: " << platform_info.architecture << std::endl;
        std::cout << "内核版本: " << platform_info.kernel_version << std::endl;
        std::cout << "开发环境: " << (platform_info.is_development_env ? "是" : "否") << std::endl;
        std::cout << std::endl;
        return platform_info;
    }
    
    /**
     * @brief 创建并验证HAL工厂
     */
    static std::shared_ptr<timing_server::hal::I_HalFactory> CreateAndValidateHalFactory() {
        auto hal_factory = timing_server::hal::CreateHalFactory();
        if (!hal_factory) {
            std::cerr << "错误: 无法创建HAL工厂" << std::endl;
        }
        return hal_factory;
    }
    
    /**
     * @brief 运行验证阶段
     */
    static PlatformValidationReport RunValidationPhase(PlatformValidator& validator) {
        std::cout << "=== 第一阶段: 平台功能验证 ===" << std::endl;
        auto validation_report = validator.ValidatePlatform();
        PrintValidationReport(validation_report);
        
        if (!validation_report.overall_success) {
            std::cout << "\n警告: 平台验证存在问题，可能影响基准测试结果" << std::endl;
        }
        return validation_report;
    }
    
    /**
     * @brief 运行基准测试阶段
     */
    static PlatformBenchmarkReport RunBenchmarkPhase(PlatformValidator& validator) {
        std::cout << "\n=== 第二阶段: 性能基准测试 ===" << std::endl;
        auto benchmark_report = validator.RunBenchmarks();
        PrintBenchmarkReport(benchmark_report);
        return benchmark_report;
    }
    
    /**
     * @brief 运行平台特定测试阶段
     */
    static void RunPlatformSpecificPhase(PlatformType platform_type, PlatformValidator& validator) {
        std::cout << "\n=== 第三阶段: 平台特定测试 ===" << std::endl;
        RunPlatformSpecificTests(platform_type, validator);
    }
    
    /**
     * @brief 生成报告阶段
     */
    static void GenerateReportPhase(const PlatformValidationReport& validation_report,
                                   const PlatformBenchmarkReport& benchmark_report,
                                   const PlatformInfo& platform_info) {
        std::cout << "\n=== 第四阶段: 生成详细报告 ===" << std::endl;
        GenerateDetailedReport(validation_report, benchmark_report, platform_info);
    }
    
    /**
     * @brief 提供优化建议阶段
     */
    static void ProvideOptimizationPhase(PlatformType platform_type, 
                                        const PlatformBenchmarkReport& benchmark_report) {
        std::cout << "\n=== 第五阶段: 优化建议 ===" << std::endl;
        ProvideOptimizationGuidance(platform_type, benchmark_report);
    }

    /**
     * @brief 打印验证报告
     */
    static void PrintValidationReport(const PlatformValidationReport& report) {
        std::cout << "平台验证结果:" << std::endl;
        std::cout << "  总体状态: " << (report.overall_success ? "✓ 通过" : "✗ 失败") << std::endl;
        std::cout << "  执行时间: " << std::fixed << std::setprecision(2) 
                  << report.total_execution_time_ms << " ms" << std::endl;
        std::cout << "  统计信息: 成功=" << report.success_count 
                  << ", 警告=" << report.warning_count 
                  << ", 错误=" << report.error_count 
                  << ", 严重=" << report.critical_count << std::endl;
        
        // 显示详细验证项目
        std::cout << "\n详细验证结果:" << std::endl;
        for (const auto& item : report.validation_items) {
            std::string status_icon;
            std::string status_color;
            
            switch (item.result_type) {
                case ValidationResultType::SUCCESS:
                    status_icon = "✓";
                    status_color = "\033[32m"; // 绿色
                    break;
                case ValidationResultType::WARNING:
                    status_icon = "⚠";
                    status_color = "\033[33m"; // 黄色
                    break;
                case ValidationResultType::ERROR:
                    status_icon = "✗";
                    status_color = "\033[31m"; // 红色
                    break;
                case ValidationResultType::CRITICAL:
                    status_icon = "⚠";
                    status_color = "\033[35m"; // 紫色
                    break;
            }
            
            std::cout << "  " << status_color << status_icon << "\033[0m " 
                      << std::setw(25) << std::left << item.test_name 
                      << ": " << item.message;
            
            if (item.execution_time_ms > 0) {
                std::cout << " (" << std::fixed << std::setprecision(1) 
                          << item.execution_time_ms << "ms)";
            }
            std::cout << std::endl;
            
            if (!item.details.empty()) {
                std::cout << "    详情: " << item.details << std::endl;
            }
        }
    }
    
    /**
     * @brief 打印基准测试报告
     */
    static void PrintBenchmarkReport(const PlatformBenchmarkReport& report) {
        std::cout << "性能基准测试结果:" << std::endl;
        std::cout << "  总体评分: " << std::fixed << std::setprecision(1) 
                  << report.overall_score << "/100 (" << report.performance_grade << ")" << std::endl;
        
        // 性能等级颜色显示
        std::string grade_color;
        if (report.performance_grade == "A") grade_color = "\033[32m"; // 绿色
        else if (report.performance_grade == "B") grade_color = "\033[36m"; // 青色
        else if (report.performance_grade == "C") grade_color = "\033[33m"; // 黄色
        else if (report.performance_grade == "D") grade_color = "\033[31m"; // 红色
        else grade_color = "\033[35m"; // 紫色
        
        std::cout << "  性能等级: " << grade_color << report.performance_grade << "\033[0m" << std::endl;
        
        std::cout << "\n详细基准测试结果:" << std::endl;
        std::cout << std::setw(20) << std::left << "测试项目"
                  << std::setw(15) << "平均延迟(ns)"
                  << std::setw(15) << "吞吐量(ops/s)"
                  << std::setw(12) << "CPU使用率"
                  << std::setw(12) << "内存使用" << std::endl;
        std::cout << std::string(74, '-') << std::endl;
        
        for (const auto& benchmark : report.benchmarks) {
            std::cout << std::setw(20) << std::left << benchmark.benchmark_name
                      << std::setw(15) << std::fixed << std::setprecision(1) << benchmark.average_latency_ns
                      << std::setw(15) << std::fixed << std::setprecision(0) << benchmark.throughput_ops_per_sec
                      << std::setw(11) << std::fixed << std::setprecision(1) << benchmark.cpu_usage_percent << "%"
                      << std::setw(11) << std::fixed << std::setprecision(1) << benchmark.memory_usage_mb << "MB"
                      << std::endl;
        }
    }
    
    /**
     * @brief 运行平台特定测试
     */
    static void RunPlatformSpecificTests(PlatformType platform_type, PlatformValidator& validator) {
        switch (platform_type) {
            case PlatformType::LINUX_X86_64:
                RunLinuxX86_64Tests(validator);
                break;
            case PlatformType::LINUX_LOONGARCH64:
                RunLoongArch64Tests(validator);
                break;
            case PlatformType::MACOS_X86_64:
            case PlatformType::MACOS_ARM64:
                RunMacOSTests(validator);
                break;
            default:
                std::cout << "当前平台无特定测试项目" << std::endl;
                break;
        }
    }
    
    /**
     * @brief Linux x86_64特定测试
     */
    static void RunLinuxX86_64Tests(PlatformValidator& validator) {
        std::cout << "执行Linux x86_64特定测试..." << std::endl;
        
        auto tests = validator.ValidateLinuxX86_64();
        for (const auto& test : tests) {
            std::string status = (test.result_type == ValidationResultType::SUCCESS) ? "✓" : "✗";
            std::cout << "  " << status << " " << test.test_name << ": " << test.message << std::endl;
        }
        
        // 额外的x86_64性能测试
        std::cout << "\n执行x86_64特定性能测试:" << std::endl;
        TestX86_64Features();
    }
    
    /**
     * @brief 龙芯LoongArch64特定测试
     */
    static void RunLoongArch64Tests(PlatformValidator& validator) {
        std::cout << "执行龙芯LoongArch64特定测试..." << std::endl;
        
        auto tests = validator.ValidateLoongArch64();
        for (const auto& test : tests) {
            std::string status = (test.result_type == ValidationResultType::SUCCESS) ? "✓" : "✗";
            std::cout << "  " << status << " " << test.test_name << ": " << test.message << std::endl;
        }
        
        // 额外的龙芯性能测试
        std::cout << "\n执行龙芯特定性能测试:" << std::endl;
        TestLoongArchFeatures();
    }
    
    /**
     * @brief macOS特定测试
     */
    static void RunMacOSTests(PlatformValidator& validator) {
        std::cout << "执行macOS开发环境特定测试..." << std::endl;
        
        auto tests = validator.ValidateMacOSMockHal();
        for (const auto& test : tests) {
            std::string status = (test.result_type == ValidationResultType::SUCCESS) ? "✓" : "✗";
            std::cout << "  " << status << " " << test.test_name << ": " << test.message << std::endl;
        }
        
        // 额外的macOS开发环境测试
        std::cout << "\n执行macOS开发环境特定测试:" << std::endl;
        TestMacOSDevFeatures();
    }
    
    /**
     * @brief 测试x86_64特定特性
     */
    static void TestX86_64Features() {
        // 测试SSE/AVX指令集支持
        std::cout << "  检查SSE/AVX指令集支持..." << std::endl;
        
        // 测试高精度时间戳计数器
        auto start = std::chrono::high_resolution_clock::now();
        std::this_thread::sleep_for(std::chrono::microseconds(100));
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start).count();
        std::cout << "  高精度时间戳测试: " << duration << "ns (预期~100000ns)" << std::endl;
        
        // 测试内存对齐性能
        TestMemoryAlignment();
    }
    
    /**
     * @brief 测试龙芯特定特性
     */
    static void TestLoongArchFeatures() {
        std::cout << "  检查龙芯处理器特性..." << std::endl;
        
        // 读取/proc/cpuinfo检查龙芯特定信息
        std::ifstream cpuinfo("/proc/cpuinfo");
        if (cpuinfo.is_open()) {
            std::string line;
            bool found_loongarch = false;
            while (std::getline(cpuinfo, line)) {
                if (line.find("loongarch") != std::string::npos || 
                    line.find("Loongson") != std::string::npos) {
                    found_loongarch = true;
                    std::cout << "  检测到龙芯处理器信息: " << line << std::endl;
                    break;
                }
            }
            if (!found_loongarch) {
                std::cout << "  未检测到龙芯处理器特征（可能在模拟环境中）" << std::endl;
            }
        }
        
        // 测试龙芯特定的性能特性
        TestLoongArchPerformance();
    }
    
    /**
     * @brief 测试macOS开发环境特性
     */
    static void TestMacOSDevFeatures() {
        std::cout << "  检查macOS开发工具..." << std::endl;
        
        // 检查Xcode命令行工具
        if (CheckToolAvailability("clang")) {
            std::cout << "  ✓ Clang编译器可用" << std::endl;
        } else {
            std::cout << "  ✗ Clang编译器不可用" << std::endl;
        }
        
        // 检查CMake
        if (CheckToolAvailability("cmake")) {
            std::cout << "  ✓ CMake构建工具可用" << std::endl;
        } else {
            std::cout << "  ✗ CMake构建工具不可用" << std::endl;
        }
        
        // 测试Mock数据生成性能
        TestMockDataPerformance();
    }
    
    /**
     * @brief 测试内存对齐性能
     */
    static void TestMemoryAlignment() {
        constexpr int iterations = 1000000;
        constexpr int array_size = 1024;
        
        // 使用对齐内存分配
        alignas(64) double aligned_array[array_size];
        
        const auto start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < iterations; ++i) {
            for (int j = 0; j < array_size; ++j) {
                aligned_array[j] = static_cast<double>(i * j);
            }
        }
        
        const auto end = std::chrono::high_resolution_clock::now();
        const auto aligned_duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        
        std::cout << "  内存对齐访问性能: " << aligned_duration << "μs" << std::endl;
    }
    
    /**
     * @brief 测试龙芯性能特性
     */
    static void TestLoongArchPerformance() {
        // 简单的计算密集型测试，验证龙芯处理器性能
        constexpr int iterations = 100000;
        const auto start = std::chrono::high_resolution_clock::now();
        
        volatile double result = 0.0;
        for (int i = 1; i <= iterations; ++i) { // 从1开始避免sqrt(0)
            const double i_double = static_cast<double>(i);
            result += std::sin(i_double) * std::cos(i_double) + std::sqrt(i_double);
        }
        
        const auto end = std::chrono::high_resolution_clock::now();
        const auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        
        std::cout << "  龙芯浮点运算性能: " << duration << "μs (" << iterations << " 次运算)" << std::endl;
        std::cout << "  平均每次运算: " << std::fixed << std::setprecision(3) 
                  << static_cast<double>(duration) / iterations << "μs" << std::endl;
        
        // 防止编译器优化掉计算
        if (result < 0) {
            std::cout << "  计算结果验证: " << result << std::endl;
        }
    }
    
    /**
     * @brief 测试Mock数据性能
     */
    static void TestMockDataPerformance() {
        // 测试Mock数据生成和处理性能
        constexpr int iterations = 10000;
        constexpr std::string_view mock_nmea_template = 
            "$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47";
        
        const auto start = std::chrono::high_resolution_clock::now();
        
        std::string timestamp;
        timestamp.reserve(6); // 预分配内存避免重复分配
        
        // 预先创建字符串避免重复构造
        const std::string mock_nmea{mock_nmea_template};
        const size_t comma_pos = mock_nmea.find(',');
        
        for (int i = 0; i < iterations; ++i) {
            // 简单的字符串处理
            if (comma_pos != std::string::npos && comma_pos + 7 < mock_nmea.length()) {
                timestamp.assign(mock_nmea, comma_pos + 1, 6);
                // 防止编译器优化掉处理逻辑
                volatile auto len = timestamp.length();
                (void)len; // 避免未使用变量警告
            }
        }
        
        const auto end = std::chrono::high_resolution_clock::now();
        const auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        
        std::cout << "  Mock数据处理性能: " << duration << "μs (" << iterations << " 次处理)" << std::endl;
        std::cout << "  平均每次处理: " << std::fixed << std::setprecision(3) 
                  << static_cast<double>(duration) / iterations << "μs" << std::endl;
    }
    
    /**
     * @brief 生成详细报告
     */
    static void GenerateDetailedReport(const PlatformValidationReport& validation_report,
                                     const PlatformBenchmarkReport& benchmark_report,
                                     const PlatformInfo& platform_info) {
        std::string filename = "detailed_platform_report_" + 
                              PlatformDetector::PlatformTypeToString(platform_info.type) + ".txt";
        
        std::ofstream report_file(filename);
        if (!report_file.is_open()) {
            std::cerr << "无法创建详细报告文件: " << filename << std::endl;
            return;
        }
        
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        report_file << "=== 平台详细基准测试报告 ===" << std::endl;
        report_file << "生成时间: " << std::ctime(&time_t) << std::endl;
        report_file << "平台信息:" << std::endl;
        report_file << "  类型: " << platform_info.description << std::endl;
        report_file << "  操作系统: " << platform_info.os_name << std::endl;
        report_file << "  处理器架构: " << platform_info.architecture << std::endl;
        report_file << "  内核版本: " << platform_info.kernel_version << std::endl;
        report_file << "  开发环境: " << (platform_info.is_development_env ? "是" : "否") << std::endl;
        report_file << std::endl;
        
        // 验证结果详情
        report_file << "=== 功能验证结果 ===" << std::endl;
        report_file << "总体状态: " << (validation_report.overall_success ? "通过" : "失败") << std::endl;
        report_file << "执行时间: " << validation_report.total_execution_time_ms << " ms" << std::endl;
        report_file << "统计: 成功=" << validation_report.success_count 
                    << ", 警告=" << validation_report.warning_count 
                    << ", 错误=" << validation_report.error_count 
                    << ", 严重=" << validation_report.critical_count << std::endl;
        report_file << std::endl;
        
        for (const auto& item : validation_report.validation_items) {
            std::string result_str;
            switch (item.result_type) {
                case ValidationResultType::SUCCESS: result_str = "成功"; break;
                case ValidationResultType::WARNING: result_str = "警告"; break;
                case ValidationResultType::ERROR: result_str = "错误"; break;
                case ValidationResultType::CRITICAL: result_str = "严重"; break;
            }
            report_file << "[" << result_str << "] " << item.test_name << ": " << item.message << std::endl;
            if (!item.details.empty()) {
                report_file << "  详情: " << item.details << std::endl;
            }
            if (item.execution_time_ms > 0) {
                report_file << "  执行时间: " << item.execution_time_ms << " ms" << std::endl;
            }
            report_file << std::endl;
        }
        
        // 性能基准测试详情
        report_file << "=== 性能基准测试结果 ===" << std::endl;
        report_file << "总体评分: " << benchmark_report.overall_score << "/100 (" 
                    << benchmark_report.performance_grade << ")" << std::endl;
        report_file << std::endl;
        
        for (const auto& benchmark : benchmark_report.benchmarks) {
            report_file << benchmark.benchmark_name << ":" << std::endl;
            report_file << "  平均延迟: " << benchmark.average_latency_ns << " ns" << std::endl;
            report_file << "  最大延迟: " << benchmark.max_latency_ns << " ns" << std::endl;
            report_file << "  最小延迟: " << benchmark.min_latency_ns << " ns" << std::endl;
            report_file << "  吞吐量: " << benchmark.throughput_ops_per_sec << " ops/s" << std::endl;
            report_file << "  CPU使用率: " << benchmark.cpu_usage_percent << "%" << std::endl;
            report_file << "  内存使用: " << benchmark.memory_usage_mb << " MB" << std::endl;
            report_file << std::endl;
        }
        
        // 优化建议
        report_file << "=== 优化建议 ===" << std::endl;
        for (const auto& recommendation : validation_report.recommendations) {
            report_file << "- " << recommendation << std::endl;
        }
        report_file << std::endl;
        
        for (const auto& tip : benchmark_report.optimization_tips) {
            report_file << "- " << tip << std::endl;
        }
        
        report_file.close();
        std::cout << "详细报告已保存到: " << filename << std::endl;
    }
    
    /**
     * @brief 安全检查工具可用性
     * @param tool_name 工具名称
     * @return 工具是否可用
     */
    static bool CheckToolAvailability(const std::string& tool_name) {
        // 使用更安全的方法检查工具可用性
        const std::string command = "which " + tool_name + " > /dev/null 2>&1";
        return system(command.c_str()) == 0;
    }
    
    /**
     * @brief 提供优化指导
     */
    static void ProvideOptimizationGuidance(PlatformType platform_type, 
                                          const PlatformBenchmarkReport& benchmark_report) {
        std::cout << "基于测试结果的优化建议:" << std::endl;
        
        // 基于性能评分提供建议
        if (benchmark_report.overall_score < 60) {
            std::cout << "  🔴 性能评分较低，建议进行以下优化:" << std::endl;
            std::cout << "    - 检查系统资源使用情况" << std::endl;
            std::cout << "    - 关闭不必要的后台进程" << std::endl;
            std::cout << "    - 考虑硬件升级" << std::endl;
        } else if (benchmark_report.overall_score < 80) {
            std::cout << "  🟡 性能评分中等，可考虑以下优化:" << std::endl;
            std::cout << "    - 调整系统调度策略" << std::endl;
            std::cout << "    - 优化编译器选项" << std::endl;
            std::cout << "    - 启用特定平台优化" << std::endl;
        } else {
            std::cout << "  🟢 性能评分良好，系统运行状态佳" << std::endl;
        }
        
        // 平台特定优化建议
        switch (platform_type) {
            case PlatformType::LINUX_X86_64:
                std::cout << "\nLinux x86_64特定优化建议:" << std::endl;
                std::cout << "  - 启用实时内核(PREEMPT_RT)" << std::endl;
                std::cout << "  - 配置CPU隔离(isolcpus)" << std::endl;
                std::cout << "  - 使用高精度定时器" << std::endl;
                std::cout << "  - 调整网络中断亲和性" << std::endl;
                break;
                
            case PlatformType::LINUX_LOONGARCH64:
                std::cout << "\n龙芯LoongArch64特定优化建议:" << std::endl;
                std::cout << "  - 使用龙芯优化编译器" << std::endl;
                std::cout << "  - 启用龙芯特定内核选项" << std::endl;
                std::cout << "  - 配置龙芯缓存策略" << std::endl;
                std::cout << "  - 使用龙芯优化数学库" << std::endl;
                break;
                
            case PlatformType::MACOS_X86_64:
            case PlatformType::MACOS_ARM64:
                std::cout << "\nmacOS开发环境优化建议:" << std::endl;
                std::cout << "  - 使用Xcode优化编译选项" << std::endl;
                std::cout << "  - 配置实时调度策略" << std::endl;
                std::cout << "  - 优化Mock数据生成" << std::endl;
                std::cout << "  - 使用高精度定时器API" << std::endl;
                break;
                
            default:
                break;
        }
        
        std::cout << "\n通用优化建议:" << std::endl;
        std::cout << "  - 定期监控系统性能指标" << std::endl;
        std::cout << "  - 保持系统和驱动程序更新" << std::endl;
        std::cout << "  - 根据工作负载调整配置参数" << std::endl;
        std::cout << "  - 实施性能回归测试" << std::endl;
    }
};

/**
 * @brief 主函数
 */
int main(int argc __attribute__((unused)), char* argv[] __attribute__((unused))) {
    return PlatformBenchmarkTool::RunBenchmarkSuite();
}