#include "core/logger.h"
#include "core/types.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <algorithm>
#include <regex>
#include <cstring>
#include <ctime>

#ifdef __linux__
#include <syslog.h>
#include <unistd.h>
#elif __APPLE__
#include <syslog.h>
#include <unistd.h>
#endif

namespace timing_server {
namespace core {

// LogFilter实现
bool LogFilter::Matches(const LogEntry& entry) const {
    // 检查日志级别
    if (entry.level < min_level || entry.level > max_level) {
        return false;
    }
    
    // 检查组件过滤
    if (!components.empty()) {
        bool component_match = false;
        for (LogComponent comp : components) {
            if (entry.component == comp) {
                component_match = true;
                break;
            }
        }
        if (!component_match) {
            return false;
        }
    }
    
    // 检查时间范围
    if (entry.timestamp_ns < start_time_ns || entry.timestamp_ns > end_time_ns) {
        return false;
    }
    
    // 检查消息模式匹配
    if (!message_pattern.empty()) {
        try {
            std::regex pattern(message_pattern, std::regex_constants::icase);
            if (!std::regex_search(entry.message, pattern)) {
                return false;
            }
        } catch (const std::regex_error& e) {
            // 如果正则表达式无效，使用简单的字符串包含匹配
            std::string lower_message = entry.message;
            std::string lower_pattern = message_pattern;
            std::transform(lower_message.begin(), lower_message.end(), lower_message.begin(), ::tolower);
            std::transform(lower_pattern.begin(), lower_pattern.end(), lower_pattern.begin(), ::tolower);
            if (lower_message.find(lower_pattern) == std::string::npos) {
                return false;
            }
        }
    }
    
    return true;
}

// FileLogOutput实现
FileLogOutput::FileLogOutput(const std::string& file_path, const LogRotationConfig& rotation_config)
    : file_path_(file_path)
    , rotation_config_(rotation_config)
    , shutdown_requested_(false) {
    
    // 确保日志目录存在
    std::filesystem::path log_path(file_path_);
    std::filesystem::create_directories(log_path.parent_path());
    
    // 打开日志文件
    file_stream_ = std::make_unique<std::ofstream>(file_path_, std::ios::app);
    
    // 启动轮转检查线程
    rotation_thread_ = std::make_unique<std::thread>(&FileLogOutput::RotationThreadFunc, this);
}

FileLogOutput::~FileLogOutput() {
    Close();
}

void FileLogOutput::WriteLog(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (file_stream_ && file_stream_->is_open()) {
        *file_stream_ << FormatLogEntry(entry) << std::endl;
        file_stream_->flush();
    }
}

void FileLogOutput::Flush() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (file_stream_ && file_stream_->is_open()) {
        file_stream_->flush();
    }
}

void FileLogOutput::Close() {
    shutdown_requested_ = true;
    
    if (rotation_thread_ && rotation_thread_->joinable()) {
        rotation_thread_->join();
        rotation_thread_.reset();
    }
    
    std::lock_guard<std::mutex> lock(file_mutex_);
    if (file_stream_ && file_stream_->is_open()) {
        file_stream_->close();
        file_stream_.reset();
    }
}

size_t FileLogOutput::GetCurrentFileSize() const {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    try {
        if (std::filesystem::exists(file_path_)) {
            return std::filesystem::file_size(file_path_);
        }
    } catch (const std::exception& e) {
        // 忽略异常
    }
    
    return 0;
}

void FileLogOutput::RotateNow() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    PerformRotation();
}

bool FileLogOutput::NeedsRotation() const {
    size_t current_size = GetCurrentFileSize();
    return current_size >= (rotation_config_.max_file_size_mb * 1024 * 1024);
}

void FileLogOutput::PerformRotation() {
    if (!file_stream_ || !file_stream_->is_open()) {
        return;
    }
    
    // 关闭当前文件
    file_stream_->close();
    
    // 轮转文件名
    std::filesystem::path base_path(file_path_);
    std::string base_name = base_path.stem().string();
    std::string extension = base_path.extension().string();
    std::string dir_path = base_path.parent_path().string();
    
    // 移动现有的轮转文件
    for (int i = rotation_config_.max_files - 1; i > 0; --i) {
        std::string old_file = dir_path + "/" + base_name + "." + std::to_string(i) + extension;
        std::string new_file = dir_path + "/" + base_name + "." + std::to_string(i + 1) + extension;
        
        if (std::filesystem::exists(old_file)) {
            if (i == static_cast<int>(rotation_config_.max_files - 1)) {
                // 删除最老的文件
                std::filesystem::remove(old_file);
            } else {
                std::filesystem::rename(old_file, new_file);
            }
        }
    }
    
    // 移动当前文件到 .1
    std::string rotated_file = dir_path + "/" + base_name + ".1" + extension;
    std::filesystem::rename(file_path_, rotated_file);
    
    // 重新打开新文件
    file_stream_ = std::make_unique<std::ofstream>(file_path_, std::ios::app);
    
    // 压缩旧文件
    if (rotation_config_.enable_compression) {
        CompressOldFiles();
    }
}

void FileLogOutput::CompressOldFiles() {
    // 简单的gzip压缩实现
    // 在实际项目中，建议使用专业的压缩库如zlib
    std::filesystem::path base_path(file_path_);
    std::string base_name = base_path.stem().string();
    std::string extension = base_path.extension().string();
    std::string dir_path = base_path.parent_path().string();
    
    for (size_t i = 1; i <= rotation_config_.max_files; ++i) {
        std::string log_file = dir_path + "/" + base_name + "." + std::to_string(i) + extension;
        std::string compressed_file = log_file + ".gz";
        
        if (std::filesystem::exists(log_file) && !std::filesystem::exists(compressed_file)) {
            // 使用系统gzip命令压缩
            std::string command = "gzip \"" + log_file + "\"";
            int result = std::system(command.c_str());
            (void)result; // 忽略返回值
        }
    }
}

void FileLogOutput::RotationThreadFunc() {
    while (!shutdown_requested_) {
        if (NeedsRotation()) {
            std::lock_guard<std::mutex> lock(file_mutex_);
            PerformRotation();
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(rotation_config_.rotation_check_interval_s));
    }
}

std::string FileLogOutput::FormatLogEntry(const LogEntry& entry) const {
    std::ostringstream oss;
    
    // 时间戳格式化
    auto seconds = entry.timestamp_ns / 1000000000ULL;
    auto nanoseconds = entry.timestamp_ns % 1000000000ULL;
    std::time_t time = static_cast<std::time_t>(seconds);
    std::tm* utc_tm = std::gmtime(&time);
    
    oss << std::put_time(utc_tm, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(9) << nanoseconds;
    
    // 日志级别
    oss << " [" << LogLevelToString(entry.level) << "]";
    
    // 组件
    oss << " [" << LogComponentToString(entry.component) << "]";
    
    // 位置信息
    if (!entry.file.empty()) {
        std::filesystem::path file_path(entry.file);
        oss << " " << file_path.filename().string() << ":" << entry.line;
        if (!entry.function.empty()) {
            oss << " (" << entry.function << ")";
        }
    }
    
    // 消息
    oss << " - " << entry.message;
    
    // 上下文信息
    if (!entry.context.empty()) {
        oss << " {";
        bool first = true;
        for (const auto& ctx : entry.context) {
            if (!first) oss << ", ";
            oss << ctx.first << "=" << ctx.second;
            first = false;
        }
        oss << "}";
    }
    
    return oss.str();
}

// ConsoleLogOutput实现
ConsoleLogOutput::ConsoleLogOutput(bool enable_colors)
    : enable_colors_(enable_colors) {
    
    // 在非终端环境下禁用颜色
    if (enable_colors_ && !isatty(STDOUT_FILENO)) {
        enable_colors_ = false;
    }
}

void ConsoleLogOutput::WriteLog(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(console_mutex_);
    
    std::string formatted = FormatLogEntry(entry);
    
    // 根据日志级别选择输出流
    if (entry.level >= LogLevel::ERROR) {
        std::cerr << formatted << std::endl;
    } else {
        std::cout << formatted << std::endl;
    }
}

void ConsoleLogOutput::Flush() {
    std::lock_guard<std::mutex> lock(console_mutex_);
    std::cout.flush();
    std::cerr.flush();
}

void ConsoleLogOutput::Close() {
    // 控制台输出不需要特殊关闭操作
}

std::string ConsoleLogOutput::GetColorCode(LogLevel level) const {
    if (!enable_colors_) {
        return "";
    }
    
    switch (level) {
        case LogLevel::TRACE:    return "\033[37m";    // 白色
        case LogLevel::DEBUG:    return "\033[36m";    // 青色
        case LogLevel::INFO:     return "\033[32m";    // 绿色
        case LogLevel::WARNING:  return "\033[33m";    // 黄色
        case LogLevel::ERROR:    return "\033[31m";    // 红色
        case LogLevel::CRITICAL: return "\033[35m";    // 紫色
        default:                 return "\033[0m";     // 重置
    }
}

std::string ConsoleLogOutput::FormatLogEntry(const LogEntry& entry) const {
    std::ostringstream oss;
    
    std::string color_code = GetColorCode(entry.level);
    std::string reset_code = enable_colors_ ? "\033[0m" : "";
    
    // 时间戳格式化（简化版）
    auto seconds = entry.timestamp_ns / 1000000000ULL;
    auto milliseconds = (entry.timestamp_ns % 1000000000ULL) / 1000000ULL;
    std::time_t time = static_cast<std::time_t>(seconds);
    std::tm* local_tm = std::localtime(&time);
    
    oss << color_code;
    oss << std::put_time(local_tm, "%H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << milliseconds;
    
    // 日志级别（简化显示）
    std::string level_str = LogLevelToString(entry.level);
    if (level_str.length() > 4) {
        level_str = level_str.substr(0, 4);
    }
    oss << " [" << std::setw(4) << level_str << "]";
    
    // 组件（简化显示）
    std::string comp_str = LogComponentToString(entry.component);
    if (comp_str.length() > 12) {
        comp_str = comp_str.substr(0, 12);
    }
    oss << " [" << std::setw(12) << comp_str << "]";
    
    oss << reset_code;
    
    // 消息
    oss << " " << entry.message;
    
    return oss.str();
}

// SyslogOutput实现
SyslogOutput::SyslogOutput(const std::string& ident)
    : ident_(ident), syslog_opened_(false) {
    
#ifdef __linux__
    openlog(ident_.c_str(), LOG_PID | LOG_NDELAY, LOG_DAEMON);
    syslog_opened_ = true;
#elif __APPLE__
    openlog(ident_.c_str(), LOG_PID | LOG_NDELAY, LOG_DAEMON);
    syslog_opened_ = true;
#endif
}

SyslogOutput::~SyslogOutput() {
    Close();
}

void SyslogOutput::WriteLog(const LogEntry& entry) {
    if (!syslog_opened_) {
        return;
    }
    
#if defined(__linux__) || defined(__APPLE__)
    int priority = LogLevelToSyslogPriority(entry.level);
    
    std::ostringstream oss;
    oss << "[" << LogComponentToString(entry.component) << "] " << entry.message;
    
    if (!entry.context.empty()) {
        oss << " {";
        bool first = true;
        for (const auto& ctx : entry.context) {
            if (!first) oss << ", ";
            oss << ctx.first << "=" << ctx.second;
            first = false;
        }
        oss << "}";
    }
    
    syslog(priority, "%s", oss.str().c_str());
#endif
}

void SyslogOutput::Flush() {
    // syslog自动刷新，无需手动操作
}

void SyslogOutput::Close() {
    if (syslog_opened_) {
#if defined(__linux__) || defined(__APPLE__)
        closelog();
#endif
        syslog_opened_ = false;
    }
}

int SyslogOutput::LogLevelToSyslogPriority(LogLevel level) const {
#if defined(__linux__) || defined(__APPLE__)
    switch (level) {
        case LogLevel::TRACE:    return LOG_DEBUG;
        case LogLevel::DEBUG:    return LOG_DEBUG;
        case LogLevel::INFO:     return LOG_INFO;
        case LogLevel::WARNING:  return LOG_WARNING;
        case LogLevel::ERROR:    return LOG_ERR;
        case LogLevel::CRITICAL: return LOG_CRIT;
        default:                 return LOG_INFO;
    }
#else
    return 0;
#endif
}

// Logger实现
Logger& Logger::GetInstance() {
    static Logger instance;
    return instance;
}

Logger::~Logger() {
    Shutdown();
}

bool Logger::Initialize(const std::string& config_file_path) {
    std::lock_guard<std::mutex> lock(outputs_mutex_);
    
    // 设置默认配置
    global_log_level_ = LogLevel::INFO;
    async_logging_enabled_ = true;
    max_history_size_ = 10000;
    shutdown_requested_ = false;
    
    // 加载配置文件
    if (!config_file_path.empty()) {
        LoadConfig(config_file_path);
    }
    
    // 启动异步工作线程
    if (async_logging_enabled_) {
        worker_thread_ = std::make_unique<std::thread>(&Logger::AsyncWorkerThread, this);
    }
    
    return true;
}

void Logger::Shutdown() {
    shutdown_requested_ = true;
    
    // 停止异步工作线程
    if (worker_thread_ && worker_thread_->joinable()) {
        queue_condition_.notify_all();
        worker_thread_->join();
        worker_thread_.reset();
    }
    
    // 关闭所有输出
    std::lock_guard<std::mutex> lock(outputs_mutex_);
    for (auto& output : outputs_) {
        output->Close();
    }
    outputs_.clear();
}

void Logger::SetLogLevel(LogLevel level) {
    global_log_level_ = level;
}

LogLevel Logger::GetLogLevel() const {
    return global_log_level_;
}

void Logger::SetComponentLogLevel(LogComponent component, LogLevel level) {
    component_log_levels_[component] = level;
}

void Logger::AddOutput(std::unique_ptr<ILogOutput> output) {
    std::lock_guard<std::mutex> lock(outputs_mutex_);
    outputs_.push_back(std::move(output));
}

void Logger::ClearOutputs() {
    std::lock_guard<std::mutex> lock(outputs_mutex_);
    for (auto& output : outputs_) {
        output->Close();
    }
    outputs_.clear();
}

void Logger::Log(LogLevel level, LogComponent component, const std::string& message,
                 const std::string& file, int line, const std::string& function) {
    
    if (!IsLogEnabled(level, component)) {
        return;
    }
    
    LogEntry entry(level, component, message, file, line, function);
    
    if (async_logging_enabled_) {
        // 异步日志记录
        std::lock_guard<std::mutex> lock(queue_mutex_);
        log_queue_.push(entry);
        queue_condition_.notify_one();
    } else {
        // 同步日志记录
        ProcessLogEntry(entry);
    }
}

void Logger::LogWithContext(LogLevel level, LogComponent component, const std::string& message,
                           const std::map<std::string, std::string>& context,
                           const std::string& file, int line, const std::string& function) {
    
    if (!IsLogEnabled(level, component)) {
        return;
    }
    
    LogEntry entry(level, component, message, file, line, function);
    entry.context = context;
    
    if (async_logging_enabled_) {
        // 异步日志记录
        std::lock_guard<std::mutex> lock(queue_mutex_);
        log_queue_.push(entry);
        queue_condition_.notify_one();
    } else {
        // 同步日志记录
        ProcessLogEntry(entry);
    }
}

std::vector<LogEntry> Logger::SearchLogs(const LogFilter& filter) const {
    std::lock_guard<std::mutex> lock(history_mutex_);
    
    std::vector<LogEntry> results;
    results.reserve(std::min(filter.max_results, log_history_.size()));
    
    for (const auto& entry : log_history_) {
        if (filter.Matches(entry)) {
            results.push_back(entry);
            if (results.size() >= filter.max_results) {
                break;
            }
        }
    }
    
    return results;
}

std::vector<LogEntry> Logger::GetRecentLogs(size_t count, LogComponent component) const {
    std::lock_guard<std::mutex> lock(history_mutex_);
    
    std::vector<LogEntry> results;
    results.reserve(std::min(count, log_history_.size()));
    
    // 从最新的日志开始查找
    for (auto it = log_history_.rbegin(); it != log_history_.rend() && results.size() < count; ++it) {
        if (component == LogComponent::SYSTEM || it->component == component) {
            results.push_back(*it);
        }
    }
    
    // 反转结果，使其按时间顺序排列
    std::reverse(results.begin(), results.end());
    
    return results;
}

void Logger::FlushAll() {
    std::lock_guard<std::mutex> lock(outputs_mutex_);
    for (auto& output : outputs_) {
        output->Flush();
    }
}

void Logger::SetAsyncLogging(bool enable) {
    if (enable == async_logging_enabled_) {
        return;
    }
    
    if (enable) {
        // 启用异步日志
        async_logging_enabled_ = true;
        shutdown_requested_ = false;
        worker_thread_ = std::make_unique<std::thread>(&Logger::AsyncWorkerThread, this);
    } else {
        // 禁用异步日志
        shutdown_requested_ = true;
        if (worker_thread_ && worker_thread_->joinable()) {
            queue_condition_.notify_all();
            worker_thread_->join();
            worker_thread_.reset();
        }
        async_logging_enabled_ = false;
    }
}

bool Logger::IsLogEnabled(LogLevel level, LogComponent component) const {
    // 检查组件特定的日志级别
    auto it = component_log_levels_.find(component);
    if (it != component_log_levels_.end()) {
        return level >= it->second;
    }
    
    // 使用全局日志级别
    return level >= global_log_level_;
}

void Logger::AsyncWorkerThread() {
    while (!shutdown_requested_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        // 等待日志条目或关闭信号
        queue_condition_.wait(lock, [this] {
            return !log_queue_.empty() || shutdown_requested_;
        });
        
        // 处理队列中的所有日志条目
        while (!log_queue_.empty()) {
            LogEntry entry = log_queue_.front();
            log_queue_.pop();
            lock.unlock();
            
            ProcessLogEntry(entry);
            
            lock.lock();
        }
    }
}

void Logger::ProcessLogEntry(const LogEntry& entry) {
    // 添加到历史记录
    AddToHistory(entry);
    
    // 输出到所有注册的输出
    std::lock_guard<std::mutex> lock(outputs_mutex_);
    for (auto& output : outputs_) {
        output->WriteLog(entry);
    }
}

void Logger::AddToHistory(const LogEntry& entry) {
    std::lock_guard<std::mutex> lock(history_mutex_);
    
    log_history_.push_back(entry);
    
    // 限制历史记录大小
    if (log_history_.size() > max_history_size_) {
        log_history_.erase(log_history_.begin(), 
                          log_history_.begin() + (log_history_.size() - max_history_size_));
    }
}

bool Logger::LoadConfig(const std::string& config_file_path) {
    // 简单的配置加载实现
    // 在实际项目中，建议使用JSON或YAML配置文件
    try {
        std::ifstream file(config_file_path);
        if (!file.is_open()) {
            return false;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            // 跳过注释和空行
            if (line.empty() || line[0] == '#') {
                continue;
            }
            
            // 解析配置项
            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = line.substr(0, pos);
                std::string value = line.substr(pos + 1);
                
                // 去除空格
                key.erase(0, key.find_first_not_of(" \t"));
                key.erase(key.find_last_not_of(" \t") + 1);
                value.erase(0, value.find_first_not_of(" \t"));
                value.erase(value.find_last_not_of(" \t") + 1);
                
                if (key == "log_level") {
                    global_log_level_ = StringToLogLevel(value);
                } else if (key == "async_logging") {
                    async_logging_enabled_ = (value == "true" || value == "1");
                } else if (key == "max_history_size") {
                    max_history_size_ = std::stoul(value);
                }
            }
        }
        
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

// 工具函数实现
std::string LogLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE:    return "TRACE";
        case LogLevel::DEBUG:    return "DEBUG";
        case LogLevel::INFO:     return "INFO";
        case LogLevel::WARNING:  return "WARNING";
        case LogLevel::ERROR:    return "ERROR";
        case LogLevel::CRITICAL: return "CRITICAL";
        default:                 return "UNKNOWN";
    }
}

LogLevel StringToLogLevel(const std::string& str) {
    if (str == "TRACE") return LogLevel::TRACE;
    if (str == "DEBUG") return LogLevel::DEBUG;
    if (str == "INFO") return LogLevel::INFO;
    if (str == "WARNING") return LogLevel::WARNING;
    if (str == "ERROR") return LogLevel::ERROR;
    if (str == "CRITICAL") return LogLevel::CRITICAL;
    return LogLevel::INFO; // 默认值
}

std::string LogComponentToString(LogComponent component) {
    switch (component) {
        case LogComponent::SYSTEM:         return "SYSTEM";
        case LogComponent::TIMING_ENGINE:  return "TIMING_ENGINE";
        case LogComponent::STATE_MACHINE:  return "STATE_MACHINE";
        case LogComponent::HAL_GNSS:       return "HAL_GNSS";
        case LogComponent::HAL_RUBIDIUM:   return "HAL_RUBIDIUM";
        case LogComponent::HAL_RTC:        return "HAL_RTC";
        case LogComponent::HAL_PPS:        return "HAL_PPS";
        case LogComponent::HAL_FREQ:       return "HAL_FREQ";
        case LogComponent::HAL_NETWORK:    return "HAL_NETWORK";
        case LogComponent::DAEMON_MANAGER: return "DAEMON_MANAGER";
        case LogComponent::CONFIG_MANAGER: return "CONFIG_MANAGER";
        case LogComponent::API_SERVER:     return "API_SERVER";
        case LogComponent::WEBSOCKET:      return "WEBSOCKET";
        case LogComponent::AUTH_MANAGER:   return "AUTH_MANAGER";
        case LogComponent::PTP4L:          return "PTP4L";
        case LogComponent::CHRONY:         return "CHRONY";
        case LogComponent::TS2PHC:         return "TS2PHC";
        default:                           return "UNKNOWN";
    }
}

LogComponent StringToLogComponent(const std::string& str) {
    if (str == "SYSTEM") return LogComponent::SYSTEM;
    if (str == "TIMING_ENGINE") return LogComponent::TIMING_ENGINE;
    if (str == "STATE_MACHINE") return LogComponent::STATE_MACHINE;
    if (str == "HAL_GNSS") return LogComponent::HAL_GNSS;
    if (str == "HAL_RUBIDIUM") return LogComponent::HAL_RUBIDIUM;
    if (str == "HAL_RTC") return LogComponent::HAL_RTC;
    if (str == "HAL_PPS") return LogComponent::HAL_PPS;
    if (str == "HAL_FREQ") return LogComponent::HAL_FREQ;
    if (str == "HAL_NETWORK") return LogComponent::HAL_NETWORK;
    if (str == "DAEMON_MANAGER") return LogComponent::DAEMON_MANAGER;
    if (str == "CONFIG_MANAGER") return LogComponent::CONFIG_MANAGER;
    if (str == "API_SERVER") return LogComponent::API_SERVER;
    if (str == "WEBSOCKET") return LogComponent::WEBSOCKET;
    if (str == "AUTH_MANAGER") return LogComponent::AUTH_MANAGER;
    if (str == "PTP4L") return LogComponent::PTP4L;
    if (str == "CHRONY") return LogComponent::CHRONY;
    if (str == "TS2PHC") return LogComponent::TS2PHC;
    return LogComponent::SYSTEM; // 默认值
}

} // namespace core
} // namespace timing_server