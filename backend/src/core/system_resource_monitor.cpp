#include "core/system_resource_monitor.h"
#include "core/logger.h"
#include <algorithm>
#include <stdexcept>

#ifdef __linux__
#include <fstream>
#include <sstream>
#include <sys/resource.h>
#elif __APPLE__
#include <sys/resource.h>
#include <mach/mach.h>
#include <mach/task.h>
#include <mach/mach_init.h>
#endif

namespace timing_server {
namespace core {

#ifdef __linux__
/**
 * @brief Linux平台系统资源监控实现
 */
class LinuxSystemResourceMonitor : public ISystemResourceMonitor {
public:
    double GetCpuUsage() override {
        try {
            std::ifstream stat_file("/proc/stat");
            if (!stat_file.is_open()) {
                LOG_WARNING(LogComponent::SYSTEM, "无法打开/proc/stat文件");
                return 0.0;
            }
            
            std::string line;
            std::getline(stat_file, line);
            
            std::istringstream iss(line);
            std::string cpu_label;
            long user, nice, system, idle, iowait, irq, softirq, steal;
            
            if (!(iss >> cpu_label >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal)) {
                LOG_WARNING(LogComponent::SYSTEM, "解析/proc/stat数据失败");
                return 0.0;
            }
            
            long total = user + nice + system + idle + iowait + irq + softirq + steal;
            long total_idle = idle + iowait;
            
            if (last_total_ != 0) {
                long total_diff = total - last_total_;
                long idle_diff = total_idle - last_idle_;
                
                if (total_diff > 0) {
                    double cpu_usage = 100.0 * (total_diff - idle_diff) / total_diff;
                    last_total_ = total;
                    last_idle_ = total_idle;
                    return std::clamp(cpu_usage, 0.0, 100.0);
                }
            }
            
            last_total_ = total;
            last_idle_ = total_idle;
            return 0.0;
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "获取CPU使用率失败: " + std::string(e.what()));
            return 0.0;
        }
    }
    
    uint64_t GetMemoryUsage() override {
        try {
            std::ifstream status_file("/proc/self/status");
            if (!status_file.is_open()) {
                LOG_WARNING(LogComponent::SYSTEM, "无法打开/proc/self/status文件");
                return 0;
            }
            
            std::string line;
            while (std::getline(status_file, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label, value, unit;
                    if (iss >> label >> value >> unit) {
                        return std::stoull(value) / 1024; // 转换为MB
                    }
                    break;
                }
            }
            
            LOG_WARNING(LogComponent::SYSTEM, "未找到VmRSS信息");
            return 0;
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "获取内存使用量失败: " + std::string(e.what()));
            return 0;
        }
    }
    
    uint32_t GetNetworkLatency() override {
        // TODO: 实现网络延迟测量
        return 0;
    }

private:
    long last_total_ = 0;
    long last_idle_ = 0;
};

#elif __APPLE__
/**
 * @brief macOS平台系统资源监控实现
 */
class MacOSSystemResourceMonitor : public ISystemResourceMonitor {
public:
    double GetCpuUsage() override {
        try {
            struct rusage usage;
            if (getrusage(RUSAGE_SELF, &usage) != 0) {
                LOG_WARNING(LogComponent::SYSTEM, "getrusage调用失败");
                return 0.0;
            }
            
            if (!first_call_) {
                auto current_time = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::microseconds>(
                    current_time - last_measurement_time_).count() / 1000000.0;
                
                double user_time = (usage.ru_utime.tv_sec - last_usage_.ru_utime.tv_sec) + 
                                 (usage.ru_utime.tv_usec - last_usage_.ru_utime.tv_usec) / 1000000.0;
                double sys_time = (usage.ru_stime.tv_sec - last_usage_.ru_stime.tv_sec) + 
                                (usage.ru_stime.tv_usec - last_usage_.ru_stime.tv_usec) / 1000000.0;
                
                if (elapsed > 0) {
                    double cpu_usage = ((user_time + sys_time) / elapsed) * 100.0;
                    last_usage_ = usage;
                    last_measurement_time_ = current_time;
                    return std::clamp(cpu_usage, 0.0, 100.0);
                }
            }
            
            last_usage_ = usage;
            last_measurement_time_ = std::chrono::steady_clock::now();
            first_call_ = false;
            return 0.0;
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "获取CPU使用率失败: " + std::string(e.what()));
            return 0.0;
        }
    }
    
    uint64_t GetMemoryUsage() override {
        try {
            struct rusage usage;
            if (getrusage(RUSAGE_SELF, &usage) == 0) {
                return usage.ru_maxrss / (1024 * 1024); // 转换为MB
            }
            
            LOG_WARNING(LogComponent::SYSTEM, "getrusage调用失败");
            return 0;
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "获取内存使用量失败: " + std::string(e.what()));
            return 0;
        }
    }
    
    uint32_t GetNetworkLatency() override {
        // TODO: 实现网络延迟测量
        return 0;
    }

private:
    struct rusage last_usage_ = {};
    std::chrono::steady_clock::time_point last_measurement_time_;
    bool first_call_ = true;
};
#endif

// 缓存系统资源监控器实现
CachedSystemResourceMonitor::CachedSystemResourceMonitor(std::unique_ptr<ISystemResourceMonitor> monitor)
    : monitor_(std::move(monitor))
    , cached_cpu_usage_(0.0)
    , cached_memory_usage_(0)
    , cached_network_latency_(0) {
}

double CachedSystemResourceMonitor::GetCpuUsage() {
    auto current_time = std::chrono::steady_clock::now();
    if (current_time - last_cpu_measurement_ >= CPU_CACHE_DURATION) {
        cached_cpu_usage_ = monitor_->GetCpuUsage();
        last_cpu_measurement_ = current_time;
    }
    return cached_cpu_usage_;
}

uint64_t CachedSystemResourceMonitor::GetMemoryUsage() {
    auto current_time = std::chrono::steady_clock::now();
    if (current_time - last_memory_measurement_ >= MEMORY_CACHE_DURATION) {
        cached_memory_usage_ = monitor_->GetMemoryUsage();
        last_memory_measurement_ = current_time;
    }
    return cached_memory_usage_;
}

uint32_t CachedSystemResourceMonitor::GetNetworkLatency() {
    auto current_time = std::chrono::steady_clock::now();
    if (current_time - last_network_measurement_ >= NETWORK_CACHE_DURATION) {
        cached_network_latency_ = monitor_->GetNetworkLatency();
        last_network_measurement_ = current_time;
    }
    return cached_network_latency_;
}

std::unique_ptr<ISystemResourceMonitor> CreateSystemResourceMonitor() {
#ifdef __linux__
    return std::make_unique<LinuxSystemResourceMonitor>();
#elif __APPLE__
    return std::make_unique<MacOSSystemResourceMonitor>();
#else
    #error "Unsupported platform"
#endif
}

} // namespace core
} // namespace timing_server