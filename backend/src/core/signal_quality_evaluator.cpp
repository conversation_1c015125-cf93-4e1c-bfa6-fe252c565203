#include "core/signal_quality_evaluator.h"
#include <sstream>
#include <algorithm>
#include <cmath>
#include <iostream>
#include <numeric>

namespace timing_server {
namespace core {

// ============================================================================
// GnssSignalQualityEvaluator Implementation
// ============================================================================

GnssSignalQualityEvaluator::GnssSignalQualityEvaluator()
    : min_satellites_(4)
    , min_signal_strength_(-150.0)
    , max_pdop_(10.0) {
    history_.reserve(100); // 预留历史数据空间
}

GnssSignalQuality GnssSignalQualityEvaluator::EvaluateGnssQuality(const std::string& gnss_data) {
    GnssSignalQuality quality;
    
    if (gnss_data.empty()) {
        return quality; // 返回默认的无效质量
    }
    
    // 按行分割NMEA数据
    std::istringstream stream(gnss_data);
    std::string line;
    
    while (std::getline(stream, line)) {
        if (line.empty() || line[0] != '$') {
            continue;
        }
        
        // 解析不同类型的NMEA语句
        if (line.find("$GPGGA") != std::string::npos || line.find("$GNGGA") != std::string::npos) {
            ParseGgaSentence(line, quality);
        } else if (line.find("$GPGSA") != std::string::npos || line.find("$GNGSA") != std::string::npos) {
            ParseGsaSentence(line, quality);
        }
    }
    
    // 更新时间戳
    quality.last_update_time_ns = GetCurrentTimestampNs();
    
    // 添加到历史记录
    history_.push_back(quality);
    if (history_.size() > 100) {
        history_.erase(history_.begin());
    }
    
    return quality;
}

bool GnssSignalQualityEvaluator::IsSignalUsable(const GnssSignalQuality& quality) {
    // 检查基本质量指标
    if (quality.satellite_count < min_satellites_) {
        return false;
    }
    
    if (quality.signal_strength_dbm < min_signal_strength_) {
        return false;
    }
    
    if (quality.position_dilution > max_pdop_) {
        return false;
    }
    
    if (quality.fix_quality == 0) {
        return false;
    }
    
    // 检查时间精度
    if (quality.time_accuracy_ns > 1000.0) { // 1微秒阈值
        return false;
    }
    
    return true;
}

uint32_t GnssSignalQualityEvaluator::GetQualityScore(const GnssSignalQuality& quality) {
    return CalculateQualityScore(quality);
}

void GnssSignalQualityEvaluator::SetQualityThresholds(uint32_t min_satellites, double min_signal_strength, double max_pdop) {
    min_satellites_ = min_satellites;
    min_signal_strength_ = min_signal_strength;
    max_pdop_ = max_pdop;
}

bool GnssSignalQualityEvaluator::ParseNmeaData(const std::string& nmea_sentence, GnssSignalQuality& quality) {
    if (nmea_sentence.find("$GPGGA") != std::string::npos || nmea_sentence.find("$GNGGA") != std::string::npos) {
        return ParseGgaSentence(nmea_sentence, quality);
    } else if (nmea_sentence.find("$GPGSA") != std::string::npos || nmea_sentence.find("$GNGSA") != std::string::npos) {
        return ParseGsaSentence(nmea_sentence, quality);
    }
    
    return false;
}

bool GnssSignalQualityEvaluator::ParseGgaSentence(const std::string& gga_sentence, GnssSignalQuality& quality) {
    // 简化的GGA解析实现
    // 实际项目中应该使用专业的NMEA解析库
    
    std::istringstream stream(gga_sentence);
    std::string token;
    std::vector<std::string> fields;
    
    // 按逗号分割字段
    while (std::getline(stream, token, ',')) {
        fields.push_back(token);
    }
    
    if (fields.size() < 15) {
        return false; // GGA语句字段不足
    }
    
    try {
        // 字段6: 定位质量指示
        if (!fields[6].empty()) {
            quality.fix_quality = static_cast<uint32_t>(std::stoi(fields[6]));
        }
        
        // 字段7: 使用的卫星数量
        if (!fields[7].empty()) {
            quality.satellite_count = static_cast<uint32_t>(std::stoi(fields[7]));
        }
        
        // 字段8: 水平精度因子
        if (!fields[8].empty()) {
            quality.position_dilution = std::stod(fields[8]);
        }
        
        // 字段9: 海拔高度
        if (!fields[9].empty()) {
            // 可以用于计算垂直精度
            quality.vertical_accuracy_m = std::abs(std::stod(fields[9])) * 0.1; // 简化计算
        }
        
        // 估算信号强度（基于卫星数量和HDOP）
        if (quality.satellite_count > 0 && quality.position_dilution > 0) {
            quality.signal_strength_dbm = -140.0 - (quality.position_dilution * 2.0) + (quality.satellite_count * 1.5);
        }
        
        // 估算时间精度（基于定位质量）
        switch (quality.fix_quality) {
            case 0: quality.time_accuracy_ns = 1000000.0; break; // 无效
            case 1: quality.time_accuracy_ns = 100.0; break;     // GPS
            case 2: quality.time_accuracy_ns = 50.0; break;      // DGPS
            default: quality.time_accuracy_ns = 200.0; break;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "GGA解析错误: " << e.what() << std::endl;
        return false;
    }
}

bool GnssSignalQualityEvaluator::ParseGsaSentence(const std::string& gsa_sentence, GnssSignalQuality& quality) {
    // 简化的GSA解析实现
    std::istringstream stream(gsa_sentence);
    std::string token;
    std::vector<std::string> fields;
    
    // 按逗号分割字段
    while (std::getline(stream, token, ',')) {
        fields.push_back(token);
    }
    
    if (fields.size() < 18) {
        return false; // GSA语句字段不足
    }
    
    try {
        // 字段15: PDOP (位置精度因子)
        if (!fields[15].empty()) {
            quality.position_dilution = std::stod(fields[15]);
        }
        
        // 字段17: TDOP (时间精度因子)
        if (!fields[17].empty()) {
            quality.time_dilution = std::stod(fields[17]);
        }
        
        // 根据TDOP计算时间精度
        if (quality.time_dilution > 0) {
            quality.time_accuracy_ns = quality.time_dilution * 10.0; // 简化计算
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "GSA解析错误: " << e.what() << std::endl;
        return false;
    }
}

uint32_t GnssSignalQualityEvaluator::CalculateQualityScore(const GnssSignalQuality& quality) {
    uint32_t score = 0;
    
    // 卫星数量评分 (0-30分)
    if (quality.satellite_count >= 12) {
        score += 30;
    } else if (quality.satellite_count >= 8) {
        score += 25;
    } else if (quality.satellite_count >= 6) {
        score += 20;
    } else if (quality.satellite_count >= 4) {
        score += 15;
    }
    
    // 信号强度评分 (0-25分)
    if (quality.signal_strength_dbm >= -140.0) {
        score += 25;
    } else if (quality.signal_strength_dbm >= -145.0) {
        score += 20;
    } else if (quality.signal_strength_dbm >= -150.0) {
        score += 15;
    } else if (quality.signal_strength_dbm >= -155.0) {
        score += 10;
    }
    
    // PDOP评分 (0-25分)
    if (quality.position_dilution <= 2.0) {
        score += 25;
    } else if (quality.position_dilution <= 3.0) {
        score += 20;
    } else if (quality.position_dilution <= 5.0) {
        score += 15;
    } else if (quality.position_dilution <= 8.0) {
        score += 10;
    }
    
    // 定位质量评分 (0-20分)
    switch (quality.fix_quality) {
        case 2: score += 20; break; // DGPS
        case 1: score += 15; break; // GPS
        default: break;
    }
    
    return std::min(score, 100u);
}

// ============================================================================
// DiscipliningConvergenceEvaluator Implementation
// ============================================================================

DiscipliningConvergenceEvaluator::DiscipliningConvergenceEvaluator(double convergence_threshold_ns, uint32_t min_stable_samples)
    : convergence_threshold_ns_(convergence_threshold_ns)
    , min_stable_samples_(min_stable_samples)
    , max_samples_(1000) {
    
    phase_errors_.reserve(max_samples_);
    frequency_errors_.reserve(max_samples_);
    timestamps_.reserve(max_samples_);
    
    current_state_.convergence_start_time_ns = GetCurrentTimestampNs();
}

void DiscipliningConvergenceEvaluator::AddSample(double phase_error_ns, double frequency_error_ppm) {
    uint64_t current_time = GetCurrentTimestampNs();
    
    // 添加新样本
    phase_errors_.push_back(phase_error_ns);
    frequency_errors_.push_back(frequency_error_ppm);
    timestamps_.push_back(current_time);
    
    // 限制样本数量
    if (phase_errors_.size() > max_samples_) {
        phase_errors_.erase(phase_errors_.begin());
        frequency_errors_.erase(frequency_errors_.begin());
        timestamps_.erase(timestamps_.begin());
    }
    
    // 更新当前状态
    current_state_.phase_error_ns = phase_error_ns;
    current_state_.frequency_error_ppm = frequency_error_ppm;
    current_state_.total_samples = static_cast<uint32_t>(phase_errors_.size());
}

DiscipliningConvergence DiscipliningConvergenceEvaluator::EvaluateConvergence() {
    if (phase_errors_.empty()) {
        return current_state_;
    }
    
    // 计算趋势
    current_state_.phase_error_trend = CalculateTrend(phase_errors_);
    current_state_.frequency_error_trend = CalculateTrend(frequency_errors_);
    
    // 计算稳定样本数
    current_state_.stable_samples = 0;
    if (phase_errors_.size() >= min_stable_samples_) {
        for (size_t i = phase_errors_.size() - min_stable_samples_; i < phase_errors_.size(); ++i) {
            if (std::abs(phase_errors_[i]) <= convergence_threshold_ns_) {
                current_state_.stable_samples++;
            }
        }
    }
    
    // 判断是否收敛
    current_state_.is_converged = (current_state_.stable_samples >= min_stable_samples_) &&
                                  (std::abs(current_state_.phase_error_ns) <= convergence_threshold_ns_) &&
                                  (std::abs(current_state_.phase_error_trend) < 1.0); // 趋势稳定
    
    // 计算收敛速率
    if (timestamps_.size() >= 2) {
        uint64_t time_span = timestamps_.back() - timestamps_.front();
        if (time_span > 0) {
            double initial_error = std::abs(phase_errors_.front());
            double current_error = std::abs(phase_errors_.back());
            current_state_.convergence_rate = (initial_error - current_error) / (time_span / 1e9); // 每秒收敛速率
        }
    }
    
    // 更新收敛持续时间
    current_state_.convergence_duration_ns = GetCurrentTimestampNs() - current_state_.convergence_start_time_ns;
    
    return current_state_;
}

bool DiscipliningConvergenceEvaluator::IsConverged() {
    EvaluateConvergence();
    return current_state_.is_converged;
}

void DiscipliningConvergenceEvaluator::Reset() {
    phase_errors_.clear();
    frequency_errors_.clear();
    timestamps_.clear();
    
    current_state_ = DiscipliningConvergence();
    current_state_.convergence_start_time_ns = GetCurrentTimestampNs();
}

double DiscipliningConvergenceEvaluator::CalculateTrend(const std::vector<double>& samples) {
    if (samples.size() < 2) {
        return 0.0;
    }
    
    // 简单的线性趋势计算（最小二乘法）
    size_t n = samples.size();
    double sum_x = 0.0, sum_y = 0.0, sum_xy = 0.0, sum_x2 = 0.0;
    
    for (size_t i = 0; i < n; ++i) {
        double x = static_cast<double>(i);
        double y = samples[i];
        
        sum_x += x;
        sum_y += y;
        sum_xy += x * y;
        sum_x2 += x * x;
    }
    
    double denominator = n * sum_x2 - sum_x * sum_x;
    if (std::abs(denominator) < 1e-10) {
        return 0.0;
    }
    
    return (n * sum_xy - sum_x * sum_y) / denominator;
}

double DiscipliningConvergenceEvaluator::CalculateStandardDeviation(const std::vector<double>& samples) {
    if (samples.size() < 2) {
        return 0.0;
    }
    
    double mean = std::accumulate(samples.begin(), samples.end(), 0.0) / samples.size();
    double variance = 0.0;
    
    for (double sample : samples) {
        variance += (sample - mean) * (sample - mean);
    }
    
    return std::sqrt(variance / (samples.size() - 1));
}

// ============================================================================
// HoldoverQualityEvaluator Implementation
// ============================================================================

HoldoverQualityEvaluator::HoldoverQualityEvaluator()
    : is_holdover_active_(false) {
    frequency_history_.reserve(1000);
    temperature_history_.reserve(1000);
}

void HoldoverQualityEvaluator::StartHoldover(double initial_frequency_offset) {
    current_quality_ = HoldoverQuality();
    current_quality_.initial_frequency_offset_ppm = initial_frequency_offset;
    current_quality_.current_frequency_offset_ppm = initial_frequency_offset;
    current_quality_.holdover_start_time_ns = GetCurrentTimestampNs();
    
    frequency_history_.clear();
    temperature_history_.clear();
    
    // 添加初始样本
    frequency_history_.emplace_back(current_quality_.holdover_start_time_ns, initial_frequency_offset);
    
    is_holdover_active_ = true;
    
    std::cout << "开始守时评估，初始频率偏移: " << initial_frequency_offset << " ppm" << std::endl;
}

void HoldoverQualityEvaluator::UpdateHoldover(double current_frequency_offset, double temperature) {
    if (!is_holdover_active_) {
        return;
    }
    
    uint64_t current_time = GetCurrentTimestampNs();
    
    // 更新当前状态
    current_quality_.current_frequency_offset_ppm = current_frequency_offset;
    current_quality_.holdover_duration_ns = current_time - current_quality_.holdover_start_time_ns;
    
    // 添加历史数据
    frequency_history_.emplace_back(current_time, current_frequency_offset);
    temperature_history_.emplace_back(current_time, temperature);
    
    // 限制历史数据大小
    if (frequency_history_.size() > 1000) {
        frequency_history_.erase(frequency_history_.begin());
    }
    if (temperature_history_.size() > 1000) {
        temperature_history_.erase(temperature_history_.begin());
    }
    
    // 计算频率漂移率
    if (frequency_history_.size() >= 2) {
        auto& first = frequency_history_.front();
        auto& last = frequency_history_.back();
        
        double time_hours = (last.first - first.first) / (3600.0 * 1e9);
        if (time_hours > 0) {
            current_quality_.frequency_drift_rate_ppm_per_hour = 
                (last.second - first.second) / time_hours;
        }
    }
    
    // 计算温度系数（简化）
    if (temperature_history_.size() >= 10) {
        // 使用最近10个样本计算温度系数
        double temp_sum = 0.0, freq_sum = 0.0;
        for (size_t i = temperature_history_.size() - 10; i < temperature_history_.size(); ++i) {
            temp_sum += temperature_history_[i].second;
            freq_sum += frequency_history_[i].second;
        }
        
        double temp_avg = temp_sum / 10.0;
        double freq_avg = freq_sum / 10.0;
        
        double numerator = 0.0, denominator = 0.0;
        for (size_t i = temperature_history_.size() - 10; i < temperature_history_.size(); ++i) {
            double temp_diff = temperature_history_[i].second - temp_avg;
            double freq_diff = frequency_history_[i].second - freq_avg;
            numerator += temp_diff * freq_diff;
            denominator += temp_diff * temp_diff;
        }
        
        if (std::abs(denominator) > 1e-10) {
            current_quality_.temperature_coefficient = numerator / denominator;
        }
    }
    
    // 预测精度
    double hours_elapsed = current_quality_.holdover_duration_ns / (3600.0 * 1e9);
    current_quality_.predicted_accuracy_ns = std::abs(current_quality_.frequency_drift_rate_ppm_per_hour * hours_elapsed * 1000.0);
    
    // 计算质量评分
    current_quality_.quality_score = CalculateQualityScore();
    
    // 检查是否在规格范围内
    current_quality_.is_within_spec = (current_quality_.predicted_accuracy_ns <= 1000.0) && // 1微秒
                                      (std::abs(current_quality_.frequency_drift_rate_ppm_per_hour) <= 1e-11);
}

HoldoverQuality HoldoverQualityEvaluator::EvaluateHoldover() {
    return current_quality_;
}

bool HoldoverQualityEvaluator::IsHoldoverTimeout(uint32_t max_holdover_hours) {
    if (!is_holdover_active_) {
        return false;
    }
    
    double hours_elapsed = current_quality_.holdover_duration_ns / (3600.0 * 1e9);
    return hours_elapsed >= max_holdover_hours;
}

void HoldoverQualityEvaluator::StopHoldover() {
    is_holdover_active_ = false;
    
    std::cout << "停止守时评估，总持续时间: " 
              << (current_quality_.holdover_duration_ns / 1e9) << " 秒" << std::endl;
}

double HoldoverQualityEvaluator::PredictFrequencyDrift(double duration_hours) {
    if (frequency_history_.size() < 2) {
        return current_quality_.current_frequency_offset_ppm;
    }
    
    // 使用线性预测
    double predicted_offset = current_quality_.current_frequency_offset_ppm + 
                             (current_quality_.frequency_drift_rate_ppm_per_hour * duration_hours);
    
    return predicted_offset;
}

uint32_t HoldoverQualityEvaluator::CalculateQualityScore() {
    uint32_t score = 100;
    
    // 根据频率漂移率扣分
    double drift_rate_abs = std::abs(current_quality_.frequency_drift_rate_ppm_per_hour);
    if (drift_rate_abs > 1e-10) {
        score -= 30;
    } else if (drift_rate_abs > 1e-11) {
        score -= 20;
    } else if (drift_rate_abs > 1e-12) {
        score -= 10;
    }
    
    // 根据预测精度扣分
    if (current_quality_.predicted_accuracy_ns > 10000.0) { // 10微秒
        score -= 40;
    } else if (current_quality_.predicted_accuracy_ns > 1000.0) { // 1微秒
        score -= 20;
    } else if (current_quality_.predicted_accuracy_ns > 100.0) { // 100纳秒
        score -= 10;
    }
    
    // 根据守时持续时间扣分
    double hours_elapsed = current_quality_.holdover_duration_ns / (3600.0 * 1e9);
    if (hours_elapsed > 24.0) {
        score -= static_cast<uint32_t>((hours_elapsed - 24.0) * 2); // 每小时扣2分
    }
    
    return std::max(score, 0u);
}

} // namespace core
} // namespace timing_server