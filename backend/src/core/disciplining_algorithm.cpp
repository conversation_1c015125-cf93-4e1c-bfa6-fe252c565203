#include "core/disciplining_algorithm.h"
#include <algorithm>
#include <cmath>
#include <fstream>
#include <iostream>
#include <numeric>
#include <sstream>

namespace timing_server {
namespace core {

// HybridDiscipliningAlgorithm实现

HybridDiscipliningAlgorithm::HybridDiscipliningAlgorithm()
    : integrator_state_(0.0), proportional_gain_(1.0), integral_gain_(0.1),
      loop_bandwidth_(0.1), damping_factor_(0.707), measurement_count_(0),
      convergence_start_time_(0) {
    
    status_.state = DiscipliningState::INITIALIZING;
    status_.algorithm = DiscipliningAlgorithmType::HYBRID;
    status_.current_phase_error_ns = 0.0;
    status_.current_frequency_error_ppm = 0.0;
    status_.loop_bandwidth_hz = loop_bandwidth_;
    status_.time_constant_s = 1.0 / (2.0 * M_PI * loop_bandwidth_);
    status_.convergence_time_s = 0;
    status_.is_converged = false;
    status_.last_update_ns = 0;
}

HybridDiscipliningAlgorithm::~HybridDiscipliningAlgorithm() = default;

bool HybridDiscipliningAlgorithm::Initialize(const DiscipliningParameters& config) {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    
    config_ = config;
    
    // 根据配置设置环路参数
    // 环路带宽 = 1 / (2π × 时间常数)
    double time_constant = 1.0 / config_.frequency_gain;
    loop_bandwidth_ = 1.0 / (2.0 * M_PI * time_constant);
    
    // 计算PLL增益
    // 对于二阶环路：Kp = 2ζωn, Ki = ωn²
    // 其中 ωn = 2π × 环路带宽, ζ = 阻尼因子
    double natural_frequency = 2.0 * M_PI * loop_bandwidth_;
    proportional_gain_ = 2.0 * damping_factor_ * natural_frequency;
    integral_gain_ = natural_frequency * natural_frequency;
    
    // 重置状态
    integrator_state_ = 0.0;
    measurement_count_ = 0;
    convergence_start_time_ = 0;
    
    // 清空历史数据
    measurement_history_.clear();
    phase_error_history_.clear();
    frequency_error_history_.clear();
    
    // 更新状态
    status_.state = DiscipliningState::ACQUIRING;
    status_.loop_bandwidth_hz = loop_bandwidth_;
    status_.time_constant_s = time_constant;
    status_.is_converged = false;
    status_.last_update_ns = GetCurrentTimestampNs();
    
    std::cout << "混合驯服算法初始化完成" << std::endl;
    std::cout << "  环路带宽: " << loop_bandwidth_ << " Hz" << std::endl;
    std::cout << "  时间常数: " << time_constant << " s" << std::endl;
    std::cout << "  比例增益: " << proportional_gain_ << std::endl;
    std::cout << "  积分增益: " << integral_gain_ << std::endl;
    
    return true;
}

bool HybridDiscipliningAlgorithm::ProcessMeasurement(const TimeMeasurement& measurement) {
    if (!measurement.is_valid) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    
    // 添加到历史记录
    measurement_history_.push_back(measurement);
    if (measurement_history_.size() > MAX_HISTORY_SIZE) {
        measurement_history_.pop_front();
    }
    
    // 根据当前算法类型处理测量
    switch (status_.algorithm) {
        case DiscipliningAlgorithmType::PLL:
            ProcessPLL(measurement);
            break;
        case DiscipliningAlgorithmType::FLL:
            ProcessFLL(measurement);
            break;
        case DiscipliningAlgorithmType::HYBRID:
            ProcessHybrid(measurement);
            break;
    }
    
    // 更新状态信息
    status_.current_phase_error_ns = measurement.phase_offset_ns;
    status_.current_frequency_error_ppm = measurement.frequency_offset_ppm;
    status_.last_update_ns = measurement.timestamp_ns;
    measurement_count_++;
    
    // 检查收敛状态
    bool was_converged = status_.is_converged;
    status_.is_converged = CheckConvergence();
    
    if (!was_converged && status_.is_converged) {
        status_.state = DiscipliningState::CONVERGED;
        status_.convergence_time_s = (measurement.timestamp_ns - convergence_start_time_) / 1000000000ULL;
        std::cout << "驯服算法收敛完成，用时: " << status_.convergence_time_s << " 秒" << std::endl;
    } else if (status_.state == DiscipliningState::ACQUIRING && measurement_count_ > 10) {
        status_.state = DiscipliningState::TRACKING;
        convergence_start_time_ = measurement.timestamp_ns;
    }
    
    // 动态调整环路参数
    AdaptLoopParameters();
    
    return true;
}

void HybridDiscipliningAlgorithm::ProcessPLL(const TimeMeasurement& measurement) {
    // PLL算法：主要跟踪相位误差
    // 相位检测器输出 = 相位误差
    double phase_error = measurement.phase_offset_ns;
    
    // 环路滤波器：PI控制器
    // 输出 = Kp × 相位误差 + Ki × ∫相位误差dt
    integrator_state_ += integral_gain_ * phase_error * (config_.measurement_interval_ms / 1000.0);
    double loop_output = proportional_gain_ * phase_error + integrator_state_;
    
    // 限制积分器防止饱和
    const double MAX_INTEGRATOR = 100.0; // ppm
    integrator_state_ = std::clamp(integrator_state_, -MAX_INTEGRATOR, MAX_INTEGRATOR);
    
    // 更新环路滤波器状态
    UpdateLoopFilter(phase_error, loop_output);
    
    // 记录相位误差历史
    phase_error_history_.push_back(phase_error);
    if (phase_error_history_.size() > CONVERGENCE_WINDOW_SIZE) {
        phase_error_history_.pop_front();
    }
}

void HybridDiscipliningAlgorithm::ProcessFLL(const TimeMeasurement& measurement) {
    // FLL算法：主要跟踪频率误差
    // 频率检测器输出 = 频率误差
    double frequency_error = measurement.frequency_offset_ppm;
    
    // 简化的FLL环路滤波器
    // 使用一阶低通滤波器平滑频率误差
    const double alpha = 0.1; // 滤波器系数
    static double filtered_frequency_error = 0.0;
    filtered_frequency_error = alpha * frequency_error + (1.0 - alpha) * filtered_frequency_error;
    
    // 更新积分器状态（频率校正）
    integrator_state_ = -filtered_frequency_error;
    
    // 记录频率误差历史
    frequency_error_history_.push_back(frequency_error);
    if (frequency_error_history_.size() > CONVERGENCE_WINDOW_SIZE) {
        frequency_error_history_.pop_front();
    }
}

void HybridDiscipliningAlgorithm::ProcessHybrid(const TimeMeasurement& measurement) {
    // 混合算法：根据误差大小动态选择PLL或FLL
    double phase_error = std::abs(measurement.phase_offset_ns);
    double frequency_error = std::abs(measurement.frequency_offset_ppm);
    
    // 阈值设定：相位误差 > 1000ns 或频率误差 > 1ppm 时使用FLL
    const double PHASE_THRESHOLD_NS = 1000.0;
    const double FREQUENCY_THRESHOLD_PPM = 1.0;
    
    if (phase_error > PHASE_THRESHOLD_NS || frequency_error > FREQUENCY_THRESHOLD_PPM) {
        // 大误差时使用FLL快速捕获
        ProcessFLL(measurement);
        status_.algorithm = DiscipliningAlgorithmType::FLL;
    } else {
        // 小误差时使用PLL精确跟踪
        ProcessPLL(measurement);
        status_.algorithm = DiscipliningAlgorithmType::PLL;
    }
    
    // 记录误差历史用于收敛检测
    phase_error_history_.push_back(measurement.phase_offset_ns);
    frequency_error_history_.push_back(measurement.frequency_offset_ppm);
    
    if (phase_error_history_.size() > CONVERGENCE_WINDOW_SIZE) {
        phase_error_history_.pop_front();
    }
    if (frequency_error_history_.size() > CONVERGENCE_WINDOW_SIZE) {
        frequency_error_history_.pop_front();
    }
}

void HybridDiscipliningAlgorithm::UpdateLoopFilter(double phase_error, double frequency_error) {
    // 更新环路滤波器内部状态
    // 这里可以实现更复杂的滤波器，如卡尔曼滤波器
    
    // 简单的噪声估计和滤波
    if (measurement_history_.size() >= 10) {
        double noise_estimate = EstimateNoise(measurement_history_);
        
        // 根据噪声水平调整增益
        if (noise_estimate > 100.0) { // 高噪声环境
            proportional_gain_ *= 0.9;
            integral_gain_ *= 0.9;
        } else if (noise_estimate < 10.0) { // 低噪声环境
            proportional_gain_ *= 1.1;
            integral_gain_ *= 1.1;
        }
        
        // 限制增益范围
        proportional_gain_ = std::clamp(proportional_gain_, 0.1, 10.0);
        integral_gain_ = std::clamp(integral_gain_, 0.01, 1.0);
    }
}

bool HybridDiscipliningAlgorithm::CheckConvergence() {
    // 收敛判断条件：
    // 1. 相位误差RMS < 收敛阈值
    // 2. 频率误差RMS < 0.1 ppm
    // 3. 连续满足条件的时间 > 最小收敛时间
    
    if (phase_error_history_.size() < CONVERGENCE_WINDOW_SIZE ||
        frequency_error_history_.size() < CONVERGENCE_WINDOW_SIZE) {
        return false;
    }
    
    // 计算相位误差RMS
    double phase_rms = 0.0;
    for (double error : phase_error_history_) {
        phase_rms += error * error;
    }
    phase_rms = std::sqrt(phase_rms / phase_error_history_.size());
    
    // 计算频率误差RMS
    double frequency_rms = 0.0;
    for (double error : frequency_error_history_) {
        frequency_rms += error * error;
    }
    frequency_rms = std::sqrt(frequency_rms / frequency_error_history_.size());
    
    // 检查收敛条件
    bool phase_converged = phase_rms < config_.convergence_threshold_ns;
    bool frequency_converged = frequency_rms < 0.1; // 0.1 ppm
    
    return phase_converged && frequency_converged;
}

void HybridDiscipliningAlgorithm::AdaptLoopParameters() {
    // 自适应环路参数调整
    // 根据测量噪声和误差大小动态调整环路带宽
    
    if (measurement_history_.size() < 50) {
        return; // 数据不足，不进行调整
    }
    
    // 估计测量噪声
    double noise_level = EstimateNoise(measurement_history_);
    
    // 计算当前误差水平
    double current_phase_error = std::abs(status_.current_phase_error_ns);
    double current_frequency_error = std::abs(status_.current_frequency_error_ppm);
    
    // 根据噪声和误差调整环路带宽
    double target_bandwidth = loop_bandwidth_;
    
    if (noise_level > 100.0) {
        // 高噪声环境：降低带宽，提高稳定性
        target_bandwidth *= 0.8;
    } else if (noise_level < 10.0 && current_phase_error > 100.0) {
        // 低噪声但大误差：提高带宽，加快响应
        target_bandwidth *= 1.2;
    }
    
    // 限制带宽范围
    target_bandwidth = std::clamp(target_bandwidth, 0.01, 1.0);
    
    // 平滑调整
    const double adjustment_rate = 0.1;
    loop_bandwidth_ = (1.0 - adjustment_rate) * loop_bandwidth_ + adjustment_rate * target_bandwidth;
    
    // 更新相关参数
    status_.loop_bandwidth_hz = loop_bandwidth_;
    status_.time_constant_s = 1.0 / (2.0 * M_PI * loop_bandwidth_);
}

double HybridDiscipliningAlgorithm::EstimateNoise(const std::deque<TimeMeasurement>& measurements) {
    if (measurements.size() < 10) {
        return 50.0; // 默认噪声水平
    }
    
    // 使用Allan方差估计噪声
    std::vector<double> phase_values;
    for (const auto& measurement : measurements) {
        phase_values.push_back(measurement.phase_offset_ns);
    }
    
    // 计算相邻测量值的差分
    std::vector<double> differences;
    for (size_t i = 1; i < phase_values.size(); ++i) {
        differences.push_back(phase_values[i] - phase_values[i-1]);
    }
    
    // 计算差分的标准差作为噪声估计
    if (differences.empty()) {
        return 50.0;
    }
    
    double mean = std::accumulate(differences.begin(), differences.end(), 0.0) / differences.size();
    double variance = 0.0;
    for (double diff : differences) {
        variance += (diff - mean) * (diff - mean);
    }
    variance /= differences.size();
    
    return std::sqrt(variance);
}

double HybridDiscipliningAlgorithm::GetFrequencyCorrection() {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    return integrator_state_;
}

double HybridDiscipliningAlgorithm::GetPhaseCorrection() {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    // 相位校正通常是频率校正的积分
    // 这里简化为当前相位误差的负值
    return -status_.current_phase_error_ns;
}

bool HybridDiscipliningAlgorithm::IsConverged() {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    return status_.is_converged;
}

DiscipliningStatus HybridDiscipliningAlgorithm::GetStatus() {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    return status_;
}

void HybridDiscipliningAlgorithm::Reset() {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    
    integrator_state_ = 0.0;
    measurement_count_ = 0;
    convergence_start_time_ = 0;
    
    measurement_history_.clear();
    phase_error_history_.clear();
    frequency_error_history_.clear();
    
    status_.state = DiscipliningState::INITIALIZING;
    status_.current_phase_error_ns = 0.0;
    status_.current_frequency_error_ppm = 0.0;
    status_.is_converged = false;
    status_.convergence_time_s = 0;
    status_.last_update_ns = 0;
    
    std::cout << "驯服算法已重置" << std::endl;
}

void HybridDiscipliningAlgorithm::SetAlgorithmType(DiscipliningAlgorithmType type) {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    status_.algorithm = type;
}

double HybridDiscipliningAlgorithm::GetLoopBandwidth() const {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    return loop_bandwidth_;
}

void HybridDiscipliningAlgorithm::SetLoopBandwidth(double bandwidth) {
    std::lock_guard<std::mutex> lock(algorithm_mutex_);
    loop_bandwidth_ = std::clamp(bandwidth, 0.001, 10.0);
    status_.loop_bandwidth_hz = loop_bandwidth_;
    status_.time_constant_s = 1.0 / (2.0 * M_PI * loop_bandwidth_);
}

// RubidiumLearningAlgorithm实现

RubidiumLearningAlgorithm::RubidiumLearningAlgorithm()
    : aging_slope_(0.0), aging_intercept_(0.0), temperature_slope_(0.0),
      temperature_intercept_(0.0), reference_timestamp_(0), reference_temperature_(25.0) {
    
    learning_data_.aging_rate_ppm_per_day = 0.0;
    learning_data_.temperature_coefficient = 0.0;
    learning_data_.temperature_reference = 25.0;
    learning_data_.learning_duration_hours = 0;
    learning_data_.sample_count = 0;
    learning_data_.confidence_level = 0.0;
    learning_data_.prediction_accuracy = 0.0;
}

RubidiumLearningAlgorithm::~RubidiumLearningAlgorithm() = default;

bool RubidiumLearningAlgorithm::Initialize(const HoldoverParameters& config) {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    
    config_ = config;
    reference_timestamp_ = GetCurrentTimestampNs();
    reference_temperature_ = config_.enable_temperature_compensation ? 25.0 : 0.0;
    
    // 重置学习数据
    learning_data_ = RubidiumLearningData();
    learning_data_.temperature_reference = reference_temperature_;
    samples_.clear();
    
    std::cout << "铷钟学习算法初始化完成" << std::endl;
    std::cout << "  最大守时时间: " << config_.max_holdover_hours << " 小时" << std::endl;
    std::cout << "  学习时间: " << config_.learning_duration_hours << " 小时" << std::endl;
    std::cout << "  温度补偿: " << (config_.enable_temperature_compensation ? "启用" : "禁用") << std::endl;
    
    return true;
}

bool RubidiumLearningAlgorithm::AddSample(double frequency_offset, double temperature, uint64_t timestamp_ns) {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    
    // 添加新样本
    samples_.emplace_back(frequency_offset, temperature, timestamp_ns);
    
    // 限制样本数量
    if (samples_.size() > MAX_SAMPLES) {
        samples_.erase(samples_.begin());
    }
    
    // 更新学习数据
    learning_data_.sample_count = samples_.size();
    learning_data_.last_update_ns = timestamp_ns;
    
    // 计算学习时长
    if (!samples_.empty()) {
        uint64_t duration_ns = timestamp_ns - samples_.front().timestamp_ns;
        learning_data_.learning_duration_hours = duration_ns / (3600ULL * 1000000000ULL);
    }
    
    // 如果样本足够，更新模型
    if (samples_.size() >= 100 && learning_data_.learning_duration_hours >= MIN_LEARNING_HOURS) {
        UpdateAgingModel();
        if (config_.enable_temperature_compensation) {
            UpdateTemperatureModel();
        }
        learning_data_.prediction_accuracy = CalculatePredictionAccuracy();
    }
    
    return true;
}

void RubidiumLearningAlgorithm::UpdateAgingModel() {
    // 使用最小二乘法拟合老化曲线
    // y = ax + b，其中 x 是时间（小时），y 是频率偏移（ppm）
    
    std::vector<double> time_hours;
    std::vector<double> frequency_offsets;
    
    uint64_t base_time = samples_.front().timestamp_ns;
    
    for (const auto& sample : samples_) {
        double hours = (sample.timestamp_ns - base_time) / (3600.0 * 1000000000.0);
        time_hours.push_back(hours);
        frequency_offsets.push_back(sample.frequency_offset_ppm);
    }
    
    double correlation;
    if (CalculateLinearRegression(time_hours, frequency_offsets, aging_slope_, aging_intercept_, correlation)) {
        learning_data_.aging_rate_ppm_per_day = aging_slope_ * 24.0; // 转换为每天的老化率
        learning_data_.confidence_level = std::abs(correlation);
        
        std::cout << "铷钟老化模型更新:" << std::endl;
        std::cout << "  老化率: " << learning_data_.aging_rate_ppm_per_day << " ppm/天" << std::endl;
        std::cout << "  相关系数: " << correlation << std::endl;
    }
}

void RubidiumLearningAlgorithm::UpdateTemperatureModel() {
    // 使用最小二乘法拟合温度特性
    // y = ax + b，其中 x 是温度（°C），y 是频率偏移（ppm）
    
    std::vector<double> temperatures;
    std::vector<double> frequency_offsets;
    
    for (const auto& sample : samples_) {
        temperatures.push_back(sample.temperature);
        frequency_offsets.push_back(sample.frequency_offset_ppm);
    }
    
    double correlation;
    if (CalculateLinearRegression(temperatures, frequency_offsets, temperature_slope_, temperature_intercept_, correlation)) {
        learning_data_.temperature_coefficient = temperature_slope_;
        
        std::cout << "铷钟温度模型更新:" << std::endl;
        std::cout << "  温度系数: " << learning_data_.temperature_coefficient << " ppm/°C" << std::endl;
        std::cout << "  相关系数: " << correlation << std::endl;
    }
}

bool RubidiumLearningAlgorithm::CalculateLinearRegression(const std::vector<double>& x_values,
                                                         const std::vector<double>& y_values,
                                                         double& slope, double& intercept, double& correlation) const {
    if (x_values.size() != y_values.size() || x_values.size() < 2) {
        return false;
    }
    
    size_t n = x_values.size();
    
    // 计算均值
    double x_mean = std::accumulate(x_values.begin(), x_values.end(), 0.0) / n;
    double y_mean = std::accumulate(y_values.begin(), y_values.end(), 0.0) / n;
    
    // 计算协方差和方差
    double covariance = 0.0;
    double x_variance = 0.0;
    double y_variance = 0.0;
    
    for (size_t i = 0; i < n; ++i) {
        double x_diff = x_values[i] - x_mean;
        double y_diff = y_values[i] - y_mean;
        
        covariance += x_diff * y_diff;
        x_variance += x_diff * x_diff;
        y_variance += y_diff * y_diff;
    }
    
    if (x_variance == 0.0) {
        return false;
    }
    
    // 计算回归系数
    slope = covariance / x_variance;
    intercept = y_mean - slope * x_mean;
    
    // 计算相关系数
    if (y_variance > 0.0) {
        correlation = covariance / std::sqrt(x_variance * y_variance);
    } else {
        correlation = 0.0;
    }
    
    return true;
}

double RubidiumLearningAlgorithm::CalculatePredictionAccuracy() const {
    if (samples_.size() < 50) {
        return 0.0;
    }
    
    // 使用后一半数据验证前一半数据建立的模型
    size_t split_point = samples_.size() / 2;
    
    // 使用前一半数据计算模型参数
    std::vector<double> time_hours_train;
    std::vector<double> frequency_offsets_train;
    
    uint64_t base_time = samples_.front().timestamp_ns;
    
    for (size_t i = 0; i < split_point; ++i) {
        double hours = (samples_[i].timestamp_ns - base_time) / (3600.0 * 1000000000.0);
        time_hours_train.push_back(hours);
        frequency_offsets_train.push_back(samples_[i].frequency_offset_ppm);
    }
    
    double train_slope, train_intercept, train_correlation;
    if (!CalculateLinearRegression(time_hours_train, frequency_offsets_train, 
                                  train_slope, train_intercept, train_correlation)) {
        return 0.0;
    }
    
    // 使用后一半数据验证模型
    double total_error = 0.0;
    size_t test_count = 0;
    
    for (size_t i = split_point; i < samples_.size(); ++i) {
        double hours = (samples_[i].timestamp_ns - base_time) / (3600.0 * 1000000000.0);
        double predicted = train_slope * hours + train_intercept;
        double actual = samples_[i].frequency_offset_ppm;
        double error = std::abs(predicted - actual);
        
        total_error += error;
        test_count++;
    }
    
    return test_count > 0 ? total_error / test_count : 0.0;
}

RubidiumLearningData RubidiumLearningAlgorithm::GetLearningData() const {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    return learning_data_;
}

double RubidiumLearningAlgorithm::PredictFrequencyOffset(double temperature, double time_since_reference_hours) const {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    
    // 老化补偿
    double aging_compensation = learning_data_.aging_rate_ppm_per_day * (time_since_reference_hours / 24.0);
    
    // 温度补偿
    double temperature_compensation = 0.0;
    if (config_.enable_temperature_compensation) {
        temperature_compensation = learning_data_.temperature_coefficient * 
                                 (temperature - learning_data_.temperature_reference);
    }
    
    return aging_compensation + temperature_compensation;
}

double RubidiumLearningAlgorithm::GetTemperatureCompensation(double current_temperature) const {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    
    if (!config_.enable_temperature_compensation) {
        return 0.0;
    }
    
    return learning_data_.temperature_coefficient * 
           (current_temperature - learning_data_.temperature_reference);
}

bool RubidiumLearningAlgorithm::SaveLearningData(const std::string& filename) const {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    
    try {
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "无法打开文件保存学习数据: " << filename << std::endl;
            return false;
        }
        
        // 保存学习数据结构
        file.write(reinterpret_cast<const char*>(&learning_data_), sizeof(learning_data_));
        
        // 保存样本数据
        size_t sample_count = samples_.size();
        file.write(reinterpret_cast<const char*>(&sample_count), sizeof(sample_count));
        
        for (const auto& sample : samples_) {
            file.write(reinterpret_cast<const char*>(&sample.frequency_offset_ppm), sizeof(double));
            file.write(reinterpret_cast<const char*>(&sample.temperature), sizeof(double));
            file.write(reinterpret_cast<const char*>(&sample.timestamp_ns), sizeof(uint64_t));
        }
        
        file.close();
        std::cout << "铷钟学习数据已保存到: " << filename << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "保存学习数据异常: " << e.what() << std::endl;
        return false;
    }
}

bool RubidiumLearningAlgorithm::LoadLearningData(const std::string& filename) {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    
    try {
        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            std::cout << "学习数据文件不存在，将从头开始学习: " << filename << std::endl;
            return false;
        }
        
        // 加载学习数据结构
        file.read(reinterpret_cast<char*>(&learning_data_), sizeof(learning_data_));
        
        // 加载样本数据
        size_t sample_count;
        file.read(reinterpret_cast<char*>(&sample_count), sizeof(sample_count));
        
        samples_.clear();
        samples_.reserve(sample_count);
        
        for (size_t i = 0; i < sample_count; ++i) {
            double frequency_offset, temperature;
            uint64_t timestamp;
            
            file.read(reinterpret_cast<char*>(&frequency_offset), sizeof(double));
            file.read(reinterpret_cast<char*>(&temperature), sizeof(double));
            file.read(reinterpret_cast<char*>(&timestamp), sizeof(uint64_t));
            
            samples_.emplace_back(frequency_offset, temperature, timestamp);
        }
        
        file.close();
        
        std::cout << "铷钟学习数据加载完成: " << filename << std::endl;
        std::cout << "  样本数量: " << samples_.size() << std::endl;
        std::cout << "  学习时长: " << learning_data_.learning_duration_hours << " 小时" << std::endl;
        std::cout << "  老化率: " << learning_data_.aging_rate_ppm_per_day << " ppm/天" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "加载学习数据异常: " << e.what() << std::endl;
        return false;
    }
}

void RubidiumLearningAlgorithm::Reset() {
    std::lock_guard<std::mutex> lock(learning_mutex_);
    
    learning_data_ = RubidiumLearningData();
    samples_.clear();
    aging_slope_ = 0.0;
    aging_intercept_ = 0.0;
    temperature_slope_ = 0.0;
    temperature_intercept_ = 0.0;
    reference_timestamp_ = GetCurrentTimestampNs();
    
    std::cout << "铷钟学习数据已重置" << std::endl;
}

// HoldoverPredictionAlgorithm实现

HoldoverPredictionAlgorithm::HoldoverPredictionAlgorithm()
    : is_holdover_active_(false), holdover_start_time_(0), initial_frequency_offset_(0.0),
      initial_temperature_(25.0), current_temperature_(25.0),
      accumulated_phase_error_ns_(0.0), accumulated_frequency_error_ppm_(0.0) {
}

HoldoverPredictionAlgorithm::~HoldoverPredictionAlgorithm() = default;

bool HoldoverPredictionAlgorithm::Initialize(const RubidiumLearningData& learning_data, 
                                           const HoldoverParameters& config) {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    
    learning_data_ = learning_data;
    config_ = config;
    
    // 重置状态
    is_holdover_active_ = false;
    accumulated_phase_error_ns_ = 0.0;
    accumulated_frequency_error_ppm_ = 0.0;
    
    std::cout << "守时预测算法初始化完成" << std::endl;
    std::cout << "  最大守时时间: " << config_.max_holdover_hours << " 小时" << std::endl;
    std::cout << "  频率漂移限制: " << config_.frequency_drift_limit_ppm << " ppm" << std::endl;
    std::cout << "  温度补偿: " << (config_.enable_temperature_compensation ? "启用" : "禁用") << std::endl;
    
    return true;
}

bool HoldoverPredictionAlgorithm::StartHoldover(double initial_frequency_offset, double initial_temperature) {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    
    if (is_holdover_active_) {
        std::cout << "守时预测已在运行" << std::endl;
        return true;
    }
    
    // 初始化守时状态
    is_holdover_active_ = true;
    holdover_start_time_ = GetCurrentTimestampNs();
    initial_frequency_offset_ = initial_frequency_offset;
    initial_temperature_ = initial_temperature;
    current_temperature_ = initial_temperature;
    
    // 重置累积误差
    accumulated_phase_error_ns_ = 0.0;
    accumulated_frequency_error_ppm_ = 0.0;
    
    // 初始化预测
    UpdatePrediction();
    
    std::cout << "守时预测已启动" << std::endl;
    std::cout << "  初始频率偏移: " << initial_frequency_offset_ << " ppm" << std::endl;
    std::cout << "  初始温度: " << initial_temperature_ << " °C" << std::endl;
    
    return true;
}

void HoldoverPredictionAlgorithm::UpdateTemperature(double current_temperature) {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    
    if (!is_holdover_active_) {
        return;
    }
    
    current_temperature_ = current_temperature;
    UpdatePrediction();
}

void HoldoverPredictionAlgorithm::UpdatePrediction() {
    if (!is_holdover_active_) {
        return;
    }
    
    uint64_t current_time = GetCurrentTimestampNs();
    double elapsed_hours = (current_time - holdover_start_time_) / (3600.0 * 1000000000.0);
    
    // 计算老化补偿
    double aging_compensation = CalculateAgingCompensation(elapsed_hours);
    
    // 计算温度补偿
    double temperature_compensation = CalculateTemperatureCompensation(current_temperature_);
    
    // 总频率漂移预测
    current_prediction_.predicted_frequency_drift_ppm = aging_compensation + temperature_compensation;
    
    // 相位漂移预测（频率漂移的积分）
    // Δφ = ∫ Δf dt = Δf × t （简化为线性积分）
    current_prediction_.predicted_phase_drift_ns = 
        current_prediction_.predicted_frequency_drift_ppm * elapsed_hours * 3600.0 * 1e9 / 1e6;
    
    // 预测不确定度
    current_prediction_.prediction_uncertainty_ns = CalculateUncertainty(elapsed_hours);
    
    // 更新其他字段
    current_prediction_.prediction_horizon_hours = static_cast<uint64_t>(elapsed_hours);
    current_prediction_.temperature_compensation_ppm = temperature_compensation;
    current_prediction_.prediction_timestamp_ns = current_time;
    
    // 更新累积误差
    accumulated_frequency_error_ppm_ = current_prediction_.predicted_frequency_drift_ppm;
    accumulated_phase_error_ns_ = current_prediction_.predicted_phase_drift_ns;
}

double HoldoverPredictionAlgorithm::CalculateAgingCompensation(double elapsed_hours) const {
    // 基于学习的老化率计算补偿
    return learning_data_.aging_rate_ppm_per_day * (elapsed_hours / 24.0);
}

double HoldoverPredictionAlgorithm::CalculateTemperatureCompensation(double current_temperature) const {
    if (!config_.enable_temperature_compensation) {
        return 0.0;
    }
    
    // 基于学习的温度系数计算补偿
    return learning_data_.temperature_coefficient * 
           (current_temperature - learning_data_.temperature_reference);
}

double HoldoverPredictionAlgorithm::CalculateUncertainty(double elapsed_hours) const {
    // 不确定度随时间增长
    // 基础不确定度 + 时间相关不确定度
    double base_uncertainty = 100.0; // 100ns 基础不确定度
    double time_factor = elapsed_hours / 24.0; // 每天增长因子
    double learning_factor = 1.0 - learning_data_.confidence_level; // 学习置信度影响
    
    return base_uncertainty * (1.0 + time_factor * learning_factor);
}

HoldoverPrediction HoldoverPredictionAlgorithm::GetCurrentPrediction() const {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    return current_prediction_;
}

double HoldoverPredictionAlgorithm::GetFrequencyCorrection() const {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    
    if (!is_holdover_active_) {
        return 0.0;
    }
    
    // 返回负的预测漂移作为校正值
    return -current_prediction_.predicted_frequency_drift_ppm;
}

double HoldoverPredictionAlgorithm::GetPhaseCorrection() const {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    
    if (!is_holdover_active_) {
        return 0.0;
    }
    
    // 返回负的预测相位漂移作为校正值
    return -current_prediction_.predicted_phase_drift_ns;
}

double HoldoverPredictionAlgorithm::GetPredictionAccuracy(double holdover_duration_hours) const {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    
    // 基于学习数据的预测精度和时间长度计算精度
    double base_accuracy = learning_data_.prediction_accuracy;
    double time_degradation = holdover_duration_hours / 24.0; // 每天精度下降
    
    return base_accuracy * (1.0 + time_degradation);
}

void HoldoverPredictionAlgorithm::StopHoldover() {
    std::lock_guard<std::mutex> lock(prediction_mutex_);
    
    if (!is_holdover_active_) {
        return;
    }
    
    is_holdover_active_ = false;
    
    uint64_t current_time = GetCurrentTimestampNs();
    double elapsed_hours = (current_time - holdover_start_time_) / (3600.0 * 1000000000.0);
    
    std::cout << "守时预测已停止" << std::endl;
    std::cout << "  守时时长: " << elapsed_hours << " 小时" << std::endl;
    std::cout << "  最终频率漂移: " << accumulated_frequency_error_ppm_ << " ppm" << std::endl;
    std::cout << "  最终相位漂移: " << accumulated_phase_error_ns_ << " ns" << std::endl;
}

// ClockDiscipliningManager实现

ClockDiscipliningManager::ClockDiscipliningManager()
    : current_state_(DiscipliningState::INITIALIZING), is_gnss_disciplining_active_(false),
      is_holdover_active_(false) {
}

ClockDiscipliningManager::~ClockDiscipliningManager() = default;

bool ClockDiscipliningManager::Initialize(const TimingConfig& config) {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    config_ = config;
    
    // 初始化驯服算法
    disciplining_algorithm_ = std::make_unique<HybridDiscipliningAlgorithm>();
    if (!disciplining_algorithm_->Initialize(config_.discipline)) {
        std::cerr << "驯服算法初始化失败" << std::endl;
        return false;
    }
    
    // 初始化学习算法
    learning_algorithm_ = std::make_unique<RubidiumLearningAlgorithm>();
    if (!learning_algorithm_->Initialize(config_.holdover)) {
        std::cerr << "学习算法初始化失败" << std::endl;
        return false;
    }
    
    // 尝试加载历史学习数据
    learning_algorithm_->LoadLearningData("/var/lib/timing-server/rubidium_learning.dat");
    
    // 初始化预测算法
    prediction_algorithm_ = std::make_unique<HoldoverPredictionAlgorithm>();
    auto learning_data = learning_algorithm_->GetLearningData();
    if (!prediction_algorithm_->Initialize(learning_data, config_.holdover)) {
        std::cerr << "预测算法初始化失败" << std::endl;
        return false;
    }
    
    current_state_ = DiscipliningState::INITIALIZING;
    
    std::cout << "时钟驯服管理器初始化完成" << std::endl;
    return true;
}

bool ClockDiscipliningManager::StartGnssDisciplining() {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_gnss_disciplining_active_) {
        return true;
    }
    
    is_gnss_disciplining_active_ = true;
    is_holdover_active_ = false;
    
    DiscipliningState old_state = current_state_;
    current_state_ = DiscipliningState::ACQUIRING;
    
    NotifyStatusChange(old_state, current_state_);
    
    std::cout << "GNSS驯服已启动" << std::endl;
    return true;
}

void ClockDiscipliningManager::StopGnssDisciplining() {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (!is_gnss_disciplining_active_) {
        return;
    }
    
    is_gnss_disciplining_active_ = false;
    
    // 保存学习数据
    learning_algorithm_->SaveLearningData("/var/lib/timing-server/rubidium_learning.dat");
    
    std::cout << "GNSS驯服已停止" << std::endl;
}

bool ClockDiscipliningManager::StartHoldover() {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_holdover_active_) {
        return true;
    }
    
    // 获取当前状态作为守时起始点
    auto disciplining_status = disciplining_algorithm_->GetStatus();
    double initial_frequency_offset = disciplining_status.current_frequency_error_ppm;
    double initial_temperature = 25.0; // 默认温度，实际应从硬件获取
    
    if (!prediction_algorithm_->StartHoldover(initial_frequency_offset, initial_temperature)) {
        std::cerr << "启动守时预测失败" << std::endl;
        return false;
    }
    
    is_holdover_active_ = true;
    is_gnss_disciplining_active_ = false;
    
    DiscipliningState old_state = current_state_;
    current_state_ = DiscipliningState::HOLDOVER;
    
    NotifyStatusChange(old_state, current_state_);
    
    std::cout << "守时模式已启动" << std::endl;
    return true;
}

void ClockDiscipliningManager::StopHoldover() {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (!is_holdover_active_) {
        return;
    }
    
    is_holdover_active_ = false;
    prediction_algorithm_->StopHoldover();
    
    std::cout << "守时模式已停止" << std::endl;
}

bool ClockDiscipliningManager::ProcessTimeMeasurement(const TimeMeasurement& measurement) {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (!measurement.is_valid) {
        return false;
    }
    
    bool success = true;
    
    // 如果正在进行GNSS驯服，处理测量数据
    if (is_gnss_disciplining_active_) {
        success = disciplining_algorithm_->ProcessMeasurement(measurement);
        
        // 同时添加到学习算法（假设有温度信息）
        double temperature = 25.0; // 实际应从硬件获取
        learning_algorithm_->AddSample(measurement.frequency_offset_ppm, temperature, measurement.timestamp_ns);
        
        // 检查状态变化
        auto status = disciplining_algorithm_->GetStatus();
        if (status.state != current_state_) {
            DiscipliningState old_state = current_state_;
            current_state_ = status.state;
            NotifyStatusChange(old_state, current_state_);
        }
    }
    
    return success;
}

void ClockDiscipliningManager::UpdateRubidiumTemperature(double temperature) {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_holdover_active_) {
        prediction_algorithm_->UpdateTemperature(temperature);
    }
}

double ClockDiscipliningManager::GetFrequencyCorrection() const {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_holdover_active_) {
        return prediction_algorithm_->GetFrequencyCorrection();
    } else if (is_gnss_disciplining_active_) {
        return disciplining_algorithm_->GetFrequencyCorrection();
    }
    
    return 0.0;
}

double ClockDiscipliningManager::GetPhaseCorrection() const {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_holdover_active_) {
        return prediction_algorithm_->GetPhaseCorrection();
    } else if (is_gnss_disciplining_active_) {
        return disciplining_algorithm_->GetPhaseCorrection();
    }
    
    return 0.0;
}

bool ClockDiscipliningManager::IsDiscipliningConverged() const {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_gnss_disciplining_active_) {
        return disciplining_algorithm_->IsConverged();
    }
    
    return false;
}

DiscipliningStatus ClockDiscipliningManager::GetDiscipliningStatus() const {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_gnss_disciplining_active_) {
        return disciplining_algorithm_->GetStatus();
    }
    
    // 返回默认状态
    DiscipliningStatus status;
    status.state = current_state_;
    status.algorithm = DiscipliningAlgorithmType::HYBRID;
    status.is_converged = false;
    return status;
}

RubidiumLearningData ClockDiscipliningManager::GetRubidiumLearningData() const {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    return learning_algorithm_->GetLearningData();
}

HoldoverPrediction ClockDiscipliningManager::GetHoldoverPrediction() const {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    
    if (is_holdover_active_) {
        return prediction_algorithm_->GetCurrentPrediction();
    }
    
    return HoldoverPrediction();
}

void ClockDiscipliningManager::SetStatusChangeCallback(std::function<void(DiscipliningState, DiscipliningState)> callback) {
    std::lock_guard<std::mutex> lock(manager_mutex_);
    status_change_callback_ = callback;
}

void ClockDiscipliningManager::NotifyStatusChange(DiscipliningState old_state, DiscipliningState new_state) {
    std::cout << "驯服状态变化: " << static_cast<int>(old_state) 
              << " -> " << static_cast<int>(new_state) << std::endl;
    
    if (status_change_callback_) {
        try {
            status_change_callback_(old_state, new_state);
        } catch (const std::exception& e) {
            std::cerr << "状态变化回调异常: " << e.what() << std::endl;
        }
    }
}

} // namespace core
} // namespace timing_server