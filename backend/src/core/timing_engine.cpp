#include "core/timing_engine.h"
#include "core/config_manager.h"
#include "core/disciplining_algorithm.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <fstream>
#include <sys/resource.h>
#include <unistd.h>

namespace timing_server {
namespace core {

// TimeSourceManager实现
TimeSourceManager::TimeSourceManager(std::shared_ptr<hal::I_HalFactory> hal_factory)
    : hal_factory_(hal_factory), running_(false) {
    
    // 初始化时间源信息
    std::vector<TimeSource> sources = {
        TimeSource::GNSS,
        TimeSource::RUBIDIUM,
        TimeSource::RTC,
        TimeSource::EXTERNAL_PPS,
        TimeSource::EXTERNAL_10MHZ,
        TimeSource::PHC
    };
    
    for (auto source : sources) {
        TimeSourceInfo info;
        info.type = source;
        info.status = TimeSourceStatus::UNAVAILABLE;
        info.priority = 99; // 默认低优先级
        info.last_update_ns = GetCurrentTimestampNs();
        
        // 设置默认质量指标
        info.quality.accuracy_ns = 1000000.0; // 1ms默认精度
        info.quality.stability_ppm = 1.0;
        info.quality.confidence = 0;
        info.quality.is_traceable = false;
        info.quality.reference = "未知";
        
        time_sources_[source] = info;
    }
}

TimeSourceManager::~TimeSourceManager() {
    Stop();
}

bool TimeSourceManager::Initialize() {
    try {
        std::cout << "初始化时间源管理器..." << std::endl;
        
        if (!hal_factory_) {
            std::cerr << "HAL工厂实例为空" << std::endl;
            return false;
        }
        
        // 创建HAL设备实例
        try {
            gnss_receiver_ = hal_factory_->CreateGnssReceiver();
            if (gnss_receiver_ && gnss_receiver_->Initialize()) {
                std::cout << "✓ GNSS接收机初始化成功" << std::endl;
                time_sources_[TimeSource::GNSS].status = TimeSourceStatus::STANDBY;
            } else {
                std::cout << "✗ GNSS接收机初始化失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "✗ GNSS接收机创建异常: " << e.what() << std::endl;
        }
        
        try {
            pps_input_ = hal_factory_->CreatePpsInput();
            if (pps_input_ && pps_input_->Initialize()) {
                std::cout << "✓ PPS输入初始化成功" << std::endl;
                time_sources_[TimeSource::EXTERNAL_PPS].status = TimeSourceStatus::STANDBY;
            } else {
                std::cout << "✗ PPS输入初始化失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "✗ PPS输入创建异常: " << e.what() << std::endl;
        }
        
        try {
            atomic_clock_ = hal_factory_->CreateAtomicClock();
            if (atomic_clock_ && atomic_clock_->Initialize()) {
                std::cout << "✓ 原子钟初始化成功" << std::endl;
                time_sources_[TimeSource::RUBIDIUM].status = TimeSourceStatus::STANDBY;
            } else {
                std::cout << "✗ 原子钟初始化失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "✗ 原子钟创建异常: " << e.what() << std::endl;
        }
        
        try {
            frequency_input_ = hal_factory_->CreateFrequencyInput();
            if (frequency_input_ && frequency_input_->Initialize()) {
                std::cout << "✓ 频率输入初始化成功" << std::endl;
                time_sources_[TimeSource::EXTERNAL_10MHZ].status = TimeSourceStatus::STANDBY;
            } else {
                std::cout << "✗ 频率输入初始化失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "✗ 频率输入创建异常: " << e.what() << std::endl;
        }
        
        try {
            rtc_ = hal_factory_->CreateRtc();
            if (rtc_ && rtc_->Initialize()) {
                std::cout << "✓ 高精度RTC初始化成功" << std::endl;
                time_sources_[TimeSource::RTC].status = TimeSourceStatus::STANDBY;
            } else {
                std::cout << "✗ 高精度RTC初始化失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "✗ 高精度RTC创建异常: " << e.what() << std::endl;
        }
        
        try {
            network_interface_ = hal_factory_->CreateNetworkInterface();
            if (network_interface_ && network_interface_->Initialize()) {
                std::cout << "✓ 网络接口初始化成功" << std::endl;
                time_sources_[TimeSource::PHC].status = TimeSourceStatus::STANDBY;
            } else {
                std::cout << "✗ 网络接口初始化失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "✗ 网络接口创建异常: " << e.what() << std::endl;
        }
        
        std::cout << "时间源管理器初始化完成" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "时间源管理器初始化异常: " << e.what() << std::endl;
        return false;
    }
}

bool TimeSourceManager::Start() {
    if (running_.load()) {
        std::cout << "时间源管理器已在运行" << std::endl;
        return true;
    }
    
    std::cout << "启动时间源监控..." << std::endl;
    running_.store(true);
    
    // 启动监控线程
    monitoring_thread_ = std::thread(&TimeSourceManager::MonitoringThread, this);
    
    std::cout << "时间源监控已启动" << std::endl;
    return true;
}

bool TimeSourceManager::Stop() {
    if (!running_.load()) {
        return true;
    }
    
    std::cout << "停止时间源监控..." << std::endl;
    running_.store(false);
    
    // 通知监控线程退出
    {
        std::lock_guard<std::mutex> lock(monitoring_mutex_);
        monitoring_cv_.notify_all();
    }
    
    // 等待监控线程结束
    if (monitoring_thread_.joinable()) {
        monitoring_thread_.join();
    }
    
    // 关闭所有HAL设备
    if (gnss_receiver_) gnss_receiver_->Close();
    if (pps_input_) pps_input_->Close();
    if (atomic_clock_) atomic_clock_->Close();
    if (frequency_input_) frequency_input_->Close();
    if (rtc_) rtc_->Close();
    if (network_interface_) network_interface_->Close();
    
    std::cout << "时间源监控已停止" << std::endl;
    return true;
}

TimeSourceInfo TimeSourceManager::GetTimeSourceInfo(TimeSource source) {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    auto it = time_sources_.find(source);
    if (it != time_sources_.end()) {
        return it->second;
    }
    
    // 返回默认信息
    TimeSourceInfo info;
    info.type = source;
    info.status = TimeSourceStatus::UNAVAILABLE;
    return info;
}

std::vector<TimeSourceInfo> TimeSourceManager::GetAllTimeSourceInfo() {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    std::vector<TimeSourceInfo> result;
    
    for (const auto& pair : time_sources_) {
        result.push_back(pair.second);
    }
    
    return result;
}

TimeSource TimeSourceManager::SelectBestTimeSource(const TimeSourcePriority& priorities) {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    
    TimeSource best_source = TimeSource::RTC; // 默认使用RTC
    uint32_t best_priority = UINT32_MAX;
    
    for (const auto& pair : time_sources_) {
        TimeSource source = pair.first;
        const TimeSourceInfo& info = pair.second;
        
        // 只考虑可用的时间源
        if (info.status != TimeSourceStatus::ACTIVE && info.status != TimeSourceStatus::STANDBY) {
            continue;
        }
        
        // 获取优先级
        uint32_t priority = UINT32_MAX;
        auto priority_it = priorities.priorities.find(source);
        if (priority_it != priorities.priorities.end()) {
            priority = priority_it->second;
        }
        
        // 选择优先级最高（数值最小）的时间源
        if (priority < best_priority) {
            best_priority = priority;
            best_source = source;
        }
    }
    
    return best_source;
}

bool TimeSourceManager::IsTimeSourceAvailable(TimeSource source) {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    auto it = time_sources_.find(source);
    if (it != time_sources_.end()) {
        return it->second.status == TimeSourceStatus::ACTIVE || 
               it->second.status == TimeSourceStatus::STANDBY;
    }
    return false;
}

void TimeSourceManager::SetStatusChangeCallback(std::function<void(TimeSource, TimeSourceStatus, TimeSourceStatus)> callback) {
    status_change_callback_ = callback;
}

void TimeSourceManager::MonitoringThread() {
    std::cout << "时间源监控线程已启动" << std::endl;
    
    while (running_.load()) {
        try {
            // 更新所有时间源状态
            for (auto source : {TimeSource::GNSS, TimeSource::RUBIDIUM, TimeSource::RTC, 
                               TimeSource::EXTERNAL_PPS, TimeSource::EXTERNAL_10MHZ, TimeSource::PHC}) {
                UpdateTimeSourceStatus(source);
            }
            
            // 等待下一次监控周期
            std::unique_lock<std::mutex> lock(monitoring_mutex_);
            monitoring_cv_.wait_for(lock, std::chrono::milliseconds(MONITORING_INTERVAL_MS), 
                                  [this] { return !running_.load(); });
            
        } catch (const std::exception& e) {
            std::cerr << "时间源监控异常: " << e.what() << std::endl;
        }
    }
    
    std::cout << "时间源监控线程已退出" << std::endl;
}

void TimeSourceManager::UpdateTimeSourceStatus(TimeSource source) {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    
    auto it = time_sources_.find(source);
    if (it == time_sources_.end()) {
        return;
    }
    
    TimeSourceInfo& info = it->second;
    TimeSourceStatus old_status = info.status;
    TimeSourceStatus new_status = old_status;
    
    try {
        switch (source) {
            case TimeSource::GNSS:
                if (gnss_receiver_) {
                    if (gnss_receiver_->IsSignalValid()) {
                        new_status = TimeSourceStatus::ACTIVE;
                        auto sat_info = gnss_receiver_->GetSatelliteInfo();
                        info.quality.confidence = std::min(100u, sat_info.satellite_count * 8);
                        info.quality.is_traceable = sat_info.is_locked;
                        info.quality.reference = "GPS/GNSS";
                        info.properties["satellites"] = std::to_string(sat_info.satellite_count);
                        info.properties["signal_strength"] = std::to_string(sat_info.signal_strength_db);
                    } else {
                        new_status = TimeSourceStatus::FAULT;
                    }
                }
                break;
                
            case TimeSource::RUBIDIUM:
                if (atomic_clock_) {
                    auto health = atomic_clock_->GetHealth();
                    if (health.is_healthy) {
                        new_status = TimeSourceStatus::STANDBY;
                        info.quality.confidence = 90;
                        info.quality.stability_ppm = 1e-12;
                        info.quality.reference = "Rubidium";
                        info.properties["temperature"] = std::to_string(health.temperature);
                        info.properties["frequency_offset"] = std::to_string(health.frequency_offset);
                    } else {
                        new_status = TimeSourceStatus::FAULT;
                    }
                }
                break;
                
            case TimeSource::RTC:
                if (rtc_) {
                    if (rtc_->IsValid()) {
                        new_status = TimeSourceStatus::STANDBY;
                        info.quality.confidence = 50;
                        info.quality.accuracy_ns = 1000000.0; // 1ms精度
                        info.quality.reference = "RTC";
                    } else {
                        new_status = TimeSourceStatus::FAULT;
                    }
                }
                break;
                
            case TimeSource::EXTERNAL_PPS:
                if (pps_input_) {
                    // 简单检查：尝试等待PPS信号（短超时）
                    if (pps_input_->WaitForPpsEdge(10)) {
                        new_status = TimeSourceStatus::ACTIVE;
                        info.quality.confidence = 95;
                        info.quality.accuracy_ns = 50.0; // 50ns精度
                        info.quality.reference = "External PPS";
                    } else {
                        new_status = TimeSourceStatus::STANDBY;
                    }
                }
                break;
                
            case TimeSource::EXTERNAL_10MHZ:
                if (frequency_input_) {
                    if (frequency_input_->IsSignalPresent()) {
                        new_status = TimeSourceStatus::ACTIVE;
                        double freq = frequency_input_->MeasureFrequency();
                        info.quality.confidence = 85;
                        info.quality.stability_ppm = 1e-9;
                        info.quality.reference = "External 10MHz";
                        info.properties["frequency"] = std::to_string(freq);
                    } else {
                        new_status = TimeSourceStatus::STANDBY;
                    }
                }
                break;
                
            case TimeSource::PHC:
                if (network_interface_) {
                    auto phc_status = network_interface_->GetPHCStatus();
                    if (phc_status.is_synchronized) {
                        new_status = TimeSourceStatus::ACTIVE;
                        info.quality.confidence = 80;
                        info.quality.accuracy_ns = 100.0; // 100ns精度
                        info.quality.reference = "PHC";
                        info.properties["offset_ns"] = std::to_string(phc_status.offset_ns);
                        info.properties["clock_class"] = std::to_string(phc_status.clock_class);
                    } else {
                        new_status = TimeSourceStatus::STANDBY;
                    }
                }
                break;
                
            default:
                break;
        }
        
        // 更新时间戳
        info.last_update_ns = GetCurrentTimestampNs();
        
        // 如果状态发生变化，通知回调
        if (new_status != old_status) {
            info.status = new_status;
            NotifyStatusChange(source, old_status, new_status);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "更新时间源状态异常 (" << TimeSourceToString(source) << "): " << e.what() << std::endl;
        info.status = TimeSourceStatus::FAULT;
        if (old_status != TimeSourceStatus::FAULT) {
            NotifyStatusChange(source, old_status, TimeSourceStatus::FAULT);
        }
    }
}

TimeQuality TimeSourceManager::EvaluateTimeSourceQuality(TimeSource source) {
    std::lock_guard<std::mutex> lock(sources_mutex_);
    
    auto it = time_sources_.find(source);
    if (it != time_sources_.end()) {
        return it->second.quality;
    }
    
    // 返回默认质量指标
    TimeQuality quality;
    quality.accuracy_ns = 1000000.0;
    quality.stability_ppm = 1.0;
    quality.confidence = 0;
    quality.is_traceable = false;
    quality.reference = "未知";
    return quality;
}

void TimeSourceManager::NotifyStatusChange(TimeSource source, TimeSourceStatus old_status, TimeSourceStatus new_status) {
    std::cout << "时间源状态变化: " << TimeSourceToString(source) 
              << " " << TimeSourceStatusToString(old_status) 
              << " -> " << TimeSourceStatusToString(new_status) << std::endl;
    
    if (status_change_callback_) {
        try {
            status_change_callback_(source, old_status, new_status);
        } catch (const std::exception& e) {
            std::cerr << "状态变化回调异常: " << e.what() << std::endl;
        }
    }
}

// TimingEngine实现
TimingEngine::TimingEngine(const TimingConfig& config)
    : config_(config), active_time_source_(TimeSource::RTC), running_(false) {
    
    // 初始化系统状态
    system_status_.current_state = ClockState::FREE_RUN;
    system_status_.active_source = active_time_source_;
    system_status_.health = SystemHealth::WARNING;
    system_status_.uptime_seconds = 0;
    system_status_.cpu_usage_percent = 0.0;
    system_status_.memory_usage_mb = 0;
    system_status_.version = VERSION;
    
    // 检测平台
    auto& hal_manager = hal::HalFactoryManager::GetInstance();
    if (hal_manager.IsInitialized()) {
        const auto& platform_info = hal_manager.GetPlatformInfo();
        system_status_.platform = platform_info.description;
    } else {
        system_status_.platform = "未知平台";
    }
    
    // 初始化性能指标
    performance_metrics_ = {};
    start_time_ = std::chrono::steady_clock::now();
    
    std::cout << "授时引擎已创建，平台: " << system_status_.platform << std::endl;
}

TimingEngine::~TimingEngine() {
    Stop();
}

bool TimingEngine::Start() {
    if (running_.load()) {
        std::cout << "授时引擎已在运行" << std::endl;
        return true;
    }
    
    std::cout << "启动授时引擎..." << std::endl;
    
    if (!Initialize()) {
        std::cerr << "授时引擎初始化失败" << std::endl;
        return false;
    }
    
    running_.store(true);
    
    // 启动控制线程
    control_thread_ = std::thread(&TimingEngine::ControlLoop, this);
    
    std::cout << "授时引擎已启动" << std::endl;
    return true;
}

bool TimingEngine::Stop() {
    if (!running_.load()) {
        return true;
    }
    
    std::cout << "停止授时引擎..." << std::endl;
    running_.store(false);
    
    // 通知控制线程退出
    {
        std::lock_guard<std::mutex> lock(control_mutex_);
        control_cv_.notify_all();
    }
    
    // 等待控制线程结束
    if (control_thread_.joinable()) {
        control_thread_.join();
    }
    
    // 停止组件
    if (time_source_manager_) {
        time_source_manager_->Stop();
    }
    
    if (state_machine_) {
        state_machine_->Stop();
    }
    
    // 清理HAL工厂
    if (hal_factory_) {
        auto& hal_manager = hal::HalFactoryManager::GetInstance();
        hal_manager.Cleanup();
    }
    
    std::cout << "授时引擎已停止" << std::endl;
    return true;
}

ClockState TimingEngine::GetCurrentState() {
    if (state_machine_) {
        return state_machine_->GetCurrentState();
    }
    return ClockState::FREE_RUN;
}

SystemStatus TimingEngine::GetSystemStatus() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 更新动态信息
    system_status_.current_state = GetCurrentState();
    system_status_.active_source = active_time_source_;
    system_status_.uptime_seconds = GetUptime();
    
    // 更新时间源信息
    if (time_source_manager_) {
        system_status_.sources = time_source_manager_->GetAllTimeSourceInfo();
    }
    
    // 更新系统健康状况
    system_status_.health = CheckSystemHealth();
    
    return system_status_;
}

bool TimingEngine::ConfigureTimeSource(const TimeSourcePriority& config) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    config_.priorities = config;
    
    // 重新选择最佳时间源
    if (time_source_manager_) {
        TimeSource new_source = time_source_manager_->SelectBestTimeSource(config);
        if (new_source != active_time_source_) {
            return SwitchTimeSource(new_source);
        }
    }
    
    return true;
}

TimeSource TimingEngine::GetActiveTimeSource() {
    return active_time_source_;
}

TimeSourceInfo TimingEngine::GetTimeSourceInfo(TimeSource source) {
    if (time_source_manager_) {
        return time_source_manager_->GetTimeSourceInfo(source);
    }
    
    TimeSourceInfo info;
    info.type = source;
    info.status = TimeSourceStatus::UNAVAILABLE;
    return info;
}

std::vector<TimeSourceInfo> TimingEngine::GetAllTimeSourceInfo() {
    if (time_source_manager_) {
        return time_source_manager_->GetAllTimeSourceInfo();
    }
    return {};
}

bool TimingEngine::Initialize() {
    try {
        std::cout << "初始化授时引擎组件..." << std::endl;
        
        // 初始化HAL工厂
        auto& hal_manager = hal::HalFactoryManager::GetInstance();
        if (!hal_manager.IsInitialized()) {
            if (!hal_manager.Initialize()) {
                std::cerr << "HAL工厂初始化失败" << std::endl;
                return false;
            }
        }
        
        hal_factory_ = hal_manager.GetFactory();
        if (!hal_factory_) {
            std::cerr << "获取HAL工厂失败" << std::endl;
            return false;
        }
        
        // 初始化时间源管理器
        time_source_manager_ = std::make_unique<TimeSourceManager>(hal_factory_);
        if (!time_source_manager_->Initialize()) {
            std::cerr << "时间源管理器初始化失败" << std::endl;
            return false;
        }
        
        // 设置时间源状态变化回调
        time_source_manager_->SetStatusChangeCallback(
            [this](TimeSource source, TimeSourceStatus old_status, TimeSourceStatus new_status) {
                HandleTimeSourceStatusChange(source, old_status, new_status);
            });
        
        // 启动时间源管理器
        if (!time_source_manager_->Start()) {
            std::cerr << "时间源管理器启动失败" << std::endl;
            return false;
        }
        
        // 初始化状态机
        state_machine_ = std::make_unique<ClockStateMachine>(ClockState::FREE_RUN);
        if (!state_machine_->Initialize()) {
            std::cerr << "状态机初始化失败" << std::endl;
            return false;
        }
        
        // 添加状态机监听器
        state_machine_->AddListener(std::shared_ptr<IStateMachineListener>(this, [](IStateMachineListener*){}));
        
        // 启动状态机
        if (!state_machine_->Start()) {
            std::cerr << "状态机启动失败" << std::endl;
            return false;
        }
        
        // 初始化驯服管理器
        disciplining_manager_ = std::make_unique<ClockDiscipliningManager>();
        if (!disciplining_manager_->Initialize(config_)) {
            std::cerr << "驯服管理器初始化失败" << std::endl;
            return false;
        }
        
        // 设置驯服状态变化回调
        disciplining_manager_->SetStatusChangeCallback(
            [this](DiscipliningState old_state, DiscipliningState new_state) {
                std::cout << "驯服状态变化: " << static_cast<int>(old_state) 
                          << " -> " << static_cast<int>(new_state) << std::endl;
            });
        
        // 选择初始时间源
        active_time_source_ = time_source_manager_->SelectBestTimeSource(config_.priorities);
        
        std::cout << "授时引擎初始化完成，初始时间源: " << TimeSourceToString(active_time_source_) << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "授时引擎初始化异常: " << e.what() << std::endl;
        return false;
    }
}

void TimingEngine::ControlLoop() {
    std::cout << "授时引擎控制循环已启动" << std::endl;
    
    while (running_.load()) {
        try {
            // 根据当前状态执行相应逻辑
            ClockState current_state = GetCurrentState();
            
            switch (current_state) {
                case ClockState::FREE_RUN:
                    HandleFreeRunState();
                    break;
                case ClockState::DISCIPLINING:
                    HandleDiscipliningState();
                    break;
                case ClockState::LOCKED:
                    HandleLockedState();
                    break;
                case ClockState::HOLDOVER:
                    HandleHoldoverState();
                    break;
            }
            
            // 更新系统状态
            UpdateSystemStatus();
            
            // 记录性能指标
            RecordPerformanceMetrics();
            
            // 等待下一次控制周期
            std::unique_lock<std::mutex> lock(control_mutex_);
            control_cv_.wait_for(lock, std::chrono::milliseconds(CONTROL_LOOP_INTERVAL_MS), 
                               [this] { return !running_.load(); });
            
        } catch (const std::exception& e) {
            std::cerr << "控制循环异常: " << e.what() << std::endl;
        }
    }
    
    std::cout << "授时引擎控制循环已退出" << std::endl;
}

void TimingEngine::HandleTimeSourceStatusChange(TimeSource source, TimeSourceStatus old_status, TimeSourceStatus new_status) {
    // 如果当前活跃时间源出现故障，需要切换
    if (source == active_time_source_ && new_status == TimeSourceStatus::FAULT) {
        std::cout << "当前时间源故障，尝试切换..." << std::endl;
        
        // 选择新的时间源
        TimeSource new_source = time_source_manager_->SelectBestTimeSource(config_.priorities);
        if (new_source != active_time_source_) {
            SwitchTimeSource(new_source);
        }
        
        // 触发硬件故障事件
        if (state_machine_) {
            StateMachineEvent event(ClockEvent::HARDWARE_FAULT, "时间源故障: " + TimeSourceToString(source));
            state_machine_->ProcessEvent(event);
        }
    }
    
    // 如果GNSS信号状态变化，触发相应事件
    if (source == TimeSource::GNSS) {
        if (old_status != TimeSourceStatus::ACTIVE && new_status == TimeSourceStatus::ACTIVE) {
            // GNSS信号获取
            if (state_machine_) {
                StateMachineEvent event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号获取");
                state_machine_->ProcessEvent(event);
            }
        } else if (old_status == TimeSourceStatus::ACTIVE && new_status != TimeSourceStatus::ACTIVE) {
            // GNSS信号丢失
            if (state_machine_) {
                StateMachineEvent event(ClockEvent::GNSS_SIGNAL_LOST, "GNSS信号丢失");
                state_machine_->ProcessEvent(event);
            }
        }
    }
}

bool TimingEngine::SwitchTimeSource(TimeSource new_source) {
    if (new_source == active_time_source_) {
        return true;
    }
    
    if (!time_source_manager_->IsTimeSourceAvailable(new_source)) {
        std::cerr << "目标时间源不可用: " << TimeSourceToString(new_source) << std::endl;
        return false;
    }
    
    std::cout << "切换时间源: " << TimeSourceToString(active_time_source_) 
              << " -> " << TimeSourceToString(new_source) << std::endl;
    
    active_time_source_ = new_source;
    
    // 更新系统状态
    {
        std::lock_guard<std::mutex> lock(status_mutex_);
        system_status_.active_source = active_time_source_;
    }
    
    return true;
}

void TimingEngine::UpdateSystemStatus() {
    std::lock_guard<std::mutex> lock(status_mutex_);
    
    // 计算资源使用情况
    CalculateResourceUsage();
    
    // 更新运行时间
    system_status_.uptime_seconds = GetUptime();
    
    // 更新健康状况
    system_status_.health = CheckSystemHealth();
}

void TimingEngine::HandleFreeRunState() {
    // 在自由运行状态下，检查是否有GNSS信号可用
    if (CheckGnssSignal()) {
        // 如果GNSS信号可用，尝试进入驯服状态
        if (state_machine_) {
            StateMachineEvent event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "检测到GNSS信号");
            state_machine_->ProcessEvent(event);
        }
    }
}

void TimingEngine::HandleDiscipliningState() {
    // 启动GNSS驯服（如果尚未启动）
    if (disciplining_manager_ && CheckGnssSignal()) {
        disciplining_manager_->StartGnssDisciplining();
        
        // 生成模拟时间测量数据并处理
        // 在实际实现中，这些数据应该来自硬件测量
        TimeMeasurement measurement;
        measurement.timestamp_ns = GetCurrentTimestampNs();
        measurement.phase_offset_ns = 100.0; // 模拟相位偏移
        measurement.frequency_offset_ppm = 0.1; // 模拟频率偏移
        measurement.measurement_noise_ns = 10.0;
        measurement.source = TimeSource::GNSS;
        measurement.is_valid = true;
        
        disciplining_manager_->ProcessTimeMeasurement(measurement);
        
        // 更新铷钟温度（模拟数据）
        disciplining_manager_->UpdateRubidiumTemperature(25.0);
    }
    
    // 在驯服状态下，检查收敛情况
    if (CheckDiscipliningConvergence()) {
        // 如果已收敛，进入锁定状态
        if (state_machine_) {
            StateMachineEvent event(ClockEvent::CONVERGENCE_ACHIEVED, "驯服收敛完成");
            state_machine_->ProcessEvent(event);
        }
    }
    
    // 检查GNSS信号是否仍然有效
    if (!CheckGnssSignal()) {
        if (disciplining_manager_) {
            disciplining_manager_->StopGnssDisciplining();
        }
        if (state_machine_) {
            StateMachineEvent event(ClockEvent::GNSS_SIGNAL_LOST, "GNSS信号丢失");
            state_machine_->ProcessEvent(event);
        }
    }
}

void TimingEngine::HandleLockedState() {
    // 在锁定状态下，继续GNSS驯服和学习
    if (disciplining_manager_ && CheckGnssSignal()) {
        // 继续处理测量数据进行学习
        TimeMeasurement measurement;
        measurement.timestamp_ns = GetCurrentTimestampNs();
        measurement.phase_offset_ns = 20.0; // 锁定状态下的小相位偏移
        measurement.frequency_offset_ppm = 0.01; // 锁定状态下的小频率偏移
        measurement.measurement_noise_ns = 5.0;
        measurement.source = TimeSource::GNSS;
        measurement.is_valid = true;
        
        disciplining_manager_->ProcessTimeMeasurement(measurement);
        
        // 更新铷钟温度
        disciplining_manager_->UpdateRubidiumTemperature(25.0);
    }
    
    // 持续监控GNSS信号
    if (!CheckGnssSignal()) {
        // 如果GNSS信号丢失，进入守时状态
        if (disciplining_manager_) {
            disciplining_manager_->StopGnssDisciplining();
            disciplining_manager_->StartHoldover();
        }
        if (state_machine_) {
            StateMachineEvent event(ClockEvent::GNSS_SIGNAL_LOST, "GNSS信号丢失，进入守时");
            state_machine_->ProcessEvent(event);
        }
    }
}

void TimingEngine::HandleHoldoverState() {
    // 确保守时预测已启动
    if (disciplining_manager_) {
        // 更新铷钟温度以进行温度补偿
        disciplining_manager_->UpdateRubidiumTemperature(25.0);
    }
    
    // 在守时状态下，检查是否超时
    if (CheckHoldoverTimeout()) {
        if (disciplining_manager_) {
            disciplining_manager_->StopHoldover();
        }
        if (state_machine_) {
            StateMachineEvent event(ClockEvent::HOLDOVER_TIMEOUT, "守时超时");
            state_machine_->ProcessEvent(event);
        }
    }
    
    // 检查GNSS信号是否恢复
    if (CheckGnssSignal()) {
        if (disciplining_manager_) {
            disciplining_manager_->StopHoldover();
            disciplining_manager_->StartGnssDisciplining();
        }
        if (state_machine_) {
            StateMachineEvent event(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号恢复");
            state_machine_->ProcessEvent(event);
        }
    }
}

bool TimingEngine::CheckGnssSignal() {
    if (!time_source_manager_) {
        return false;
    }
    
    TimeSourceInfo gnss_info = time_source_manager_->GetTimeSourceInfo(TimeSource::GNSS);
    return gnss_info.status == TimeSourceStatus::ACTIVE;
}

bool TimingEngine::CheckDiscipliningConvergence() {
    if (!disciplining_manager_) {
        return false;
    }
    
    // 使用驯服管理器的收敛检查功能
    return disciplining_manager_->IsDiscipliningConverged();
}

bool TimingEngine::CheckHoldoverTimeout() {
    if (!state_machine_) {
        return false;
    }
    
    // 检查守时是否超时
    return state_machine_->IsHoldoverTimeout(config_.holdover.max_holdover_hours);
}

void TimingEngine::RecordPerformanceMetrics() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    
    // 更新状态转换次数
    if (state_machine_) {
        // 这里需要从状态机获取统计信息
        // performance_metrics_.state_transitions = state_machine_->GetTransitionCount();
    }
    
    // 更新当前精度
    if (time_source_manager_) {
        TimeSourceInfo active_info = time_source_manager_->GetTimeSourceInfo(active_time_source_);
        performance_metrics_.current_accuracy_ns = active_info.quality.accuracy_ns;
    }
    
    // 更新平均响应时间（简化实现）
    performance_metrics_.average_response_time_ms = 1.0; // 1ms
}

void TimingEngine::CalculateResourceUsage() {
    // 获取CPU使用率（简化实现）
    system_status_.cpu_usage_percent = 2.5; // 假设2.5%
    
    // 获取内存使用量
    struct rusage usage;
    if (getrusage(RUSAGE_SELF, &usage) == 0) {
        system_status_.memory_usage_mb = usage.ru_maxrss / 1024; // KB转MB
    } else {
        system_status_.memory_usage_mb = 50; // 默认50MB
    }
}

uint64_t TimingEngine::GetUptime() const {
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_);
    return duration.count();
}

SystemHealth TimingEngine::CheckSystemHealth() const {
    // 检查关键组件状态
    if (!time_source_manager_ || !state_machine_) {
        return SystemHealth::CRITICAL;
    }
    
    // 检查是否有可用的时间源
    bool has_active_source = false;
    if (time_source_manager_) {
        auto sources = time_source_manager_->GetAllTimeSourceInfo();
        for (const auto& source : sources) {
            if (source.status == TimeSourceStatus::ACTIVE || source.status == TimeSourceStatus::STANDBY) {
                has_active_source = true;
                break;
            }
        }
    }
    
    if (!has_active_source) {
        return SystemHealth::ERROR;
    }
    
    // 检查系统资源使用情况
    if (system_status_.cpu_usage_percent > 80.0 || system_status_.memory_usage_mb > 200) {
        return SystemHealth::WARNING;
    }
    
    return SystemHealth::HEALTHY;
}

// IStateMachineListener接口实现
void TimingEngine::OnStateEntered(ClockState state, ClockState previous_state) {
    std::cout << "状态机进入状态: " << ClockStateToString(previous_state) 
              << " -> " << ClockStateToString(state) << std::endl;
    
    std::lock_guard<std::mutex> lock(status_mutex_);
    system_status_.current_state = state;
}

void TimingEngine::OnStateExited(ClockState state, ClockState next_state) {
    std::cout << "状态机退出状态: " << ClockStateToString(state) 
              << " -> " << ClockStateToString(next_state) << std::endl;
}

void TimingEngine::OnEventProcessed(const StateMachineEvent& event, ClockState current_state) {
    std::cout << "状态机处理事件: " << ClockEventToString(event.event) 
              << " (当前状态: " << ClockStateToString(current_state) << ")" << std::endl;
}

void TimingEngine::OnTransitionFailed(const StateMachineEvent& event, ClockState current_state, TransitionResult result) {
    std::cerr << "状态机转换失败: " << ClockEventToString(event.event) 
              << " (当前状态: " << ClockStateToString(current_state) 
              << ", 结果: " << TransitionResultToString(result) << ")" << std::endl;
}

// 其他公共方法实现
bool TimingEngine::SetConfiguration(const TimingConfig& config) {
    std::lock_guard<std::mutex> lock(status_mutex_);
    config_ = config;
    
    // 重新配置时间源优先级
    return ConfigureTimeSource(config.priorities);
}

const TimingConfig& TimingEngine::GetConfiguration() const {
    return config_;
}

PerformanceMetrics TimingEngine::GetPerformanceMetrics() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    return performance_metrics_;
}

bool TimingEngine::TriggerEvent(ClockEvent event) {
    if (!state_machine_) {
        return false;
    }
    
    StateMachineEvent sm_event(event, "手动触发事件");
    TransitionResult result = state_machine_->ProcessEvent(sm_event);
    return result == TransitionResult::SUCCESS;
}

bool TimingEngine::ForceTimeSourceSwitch(TimeSource source) {
    return SwitchTimeSource(source);
}

DiscipliningStatus TimingEngine::GetDiscipliningStatus() const {
    if (disciplining_manager_) {
        return disciplining_manager_->GetDiscipliningStatus();
    }
    
    // 返回默认状态
    DiscipliningStatus status;
    status.state = DiscipliningState::INITIALIZING;
    status.algorithm = DiscipliningAlgorithmType::HYBRID;
    status.is_converged = false;
    return status;
}

RubidiumLearningData TimingEngine::GetRubidiumLearningData() const {
    if (disciplining_manager_) {
        return disciplining_manager_->GetRubidiumLearningData();
    }
    
    return RubidiumLearningData();
}

HoldoverPrediction TimingEngine::GetHoldoverPrediction() const {
    if (disciplining_manager_) {
        return disciplining_manager_->GetHoldoverPrediction();
    }
    
    return HoldoverPrediction();
}

double TimingEngine::GetFrequencyCorrection() const {
    if (disciplining_manager_) {
        return disciplining_manager_->GetFrequencyCorrection();
    }
    
    return 0.0;
}

double TimingEngine::GetPhaseCorrection() const {
    if (disciplining_manager_) {
        return disciplining_manager_->GetPhaseCorrection();
    }
    
    return 0.0;
}

} // namespace core
} // namespace timing_server