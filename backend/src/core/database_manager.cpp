#include "core/database_manager.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <regex>

namespace timing_server {
namespace core {

// EventRecord从LogEntry构造的实现
EventRecord::EventRecord(const LogEntry& log_entry) {
    timestamp_ns = log_entry.timestamp_ns;
    level = LogLevelToString(log_entry.level);
    component = LogComponentToString(log_entry.component);
    event_type = "LOG_EVENT";
    message = log_entry.message;
    
    // 构建上下文JSON
    std::ostringstream context_json;
    context_json << "{";
    context_json << "\"file\":\"" << log_entry.file << "\",";
    context_json << "\"line\":" << log_entry.line << ",";
    context_json << "\"function\":\"" << log_entry.function << "\"";
    
    if (!log_entry.context.empty()) {
        context_json << ",\"context\":{";
        bool first = true;
        for (const auto& [key, value] : log_entry.context) {
            if (!first) context_json << ",";
            context_json << "\"" << key << "\":\"" << value << "\"";
            first = false;
        }
        context_json << "}";
    }
    context_json << "}";
    
    context = context_json.str();
}

// DatabaseFilter的WHERE子句构建
std::string DatabaseFilter::BuildWhereClause() const {
    std::vector<std::string> conditions;
    
    if (start_time_ns > 0) {
        conditions.push_back("timestamp >= " + std::to_string(start_time_ns));
    }
    
    if (end_time_ns < UINT64_MAX) {
        conditions.push_back("timestamp <= " + std::to_string(end_time_ns));
    }
    
    if (!sources.empty()) {
        std::string source_list = "(";
        for (size_t i = 0; i < sources.size(); ++i) {
            if (i > 0) source_list += ",";
            source_list += "'" + sources[i] + "'";
        }
        source_list += ")";
        conditions.push_back("source IN " + source_list);
    }
    
    if (!types.empty()) {
        std::string type_list = "(";
        for (size_t i = 0; i < types.size(); ++i) {
            if (i > 0) type_list += ",";
            type_list += "'" + types[i] + "'";
        }
        type_list += ")";
        conditions.push_back("metric_type IN " + type_list);
    }
    
    if (conditions.empty()) {
        return "";
    }
    
    return "WHERE " + std::accumulate(conditions.begin() + 1, conditions.end(),
                                     conditions[0],
                                     [](const std::string& a, const std::string& b) {
                                         return a + " AND " + b;
                                     });
}

// DatabaseConnectionPool实现

DatabaseConnectionPool::DatabaseConnectionPool(const std::string& db_path, size_t pool_size)
    : db_path_(db_path), pool_size_(pool_size), initialized_(false), shutdown_requested_(false) {
}

DatabaseConnectionPool::~DatabaseConnectionPool() {
    Shutdown();
}

DatabaseResult DatabaseConnectionPool::Initialize() {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    if (initialized_.load()) {
        return DatabaseResult::SUCCESS;
    }
    
    // 确保数据库目录存在
    std::filesystem::path db_file_path(db_path_);
    std::filesystem::path db_dir = db_file_path.parent_path();
    
    if (!db_dir.empty() && !std::filesystem::exists(db_dir)) {
        std::error_code ec;
        if (!std::filesystem::create_directories(db_dir, ec)) {
            LOG_ERROR(LogComponent::SYSTEM, 
                     "创建数据库目录失败: " + db_dir.string() + ", 错误: " + ec.message());
            return DatabaseResult::ERROR_OPEN_FAILED;
        }
    }
    
    // 创建连接池
    for (size_t i = 0; i < pool_size_; ++i) {
        sqlite3* conn = CreateConnection();
        if (!conn) {
            LOG_ERROR(LogComponent::SYSTEM, "创建数据库连接失败");
            // 清理已创建的连接
            while (!available_connections_.empty()) {
                CloseConnection(available_connections_.front());
                available_connections_.pop();
            }
            return DatabaseResult::ERROR_OPEN_FAILED;
        }
        available_connections_.push(conn);
    }
    
    initialized_.store(true);
    LOG_INFO(LogComponent::SYSTEM, 
             "数据库连接池初始化成功，连接数: " + std::to_string(pool_size_));
    
    return DatabaseResult::SUCCESS;
}

void DatabaseConnectionPool::Shutdown() {
    {
        std::lock_guard<std::mutex> lock(pool_mutex_);
        shutdown_requested_.store(true);
    }
    
    pool_condition_.notify_all();
    
    std::lock_guard<std::mutex> lock(pool_mutex_);
    while (!available_connections_.empty()) {
        CloseConnection(available_connections_.front());
        available_connections_.pop();
    }
    
    initialized_.store(false);
    LOG_INFO(LogComponent::SYSTEM, "数据库连接池已关闭");
}

sqlite3* DatabaseConnectionPool::GetConnection(int timeout_ms) {
    std::unique_lock<std::mutex> lock(pool_mutex_);
    
    if (!initialized_.load() || shutdown_requested_.load()) {
        return nullptr;
    }
    
    // 等待可用连接
    auto timeout = std::chrono::milliseconds(timeout_ms);
    if (!pool_condition_.wait_for(lock, timeout, [this] {
        return !available_connections_.empty() || shutdown_requested_.load();
    })) {
        LOG_WARNING(LogComponent::SYSTEM, "获取数据库连接超时");
        return nullptr;
    }
    
    if (shutdown_requested_.load() || available_connections_.empty()) {
        return nullptr;
    }
    
    sqlite3* conn = available_connections_.front();
    available_connections_.pop();
    
    return conn;
}

void DatabaseConnectionPool::ReturnConnection(sqlite3* conn) {
    if (!conn) return;
    
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    if (!shutdown_requested_.load()) {
        available_connections_.push(conn);
        pool_condition_.notify_one();
    } else {
        CloseConnection(conn);
    }
}

DatabaseConnectionPool::PoolStatus DatabaseConnectionPool::GetStatus() const {
    std::lock_guard<std::mutex> lock(pool_mutex_);
    
    PoolStatus status;
    status.total_connections = pool_size_;
    status.available_connections = available_connections_.size();
    status.active_connections = pool_size_ - available_connections_.size();
    
    return status;
}

sqlite3* DatabaseConnectionPool::CreateConnection() {
    sqlite3* conn = nullptr;
    
    int result = sqlite3_open_v2(db_path_.c_str(), &conn,
                                SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_FULLMUTEX,
                                nullptr);
    
    if (result != SQLITE_OK) {
        LOG_ERROR(LogComponent::SYSTEM, 
                 "打开数据库失败: " + std::string(sqlite3_errmsg(conn)));
        if (conn) {
            sqlite3_close(conn);
        }
        return nullptr;
    }
    
    // 设置数据库配置
    sqlite3_exec(conn, "PRAGMA journal_mode=WAL;", nullptr, nullptr, nullptr);
    sqlite3_exec(conn, "PRAGMA synchronous=NORMAL;", nullptr, nullptr, nullptr);
    sqlite3_exec(conn, "PRAGMA cache_size=10000;", nullptr, nullptr, nullptr);
    sqlite3_exec(conn, "PRAGMA temp_store=MEMORY;", nullptr, nullptr, nullptr);
    sqlite3_exec(conn, "PRAGMA mmap_size=268435456;", nullptr, nullptr, nullptr); // 256MB
    
    return conn;
}

void DatabaseConnectionPool::CloseConnection(sqlite3* conn) {
    if (conn) {
        sqlite3_close(conn);
    }
}

// DatabaseManager实现

DatabaseManager& DatabaseManager::GetInstance() {
    static DatabaseManager instance;
    return instance;
}

DatabaseManager::~DatabaseManager() {
    Shutdown();
}

DatabaseResult DatabaseManager::Initialize(const std::string& db_path, size_t pool_size) {
    if (initialized_.load()) {
        return DatabaseResult::SUCCESS;
    }
    
    connection_pool_ = std::make_unique<DatabaseConnectionPool>(db_path, pool_size);
    
    DatabaseResult result = connection_pool_->Initialize();
    if (result != DatabaseResult::SUCCESS) {
        return result;
    }
    
    // 创建数据库表结构
    result = CreateTables();
    if (result != DatabaseResult::SUCCESS) {
        return result;
    }
    
    // 初始化异步写入
    async_writing_enabled_ = true;
    batch_size_ = 100;
    shutdown_requested_.store(false);
    
    writer_thread_ = std::make_unique<std::thread>(&DatabaseManager::AsyncWriterThread, this);
    
    initialized_.store(true);
    LOG_INFO(LogComponent::SYSTEM, "数据库管理器初始化成功");
    
    return DatabaseResult::SUCCESS;
}

void DatabaseManager::Shutdown() {
    if (!initialized_.load()) {
        return;
    }
    
    // 停止异步写入线程
    {
        std::lock_guard<std::mutex> lock(pending_mutex_);
        shutdown_requested_.store(true);
    }
    pending_condition_.notify_all();
    
    if (writer_thread_ && writer_thread_->joinable()) {
        writer_thread_->join();
    }
    
    // 刷新剩余数据
    FlushPendingWrites();
    
    // 关闭连接池
    if (connection_pool_) {
        connection_pool_->Shutdown();
    }
    
    initialized_.store(false);
    LOG_INFO(LogComponent::SYSTEM, "数据库管理器已关闭");
}

DatabaseResult DatabaseManager::CreateTables() {
    const std::vector<std::string> create_table_sqls = {
        // 性能指标表
        R"(
        CREATE TABLE IF NOT EXISTS metrics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp INTEGER NOT NULL,
            metric_type TEXT NOT NULL,
            source TEXT NOT NULL,
            value REAL NOT NULL,
            unit TEXT NOT NULL,
            quality INTEGER DEFAULT 100,
            UNIQUE(timestamp, metric_type, source)
        )
        )",
        
        // 事件日志表
        R"(
        CREATE TABLE IF NOT EXISTS events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp INTEGER NOT NULL,
            level TEXT NOT NULL,
            component TEXT NOT NULL,
            event_type TEXT NOT NULL,
            message TEXT NOT NULL,
            context TEXT
        )
        )",
        
        // 状态转换表
        R"(
        CREATE TABLE IF NOT EXISTS state_transitions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp INTEGER NOT NULL,
            from_state TEXT NOT NULL,
            to_state TEXT NOT NULL,
            trigger_event TEXT NOT NULL,
            duration_ms INTEGER,
            success BOOLEAN NOT NULL
        )
        )",
        
        // 创建索引
        "CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_metrics_type ON metrics(metric_type);",
        "CREATE INDEX IF NOT EXISTS idx_metrics_source ON metrics(source);",
        "CREATE INDEX IF NOT EXISTS idx_events_timestamp ON events(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_events_level ON events(level);",
        "CREATE INDEX IF NOT EXISTS idx_events_component ON events(component);",
        "CREATE INDEX IF NOT EXISTS idx_transitions_timestamp ON state_transitions(timestamp);",
        "CREATE INDEX IF NOT EXISTS idx_transitions_states ON state_transitions(from_state, to_state);"
    };
    
    for (const auto& sql : create_table_sqls) {
        DatabaseResult result = ExecuteSQL(sql);
        if (result != DatabaseResult::SUCCESS) {
            LOG_ERROR(LogComponent::SYSTEM, "创建数据库表失败: " + sql);
            return result;
        }
    }
    
    LOG_INFO(LogComponent::SYSTEM, "数据库表结构创建成功");
    return DatabaseResult::SUCCESS;
}

DatabaseResult DatabaseManager::InsertMetric(const MetricRecord& record) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    if (async_writing_enabled_) {
        // 异步写入
        std::lock_guard<std::mutex> lock(pending_mutex_);
        pending_metrics_.push(record);
        pending_condition_.notify_one();
        return DatabaseResult::SUCCESS;
    } else {
        // 同步写入
        return InsertMetrics({record});
    }
}

DatabaseResult DatabaseManager::InsertMetrics(const std::vector<MetricRecord>& records) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    if (records.empty()) {
        return DatabaseResult::SUCCESS;
    }
    
    sqlite3* conn = connection_pool_->GetConnection();
    if (!conn) {
        return DatabaseResult::ERROR_BUSY;
    }
    
    DatabaseResult result = DatabaseResult::SUCCESS;
    
    // 开始事务
    result = BeginTransaction(conn);
    if (result != DatabaseResult::SUCCESS) {
        connection_pool_->ReturnConnection(conn);
        return result;
    }
    
    const char* sql = R"(
        INSERT OR REPLACE INTO metrics 
        (timestamp, metric_type, source, value, unit, quality) 
        VALUES (?, ?, ?, ?, ?, ?)
    )";
    
    sqlite3_stmt* stmt;
    int prepare_result = sqlite3_prepare_v2(conn, sql, -1, &stmt, nullptr);
    if (prepare_result != SQLITE_OK) {
        LOG_ERROR(LogComponent::SYSTEM, 
                 "准备插入指标SQL失败: " + std::string(sqlite3_errmsg(conn)));
        RollbackTransaction(conn);
        connection_pool_->ReturnConnection(conn);
        return DatabaseResult::ERROR_PREPARE_FAILED;
    }
    
    for (const auto& record : records) {
        sqlite3_bind_int64(stmt, 1, record.timestamp_ns);
        sqlite3_bind_text(stmt, 2, record.metric_type.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 3, record.source.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_double(stmt, 4, record.value);
        sqlite3_bind_text(stmt, 5, record.unit.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_int(stmt, 6, record.quality);
        
        int step_result = sqlite3_step(stmt);
        if (step_result != SQLITE_DONE) {
            LOG_ERROR(LogComponent::SYSTEM, 
                     "执行插入指标SQL失败: " + std::string(sqlite3_errmsg(conn)));
            result = DatabaseResult::ERROR_EXECUTE_FAILED;
            break;
        }
        
        sqlite3_reset(stmt);
    }
    
    sqlite3_finalize(stmt);
    
    if (result == DatabaseResult::SUCCESS) {
        result = CommitTransaction(conn);
    } else {
        RollbackTransaction(conn);
    }
    
    connection_pool_->ReturnConnection(conn);
    return result;
}

std::vector<MetricRecord> DatabaseManager::QueryMetrics(const DatabaseFilter& filter) {
    std::vector<MetricRecord> results;
    
    if (!initialized_.load()) {
        return results;
    }
    
    std::string sql = "SELECT timestamp, metric_type, source, value, unit, quality FROM metrics ";
    sql += filter.BuildWhereClause();
    sql += " ORDER BY timestamp ";
    sql += filter.ascending ? "ASC" : "DESC";
    sql += " LIMIT " + std::to_string(filter.limit);
    
    ExecuteQuery(sql, nullptr, [&results](sqlite3_stmt* stmt) {
        MetricRecord record;
        record.timestamp_ns = sqlite3_column_int64(stmt, 0);
        record.metric_type = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
        record.source = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
        record.value = sqlite3_column_double(stmt, 3);
        record.unit = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4));
        record.quality = sqlite3_column_int(stmt, 5);
        results.push_back(record);
    });
    
    return results;
}

DatabaseManager::MetricStatistics DatabaseManager::GetMetricStatistics(
    const std::string& metric_type, const std::string& source,
    uint64_t start_time, uint64_t end_time) {
    
    MetricStatistics stats = {0.0, 0.0, 0.0, 0.0, 0};
    
    if (!initialized_.load()) {
        return stats;
    }
    
    std::string sql = R"(
        SELECT AVG(value), MAX(value), MIN(value), COUNT(*) 
        FROM metrics 
        WHERE metric_type = ? AND source = ? 
        AND timestamp >= ? AND timestamp <= ?
    )";
    
    ExecuteQuery(sql, 
        [&](sqlite3_stmt* stmt) {
            sqlite3_bind_text(stmt, 1, metric_type.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_text(stmt, 2, source.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_int64(stmt, 3, start_time);
            sqlite3_bind_int64(stmt, 4, end_time);
        },
        [&stats](sqlite3_stmt* stmt) {
            stats.average = sqlite3_column_double(stmt, 0);
            stats.maximum = sqlite3_column_double(stmt, 1);
            stats.minimum = sqlite3_column_double(stmt, 2);
            stats.count = sqlite3_column_int64(stmt, 3);
        }
    );
    
    // 计算标准差
    if (stats.count > 1) {
        std::string variance_sql = R"(
            SELECT AVG((value - ?) * (value - ?)) 
            FROM metrics 
            WHERE metric_type = ? AND source = ? 
            AND timestamp >= ? AND timestamp <= ?
        )";
        
        ExecuteQuery(variance_sql,
            [&](sqlite3_stmt* stmt) {
                sqlite3_bind_double(stmt, 1, stats.average);
                sqlite3_bind_double(stmt, 2, stats.average);
                sqlite3_bind_text(stmt, 3, metric_type.c_str(), -1, SQLITE_STATIC);
                sqlite3_bind_text(stmt, 4, source.c_str(), -1, SQLITE_STATIC);
                sqlite3_bind_int64(stmt, 5, start_time);
                sqlite3_bind_int64(stmt, 6, end_time);
            },
            [&stats](sqlite3_stmt* stmt) {
                double variance = sqlite3_column_double(stmt, 0);
                stats.std_deviation = std::sqrt(variance);
            }
        );
    }
    
    return stats;
}

DatabaseResult DatabaseManager::InsertEvent(const EventRecord& record) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    if (async_writing_enabled_) {
        // 异步写入
        std::lock_guard<std::mutex> lock(pending_mutex_);
        pending_events_.push(record);
        pending_condition_.notify_one();
        return DatabaseResult::SUCCESS;
    } else {
        // 同步写入
        return InsertEvents({record});
    }
}

DatabaseResult DatabaseManager::InsertEvents(const std::vector<EventRecord>& records) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    if (records.empty()) {
        return DatabaseResult::SUCCESS;
    }
    
    sqlite3* conn = connection_pool_->GetConnection();
    if (!conn) {
        return DatabaseResult::ERROR_BUSY;
    }
    
    DatabaseResult result = DatabaseResult::SUCCESS;
    
    // 开始事务
    result = BeginTransaction(conn);
    if (result != DatabaseResult::SUCCESS) {
        connection_pool_->ReturnConnection(conn);
        return result;
    }
    
    const char* sql = R"(
        INSERT INTO events 
        (timestamp, level, component, event_type, message, context) 
        VALUES (?, ?, ?, ?, ?, ?)
    )";
    
    sqlite3_stmt* stmt;
    int prepare_result = sqlite3_prepare_v2(conn, sql, -1, &stmt, nullptr);
    if (prepare_result != SQLITE_OK) {
        LOG_ERROR(LogComponent::SYSTEM, 
                 "准备插入事件SQL失败: " + std::string(sqlite3_errmsg(conn)));
        RollbackTransaction(conn);
        connection_pool_->ReturnConnection(conn);
        return DatabaseResult::ERROR_PREPARE_FAILED;
    }
    
    for (const auto& record : records) {
        sqlite3_bind_int64(stmt, 1, record.timestamp_ns);
        sqlite3_bind_text(stmt, 2, record.level.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 3, record.component.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 4, record.event_type.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 5, record.message.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_text(stmt, 6, record.context.c_str(), -1, SQLITE_STATIC);
        
        int step_result = sqlite3_step(stmt);
        if (step_result != SQLITE_DONE) {
            LOG_ERROR(LogComponent::SYSTEM, 
                     "执行插入事件SQL失败: " + std::string(sqlite3_errmsg(conn)));
            result = DatabaseResult::ERROR_EXECUTE_FAILED;
            break;
        }
        
        sqlite3_reset(stmt);
    }
    
    sqlite3_finalize(stmt);
    
    if (result == DatabaseResult::SUCCESS) {
        result = CommitTransaction(conn);
    } else {
        RollbackTransaction(conn);
    }
    
    connection_pool_->ReturnConnection(conn);
    return result;
}

std::vector<EventRecord> DatabaseManager::QueryEvents(const DatabaseFilter& filter) {
    std::vector<EventRecord> results;
    
    if (!initialized_.load()) {
        return results;
    }
    
    std::string sql = "SELECT timestamp, level, component, event_type, message, context FROM events ";
    sql += filter.BuildWhereClause();
    sql += " ORDER BY timestamp ";
    sql += filter.ascending ? "ASC" : "DESC";
    sql += " LIMIT " + std::to_string(filter.limit);
    
    ExecuteQuery(sql, nullptr, [&results](sqlite3_stmt* stmt) {
        EventRecord record;
        record.timestamp_ns = sqlite3_column_int64(stmt, 0);
        record.level = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
        record.component = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
        record.event_type = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 3));
        record.message = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 4));
        
        const char* context_text = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 5));
        if (context_text) {
            record.context = context_text;
        }
        
        results.push_back(record);
    });
    
    return results;
}

DatabaseResult DatabaseManager::InsertStateTransition(const StateTransitionRecord& record) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    if (async_writing_enabled_) {
        // 异步写入
        std::lock_guard<std::mutex> lock(pending_mutex_);
        pending_transitions_.push(record);
        pending_condition_.notify_one();
        return DatabaseResult::SUCCESS;
    } else {
        // 同步写入
        const char* sql = R"(
            INSERT INTO state_transitions 
            (timestamp, from_state, to_state, trigger_event, duration_ms, success) 
            VALUES (?, ?, ?, ?, ?, ?)
        )";
        
        return ExecuteSQL(sql, [&record](sqlite3_stmt* stmt) {
            sqlite3_bind_int64(stmt, 1, record.timestamp_ns);
            sqlite3_bind_text(stmt, 2, record.from_state.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_text(stmt, 3, record.to_state.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_text(stmt, 4, record.trigger_event.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_int64(stmt, 5, record.duration_ms);
            sqlite3_bind_int(stmt, 6, record.success ? 1 : 0);
        });
    }
}

std::vector<StateTransitionRecord> DatabaseManager::QueryStateTransitions(const DatabaseFilter& filter) {
    std::vector<StateTransitionRecord> results;
    
    if (!initialized_.load()) {
        return results;
    }
    
    std::string sql = "SELECT timestamp, from_state, to_state, trigger_event, duration_ms, success FROM state_transitions ";
    sql += filter.BuildWhereClause();
    sql += " ORDER BY timestamp ";
    sql += filter.ascending ? "ASC" : "DESC";
    sql += " LIMIT " + std::to_string(filter.limit);
    
    ExecuteQuery(sql, nullptr, [&results](sqlite3_stmt* stmt) {
        StateTransitionRecord record;
        record.timestamp_ns = sqlite3_column_int64(stmt, 0);
        record.from_state = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 1));
        record.to_state = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 2));
        record.trigger_event = reinterpret_cast<const char*>(sqlite3_column_text(stmt, 3));
        record.duration_ms = sqlite3_column_int64(stmt, 4);
        record.success = sqlite3_column_int(stmt, 5) != 0;
        results.push_back(record);
    });
    
    return results;
}

DatabaseResult DatabaseManager::CleanupExpiredData(uint32_t retention_hours, const std::string& table_name) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    uint64_t cutoff_time = GetCurrentTimestampNs() - (static_cast<uint64_t>(retention_hours) * 3600ULL * 1000000000ULL);
    
    std::vector<std::string> tables;
    if (table_name.empty()) {
        tables = {"metrics", "events", "state_transitions"};
    } else {
        tables = {table_name};
    }
    
    for (const auto& table : tables) {
        std::string sql = "DELETE FROM " + table + " WHERE timestamp < ?";
        
        DatabaseResult result = ExecuteSQL(sql, [cutoff_time](sqlite3_stmt* stmt) {
            sqlite3_bind_int64(stmt, 1, cutoff_time);
        });
        
        if (result != DatabaseResult::SUCCESS) {
            LOG_ERROR(LogComponent::SYSTEM, "清理表 " + table + " 的过期数据失败");
            return result;
        }
        
        LOG_INFO(LogComponent::SYSTEM, "清理表 " + table + " 的过期数据成功");
    }
    
    return DatabaseResult::SUCCESS;
}

DatabaseResult DatabaseManager::CompactDatabase() {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    DatabaseResult result = ExecuteSQL("VACUUM;");
    if (result == DatabaseResult::SUCCESS) {
        LOG_INFO(LogComponent::SYSTEM, "数据库压缩完成");
    } else {
        LOG_ERROR(LogComponent::SYSTEM, "数据库压缩失败");
    }
    
    return result;
}

DatabaseResult DatabaseManager::BackupDatabase(const std::string& backup_path) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    sqlite3* conn = connection_pool_->GetConnection();
    if (!conn) {
        return DatabaseResult::ERROR_BUSY;
    }
    
    sqlite3* backup_db;
    int result = sqlite3_open(backup_path.c_str(), &backup_db);
    if (result != SQLITE_OK) {
        LOG_ERROR(LogComponent::SYSTEM, "创建备份数据库失败: " + backup_path);
        connection_pool_->ReturnConnection(conn);
        return DatabaseResult::ERROR_OPEN_FAILED;
    }
    
    sqlite3_backup* backup = sqlite3_backup_init(backup_db, "main", conn, "main");
    if (!backup) {
        LOG_ERROR(LogComponent::SYSTEM, "初始化数据库备份失败");
        sqlite3_close(backup_db);
        connection_pool_->ReturnConnection(conn);
        return DatabaseResult::ERROR_EXECUTE_FAILED;
    }
    
    result = sqlite3_backup_step(backup, -1);
    sqlite3_backup_finish(backup);
    sqlite3_close(backup_db);
    connection_pool_->ReturnConnection(conn);
    
    if (result == SQLITE_DONE) {
        LOG_INFO(LogComponent::SYSTEM, "数据库备份完成: " + backup_path);
        return DatabaseResult::SUCCESS;
    } else {
        LOG_ERROR(LogComponent::SYSTEM, "数据库备份失败");
        return DatabaseResult::ERROR_EXECUTE_FAILED;
    }
}

DatabaseManager::DatabaseStatistics DatabaseManager::GetDatabaseStatistics() {
    DatabaseStatistics stats = {0, 0, 0, 0, 0, 0};
    
    if (!initialized_.load()) {
        return stats;
    }
    
    // 获取各表记录数
    ExecuteQuery("SELECT COUNT(*) FROM metrics", nullptr, [&stats](sqlite3_stmt* stmt) {
        stats.total_metrics = sqlite3_column_int64(stmt, 0);
    });
    
    ExecuteQuery("SELECT COUNT(*) FROM events", nullptr, [&stats](sqlite3_stmt* stmt) {
        stats.total_events = sqlite3_column_int64(stmt, 0);
    });
    
    ExecuteQuery("SELECT COUNT(*) FROM state_transitions", nullptr, [&stats](sqlite3_stmt* stmt) {
        stats.total_state_transitions = sqlite3_column_int64(stmt, 0);
    });
    
    // 获取时间范围
    ExecuteQuery("SELECT MIN(timestamp), MAX(timestamp) FROM metrics", nullptr, [&stats](sqlite3_stmt* stmt) {
        stats.oldest_record_timestamp = sqlite3_column_int64(stmt, 0);
        stats.newest_record_timestamp = sqlite3_column_int64(stmt, 1);
    });
    
    // 获取数据库文件大小
    stats.database_size_bytes = GetDatabaseFileSize();
    
    return stats;
}

void DatabaseManager::SetAsyncWriting(bool enable) {
    async_writing_enabled_ = enable;
    LOG_INFO(LogComponent::SYSTEM, 
             "异步写入模式" + std::string(enable ? "启用" : "禁用"));
}

void DatabaseManager::SetBatchSize(size_t batch_size) {
    batch_size_ = batch_size;
    LOG_INFO(LogComponent::SYSTEM, 
             "批量写入大小设置为: " + std::to_string(batch_size));
}

void DatabaseManager::FlushPendingWrites() {
    if (!initialized_.load()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(pending_mutex_);
    PerformBatchWrite();
}

void DatabaseManager::AsyncWriterThread() {
    LOG_INFO(LogComponent::SYSTEM, "异步写入线程启动");
    
    while (!shutdown_requested_.load()) {
        std::unique_lock<std::mutex> lock(pending_mutex_);
        
        // 等待数据或关闭信号
        pending_condition_.wait(lock, [this] {
            return !pending_metrics_.empty() || 
                   !pending_events_.empty() || 
                   !pending_transitions_.empty() || 
                   shutdown_requested_.load();
        });
        
        if (shutdown_requested_.load()) {
            break;
        }
        
        // 检查是否需要批量写入
        size_t total_pending = pending_metrics_.size() + 
                              pending_events_.size() + 
                              pending_transitions_.size();
        
        if (total_pending >= batch_size_) {
            PerformBatchWrite();
        }
        
        lock.unlock();
        
        // 短暂休眠避免过度占用CPU
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 处理剩余数据
    std::lock_guard<std::mutex> lock(pending_mutex_);
    PerformBatchWrite();
    
    LOG_INFO(LogComponent::SYSTEM, "异步写入线程结束");
}

void DatabaseManager::PerformBatchWrite() {
    // 处理待写入的指标
    if (!pending_metrics_.empty()) {
        std::vector<MetricRecord> metrics;
        while (!pending_metrics_.empty() && metrics.size() < batch_size_) {
            metrics.push_back(pending_metrics_.front());
            pending_metrics_.pop();
        }
        
        if (!metrics.empty()) {
            DatabaseResult result = InsertMetrics(metrics);
            if (result != DatabaseResult::SUCCESS) {
                LOG_ERROR(LogComponent::SYSTEM, 
                         "批量写入指标失败: " + DatabaseResultToString(result));
            }
        }
    }
    
    // 处理待写入的事件
    if (!pending_events_.empty()) {
        std::vector<EventRecord> events;
        while (!pending_events_.empty() && events.size() < batch_size_) {
            events.push_back(pending_events_.front());
            pending_events_.pop();
        }
        
        if (!events.empty()) {
            DatabaseResult result = InsertEvents(events);
            if (result != DatabaseResult::SUCCESS) {
                LOG_ERROR(LogComponent::SYSTEM, 
                         "批量写入事件失败: " + DatabaseResultToString(result));
            }
        }
    }
    
    // 处理待写入的状态转换
    while (!pending_transitions_.empty()) {
        StateTransitionRecord record = pending_transitions_.front();
        pending_transitions_.pop();
        
        const char* sql = R"(
            INSERT INTO state_transitions 
            (timestamp, from_state, to_state, trigger_event, duration_ms, success) 
            VALUES (?, ?, ?, ?, ?, ?)
        )";
        
        DatabaseResult result = ExecuteSQL(sql, [&record](sqlite3_stmt* stmt) {
            sqlite3_bind_int64(stmt, 1, record.timestamp_ns);
            sqlite3_bind_text(stmt, 2, record.from_state.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_text(stmt, 3, record.to_state.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_text(stmt, 4, record.trigger_event.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_int64(stmt, 5, record.duration_ms);
            sqlite3_bind_int(stmt, 6, record.success ? 1 : 0);
        });
        
        if (result != DatabaseResult::SUCCESS) {
            LOG_ERROR(LogComponent::SYSTEM, 
                     "写入状态转换失败: " + DatabaseResultToString(result));
        }
    }
}

DatabaseResult DatabaseManager::ExecuteSQL(const std::string& sql, 
                                          std::function<void(sqlite3_stmt*)> bind_func) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    sqlite3* conn = connection_pool_->GetConnection();
    if (!conn) {
        return DatabaseResult::ERROR_BUSY;
    }
    
    sqlite3_stmt* stmt;
    int prepare_result = sqlite3_prepare_v2(conn, sql.c_str(), -1, &stmt, nullptr);
    if (prepare_result != SQLITE_OK) {
        LOG_ERROR(LogComponent::SYSTEM, 
                 "准备SQL失败: " + std::string(sqlite3_errmsg(conn)));
        connection_pool_->ReturnConnection(conn);
        return DatabaseResult::ERROR_PREPARE_FAILED;
    }
    
    if (bind_func) {
        bind_func(stmt);
    }
    
    int step_result = sqlite3_step(stmt);
    sqlite3_finalize(stmt);
    connection_pool_->ReturnConnection(conn);
    
    if (step_result == SQLITE_DONE || step_result == SQLITE_ROW) {
        return DatabaseResult::SUCCESS;
    } else {
        LOG_ERROR(LogComponent::SYSTEM, 
                 "执行SQL失败: " + std::string(sqlite3_errmsg(conn)));
        return DatabaseResult::ERROR_EXECUTE_FAILED;
    }
}

DatabaseResult DatabaseManager::ExecuteQuery(const std::string& sql,
                                            std::function<void(sqlite3_stmt*)> bind_func,
                                            std::function<void(sqlite3_stmt*)> result_func) {
    if (!initialized_.load()) {
        return DatabaseResult::ERROR_NOT_INITIALIZED;
    }
    
    sqlite3* conn = connection_pool_->GetConnection();
    if (!conn) {
        return DatabaseResult::ERROR_BUSY;
    }
    
    sqlite3_stmt* stmt;
    int prepare_result = sqlite3_prepare_v2(conn, sql.c_str(), -1, &stmt, nullptr);
    if (prepare_result != SQLITE_OK) {
        LOG_ERROR(LogComponent::SYSTEM, 
                 "准备查询SQL失败: " + std::string(sqlite3_errmsg(conn)));
        connection_pool_->ReturnConnection(conn);
        return DatabaseResult::ERROR_PREPARE_FAILED;
    }
    
    if (bind_func) {
        bind_func(stmt);
    }
    
    DatabaseResult result = DatabaseResult::SUCCESS;
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        if (result_func) {
            result_func(stmt);
        }
    }
    
    sqlite3_finalize(stmt);
    connection_pool_->ReturnConnection(conn);
    
    return result;
}

DatabaseResult DatabaseManager::BeginTransaction(sqlite3* conn) {
    int result = sqlite3_exec(conn, "BEGIN TRANSACTION;", nullptr, nullptr, nullptr);
    return (result == SQLITE_OK) ? DatabaseResult::SUCCESS : DatabaseResult::ERROR_TRANSACTION_FAILED;
}

DatabaseResult DatabaseManager::CommitTransaction(sqlite3* conn) {
    int result = sqlite3_exec(conn, "COMMIT;", nullptr, nullptr, nullptr);
    return (result == SQLITE_OK) ? DatabaseResult::SUCCESS : DatabaseResult::ERROR_TRANSACTION_FAILED;
}

DatabaseResult DatabaseManager::RollbackTransaction(sqlite3* conn) {
    int result = sqlite3_exec(conn, "ROLLBACK;", nullptr, nullptr, nullptr);
    return (result == SQLITE_OK) ? DatabaseResult::SUCCESS : DatabaseResult::ERROR_TRANSACTION_FAILED;
}

bool DatabaseManager::TableExists(const std::string& table_name) {
    bool exists = false;
    
    std::string sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?";
    
    ExecuteQuery(sql, 
        [&table_name](sqlite3_stmt* stmt) {
            sqlite3_bind_text(stmt, 1, table_name.c_str(), -1, SQLITE_STATIC);
        },
        [&exists](sqlite3_stmt* stmt) {
            exists = true;
        }
    );
    
    return exists;
}

uint64_t DatabaseManager::GetDatabaseFileSize() {
    try {
        // 这里需要获取数据库文件路径，暂时返回0
        // 实际实现中应该从connection_pool_获取数据库路径
        return 0;
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "获取数据库文件大小失败: " + std::string(e.what()));
        return 0;
    }
}

// 工具函数实现

std::string DatabaseResultToString(DatabaseResult result) {
    switch (result) {
        case DatabaseResult::SUCCESS: return "SUCCESS";
        case DatabaseResult::ERROR_OPEN_FAILED: return "ERROR_OPEN_FAILED";
        case DatabaseResult::ERROR_PREPARE_FAILED: return "ERROR_PREPARE_FAILED";
        case DatabaseResult::ERROR_EXECUTE_FAILED: return "ERROR_EXECUTE_FAILED";
        case DatabaseResult::ERROR_TRANSACTION_FAILED: return "ERROR_TRANSACTION_FAILED";
        case DatabaseResult::ERROR_CONSTRAINT_VIOLATION: return "ERROR_CONSTRAINT_VIOLATION";
        case DatabaseResult::ERROR_DISK_FULL: return "ERROR_DISK_FULL";
        case DatabaseResult::ERROR_CORRUPTED: return "ERROR_CORRUPTED";
        case DatabaseResult::ERROR_BUSY: return "ERROR_BUSY";
        case DatabaseResult::ERROR_INVALID_PARAMETER: return "ERROR_INVALID_PARAMETER";
        case DatabaseResult::ERROR_NOT_INITIALIZED: return "ERROR_NOT_INITIALIZED";
        default: return "UNKNOWN";
    }
}

} // namespace core
} // namespace timing_server