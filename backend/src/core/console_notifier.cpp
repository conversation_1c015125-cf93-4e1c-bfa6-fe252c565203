#include "core/alarm_system.h"
#include "core/logger.h"
#include <iostream>
#include <iomanip>

namespace timing_server {
namespace core {

/**
 * @brief 控制台告警通知器实现
 * 将告警信息输出到控制台
 */
class ConsoleAlarmNotifier : public IAlarmNotifier {
public:
    /**
     * @brief 构造函数
     */
    ConsoleAlarmNotifier() = default;
    
    /**
     * @brief 析构函数
     */
    ~ConsoleAlarmNotifier() override = default;
    
    /**
     * @brief 发送告警通知
     * @param alarm 告警事件
     * @param targets 通知目标列表（控制台通知器忽略此参数）
     * @return 发送是否成功
     */
    bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) override {
        try {
            // 获取颜色代码
            std::string color_code = GetColorCode(alarm.level);
            std::string reset_code = "\033[0m";
            
            // 格式化时间戳
            std::string timestamp_str = TimestampToIsoString(alarm.timestamp_ns);
            
            // 输出告警信息
            std::cout << color_code << "【告警】" << reset_code << " "
                      << timestamp_str << " "
                      << "[" << AlarmLevelToString(alarm.level) << "] "
                      << alarm.title << std::endl;
            
            if (!alarm.description.empty()) {
                std::cout << "  描述: " << alarm.description << std::endl;
            }
            
            if (!alarm.source_component.empty()) {
                std::cout << "  来源: " << alarm.source_component << std::endl;
            }
            
            if (alarm.threshold_value != 0.0 || alarm.current_value != 0.0) {
                std::cout << "  阈值: " << alarm.threshold_value << alarm.unit
                          << ", 当前值: " << alarm.current_value << alarm.unit << std::endl;
            }
            
            if (alarm.occurrence_count > 1) {
                std::cout << "  发生次数: " << alarm.occurrence_count << std::endl;
            }
            
            // 输出上下文信息
            if (!alarm.context.empty()) {
                std::cout << "  上下文信息:" << std::endl;
                for (const auto& [key, value] : alarm.context) {
                    std::cout << "    " << key << ": " << value << std::endl;
                }
            }
            
            std::cout << std::endl;
            std::cout.flush();
            
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "控制台告警通知发送失败: " + std::string(e.what()));
            return false;
        }
    }
    
    /**
     * @brief 获取通知方式
     * @return 通知方式
     */
    NotificationMethod GetNotificationMethod() const override {
        return NotificationMethod::CONSOLE;
    }
    
    /**
     * @brief 检查通知器是否可用
     * @return 是否可用
     */
    bool IsAvailable() const override {
        return true; // 控制台通知器总是可用
    }

private:
    /**
     * @brief 获取告警级别对应的颜色代码
     * @param level 告警级别
     * @return ANSI颜色代码
     */
    std::string GetColorCode(AlarmLevel level) {
        switch (level) {
            case AlarmLevel::INFO:
                return "\033[36m"; // 青色
            case AlarmLevel::WARNING:
                return "\033[33m"; // 黄色
            case AlarmLevel::ERROR:
                return "\033[31m"; // 红色
            case AlarmLevel::CRITICAL:
                return "\033[35m"; // 紫色
            default:
                return "\033[37m"; // 白色
        }
    }
};

/**
 * @brief 日志告警通知器实现
 * 将告警信息记录到日志系统
 */
class LogAlarmNotifier : public IAlarmNotifier {
public:
    /**
     * @brief 构造函数
     */
    LogAlarmNotifier() = default;
    
    /**
     * @brief 析构函数
     */
    ~LogAlarmNotifier() override = default;
    
    /**
     * @brief 发送告警通知
     * @param alarm 告警事件
     * @param targets 通知目标列表（日志通知器忽略此参数）
     * @return 发送是否成功
     */
    bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) override {
        try {
            // 构建日志消息
            std::ostringstream oss;
            oss << "告警触发: [" << AlarmTypeToString(alarm.type) << "] " << alarm.title;
            
            if (!alarm.description.empty()) {
                oss << " - " << alarm.description;
            }
            
            if (alarm.threshold_value != 0.0 || alarm.current_value != 0.0) {
                oss << " (阈值: " << alarm.threshold_value << alarm.unit
                    << ", 当前值: " << alarm.current_value << alarm.unit << ")";
            }
            
            if (alarm.occurrence_count > 1) {
                oss << " [发生次数: " << alarm.occurrence_count << "]";
            }
            
            // 根据告警级别选择日志级别
            LogLevel log_level;
            switch (alarm.level) {
                case AlarmLevel::INFO:
                    log_level = LogLevel::INFO;
                    break;
                case AlarmLevel::WARNING:
                    log_level = LogLevel::WARNING;
                    break;
                case AlarmLevel::ERROR:
                    log_level = LogLevel::ERROR;
                    break;
                case AlarmLevel::CRITICAL:
                    log_level = LogLevel::CRITICAL;
                    break;
                default:
                    log_level = LogLevel::INFO;
                    break;
            }
            
            // 记录日志
            Logger::GetInstance().Log(log_level, LogComponent::SYSTEM, oss.str());
            
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, "日志告警通知发送失败: " + std::string(e.what()));
            return false;
        }
    }
    
    /**
     * @brief 获取通知方式
     * @return 通知方式
     */
    NotificationMethod GetNotificationMethod() const override {
        return NotificationMethod::LOG_ONLY;
    }
    
    /**
     * @brief 检查通知器是否可用
     * @return 是否可用
     */
    bool IsAvailable() const override {
        return true; // 日志通知器总是可用
    }
};

/**
 * @brief 创建控制台告警通知器
 * @return 控制台通知器实例
 */
std::shared_ptr<IAlarmNotifier> CreateConsoleNotifier() {
    return std::make_shared<ConsoleAlarmNotifier>();
}

/**
 * @brief 创建日志告警通知器
 * @return 日志通知器实例
 */
std::shared_ptr<IAlarmNotifier> CreateLogNotifier() {
    return std::make_shared<LogAlarmNotifier>();
}

} // namespace core
} // namespace timing_server