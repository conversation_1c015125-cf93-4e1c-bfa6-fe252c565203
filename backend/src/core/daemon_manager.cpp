#include "core/daemon_manager.h"
#include "core/logger.h"
#include "core/error_handler.h"
#include <fstream>
#include <sstream>
#include <filesystem>
#include <algorithm>
#include <iostream>
#include <signal.h>
#include <sys/wait.h>
#include <sys/stat.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>

namespace timing_server {
namespace core {

/**
 * @brief 守护进程操作结果枚举
 */
enum class DaemonOperationResult {
    SUCCESS,                    // 操作成功
    ALREADY_RUNNING,           // 进程已在运行
    ALREADY_STOPPED,           // 进程已停止
    EXECUTABLE_NOT_FOUND,      // 可执行文件未找到
    PERMISSION_DENIED,         // 权限不足
    FORK_FAILED,              // fork失败
    CONFIG_ERROR,             // 配置错误
    TIMEOUT,                  // 操作超时
    SIGNAL_FAILED,            // 信号发送失败
    UNKNOWN_DAEMON_TYPE,      // 未知守护进程类型
    PROCESS_NOT_RESPONDING,   // 进程无响应
    RESOURCE_UNAVAILABLE,     // 资源不可用
    UNKNOWN_ERROR             // 未知错误
};

/**
 * @brief 守护进程操作结果信息
 */
struct DaemonOperationInfo {
    DaemonOperationResult result;
    std::string message;
    int error_code;
    std::string details;
    
    DaemonOperationInfo(DaemonOperationResult res, const std::string& msg = "", 
                       int err_code = 0, const std::string& det = "")
        : result(res), message(msg), error_code(err_code), details(det) {}
    
    bool IsSuccess() const { return result == DaemonOperationResult::SUCCESS; }
    
    std::string ToString() const {
        std::ostringstream oss;
        oss << "Result: " << static_cast<int>(result);
        if (!message.empty()) oss << ", Message: " << message;
        if (error_code != 0) oss << ", Error Code: " << error_code;
        if (!details.empty()) oss << ", Details: " << details;
        return oss.str();
    }
};

/**
 * @brief 将操作结果转换为字符串
 */
std::string DaemonOperationResultToString(DaemonOperationResult result) {
    switch (result) {
        case DaemonOperationResult::SUCCESS: return "SUCCESS";
        case DaemonOperationResult::ALREADY_RUNNING: return "ALREADY_RUNNING";
        case DaemonOperationResult::ALREADY_STOPPED: return "ALREADY_STOPPED";
        case DaemonOperationResult::EXECUTABLE_NOT_FOUND: return "EXECUTABLE_NOT_FOUND";
        case DaemonOperationResult::PERMISSION_DENIED: return "PERMISSION_DENIED";
        case DaemonOperationResult::FORK_FAILED: return "FORK_FAILED";
        case DaemonOperationResult::CONFIG_ERROR: return "CONFIG_ERROR";
        case DaemonOperationResult::TIMEOUT: return "TIMEOUT";
        case DaemonOperationResult::SIGNAL_FAILED: return "SIGNAL_FAILED";
        case DaemonOperationResult::UNKNOWN_DAEMON_TYPE: return "UNKNOWN_DAEMON_TYPE";
        case DaemonOperationResult::PROCESS_NOT_RESPONDING: return "PROCESS_NOT_RESPONDING";
        case DaemonOperationResult::RESOURCE_UNAVAILABLE: return "RESOURCE_UNAVAILABLE";
        case DaemonOperationResult::UNKNOWN_ERROR: return "UNKNOWN_ERROR";
        default: return "UNKNOWN";
    }
}

// 全局SIGCHLD信号处理器
static DaemonManager* g_daemon_manager_instance = nullptr;

void sigchld_handler(int sig) {
    if (g_daemon_manager_instance) {
        g_daemon_manager_instance->HandleSigchld();
    }
}

DaemonManager::DaemonManager()
    : running_(false), sigchld_installed_(false) {
    
    // 检查是否在测试环境中运行
    const char* test_env = std::getenv("TIMING_SERVER_TEST_MODE");
    if (test_env && std::string(test_env) == "1") {
        // 测试模式：使用临时目录
        std::string temp_dir = std::filesystem::temp_directory_path() / "timing-server-test";
        config_dir_ = temp_dir + "/config";
        log_dir_ = temp_dir + "/log";
        pid_dir_ = temp_dir + "/pid";
    } else {
        // 生产模式：使用系统目录
        config_dir_ = "/etc/timing-server";
        log_dir_ = "/var/log/timing-server";
        pid_dir_ = "/var/run/timing-server";
    }
    
    // 设置全局实例指针用于信号处理
    g_daemon_manager_instance = this;
    
    LOG_INFO(LogComponent::DAEMON_MANAGER, "守护进程管理器已创建");
}

DaemonManager::~DaemonManager() {
    Stop();
    
    // 清除全局实例指针
    if (g_daemon_manager_instance == this) {
        g_daemon_manager_instance = nullptr;
    }
    
    // 恢复默认SIGCHLD处理器
    if (sigchld_installed_) {
        signal(SIGCHLD, SIG_DFL);
    }
}

bool DaemonManager::Initialize() {
    try {
        LOG_INFO(LogComponent::DAEMON_MANAGER, "初始化守护进程管理器...");
        
        // 安装SIGCHLD信号处理器
        if (!InstallSigchldHandler()) {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, "安装SIGCHLD信号处理器失败");
            return false;
        }
        
        // 创建必要的目录
        if (!CreateConfigDirectories()) {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, "创建配置目录失败");
            return false;
        }
        
        // 初始化守护进程配置
        if (!InitializeDaemonConfigs()) {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, "初始化守护进程配置失败");
            return false;
        }
        
        // 检测并解决冲突
        if (!ResolveConflicts()) {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, "解决守护进程冲突失败");
            return false;
        }
        
        LOG_INFO(LogComponent::DAEMON_MANAGER, "守护进程管理器初始化完成");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "守护进程管理器初始化异常: " + std::string(e.what()));
        return false;
    }
}

bool DaemonManager::Start() {
    if (running_.load()) {
        LOG_INFO(LogComponent::DAEMON_MANAGER, "守护进程管理器已在运行");
        return true;
    }
    
    LOG_INFO(LogComponent::DAEMON_MANAGER, "启动守护进程管理器...");
    running_.store(true);
    
    // 启动监控线程
    monitoring_thread_ = std::thread(&DaemonManager::MonitoringThread, this);
    
    // 启动健康检查线程
    health_check_thread_ = std::thread(&DaemonManager::HealthCheckThread, this);
    
    LOG_INFO(LogComponent::DAEMON_MANAGER, "守护进程管理器已启动");
    return true;
}

bool DaemonManager::Stop() {
    if (!running_.load()) {
        return true;
    }
    
    LOG_INFO(LogComponent::DAEMON_MANAGER, "停止守护进程管理器...");
    running_.store(false);
    
    // 通知线程退出
    monitoring_cv_.notify_all();
    health_check_cv_.notify_all();
    
    // 等待线程结束
    if (monitoring_thread_.joinable()) {
        monitoring_thread_.join();
    }
    
    if (health_check_thread_.joinable()) {
        health_check_thread_.join();
    }
    
    // 停止所有守护进程
    for (auto type : {DaemonType::PTP4L, DaemonType::CHRONY, DaemonType::TS2PHC}) {
        StopDaemon(type);
    }
    
    LOG_INFO(LogComponent::DAEMON_MANAGER, "守护进程管理器已停止");
    return true;
}

bool DaemonManager::InstallSigchldHandler() {
    struct sigaction sa;
    sa.sa_handler = sigchld_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = SA_RESTART | SA_NOCLDSTOP;
    
    if (sigaction(SIGCHLD, &sa, nullptr) == -1) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "安装SIGCHLD信号处理器失败: " + std::string(strerror(errno)));
        return false;
    }
    
    sigchld_installed_ = true;
    LOG_INFO(LogComponent::DAEMON_MANAGER, "SIGCHLD信号处理器安装成功");
    return true;
}

void DaemonManager::HandleSigchld() {
    pid_t pid;
    int status;
    
    // 处理所有已退出的子进程
    while ((pid = waitpid(-1, &status, WNOHANG)) > 0) {
        ProcessChildExit(pid, status);
    }
}

void DaemonManager::ProcessChildExit(pid_t pid, int status) {
    std::lock_guard<std::mutex> lock(daemons_mutex_);
    
    // 查找对应的守护进程
    DaemonType crashed_daemon = DaemonType::PTP4L; // 默认值
    bool found = false;
    
    for (auto& pair : daemons_) {
        if (pair.second.process_id == pid) {
            crashed_daemon = pair.first;
            found = true;
            break;
        }
    }
    
    if (!found) {
        LOG_WARNING(LogComponent::DAEMON_MANAGER, 
                   "收到未知进程的SIGCHLD信号，PID: " + std::to_string(pid));
        return;
    }
    
    auto& daemon = daemons_[crashed_daemon];
    daemon.process_id = 0; // 清除PID
    
    // 分析退出状态
    std::string exit_info;
    if (WIFEXITED(status)) {
        int exit_code = WEXITSTATUS(status);
        exit_info = "正常退出，退出码: " + std::to_string(exit_code);
        
        if (exit_code == 0) {
            daemon.status = DaemonStatus::STOPPED;
            LOG_INFO(LogComponent::DAEMON_MANAGER, 
                    DaemonTypeToString(crashed_daemon) + " " + exit_info);
        } else {
            daemon.status = DaemonStatus::ERROR;
            daemon.last_error = "进程异常退出，退出码: " + std::to_string(exit_code);
            LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                     DaemonTypeToString(crashed_daemon) + " " + exit_info);
            
            // 报告错误
            REPORT_ERROR_WITH_DETAILS(ErrorType::DAEMON_CRASH, ErrorSeverity::HIGH,
                                     LogComponent::DAEMON_MANAGER,
                                     DaemonTypeToString(crashed_daemon) + "守护进程崩溃",
                                     daemon.last_error);
        }
    } else if (WIFSIGNALED(status)) {
        int signal_num = WTERMSIG(status);
        exit_info = "被信号终止，信号: " + std::to_string(signal_num) + 
                   " (" + strsignal(signal_num) + ")";
        
        daemon.status = DaemonStatus::CRASHED;
        daemon.last_error = exit_info;
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 DaemonTypeToString(crashed_daemon) + " " + exit_info);
        
        // 报告错误
        REPORT_ERROR_WITH_DETAILS(ErrorType::DAEMON_CRASH, ErrorSeverity::CRITICAL,
                                 LogComponent::DAEMON_MANAGER,
                                 DaemonTypeToString(crashed_daemon) + "守护进程被信号终止",
                                 daemon.last_error);
    }
    
    // 通知状态变化
    NotifyStatusChange(crashed_daemon, DaemonStatus::RUNNING, daemon.status);
    
    // 如果启用了自动重启且重启次数未超限
    if (daemon.config.auto_restart && 
        daemon.restart_count < daemon.config.max_restart_attempts) {
        
        LOG_INFO(LogComponent::DAEMON_MANAGER, 
                "准备自动重启 " + DaemonTypeToString(crashed_daemon) + 
                "，重启次数: " + std::to_string(daemon.restart_count + 1));
        
        // 在单独的线程中执行重启，避免在信号处理器中执行复杂操作
        std::thread restart_thread([this, crashed_daemon]() {
            std::this_thread::sleep_for(
                std::chrono::milliseconds(daemons_[crashed_daemon].config.restart_delay_ms));
            RestartDaemon(crashed_daemon);
        });
        restart_thread.detach();
    } else {
        LOG_WARNING(LogComponent::DAEMON_MANAGER, 
                   DaemonTypeToString(crashed_daemon) + 
                   " 已达到最大重启次数或未启用自动重启");
    }
}

bool DaemonManager::StartDaemon(DaemonType type) {
    std::lock_guard<std::mutex> lock(daemons_mutex_);
    
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        std::cerr << "未知的守护进程类型: " << DaemonTypeToString(type) << std::endl;
        return false;
    }
    
    DaemonInfo& daemon = it->second;
    
    // 检查当前状态
    if (daemon.status == DaemonStatus::RUNNING || daemon.status == DaemonStatus::STARTING) {
        std::cout << "守护进程已在运行: " << DaemonTypeToString(type) << std::endl;
        return true;
    }
    
    std::cout << "启动守护进程: " << DaemonTypeToString(type) << std::endl;
    
    // 更新状态为启动中
    DaemonStatus old_status = daemon.status;
    daemon.status = DaemonStatus::STARTING;
    NotifyStatusChange(type, old_status, DaemonStatus::STARTING);
    
    // 启动进程
    if (StartDaemonProcessWithGroup(type)) {
        daemon.status = DaemonStatus::RUNNING;
        daemon.start_time_ns = GetCurrentTimestampNs();
        daemon.restart_count = 0;
        daemon.last_error.clear();
        
        NotifyStatusChange(type, DaemonStatus::STARTING, DaemonStatus::RUNNING);
        std::cout << "守护进程启动成功: " << DaemonTypeToString(type) << std::endl;
        return true;
    } else {
        daemon.status = DaemonStatus::ERROR;
        daemon.last_error = "启动进程失败";
        
        NotifyStatusChange(type, DaemonStatus::STARTING, DaemonStatus::ERROR);
        std::cerr << "守护进程启动失败: " << DaemonTypeToString(type) << std::endl;
        return false;
    }
}

bool DaemonManager::StopDaemon(DaemonType type) {
    std::lock_guard<std::mutex> lock(daemons_mutex_);
    
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        std::cerr << "未知的守护进程类型: " << DaemonTypeToString(type) << std::endl;
        return false;
    }
    
    DaemonInfo& daemon = it->second;
    
    // 检查当前状态
    if (daemon.status == DaemonStatus::STOPPED || daemon.status == DaemonStatus::STOPPING) {
        std::cout << "守护进程已停止: " << DaemonTypeToString(type) << std::endl;
        return true;
    }
    
    std::cout << "停止守护进程: " << DaemonTypeToString(type) << std::endl;
    
    // 更新状态为停止中
    DaemonStatus old_status = daemon.status;
    daemon.status = DaemonStatus::STOPPING;
    NotifyStatusChange(type, old_status, DaemonStatus::STOPPING);
    
    // 停止进程
    if (StopDaemonProcessGroup(type)) {
        daemon.status = DaemonStatus::STOPPED;
        daemon.process_id = 0;
        
        NotifyStatusChange(type, DaemonStatus::STOPPING, DaemonStatus::STOPPED);
        std::cout << "守护进程停止成功: " << DaemonTypeToString(type) << std::endl;
        return true;
    } else {
        daemon.status = DaemonStatus::ERROR;
        daemon.last_error = "停止进程失败";
        
        NotifyStatusChange(type, DaemonStatus::STOPPING, DaemonStatus::ERROR);
        std::cerr << "守护进程停止失败: " << DaemonTypeToString(type) << std::endl;
        return false;
    }
}

bool DaemonManager::RestartDaemon(DaemonType type) {
    std::cout << "重启守护进程: " << DaemonTypeToString(type) << std::endl;
    
    // 先停止
    if (!StopDaemon(type)) {
        std::cerr << "停止守护进程失败，无法重启: " << DaemonTypeToString(type) << std::endl;
        return false;
    }
    
    // 等待一段时间
    std::this_thread::sleep_for(std::chrono::milliseconds(DEFAULT_RESTART_DELAY_MS));
    
    // 再启动
    if (!StartDaemon(type)) {
        std::cerr << "启动守护进程失败，重启失败: " << DaemonTypeToString(type) << std::endl;
        return false;
    }
    
    // 增加重启计数
    {
        std::lock_guard<std::mutex> lock(daemons_mutex_);
        auto it = daemons_.find(type);
        if (it != daemons_.end()) {
            it->second.restart_count++;
        }
    }
    
    std::cout << "守护进程重启成功: " << DaemonTypeToString(type) << std::endl;
    return true;
}

DaemonInfo DaemonManager::GetDaemonInfo(DaemonType type) {
    std::lock_guard<std::mutex> lock(daemons_mutex_);
    
    auto it = daemons_.find(type);
    if (it != daemons_.end()) {
        return it->second;
    }
    
    // 返回默认信息
    DaemonInfo info;
    info.type = type;
    info.status = DaemonStatus::STOPPED;
    info.process_id = 0;
    return info;
}

std::vector<DaemonInfo> DaemonManager::GetAllDaemonInfo() {
    std::lock_guard<std::mutex> lock(daemons_mutex_);
    
    std::vector<DaemonInfo> result;
    for (const auto& pair : daemons_) {
        result.push_back(pair.second);
    }
    
    return result;
}

bool DaemonManager::UpdatePtpConfig(const PtpConfig& config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    std::cout << "更新PTP配置..." << std::endl;
    
    // 生成配置文件
    if (!GeneratePtp4lConfigFile(config)) {
        std::cerr << "生成PTP配置文件失败" << std::endl;
        return false;
    }
    
    // 保存配置
    ptp_configs_[DaemonType::PTP4L] = config;
    
    // 如果守护进程正在运行，需要重启以应用新配置
    if (GetDaemonInfo(DaemonType::PTP4L).status == DaemonStatus::RUNNING) {
        std::cout << "重启PTP4L以应用新配置..." << std::endl;
        if (!RestartDaemon(DaemonType::PTP4L)) {
            std::cerr << "重启PTP4L失败" << std::endl;
            return false;
        }
    }
    
    std::cout << "PTP配置更新成功" << std::endl;
    return true;
}

bool DaemonManager::UpdateNtpConfig(const NtpConfig& config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    std::cout << "更新NTP配置..." << std::endl;
    
    // 生成配置文件
    if (!GenerateChronyConfigFile(config)) {
        std::cerr << "生成NTP配置文件失败" << std::endl;
        return false;
    }
    
    // 保存配置
    ntp_configs_[DaemonType::CHRONY] = config;
    
    // 如果守护进程正在运行，需要重启以应用新配置
    if (GetDaemonInfo(DaemonType::CHRONY).status == DaemonStatus::RUNNING) {
        std::cout << "重启Chrony以应用新配置..." << std::endl;
        if (!RestartDaemon(DaemonType::CHRONY)) {
            std::cerr << "重启Chrony失败" << std::endl;
            return false;
        }
    }
    
    std::cout << "NTP配置更新成功" << std::endl;
    return true;
}

bool DaemonManager::UpdateTs2phcConfig(const Ts2phcConfig& config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    std::cout << "更新TS2PHC配置..." << std::endl;
    
    // 生成配置文件
    if (!GenerateTs2phcConfigFile(config)) {
        std::cerr << "生成TS2PHC配置文件失败" << std::endl;
        return false;
    }
    
    // 保存配置
    ts2phc_configs_[DaemonType::TS2PHC] = config;
    
    // 如果守护进程正在运行，需要重启以应用新配置
    if (GetDaemonInfo(DaemonType::TS2PHC).status == DaemonStatus::RUNNING) {
        std::cout << "重启TS2PHC以应用新配置..." << std::endl;
        if (!RestartDaemon(DaemonType::TS2PHC)) {
            std::cerr << "重启TS2PHC失败" << std::endl;
            return false;
        }
    }
    
    std::cout << "TS2PHC配置更新成功" << std::endl;
    return true;
}

bool DaemonManager::ResolveConflicts() {
    std::cout << "检测并解决守护进程冲突..." << std::endl;
    
    // 检查端口冲突
    if (!CheckPortConflicts()) {
        std::cerr << "端口冲突检查失败" << std::endl;
        return false;
    }
    
    // 检查资源冲突
    if (!CheckResourceConflicts()) {
        std::cerr << "资源冲突检查失败" << std::endl;
        return false;
    }
    
    std::cout << "守护进程冲突检查完成" << std::endl;
    return true;
}

void DaemonManager::SetStatusChangeCallback(std::function<void(DaemonType, DaemonStatus, DaemonStatus)> callback) {
    status_change_callback_ = callback;
}

bool DaemonManager::UpdateConfigForClockState(ClockState clock_state, TimeSource active_source) {
    std::cout << "根据时钟状态更新配置: " << ClockStateToString(clock_state) 
              << ", 活跃源: " << TimeSourceToString(active_source) << std::endl;
    
    // 根据时钟状态和时间源动态调整PTP配置
    PtpConfig ptp_config = GenerateOptimalPtpConfig(clock_state, active_source);
    
    // 根据时钟状态设置clockClass和clockAccuracy
    switch (clock_state) {
        case ClockState::LOCKED:
            if (active_source == TimeSource::GNSS) {
                ptp_config.clock_class = 6;        // GPS锁定 - 最高精度
                ptp_config.clock_accuracy = 0x21;  // 25ns
                ptp_config.priority1 = 1;          // 最高优先级
                std::cout << "配置为GPS锁定特级主时钟 (clockClass=6)" << std::endl;
            } else if (active_source == TimeSource::RUBIDIUM) {
                ptp_config.clock_class = 7;        // 铷钟锁定
                ptp_config.clock_accuracy = 0x22;  // 100ns
                ptp_config.priority1 = 2;          // 次高优先级
                std::cout << "配置为铷钟锁定主时钟 (clockClass=7)" << std::endl;
            } else {
                ptp_config.clock_class = 13;       // 其他高质量源
                ptp_config.clock_accuracy = 0x25;  // 1μs
                ptp_config.priority1 = 10;         // 较低优先级
                std::cout << "配置为其他源锁定主时钟 (clockClass=13)" << std::endl;
            }
            break;
            
        case ClockState::HOLDOVER:
            ptp_config.clock_class = 7;            // 守时模式
            ptp_config.clock_accuracy = 0x25;      // 1μs
            ptp_config.priority1 = 50;             // 降低优先级
            std::cout << "配置为守时模式主时钟 (clockClass=7)" << std::endl;
            break;
            
        case ClockState::DISCIPLINING:
            ptp_config.clock_class = 52;           // 驯服中
            ptp_config.clock_accuracy = 0x31;      // 未知精度
            ptp_config.priority1 = 100;            // 低优先级
            std::cout << "配置为驯服中主时钟 (clockClass=52)" << std::endl;
            break;
            
        case ClockState::FREE_RUN:
        default:
            ptp_config.clock_class = 248;          // 自由运行
            ptp_config.clock_accuracy = 0xFE;      // 未知精度
            ptp_config.priority1 = 255;            // 最低优先级
            std::cout << "配置为自由运行主时钟 (clockClass=248)" << std::endl;
            break;
    }
    
    // 根据精度调整offset_scaled_log_variance
    if (ptp_config.clock_class <= 7) {
        ptp_config.offset_scaled_log_variance = 0x4E5D;  // 高精度
    } else if (ptp_config.clock_class <= 52) {
        ptp_config.offset_scaled_log_variance = 0x5E5D;  // 中等精度
    } else {
        ptp_config.offset_scaled_log_variance = 0xFFFF;  // 低精度
    }
    
    // 更新PTP配置
    if (!UpdatePtpConfig(ptp_config)) {
        std::cerr << "更新PTP配置失败" << std::endl;
        return false;
    }
    
    // 根据时钟状态调整NTP配置
    NtpConfig ntp_config = GenerateOptimalNtpConfig(clock_state, active_source);
    
    // 更新NTP配置
    if (!UpdateNtpConfig(ntp_config)) {
        std::cerr << "更新NTP配置失败" << std::endl;
        return false;
    }
    
    // 配置TS2PHC
    Ts2phcConfig ts2phc_config = GenerateOptimalTs2phcConfig(clock_state, active_source);
    
    // 更新TS2PHC配置
    if (!UpdateTs2phcConfig(ts2phc_config)) {
        std::cerr << "更新TS2PHC配置失败" << std::endl;
        return false;
    }
    
    std::cout << "配置更新完成" << std::endl;
    return true;
}

// 私有方法实现

bool DaemonManager::InitializeDaemonConfigs() {
    std::cout << "初始化守护进程配置..." << std::endl;
    
    // 初始化PTP4L配置
    DaemonInfo ptp4l_info;
    ptp4l_info.type = DaemonType::PTP4L;
    ptp4l_info.status = DaemonStatus::STOPPED;
    ptp4l_info.process_id = 0;
    ptp4l_info.start_time_ns = 0;
    ptp4l_info.last_health_check_ns = 0;
    ptp4l_info.restart_count = 0;
    ptp4l_info.config = CreatePtp4lConfig();
    daemons_[DaemonType::PTP4L] = ptp4l_info;
    
    // 初始化Chrony配置
    DaemonInfo chrony_info;
    chrony_info.type = DaemonType::CHRONY;
    chrony_info.status = DaemonStatus::STOPPED;
    chrony_info.process_id = 0;
    chrony_info.start_time_ns = 0;
    chrony_info.last_health_check_ns = 0;
    chrony_info.restart_count = 0;
    chrony_info.config = CreateChronyConfig();
    daemons_[DaemonType::CHRONY] = chrony_info;
    
    // 初始化TS2PHC配置
    DaemonInfo ts2phc_info;
    ts2phc_info.type = DaemonType::TS2PHC;
    ts2phc_info.status = DaemonStatus::STOPPED;
    ts2phc_info.process_id = 0;
    ts2phc_info.start_time_ns = 0;
    ts2phc_info.last_health_check_ns = 0;
    ts2phc_info.restart_count = 0;
    ts2phc_info.config = CreateTs2phcConfig();
    daemons_[DaemonType::TS2PHC] = ts2phc_info;
    
    std::cout << "守护进程配置初始化完成" << std::endl;
    return true;
}

bool DaemonManager::CreateConfigDirectories() {
    try {
        // 创建配置目录
        std::filesystem::create_directories(config_dir_);
        std::filesystem::create_directories(log_dir_);
        std::filesystem::create_directories(pid_dir_);
        
        // 设置权限
        chmod(config_dir_.c_str(), 0755);
        chmod(log_dir_.c_str(), 0755);
        chmod(pid_dir_.c_str(), 0755);
        
        std::cout << "配置目录创建成功:" << std::endl;
        std::cout << "  配置目录: " << config_dir_ << std::endl;
        std::cout << "  日志目录: " << log_dir_ << std::endl;
        std::cout << "  PID目录: " << pid_dir_ << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "创建配置目录失败: " << e.what() << std::endl;
        return false;
    }
}

DaemonConfig DaemonManager::CreatePtp4lConfig() {
    DaemonConfig config;
    config.executable_path = "/usr/sbin/ptp4l";
    config.arguments = {"-f", config_dir_ + "/ptp4l.conf", "-m"};
    config.config_file_path = config_dir_ + "/ptp4l.conf";
    config.working_directory = "/";
    config.log_file_path = log_dir_ + "/ptp4l.log";
    config.auto_restart = true;
    config.restart_delay_ms = DEFAULT_RESTART_DELAY_MS;
    config.max_restart_attempts = MAX_RESTART_ATTEMPTS;
    config.health_check_interval_ms = HEALTH_CHECK_INTERVAL_MS;
    
    return config;
}

DaemonConfig DaemonManager::CreateChronyConfig() {
    DaemonConfig config;
    config.executable_path = "/usr/sbin/chronyd";
    config.arguments = {"-f", config_dir_ + "/chrony.conf", "-d"};
    config.config_file_path = config_dir_ + "/chrony.conf";
    config.working_directory = "/";
    config.log_file_path = log_dir_ + "/chrony.log";
    config.auto_restart = true;
    config.restart_delay_ms = DEFAULT_RESTART_DELAY_MS;
    config.max_restart_attempts = MAX_RESTART_ATTEMPTS;
    config.health_check_interval_ms = HEALTH_CHECK_INTERVAL_MS;
    
    return config;
}

DaemonConfig DaemonManager::CreateTs2phcConfig() {
    DaemonConfig config;
    config.executable_path = "/usr/sbin/ts2phc";
    config.arguments = {"-f", config_dir_ + "/ts2phc.conf", "-m"};
    config.config_file_path = config_dir_ + "/ts2phc.conf";
    config.working_directory = "/";
    config.log_file_path = log_dir_ + "/ts2phc.log";
    config.auto_restart = true;
    config.restart_delay_ms = DEFAULT_RESTART_DELAY_MS;
    config.max_restart_attempts = MAX_RESTART_ATTEMPTS;
    config.health_check_interval_ms = HEALTH_CHECK_INTERVAL_MS;
    
    return config;
}

bool DaemonManager::GeneratePtp4lConfigFile(const PtpConfig& config) {
    try {
        std::ofstream file(config_dir_ + "/ptp4l.conf");
        if (!file.is_open()) {
            std::cerr << "无法创建PTP4L配置文件" << std::endl;
            return false;
        }
        
        file << "# PTP4L配置文件 - 由守护进程管理器自动生成" << std::endl;
        file << "# 生成时间: " << TimestampToIsoString(GetCurrentTimestampNs()) << std::endl;
        file << std::endl;
        
        file << "[global]" << std::endl;
        file << "# 硬件时间戳配置" << std::endl;
        file << "time_stamping hardware" << std::endl;
        file << "tx_timestamp_timeout 50" << std::endl;
        file << "check_fup_sync 0" << std::endl;
        file << std::endl;
        
        file << "# 特级主时钟优先级配置" << std::endl;
        file << "priority1 " << static_cast<int>(config.priority1) << std::endl;
        file << "priority2 " << static_cast<int>(config.priority2) << std::endl;
        file << "clockClass " << static_cast<int>(config.clock_class) << std::endl;
        file << "clockAccuracy 0x" << std::hex << static_cast<int>(config.clock_accuracy) << std::dec << std::endl;
        file << "offsetScaledLogVariance 0x" << std::hex << config.offset_scaled_log_variance << std::dec << std::endl;
        file << std::endl;
        
        file << "# 主时钟模式配置" << std::endl;
        file << "masterOnly " << (config.master_only ? "1" : "0") << std::endl;
        file << "slaveOnly " << (config.slave_only ? "1" : "0") << std::endl;
        file << "twoStepFlag " << (config.two_step_flag ? "1" : "0") << std::endl;
        file << std::endl;
        
        file << "# 域和身份配置" << std::endl;
        file << "domainNumber " << static_cast<int>(config.domain_number) << std::endl;
        file << "clockIdentity 000000.FFFE.000001" << std::endl;
        file << std::endl;
        
        file << "# 报文间隔配置（log2秒）" << std::endl;
        file << "logAnnounceInterval " << static_cast<int>(config.announce_interval) << std::endl;
        file << "logSyncInterval " << static_cast<int>(config.sync_interval) << std::endl;
        file << "logMinDelayReqInterval " << static_cast<int>(config.delay_req_interval) << std::endl;
        file << std::endl;
        
        file << "# 超时配置" << std::endl;
        file << "announceReceiptTimeout " << static_cast<int>(config.announce_receipt_timeout) << std::endl;
        file << "syncReceiptTimeout " << static_cast<int>(config.sync_receipt_timeout) << std::endl;
        file << std::endl;
        
        file << "# 性能优化" << std::endl;
        file << "assume_two_step 0" << std::endl;
        file << "check_fup_sync 0" << std::endl;
        file << "clock_servo pi" << std::endl;
        file << "pi_proportional_const 0.0" << std::endl;
        file << "pi_integral_const 0.0" << std::endl;
        file << std::endl;
        
        file << "# 日志配置" << std::endl;
        file << "logging_level " << config.logging_level << std::endl;
        file << "verbose 1" << std::endl;
        file << "use_syslog " << (config.use_syslog ? "1" : "0") << std::endl;
        file << std::endl;
        
        file << "# 网络接口配置" << std::endl;
        file << "[" << config.network_interface << "]" << std::endl;
        file << "network_transport " << config.network_transport << std::endl;
        file << "delay_mechanism " << config.delay_mechanism << std::endl;
        
        file.close();
        
        std::cout << "PTP4L配置文件生成成功: " << config_dir_ + "/ptp4l.conf" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "生成PTP4L配置文件异常: " << e.what() << std::endl;
        return false;
    }
}

bool DaemonManager::GenerateChronyConfigFile(const NtpConfig& config) {
    try {
        std::ofstream file(config_dir_ + "/chrony.conf");
        if (!file.is_open()) {
            std::cerr << "无法创建Chrony配置文件" << std::endl;
            return false;
        }
        
        file << "# Chrony配置文件 - 由守护进程管理器自动生成" << std::endl;
        file << "# 生成时间: " << TimestampToIsoString(GetCurrentTimestampNs()) << std::endl;
        file << std::endl;
        
        file << "# PHC作为主要参考源" << std::endl;
        file << "refclock PHC " << config.phc_device << " poll 0 dpoll 0 offset 0 prefer refid PTP" << std::endl;
        file << std::endl;
        
        file << "# GNSS作为监控参考源" << std::endl;
        file << "refclock SHM " << config.gnss_shm_segment << " offset " << config.gnss_offset 
              << " delay 0.2 refid GPS noselect" << std::endl;
        file << std::endl;
        
        file << "# Stratum 1服务配置" << std::endl;
        file << "local stratum " << static_cast<int>(config.stratum) << std::endl;
        file << "stratumweight 0" << std::endl;
        file << std::endl;
        
        file << "# 网络服务配置" << std::endl;
        file << "port " << config.port << std::endl;
        file << "bindaddress " << config.bind_address << std::endl;
        
        for (const auto& network : config.allowed_networks) {
            file << "allow " << network << std::endl;
        }
        
        for (const auto& network : config.denied_networks) {
            file << "deny " << network << std::endl;
        }
        
        if (config.allowed_networks.empty() && config.denied_networks.empty()) {
            file << "deny all" << std::endl;
        }
        file << std::endl;
        
        file << "# 客户端限制" << std::endl;
        file << "clientloglimit " << config.client_log_limit << std::endl;
        file << "ratelimit interval " << config.rate_limit_interval 
              << " burst " << config.rate_limit_burst 
              << " leak " << config.rate_limit_leak << std::endl;
        file << std::endl;
        
        file << "# 性能优化" << std::endl;
        if (config.lock_all) {
            file << "lock_all" << std::endl;
        }
        file << "sched_priority " << config.sched_priority << std::endl;
        file << "maxupdateskew " << config.max_update_skew << std::endl;
        file << "makestep " << config.make_step_threshold << " " << config.make_step_limit << std::endl;
        file << std::endl;
        
        file << "# 日志配置" << std::endl;
        file << "logdir " << config.log_dir << std::endl;
        
        std::vector<std::string> log_types;
        if (config.log_tracking) log_types.push_back("tracking");
        if (config.log_measurements) log_types.push_back("measurements");
        if (config.log_statistics) log_types.push_back("statistics");
        
        if (!log_types.empty()) {
            file << "log";
            for (const auto& type : log_types) {
                file << " " << type;
            }
            file << std::endl;
        }
        
        file << "logchange " << config.log_change_threshold << std::endl;
        
        file.close();
        
        std::cout << "Chrony配置文件生成成功: " << config_dir_ + "/chrony.conf" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "生成Chrony配置文件异常: " << e.what() << std::endl;
        return false;
    }
}

bool DaemonManager::GenerateTs2phcConfigFile(const Ts2phcConfig& config) {
    try {
        std::ofstream file(config_dir_ + "/ts2phc.conf");
        if (!file.is_open()) {
            std::cerr << "无法创建TS2PHC配置文件" << std::endl;
            return false;
        }
        
        file << "# TS2PHC配置文件 - 由守护进程管理器自动生成" << std::endl;
        file << "# 生成时间: " << TimestampToIsoString(GetCurrentTimestampNs()) << std::endl;
        file << std::endl;
        
        file << "[global]" << std::endl;
        file << "use_syslog " << (config.use_syslog ? "1" : "0") << std::endl;
        file << "verbose " << (config.verbose ? "1" : "0") << std::endl;
        file << "logging_level " << config.logging_level << std::endl;
        file << "ts2phc.pulsewidth " << config.pulse_width << std::endl;
        file << std::endl;
        
        file << "# GNSS 1PPS输入配置" << std::endl;
        file << "[" << config.pps_device << "]" << std::endl;
        file << "ts2phc.extts_polarity " << config.extts_polarity << std::endl;
        file << "ts2phc.extts_correction " << config.extts_correction << std::endl;
        file << std::endl;
        
        file << "# PHC输出配置" << std::endl;
        file << "[" << config.network_interface << "]" << std::endl;
        file << "ts2phc.pin_index " << config.pin_index << std::endl;
        
        if (config.dpll_pin > 0) {
            file << "# Intel E810网卡DPLL配置" << std::endl;
            file << "ts2phc.dpll_pin " << config.dpll_pin << std::endl;
            file << "ts2phc.dpll_state " << config.dpll_state << std::endl;
        }
        
        file.close();
        
        std::cout << "TS2PHC配置文件生成成功: " << config_dir_ + "/ts2phc.conf" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "生成TS2PHC配置文件异常: " << e.what() << std::endl;
        return false;
    }
}

bool DaemonManager::StartDaemonProcessWithGroup(DaemonType type) {
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "未找到守护进程类型: " + DaemonTypeToString(type));
        return false;
    }
    
    const DaemonConfig& config = it->second.config;
    
    // 检查可执行文件是否存在
    if (!std::filesystem::exists(config.executable_path)) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "可执行文件不存在: " + config.executable_path);
        REPORT_ERROR_WITH_DETAILS(ErrorType::DAEMON_CRASH, ErrorSeverity::HIGH,
                                 LogComponent::DAEMON_MANAGER,
                                 "守护进程可执行文件不存在",
                                 "文件路径: " + config.executable_path);
        return false;
    }
    
    // 创建进程
    pid_t pid = fork();
    if (pid == -1) {
        std::string error_msg = "创建进程失败: " + std::string(strerror(errno));
        LOG_ERROR(LogComponent::DAEMON_MANAGER, error_msg);
        REPORT_ERROR_WITH_DETAILS(ErrorType::DAEMON_CRASH, ErrorSeverity::CRITICAL,
                                 LogComponent::DAEMON_MANAGER,
                                 "fork系统调用失败",
                                 error_msg);
        return false;
    }
    
    if (pid == 0) {
        // 子进程
        
        // 创建新的进程组和会话
        if (setsid() == -1) {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                     "setsid失败: " + std::string(strerror(errno)));
            _exit(1);
        }
        
        // 重定向标准输出和错误输出到日志文件
        int log_fd = open(config.log_file_path.c_str(), O_CREAT | O_WRONLY | O_APPEND, 0644);
        if (log_fd != -1) {
            dup2(log_fd, STDOUT_FILENO);
            dup2(log_fd, STDERR_FILENO);
            close(log_fd);
        } else {
            LOG_WARNING(LogComponent::DAEMON_MANAGER, 
                       "无法打开日志文件: " + config.log_file_path);
        }
        
        // 设置工作目录
        if (!config.working_directory.empty()) {
            if (chdir(config.working_directory.c_str()) == -1) {
                LOG_WARNING(LogComponent::DAEMON_MANAGER, 
                           "设置工作目录失败: " + config.working_directory);
            }
        }
        
        // 设置环境变量
        for (const auto& env : config.environment) {
            setenv(env.first.c_str(), env.second.c_str(), 1);
        }
        
        // 准备参数
        std::vector<char*> args;
        args.push_back(const_cast<char*>(config.executable_path.c_str()));
        
        for (const auto& arg : config.arguments) {
            args.push_back(const_cast<char*>(arg.c_str()));
        }
        
        args.push_back(nullptr);
        
        // 执行程序
        execv(config.executable_path.c_str(), args.data());
        
        // 如果到达这里，说明execv失败
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "执行程序失败: " + std::string(strerror(errno)));
        _exit(1);
    } else {
        // 父进程
        it->second.process_id = pid;
        
        LOG_INFO(LogComponent::DAEMON_MANAGER, 
                DaemonTypeToString(type) + " 进程已创建，PID: " + std::to_string(pid));
        
        // 等待一小段时间确保进程启动
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 检查进程是否仍在运行
        if (kill(pid, 0) == 0) {
            LOG_INFO(LogComponent::DAEMON_MANAGER, 
                    "守护进程启动成功: " + DaemonTypeToString(type) + 
                    " (PID: " + std::to_string(pid) + ")");
            return true;
        } else {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                     "守护进程启动后立即退出: " + DaemonTypeToString(type));
            it->second.process_id = 0;
            REPORT_ERROR_WITH_DETAILS(ErrorType::DAEMON_CRASH, ErrorSeverity::HIGH,
                                     LogComponent::DAEMON_MANAGER,
                                     "守护进程启动失败",
                                     DaemonTypeToString(type) + " 进程启动后立即退出");
            return false;
        }
    }
}

bool DaemonManager::StopDaemonProcessGroup(DaemonType type) {
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "未找到守护进程类型: " + DaemonTypeToString(type));
        return false;
    }
    
    pid_t pid = it->second.process_id;
    if (pid <= 0) {
        LOG_INFO(LogComponent::DAEMON_MANAGER, 
                DaemonTypeToString(type) + " 进程已经停止");
        return true; // 进程已经停止
    }
    
    LOG_INFO(LogComponent::DAEMON_MANAGER, 
            "停止守护进程组: " + DaemonTypeToString(type) + 
            " (PID: " + std::to_string(pid) + ")");
    
    // 向整个进程组发送SIGTERM信号
    if (kill(-pid, SIGTERM) == -1) {
        if (errno == ESRCH) {
            // 进程组不存在
            it->second.process_id = 0;
            LOG_INFO(LogComponent::DAEMON_MANAGER, 
                    DaemonTypeToString(type) + " 进程组不存在，可能已退出");
            return true;
        } else {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                     "发送SIGTERM信号到进程组失败: " + std::string(strerror(errno)));
            return false;
        }
    }
    
    // 等待进程退出（由于有SIGCHLD处理器，这里主要是确认）
    for (int i = 0; i < 50; ++i) { // 等待最多5秒
        if (kill(pid, 0) == -1 && errno == ESRCH) {
            // 进程已不存在
            it->second.process_id = 0;
            LOG_INFO(LogComponent::DAEMON_MANAGER, 
                    "守护进程正常退出: " + DaemonTypeToString(type));
            return true;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 如果进程仍未退出，发送SIGKILL信号到整个进程组
    LOG_WARNING(LogComponent::DAEMON_MANAGER, 
               DaemonTypeToString(type) + " 未响应SIGTERM，发送SIGKILL到进程组");
    return KillDaemonProcessGroup(type);
}

bool DaemonManager::KillDaemonProcessGroup(DaemonType type) {
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                 "未找到守护进程类型: " + DaemonTypeToString(type));
        return false;
    }
    
    pid_t pid = it->second.process_id;
    if (pid <= 0) {
        LOG_INFO(LogComponent::DAEMON_MANAGER, 
                DaemonTypeToString(type) + " 进程已经停止");
        return true; // 进程已经停止
    }
    
    LOG_WARNING(LogComponent::DAEMON_MANAGER, 
               "强制终止守护进程组: " + DaemonTypeToString(type) + 
               " (PID: " + std::to_string(pid) + ")");
    
    // 向整个进程组发送SIGKILL信号
    if (kill(-pid, SIGKILL) == -1) {
        if (errno == ESRCH) {
            // 进程组不存在
            it->second.process_id = 0;
            LOG_INFO(LogComponent::DAEMON_MANAGER, 
                    DaemonTypeToString(type) + " 进程组不存在，可能已退出");
            return true;
        } else {
            LOG_ERROR(LogComponent::DAEMON_MANAGER, 
                     "发送SIGKILL信号到进程组失败: " + std::string(strerror(errno)));
            return false;
        }
    }
    
    // 等待进程退出确认
    for (int i = 0; i < 10; ++i) { // 等待最多1秒
        if (kill(pid, 0) == -1 && errno == ESRCH) {
            // 进程已不存在
            it->second.process_id = 0;
            LOG_INFO(LogComponent::DAEMON_MANAGER, 
                    "守护进程被强制终止: " + DaemonTypeToString(type));
            return true;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    LOG_ERROR(LogComponent::DAEMON_MANAGER, 
             "强制终止守护进程失败: " + DaemonTypeToString(type));
    return false;
}

bool DaemonManager::IsDaemonRunning(DaemonType type) {
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        return false;
    }
    
    pid_t pid = it->second.process_id;
    if (pid <= 0) {
        return false;
    }
    
    // 使用kill(pid, 0)检查进程是否存在
    return kill(pid, 0) == 0;
}

pid_t DaemonManager::GetDaemonPid(DaemonType type) {
    auto it = daemons_.find(type);
    if (it != daemons_.end()) {
        return it->second.process_id;
    }
    return 0;
}

void DaemonManager::MonitoringThread() {
    std::cout << "守护进程监控线程已启动" << std::endl;
    
    while (running_.load()) {
        try {
            // 更新所有守护进程状态
            for (auto type : {DaemonType::PTP4L, DaemonType::CHRONY, DaemonType::TS2PHC}) {
                UpdateDaemonStatus(type);
            }
            
            // 等待下一次监控周期
            std::unique_lock<std::mutex> lock(daemons_mutex_);
            monitoring_cv_.wait_for(lock, std::chrono::milliseconds(MONITORING_INTERVAL_MS), 
                                  [this] { return !running_.load(); });
            
        } catch (const std::exception& e) {
            std::cerr << "守护进程监控异常: " << e.what() << std::endl;
        }
    }
    
    std::cout << "守护进程监控线程已退出" << std::endl;
}

void DaemonManager::HealthCheckThread() {
    std::cout << "守护进程健康检查线程已启动" << std::endl;
    
    while (running_.load()) {
        try {
            // 对所有运行中的守护进程进行健康检查
            for (auto& pair : daemons_) {
                DaemonType type = pair.first;
                DaemonInfo& daemon = pair.second;
                
                if (daemon.status == DaemonStatus::RUNNING) {
                    // 检查进程是否仍在运行
                    if (!IsDaemonRunning(type)) {
                        std::cout << "检测到守护进程崩溃: " << DaemonTypeToString(type) << std::endl;
                        HandleDaemonCrash(type);
                    } else {
                        // 执行功能性健康检查
                        bool health_ok = PerformHealthCheck(type);
                        if (!health_ok) {
                            std::cout << "守护进程健康检查失败: " << DaemonTypeToString(type) << std::endl;
                            daemon.last_error = "健康检查失败";
                            
                            // 可以选择重启不健康的进程
                            if (daemon.restart_count < daemon.config.max_restart_attempts) {
                                std::cout << "由于健康检查失败，重启守护进程: " << DaemonTypeToString(type) << std::endl;
                                std::thread restart_thread([this, type]() {
                                    std::this_thread::sleep_for(std::chrono::milliseconds(DEFAULT_RESTART_DELAY_MS));
                                    RestartDaemon(type);
                                });
                                restart_thread.detach();
                            }
                        } else {
                            daemon.last_health_check_ns = GetCurrentTimestampNs();
                        }
                    }
                }
            }
            
            // 等待下一次健康检查周期
            std::unique_lock<std::mutex> lock(daemons_mutex_);
            health_check_cv_.wait_for(lock, std::chrono::milliseconds(HEALTH_CHECK_INTERVAL_MS), 
                                    [this] { return !running_.load(); });
            
        } catch (const std::exception& e) {
            std::cerr << "守护进程健康检查异常: " << e.what() << std::endl;
        }
    }
    
    std::cout << "守护进程健康检查线程已退出" << std::endl;
}

void DaemonManager::UpdateDaemonStatus(DaemonType type) {
    std::lock_guard<std::mutex> lock(daemons_mutex_);
    
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        return;
    }
    
    DaemonInfo& daemon = it->second;
    DaemonStatus old_status = daemon.status;
    
    // 检查进程状态
    if (daemon.process_id > 0) {
        if (IsDaemonRunning(type)) {
            if (daemon.status != DaemonStatus::RUNNING) {
                daemon.status = DaemonStatus::RUNNING;
            }
        } else {
            // 进程不存在，可能已崩溃
            daemon.status = DaemonStatus::CRASHED;
            daemon.process_id = 0;
        }
    } else {
        if (daemon.status == DaemonStatus::RUNNING) {
            daemon.status = DaemonStatus::STOPPED;
        }
    }
    
    // 如果状态发生变化，通知回调
    if (daemon.status != old_status) {
        NotifyStatusChange(type, old_status, daemon.status);
    }
}

void DaemonManager::HandleDaemonCrash(DaemonType type) {
    std::lock_guard<std::mutex> lock(daemons_mutex_);
    
    auto it = daemons_.find(type);
    if (it == daemons_.end()) {
        return;
    }
    
    DaemonInfo& daemon = it->second;
    DaemonStatus old_status = daemon.status;
    
    daemon.status = DaemonStatus::CRASHED;
    daemon.process_id = 0;
    
    // 分析崩溃原因
    std::string crash_reason = AnalyzeCrashReason(type);
    daemon.last_error = "进程崩溃: " + crash_reason;
    
    std::cout << "守护进程崩溃分析: " << DaemonTypeToString(type) << " - " << crash_reason << std::endl;
    
    NotifyStatusChange(type, old_status, DaemonStatus::CRASHED);
    
    // 实施降级策略
    bool should_restart = ImplementDegradationStrategy(type, crash_reason);
    
    // 如果启用了自动重启且未超过最大重启次数
    if (should_restart && daemon.config.auto_restart && daemon.restart_count < daemon.config.max_restart_attempts) {
        std::cout << "尝试自动重启守护进程: " << DaemonTypeToString(type) 
                  << " (重启次数: " << daemon.restart_count + 1 << "/" << daemon.config.max_restart_attempts << ")" << std::endl;
        
        // 在单独的线程中执行重启，避免阻塞监控线程
        uint32_t restart_count = daemon.restart_count;
        std::thread restart_thread([this, type, restart_count]() {
            // 根据崩溃次数增加延迟时间（指数退避）
            uint32_t delay = DEFAULT_RESTART_DELAY_MS * (1 << std::min(restart_count, 3u));
            std::this_thread::sleep_for(std::chrono::milliseconds(delay));
            RestartDaemon(type);
        });
        restart_thread.detach();
    } else {
        std::cout << "守护进程不会自动重启: " << DaemonTypeToString(type) 
                  << " (自动重启: " << (daemon.config.auto_restart ? "启用" : "禁用")
                  << ", 重启次数: " << daemon.restart_count << "/" << daemon.config.max_restart_attempts 
                  << ", 降级策略: " << (should_restart ? "允许重启" : "禁止重启") << ")" << std::endl;
    }
}

void DaemonManager::NotifyStatusChange(DaemonType type, DaemonStatus old_status, DaemonStatus new_status) {
    std::cout << "守护进程状态变化: " << DaemonTypeToString(type) 
              << " " << DaemonStatusToString(old_status) 
              << " -> " << DaemonStatusToString(new_status) << std::endl;
    
    if (status_change_callback_) {
        try {
            status_change_callback_(type, old_status, new_status);
        } catch (const std::exception& e) {
            std::cerr << "状态变化回调异常: " << e.what() << std::endl;
        }
    }
}

bool DaemonManager::CheckPortConflicts() {
    std::cout << "检查端口冲突..." << std::endl;
    
    // 检查NTP端口123是否被占用
    std::vector<uint16_t> ports_to_check = {123, 319, 320}; // NTP, PTP event, PTP general
    
    for (uint16_t port : ports_to_check) {
        if (IsPortInUse(port)) {
            std::cout << "警告: 端口 " << port << " 已被占用" << std::endl;
            
            // 尝试查找占用端口的进程
            std::string process_info = GetProcessUsingPort(port);
            if (!process_info.empty()) {
                std::cout << "占用进程: " << process_info << std::endl;
                
                // 如果是已知的守护进程，尝试停止
                if (process_info.find("chronyd") != std::string::npos ||
                    process_info.find("ptp4l") != std::string::npos ||
                    process_info.find("ntpd") != std::string::npos) {
                    std::cout << "检测到冲突的授时守护进程，建议手动停止: " << process_info << std::endl;
                }
            }
        }
    }
    
    std::cout << "端口冲突检查完成" << std::endl;
    return true;
}

bool DaemonManager::CheckResourceConflicts() {
    std::cout << "检查资源冲突..." << std::endl;
    
    // 检查关键设备文件是否存在和可访问
    std::vector<std::string> critical_devices = {
        "/dev/ptp0",    // PTP硬件时钟
        "/dev/pps0",    // PPS输入设备
        "/dev/rtc0"     // 实时时钟
    };
    
    for (const auto& device : critical_devices) {
        if (access(device.c_str(), F_OK) == 0) {
            std::cout << "✓ 设备可用: " << device << std::endl;
            
            // 检查设备权限
            if (access(device.c_str(), R_OK | W_OK) != 0) {
                std::cout << "警告: 设备权限不足: " << device << " (需要读写权限)" << std::endl;
            }
        } else {
            std::cout << "警告: 设备不存在: " << device << std::endl;
        }
    }
    
    // 检查网络接口
    std::vector<std::string> network_interfaces = {"eth0", "enp0s3", "ens33"};
    bool found_interface = false;
    
    for (const auto& iface : network_interfaces) {
        std::string path = "/sys/class/net/" + iface;
        if (access(path.c_str(), F_OK) == 0) {
            std::cout << "✓ 网络接口可用: " << iface << std::endl;
            found_interface = true;
            break;
        }
    }
    
    if (!found_interface) {
        std::cout << "警告: 未找到可用的网络接口" << std::endl;
    }
    
    std::cout << "资源冲突检查完成" << std::endl;
    return true;
}

bool DaemonManager::ResolvePortConflict(DaemonType daemon1, DaemonType daemon2) {
    std::cout << "解决端口冲突: " << DaemonTypeToString(daemon1) 
              << " vs " << DaemonTypeToString(daemon2) << std::endl;
    
    // 这里可以实现具体的冲突解决逻辑
    // 例如：修改配置文件中的端口号、停止冲突的进程等
    
    return true;
}

std::string DaemonManager::DaemonTypeToString(DaemonType type) {
    return timing_server::core::DaemonTypeToString(type);
}

std::string DaemonManager::DaemonStatusToString(DaemonStatus status) {
    return timing_server::core::DaemonStatusToString(status);
}

// Simplified helper functions for basic functionality
bool DaemonManager::IsPortInUse(uint16_t port) {
    (void)port;
    return false;
}

std::string DaemonManager::GetProcessUsingPort(uint16_t port) {
    (void)port;
    return "";
}

bool DaemonManager::IsProcessRunning(const std::string& process_name) {
    (void)process_name;
    return false;
}

std::vector<pid_t> DaemonManager::FindProcessesByName(const std::string& process_name) {
    (void)process_name;
    return {};
}

PtpConfig DaemonManager::GenerateOptimalPtpConfig(ClockState clock_state, TimeSource active_source) {
    (void)active_source;
    
    PtpConfig config;
    config.domain_number = 0;
    config.priority1 = 128;
    config.priority2 = 128;
    config.network_interface = "eth0";
    config.network_transport = "UDPv4";
    config.delay_mechanism = "E2E";
    config.master_only = true;
    config.slave_only = false;
    config.two_step_flag = true;
    config.announce_receipt_timeout = 3;
    config.sync_receipt_timeout = 0;
    config.logging_level = 6;
    config.use_syslog = true;
    
    switch (clock_state) {
        case ClockState::LOCKED:
            config.announce_interval = 1;
            config.sync_interval = 0;
            config.delay_req_interval = 0;
            break;
        case ClockState::HOLDOVER:
            config.announce_interval = 2;
            config.sync_interval = 1;
            config.delay_req_interval = 1;
            break;
        case ClockState::DISCIPLINING:
            config.announce_interval = 0;
            config.sync_interval = -1;
            config.delay_req_interval = 0;
            break;
        default:
            config.announce_interval = 1;
            config.sync_interval = 0;
            config.delay_req_interval = 0;
            break;
    }
    
    return config;
}

NtpConfig DaemonManager::GenerateOptimalNtpConfig(ClockState clock_state, TimeSource active_source) {
    (void)clock_state;
    
    NtpConfig config;
    config.stratum = 1;
    config.reference_id = "GPS";
    config.phc_device = "/dev/ptp0";
    config.gnss_shm_segment = "0";
    config.gnss_offset = 0.0;
    config.bind_address = "0.0.0.0";
    config.port = 123;
    config.allowed_networks = {"***********/16", "10.0.0.0/8"};
    config.client_log_limit = 1000;
    config.rate_limit_interval = 1;
    config.rate_limit_burst = 16;
    config.rate_limit_leak = 2;
    config.lock_all = true;
    config.sched_priority = 50;
    config.max_update_skew = 100.0;
    config.make_step_threshold = 1.0;
    config.make_step_limit = 3;
    config.log_dir = log_dir_ + "/chrony";
    config.log_tracking = true;
    config.log_measurements = true;
    config.log_statistics = true;
    config.log_change_threshold = 0.5;
    
    switch (active_source) {
        case TimeSource::GNSS:
            config.reference_id = "GPS";
            break;
        case TimeSource::RUBIDIUM:
            config.reference_id = "RB";
            break;
        case TimeSource::EXTERNAL_PPS:
            config.reference_id = "PPS";
            break;
        case TimeSource::PHC:
            config.reference_id = "PHC";
            break;
        default:
            config.reference_id = "LCL";
            break;
    }
    
    return config;
}

Ts2phcConfig DaemonManager::GenerateOptimalTs2phcConfig(ClockState clock_state, TimeSource active_source) {
    (void)active_source;
    
    Ts2phcConfig config;
    config.pps_device = "/dev/pps0";
    config.network_interface = "eth0";
    config.extts_polarity = "rising";
    config.extts_correction = 0;
    config.pin_index = 0;
    config.pulse_width = 500000000;
    config.dpll_pin = 1;
    config.dpll_state = "automatic";
    config.logging_level = 6;
    config.use_syslog = true;
    config.verbose = true;
    
    switch (clock_state) {
        case ClockState::DISCIPLINING:
            config.verbose = true;
            config.logging_level = 7;
            break;
        case ClockState::LOCKED:
            config.verbose = false;
            config.logging_level = 6;
            break;
        default:
            config.verbose = false;
            config.logging_level = 5;
            break;
    }
    
    return config;
}

std::string DaemonManager::AnalyzeCrashReason(DaemonType type) {
    (void)type;
    return "Unknown reason";
}

bool DaemonManager::ImplementDegradationStrategy(DaemonType type, const std::string& crash_reason) {
    (void)type;
    (void)crash_reason;
    return true;
}

bool DaemonManager::PerformHealthCheck(DaemonType type) {
    (void)type;
    return true;
}

bool DaemonManager::CheckPtp4lHealth() {
    return true;
}

bool DaemonManager::CheckChronyHealth() {
    return true;
}

bool DaemonManager::CheckTs2phcHealth() {
    return true;
}

} // namespace core
} // namespace timing_server