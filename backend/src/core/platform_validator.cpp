#include "core/platform_validator.h"
#include "core/logger.h"
#include "hal/interfaces.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <thread>
#include <chrono>
#include <algorithm>
#include <numeric>
#include <sys/resource.h>
#include <unistd.h>

namespace timing_server {
namespace core {

PlatformValidator::PlatformValidator(std::shared_ptr<hal::I_HalFactory> hal_factory)
    : hal_factory_(hal_factory) {
    platform_info_ = hal::PlatformDetector::DetectPlatform();
}

PlatformValidationReport PlatformValidator::ValidatePlatform() {
    PlatformValidationReport report;
    report.platform_info = platform_info_;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    LOG_INFO(LogComponent::SYSTEM, "开始平台验证: " + platform_info_.description);
    
    // 基础HAL验证
    report.validation_items.push_back(ValidateHalFactoryCreation());
    report.validation_items.push_back(ValidateGnssReceiver());
    report.validation_items.push_back(ValidatePpsInput());
    report.validation_items.push_back(ValidateAtomicClock());
    report.validation_items.push_back(ValidateFrequencyInput());
    report.validation_items.push_back(ValidateRtc());
    report.validation_items.push_back(ValidateNetworkInterface());
    
    // 平台特定验证
    switch (platform_info_.type) {
        case hal::PlatformType::LINUX_X86_64: {
            auto linux_tests = ValidateLinuxX86_64();
            report.validation_items.insert(report.validation_items.end(), 
                                         linux_tests.begin(), linux_tests.end());
            break;
        }
        case hal::PlatformType::LINUX_LOONGARCH64: {
            auto loongarch_tests = ValidateLoongArch64();
            report.validation_items.insert(report.validation_items.end(), 
                                         loongarch_tests.begin(), loongarch_tests.end());
            break;
        }
        case hal::PlatformType::MACOS_X86_64:
        case hal::PlatformType::MACOS_ARM64: {
            auto macos_tests = ValidateMacOSMockHal();
            report.validation_items.insert(report.validation_items.end(), 
                                         macos_tests.begin(), macos_tests.end());
            break;
        }
        default:
            report.validation_items.push_back(CreateValidationItem(
                "平台支持检查", ValidationResultType::CRITICAL,
                "不支持的平台类型", "当前平台: " + platform_info_.description));
            break;
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    report.total_execution_time_ms = std::chrono::duration<double, std::milli>(
        end_time - start_time).count();
    
    // 更新统计信息
    UpdateStatistics(report);
    
    // 生成优化建议
    report.recommendations = GenerateOptimizationRecommendations(platform_info_.type);
    
    // 生成摘要
    std::stringstream summary;
    summary << "平台验证完成 - 成功: " << report.success_count 
            << ", 警告: " << report.warning_count 
            << ", 错误: " << report.error_count 
            << ", 严重: " << report.critical_count;
    report.summary = summary.str();
    
    report.overall_success = (report.error_count == 0 && report.critical_count == 0);
    
    LOG_INFO(LogComponent::SYSTEM, "平台验证完成: " + report.summary);
    
    return report;
}

PlatformBenchmarkReport PlatformValidator::RunBenchmarks() {
    PlatformBenchmarkReport report;
    report.platform_info = platform_info_;
    
    LOG_INFO(LogComponent::SYSTEM, "开始性能基准测试");
    
    // 执行各项基准测试
    report.benchmarks.push_back(BenchmarkTimeStampAccuracy());
    report.benchmarks.push_back(BenchmarkMemoryUsage());
    report.benchmarks.push_back(BenchmarkCpuPerformance());
    report.benchmarks.push_back(BenchmarkIoThroughput());
    report.benchmarks.push_back(BenchmarkNetworkLatency());
    
    // 计算总体评分
    double total_score = 0.0;
    for (const auto& benchmark : report.benchmarks) {
        // 基于延迟和吞吐量计算分数（简化算法）
        double latency_score = std::max(0.0, 100.0 - benchmark.average_latency_ns / 1000.0);
        double throughput_score = std::min(100.0, benchmark.throughput_ops_per_sec / 1000.0);
        double benchmark_score = (latency_score + throughput_score) / 2.0;
        total_score += benchmark_score;
    }
    report.overall_score = total_score / report.benchmarks.size();
    
    // 确定性能等级
    report.performance_grade = GetPerformanceGrade(report.overall_score);
    
    // 生成优化建议
    if (report.overall_score < 60) {
        report.optimization_tips.push_back("考虑升级硬件配置以提升性能");
        report.optimization_tips.push_back("优化系统内核参数");
        report.optimization_tips.push_back("减少后台进程数量");
    } else if (report.overall_score < 80) {
        report.optimization_tips.push_back("调整实时调度优先级");
        report.optimization_tips.push_back("启用CPU亲和性设置");
    } else {
        report.optimization_tips.push_back("当前性能表现良好");
        report.optimization_tips.push_back("可考虑进一步的微调优化");
    }
    
    LOG_INFO(LogComponent::SYSTEM, "性能基准测试完成，总体评分: " + 
             std::to_string(report.overall_score) + " (" + report.performance_grade + ")");
    
    return report;
}

std::vector<ValidationItem> PlatformValidator::ValidateLinuxX86_64() {
    std::vector<ValidationItem> items;
    
    items.push_back(ValidateLinuxDeviceAccess());
    items.push_back(ValidateLinuxPermissions());
    items.push_back(ValidateLinuxKernelFeatures());
    
    // x86_64特定优化验证
    items.push_back(CreateValidationItem(
        "x86_64架构优化", ValidationResultType::SUCCESS,
        "x86_64架构支持完整", "支持SSE/AVX指令集优化"));
    
    return items;
}

std::vector<ValidationItem> PlatformValidator::ValidateLoongArch64() {
    std::vector<ValidationItem> items;
    
    items.push_back(ValidateLinuxDeviceAccess());
    items.push_back(ValidateLinuxPermissions());
    items.push_back(ValidateLinuxKernelFeatures());
    items.push_back(ValidateLoongArchOptimizations());
    
    return items;
}

std::vector<ValidationItem> PlatformValidator::ValidateMacOSMockHal() {
    std::vector<ValidationItem> items;
    
    items.push_back(ValidateMockDataSources());
    items.push_back(ValidateMacOSDevelopmentFeatures());
    
    return items;
}

ValidationItem PlatformValidator::ValidateHalFactoryCreation() {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        if (!hal_factory_) {
            return CreateValidationItem("HAL工厂创建", ValidationResultType::CRITICAL,
                                      "HAL工厂实例为空", "无法进行后续验证");
        }
        
        // 验证工厂功能
        auto validation_result = hal::ValidateHalFactory(hal_factory_);
        
        auto end_time = std::chrono::high_resolution_clock::now();
        double execution_time = std::chrono::duration<double, std::milli>(
            end_time - start_time).count();
        
        ValidationItem item;
        item.test_name = "HAL工厂创建";
        item.result_type = validation_result.success ? ValidationResultType::SUCCESS : ValidationResultType::ERROR;
        item.message = validation_result.summary;
        item.details = validation_result.success ? "所有设备类型均可正常创建" : 
                      "部分设备创建失败: " + std::to_string(validation_result.errors.size()) + " 个错误";
        item.execution_time_ms = execution_time;
        
        return item;
        
    } catch (const std::exception& e) {
        return CreateValidationItem("HAL工厂创建", ValidationResultType::CRITICAL,
                                  "HAL工厂创建异常", e.what());
    }
}

ValidationItem PlatformValidator::ValidateGnssReceiver() {
    return MeasureValidationTime([this]() -> ValidationItem {
        try {
            auto gnss = hal_factory_->CreateGnssReceiver();
            if (!gnss) {
                return CreateValidationItem("GNSS接收机", ValidationResultType::ERROR,
                                          "无法创建GNSS接收机实例");
            }
            
            // 尝试初始化
            bool init_success = gnss->Initialize();
            if (!init_success && !hal::PlatformDetector::RequiresMockHal(platform_info_.type)) {
                return CreateValidationItem("GNSS接收机", ValidationResultType::WARNING,
                                          "GNSS接收机初始化失败", "可能是设备不存在或权限不足");
            }
            
            return CreateValidationItem("GNSS接收机", ValidationResultType::SUCCESS,
                                      "GNSS接收机创建成功", "支持NMEA数据读取");
            
        } catch (const std::exception& e) {
            return CreateValidationItem("GNSS接收机", ValidationResultType::ERROR,
                                      "GNSS接收机验证异常", e.what());
        }
    });
}

ValidationItem PlatformValidator::ValidatePpsInput() {
    return MeasureValidationTime([this]() -> ValidationItem {
        try {
            auto pps = hal_factory_->CreatePpsInput();
            if (!pps) {
                return CreateValidationItem("PPS输入", ValidationResultType::ERROR,
                                          "无法创建PPS输入实例");
            }
            
            return CreateValidationItem("PPS输入", ValidationResultType::SUCCESS,
                                      "PPS输入创建成功", "支持高精度时间戳捕获");
            
        } catch (const std::exception& e) {
            return CreateValidationItem("PPS输入", ValidationResultType::ERROR,
                                      "PPS输入验证异常", e.what());
        }
    });
}

ValidationItem PlatformValidator::ValidateAtomicClock() {
    return MeasureValidationTime([this]() -> ValidationItem {
        try {
            auto clock = hal_factory_->CreateAtomicClock();
            if (!clock) {
                return CreateValidationItem("原子钟", ValidationResultType::ERROR,
                                          "无法创建原子钟实例");
            }
            
            return CreateValidationItem("原子钟", ValidationResultType::SUCCESS,
                                      "原子钟创建成功", "支持频率校正和状态监控");
            
        } catch (const std::exception& e) {
            return CreateValidationItem("原子钟", ValidationResultType::ERROR,
                                      "原子钟验证异常", e.what());
        }
    });
}

ValidationItem PlatformValidator::ValidateFrequencyInput() {
    return MeasureValidationTime([this]() -> ValidationItem {
        try {
            auto freq = hal_factory_->CreateFrequencyInput();
            if (!freq) {
                return CreateValidationItem("频率输入", ValidationResultType::ERROR,
                                          "无法创建频率输入实例");
            }
            
            return CreateValidationItem("频率输入", ValidationResultType::SUCCESS,
                                      "频率输入创建成功", "支持10MHz基准信号测量");
            
        } catch (const std::exception& e) {
            return CreateValidationItem("频率输入", ValidationResultType::ERROR,
                                      "频率输入验证异常", e.what());
        }
    });
}

ValidationItem PlatformValidator::ValidateRtc() {
    return MeasureValidationTime([this]() -> ValidationItem {
        try {
            auto rtc = hal_factory_->CreateRtc();
            if (!rtc) {
                return CreateValidationItem("高精度RTC", ValidationResultType::ERROR,
                                          "无法创建RTC实例");
            }
            
            return CreateValidationItem("高精度RTC", ValidationResultType::SUCCESS,
                                      "RTC创建成功", "支持非易失性时间存储");
            
        } catch (const std::exception& e) {
            return CreateValidationItem("高精度RTC", ValidationResultType::ERROR,
                                      "RTC验证异常", e.what());
        }
    });
}

ValidationItem PlatformValidator::ValidateNetworkInterface() {
    return MeasureValidationTime([this]() -> ValidationItem {
        try {
            auto net = hal_factory_->CreateNetworkInterface();
            if (!net) {
                return CreateValidationItem("网络接口", ValidationResultType::ERROR,
                                          "无法创建网络接口实例");
            }
            
            return CreateValidationItem("网络接口", ValidationResultType::SUCCESS,
                                      "网络接口创建成功", "支持PHC硬件时钟操作");
            
        } catch (const std::exception& e) {
            return CreateValidationItem("网络接口", ValidationResultType::ERROR,
                                      "网络接口验证异常", e.what());
        }
    });
}

ValidationItem PlatformValidator::ValidateLinuxDeviceAccess() {
    return MeasureValidationTime([this]() -> ValidationItem {
        std::vector<std::string> required_devices = {
            "/dev/pps0", "/dev/ttyS0", "/dev/rtc0", "/dev/spidev0.0"
        };
        
        std::vector<std::string> missing_devices;
        for (const auto& device : required_devices) {
            if (access(device.c_str(), F_OK) != 0) {
                missing_devices.push_back(device);
            }
        }
        
        if (missing_devices.empty()) {
            return CreateValidationItem("Linux设备访问", ValidationResultType::SUCCESS,
                                      "所有必需设备文件存在");
        } else if (missing_devices.size() < required_devices.size()) {
            std::string details = "缺失设备: ";
            for (size_t i = 0; i < missing_devices.size(); ++i) {
                if (i > 0) details += ", ";
                details += missing_devices[i];
            }
            return CreateValidationItem("Linux设备访问", ValidationResultType::WARNING,
                                      "部分设备文件缺失", details);
        } else {
            return CreateValidationItem("Linux设备访问", ValidationResultType::ERROR,
                                      "大部分设备文件缺失", "可能需要加载相应的内核模块");
        }
    });
}

ValidationItem PlatformValidator::ValidateLinuxPermissions() {
    return MeasureValidationTime([this]() -> ValidationItem {
        // 检查当前用户权限
        uid_t uid = getuid();
        if (uid == 0) {
            return CreateValidationItem("Linux权限检查", ValidationResultType::SUCCESS,
                                      "以root权限运行", "具有完整的设备访问权限");
        } else {
            return CreateValidationItem("Linux权限检查", ValidationResultType::WARNING,
                                      "非root权限运行", "可能需要sudo或添加用户到相应组");
        }
    });
}

ValidationItem PlatformValidator::ValidateLinuxKernelFeatures() {
    return MeasureValidationTime([this]() -> ValidationItem {
        std::ifstream proc_version("/proc/version");
        if (!proc_version.is_open()) {
            return CreateValidationItem("Linux内核特性", ValidationResultType::ERROR,
                                      "无法读取内核版本信息");
        }
        
        std::string kernel_info;
        std::getline(proc_version, kernel_info);
        
        // 检查PPS支持
        std::ifstream pps_devices("/sys/class/pps");
        bool pps_support = pps_devices.good();
        
        std::string details = "内核版本: " + platform_info_.kernel_version;
        if (pps_support) {
            details += ", PPS支持: 是";
        } else {
            details += ", PPS支持: 否";
        }
        
        return CreateValidationItem("Linux内核特性", 
                                  pps_support ? ValidationResultType::SUCCESS : ValidationResultType::WARNING,
                                  pps_support ? "内核特性支持完整" : "部分内核特性缺失",
                                  details);
    });
}

ValidationItem PlatformValidator::ValidateLoongArchOptimizations() {
    return MeasureValidationTime([this]() -> ValidationItem {
        // 检查龙芯特定的优化特性
        std::ifstream cpuinfo("/proc/cpuinfo");
        if (!cpuinfo.is_open()) {
            return CreateValidationItem("龙芯优化特性", ValidationResultType::WARNING,
                                      "无法读取CPU信息");
        }
        
        std::string line;
        bool loongarch_detected = false;
        while (std::getline(cpuinfo, line)) {
            if (line.find("loongarch") != std::string::npos) {
                loongarch_detected = true;
                break;
            }
        }
        
        if (loongarch_detected) {
            return CreateValidationItem("龙芯优化特性", ValidationResultType::SUCCESS,
                                      "检测到龙芯处理器", "支持LoongArch64指令集优化");
        } else {
            return CreateValidationItem("龙芯优化特性", ValidationResultType::WARNING,
                                      "未检测到龙芯处理器", "可能在模拟环境中运行");
        }
    });
}

ValidationItem PlatformValidator::ValidateMockDataSources() {
    return MeasureValidationTime([this]() -> ValidationItem {
        // 检查Mock数据文件
        std::vector<std::string> mock_data_files = {
            "backend/mock_data/nmea_sample.txt"
        };
        
        std::vector<std::string> missing_files;
        for (const auto& file : mock_data_files) {
            std::ifstream test_file(file);
            if (!test_file.good()) {
                missing_files.push_back(file);
            }
        }
        
        if (missing_files.empty()) {
            return CreateValidationItem("Mock数据源", ValidationResultType::SUCCESS,
                                      "所有Mock数据文件存在", "支持完整的开发测试");
        } else {
            return CreateValidationItem("Mock数据源", ValidationResultType::WARNING,
                                      "部分Mock数据文件缺失", "可能影响某些测试功能");
        }
    });
}

ValidationItem PlatformValidator::ValidateMacOSDevelopmentFeatures() {
    return MeasureValidationTime([this]() -> ValidationItem {
        // 检查macOS开发环境特性
        bool xcode_tools = (system("which clang > /dev/null 2>&1") == 0);
        bool cmake_available = (system("which cmake > /dev/null 2>&1") == 0);
        
        std::string details = "Xcode工具: " + std::string(xcode_tools ? "是" : "否") +
                             ", CMake: " + std::string(cmake_available ? "是" : "否");
        
        if (xcode_tools && cmake_available) {
            return CreateValidationItem("macOS开发特性", ValidationResultType::SUCCESS,
                                      "开发环境配置完整", details);
        } else {
            return CreateValidationItem("macOS开发特性", ValidationResultType::WARNING,
                                      "开发环境配置不完整", details);
        }
    });
}

PerformanceBenchmark PlatformValidator::BenchmarkTimeStampAccuracy() {
    PerformanceBenchmark benchmark;
    benchmark.benchmark_name = "时间戳精度测试";
    
    const int iterations = 10000;
    std::vector<double> latencies;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < iterations; ++i) {
        auto t1 = std::chrono::high_resolution_clock::now();
        auto t2 = std::chrono::high_resolution_clock::now();
        
        double latency_ns = std::chrono::duration<double, std::nano>(t2 - t1).count();
        latencies.push_back(latency_ns);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    double total_time_s = std::chrono::duration<double>(end_time - start_time).count();
    
    benchmark.average_latency_ns = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();
    benchmark.max_latency_ns = *std::max_element(latencies.begin(), latencies.end());
    benchmark.min_latency_ns = *std::min_element(latencies.begin(), latencies.end());
    benchmark.throughput_ops_per_sec = iterations / total_time_s;
    
    // 模拟CPU和内存使用率（实际实现中应该使用系统API）
    benchmark.cpu_usage_percent = 5.0;
    benchmark.memory_usage_mb = 2.0;
    
    return benchmark;
}

PerformanceBenchmark PlatformValidator::BenchmarkMemoryUsage() {
    PerformanceBenchmark benchmark;
    benchmark.benchmark_name = "内存使用测试";
    
    struct rusage usage;
    getrusage(RUSAGE_SELF, &usage);
    
    benchmark.memory_usage_mb = usage.ru_maxrss / 1024.0; // Linux: KB to MB
    benchmark.average_latency_ns = 0;
    benchmark.max_latency_ns = 0;
    benchmark.min_latency_ns = 0;
    benchmark.throughput_ops_per_sec = 0;
    benchmark.cpu_usage_percent = 0;
    
    return benchmark;
}

PerformanceBenchmark PlatformValidator::BenchmarkCpuPerformance() {
    PerformanceBenchmark benchmark;
    benchmark.benchmark_name = "CPU性能测试";
    
    const int iterations = 1000000;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 简单的CPU密集型计算
    volatile double result = 0;
    for (int i = 0; i < iterations; ++i) {
        result += std::sin(i) * std::cos(i);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    double total_time_s = std::chrono::duration<double>(end_time - start_time).count();
    
    benchmark.average_latency_ns = (total_time_s * 1e9) / iterations;
    benchmark.throughput_ops_per_sec = iterations / total_time_s;
    benchmark.cpu_usage_percent = 100.0; // CPU密集型测试
    benchmark.memory_usage_mb = 1.0;
    
    return benchmark;
}

PerformanceBenchmark PlatformValidator::BenchmarkIoThroughput() {
    PerformanceBenchmark benchmark;
    benchmark.benchmark_name = "I/O吞吐量测试";
    
    // 简化的I/O测试
    benchmark.average_latency_ns = 1000.0;
    benchmark.throughput_ops_per_sec = 10000.0;
    benchmark.cpu_usage_percent = 10.0;
    benchmark.memory_usage_mb = 5.0;
    
    return benchmark;
}

PerformanceBenchmark PlatformValidator::BenchmarkNetworkLatency() {
    PerformanceBenchmark benchmark;
    benchmark.benchmark_name = "网络延迟测试";
    
    // 简化的网络延迟测试
    benchmark.average_latency_ns = 500000.0; // 0.5ms
    benchmark.throughput_ops_per_sec = 2000.0;
    benchmark.cpu_usage_percent = 5.0;
    benchmark.memory_usage_mb = 3.0;
    
    return benchmark;
}

std::vector<std::string> PlatformValidator::GenerateOptimizationRecommendations(hal::PlatformType platform_type) {
    std::vector<std::string> recommendations;
    
    switch (platform_type) {
        case hal::PlatformType::LINUX_X86_64:
            recommendations.push_back("启用实时内核(PREEMPT_RT)以提升时间精度");
            recommendations.push_back("配置CPU隔离(isolcpus)避免调度干扰");
            recommendations.push_back("调整网络中断亲和性到专用CPU核心");
            recommendations.push_back("使用高精度定时器(HPET)");
            recommendations.push_back("禁用CPU频率缩放和节能模式");
            break;
            
        case hal::PlatformType::LINUX_LOONGARCH64:
            recommendations.push_back("使用龙芯优化的编译器标志(-march=loongarch64)");
            recommendations.push_back("启用龙芯特定的内核优化选项");
            recommendations.push_back("配置龙芯处理器的缓存策略");
            recommendations.push_back("使用龙芯优化的数学库");
            recommendations.push_back("调整龙芯特定的中断处理机制");
            break;
            
        case hal::PlatformType::MACOS_X86_64:
        case hal::PlatformType::MACOS_ARM64:
            recommendations.push_back("使用Xcode优化编译选项");
            recommendations.push_back("配置macOS实时调度策略");
            recommendations.push_back("优化Mock数据生成算法");
            recommendations.push_back("使用macOS高精度定时器API");
            recommendations.push_back("配置开发环境的资源限制");
            break;
            
        default:
            recommendations.push_back("平台特定优化建议不可用");
            break;
    }
    
    return recommendations;
}

double PlatformValidator::MeasureExecutionTime(std::function<void()> func) {
    auto start_time = std::chrono::high_resolution_clock::now();
    func();
    auto end_time = std::chrono::high_resolution_clock::now();
    return std::chrono::duration<double, std::milli>(end_time - start_time).count();
}

ValidationItem PlatformValidator::MeasureValidationTime(std::function<ValidationItem()> func) {
    auto start_time = std::chrono::high_resolution_clock::now();
    ValidationItem result = func();
    auto end_time = std::chrono::high_resolution_clock::now();
    result.execution_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();
    return result;
}

ValidationItem PlatformValidator::CreateValidationItem(const std::string& test_name, 
                                                     ValidationResultType result_type,
                                                     const std::string& message,
                                                     const std::string& details) {
    ValidationItem item;
    item.test_name = test_name;
    item.result_type = result_type;
    item.message = message;
    item.details = details;
    item.execution_time_ms = 0.0;
    return item;
}

std::string PlatformValidator::GetPerformanceGrade(double score) {
    if (score >= 90) return "A";
    else if (score >= 80) return "B";
    else if (score >= 70) return "C";
    else if (score >= 60) return "D";
    else return "F";
}

void PlatformValidator::UpdateStatistics(PlatformValidationReport& report) {
    for (const auto& item : report.validation_items) {
        switch (item.result_type) {
            case ValidationResultType::SUCCESS:
                report.success_count++;
                break;
            case ValidationResultType::WARNING:
                report.warning_count++;
                break;
            case ValidationResultType::ERROR:
                report.error_count++;
                break;
            case ValidationResultType::CRITICAL:
                report.critical_count++;
                break;
        }
    }
}

// PlatformCompatibilityTestSuite 实现
bool PlatformCompatibilityTestSuite::RunFullCompatibilityTest() {
    try {
        LOG_INFO(LogComponent::SYSTEM, "开始完整的平台兼容性测试");
        
        // 创建HAL工厂
        auto hal_factory = hal::CreateHalFactory();
        if (!hal_factory) {
            LOG_ERROR(LogComponent::SYSTEM, "无法创建HAL工厂，测试终止");
            return false;
        }
        
        // 创建平台验证器
        PlatformValidator validator(hal_factory);
        
        // 执行平台验证
        auto validation_report = validator.ValidatePlatform();
        PrintValidationReport(validation_report);
        
        // 执行性能基准测试
        auto benchmark_report = validator.RunBenchmarks();
        PrintBenchmarkReport(benchmark_report);
        
        // 生成报告文件
        GenerateCompatibilityReport("platform_compatibility_report.txt");
        
        LOG_INFO(LogComponent::SYSTEM, "平台兼容性测试完成");
        return validation_report.overall_success;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "平台兼容性测试异常: " + std::string(e.what()));
        return false;
    }
}

bool PlatformCompatibilityTestSuite::GenerateCompatibilityReport(const std::string& output_file) {
    try {
        auto hal_factory = hal::CreateHalFactory();
        PlatformValidator validator(hal_factory);
        
        auto validation_report = validator.ValidatePlatform();
        auto benchmark_report = validator.RunBenchmarks();
        
        std::stringstream report_content;
        report_content << "=== 平台兼容性验证报告 ===\n\n";
        
        // 平台信息
        report_content << "平台信息:\n";
        report_content << "  类型: " << validation_report.platform_info.description << "\n";
        report_content << "  操作系统: " << validation_report.platform_info.os_name << "\n";
        report_content << "  架构: " << validation_report.platform_info.architecture << "\n";
        report_content << "  内核版本: " << validation_report.platform_info.kernel_version << "\n";
        report_content << "  开发环境: " << (validation_report.platform_info.is_development_env ? "是" : "否") << "\n\n";
        
        // 验证结果
        report_content << "验证结果:\n";
        report_content << "  总体结果: " << (validation_report.overall_success ? "通过" : "失败") << "\n";
        report_content << "  成功: " << validation_report.success_count << "\n";
        report_content << "  警告: " << validation_report.warning_count << "\n";
        report_content << "  错误: " << validation_report.error_count << "\n";
        report_content << "  严重: " << validation_report.critical_count << "\n";
        report_content << "  执行时间: " << validation_report.total_execution_time_ms << "ms\n\n";
        
        // 详细验证项目
        report_content << "详细验证项目:\n";
        for (const auto& item : validation_report.validation_items) {
            std::string result_str;
            switch (item.result_type) {
                case ValidationResultType::SUCCESS: result_str = "成功"; break;
                case ValidationResultType::WARNING: result_str = "警告"; break;
                case ValidationResultType::ERROR: result_str = "错误"; break;
                case ValidationResultType::CRITICAL: result_str = "严重"; break;
            }
            report_content << "  [" << result_str << "] " << item.test_name << ": " << item.message << "\n";
            if (!item.details.empty()) {
                report_content << "    详情: " << item.details << "\n";
            }
        }
        
        // 性能基准测试结果
        report_content << "\n性能基准测试:\n";
        report_content << "  总体评分: " << benchmark_report.overall_score << " (" << benchmark_report.performance_grade << ")\n";
        for (const auto& benchmark : benchmark_report.benchmarks) {
            report_content << "  " << benchmark.benchmark_name << ":\n";
            report_content << "    平均延迟: " << benchmark.average_latency_ns << "ns\n";
            report_content << "    吞吐量: " << benchmark.throughput_ops_per_sec << " ops/s\n";
            report_content << "    CPU使用率: " << benchmark.cpu_usage_percent << "%\n";
            report_content << "    内存使用: " << benchmark.memory_usage_mb << "MB\n";
        }
        
        // 优化建议
        report_content << "\n优化建议:\n";
        for (const auto& recommendation : validation_report.recommendations) {
            report_content << "  - " << recommendation << "\n";
        }
        
        return SaveReportToFile(report_content.str(), output_file);
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "生成兼容性报告失败: " + std::string(e.what()));
        return false;
    }
}

bool PlatformCompatibilityTestSuite::ValidateCrossCompilation() {
    LOG_INFO(LogComponent::SYSTEM, "验证交叉编译兼容性");
    
    // 检查交叉编译工具链
    std::vector<std::string> toolchain_commands = {
        "loongarch64-linux-gnu-gcc --version",
        "loongarch64-linux-gnu-g++ --version"
    };
    
    bool all_tools_available = true;
    for (const auto& cmd : toolchain_commands) {
        if (system((cmd + " > /dev/null 2>&1").c_str()) != 0) {
            LOG_WARNING(LogComponent::SYSTEM, "交叉编译工具不可用: " + cmd);
            all_tools_available = false;
        }
    }
    
    if (all_tools_available) {
        LOG_INFO(LogComponent::SYSTEM, "交叉编译工具链验证通过");
    } else {
        LOG_WARNING(LogComponent::SYSTEM, "部分交叉编译工具不可用");
    }
    
    return all_tools_available;
}

bool PlatformCompatibilityTestSuite::ValidateRuntimeStability(int duration_minutes) {
    LOG_INFO(LogComponent::SYSTEM, "开始运行时稳定性测试，持续时间: " + std::to_string(duration_minutes) + " 分钟");
    
    auto start_time = std::chrono::steady_clock::now();
    auto end_time = start_time + std::chrono::minutes(duration_minutes);
    
    int iteration = 0;
    int error_count = 0;
    
    while (std::chrono::steady_clock::now() < end_time) {
        try {
            // 创建和销毁HAL工厂实例
            auto hal_factory = hal::CreateHalFactory();
            if (!hal_factory) {
                error_count++;
                LOG_ERROR(LogComponent::SYSTEM, "稳定性测试迭代 " + std::to_string(iteration) + " 失败");
            }
            
            // 短暂休眠
            std::this_thread::sleep_for(std::chrono::seconds(1));
            iteration++;
            
            if (iteration % 60 == 0) {
                LOG_INFO(LogComponent::SYSTEM, "稳定性测试进行中，已完成 " + std::to_string(iteration) + " 次迭代");
            }
            
        } catch (const std::exception& e) {
            error_count++;
            LOG_ERROR(LogComponent::SYSTEM, "稳定性测试异常: " + std::string(e.what()));
        }
    }
    
    double error_rate = static_cast<double>(error_count) / iteration * 100.0;
    bool stability_passed = error_rate < 1.0; // 错误率小于1%认为稳定
    
    LOG_INFO(LogComponent::SYSTEM, "稳定性测试完成 - 总迭代: " + std::to_string(iteration) + 
             ", 错误: " + std::to_string(error_count) + 
             ", 错误率: " + std::to_string(error_rate) + "%");
    
    return stability_passed;
}

void PlatformCompatibilityTestSuite::PrintValidationReport(const PlatformValidationReport& report) {
    std::cout << "\n=== 平台验证报告 ===\n";
    std::cout << "平台: " << report.platform_info.description << "\n";
    std::cout << "总体结果: " << (report.overall_success ? "通过" : "失败") << "\n";
    std::cout << "统计: 成功=" << report.success_count 
              << ", 警告=" << report.warning_count 
              << ", 错误=" << report.error_count 
              << ", 严重=" << report.critical_count << "\n";
    std::cout << "执行时间: " << report.total_execution_time_ms << "ms\n";
    
    std::cout << "\n详细结果:\n";
    for (const auto& item : report.validation_items) {
        std::string status;
        switch (item.result_type) {
            case ValidationResultType::SUCCESS: status = "✓"; break;
            case ValidationResultType::WARNING: status = "⚠"; break;
            case ValidationResultType::ERROR: status = "✗"; break;
            case ValidationResultType::CRITICAL: status = "⚠"; break;
        }
        std::cout << status << " " << item.test_name << ": " << item.message << "\n";
    }
}

void PlatformCompatibilityTestSuite::PrintBenchmarkReport(const PlatformBenchmarkReport& report) {
    std::cout << "\n=== 性能基准报告 ===\n";
    std::cout << "平台: " << report.platform_info.description << "\n";
    std::cout << "总体评分: " << report.overall_score << " (" << report.performance_grade << ")\n";
    
    std::cout << "\n基准测试结果:\n";
    for (const auto& benchmark : report.benchmarks) {
        std::cout << "• " << benchmark.benchmark_name << ":\n";
        std::cout << "  延迟: " << benchmark.average_latency_ns << "ns (平均)\n";
        std::cout << "  吞吐量: " << benchmark.throughput_ops_per_sec << " ops/s\n";
        std::cout << "  资源: CPU=" << benchmark.cpu_usage_percent << "%, 内存=" << benchmark.memory_usage_mb << "MB\n";
    }
}

bool PlatformCompatibilityTestSuite::SaveReportToFile(const std::string& content, const std::string& filename) {
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            LOG_ERROR(LogComponent::SYSTEM, "无法创建报告文件: " + filename);
            return false;
        }
        
        file << content;
        file.close();
        
        LOG_INFO(LogComponent::SYSTEM, "兼容性报告已保存到: " + filename);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(LogComponent::SYSTEM, "保存报告文件失败: " + std::string(e.what()));
        return false;
    }
}

} // namespace core
} // namespace timing_server