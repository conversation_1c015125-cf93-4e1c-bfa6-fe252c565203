#include "core/config_manager.h"
#include "core/serialization.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <chrono>
#include <algorithm>
#include <regex>

#ifdef __linux__
#include <sys/stat.h>
#include <unistd.h>
#elif __APPLE__
#include <sys/stat.h>
#include <unistd.h>
#endif

namespace timing_server {
namespace core {

// ConfigValidationResult实现

std::string ConfigValidationResult::GetSummary() const {
    std::ostringstream oss;
    
    if (!errors.empty()) {
        oss << "错误 (" << errors.size() << "):\n";
        for (size_t i = 0; i < errors.size(); ++i) {
            oss << "  " << (i + 1) << ". " << errors[i] << "\n";
        }
    }
    
    if (!warnings.empty()) {
        if (!errors.empty()) oss << "\n";
        oss << "警告 (" << warnings.size() << "):\n";
        for (size_t i = 0; i < warnings.size(); ++i) {
            oss << "  " << (i + 1) << ". " << warnings[i] << "\n";
        }
    }
    
    if (errors.empty() && warnings.empty()) {
        oss << "配置验证通过，无错误或警告。";
    }
    
    return oss.str();
}

// ConfigManager实现

ConfigManager::ConfigManager(const std::string& config_file_path)
    : config_file_path_(config_file_path)
    , last_file_modification_time_(0)
    , hot_reload_enabled_(false)
    , shutdown_requested_(false)
    , next_callback_id_(1) {
    
    // 设置备份文件路径
    backup_file_path_ = config_file_path_ + ".backup";
    
    // 初始化为默认配置
    current_config_ = GetDefaultConfig();
}

ConfigManager::~ConfigManager() {
    Shutdown();
}

bool ConfigManager::Initialize() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    // 检查配置文件是否存在
    if (!std::filesystem::exists(config_file_path_)) {
        // 创建默认配置文件
        if (!SaveConfig(GetDefaultConfig())) {
            return false;
        }
    }
    
    // 加载配置
    if (!LoadConfig()) {
        return false;
    }
    
    // 记录文件修改时间
    last_file_modification_time_ = GetFileModificationTime(config_file_path_);
    
    return true;
}

void ConfigManager::Shutdown() {
    // 停止热重载
    EnableHotReload(false);
    
    // 等待文件监控线程结束
    if (file_watcher_thread_ && file_watcher_thread_->joinable()) {
        shutdown_requested_ = true;
        file_watcher_thread_->join();
        file_watcher_thread_.reset();
    }
    
    // 清空回调
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    callbacks_.clear();
}

bool ConfigManager::LoadConfig() {
    std::ifstream file(config_file_path_);
    if (!file.is_open()) {
        return false;
    }
    
    // 读取文件内容
    std::string json_content((std::istreambuf_iterator<char>(file)),
                            std::istreambuf_iterator<char>());
    file.close();
    
    // 解析JSON配置
    TimingConfig new_config;
    if (!ParseJsonConfig(json_content, new_config)) {
        return false;
    }
    
    // 验证配置
    ConfigValidationResult validation = ValidateConfig(new_config);
    if (!validation.is_valid) {
        return false;
    }
    
    // 更新当前配置
    current_config_ = new_config;
    current_config_.last_modified = GetCurrentTimestampNs();
    
    return true;
}

bool ConfigManager::SaveConfig(const TimingConfig& config) {
    // 创建备份
    if (std::filesystem::exists(config_file_path_)) {
        CreateConfigBackup();
    }
    
    // 生成JSON内容
    std::string json_content = GenerateJsonConfig(config, true);
    
    // 写入文件
    std::ofstream file(config_file_path_);
    if (!file.is_open()) {
        return false;
    }
    
    file << json_content;
    file.close();
    
    // 更新文件修改时间
    last_file_modification_time_ = GetFileModificationTime(config_file_path_);
    
    return true;
}

TimingConfig ConfigManager::GetConfig() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return current_config_;
}

ConfigValidationResult ConfigManager::UpdateConfig(const TimingConfig& new_config) {
    // 验证新配置
    ConfigValidationResult validation = ValidateConfig(new_config);
    if (!validation.is_valid) {
        return validation;
    }
    
    TimingConfig old_config;
    {
        std::lock_guard<std::mutex> lock(config_mutex_);
        old_config = current_config_;
        current_config_ = new_config;
        current_config_.last_modified = GetCurrentTimestampNs();
    }
    
    // 保存到文件
    if (!SaveConfig(current_config_)) {
        // 保存失败，回滚配置
        std::lock_guard<std::mutex> lock(config_mutex_);
        current_config_ = old_config;
        validation.AddError("保存配置文件失败");
        return validation;
    }
    
    // 通知回调
    NotifyCallbacks(old_config, current_config_);
    
    return validation;
}

ConfigValidationResult ConfigManager::ValidateConfig(const TimingConfig& config) {
    ConfigValidationResult result;
    
    // 使用ConfigValidator进行详细验证
    ConfigValidator::ValidateTimeSourcePriorities(config.priorities, result);
    ConfigValidator::ValidateDiscipliningParameters(config.discipline, result);
    ConfigValidator::ValidateHoldoverParameters(config.holdover, result);
    ConfigValidator::ValidateAlarmThresholds(config.alarms, result);
    ConfigValidator::ValidateConfigConsistency(config, result);
    
    return result;
}

TimingConfig ConfigManager::GetDefaultConfig() {
    TimingConfig config;
    
    // 设置默认时间源优先级
    config.priorities.priorities[TimeSource::GNSS] = 1;
    config.priorities.priorities[TimeSource::RUBIDIUM] = 2;
    config.priorities.priorities[TimeSource::EXTERNAL_PPS] = 3;
    config.priorities.priorities[TimeSource::EXTERNAL_10MHZ] = 4;
    config.priorities.priorities[TimeSource::RTC] = 5;
    config.priorities.priorities[TimeSource::PHC] = 6;
    config.priorities.priorities[TimeSource::SYSTEM_CLOCK] = 7;
    config.priorities.auto_failover = true;
    config.priorities.failover_delay_ms = 5000;
    
    // 设置默认驯服参数
    config.discipline.convergence_threshold_ns = 50.0;
    config.discipline.convergence_time_s = 300;
    config.discipline.phase_gain = 0.1;
    config.discipline.frequency_gain = 0.01;
    config.discipline.measurement_interval_ms = 1000;
    
    // 设置默认守时参数
    config.holdover.max_holdover_hours = 24;
    config.holdover.frequency_drift_limit_ppm = 1.0;
    config.holdover.learning_duration_hours = 72;
    config.holdover.enable_temperature_compensation = true;
    
    // 设置默认告警阈值
    config.alarms.phase_offset_warning_ns = 100.0;
    config.alarms.phase_offset_critical_ns = 500.0;
    config.alarms.frequency_offset_warning_ppm = 0.01;
    config.alarms.frequency_offset_critical_ppm = 0.1;
    config.alarms.gnss_satellites_warning = 6;
    config.alarms.gnss_satellites_critical = 4;
    config.alarms.gnss_snr_warning_db = -145.0;
    config.alarms.gnss_snr_critical_db = -150.0;
    config.alarms.cpu_usage_warning = 80.0;
    config.alarms.memory_usage_warning = 80.0;
    config.alarms.temperature_warning = 70.0;
    config.alarms.temperature_critical = 75.0;
    
    // 设置版本信息
    config.config_version = "1.0.0";
    config.last_modified = GetCurrentTimestampNs();
    
    return config;
}

uint32_t ConfigManager::RegisterChangeCallback(const ConfigChangeCallback& callback) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    uint32_t callback_id = next_callback_id_++;
    callbacks_[callback_id] = callback;
    return callback_id;
}

void ConfigManager::UnregisterChangeCallback(uint32_t callback_id) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    callbacks_.erase(callback_id);
}

void ConfigManager::EnableHotReload(bool enable) {
    if (enable == hot_reload_enabled_.load()) {
        return; // 状态未改变
    }
    
    hot_reload_enabled_ = enable;
    
    if (enable) {
        // 启动文件监控线程
        shutdown_requested_ = false;
        file_watcher_thread_ = std::make_unique<std::thread>(&ConfigManager::FileWatcherThread, this);
    } else {
        // 停止文件监控线程
        shutdown_requested_ = true;
        if (file_watcher_thread_ && file_watcher_thread_->joinable()) {
            file_watcher_thread_->join();
            file_watcher_thread_.reset();
        }
    }
}

bool ConfigManager::IsConfigFileModified() const {
    uint64_t current_time = GetFileModificationTime(config_file_path_);
    return current_time > last_file_modification_time_;
}

bool ConfigManager::ResetToDefault() {
    TimingConfig default_config = GetDefaultConfig();
    ConfigValidationResult result = UpdateConfig(default_config);
    return result.is_valid;
}

std::string ConfigManager::ExportConfigJson(bool pretty_print) const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return GenerateJsonConfig(current_config_, pretty_print);
}

ConfigValidationResult ConfigManager::ImportConfigJson(const std::string& json_str) {
    TimingConfig new_config;
    ConfigValidationResult result;
    
    if (!ParseJsonConfig(json_str, new_config)) {
        result.AddError("JSON格式解析失败");
        return result;
    }
    
    return UpdateConfig(new_config);
}

// 私有方法实现

void ConfigManager::FileWatcherThread() {
    while (!shutdown_requested_) {
        if (IsConfigFileModified()) {
            // 文件已修改，重新加载配置
            TimingConfig old_config;
            {
                std::lock_guard<std::mutex> lock(config_mutex_);
                old_config = current_config_;
            }
            
            if (LoadConfig()) {
                TimingConfig new_config = GetConfig();
                NotifyCallbacks(old_config, new_config);
                last_file_modification_time_ = GetFileModificationTime(config_file_path_);
            }
        }
        
        // 每秒检查一次
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void ConfigManager::NotifyCallbacks(const TimingConfig& old_config, const TimingConfig& new_config) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    for (const auto& callback_pair : callbacks_) {
        try {
            callback_pair.second(old_config, new_config);
        } catch (const std::exception& e) {
            // 忽略回调异常，避免影响其他回调
        }
    }
}

uint64_t ConfigManager::GetFileModificationTime(const std::string& file_path) {
    struct stat file_stat;
    if (stat(file_path.c_str(), &file_stat) == 0) {
        return static_cast<uint64_t>(file_stat.st_mtime) * 1000000000ULL; // 转换为纳秒
    }
    return 0;
}

bool ConfigManager::CreateConfigBackup() {
    try {
        std::filesystem::copy_file(config_file_path_, backup_file_path_, 
                                 std::filesystem::copy_options::overwrite_existing);
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

bool ConfigManager::RestoreFromBackup() {
    try {
        if (std::filesystem::exists(backup_file_path_)) {
            std::filesystem::copy_file(backup_file_path_, config_file_path_, 
                                     std::filesystem::copy_options::overwrite_existing);
            return LoadConfig();
        }
    } catch (const std::exception& e) {
        // 忽略异常
    }
    return false;
}

bool ConfigManager::ParseJsonConfig(const std::string& json_content, TimingConfig& config) {
    try {
        // 使用JsonSerializer反序列化配置
        config = JsonSerializer::DeserializeTimingConfig(json_content);
        return true;
    } catch (const std::exception& e) {
        return false;
    }
}

std::string ConfigManager::GenerateJsonConfig(const TimingConfig& config, bool pretty_print) {
    // 使用JsonSerializer序列化配置
    std::string json = JsonSerializer::SerializeTimingConfig(config);
    
    if (pretty_print) {
        // 简单的格式化（在实际项目中建议使用专业的JSON库）
        std::string formatted;
        int indent_level = 0;
        bool in_string = false;
        bool escape_next = false;
        
        for (char c : json) {
            if (escape_next) {
                formatted += c;
                escape_next = false;
                continue;
            }
            
            if (c == '\\' && in_string) {
                formatted += c;
                escape_next = true;
                continue;
            }
            
            if (c == '"') {
                in_string = !in_string;
                formatted += c;
                continue;
            }
            
            if (in_string) {
                formatted += c;
                continue;
            }
            
            switch (c) {
                case '{':
                case '[':
                    formatted += c;
                    formatted += '\n';
                    indent_level++;
                    formatted += std::string(indent_level * 2, ' ');
                    break;
                case '}':
                case ']':
                    formatted += '\n';
                    indent_level--;
                    formatted += std::string(indent_level * 2, ' ');
                    formatted += c;
                    break;
                case ',':
                    formatted += c;
                    formatted += '\n';
                    formatted += std::string(indent_level * 2, ' ');
                    break;
                case ':':
                    formatted += c;
                    formatted += ' ';
                    break;
                default:
                    formatted += c;
                    break;
            }
        }
        
        return formatted;
    }
    
    return json;
}

// ConfigValidator实现

void ConfigValidator::ValidateTimeSourcePriorities(const TimeSourcePriority& priorities, ConfigValidationResult& result) {
    // 检查是否有重复的优先级
    std::map<uint32_t, std::vector<TimeSource>> priority_map;
    for (const auto& priority_pair : priorities.priorities) {
        priority_map[priority_pair.second].push_back(priority_pair.first);
    }
    
    for (const auto& priority_group : priority_map) {
        if (priority_group.second.size() > 1) {
            std::ostringstream oss;
            oss << "优先级 " << priority_group.first << " 被多个时间源使用: ";
            for (size_t i = 0; i < priority_group.second.size(); ++i) {
                if (i > 0) oss << ", ";
                oss << TimeSourceToString(priority_group.second[i]);
            }
            result.AddError(oss.str());
        }
    }
    
    // 检查优先级范围
    for (const auto& priority_pair : priorities.priorities) {
        if (priority_pair.second == 0 || priority_pair.second > 100) {
            result.AddError("时间源 " + TimeSourceToString(priority_pair.first) + 
                          " 的优先级必须在1-100之间");
        }
    }
    
    // 检查故障切换延迟
    ValidateRange(priorities.failover_delay_ms, 1000u, 60000u, "故障切换延迟", result);
}

void ConfigValidator::ValidateDiscipliningParameters(const DiscipliningParameters& discipline, ConfigValidationResult& result) {
    ValidatePositive(discipline.convergence_threshold_ns, "收敛阈值", result);
    ValidateRange(discipline.convergence_threshold_ns, 1.0, 1000.0, "收敛阈值", result);
    
    ValidateRange(discipline.convergence_time_s, 60u, 3600u, "收敛时间", result);
    
    ValidateRange(discipline.phase_gain, 0.001, 1.0, "相位增益", result);
    ValidateRange(discipline.frequency_gain, 0.0001, 0.1, "频率增益", result);
    
    ValidateRange(discipline.measurement_interval_ms, 100u, 10000u, "测量间隔", result);
}

void ConfigValidator::ValidateHoldoverParameters(const HoldoverParameters& holdover, ConfigValidationResult& result) {
    ValidateRange(holdover.max_holdover_hours, 1u, 168u, "最大守时时间", result); // 最多7天
    
    ValidatePositive(holdover.frequency_drift_limit_ppm, "频率漂移限制", result);
    ValidateRange(holdover.frequency_drift_limit_ppm, 0.001, 10.0, "频率漂移限制", result);
    
    ValidateRange(holdover.learning_duration_hours, 24u, 720u, "学习时间", result); // 最多30天
}

void ConfigValidator::ValidateAlarmThresholds(const AlarmThresholds& alarms, ConfigValidationResult& result) {
    // 验证相位偏移阈值
    ValidatePositive(alarms.phase_offset_warning_ns, "相位偏移警告阈值", result);
    ValidatePositive(alarms.phase_offset_critical_ns, "相位偏移严重阈值", result);
    
    if (alarms.phase_offset_critical_ns <= alarms.phase_offset_warning_ns) {
        result.AddError("相位偏移严重阈值必须大于警告阈值");
    }
    
    // 验证频率偏移阈值
    ValidatePositive(alarms.frequency_offset_warning_ppm, "频率偏移警告阈值", result);
    ValidatePositive(alarms.frequency_offset_critical_ppm, "频率偏移严重阈值", result);
    
    if (alarms.frequency_offset_critical_ppm <= alarms.frequency_offset_warning_ppm) {
        result.AddError("频率偏移严重阈值必须大于警告阈值");
    }
    
    // 验证GNSS卫星数阈值
    ValidateRange(alarms.gnss_satellites_warning, 4u, 20u, "GNSS卫星数警告阈值", result);
    ValidateRange(alarms.gnss_satellites_critical, 3u, 20u, "GNSS卫星数严重阈值", result);
    
    if (alarms.gnss_satellites_critical >= alarms.gnss_satellites_warning) {
        result.AddError("GNSS卫星数严重阈值必须小于警告阈值");
    }
    
    // 验证信噪比阈值
    ValidateRange(alarms.gnss_snr_warning_db, -160.0, -130.0, "GNSS信噪比警告阈值", result);
    ValidateRange(alarms.gnss_snr_critical_db, -160.0, -130.0, "GNSS信噪比严重阈值", result);
    
    if (alarms.gnss_snr_critical_db >= alarms.gnss_snr_warning_db) {
        result.AddError("GNSS信噪比严重阈值必须小于警告阈值");
    }
    
    // 验证系统资源阈值
    ValidateRange(alarms.cpu_usage_warning, 50.0, 95.0, "CPU使用率警告阈值", result);
    ValidateRange(alarms.memory_usage_warning, 50.0, 95.0, "内存使用率警告阈值", result);
    
    // 验证温度阈值
    ValidateRange(alarms.temperature_warning, 40.0, 80.0, "温度警告阈值", result);
    ValidateRange(alarms.temperature_critical, 50.0, 85.0, "温度严重阈值", result);
    
    if (alarms.temperature_critical <= alarms.temperature_warning) {
        result.AddError("温度严重阈值必须大于警告阈值");
    }
}

void ConfigValidator::ValidateConfigConsistency(const TimingConfig& config, ConfigValidationResult& result) {
    // 检查驯服收敛阈值与告警阈值的一致性
    if (config.discipline.convergence_threshold_ns > config.alarms.phase_offset_warning_ns) {
        result.AddWarning("驯服收敛阈值大于相位偏移警告阈值，可能导致频繁告警");
    }
    
    // 检查守时时间与学习时间的关系
    if (config.holdover.learning_duration_hours < config.holdover.max_holdover_hours) {
        result.AddWarning("学习时间短于最大守时时间，可能影响守时精度");
    }
    
    // 检查时间源优先级配置的完整性
    std::vector<TimeSource> required_sources = {
        TimeSource::GNSS, TimeSource::RUBIDIUM, TimeSource::RTC
    };
    
    for (TimeSource source : required_sources) {
        if (config.priorities.priorities.find(source) == config.priorities.priorities.end()) {
            result.AddWarning("缺少 " + TimeSourceToString(source) + " 时间源的优先级配置");
        }
    }
}

template<typename T>
void ConfigValidator::ValidateRange(T value, T min_val, T max_val, const std::string& field_name, ConfigValidationResult& result) {
    if (value < min_val || value > max_val) {
        std::ostringstream oss;
        oss << field_name << " 值 " << value << " 超出有效范围 [" << min_val << ", " << max_val << "]";
        result.AddError(oss.str());
    }
}

template<typename T>
void ConfigValidator::ValidatePositive(T value, const std::string& field_name, ConfigValidationResult& result) {
    if (value <= 0) {
        result.AddError(field_name + " 必须为正数");
    }
}

} // namespace core
} // namespace timing_server