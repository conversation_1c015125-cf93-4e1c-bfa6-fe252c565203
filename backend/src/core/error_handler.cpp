#include "core/error_handler.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>
#include <cstring>
#include <cstdlib>

#ifdef __linux__
#include <sys/sysinfo.h>
#include <sys/statvfs.h>
#include <unistd.h>
#include <signal.h>
#include <sys/wait.h>
#elif __APPLE__
#include <sys/types.h>
#include <sys/sysctl.h>
#include <sys/mount.h>
#include <unistd.h>
#include <signal.h>
#include <sys/wait.h>
#endif

namespace timing_server {
namespace core {

// ErrorInfo静态方法实现
uint64_t ErrorInfo::GenerateErrorId() {
    static std::atomic<uint64_t> counter(1);
    return counter.fetch_add(1);
}

// RetryRecoveryHandler实现
RetryRecoveryHandler::RetryRecoveryHandler(const ErrorRecoveryConfig& config)
    : config_(config) {
}

bool RetryRecoveryHandler::HandleRecovery(ErrorInfo& error_info) {
    if (error_info.retry_count >= config_.max_retry_count) {
        LOG_WARNING(LogComponent::SYSTEM, 
                   "重试次数已达上限，错误ID: " + std::to_string(error_info.error_id));
        return false;
    }
    
    // 检查重试间隔
    uint64_t current_time = GetCurrentTimestampNs();
    uint64_t interval_ns = static_cast<uint64_t>(config_.retry_interval_ms) * 1000000ULL;
    
    if (error_info.last_retry_time_ns > 0 && 
        (current_time - error_info.last_retry_time_ns) < interval_ns) {
        return false; // 还未到重试时间
    }
    
    error_info.retry_count++;
    error_info.last_retry_time_ns = current_time;
    error_info.status = ErrorStatus::IN_PROGRESS;
    
    LOG_INFO(LogComponent::SYSTEM, 
             "开始重试恢复，错误ID: " + std::to_string(error_info.error_id) + 
             "，重试次数: " + std::to_string(error_info.retry_count));
    
    // 根据错误类型执行不同的重试逻辑
    bool success = false;
    switch (error_info.type) {
        case ErrorType::GNSS_SIGNAL_LOST:
            // 重新初始化GNSS接收机
            success = RetryGnssInitialization();
            break;
            
        case ErrorType::NETWORK_TIMEOUT:
            // 重新建立网络连接
            success = RetryNetworkConnection();
            break;
            
        case ErrorType::CONFIG_ERROR:
            // 重新加载配置
            success = RetryConfigLoad();
            break;
            
        case ErrorType::API_ERROR:
            // 重启API服务
            success = RetryApiService();
            break;
            
        default:
            LOG_WARNING(LogComponent::SYSTEM, 
                       "不支持的重试错误类型: " + ErrorTypeToString(error_info.type));
            break;
    }
    
    if (success) {
        error_info.status = ErrorStatus::RESOLVED;
        error_info.resolved_time_ns = current_time;
        error_info.resolution_details = "通过重试成功恢复";
        
        LOG_INFO(LogComponent::SYSTEM, 
                 "重试恢复成功，错误ID: " + std::to_string(error_info.error_id));
    } else {
        LOG_WARNING(LogComponent::SYSTEM, 
                   "重试恢复失败，错误ID: " + std::to_string(error_info.error_id));
    }
    
    return success;
}

bool RetryRecoveryHandler::CanHandle(ErrorType error_type) const {
    static const std::vector<ErrorType> supported_types = {
        ErrorType::GNSS_SIGNAL_LOST,
        ErrorType::NETWORK_TIMEOUT,
        ErrorType::CONFIG_ERROR,
        ErrorType::API_ERROR,
        ErrorType::DATABASE_ERROR
    };
    
    return std::find(supported_types.begin(), supported_types.end(), error_type) 
           != supported_types.end();
}

bool RetryRecoveryHandler::RetryGnssInitialization() {
    // 模拟GNSS重新初始化
    LOG_INFO(LogComponent::HAL_GNSS, "重新初始化GNSS接收机");
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    return true; // 简化实现，实际应该调用HAL接口
}

bool RetryRecoveryHandler::RetryNetworkConnection() {
    // 模拟网络连接重试
    LOG_INFO(LogComponent::HAL_NETWORK, "重新建立网络连接");
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    return true; // 简化实现
}

bool RetryRecoveryHandler::RetryConfigLoad() {
    // 模拟配置重新加载
    LOG_INFO(LogComponent::CONFIG_MANAGER, "重新加载配置文件");
    return true; // 简化实现
}

bool RetryRecoveryHandler::RetryApiService() {
    // 模拟API服务重启
    LOG_INFO(LogComponent::API_SERVER, "重启API服务");
    return true; // 简化实现
}

// FailoverRecoveryHandler实现
FailoverRecoveryHandler::FailoverRecoveryHandler(const ErrorRecoveryConfig& config)
    : config_(config) {
}

bool FailoverRecoveryHandler::HandleRecovery(ErrorInfo& error_info) {
    error_info.status = ErrorStatus::IN_PROGRESS;
    
    LOG_INFO(LogComponent::SYSTEM, 
             "开始故障切换恢复，错误ID: " + std::to_string(error_info.error_id));
    
    bool success = false;
    switch (error_info.type) {
        case ErrorType::GNSS_SIGNAL_LOST:
        case ErrorType::RUBIDIUM_FAULT:
        case ErrorType::RTC_FAULT:
            success = PerformTimeSourceFailover(error_info);
            break;
            
        case ErrorType::NETWORK_INTERFACE_DOWN:
            success = PerformNetworkInterfaceFailover(error_info);
            break;
            
        case ErrorType::DAEMON_CRASH:
            success = PerformServiceFailover(error_info);
            break;
            
        default:
            LOG_WARNING(LogComponent::SYSTEM, 
                       "不支持的故障切换错误类型: " + ErrorTypeToString(error_info.type));
            break;
    }
    
    if (success) {
        error_info.status = ErrorStatus::RESOLVED;
        error_info.resolved_time_ns = GetCurrentTimestampNs();
        error_info.resolution_details = "通过故障切换成功恢复";
        
        LOG_INFO(LogComponent::SYSTEM, 
                 "故障切换恢复成功，错误ID: " + std::to_string(error_info.error_id));
    } else {
        error_info.status = ErrorStatus::FAILED;
        LOG_ERROR(LogComponent::SYSTEM, 
                 "故障切换恢复失败，错误ID: " + std::to_string(error_info.error_id));
    }
    
    return success;
}

bool FailoverRecoveryHandler::CanHandle(ErrorType error_type) const {
    static const std::vector<ErrorType> supported_types = {
        ErrorType::GNSS_SIGNAL_LOST,
        ErrorType::RUBIDIUM_FAULT,
        ErrorType::RTC_FAULT,
        ErrorType::NETWORK_INTERFACE_DOWN,
        ErrorType::DAEMON_CRASH
    };
    
    return std::find(supported_types.begin(), supported_types.end(), error_type) 
           != supported_types.end();
}

bool FailoverRecoveryHandler::PerformTimeSourceFailover(const ErrorInfo& error_info) {
    LOG_INFO(LogComponent::TIMING_ENGINE, "执行时间源故障切换");
    
    // 模拟时间源切换逻辑
    // 实际实现应该调用TimingEngine的故障切换接口
    switch (error_info.type) {
        case ErrorType::GNSS_SIGNAL_LOST:
            LOG_INFO(LogComponent::TIMING_ENGINE, "从GNSS切换到铷钟");
            break;
        case ErrorType::RUBIDIUM_FAULT:
            LOG_INFO(LogComponent::TIMING_ENGINE, "从铷钟切换到RTC");
            break;
        case ErrorType::RTC_FAULT:
            LOG_INFO(LogComponent::TIMING_ENGINE, "从RTC切换到系统时钟");
            break;
        default:
            return false;
    }
    
    return true; // 简化实现
}

bool FailoverRecoveryHandler::PerformNetworkInterfaceFailover(const ErrorInfo& error_info) {
    LOG_INFO(LogComponent::HAL_NETWORK, "执行网络接口故障切换");
    
    // 模拟网络接口切换
    // 实际实现应该调用网络HAL的接口切换功能
    return true; // 简化实现
}

bool FailoverRecoveryHandler::PerformServiceFailover(const ErrorInfo& error_info) {
    LOG_INFO(LogComponent::DAEMON_MANAGER, "执行服务故障切换");
    
    // 模拟服务切换
    // 实际实现应该启动备用服务实例
    return true; // 简化实现
}

// RestartRecoveryHandler实现
RestartRecoveryHandler::RestartRecoveryHandler(const ErrorRecoveryConfig& config)
    : config_(config) {
}

bool RestartRecoveryHandler::HandleRecovery(ErrorInfo& error_info) {
    error_info.status = ErrorStatus::IN_PROGRESS;
    
    LOG_INFO(LogComponent::SYSTEM, 
             "开始重启恢复，错误ID: " + std::to_string(error_info.error_id));
    
    bool success = false;
    switch (error_info.component) {
        case LogComponent::PTP4L:
        case LogComponent::CHRONY:
        case LogComponent::TS2PHC:
            success = RestartDaemon(error_info.component);
            break;
            
        case LogComponent::HAL_GNSS:
        case LogComponent::HAL_RUBIDIUM:
        case LogComponent::HAL_RTC:
        case LogComponent::HAL_PPS:
        case LogComponent::HAL_FREQ:
        case LogComponent::HAL_NETWORK:
            success = RestartHardwareInterface(error_info.component);
            break;
            
        case LogComponent::API_SERVER:
        case LogComponent::WEBSOCKET:
            success = RestartService("timing-server-api");
            break;
            
        default:
            LOG_WARNING(LogComponent::SYSTEM, 
                       "不支持的重启组件: " + LogComponentToString(error_info.component));
            break;
    }
    
    if (success) {
        error_info.status = ErrorStatus::RESOLVED;
        error_info.resolved_time_ns = GetCurrentTimestampNs();
        error_info.resolution_details = "通过重启成功恢复";
        
        LOG_INFO(LogComponent::SYSTEM, 
                 "重启恢复成功，错误ID: " + std::to_string(error_info.error_id));
    } else {
        error_info.status = ErrorStatus::FAILED;
        LOG_ERROR(LogComponent::SYSTEM, 
                 "重启恢复失败，错误ID: " + std::to_string(error_info.error_id));
    }
    
    return success;
}

bool RestartRecoveryHandler::CanHandle(ErrorType error_type) const {
    static const std::vector<ErrorType> supported_types = {
        ErrorType::DAEMON_CRASH,
        ErrorType::API_ERROR,
        ErrorType::HARDWARE_FAULT,
        ErrorType::CONFIG_ERROR
    };
    
    return std::find(supported_types.begin(), supported_types.end(), error_type) 
           != supported_types.end();
}

bool RestartRecoveryHandler::RestartDaemon(LogComponent component) {
    std::string daemon_name;
    switch (component) {
        case LogComponent::PTP4L:
            daemon_name = "ptp4l";
            break;
        case LogComponent::CHRONY:
            daemon_name = "chrony";
            break;
        case LogComponent::TS2PHC:
            daemon_name = "ts2phc";
            break;
        default:
            return false;
    }
    
    LOG_INFO(component, "重启守护进程: " + daemon_name);
    
    // 模拟守护进程重启
    // 实际实现应该调用DaemonManager的重启接口
    return true; // 简化实现
}

bool RestartRecoveryHandler::RestartService(const std::string& service_name) {
    LOG_INFO(LogComponent::SYSTEM, "重启服务: " + service_name);
    
    // 模拟服务重启
    // 实际实现应该使用systemctl或其他服务管理工具
    return true; // 简化实现
}

bool RestartRecoveryHandler::RestartHardwareInterface(LogComponent component) {
    LOG_INFO(component, "重启硬件接口: " + LogComponentToString(component));
    
    // 模拟硬件接口重启
    // 实际实现应该调用相应HAL的重启接口
    return true; // 简化实现
}

// SystemHealthMonitor实现
SystemHealthMonitor::SystemHealthMonitor(const HealthMonitorConfig& config)
    : config_(config), running_(false), current_health_(SystemHealth::HEALTHY) {
}

SystemHealthMonitor::~SystemHealthMonitor() {
    Stop();
}

bool SystemHealthMonitor::Start() {
    if (running_.load()) {
        return true;
    }
    
    running_ = true;
    monitor_thread_ = std::make_unique<std::thread>(&SystemHealthMonitor::MonitorThreadFunc, this);
    
    LOG_INFO(LogComponent::SYSTEM, "系统健康监控已启动");
    return true;
}

void SystemHealthMonitor::Stop() {
    if (!running_.load()) {
        return;
    }
    
    running_ = false;
    
    if (monitor_thread_ && monitor_thread_->joinable()) {
        monitor_thread_->join();
        monitor_thread_.reset();
    }
    
    LOG_INFO(LogComponent::SYSTEM, "系统健康监控已停止");
}

SystemHealth SystemHealthMonitor::GetSystemHealth() const {
    return current_health_.load();
}

std::map<std::string, std::string> SystemHealthMonitor::GetHealthReport() const {
    std::lock_guard<std::mutex> lock(health_data_mutex_);
    return health_data_;
}

void SystemHealthMonitor::RegisterHealthCheckCallback(
    const std::function<void(SystemHealth)>& callback) {
    health_callbacks_.push_back(callback);
}

void SystemHealthMonitor::MonitorThreadFunc() {
    while (running_.load()) {
        try {
            // 检查各项健康指标
            double cpu_usage = CheckCpuUsage();
            double memory_usage = CheckMemoryUsage();
            double disk_usage = CheckDiskUsage();
            double temperature = CheckSystemTemperature();
            bool network_ok = CheckNetworkConnectivity();
            
            // 更新健康数据
            {
                std::lock_guard<std::mutex> lock(health_data_mutex_);
                health_data_["cpu_usage"] = std::to_string(cpu_usage);
                health_data_["memory_usage"] = std::to_string(memory_usage);
                health_data_["disk_usage"] = std::to_string(disk_usage);
                health_data_["temperature"] = std::to_string(temperature);
                health_data_["network_status"] = network_ok ? "OK" : "FAILED";
                health_data_["last_check"] = std::to_string(GetCurrentTimestampNs());
            }
            
            // 检查监控的进程
            for (const auto& process : config_.monitored_processes) {
                bool process_ok = CheckProcessStatus(process);
                std::lock_guard<std::mutex> lock(health_data_mutex_);
                health_data_["process_" + process] = process_ok ? "RUNNING" : "STOPPED";
            }
            
            // 更新系统健康状态
            UpdateSystemHealth();
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, 
                     "健康监控检查异常: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.check_interval_ms));
    }
}

double SystemHealthMonitor::CheckCpuUsage() {
    // 简化的CPU使用率检查
    // 实际实现应该读取/proc/stat或使用系统API
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_real_distribution<> dis(10.0, 30.0);
    
    return dis(gen); // 模拟CPU使用率
}

double SystemHealthMonitor::CheckMemoryUsage() {
    // 简化的内存使用率检查
#ifdef __linux__
    struct sysinfo info;
    if (sysinfo(&info) == 0) {
        double total = static_cast<double>(info.totalram);
        double free = static_cast<double>(info.freeram);
        return ((total - free) / total) * 100.0;
    }
#elif __APPLE__
    int mib[2];
    size_t length;
    
    mib[0] = CTL_HW;
    mib[1] = HW_MEMSIZE;
    length = sizeof(uint64_t);
    uint64_t total_memory;
    
    if (sysctl(mib, 2, &total_memory, &length, NULL, 0) == 0) {
        // 简化实现，返回模拟值
        return 45.0;
    }
#endif
    
    return 50.0; // 默认值
}

double SystemHealthMonitor::CheckDiskUsage() {
    // 简化的磁盘使用率检查
#ifdef __linux__
    struct statvfs stat;
    if (statvfs("/", &stat) == 0) {
        double total = static_cast<double>(stat.f_blocks * stat.f_frsize);
        double free = static_cast<double>(stat.f_bavail * stat.f_frsize);
        return ((total - free) / total) * 100.0;
    }
#elif __APPLE__
    struct statfs stat;
    if (statfs("/", &stat) == 0) {
        double total = static_cast<double>(stat.f_blocks * stat.f_bsize);
        double free = static_cast<double>(stat.f_bavail * stat.f_bsize);
        return ((total - free) / total) * 100.0;
    }
#endif
    
    return 25.0; // 默认值
}

double SystemHealthMonitor::CheckSystemTemperature() {
    // 简化的温度检查
    // 实际实现应该读取硬件传感器
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_real_distribution<> dis(45.0, 55.0);
    
    return dis(gen); // 模拟温度
}

bool SystemHealthMonitor::CheckNetworkConnectivity() {
    // 简化的网络连接检查
    // 实际实现应该ping网关或DNS服务器
    return true; // 模拟网络正常
}

bool SystemHealthMonitor::CheckProcessStatus(const std::string& process_name) {
    // 简化的进程状态检查
    // 实际实现应该检查进程是否在运行
    std::string command = "pgrep " + process_name + " > /dev/null 2>&1";
    int result = std::system(command.c_str());
    return result == 0;
}

void SystemHealthMonitor::UpdateSystemHealth() {
    SystemHealth new_health = SystemHealth::HEALTHY;
    
    std::lock_guard<std::mutex> lock(health_data_mutex_);
    
    // 检查CPU使用率
    double cpu_usage = std::stod(health_data_["cpu_usage"]);
    if (cpu_usage > config_.cpu_threshold_percent) {
        new_health = std::max(new_health, SystemHealth::WARNING);
    }
    
    // 检查内存使用率
    double memory_usage = std::stod(health_data_["memory_usage"]);
    if (memory_usage > config_.memory_threshold_percent) {
        new_health = std::max(new_health, SystemHealth::WARNING);
    }
    
    // 检查温度
    double temperature = std::stod(health_data_["temperature"]);
    if (temperature > config_.temperature_threshold_celsius) {
        new_health = std::max(new_health, SystemHealth::ERROR);
    }
    
    // 检查网络状态
    if (health_data_["network_status"] != "OK") {
        new_health = std::max(new_health, SystemHealth::ERROR);
    }
    
    // 检查关键进程
    for (const auto& process : config_.monitored_processes) {
        if (health_data_["process_" + process] != "RUNNING") {
            new_health = std::max(new_health, SystemHealth::CRITICAL);
        }
    }
    
    // 更新健康状态
    SystemHealth old_health = current_health_.exchange(new_health);
    if (old_health != new_health) {
        NotifyHealthChange(new_health);
    }
}

void SystemHealthMonitor::NotifyHealthChange(SystemHealth new_health) {
    LOG_INFO(LogComponent::SYSTEM, 
             "系统健康状态变化: " + SystemHealthToString(new_health));
    
    for (const auto& callback : health_callbacks_) {
        try {
            callback(new_health);
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::SYSTEM, 
                     "健康状态回调异常: " + std::string(e.what()));
        }
    }
}

// ErrorHandler实现
ErrorHandler& ErrorHandler::GetInstance() {
    static ErrorHandler instance;
    return instance;
}

ErrorHandler::~ErrorHandler() {
    Shutdown();
}

bool ErrorHandler::Initialize(const std::string& config_file_path) {
    // 初始化默认配置
    max_history_size_ = 10000;
    auto_recovery_enabled_ = true;
    shutdown_requested_ = false;
    
    // 加载配置文件
    if (!config_file_path.empty()) {
        LoadConfig(config_file_path);
    }
    
    // 初始化默认恢复处理器
    InitializeDefaultHandlers();
    
    // 初始化默认恢复配置
    InitializeDefaultRecoveryConfigs();
    
    // 启动恢复处理线程
    recovery_thread_ = std::make_unique<std::thread>(&ErrorHandler::RecoveryThreadFunc, this);
    
    // 初始化健康监控器
    HealthMonitorConfig health_config;
    health_config.check_interval_ms = 5000;
    health_config.cpu_threshold_percent = 80;
    health_config.memory_threshold_percent = 80;
    health_config.temperature_threshold_celsius = 70.0;
    health_config.enable_proactive_monitoring = true;
    health_config.monitored_processes = {"ptp4l", "chrony", "ts2phc"};
    
    health_monitor_ = std::make_unique<SystemHealthMonitor>(health_config);
    health_monitor_->Start();
    
    LOG_INFO(LogComponent::SYSTEM, "错误处理器初始化完成");
    return true;
}

void ErrorHandler::Shutdown() {
    shutdown_requested_ = true;
    
    // 停止健康监控
    if (health_monitor_) {
        health_monitor_->Stop();
        health_monitor_.reset();
    }
    
    // 停止恢复处理线程
    if (recovery_thread_ && recovery_thread_->joinable()) {
        queue_condition_.notify_all();
        recovery_thread_->join();
        recovery_thread_.reset();
    }
    
    LOG_INFO(LogComponent::SYSTEM, "错误处理器已关闭");
}

uint64_t ErrorHandler::ReportError(ErrorType error_type, ErrorSeverity severity,
                                  LogComponent component, const std::string& message,
                                  const std::string& details,
                                  const std::map<std::string, std::string>& context) {
    
    auto error_info = std::make_shared<ErrorInfo>(error_type, severity, component, message, details);
    error_info->context = context;
    
    // 设置恢复策略
    auto config_it = recovery_configs_.find(error_type);
    if (config_it != recovery_configs_.end()) {
        error_info->recovery_strategy = config_it->second.strategy;
    }
    
    {
        std::lock_guard<std::mutex> lock(errors_mutex_);
        active_errors_[error_info->error_id] = error_info;
        error_history_.push_back(error_info);
        
        // 限制历史记录大小
        if (error_history_.size() > max_history_size_) {
            error_history_.erase(error_history_.begin());
        }
    }
    
    // 记录日志
    LOG_ERROR(component, "错误报告 [" + ErrorTypeToString(error_type) + "] " + 
              ErrorSeverityToString(severity) + ": " + message);
    
    // 发送告警通知
    SendAlarmNotification(*error_info);
    
    // 如果启用自动恢复，加入恢复队列
    if (auto_recovery_enabled_.load() && 
        error_info->recovery_strategy != RecoveryStrategy::MANUAL_INTERVENTION) {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        recovery_queue_.push(error_info->error_id);
        queue_condition_.notify_one();
    }
    
    return error_info->error_id;
}

bool ErrorHandler::AcknowledgeError(uint64_t error_id, const std::string& user_id) {
    std::lock_guard<std::mutex> lock(errors_mutex_);
    
    auto it = active_errors_.find(error_id);
    if (it != active_errors_.end()) {
        it->second->status = ErrorStatus::ACKNOWLEDGED;
        LOG_INFO(LogComponent::SYSTEM, 
                 "错误已确认，ID: " + std::to_string(error_id) + 
                 "，用户: " + user_id);
        return true;
    }
    
    return false;
}

bool ErrorHandler::ResolveError(uint64_t error_id, const std::string& resolution_details) {
    std::lock_guard<std::mutex> lock(errors_mutex_);
    
    auto it = active_errors_.find(error_id);
    if (it != active_errors_.end()) {
        it->second->status = ErrorStatus::RESOLVED;
        it->second->resolved_time_ns = GetCurrentTimestampNs();
        it->second->resolution_details = resolution_details;
        
        LOG_INFO(LogComponent::SYSTEM, 
                 "错误已解决，ID: " + std::to_string(error_id) + 
                 "，详情: " + resolution_details);
        
        // 从活跃错误列表中移除
        active_errors_.erase(it);
        return true;
    }
    
    return false;
}

std::shared_ptr<ErrorInfo> ErrorHandler::GetError(uint64_t error_id) const {
    std::lock_guard<std::mutex> lock(errors_mutex_);
    
    auto it = active_errors_.find(error_id);
    if (it != active_errors_.end()) {
        return it->second;
    }
    
    // 在历史记录中查找
    for (const auto& error : error_history_) {
        if (error->error_id == error_id) {
            return error;
        }
    }
    
    return nullptr;
}

std::vector<std::shared_ptr<ErrorInfo>> ErrorHandler::GetActiveErrors(
    ErrorSeverity severity_filter) const {
    
    std::lock_guard<std::mutex> lock(errors_mutex_);
    
    std::vector<std::shared_ptr<ErrorInfo>> result;
    for (const auto& pair : active_errors_) {
        if (pair.second->severity >= severity_filter) {
            result.push_back(pair.second);
        }
    }
    
    return result;
}

std::vector<std::shared_ptr<ErrorInfo>> ErrorHandler::GetErrorHistory(
    uint64_t start_time, uint64_t end_time, size_t max_count) const {
    
    std::lock_guard<std::mutex> lock(errors_mutex_);
    
    std::vector<std::shared_ptr<ErrorInfo>> result;
    result.reserve(std::min(max_count, error_history_.size()));
    
    for (const auto& error : error_history_) {
        if (error->timestamp_ns >= start_time && error->timestamp_ns <= end_time) {
            result.push_back(error);
            if (result.size() >= max_count) {
                break;
            }
        }
    }
    
    return result;
}

void ErrorHandler::RegisterRecoveryHandler(std::unique_ptr<IErrorRecoveryHandler> handler) {
    recovery_handlers_.push_back(std::move(handler));
    LOG_INFO(LogComponent::SYSTEM, "注册恢复处理器: " + recovery_handlers_.back()->GetHandlerName());
}

void ErrorHandler::SetRecoveryConfig(ErrorType error_type, const ErrorRecoveryConfig& config) {
    recovery_configs_[error_type] = config;
}

void ErrorHandler::SetAutoRecoveryEnabled(bool enable) {
    auto_recovery_enabled_ = enable;
    LOG_INFO(LogComponent::SYSTEM, 
             "自动恢复" + std::string(enable ? "已启用" : "已禁用"));
}

SystemHealthMonitor& ErrorHandler::GetHealthMonitor() {
    return *health_monitor_;
}

bool ErrorHandler::TriggerManualRecovery(uint64_t error_id) {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    recovery_queue_.push(error_id);
    queue_condition_.notify_one();
    
    LOG_INFO(LogComponent::SYSTEM, 
             "触发手动恢复，错误ID: " + std::to_string(error_id));
    return true;
}

std::map<std::string, uint64_t> ErrorHandler::GetErrorStatistics() const {
    std::lock_guard<std::mutex> lock(errors_mutex_);
    
    std::map<std::string, uint64_t> stats;
    stats["total_errors"] = error_history_.size();
    stats["active_errors"] = active_errors_.size();
    
    // 按类型统计
    std::map<ErrorType, uint64_t> type_counts;
    for (const auto& error : error_history_) {
        type_counts[error->type]++;
    }
    
    for (const auto& pair : type_counts) {
        stats[ErrorTypeToString(pair.first)] = pair.second;
    }
    
    return stats;
}

void ErrorHandler::RecoveryThreadFunc() {
    while (!shutdown_requested_) {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        
        queue_condition_.wait(lock, [this] {
            return !recovery_queue_.empty() || shutdown_requested_;
        });
        
        while (!recovery_queue_.empty()) {
            uint64_t error_id = recovery_queue_.front();
            recovery_queue_.pop();
            lock.unlock();
            
            ProcessErrorRecovery(error_id);
            
            lock.lock();
        }
    }
}

void ErrorHandler::ProcessErrorRecovery(uint64_t error_id) {
    auto error_info = GetError(error_id);
    if (!error_info) {
        return;
    }
    
    LOG_INFO(LogComponent::SYSTEM, 
             "开始处理错误恢复，ID: " + std::to_string(error_id));
    
    // 查找合适的恢复处理器
    for (auto& handler : recovery_handlers_) {
        if (handler->CanHandle(error_info->type)) {
            bool success = handler->HandleRecovery(*error_info);
            if (success) {
                // 从活跃错误列表中移除
                std::lock_guard<std::mutex> lock(errors_mutex_);
                active_errors_.erase(error_id);
                return;
            }
        }
    }
    
    LOG_WARNING(LogComponent::SYSTEM, 
               "未找到合适的恢复处理器，错误ID: " + std::to_string(error_id));
}

void ErrorHandler::SendAlarmNotification(const ErrorInfo& error_info) {
    // 检查严重程度过滤
    auto severity_it = alarm_config_.severity_filters.find(error_info.severity);
    if (severity_it != alarm_config_.severity_filters.end() && !severity_it->second) {
        return; // 该严重程度的告警被过滤
    }
    
    std::string alarm_message = "错误告警: " + ErrorTypeToString(error_info.type) + 
                               " [" + ErrorSeverityToString(error_info.severity) + "] " +
                               error_info.message;
    
    LOG_WARNING(LogComponent::SYSTEM, "发送告警通知: " + alarm_message);
    
    // 实际实现应该发送邮件、短信或Webhook通知
    // 这里只是记录日志
}

bool ErrorHandler::LoadConfig(const std::string& config_file_path) {
    // 简化的配置加载实现
    // 实际项目中应该使用JSON或YAML配置文件
    return true;
}

void ErrorHandler::InitializeDefaultHandlers() {
    // 初始化默认恢复处理器
    ErrorRecoveryConfig retry_config;
    retry_config.strategy = RecoveryStrategy::RETRY;
    retry_config.max_retry_count = 3;
    retry_config.retry_interval_ms = 5000;
    retry_config.enable_auto_recovery = true;
    
    recovery_handlers_.push_back(std::make_unique<RetryRecoveryHandler>(retry_config));
    
    ErrorRecoveryConfig failover_config;
    failover_config.strategy = RecoveryStrategy::FAILOVER;
    failover_config.enable_auto_recovery = true;
    
    recovery_handlers_.push_back(std::make_unique<FailoverRecoveryHandler>(failover_config));
    
    ErrorRecoveryConfig restart_config;
    restart_config.strategy = RecoveryStrategy::RESTART_COMPONENT;
    restart_config.enable_auto_recovery = true;
    
    recovery_handlers_.push_back(std::make_unique<RestartRecoveryHandler>(restart_config));
}

void ErrorHandler::InitializeDefaultRecoveryConfigs() {
    // 配置不同错误类型的恢复策略
    ErrorRecoveryConfig config;
    
    // GNSS信号丢失 - 故障切换到铷钟
    config.strategy = RecoveryStrategy::FAILOVER;
    config.enable_auto_recovery = true;
    recovery_configs_[ErrorType::GNSS_SIGNAL_LOST] = config;
    
    // 守护进程崩溃 - 重启组件
    config.strategy = RecoveryStrategy::RESTART_COMPONENT;
    config.max_retry_count = 3;
    config.retry_interval_ms = 10000;
    recovery_configs_[ErrorType::DAEMON_CRASH] = config;
    
    // 网络超时 - 重试
    config.strategy = RecoveryStrategy::RETRY;
    config.max_retry_count = 5;
    config.retry_interval_ms = 2000;
    recovery_configs_[ErrorType::NETWORK_TIMEOUT] = config;
    
    // 配置错误 - 需要人工干预
    config.strategy = RecoveryStrategy::MANUAL_INTERVENTION;
    config.enable_auto_recovery = false;
    recovery_configs_[ErrorType::CONFIG_ERROR] = config;
}

void ErrorHandler::CleanupExpiredErrors() {
    // 清理过期的错误记录
    // 实际实现应该定期调用此方法
}

// 工具函数实现
std::string ErrorTypeToString(ErrorType type) {
    switch (type) {
        case ErrorType::HARDWARE_FAULT: return "HARDWARE_FAULT";
        case ErrorType::GNSS_SIGNAL_LOST: return "GNSS_SIGNAL_LOST";
        case ErrorType::RUBIDIUM_FAULT: return "RUBIDIUM_FAULT";
        case ErrorType::RTC_FAULT: return "RTC_FAULT";
        case ErrorType::PPS_SIGNAL_LOST: return "PPS_SIGNAL_LOST";
        case ErrorType::FREQ_REF_LOST: return "FREQ_REF_LOST";
        case ErrorType::NETWORK_INTERFACE_DOWN: return "NETWORK_INTERFACE_DOWN";
        case ErrorType::CONFIG_ERROR: return "CONFIG_ERROR";
        case ErrorType::DAEMON_CRASH: return "DAEMON_CRASH";
        case ErrorType::API_ERROR: return "API_ERROR";
        case ErrorType::DATABASE_ERROR: return "DATABASE_ERROR";
        case ErrorType::FILE_SYSTEM_ERROR: return "FILE_SYSTEM_ERROR";
        case ErrorType::MEMORY_ERROR: return "MEMORY_ERROR";
        case ErrorType::SYSTEM_OVERLOAD: return "SYSTEM_OVERLOAD";
        case ErrorType::TEMPERATURE_ALARM: return "TEMPERATURE_ALARM";
        case ErrorType::POWER_SUPPLY_FAULT: return "POWER_SUPPLY_FAULT";
        case ErrorType::CLOCK_SYNC_LOST: return "CLOCK_SYNC_LOST";
        case ErrorType::HOLDOVER_TIMEOUT: return "HOLDOVER_TIMEOUT";
        case ErrorType::NETWORK_TIMEOUT: return "NETWORK_TIMEOUT";
        case ErrorType::PTP_SYNC_LOST: return "PTP_SYNC_LOST";
        case ErrorType::NTP_SYNC_LOST: return "NTP_SYNC_LOST";
        case ErrorType::AUTHENTICATION_FAILED: return "AUTHENTICATION_FAILED";
        case ErrorType::AUTHORIZATION_DENIED: return "AUTHORIZATION_DENIED";
        case ErrorType::INVALID_REQUEST: return "INVALID_REQUEST";
        case ErrorType::UNKNOWN_ERROR: return "UNKNOWN_ERROR";
        default: return "UNKNOWN";
    }
}

ErrorType StringToErrorType(const std::string& str) {
    if (str == "HARDWARE_FAULT") return ErrorType::HARDWARE_FAULT;
    if (str == "GNSS_SIGNAL_LOST") return ErrorType::GNSS_SIGNAL_LOST;
    if (str == "RUBIDIUM_FAULT") return ErrorType::RUBIDIUM_FAULT;
    if (str == "RTC_FAULT") return ErrorType::RTC_FAULT;
    if (str == "PPS_SIGNAL_LOST") return ErrorType::PPS_SIGNAL_LOST;
    if (str == "FREQ_REF_LOST") return ErrorType::FREQ_REF_LOST;
    if (str == "NETWORK_INTERFACE_DOWN") return ErrorType::NETWORK_INTERFACE_DOWN;
    if (str == "CONFIG_ERROR") return ErrorType::CONFIG_ERROR;
    if (str == "DAEMON_CRASH") return ErrorType::DAEMON_CRASH;
    if (str == "API_ERROR") return ErrorType::API_ERROR;
    if (str == "DATABASE_ERROR") return ErrorType::DATABASE_ERROR;
    if (str == "FILE_SYSTEM_ERROR") return ErrorType::FILE_SYSTEM_ERROR;
    if (str == "MEMORY_ERROR") return ErrorType::MEMORY_ERROR;
    if (str == "SYSTEM_OVERLOAD") return ErrorType::SYSTEM_OVERLOAD;
    if (str == "TEMPERATURE_ALARM") return ErrorType::TEMPERATURE_ALARM;
    if (str == "POWER_SUPPLY_FAULT") return ErrorType::POWER_SUPPLY_FAULT;
    if (str == "CLOCK_SYNC_LOST") return ErrorType::CLOCK_SYNC_LOST;
    if (str == "HOLDOVER_TIMEOUT") return ErrorType::HOLDOVER_TIMEOUT;
    if (str == "NETWORK_TIMEOUT") return ErrorType::NETWORK_TIMEOUT;
    if (str == "PTP_SYNC_LOST") return ErrorType::PTP_SYNC_LOST;
    if (str == "NTP_SYNC_LOST") return ErrorType::NTP_SYNC_LOST;
    if (str == "AUTHENTICATION_FAILED") return ErrorType::AUTHENTICATION_FAILED;
    if (str == "AUTHORIZATION_DENIED") return ErrorType::AUTHORIZATION_DENIED;
    if (str == "INVALID_REQUEST") return ErrorType::INVALID_REQUEST;
    return ErrorType::UNKNOWN_ERROR;
}

std::string ErrorSeverityToString(ErrorSeverity severity) {
    switch (severity) {
        case ErrorSeverity::LOW: return "LOW";
        case ErrorSeverity::MEDIUM: return "MEDIUM";
        case ErrorSeverity::HIGH: return "HIGH";
        case ErrorSeverity::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

ErrorSeverity StringToErrorSeverity(const std::string& str) {
    if (str == "LOW") return ErrorSeverity::LOW;
    if (str == "MEDIUM") return ErrorSeverity::MEDIUM;
    if (str == "HIGH") return ErrorSeverity::HIGH;
    if (str == "CRITICAL") return ErrorSeverity::CRITICAL;
    return ErrorSeverity::LOW;
}

std::string RecoveryStrategyToString(RecoveryStrategy strategy) {
    switch (strategy) {
        case RecoveryStrategy::IGNORE: return "IGNORE";
        case RecoveryStrategy::RETRY: return "RETRY";
        case RecoveryStrategy::RESTART_COMPONENT: return "RESTART_COMPONENT";
        case RecoveryStrategy::FAILOVER: return "FAILOVER";
        case RecoveryStrategy::DEGRADE_SERVICE: return "DEGRADE_SERVICE";
        case RecoveryStrategy::SHUTDOWN_SYSTEM: return "SHUTDOWN_SYSTEM";
        case RecoveryStrategy::MANUAL_INTERVENTION: return "MANUAL_INTERVENTION";
        default: return "UNKNOWN";
    }
}

RecoveryStrategy StringToRecoveryStrategy(const std::string& str) {
    if (str == "IGNORE") return RecoveryStrategy::IGNORE;
    if (str == "RETRY") return RecoveryStrategy::RETRY;
    if (str == "RESTART_COMPONENT") return RecoveryStrategy::RESTART_COMPONENT;
    if (str == "FAILOVER") return RecoveryStrategy::FAILOVER;
    if (str == "DEGRADE_SERVICE") return RecoveryStrategy::DEGRADE_SERVICE;
    if (str == "SHUTDOWN_SYSTEM") return RecoveryStrategy::SHUTDOWN_SYSTEM;
    if (str == "MANUAL_INTERVENTION") return RecoveryStrategy::MANUAL_INTERVENTION;
    return RecoveryStrategy::IGNORE;
}

std::string ErrorStatusToString(ErrorStatus status) {
    switch (status) {
        case ErrorStatus::NEW: return "NEW";
        case ErrorStatus::ACKNOWLEDGED: return "ACKNOWLEDGED";
        case ErrorStatus::IN_PROGRESS: return "IN_PROGRESS";
        case ErrorStatus::RESOLVED: return "RESOLVED";
        case ErrorStatus::FAILED: return "FAILED";
        case ErrorStatus::IGNORED: return "IGNORED";
        default: return "UNKNOWN";
    }
}

ErrorStatus StringToErrorStatus(const std::string& str) {
    if (str == "NEW") return ErrorStatus::NEW;
    if (str == "ACKNOWLEDGED") return ErrorStatus::ACKNOWLEDGED;
    if (str == "IN_PROGRESS") return ErrorStatus::IN_PROGRESS;
    if (str == "RESOLVED") return ErrorStatus::RESOLVED;
    if (str == "FAILED") return ErrorStatus::FAILED;
    if (str == "IGNORED") return ErrorStatus::IGNORED;
    return ErrorStatus::NEW;
}

} // namespace core
} // namespace timing_server