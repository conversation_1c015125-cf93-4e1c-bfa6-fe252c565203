#include "core/json_fallback.h"

#ifdef USE_JSONCPP_PLACEHOLDER

namespace Json {

std::string writeString(const StreamWriterBuilder& /* builder */, const Value& root) {
    std::ostringstream oss;
    
    std::function<void(const Value&)> writeValue = [&](const Value& value) {
        if (value.isNull()) {
            oss << "null";
        } else if (value.isBool()) {
            oss << (value.asBool() ? "true" : "false");
        } else if (value.isInt()) {
            oss << value.asInt();
        } else if (value.isUInt()) {
            oss << value.asUInt();
        } else if (value.isDouble()) {
            oss << value.asDouble();
        } else if (value.isString()) {
            oss << "\"" << value.asString() << "\"";
        } else if (value.isArray()) {
            oss << "[";
            for (size_t i = 0; i < value.size(); ++i) {
                if (i > 0) oss << ",";
                // 简化实现：无法访问数组元素
                oss << "null";
            }
            oss << "]";
        } else if (value.isObject()) {
            oss << "{";
            // 简化实现：无法遍历对象成员
            oss << "}";
        }
    };
    
    writeValue(root);
    return oss.str();
}

} // namespace Json

#endif // USE_JSONCPP_PLACEHOLDER