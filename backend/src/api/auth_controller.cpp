#include "api/auth_controller.h"
#include <core/logger.h>
#include <core/types.h>
#include <sstream>
#include <chrono>

#ifdef USE_OATPP_PLACEHOLDER

// 占位实现，当oatpp不可用时使用
namespace timing_server {
namespace api {

// 占位方法实现
AuthController::AuthController(std::shared_ptr<void> objectMapper,
                              std::shared_ptr<AuthManager> auth_manager)
    : m_auth_manager(auth_manager) {
    LOG_INFO(core::LogComponent::AUTH_MANAGER, "认证API控制器已创建（占位实现）");
}

std::shared_ptr<AuthController> AuthController::createShared(
    std::shared_ptr<void> objectMapper,
    std::shared_ptr<AuthManager> auth_manager) {
    return std::make_shared<AuthController>(objectMapper, auth_manager);
}

std::string AuthController::handleLogin(const std::string& username, const std::string& password) {
    auto tokens = m_auth_manager->authenticate(username, password, "127.0.0.1", "placeholder-client");
    if (!tokens.first.empty()) {
        return "{\"access_token\":\"" + tokens.first + "\",\"token_type\":\"Bearer\"}";
    }
    return "{\"error\":\"Authentication failed\"}";
}

bool AuthController::validateRequest(const std::string& token) {
    auto token_info = m_auth_manager->validateToken(token);
    return token_info != nullptr;
}

} // namespace api
} // namespace timing_server

#else

// 完整的oatpp实现（当oatpp可用时）
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "api/dto.h"

#include OATPP_CODEGEN_BEGIN(ApiController)

namespace timing_server {
namespace api {

AuthController::AuthController(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
                              std::shared_ptr<AuthManager> auth_manager)
    : oatpp::web::server::api::ApiController(objectMapper), m_auth_manager(auth_manager) {
    
    LOG_INFO(core::LogComponent::AUTH_MANAGER, "认证API控制器已创建");
}

std::shared_ptr<AuthController> AuthController::createShared(
    OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
    std::shared_ptr<AuthManager> auth_manager) {
    
    return std::make_shared<AuthController>(objectMapper, auth_manager);
}

// 实现方法

std::shared_ptr<AuthController::OutgoingResponse> 
AuthController::loginImpl(const oatpp::Object<LoginRequestDto>& dto,
                         const std::shared_ptr<IncomingRequest>& request) {
    try {
        if (!dto || !dto->username || !dto->password) {
            return createErrorResponse(Status::CODE_400, "INVALID_REQUEST", "用户名和密码不能为空");
        }
        
        std::string username = dto->username->c_str();
        std::string password = dto->password->c_str();
        std::string ip_address = getClientIpAddress(request);
        std::string user_agent = getUserAgent(request);
        
        // 执行认证
        auto tokens = m_auth_manager->authenticate(username, password, ip_address, user_agent);
        
        if (tokens.first.empty() || tokens.second.empty()) {
            return createUnauthorizedResponse("用户名或密码错误");
        }
        
        // 获取用户信息
        auto user_info = m_auth_manager->getUserInfo(username);
        if (!user_info) {
            return createErrorResponse(Status::CODE_500, "INTERNAL_ERROR", "获取用户信息失败");
        }
        
        // 创建响应
        auto response_dto = AuthResponseDto::createShared();
        response_dto->access_token = tokens.first;
        response_dto->refresh_token = tokens.second;
        response_dto->token_type = "Bearer";
        response_dto->expires_in = 3600; // 1小时
        response_dto->user = convertUserInfo(*user_info);
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "用户登录成功: " + username + " from " + ip_address);
        
        return createDtoResponse(Status::CODE_200, response_dto);
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "登录处理异常: " + std::string(e.what()));
        return createErrorResponse(Status::CODE_500, "INTERNAL_ERROR", "服务器内部错误");
    }
}

// 辅助方法实现

std::string AuthController::extractToken(const oatpp::String& authorization) {
    if (!authorization) {
        return "";
    }
    
    std::string auth_str = authorization->c_str();
    const std::string bearer_prefix = "Bearer ";
    
    if (auth_str.length() > bearer_prefix.length() && 
        auth_str.substr(0, bearer_prefix.length()) == bearer_prefix) {
        return auth_str.substr(bearer_prefix.length());
    }
    
    return "";
}

std::string AuthController::getClientIpAddress(const std::shared_ptr<IncomingRequest>& request) {
    // 尝试从X-Forwarded-For头获取真实IP
    auto xff_header = request->getHeader("X-Forwarded-For");
    if (xff_header) {
        std::string xff = xff_header->c_str();
        size_t comma_pos = xff.find(',');
        if (comma_pos != std::string::npos) {
            return xff.substr(0, comma_pos);
        }
        return xff;
    }
    
    // 尝试从X-Real-IP头获取
    auto xri_header = request->getHeader("X-Real-IP");
    if (xri_header) {
        return xri_header->c_str();
    }
    
    // 返回默认值
    return "unknown";
}

std::string AuthController::getUserAgent(const std::shared_ptr<IncomingRequest>& request) {
    auto ua_header = request->getHeader("User-Agent");
    return ua_header ? ua_header->c_str() : "unknown";
}

std::shared_ptr<AuthController::OutgoingResponse> 
AuthController::createUnauthorizedResponse(const std::string& message) {
    return createErrorResponse(Status::CODE_401, "UNAUTHORIZED", message);
}

std::shared_ptr<AuthController::OutgoingResponse> 
AuthController::createForbiddenResponse(const std::string& message) {
    return createErrorResponse(Status::CODE_403, "FORBIDDEN", message);
}

std::shared_ptr<AuthController::OutgoingResponse> 
AuthController::createErrorResponse(const Status& status,
                                   const std::string& code,
                                   const std::string& message) {
    auto error_dto = ErrorResponseDto::createShared();
    error_dto->code = code;
    error_dto->message = message;
    error_dto->timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
    error_dto->request_id = "req_" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count());
    error_dto->details = oatpp::Fields<oatpp::String>::createShared();
    
    return createDtoResponse(status, error_dto);
}

oatpp::Object<UserInfoDto> AuthController::convertUserInfo(const UserInfo& user) {
    auto user_dto = UserInfoDto::createShared();
    user_dto->username = user.username;
    user_dto->role = AuthManager::roleToString(user.role);
    user_dto->created_at = core::TimestampToIsoString(user.created_at * 1000000); // 转换为纳秒
    user_dto->last_login = user.last_login > 0 ? 
                          core::TimestampToIsoString(user.last_login * 1000000) : "";
    user_dto->is_active = user.is_active;
    
    // 转换权限列表
    user_dto->permissions = oatpp::List<oatpp::String>::createShared();
    for (const auto& perm : user.permissions) {
        user_dto->permissions->push_back(AuthManager::permissionToString(perm));
    }
    
    return user_dto;
}

uint64_t AuthController::parseTimestamp(const std::string& time_str) {
    // 简化的时间戳解析实现
    // 实际应该解析ISO格式时间字符串
    try {
        return std::stoull(time_str);
    } catch (...) {
        return 0;
    }
}

} // namespace api
} // namespace timing_server

#include OATPP_CODEGEN_END(ApiController)

#endif // USE_OATPP_PLACEHOLDER