#include <api/websocket_event_listener.h>
#include <core/logger.h>

namespace timing_server {
namespace api {

WebSocketEventListener::WebSocketEventListener() {
    LOG_INFO(core::LogComponent::WEBSOCKET, "创建WebSocket事件监听器");
}

void WebSocketEventListener::onClientConnected(uint64_t connection_id, const std::string& remote_address) {
    {
        std::lock_guard<std::mutex> lock(m_stats_mutex);
        m_stats.total_connections++;
    }

    LOG_INFO(core::LogComponent::WEBSOCKET, 
            "WebSocket客户端连接事件 - ID: " + std::to_string(connection_id) + 
            ", 地址: " + remote_address);

    // 记录连接事件到安全日志
    logSecurityEvent("CLIENT_CONNECTED", connection_id, "remote_address=" + remote_address);
}

void WebSocketEventListener::onClientDisconnected(uint64_t connection_id, const std::string& reason) {
    LOG_INFO(core::LogComponent::WEBSOCKET, 
            "WebSocket客户端断开事件 - ID: " + std::to_string(connection_id) + 
            ", 原因: " + reason);

    // 记录断开事件到安全日志
    logSecurityEvent("CLIENT_DISCONNECTED", connection_id, "reason=" + reason);
}

void WebSocketEventListener::onClientAuthenticated(uint64_t connection_id, const std::string& user_id, bool success) {
    {
        std::lock_guard<std::mutex> lock(m_stats_mutex);
        if (success) {
            m_stats.successful_authentications++;
        } else {
            m_stats.failed_authentications++;
        }
    }

    if (success) {
        LOG_INFO(core::LogComponent::WEBSOCKET, 
                "WebSocket客户端认证成功 - ID: " + std::to_string(connection_id) + 
                ", 用户: " + user_id);
    } else {
        LOG_WARNING(core::LogComponent::WEBSOCKET, 
                   "WebSocket客户端认证失败 - ID: " + std::to_string(connection_id) + 
                   ", 用户: " + user_id);
    }

    // 记录认证事件到安全日志
    logSecurityEvent(success ? "AUTH_SUCCESS" : "AUTH_FAILURE", connection_id, 
                    "user_id=" + user_id);
}

void WebSocketEventListener::onMessageReceived(uint64_t connection_id, const WebSocketMessage& message) {
    {
        std::lock_guard<std::mutex> lock(m_stats_mutex);
        m_stats.messages_processed++;
    }

    // 根据消息类型记录不同级别的日志
    switch (message.type) {
        case WebSocketMessageType::AUTH:
            LOG_DEBUG(core::LogComponent::WEBSOCKET, 
                     "收到认证消息 - 连接ID: " + std::to_string(connection_id));
            break;
        case WebSocketMessageType::SUBSCRIPTION:
            LOG_DEBUG(core::LogComponent::WEBSOCKET, 
                     "收到订阅消息 - 连接ID: " + std::to_string(connection_id));
            break;
        case WebSocketMessageType::HEARTBEAT:
            // 心跳消息不记录，避免日志过多
            break;
        default:
            LOG_DEBUG(core::LogComponent::WEBSOCKET, 
                     "收到消息 - 连接ID: " + std::to_string(connection_id) + 
                     ", 类型: " + std::to_string(static_cast<int>(message.type)));
            break;
    }
}

WebSocketEventListener::ConnectionStats WebSocketEventListener::getConnectionStats() const {
    std::lock_guard<std::mutex> lock(m_stats_mutex);
    return m_stats;
}

void WebSocketEventListener::logSecurityEvent(const std::string& event_type, 
                                             uint64_t connection_id, 
                                             const std::string& details) {
    // 创建结构化的安全事件日志
    std::string security_log = "WebSocket安全事件 - 类型: " + event_type + 
                              ", 连接ID: " + std::to_string(connection_id) + 
                              ", 详情: " + details;

    if (event_type == "AUTH_FAILURE" || event_type.find("FAILURE") != std::string::npos) {
        LOG_WARNING(core::LogComponent::WEBSOCKET, security_log);
    } else {
        LOG_INFO(core::LogComponent::WEBSOCKET, security_log);
    }
}

} // namespace api
} // namespace timing_server