#include "api/timing_controller.h"
#include "api/timing_service.h"
#include <core/logger.h>
#include <core/types.h>
#include <random>
#include <sstream>
#include <iomanip>

namespace timing_server {
namespace api {

TimingController::TimingController(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
                                 std::shared_ptr<TimingService> timing_service)
    : oatpp::web::server::api::ApiController(objectMapper), m_timing_service(timing_service) {
    
    LOG_INFO(core::LogComponent::API_SERVER, "授时API控制器已创建");
}

std::shared_ptr<TimingController> TimingController::createShared(
    OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
    std::shared_ptr<TimingService> timing_service) {
    
    return std::make_shared<TimingController>(objectMapper, timing_service);
}

// 实现方法

oatpp::Object<SystemStatusDto> TimingController::getSystemStatusImpl() {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        auto system_status = m_timing_service->getSystemStatus();
        
        // 创建SystemStatusDto
        auto dto = SystemStatusDto::createShared();
        
        // 填充系统基本信息
        dto->state = core::ClockStateToString(system_status.current_state);
        dto->uptime_seconds = system_status.uptime_seconds;
        dto->version = system_status.version;
        dto->platform = system_status.platform;
        dto->cpu_usage_percent = system_status.cpu_usage_percent;
        dto->memory_usage_mb = system_status.memory_usage_mb;
        dto->health = core::SystemHealthToString(system_status.health);
        
        // 填充授时相关信息
        dto->active_source = core::TimeSourceToString(system_status.active_source);
        
        // 从活跃时间源获取精度信息
        for (const auto& source : system_status.sources) {
            if (source.type == system_status.active_source && 
                source.status == core::TimeSourceStatus::ACTIVE) {
                dto->accuracy_ns = source.quality.accuracy_ns;
                break;
            }
        }
        
        // 设置默认值（实际应从时间数据获取）
        dto->phase_offset_ns = 12.5;
        dto->frequency_offset_ppm = 0.001;
        
        // 填充时间源列表
        dto->sources = oatpp::List<oatpp::Object<TimeSourceInfoDto>>::createShared();
        
        for (const auto& source : system_status.sources) {
            auto source_dto = TimeSourceInfoDto::createShared();
            source_dto->type = core::TimeSourceToString(source.type);
            source_dto->status = core::TimeSourceStatusToString(source.status);
            source_dto->priority = source.priority;
            source_dto->last_update = core::TimestampToIsoString(source.last_update_ns);
            
            // 填充质量指标
            auto quality_dto = TimeQualityDto::createShared();
            quality_dto->accuracy_ns = source.quality.accuracy_ns;
            quality_dto->stability_ppm = source.quality.stability_ppm;
            quality_dto->confidence = source.quality.confidence;
            quality_dto->is_traceable = source.quality.is_traceable;
            quality_dto->reference = source.quality.reference;
            source_dto->quality = quality_dto;
            
            // 填充扩展属性
            source_dto->properties = oatpp::Fields<oatpp::String>::createShared();
            for (const auto& prop : source.properties) {
                source_dto->properties->put(prop.first, prop.second);
            }
            
            dto->sources->push_back(source_dto);
        }
        
        // 记录性能指标
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        
        LOG_DEBUG(core::LogComponent::API_SERVER, 
                 "getSystemStatus响应时间: " + std::to_string(duration.count() / 1000.0) + "ms");
        
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "获取系统状态失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<PtpConfigDto> TimingController::getPtpConfigImpl() {
    try {
        auto config_json = m_timing_service->getPtpConfig();
        
        // 创建PtpConfigDto并填充默认值（实际应解析JSON）
        auto dto = PtpConfigDto::createShared();
        dto->domain = 0;
        dto->priority1 = 128;
        dto->priority2 = 128;
        dto->clock_class = 6;
        dto->clock_accuracy = 0x21;
        dto->offset_scaled_log_variance = 0x4E5D;
        dto->interface = "eth0";
        dto->network_transport = "UDPv4";
        dto->delay_mechanism = "E2E";
        dto->announce_interval = 1;
        dto->sync_interval = 0;
        dto->delay_req_interval = 0;
        
        LOG_DEBUG(core::LogComponent::API_SERVER, "获取PTP配置成功");
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "获取PTP配置失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<PtpConfigDto> TimingController::updatePtpConfigImpl(const oatpp::Object<PtpConfigDto>& dto) {
    try {
        // 验证输入参数
        if (!dto) {
            LOG_ERROR(core::LogComponent::API_SERVER, "PTP配置DTO为空");
            return nullptr;
        }
        
        // 验证关键参数
        if (dto->domain && (dto->domain < 0 || dto->domain > 255)) {
            LOG_ERROR(core::LogComponent::API_SERVER, "PTP域值无效: " + std::to_string(*dto->domain));
            return nullptr;
        }
        
        if (dto->clock_class && (*dto->clock_class < 6 || *dto->clock_class > 255)) {
            LOG_ERROR(core::LogComponent::API_SERVER, "PTP时钟等级无效: " + std::to_string(*dto->clock_class));
            return nullptr;
        }
        
        // 构建配置JSON（简化实现）
        std::ostringstream config_json;
        config_json << "{";
        config_json << "\"domain\":" << (dto->domain ? *dto->domain : 0) << ",";
        config_json << "\"priority1\":" << (dto->priority1 ? *dto->priority1 : 128) << ",";
        config_json << "\"priority2\":" << (dto->priority2 ? *dto->priority2 : 128) << ",";
        config_json << "\"clock_class\":" << (dto->clock_class ? *dto->clock_class : 6);
        config_json << "}";
        
        // 更新配置
        bool success = m_timing_service->updatePtpConfig(config_json.str());
        if (!success) {
            LOG_ERROR(core::LogComponent::API_SERVER, "PTP配置更新失败");
            return nullptr;
        }
        
        LOG_INFO(core::LogComponent::API_SERVER, "PTP配置更新成功");
        return dto; // 返回更新后的配置
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "更新PTP配置失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<NtpConfigDto> TimingController::getNtpConfigImpl() {
    try {
        auto config_json = m_timing_service->getNtpConfig();
        
        // 创建NtpConfigDto并填充默认值
        auto dto = NtpConfigDto::createShared();
        dto->stratum = 1;
        dto->reference_id = "GPS";
        dto->server_address = "0.0.0.0";
        dto->server_port = 123;
        dto->allowed_networks = oatpp::List<oatpp::String>::createShared();
        dto->allowed_networks->push_back("***********/24");
        dto->allowed_networks->push_back("10.0.0.0/8");
        dto->max_clients = 1000;
        dto->poll_interval = 6;
        dto->precision = -20;
        
        LOG_DEBUG(core::LogComponent::API_SERVER, "获取NTP配置成功");
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "获取NTP配置失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<NtpConfigDto> TimingController::updateNtpConfigImpl(const oatpp::Object<NtpConfigDto>& dto) {
    try {
        if (!dto) {
            LOG_ERROR(core::LogComponent::API_SERVER, "NTP配置DTO为空");
            return nullptr;
        }
        
        // 验证stratum值
        if (dto->stratum && (*dto->stratum < 1 || *dto->stratum > 15)) {
            LOG_ERROR(core::LogComponent::API_SERVER, "NTP层级无效: " + std::to_string(*dto->stratum));
            return nullptr;
        }
        
        // 构建配置JSON（简化实现）
        std::ostringstream config_json;
        config_json << "{";
        config_json << "\"stratum\":" << (dto->stratum ? *dto->stratum : 1) << ",";
        config_json << "\"reference_id\":\"" << (dto->reference_id ? dto->reference_id->c_str() : "GPS") << "\",";
        config_json << "\"server_port\":" << (dto->server_port ? *dto->server_port : 123);
        config_json << "}";
        
        bool success = m_timing_service->updateNtpConfig(config_json.str());
        if (!success) {
            LOG_ERROR(core::LogComponent::API_SERVER, "NTP配置更新失败");
            return nullptr;
        }
        
        LOG_INFO(core::LogComponent::API_SERVER, "NTP配置更新成功");
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "更新NTP配置失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<LogEntriesDto> TimingController::getLogsImpl(const oatpp::String& level,
                                                          const oatpp::UInt32& page,
                                                          const oatpp::UInt32& limit,
                                                          const oatpp::String& from,
                                                          const oatpp::String& to) {
    try {
        TimingService::LogQueryParams params;
        params.level = level ? level->c_str() : "";
        params.page = page ? *page : 1;
        params.limit = limit ? *limit : 100;
        params.from = from ? from->c_str() : "";
        params.to = to ? to->c_str() : "";
        
        // 限制每页最大条目数
        if (params.limit > 1000) {
            params.limit = 1000;
        }
        
        auto result = m_timing_service->queryLogs(params);
        
        // 创建响应DTO
        auto dto = LogEntriesDto::createShared();
        dto->logs = oatpp::List<oatpp::Object<LogEntryDto>>::createShared();
        
        // 填充日志条目
        for (const auto& log : result.logs) {
            auto log_dto = LogEntryDto::createShared();
            log_dto->timestamp = log.timestamp;
            log_dto->level = log.level;
            log_dto->component = log.component;
            log_dto->message = log.message;
            
            // 填充上下文信息
            log_dto->context = oatpp::Fields<oatpp::String>::createShared();
            for (const auto& ctx : log.context) {
                log_dto->context->put(ctx.first, ctx.second);
            }
            
            dto->logs->push_back(log_dto);
        }
        
        // 填充分页信息
        auto pagination = PaginationDto::createShared();
        pagination->current_page = result.current_page;
        pagination->total_pages = result.total_pages;
        pagination->total_entries = result.total_entries;
        pagination->entries_per_page = params.limit;
        dto->pagination = pagination;
        
        LOG_DEBUG(core::LogComponent::API_SERVER, 
                 "查询日志成功，返回" + std::to_string(result.logs.size()) + "条记录");
        
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "查询日志失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<HealthStatusDto> TimingController::getHealthImpl() {
    try {
        auto health = m_timing_service->getHealthStatus();
        
        auto dto = HealthStatusDto::createShared();
        dto->status = health.status;
        dto->timestamp = health.timestamp;
        dto->uptime_seconds = health.uptime_seconds;
        
        // 填充组件状态
        dto->components = oatpp::Fields<oatpp::String>::createShared();
        for (const auto& comp : health.components) {
            dto->components->put(comp.first, comp.second);
        }
        
        LOG_DEBUG(core::LogComponent::API_SERVER, "获取健康状态成功");
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "获取健康状态失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<OperationResultDto> TimingController::restartSystemImpl() {
    try {
        bool success = m_timing_service->restartSystem();
        
        auto dto = OperationResultDto::createShared();
        dto->success = success;
        dto->message = success ? "系统重启请求已提交" : "系统重启失败";
        dto->timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
        dto->data = oatpp::Fields<oatpp::String>::createShared();
        
        if (success) {
            LOG_INFO(core::LogComponent::API_SERVER, "系统重启请求已提交");
        } else {
            LOG_ERROR(core::LogComponent::API_SERVER, "系统重启失败");
        }
        
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "系统重启异常: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<MetricsDto> TimingController::getMetricsImpl(const oatpp::String& from,
                                                          const oatpp::String& to) {
    try {
        std::string from_str = from ? from->c_str() : "";
        std::string to_str = to ? to->c_str() : "";
        
        auto metrics = m_timing_service->getMetrics(from_str, to_str);
        
        auto dto = MetricsDto::createShared();
        dto->current_accuracy_ns = metrics.current_accuracy_ns;
        dto->average_accuracy_ns = metrics.average_accuracy_ns;
        dto->max_accuracy_ns = metrics.max_accuracy_ns;
        dto->allan_deviation_1s = metrics.allan_deviation_1s;
        dto->allan_deviation_10s = metrics.allan_deviation_10s;
        dto->allan_deviation_100s = metrics.allan_deviation_100s;
        dto->state_transitions = metrics.state_transitions;
        dto->error_count = metrics.error_count;
        dto->packets_processed = metrics.packets_processed;
        dto->average_response_time_ms = metrics.average_response_time_ms;
        dto->start_time = metrics.start_time;
        dto->end_time = metrics.end_time;
        
        LOG_DEBUG(core::LogComponent::API_SERVER, "获取性能指标成功");
        return dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "获取性能指标失败: " + std::string(e.what()));
        return nullptr;
    }
}

oatpp::Object<OperationResultDto> TimingController::validateConfigImpl(const oatpp::String& config_type,
                                                                       const oatpp::String& dto) {
    try {
        std::string type_str = config_type ? config_type->c_str() : "";
        std::string config_str = dto ? dto->c_str() : "";
        
        std::string validation_result = m_timing_service->validateConfig(type_str, config_str);
        
        auto result_dto = OperationResultDto::createShared();
        result_dto->success = validation_result.empty();
        result_dto->message = validation_result.empty() ? "配置验证通过" : validation_result;
        result_dto->timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
        result_dto->data = oatpp::Fields<oatpp::String>::createShared();
        
        LOG_DEBUG(core::LogComponent::API_SERVER, 
                 "配置验证完成，类型: " + type_str + "，结果: " + 
                 (validation_result.empty() ? "通过" : "失败"));
        
        return result_dto;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "配置验证异常: " + std::string(e.what()));
        return nullptr;
    }
}

std::shared_ptr<oatpp::String> TimingController::getConfigSchemaImpl(const oatpp::String& config_type) {
    try {
        std::string type_str = config_type ? config_type->c_str() : "";
        std::string schema = m_timing_service->getConfigSchema(type_str);
        
        if (!schema.empty()) {
            LOG_DEBUG(core::LogComponent::API_SERVER, "获取配置模式成功，类型: " + type_str);
            return std::make_shared<oatpp::String>(schema);
        } else {
            LOG_WARNING(core::LogComponent::API_SERVER, "配置模式未找到，类型: " + type_str);
            return nullptr;
        }
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "获取配置模式失败: " + std::string(e.what()));
        return nullptr;
    }
}

// 辅助方法实现

std::shared_ptr<TimingController::OutgoingResponse> 
TimingController::createErrorResponse(const Status& status,
                                    const std::string& code,
                                    const std::string& message) {
    auto error_dto = ErrorResponseDto::createShared();
    error_dto->code = code;
    error_dto->message = message;
    error_dto->timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
    error_dto->request_id = generateRequestId();
    error_dto->details = oatpp::Fields<oatpp::String>::createShared();
    
    LOG_WARNING(core::LogComponent::API_SERVER, 
               "API错误响应: " + code + " - " + message);
    
    return createDtoResponse(status, error_dto);
}

std::string TimingController::generateRequestId() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<uint64_t> dis;
    
    uint64_t id = dis(gen);
    
    std::ostringstream oss;
    oss << "req_" << std::hex << id;
    return oss.str();
}

} // namespace api
} // namespace timing_server