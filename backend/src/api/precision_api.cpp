#include "api/precision_api.h"
#include "core/logger.h"
#include <sstream>
#include <iomanip>
#include <chrono>
#include <ctime>

namespace timing_server {
namespace api {

// ============================================================================
// DTO Implementations
// ============================================================================

PrecisionMeasurementDto PrecisionMeasurementDto::FromCore(const core::PrecisionMeasurement& measurement) {
    PrecisionMeasurementDto dto;
    dto.timestamp_ns = measurement.timestamp_ns;
    dto.source = core::TimeSourceToString(measurement.source);
    dto.system_state = core::ClockStateToString(measurement.system_state);
    dto.absolute_accuracy_ns = measurement.absolute_accuracy_ns;
    dto.phase_offset_ns = measurement.phase_offset_ns;
    dto.frequency_offset_ppm = measurement.frequency_offset_ppm;
    dto.allan_deviation_1s = measurement.allan_deviation_1s;
    dto.allan_deviation_10s = measurement.allan_deviation_10s;
    dto.allan_deviation_100s = measurement.allan_deviation_100s;
    dto.gnss_satellites = measurement.gnss_satellites;
    dto.gnss_snr_db = measurement.gnss_snr_db;
    dto.rubidium_temperature = measurement.rubidium_temperature;
    dto.system_temperature = measurement.system_temperature;
    dto.cpu_usage_percent = measurement.cpu_usage_percent;
    dto.memory_usage_mb = measurement.memory_usage_mb;
    dto.network_latency_us = measurement.network_latency_us;
    dto.overall_quality_score = measurement.overall_quality_score;
    dto.meets_spec_requirements = measurement.meets_spec_requirements;
    return dto;
}

std::string PrecisionMeasurementDto::ToJson() const {
    std::ostringstream json;
    json << "{\n";
    json << "  \"timestamp_ns\": " << timestamp_ns << ",\n";
    json << "  \"source\": \"" << source << "\",\n";
    json << "  \"system_state\": \"" << system_state << "\",\n";
    json << "  \"absolute_accuracy_ns\": " << absolute_accuracy_ns << ",\n";
    json << "  \"phase_offset_ns\": " << phase_offset_ns << ",\n";
    json << "  \"frequency_offset_ppm\": " << frequency_offset_ppm << ",\n";
    json << "  \"allan_deviation_1s\": " << allan_deviation_1s << ",\n";
    json << "  \"allan_deviation_10s\": " << allan_deviation_10s << ",\n";
    json << "  \"allan_deviation_100s\": " << allan_deviation_100s << ",\n";
    json << "  \"gnss_satellites\": " << gnss_satellites << ",\n";
    json << "  \"gnss_snr_db\": " << gnss_snr_db << ",\n";
    json << "  \"rubidium_temperature\": " << rubidium_temperature << ",\n";
    json << "  \"system_temperature\": " << system_temperature << ",\n";
    json << "  \"cpu_usage_percent\": " << cpu_usage_percent << ",\n";
    json << "  \"memory_usage_mb\": " << memory_usage_mb << ",\n";
    json << "  \"network_latency_us\": " << network_latency_us << ",\n";
    json << "  \"overall_quality_score\": " << overall_quality_score << ",\n";
    json << "  \"meets_spec_requirements\": " << (meets_spec_requirements ? "true" : "false") << "\n";
    json << "}";
    return json.str();
}

PrecisionTrendDto PrecisionTrendDto::FromCore(const core::PrecisionTrend& trend) {
    PrecisionTrendDto dto;
    dto.analysis_period_hours = trend.analysis_period_hours;
    dto.accuracy_trend_ns_per_hour = trend.accuracy_trend_ns_per_hour;
    dto.frequency_drift_ppm_per_hour = trend.frequency_drift_ppm_per_hour;
    dto.stability_trend_coefficient = trend.stability_trend_coefficient;
    dto.mean_accuracy_ns = trend.mean_accuracy_ns;
    dto.std_deviation_accuracy_ns = trend.std_deviation_accuracy_ns;
    dto.min_accuracy_ns = trend.min_accuracy_ns;
    dto.max_accuracy_ns = trend.max_accuracy_ns;
    dto.predicted_accuracy_1h_ns = trend.predicted_accuracy_1h_ns;
    dto.predicted_accuracy_24h_ns = trend.predicted_accuracy_24h_ns;
    dto.confidence_level = trend.confidence_level;
    dto.trend_is_stable = trend.trend_is_stable;
    dto.within_specification = trend.within_specification;
    dto.recommendations = trend.recommendations;
    return dto;
}

std::string PrecisionTrendDto::ToJson() const {
    std::ostringstream json;
    json << "{\n";
    json << "  \"analysis_period_hours\": " << analysis_period_hours << ",\n";
    json << "  \"accuracy_trend_ns_per_hour\": " << accuracy_trend_ns_per_hour << ",\n";
    json << "  \"frequency_drift_ppm_per_hour\": " << frequency_drift_ppm_per_hour << ",\n";
    json << "  \"stability_trend_coefficient\": " << stability_trend_coefficient << ",\n";
    json << "  \"mean_accuracy_ns\": " << mean_accuracy_ns << ",\n";
    json << "  \"std_deviation_accuracy_ns\": " << std_deviation_accuracy_ns << ",\n";
    json << "  \"min_accuracy_ns\": " << min_accuracy_ns << ",\n";
    json << "  \"max_accuracy_ns\": " << max_accuracy_ns << ",\n";
    json << "  \"predicted_accuracy_1h_ns\": " << predicted_accuracy_1h_ns << ",\n";
    json << "  \"predicted_accuracy_24h_ns\": " << predicted_accuracy_24h_ns << ",\n";
    json << "  \"confidence_level\": " << confidence_level << ",\n";
    json << "  \"trend_is_stable\": " << (trend_is_stable ? "true" : "false") << ",\n";
    json << "  \"within_specification\": " << (within_specification ? "true" : "false") << ",\n";
    json << "  \"recommendations\": [\n";
    for (size_t i = 0; i < recommendations.size(); ++i) {
        json << "    \"" << recommendations[i] << "\"";
        if (i < recommendations.size() - 1) json << ",";
        json << "\n";
    }
    json << "  ]\n";
    json << "}";
    return json.str();
}

SystemHealthScoreDto SystemHealthScoreDto::FromCore(const core::SystemHealthScore& score) {
    SystemHealthScoreDto dto;
    dto.overall_score = score.overall_score;
    dto.timing_accuracy_score = score.timing_accuracy_score;
    dto.signal_quality_score = score.signal_quality_score;
    dto.system_stability_score = score.system_stability_score;
    dto.hardware_health_score = score.hardware_health_score;
    dto.performance_score = score.performance_score;
    dto.health_status = core::SystemHealthToString(score.health_status);
    dto.issues = score.issues;
    dto.warnings = score.warnings;
    dto.suggestions = score.suggestions;
    dto.needs_calibration = score.needs_calibration;
    dto.needs_maintenance = score.needs_maintenance;
    dto.days_until_maintenance = score.days_until_maintenance;
    return dto;
}

std::string SystemHealthScoreDto::ToJson() const {
    std::ostringstream json;
    json << "{\n";
    json << "  \"overall_score\": " << overall_score << ",\n";
    json << "  \"timing_accuracy_score\": " << timing_accuracy_score << ",\n";
    json << "  \"signal_quality_score\": " << signal_quality_score << ",\n";
    json << "  \"system_stability_score\": " << system_stability_score << ",\n";
    json << "  \"hardware_health_score\": " << hardware_health_score << ",\n";
    json << "  \"performance_score\": " << performance_score << ",\n";
    json << "  \"health_status\": \"" << health_status << "\",\n";
    json << "  \"needs_calibration\": " << (needs_calibration ? "true" : "false") << ",\n";
    json << "  \"needs_maintenance\": " << (needs_maintenance ? "true" : "false") << ",\n";
    json << "  \"days_until_maintenance\": " << days_until_maintenance << ",\n";
    
    // Issues array
    json << "  \"issues\": [\n";
    for (size_t i = 0; i < issues.size(); ++i) {
        json << "    \"" << issues[i] << "\"";
        if (i < issues.size() - 1) json << ",";
        json << "\n";
    }
    json << "  ],\n";
    
    // Warnings array
    json << "  \"warnings\": [\n";
    for (size_t i = 0; i < warnings.size(); ++i) {
        json << "    \"" << warnings[i] << "\"";
        if (i < warnings.size() - 1) json << ",";
        json << "\n";
    }
    json << "  ],\n";
    
    // Suggestions array
    json << "  \"suggestions\": [\n";
    for (size_t i = 0; i < suggestions.size(); ++i) {
        json << "    \"" << suggestions[i] << "\"";
        if (i < suggestions.size() - 1) json << ",";
        json << "\n";
    }
    json << "  ]\n";
    json << "}";
    return json.str();
}

AlarmEventDto AlarmEventDto::FromCore(const core::AlarmEvent& alarm) {
    AlarmEventDto dto;
    dto.id = alarm.id;
    dto.timestamp_ns = alarm.timestamp_ns;
    dto.type = core::AlarmTypeToString(alarm.type);
    dto.level = core::AlarmLevelToString(alarm.level);
    dto.status = core::AlarmStatusToString(alarm.status);
    dto.title = alarm.title;
    dto.description = alarm.description;
    dto.source_component = alarm.source_component;
    dto.threshold_value = alarm.threshold_value;
    dto.current_value = alarm.current_value;
    dto.unit = alarm.unit;
    dto.occurrence_count = alarm.occurrence_count;
    dto.first_occurrence_ns = alarm.first_occurrence_ns;
    dto.last_occurrence_ns = alarm.last_occurrence_ns;
    dto.acknowledged_time_ns = alarm.acknowledged_time_ns;
    dto.resolved_time_ns = alarm.resolved_time_ns;
    dto.acknowledged_by = alarm.acknowledged_by;
    dto.resolution_notes = alarm.resolution_notes;
    return dto;
}

std::string AlarmEventDto::ToJson() const {
    std::ostringstream json;
    json << "{\n";
    json << "  \"id\": " << id << ",\n";
    json << "  \"timestamp_ns\": " << timestamp_ns << ",\n";
    json << "  \"type\": \"" << type << "\",\n";
    json << "  \"level\": \"" << level << "\",\n";
    json << "  \"status\": \"" << status << "\",\n";
    json << "  \"title\": \"" << title << "\",\n";
    json << "  \"description\": \"" << description << "\",\n";
    json << "  \"source_component\": \"" << source_component << "\",\n";
    json << "  \"threshold_value\": " << threshold_value << ",\n";
    json << "  \"current_value\": " << current_value << ",\n";
    json << "  \"unit\": \"" << unit << "\",\n";
    json << "  \"occurrence_count\": " << occurrence_count << ",\n";
    json << "  \"first_occurrence_ns\": " << first_occurrence_ns << ",\n";
    json << "  \"last_occurrence_ns\": " << last_occurrence_ns << ",\n";
    json << "  \"acknowledged_time_ns\": " << acknowledged_time_ns << ",\n";
    json << "  \"resolved_time_ns\": " << resolved_time_ns << ",\n";
    json << "  \"acknowledged_by\": \"" << acknowledged_by << "\",\n";
    json << "  \"resolution_notes\": \"" << resolution_notes << "\"\n";
    json << "}";
    return json.str();
}

AlarmStatisticsDto AlarmStatisticsDto::FromCore(const core::AlarmStatistics& stats) {
    AlarmStatisticsDto dto;
    dto.total_alarms = stats.total_alarms;
    dto.active_alarms = stats.active_alarms;
    dto.acknowledged_alarms = stats.acknowledged_alarms;
    dto.resolved_alarms = stats.resolved_alarms;
    dto.suppressed_alarms = stats.suppressed_alarms;
    dto.average_resolution_time_minutes = stats.average_resolution_time_minutes;
    dto.average_acknowledgment_time_minutes = stats.average_acknowledgment_time_minutes;
    dto.notifications_sent = stats.notifications_sent;
    dto.notification_failures = stats.notification_failures;
    return dto;
}

std::string AlarmStatisticsDto::ToJson() const {
    std::ostringstream json;
    json << "{\n";
    json << "  \"total_alarms\": " << total_alarms << ",\n";
    json << "  \"active_alarms\": " << active_alarms << ",\n";
    json << "  \"acknowledged_alarms\": " << acknowledged_alarms << ",\n";
    json << "  \"resolved_alarms\": " << resolved_alarms << ",\n";
    json << "  \"suppressed_alarms\": " << suppressed_alarms << ",\n";
    json << "  \"average_resolution_time_minutes\": " << average_resolution_time_minutes << ",\n";
    json << "  \"average_acknowledgment_time_minutes\": " << average_acknowledgment_time_minutes << ",\n";
    json << "  \"notifications_sent\": " << notifications_sent << ",\n";
    json << "  \"notification_failures\": " << notification_failures << "\n";
    json << "}";
    return json.str();
}

// ============================================================================
// PrecisionApiController Implementation
// ============================================================================

PrecisionApiController::PrecisionApiController(std::shared_ptr<core::IPrecisionMonitor> precision_monitor,
                                             std::shared_ptr<core::IAlarmSystem> alarm_system)
    : precision_monitor_(precision_monitor), alarm_system_(alarm_system) {
    
    LOG_INFO(core::LogComponent::API_SERVER, "精度监控API控制器已创建");
}

std::string PrecisionApiController::GetCurrentMeasurement() {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        auto measurement = precision_monitor_->GetCurrentMeasurement();
        auto dto = PrecisionMeasurementDto::FromCore(measurement);
        
        return CreateSuccessResponse(dto.ToJson());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取当前精度测量失败: " + std::string(e.what()));
        return CreateErrorResponse("MEASUREMENT_ERROR", "获取精度测量数据失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::GetHistoricalMeasurements(uint64_t start_time, uint64_t end_time, uint32_t max_samples) {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        auto measurements = precision_monitor_->GetHistoricalMeasurements(start_time, end_time, max_samples);
        
        std::ostringstream json;
        json << "{\n  \"measurements\": [\n";
        
        for (size_t i = 0; i < measurements.size(); ++i) {
            auto dto = PrecisionMeasurementDto::FromCore(measurements[i]);
            json << "    " << dto.ToJson();
            if (i < measurements.size() - 1) json << ",";
            json << "\n";
        }
        
        json << "  ],\n";
        json << "  \"total_count\": " << measurements.size() << ",\n";
        json << "  \"start_time\": " << start_time << ",\n";
        json << "  \"end_time\": " << end_time << "\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取历史精度测量失败: " + std::string(e.what()));
        return CreateErrorResponse("HISTORICAL_DATA_ERROR", "获取历史数据失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::GetPrecisionTrend(uint32_t period_hours) {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        auto trend = precision_monitor_->GetPrecisionTrend(period_hours);
        auto dto = PrecisionTrendDto::FromCore(trend);
        
        return CreateSuccessResponse(dto.ToJson());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取精度趋势分析失败: " + std::string(e.what()));
        return CreateErrorResponse("TREND_ANALYSIS_ERROR", "趋势分析失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::GetSystemHealthScore() {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        auto health_score = precision_monitor_->GetSystemHealthScore();
        auto dto = SystemHealthScoreDto::FromCore(health_score);
        
        return CreateSuccessResponse(dto.ToJson());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取系统健康评分失败: " + std::string(e.what()));
        return CreateErrorResponse("HEALTH_SCORE_ERROR", "获取健康评分失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::GetMaintenanceAdvice() {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        auto advice = precision_monitor_->GetMaintenanceAdvice();
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"analysis_timestamp_ns\": " << advice.analysis_timestamp_ns << ",\n";
        json << "  \"estimated_days_to_failure\": " << advice.estimated_days_to_failure << ",\n";
        json << "  \"failure_probability\": " << advice.failure_probability << ",\n";
        json << "  \"primary_concern\": \"" << advice.primary_concern << "\",\n";
        
        // Component predictions
        json << "  \"component_predictions\": [\n";
        for (size_t i = 0; i < advice.component_predictions.size(); ++i) {
            const auto& pred = advice.component_predictions[i];
            json << "    {\n";
            json << "      \"component_name\": \"" << pred.component_name << "\",\n";
            json << "      \"health_percentage\": " << pred.health_percentage << ",\n";
            json << "      \"estimated_lifetime_days\": " << pred.estimated_lifetime_days << ",\n";
            json << "      \"maintenance_action\": \"" << pred.maintenance_action << "\",\n";
            json << "      \"urgent\": " << (pred.urgent ? "true" : "false") << "\n";
            json << "    }";
            if (i < advice.component_predictions.size() - 1) json << ",";
            json << "\n";
        }
        json << "  ],\n";
        
        // Actions arrays
        auto writeStringArray = [&json](const std::string& name, const std::vector<std::string>& array, bool isLast = false) {
            json << "  \"" << name << "\": [\n";
            for (size_t i = 0; i < array.size(); ++i) {
                json << "    \"" << array[i] << "\"";
                if (i < array.size() - 1) json << ",";
                json << "\n";
            }
            json << "  ]";
            if (!isLast) json << ",";
            json << "\n";
        };
        
        writeStringArray("immediate_actions", advice.immediate_actions);
        writeStringArray("scheduled_actions", advice.scheduled_actions);
        writeStringArray("preventive_actions", advice.preventive_actions);
        writeStringArray("performance_optimizations", advice.performance_optimizations);
        writeStringArray("configuration_changes", advice.configuration_changes, true);
        
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取维护建议失败: " + std::string(e.what()));
        return CreateErrorResponse("MAINTENANCE_ADVICE_ERROR", "获取维护建议失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::CheckAccuracyRequirement(double required_accuracy_ns) {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        bool meets_requirement = precision_monitor_->MeetsAccuracyRequirement(required_accuracy_ns);
        auto current_measurement = precision_monitor_->GetCurrentMeasurement();
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"required_accuracy_ns\": " << required_accuracy_ns << ",\n";
        json << "  \"current_accuracy_ns\": " << std::abs(current_measurement.absolute_accuracy_ns) << ",\n";
        json << "  \"meets_requirement\": " << (meets_requirement ? "true" : "false") << ",\n";
        json << "  \"system_state\": \"" << core::ClockStateToString(current_measurement.system_state) << "\",\n";
        json << "  \"time_source\": \"" << core::TimeSourceToString(current_measurement.source) << "\"\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "检查精度要求失败: " + std::string(e.what()));
        return CreateErrorResponse("ACCURACY_CHECK_ERROR", "精度检查失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::SetMonitoringLevel(const std::string& level) {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        auto monitoring_level = ParseMonitoringLevel(level);
        precision_monitor_->SetMonitoringLevel(monitoring_level);
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"monitoring_level\": \"" << level << "\",\n";
        json << "  \"message\": \"监控级别已成功设置\"\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "设置监控级别失败: " + std::string(e.what()));
        return CreateErrorResponse("MONITORING_LEVEL_ERROR", "设置监控级别失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::ExportMeasurementData(const std::string& format) {
    try {
        if (!precision_monitor_) {
            return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
        }
        
        if (format != "csv" && format != "json") {
            return CreateErrorResponse("INVALID_FORMAT", "不支持的导出格式，仅支持 'csv' 和 'json'");
        }
        
        std::string filename = GenerateExportFilename(format);
        bool success = precision_monitor_->ExportMeasurementData(filename, format);
        
        if (!success) {
            return CreateErrorResponse("EXPORT_FAILED", "数据导出失败");
        }
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"filename\": \"" << filename << "\",\n";
        json << "  \"format\": \"" << format << "\",\n";
        json << "  \"download_url\": \"/api/v1/precision/download/" << filename << "\",\n";
        json << "  \"message\": \"数据导出成功\"\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "导出测量数据失败: " + std::string(e.what()));
        return CreateErrorResponse("EXPORT_ERROR", "数据导出失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::GetActiveAlarms() {
    try {
        if (!alarm_system_) {
            return CreateErrorResponse("INVALID_ALARM_SYSTEM", "告警系统实例无效");
        }
        
        auto alarms = alarm_system_->GetActiveAlarms();
        
        std::ostringstream json;
        json << "{\n  \"alarms\": [\n";
        
        for (size_t i = 0; i < alarms.size(); ++i) {
            auto dto = AlarmEventDto::FromCore(alarms[i]);
            json << "    " << dto.ToJson();
            if (i < alarms.size() - 1) json << ",";
            json << "\n";
        }
        
        json << "  ],\n";
        json << "  \"total_count\": " << alarms.size() << "\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取活跃告警失败: " + std::string(e.what()));
        return CreateErrorResponse("ACTIVE_ALARMS_ERROR", "获取活跃告警失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::GetHistoricalAlarms(uint64_t start_time, uint64_t end_time, uint32_t max_count) {
    try {
        if (!alarm_system_) {
            return CreateErrorResponse("INVALID_ALARM_SYSTEM", "告警系统实例无效");
        }
        
        auto alarms = alarm_system_->GetHistoricalAlarms(start_time, end_time, max_count);
        
        std::ostringstream json;
        json << "{\n  \"alarms\": [\n";
        
        for (size_t i = 0; i < alarms.size(); ++i) {
            auto dto = AlarmEventDto::FromCore(alarms[i]);
            json << "    " << dto.ToJson();
            if (i < alarms.size() - 1) json << ",";
            json << "\n";
        }
        
        json << "  ],\n";
        json << "  \"total_count\": " << alarms.size() << ",\n";
        json << "  \"start_time\": " << start_time << ",\n";
        json << "  \"end_time\": " << end_time << "\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取历史告警失败: " + std::string(e.what()));
        return CreateErrorResponse("HISTORICAL_ALARMS_ERROR", "获取历史告警失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::AcknowledgeAlarm(uint64_t alarm_id, const std::string& acknowledged_by, 
                                                   const std::string& notes) {
    try {
        if (!alarm_system_) {
            return CreateErrorResponse("INVALID_ALARM_SYSTEM", "告警系统实例无效");
        }
        
        bool success = alarm_system_->AcknowledgeAlarm(alarm_id, acknowledged_by, notes);
        
        if (!success) {
            return CreateErrorResponse("ACKNOWLEDGE_FAILED", "告警确认失败，可能告警不存在或状态不正确");
        }
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"alarm_id\": " << alarm_id << ",\n";
        json << "  \"acknowledged_by\": \"" << acknowledged_by << "\",\n";
        json << "  \"notes\": \"" << notes << "\",\n";
        json << "  \"message\": \"告警已成功确认\"\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "确认告警失败: " + std::string(e.what()));
        return CreateErrorResponse("ACKNOWLEDGE_ERROR", "确认告警失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::ResolveAlarm(uint64_t alarm_id, const std::string& resolution_notes) {
    try {
        if (!alarm_system_) {
            return CreateErrorResponse("INVALID_ALARM_SYSTEM", "告警系统实例无效");
        }
        
        bool success = alarm_system_->ResolveAlarm(alarm_id, resolution_notes);
        
        if (!success) {
            return CreateErrorResponse("RESOLVE_FAILED", "告警解决失败，可能告警不存在");
        }
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"alarm_id\": " << alarm_id << ",\n";
        json << "  \"resolution_notes\": \"" << resolution_notes << "\",\n";
        json << "  \"message\": \"告警已成功解决\"\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "解决告警失败: " + std::string(e.what()));
        return CreateErrorResponse("RESOLVE_ERROR", "解决告警失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::SuppressAlarm(uint64_t alarm_id, uint32_t duration_seconds) {
    try {
        if (!alarm_system_) {
            return CreateErrorResponse("INVALID_ALARM_SYSTEM", "告警系统实例无效");
        }
        
        bool success = alarm_system_->SuppressAlarm(alarm_id, duration_seconds);
        
        if (!success) {
            return CreateErrorResponse("SUPPRESS_FAILED", "告警抑制失败，可能告警不存在");
        }
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"alarm_id\": " << alarm_id << ",\n";
        json << "  \"duration_seconds\": " << duration_seconds << ",\n";
        json << "  \"message\": \"告警已成功抑制\"\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "抑制告警失败: " + std::string(e.what()));
        return CreateErrorResponse("SUPPRESS_ERROR", "抑制告警失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::GetAlarmStatistics() {
    try {
        if (!alarm_system_) {
            return CreateErrorResponse("INVALID_ALARM_SYSTEM", "告警系统实例无效");
        }
        
        auto stats = alarm_system_->GetAlarmStatistics();
        auto dto = AlarmStatisticsDto::FromCore(stats);
        
        return CreateSuccessResponse(dto.ToJson());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "获取告警统计失败: " + std::string(e.what()));
        return CreateErrorResponse("STATISTICS_ERROR", "获取告警统计失败: " + std::string(e.what()));
    }
}

std::string PrecisionApiController::ClearHistoricalData(const std::string& data_type, uint32_t older_than_hours) {
    try {
        if (data_type == "measurements") {
            if (!precision_monitor_) {
                return CreateErrorResponse("INVALID_MONITOR", "精度监控器实例无效");
            }
            precision_monitor_->ClearHistoricalData(older_than_hours);
        } else if (data_type == "alarms") {
            if (!alarm_system_) {
                return CreateErrorResponse("INVALID_ALARM_SYSTEM", "告警系统实例无效");
            }
            // 假设告警系统有类似的清理方法
            // alarm_system_->ClearHistoricalAlarms(older_than_hours);
        } else {
            return CreateErrorResponse("INVALID_DATA_TYPE", "无效的数据类型，仅支持 'measurements' 和 'alarms'");
        }
        
        std::ostringstream json;
        json << "{\n";
        json << "  \"data_type\": \"" << data_type << "\",\n";
        json << "  \"older_than_hours\": " << older_than_hours << ",\n";
        json << "  \"message\": \"历史数据已成功清除\"\n";
        json << "}";
        
        return CreateSuccessResponse(json.str());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, "清除历史数据失败: " + std::string(e.what()));
        return CreateErrorResponse("CLEAR_DATA_ERROR", "清除历史数据失败: " + std::string(e.what()));
    }
}

// Private helper methods

std::string PrecisionApiController::CreateSuccessResponse(const std::string& data) {
    std::ostringstream response;
    response << "{\n";
    response << "  \"success\": true,\n";
    response << "  \"timestamp\": " << core::GetCurrentTimestampNs() << ",\n";
    response << "  \"data\": " << data << "\n";
    response << "}";
    return response.str();
}

std::string PrecisionApiController::CreateErrorResponse(const std::string& error_code, const std::string& error_message) {
    std::ostringstream response;
    response << "{\n";
    response << "  \"success\": false,\n";
    response << "  \"timestamp\": " << core::GetCurrentTimestampNs() << ",\n";
    response << "  \"error\": {\n";
    response << "    \"code\": \"" << error_code << "\",\n";
    response << "    \"message\": \"" << error_message << "\"\n";
    response << "  }\n";
    response << "}";
    return response.str();
}

core::MonitoringLevel PrecisionApiController::ParseMonitoringLevel(const std::string& level_str) {
    if (level_str == "BASIC" || level_str == "basic") {
        return core::MonitoringLevel::BASIC;
    } else if (level_str == "STANDARD" || level_str == "standard") {
        return core::MonitoringLevel::STANDARD;
    } else if (level_str == "ADVANCED" || level_str == "advanced") {
        return core::MonitoringLevel::ADVANCED;
    } else if (level_str == "DIAGNOSTIC" || level_str == "diagnostic") {
        return core::MonitoringLevel::DIAGNOSTIC;
    } else {
        throw std::invalid_argument("无效的监控级别: " + level_str);
    }
}

std::string PrecisionApiController::GenerateExportFilename(const std::string& format) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    std::ostringstream filename;
    filename << "precision_measurements_";
    filename << std::put_time(&tm, "%Y%m%d_%H%M%S");
    filename << "." << format;
    
    return filename.str();
}

} // namespace api
} // namespace timing_server