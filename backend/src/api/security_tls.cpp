#include "api/security_manager.h"
#include <core/logger.h>
#include <openssl/ssl.h>
#include <openssl/err.h>
#include <openssl/x509.h>
#include <openssl/x509v3.h>
#include <openssl/pem.h>
#include <openssl/rsa.h>
#include <openssl/evp.h>

namespace timing_server {
namespace api {

bool SecurityManager::validateTlsCertificate(const std::string& cert_file,
                                            const std::string& key_file) const {
    try {
        // 读取证书文件
        FILE* cert_fp = fopen(cert_file.c_str(), "r");
        if (!cert_fp) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "无法打开证书文件: " + cert_file);
            return false;
        }
        
        X509* cert = PEM_read_X509(cert_fp, nullptr, nullptr, nullptr);
        fclose(cert_fp);
        
        if (!cert) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "无法解析证书文件: " + cert_file);
            return false;
        }
        
        // 读取私钥文件
        FILE* key_fp = fopen(key_file.c_str(), "r");
        if (!key_fp) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "无法打开私钥文件: " + key_file);
            X509_free(cert);
            return false;
        }
        
        EVP_PKEY* pkey = PEM_read_PrivateKey(key_fp, nullptr, nullptr, nullptr);
        fclose(key_fp);
        
        if (!pkey) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "无法解析私钥文件: " + key_file);
            X509_free(cert);
            return false;
        }
        
        // 验证证书和私钥是否匹配
        EVP_PKEY* cert_pkey = X509_get_pubkey(cert);
        bool keys_match = EVP_PKEY_eq(cert_pkey, pkey) == 1;
        
        // 检查证书是否过期
        time_t now = time(nullptr);
        bool cert_valid = (X509_cmp_time(X509_get_notBefore(cert), &now) <= 0) &&
                         (X509_cmp_time(X509_get_notAfter(cert), &now) >= 0);
        
        // 清理资源
        EVP_PKEY_free(cert_pkey);
        EVP_PKEY_free(pkey);
        X509_free(cert);
        
        if (!keys_match) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "证书和私钥不匹配");
            return false;
        }
        
        if (!cert_valid) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "证书已过期或尚未生效");
            return false;
        }
        
        LOG_INFO(core::LogComponent::API_SERVER, 
                "TLS证书验证成功: " + cert_file);
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "验证TLS证书异常: " + std::string(e.what()));
        return false;
    }
}

bool SecurityManager::generateSelfSignedCertificate(const std::string& cert_file,
                                                   const std::string& key_file,
                                                   const std::string& common_name,
                                                   uint32_t days_valid) const {
    try {
        // 生成RSA密钥对 (使用现代OpenSSL 3.0 API)
        EVP_PKEY* pkey = nullptr;
        EVP_PKEY_CTX* ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, nullptr);
        
        if (!ctx) {
            LOG_ERROR(core::LogComponent::API_SERVER, "创建密钥上下文失败");
            return false;
        }
        
        if (EVP_PKEY_keygen_init(ctx) <= 0) {
            LOG_ERROR(core::LogComponent::API_SERVER, "初始化密钥生成失败");
            EVP_PKEY_CTX_free(ctx);
            return false;
        }
        
        if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, 2048) <= 0) {
            LOG_ERROR(core::LogComponent::API_SERVER, "设置RSA密钥长度失败");
            EVP_PKEY_CTX_free(ctx);
            return false;
        }
        
        if (EVP_PKEY_keygen(ctx, &pkey) <= 0) {
            LOG_ERROR(core::LogComponent::API_SERVER, "生成RSA密钥失败");
            EVP_PKEY_CTX_free(ctx);
            return false;
        }
        
        EVP_PKEY_CTX_free(ctx);
        
        // 创建X509证书
        X509* cert = X509_new();
        if (!cert) {
            LOG_ERROR(core::LogComponent::API_SERVER, "创建X509证书失败");
            EVP_PKEY_free(pkey);
            return false;
        }
        
        // 设置证书版本
        X509_set_version(cert, 2);
        
        // 设置序列号
        ASN1_INTEGER_set(X509_get_serialNumber(cert), 1);
        
        // 设置有效期
        X509_gmtime_adj(X509_get_notBefore(cert), 0);
        X509_gmtime_adj(X509_get_notAfter(cert), days_valid * 24 * 3600);
        
        // 设置公钥
        X509_set_pubkey(cert, pkey);
        
        // 设置证书主题
        X509_NAME* name = X509_get_subject_name(cert);
        X509_NAME_add_entry_by_txt(name, "C", MBSTRING_ASC, 
                                  reinterpret_cast<const unsigned char*>("CN"), -1, -1, 0);
        X509_NAME_add_entry_by_txt(name, "O", MBSTRING_ASC, 
                                  reinterpret_cast<const unsigned char*>("Timing Server"), -1, -1, 0);
        X509_NAME_add_entry_by_txt(name, "CN", MBSTRING_ASC, 
                                  reinterpret_cast<const unsigned char*>(common_name.c_str()), -1, -1, 0);
        
        // 设置颁发者（自签名）
        X509_set_issuer_name(cert, name);
        
        // 签名证书
        if (X509_sign(cert, pkey, EVP_sha256()) == 0) {
            LOG_ERROR(core::LogComponent::API_SERVER, "签名证书失败");
            X509_free(cert);
            EVP_PKEY_free(pkey);
            return false;
        }
        
        // 保存证书到文件
        FILE* cert_fp = fopen(cert_file.c_str(), "w");
        if (!cert_fp) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "无法创建证书文件: " + cert_file);
            X509_free(cert);
            EVP_PKEY_free(pkey);
            return false;
        }
        
        PEM_write_X509(cert_fp, cert);
        fclose(cert_fp);
        
        // 保存私钥到文件
        FILE* key_fp = fopen(key_file.c_str(), "w");
        if (!key_fp) {
            LOG_ERROR(core::LogComponent::API_SERVER, 
                     "无法创建私钥文件: " + key_file);
            X509_free(cert);
            EVP_PKEY_free(pkey);
            return false;
        }
        
        PEM_write_PrivateKey(key_fp, pkey, nullptr, nullptr, 0, nullptr, nullptr);
        fclose(key_fp);
        
        // 清理资源
        X509_free(cert);
        EVP_PKEY_free(pkey);
        
        LOG_INFO(core::LogComponent::API_SERVER, 
                "自签名证书生成成功: " + cert_file + ", " + key_file);
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::API_SERVER, 
                 "生成自签名证书异常: " + std::string(e.what()));
        return false;
    }
}

} // namespace api
} // namespace timing_server