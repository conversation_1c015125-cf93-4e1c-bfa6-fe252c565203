#include "api/auth_manager.h"
#include <core/logger.h>
#include <core/types.h>
#include <api/json_fallback.h>
#include <random>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <fstream>
#include <cstring>

// 简化的JsonValue类，用于兼容现有代码
class JsonValue {
public:
    JsonValue() = default;
    JsonValue(const std::string& str) : json_(str) {}
    JsonValue(const char* str) : json_(std::string(str)) {}
    JsonValue(double val) : json_(val) {}
    JsonValue(bool val) : json_(val) {}
    
    void setObject() { json_ = nlohmann::json(); }
    void setArray() { json_ = nlohmann::json::array(); }
    
    JsonValue& operator[](const std::string& key) {
        static JsonValue temp;
        temp.json_ = json_[key];
        return temp;
    }
    
    const JsonValue operator[](const std::string& key) const {
        JsonValue temp;
        temp.json_ = json_[key];
        return temp;
    }
    
    void append(const JsonValue& value) {
        json_.push_back(value.json_);
    }
    
    std::string toString() const {
        return json_.dump();
    }
    
    bool parse(const std::string& str) {
        try {
            json_ = nlohmann::json::parse(str);
            return true;
        } catch (...) {
            return false;
        }
    }
    
    std::string asString() const {
        return json_.is_string() ? json_.get_string() : "";
    }
    
    double asNumber() const {
        return json_.is_number() ? json_.get_double() : 0.0;
    }
    
    bool asBool() const {
        return json_.is_boolean() ? json_.get_bool() : false;
    }
    
    bool isArray() const {
        return json_.is_array();
    }
    
    size_t size() const {
        // 简化实现
        return 0;
    }
    
    JsonValue operator[](size_t index) const {
        // 简化实现
        JsonValue temp;
        return temp;
    }
    
private:
    nlohmann::json json_;
};

// 简化的SHA256实现（用于演示，生产环境应使用OpenSSL）
namespace {
    std::string simple_sha256(const std::string& input) {
        // 这是一个简化的哈希实现，仅用于演示
        // 生产环境应该使用OpenSSL或其他加密库
        std::hash<std::string> hasher;
        size_t hash_value = hasher(input);
        
        std::ostringstream oss;
        oss << std::hex << hash_value;
        return oss.str();
    }
    
    std::string base64_encode_simple(const std::string& input) {
        // 简化的Base64编码实现
        const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;
        int val = 0, valb = -6;
        
        for (unsigned char c : input) {
            val = (val << 8) + c;
            valb += 8;
            while (valb >= 0) {
                result.push_back(chars[(val >> valb) & 0x3F]);
                valb -= 6;
            }
        }
        
        if (valb > -6) {
            result.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
        }
        
        while (result.size() % 4) {
            result.push_back('=');
        }
        
        return result;
    }
    
    std::string base64_decode_simple(const std::string& input) {
        // 简化的Base64解码实现
        const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;
        int val = 0, valb = -8;
        
        for (unsigned char c : input) {
            if (c == '=') break;
            auto pos = chars.find(c);
            if (pos == std::string::npos) continue;
            
            val = (val << 6) + pos;
            valb += 6;
            if (valb >= 0) {
                result.push_back(char((val >> valb) & 0xFF));
                valb -= 8;
            }
        }
        
        return result;
    }
}

namespace timing_server {
namespace api {

AuthManager::AuthManager(const std::string& jwt_secret, 
                        uint32_t token_expiry_seconds,
                        uint32_t refresh_token_expiry_seconds)
    : m_jwt_secret(jwt_secret)
    , m_token_expiry_seconds(token_expiry_seconds)
    , m_refresh_token_expiry_seconds(refresh_token_expiry_seconds) {
    
    LOG_INFO(core::LogComponent::AUTH_MANAGER, 
             "认证管理器已创建，令牌过期时间: " + std::to_string(token_expiry_seconds) + "秒");
}

AuthManager::~AuthManager() {
    LOG_INFO(core::LogComponent::AUTH_MANAGER, "认证管理器已销毁");
}

bool AuthManager::initialize() {
    try {
        // 加载现有用户数据
        if (!loadUsers()) {
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, "加载用户数据失败，将创建默认配置");
        }
        
        // 如果没有用户，创建默认管理员
        if (m_users.empty()) {
            if (!createDefaultAdmin()) {
                LOG_ERROR(core::LogComponent::AUTH_MANAGER, "创建默认管理员失败");
                return false;
            }
        }
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "认证管理器初始化成功，用户数量: " + std::to_string(m_users.size()));
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "认证管理器初始化失败: " + std::string(e.what()));
        return false;
    }
}

std::pair<std::string, std::string> AuthManager::authenticate(const std::string& username,
                                                             const std::string& password,
                                                             const std::string& ip_address,
                                                             const std::string& user_agent) {
    std::lock_guard<std::mutex> lock(m_users_mutex);
    
    try {
        // 查找用户
        auto user_it = m_users.find(username);
        if (user_it == m_users.end()) {
            logAudit(username, "LOGIN_FAILED", "/api/v1/auth/login", 
                    ip_address, user_agent, false, "用户不存在");
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "登录失败，用户不存在: " + username + " from " + ip_address);
            return {"", ""};
        }
        
        const UserInfo& user = user_it->second;
        
        // 检查用户是否激活
        if (!user.is_active) {
            logAudit(username, "LOGIN_FAILED", "/api/v1/auth/login", 
                    ip_address, user_agent, false, "用户已禁用");
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "登录失败，用户已禁用: " + username);
            return {"", ""};
        }
        
        // 验证密码
        if (!verifyPassword(password, user.password_hash, user.salt)) {
            logAudit(username, "LOGIN_FAILED", "/api/v1/auth/login", 
                    ip_address, user_agent, false, "密码错误");
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "登录失败，密码错误: " + username + " from " + ip_address);
            return {"", ""};
        }
        
        // 生成访问令牌和刷新令牌
        std::string access_token = generateJWT(username, user.role, user.permissions, m_token_expiry_seconds);
        std::string refresh_token = generateJWT(username, user.role, user.permissions, m_refresh_token_expiry_seconds);
        
        // 更新最后登录时间
        const_cast<UserInfo&>(user).last_login = core::GetCurrentTimestampNs() / 1000000; // 转换为毫秒
        
        // 创建会话信息
        {
            std::lock_guard<std::mutex> session_lock(m_sessions_mutex);
            SessionInfo session;
            session.username = username;
            session.ip_address = ip_address;
            session.user_agent = user_agent;
            session.created_at = core::GetCurrentTimestampNs() / 1000000;
            session.last_activity = session.created_at;
            session.is_active = true;
            
            m_sessions[access_token] = session;
        }
        
        // 记录成功登录
        logAudit(username, "LOGIN_SUCCESS", "/api/v1/auth/login", 
                ip_address, user_agent, true, "登录成功");
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "用户登录成功: " + username + " from " + ip_address);
        
        return {access_token, refresh_token};
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "用户认证异常: " + std::string(e.what()));
        logAudit(username, "LOGIN_ERROR", "/api/v1/auth/login", 
                ip_address, user_agent, false, "系统异常");
        return {"", ""};
    }
}

std::unique_ptr<TokenInfo> AuthManager::validateToken(const std::string& token) {
    try {
        auto token_info = parseJWT(token);
        if (!token_info) {
            return nullptr;
        }
        
        // 检查令牌是否过期
        uint64_t current_time = core::GetCurrentTimestampNs() / 1000000; // 转换为毫秒
        if (current_time > token_info->expires_at) {
            LOG_DEBUG(core::LogComponent::AUTH_MANAGER, 
                     "令牌已过期: " + token_info->username);
            return nullptr;
        }
        
        // 检查用户是否仍然存在且激活
        {
            std::lock_guard<std::mutex> lock(m_users_mutex);
            auto user_it = m_users.find(token_info->username);
            if (user_it == m_users.end() || !user_it->second.is_active) {
                LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                           "令牌对应的用户不存在或已禁用: " + token_info->username);
                return nullptr;
            }
        }
        
        // 更新会话活动时间
        {
            std::lock_guard<std::mutex> session_lock(m_sessions_mutex);
            auto session_it = m_sessions.find(token);
            if (session_it != m_sessions.end()) {
                session_it->second.last_activity = current_time;
            }
        }
        
        return token_info;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "令牌验证异常: " + std::string(e.what()));
        return nullptr;
    }
}

std::pair<std::string, std::string> AuthManager::refreshToken(const std::string& refresh_token,
                                                             const std::string& ip_address) {
    try {
        auto token_info = parseJWT(refresh_token);
        if (!token_info) {
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "刷新令牌无效 from " + ip_address);
            return {"", ""};
        }
        
        // 检查刷新令牌是否过期
        uint64_t current_time = core::GetCurrentTimestampNs() / 1000000;
        if (current_time > token_info->expires_at) {
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "刷新令牌已过期: " + token_info->username);
            return {"", ""};
        }
        
        // 检查用户状态
        std::lock_guard<std::mutex> lock(m_users_mutex);
        auto user_it = m_users.find(token_info->username);
        if (user_it == m_users.end() || !user_it->second.is_active) {
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "刷新令牌对应的用户不存在或已禁用: " + token_info->username);
            return {"", ""};
        }
        
        const UserInfo& user = user_it->second;
        
        // 生成新的访问令牌和刷新令牌
        std::string new_access_token = generateJWT(user.username, user.role, user.permissions, m_token_expiry_seconds);
        std::string new_refresh_token = generateJWT(user.username, user.role, user.permissions, m_refresh_token_expiry_seconds);
        
        // 记录令牌刷新
        logAudit(token_info->username, "TOKEN_REFRESH", "/api/v1/auth/refresh", 
                ip_address, "", true, "令牌刷新成功");
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "令牌刷新成功: " + token_info->username + " from " + ip_address);
        
        return {new_access_token, new_refresh_token};
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "令牌刷新异常: " + std::string(e.what()));
        return {"", ""};
    }
}

bool AuthManager::logout(const std::string& token, const std::string& ip_address) {
    try {
        auto token_info = validateToken(token);
        if (!token_info) {
            return false;
        }
        
        // 移除会话信息
        {
            std::lock_guard<std::mutex> session_lock(m_sessions_mutex);
            m_sessions.erase(token);
        }
        
        // 记录登出
        logAudit(token_info->username, "LOGOUT", "/api/v1/auth/logout", 
                ip_address, "", true, "用户登出");
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "用户登出: " + token_info->username + " from " + ip_address);
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "用户登出异常: " + std::string(e.what()));
        return false;
    }
}

bool AuthManager::hasPermission(const std::string& username, Permission permission) {
    std::lock_guard<std::mutex> lock(m_users_mutex);
    
    auto user_it = m_users.find(username);
    if (user_it == m_users.end() || !user_it->second.is_active) {
        return false;
    }
    
    const auto& permissions = user_it->second.permissions;
    return std::find(permissions.begin(), permissions.end(), permission) != permissions.end();
}

bool AuthManager::createUser(const std::string& username,
                            const std::string& password,
                            UserRole role,
                            const std::string& creator,
                            const std::string& ip_address) {
    std::lock_guard<std::mutex> lock(m_users_mutex);
    
    try {
        // 检查用户是否已存在
        if (m_users.find(username) != m_users.end()) {
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "创建用户失败，用户已存在: " + username);
            logAudit(creator, "CREATE_USER_FAILED", "/api/v1/users", 
                    ip_address, "", false, "用户已存在: " + username);
            return false;
        }
        
        // 创建用户信息
        UserInfo user;
        user.username = username;
        user.salt = generateSalt();
        user.password_hash = hashPassword(password, user.salt);
        user.role = role;
        user.permissions = getRolePermissions(role);
        user.created_at = core::GetCurrentTimestampNs() / 1000000;
        user.last_login = 0;
        user.is_active = true;
        
        m_users[username] = user;
        
        // 保存用户数据
        saveUsers();
        
        // 记录用户创建
        logAudit(creator, "CREATE_USER", "/api/v1/users", 
                ip_address, "", true, "创建用户: " + username + ", 角色: " + roleToString(role));
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "用户创建成功: " + username + " by " + creator);
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "创建用户异常: " + std::string(e.what()));
        logAudit(creator, "CREATE_USER_ERROR", "/api/v1/users", 
                ip_address, "", false, "系统异常");
        return false;
    }
}

bool AuthManager::updateUser(const std::string& username,
                            const std::string& new_password,
                            UserRole new_role,
                            bool is_active,
                            const std::string& updater,
                            const std::string& ip_address) {
    std::lock_guard<std::mutex> lock(m_users_mutex);
    
    try {
        auto user_it = m_users.find(username);
        if (user_it == m_users.end()) {
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "更新用户失败，用户不存在: " + username);
            logAudit(updater, "UPDATE_USER_FAILED", "/api/v1/users/" + username, 
                    ip_address, "", false, "用户不存在");
            return false;
        }
        
        UserInfo& user = user_it->second;
        std::string changes;
        
        // 更新密码
        if (!new_password.empty()) {
            user.salt = generateSalt();
            user.password_hash = hashPassword(new_password, user.salt);
            changes += "密码已更新; ";
        }
        
        // 更新角色
        if (user.role != new_role) {
            user.role = new_role;
            user.permissions = getRolePermissions(new_role);
            changes += "角色更新为: " + roleToString(new_role) + "; ";
        }
        
        // 更新激活状态
        if (user.is_active != is_active) {
            user.is_active = is_active;
            changes += "激活状态: " + std::string(is_active ? "激活" : "禁用") + "; ";
            
            // 如果禁用用户，终止其所有会话
            if (!is_active) {
                terminateUserSessions(username, updater, ip_address);
            }
        }
        
        // 保存用户数据
        saveUsers();
        
        // 记录用户更新
        logAudit(updater, "UPDATE_USER", "/api/v1/users/" + username, 
                ip_address, "", true, "更新用户: " + username + ", 变更: " + changes);
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "用户更新成功: " + username + " by " + updater + ", 变更: " + changes);
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "更新用户异常: " + std::string(e.what()));
        logAudit(updater, "UPDATE_USER_ERROR", "/api/v1/users/" + username, 
                ip_address, "", false, "系统异常");
        return false;
    }
}

bool AuthManager::deleteUser(const std::string& username,
                            const std::string& deleter,
                            const std::string& ip_address) {
    std::lock_guard<std::mutex> lock(m_users_mutex);
    
    try {
        auto user_it = m_users.find(username);
        if (user_it == m_users.end()) {
            LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                       "删除用户失败，用户不存在: " + username);
            logAudit(deleter, "DELETE_USER_FAILED", "/api/v1/users/" + username, 
                    ip_address, "", false, "用户不存在");
            return false;
        }
        
        // 不允许删除管理员用户（如果只有一个管理员）
        if (user_it->second.role == UserRole::ADMINISTRATOR) {
            int admin_count = 0;
            for (const auto& pair : m_users) {
                if (pair.second.role == UserRole::ADMINISTRATOR && pair.second.is_active) {
                    admin_count++;
                }
            }
            
            if (admin_count <= 1) {
                LOG_WARNING(core::LogComponent::AUTH_MANAGER, 
                           "删除用户失败，不能删除最后一个管理员: " + username);
                logAudit(deleter, "DELETE_USER_FAILED", "/api/v1/users/" + username, 
                        ip_address, "", false, "不能删除最后一个管理员");
                return false;
            }
        }
        
        // 终止用户所有会话
        terminateUserSessions(username, deleter, ip_address);
        
        // 删除用户
        m_users.erase(user_it);
        
        // 保存用户数据
        saveUsers();
        
        // 记录用户删除
        logAudit(deleter, "DELETE_USER", "/api/v1/users/" + username, 
                ip_address, "", true, "删除用户: " + username);
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "用户删除成功: " + username + " by " + deleter);
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "删除用户异常: " + std::string(e.what()));
        logAudit(deleter, "DELETE_USER_ERROR", "/api/v1/users/" + username, 
                ip_address, "", false, "系统异常");
        return false;
    }
}

std::unique_ptr<UserInfo> AuthManager::getUserInfo(const std::string& username) {
    std::lock_guard<std::mutex> lock(m_users_mutex);
    
    auto user_it = m_users.find(username);
    if (user_it == m_users.end()) {
        return nullptr;
    }
    
    return std::make_unique<UserInfo>(user_it->second);
}

std::pair<std::vector<UserInfo>, uint32_t> AuthManager::getUserList(uint32_t page, uint32_t limit) {
    std::lock_guard<std::mutex> lock(m_users_mutex);
    
    std::vector<UserInfo> users;
    users.reserve(m_users.size());
    
    for (const auto& pair : m_users) {
        users.push_back(pair.second);
    }
    
    // 按创建时间排序
    std::sort(users.begin(), users.end(), 
              [](const UserInfo& a, const UserInfo& b) {
                  return a.created_at > b.created_at;
              });
    
    uint32_t total = users.size();
    uint32_t start = (page - 1) * limit;
    
    if (start >= total) {
        return {std::vector<UserInfo>(), total};
    }
    
    uint32_t end = std::min(start + limit, total);
    std::vector<UserInfo> result(users.begin() + start, users.begin() + end);
    
    return {result, total};
}

void AuthManager::logAudit(const std::string& username,
                          const std::string& action,
                          const std::string& resource,
                          const std::string& ip_address,
                          const std::string& user_agent,
                          bool success,
                          const std::string& details) {
    std::lock_guard<std::mutex> lock(m_audit_mutex);
    
    AuditLogEntry entry;
    entry.timestamp = core::GetCurrentTimestampNs() / 1000000; // 转换为毫秒
    entry.username = username;
    entry.action = action;
    entry.resource = resource;
    entry.ip_address = ip_address;
    entry.user_agent = user_agent;
    entry.success = success;
    entry.details = details;
    
    m_audit_logs.push_back(entry);
    
    // 限制审计日志数量，保留最近的10000条
    if (m_audit_logs.size() > 10000) {
        m_audit_logs.erase(m_audit_logs.begin(), m_audit_logs.begin() + 1000);
    }
    
    LOG_INFO(core::LogComponent::AUTH_MANAGER, 
             "审计日志: " + username + " " + action + " " + resource + 
             " from " + ip_address + " " + (success ? "成功" : "失败"));
}

std::pair<std::vector<AuditLogEntry>, uint32_t> AuthManager::getAuditLogs(
    uint32_t page, uint32_t limit,
    const std::string& username,
    const std::string& action,
    uint64_t from_time,
    uint64_t to_time) {
    
    std::lock_guard<std::mutex> lock(m_audit_mutex);
    
    std::vector<AuditLogEntry> filtered_logs;
    
    for (const auto& entry : m_audit_logs) {
        // 应用过滤条件
        if (!username.empty() && entry.username != username) continue;
        if (!action.empty() && entry.action != action) continue;
        if (from_time > 0 && entry.timestamp < from_time) continue;
        if (to_time > 0 && entry.timestamp > to_time) continue;
        
        filtered_logs.push_back(entry);
    }
    
    // 按时间倒序排序
    std::sort(filtered_logs.begin(), filtered_logs.end(),
              [](const AuditLogEntry& a, const AuditLogEntry& b) {
                  return a.timestamp > b.timestamp;
              });
    
    uint32_t total = filtered_logs.size();
    uint32_t start = (page - 1) * limit;
    
    if (start >= total) {
        return {std::vector<AuditLogEntry>(), total};
    }
    
    uint32_t end = std::min(start + limit, total);
    std::vector<AuditLogEntry> result(filtered_logs.begin() + start, filtered_logs.begin() + end);
    
    return {result, total};
}

std::vector<SessionInfo> AuthManager::getActiveSessions() {
    std::lock_guard<std::mutex> lock(m_sessions_mutex);
    
    std::vector<SessionInfo> sessions;
    uint64_t current_time = core::GetCurrentTimestampNs() / 1000000;
    
    for (const auto& pair : m_sessions) {
        const SessionInfo& session = pair.second;
        
        // 检查会话是否仍然活跃（30分钟内有活动）
        if (session.is_active && (current_time - session.last_activity) < 1800000) {
            sessions.push_back(session);
        }
    }
    
    return sessions;
}

bool AuthManager::terminateUserSessions(const std::string& username,
                                       const std::string& admin_username,
                                       const std::string& ip_address) {
    std::lock_guard<std::mutex> lock(m_sessions_mutex);
    
    int terminated_count = 0;
    auto it = m_sessions.begin();
    
    while (it != m_sessions.end()) {
        if (it->second.username == username) {
            it = m_sessions.erase(it);
            terminated_count++;
        } else {
            ++it;
        }
    }
    
    if (terminated_count > 0) {
        logAudit(admin_username, "TERMINATE_SESSIONS", "/api/v1/sessions/" + username, 
                ip_address, "", true, 
                "终止用户会话: " + username + ", 数量: " + std::to_string(terminated_count));
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "终止用户会话: " + username + ", 数量: " + std::to_string(terminated_count) + 
                " by " + admin_username);
    }
    
    return terminated_count > 0;
}

void AuthManager::cleanupExpiredTokens() {
    uint64_t current_time = core::GetCurrentTimestampNs() / 1000000;
    int cleaned_count = 0;
    
    {
        std::lock_guard<std::mutex> lock(m_sessions_mutex);
        auto it = m_sessions.begin();
        
        while (it != m_sessions.end()) {
            // 清理超过2小时无活动的会话
            if ((current_time - it->second.last_activity) > 7200000) {
                it = m_sessions.erase(it);
                cleaned_count++;
            } else {
                ++it;
            }
        }
    }
    
    if (cleaned_count > 0) {
        LOG_DEBUG(core::LogComponent::AUTH_MANAGER, 
                 "清理过期会话: " + std::to_string(cleaned_count) + "个");
    }
}

// 静态方法实现

std::vector<Permission> AuthManager::getRolePermissions(UserRole role) {
    switch (role) {
        case UserRole::VIEWER:
            return {Permission::READ_STATUS, Permission::READ_LOGS};
            
        case UserRole::OPERATOR:
            return {Permission::READ_STATUS, Permission::READ_LOGS, 
                   Permission::READ_CONFIG, Permission::WRITE_CONFIG};
            
        case UserRole::ADMINISTRATOR:
            return {Permission::READ_STATUS, Permission::READ_LOGS, 
                   Permission::READ_CONFIG, Permission::WRITE_CONFIG,
                   Permission::CONTROL_SYSTEM, Permission::MANAGE_USERS, 
                   Permission::VIEW_AUDIT};
            
        default:
            return {};
    }
}

std::string AuthManager::roleToString(UserRole role) {
    switch (role) {
        case UserRole::VIEWER: return "viewer";
        case UserRole::OPERATOR: return "operator";
        case UserRole::ADMINISTRATOR: return "administrator";
        default: return "unknown";
    }
}

UserRole AuthManager::stringToRole(const std::string& role_str) {
    if (role_str == "viewer") return UserRole::VIEWER;
    if (role_str == "operator") return UserRole::OPERATOR;
    if (role_str == "administrator") return UserRole::ADMINISTRATOR;
    return UserRole::VIEWER; // 默认为查看者
}

std::string AuthManager::permissionToString(Permission permission) {
    switch (permission) {
        case Permission::READ_STATUS: return "read_status";
        case Permission::READ_LOGS: return "read_logs";
        case Permission::READ_CONFIG: return "read_config";
        case Permission::WRITE_CONFIG: return "write_config";
        case Permission::CONTROL_SYSTEM: return "control_system";
        case Permission::MANAGE_USERS: return "manage_users";
        case Permission::VIEW_AUDIT: return "view_audit";
        default: return "unknown";
    }
}

// 私有方法实现

std::string AuthManager::hashPassword(const std::string& password, const std::string& salt) {
    std::string salted_password = salt + password;
    return simple_sha256(salted_password);
}

std::string AuthManager::generateSalt() {
    const int salt_length = 16;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);
    
    std::ostringstream oss;
    for (int i = 0; i < salt_length; i++) {
        unsigned char byte = static_cast<unsigned char>(dis(gen));
        oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(byte);
    }
    
    return oss.str();
}

bool AuthManager::verifyPassword(const std::string& password, 
                                const std::string& hash, 
                                const std::string& salt) {
    return hashPassword(password, salt) == hash;
}

std::string AuthManager::generateJWT(const std::string& username,
                                    UserRole role,
                                    const std::vector<Permission>& permissions,
                                    uint32_t expires_in) {
    try {
        // JWT Header
        JsonValue header;
        header.setObject();
        header["alg"] = JsonValue("HS256");
        header["typ"] = JsonValue("JWT");
        
        std::string header_str = header.toString();
        
        // JWT Payload
        uint64_t current_time = core::GetCurrentTimestampNs() / 1000000000; // 转换为秒
        
        JsonValue payload;
        payload.setObject();
        payload["sub"] = JsonValue(username);
        payload["role"] = JsonValue(roleToString(role));
        payload["iat"] = JsonValue(static_cast<double>(current_time));
        payload["exp"] = JsonValue(static_cast<double>(current_time + expires_in));
        payload["jti"] = JsonValue(generateTokenId());
        
        JsonValue perms;
        perms.setArray();
        for (const auto& perm : permissions) {
            perms.append(JsonValue(permissionToString(perm)));
        }
        payload["permissions"] = perms;
        
        std::string payload_str = payload.toString();
        
        // Base64 encode header and payload
        std::string encoded_header = base64_encode_simple(header_str);
        std::string encoded_payload = base64_encode_simple(payload_str);
        
        // Create signature (简化实现)
        std::string message = encoded_header + "." + encoded_payload;
        std::string signature_data = m_jwt_secret + message;
        std::string signature = base64_encode_simple(simple_sha256(signature_data));
        
        return encoded_header + "." + encoded_payload + "." + signature;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "生成JWT令牌失败: " + std::string(e.what()));
        return "";
    }
}

std::unique_ptr<TokenInfo> AuthManager::parseJWT(const std::string& token) {
    try {
        // 分割JWT令牌
        std::vector<std::string> parts;
        std::stringstream ss(token);
        std::string part;
        
        while (std::getline(ss, part, '.')) {
            parts.push_back(part);
        }
        
        if (parts.size() != 3) {
            return nullptr;
        }
        
        // 验证签名
        std::string message = parts[0] + "." + parts[1];
        std::string signature_data = m_jwt_secret + message;
        std::string expected_signature = base64_encode_simple(simple_sha256(signature_data));
        
        if (parts[2] != expected_signature) {
            return nullptr;
        }
        
        // 解析payload
        std::string payload_str = base64_decode_simple(parts[1]);
        
        JsonValue payload;
        if (!payload.parse(payload_str)) {
            return nullptr;
        }
        
        // 创建TokenInfo
        auto token_info = std::make_unique<TokenInfo>();
        token_info->username = payload["sub"].asString();
        token_info->role = stringToRole(payload["role"].asString());
        token_info->issued_at = static_cast<uint64_t>(payload["iat"].asNumber()) * 1000; // 转换为毫秒
        token_info->expires_at = static_cast<uint64_t>(payload["exp"].asNumber()) * 1000; // 转换为毫秒
        token_info->token_id = payload["jti"].asString();
        
        // 解析权限
        const JsonValue& perms = payload["permissions"];
        if (perms.isArray()) {
            for (size_t i = 0; i < perms.size(); i++) {
                std::string perm_str = perms[i].asString();
                if (perm_str == "read_status") token_info->permissions.push_back(Permission::READ_STATUS);
                else if (perm_str == "read_logs") token_info->permissions.push_back(Permission::READ_LOGS);
                else if (perm_str == "read_config") token_info->permissions.push_back(Permission::READ_CONFIG);
                else if (perm_str == "write_config") token_info->permissions.push_back(Permission::WRITE_CONFIG);
                else if (perm_str == "control_system") token_info->permissions.push_back(Permission::CONTROL_SYSTEM);
                else if (perm_str == "manage_users") token_info->permissions.push_back(Permission::MANAGE_USERS);
                else if (perm_str == "view_audit") token_info->permissions.push_back(Permission::VIEW_AUDIT);
            }
        }
        
        return token_info;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "解析JWT令牌失败: " + std::string(e.what()));
        return nullptr;
    }
}

std::string AuthManager::generateTokenId() {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint64_t> dis;
    
    uint64_t id = dis(gen);
    
    std::ostringstream oss;
    oss << std::hex << id;
    return oss.str();
}

bool AuthManager::loadUsers() {
    try {
        std::ifstream file("/etc/timing-server/users.json");
        if (!file.is_open()) {
            // 尝试从当前目录加载（开发环境）
            file.open("platform/config/users.json");
            if (!file.is_open()) {
                LOG_INFO(core::LogComponent::AUTH_MANAGER, "用户配置文件不存在，将创建默认配置");
                return false;
            }
        }
        
        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();
        
        JsonValue root;
        if (!root.parse(content)) {
            LOG_ERROR(core::LogComponent::AUTH_MANAGER, "解析用户配置文件失败");
            return false;
        }
        
        m_users.clear();
        
        const JsonValue& users = root["users"];
        if (users.isArray()) {
            for (size_t i = 0; i < users.size(); i++) {
                const JsonValue& user_json = users[i];
                
                UserInfo user;
                user.username = user_json["username"].asString();
                user.password_hash = user_json["password_hash"].asString();
                user.salt = user_json["salt"].asString();
                user.role = stringToRole(user_json["role"].asString());
                user.permissions = getRolePermissions(user.role);
                user.created_at = static_cast<uint64_t>(user_json["created_at"].asNumber());
                user.last_login = static_cast<uint64_t>(user_json["last_login"].asNumber());
                user.is_active = user_json["is_active"].asBool();
                
                m_users[user.username] = user;
            }
        }
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "加载用户配置成功，用户数量: " + std::to_string(m_users.size()));
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "加载用户配置异常: " + std::string(e.what()));
        return false;
    }
}

bool AuthManager::saveUsers() {
    try {
        JsonValue root;
        root.setObject();
        
        JsonValue users;
        users.setArray();
        
        for (const auto& pair : m_users) {
            const UserInfo& user = pair.second;
            
            JsonValue user_json;
            user_json.setObject();
            user_json["username"] = JsonValue(user.username);
            user_json["password_hash"] = JsonValue(user.password_hash);
            user_json["salt"] = JsonValue(user.salt);
            user_json["role"] = JsonValue(roleToString(user.role));
            user_json["created_at"] = JsonValue(static_cast<double>(user.created_at));
            user_json["last_login"] = JsonValue(static_cast<double>(user.last_login));
            user_json["is_active"] = JsonValue(user.is_active);
            
            users.append(user_json);
        }
        
        root["users"] = users;
        
        // 尝试保存到系统配置目录
        std::ofstream file("/etc/timing-server/users.json");
        if (!file.is_open()) {
            // 如果系统目录不可写，保存到当前目录
            file.open("platform/config/users.json");
            if (!file.is_open()) {
                LOG_ERROR(core::LogComponent::AUTH_MANAGER, "无法打开用户配置文件进行写入");
                return false;
            }
        }
        
        file << root.toString();
        file.close();
        
        LOG_DEBUG(core::LogComponent::AUTH_MANAGER, "用户配置保存成功");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "保存用户配置异常: " + std::string(e.what()));
        return false;
    }
}

bool AuthManager::createDefaultAdmin() {
    try {
        UserInfo admin;
        admin.username = "admin";
        admin.salt = generateSalt();
        admin.password_hash = hashPassword("admin123", admin.salt); // 默认密码
        admin.role = UserRole::ADMINISTRATOR;
        admin.permissions = getRolePermissions(UserRole::ADMINISTRATOR);
        admin.created_at = core::GetCurrentTimestampNs() / 1000000;
        admin.last_login = 0;
        admin.is_active = true;
        
        m_users["admin"] = admin;
        
        // 保存用户数据
        saveUsers();
        
        LOG_INFO(core::LogComponent::AUTH_MANAGER, 
                "默认管理员用户已创建，用户名: admin, 密码: admin123");
        
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::AUTH_MANAGER, 
                 "创建默认管理员异常: " + std::string(e.what()));
        return false;
    }
}

} // namespace api
} // namespace timing_server