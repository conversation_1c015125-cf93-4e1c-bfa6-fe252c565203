#include <api/websocket_server.h>
#include <core/logger.h>
#include <core/error_handler.h>
#include <api/json_fallback.h>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <poll.h>

using json = nlohmann::json;

namespace timing_server {
namespace api {

WebSocketServer::WebSocketServer(const WebSocketServerConfig& config, 
                               std::shared_ptr<TimingService> timing_service)
    : m_config(config), m_timing_service(timing_service) {
    
    m_stats.start_time = std::chrono::steady_clock::now();
    
    LOG_INFO(core::LogComponent::WEBSOCKET, 
            "创建WebSocket服务器，端口: " + std::to_string(m_config.port) + 
            "，最大连接数: " + std::to_string(m_config.max_connections));
}

WebSocketServer::~WebSocketServer() {
    stop();
}

bool WebSocketServer::start() {
    if (m_running.load()) {
        LOG_WARNING(core::LogComponent::WEBSOCKET, "WebSocket服务器已在运行");
        return true;
    }

    try {
        LOG_INFO(core::LogComponent::WEBSOCKET, "启动WebSocket服务器...");

        // 启动服务器线程
        m_server_thread = std::make_unique<std::thread>(&WebSocketServer::serverLoop, this);
        
        // 启动心跳检测线程
        m_heartbeat_thread = std::make_unique<std::thread>(&WebSocketServer::heartbeatLoop, this);
        
        // 启动消息处理线程
        m_message_processor_thread = std::make_unique<std::thread>(&WebSocketServer::messageProcessorLoop, this);

        // 等待服务器启动
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        if (m_running.load()) {
            LOG_INFO(core::LogComponent::WEBSOCKET, 
                    "WebSocket服务器启动成功，监听端口: " + std::to_string(m_config.port));
            return true;
        } else {
            LOG_ERROR(core::LogComponent::WEBSOCKET, "WebSocket服务器启动失败");
            return false;
        }

    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::WEBSOCKET, 
                 "WebSocket服务器启动异常: " + std::string(e.what()));
        return false;
    }
}

void WebSocketServer::stop() {
    if (!m_running.load()) {
        return;
    }

    LOG_INFO(core::LogComponent::WEBSOCKET, "正在停止WebSocket服务器...");

    m_stop_requested.store(true);
    stopStatusPushing();

    // 通知消息处理线程退出
    {
        std::lock_guard<std::mutex> lock(m_message_queue_mutex);
        m_message_queue_cv.notify_all();
    }

    // 等待线程结束
    if (m_server_thread && m_server_thread->joinable()) {
        m_server_thread->join();
    }
    if (m_heartbeat_thread && m_heartbeat_thread->joinable()) {
        m_heartbeat_thread->join();
    }
    if (m_status_push_thread && m_status_push_thread->joinable()) {
        m_status_push_thread->join();
    }
    if (m_message_processor_thread && m_message_processor_thread->joinable()) {
        m_message_processor_thread->join();
    }

    // 断开所有连接
    {
        std::lock_guard<std::mutex> lock(m_connections_mutex);
        m_connections.clear();
    }

    m_running.store(false);
    LOG_INFO(core::LogComponent::WEBSOCKET, "WebSocket服务器已停止");
}

uint32_t WebSocketServer::broadcastMessage(const WebSocketMessage& message, const std::string& event_type) {
    std::lock_guard<std::mutex> lock(m_connections_mutex);
    
    uint32_t sent_count = 0;
    std::string serialized_message = serializeMessage(message);
    
    for (const auto& [connection_id, connection] : m_connections) {
        if (connection->state == WebSocketConnectionState::AUTHENTICATED) {
            // 检查订阅过滤
            if (!event_type.empty() && !isConnectionSubscribed(connection_id, event_type)) {
                continue;
            }
            
            // 添加到消息队列
            {
                std::lock_guard<std::mutex> queue_lock(m_message_queue_mutex);
                if (m_message_queue.size() < m_config.message_queue_size) {
                    m_message_queue.emplace(connection_id, message);
                    sent_count++;
                }
            }
        }
    }
    
    m_message_queue_cv.notify_all();
    
    LOG_DEBUG(core::LogComponent::WEBSOCKET, 
             "广播消息给 " + std::to_string(sent_count) + " 个客户端");
    
    return sent_count;
}

bool WebSocketServer::sendMessage(uint64_t connection_id, const WebSocketMessage& message) {
    std::lock_guard<std::mutex> lock(m_connections_mutex);
    
    auto it = m_connections.find(connection_id);
    if (it == m_connections.end() || it->second->state != WebSocketConnectionState::AUTHENTICATED) {
        return false;
    }
    
    // 添加到消息队列
    {
        std::lock_guard<std::mutex> queue_lock(m_message_queue_mutex);
        if (m_message_queue.size() < m_config.message_queue_size) {
            m_message_queue.emplace(connection_id, message);
            m_message_queue_cv.notify_one();
            return true;
        }
    }
    
    return false;
}

WebSocketServerStats WebSocketServer::getStats() const {
    std::lock_guard<std::mutex> lock(m_stats_mutex);
    return m_stats;
}

std::vector<ClientConnection> WebSocketServer::getActiveConnections() const {
    std::lock_guard<std::mutex> lock(m_connections_mutex);
    
    std::vector<ClientConnection> connections;
    connections.reserve(m_connections.size());
    
    for (const auto& [id, connection] : m_connections) {
        connections.push_back(*connection);
    }
    
    return connections;
}

bool WebSocketServer::disconnectClient(uint64_t connection_id, const std::string& reason) {
    std::lock_guard<std::mutex> lock(m_connections_mutex);
    
    auto it = m_connections.find(connection_id);
    if (it != m_connections.end()) {
        handleClientDisconnection(connection_id, reason);
        m_connections.erase(it);
        return true;
    }
    
    return false;
}

void WebSocketServer::setEventListener(std::shared_ptr<IWebSocketEventListener> listener) {
    m_event_listener = listener;
}

void WebSocketServer::startStatusPushing() {
    if (m_status_pushing.load()) {
        return;
    }
    
    m_status_pushing.store(true);
    m_status_push_thread = std::make_unique<std::thread>(&WebSocketServer::statusPushLoop, this);
    
    LOG_INFO(core::LogComponent::WEBSOCKET, "启动实时状态推送");
}

void WebSocketServer::stopStatusPushing() {
    if (!m_status_pushing.load()) {
        return;
    }
    
    m_status_pushing.store(false);
    
    if (m_status_push_thread && m_status_push_thread->joinable()) {
        m_status_push_thread->join();
    }
    
    LOG_INFO(core::LogComponent::WEBSOCKET, "停止实时状态推送");
}

void WebSocketServer::serverLoop() {
    // 简化的服务器实现 - 在实际项目中需要完整的WebSocket协议实现
    LOG_INFO(core::LogComponent::WEBSOCKET, "WebSocket服务器主循环启动");
    
    m_running.store(true);
    
    // 模拟服务器运行
    while (!m_stop_requested.load()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 在实际实现中，这里会处理TCP连接、WebSocket握手、消息接收等
        // 由于完整的WebSocket实现较为复杂，这里提供简化版本
    }
    
    m_running.store(false);
    LOG_INFO(core::LogComponent::WEBSOCKET, "WebSocket服务器主循环结束");
}

void WebSocketServer::heartbeatLoop() {
    while (!m_stop_requested.load()) {
        std::this_thread::sleep_for(std::chrono::milliseconds(m_config.heartbeat_interval_ms));
        
        if (m_stop_requested.load()) break;
        
        // 检查心跳超时
        cleanupTimeoutConnections();
        
        // 发送心跳消息
        auto heartbeat_message = createHeartbeatMessage();
        broadcastMessage(heartbeat_message, "heartbeat");
    }
}

void WebSocketServer::statusPushLoop() {
    while (m_status_pushing.load() && !m_stop_requested.load()) {
        try {
            if (m_timing_service) {
                auto status_message = createStatusUpdateMessage();
                broadcastMessage(status_message, "status_update");
            }
            
            m_last_status_push = std::chrono::steady_clock::now();
            
            // 每秒推送一次状态更新
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            
        } catch (const std::exception& e) {
            LOG_ERROR(core::LogComponent::WEBSOCKET, 
                     "状态推送异常: " + std::string(e.what()));
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
        }
    }
}

void WebSocketServer::messageProcessorLoop() {
    while (!m_stop_requested.load()) {
        std::unique_lock<std::mutex> lock(m_message_queue_mutex);
        
        // 等待消息或停止信号
        m_message_queue_cv.wait(lock, [this] {
            return !m_message_queue.empty() || m_stop_requested.load();
        });
        
        if (m_stop_requested.load()) break;
        
        // 处理队列中的消息
        while (!m_message_queue.empty()) {
            auto [connection_id, message] = m_message_queue.front();
            m_message_queue.pop();
            
            lock.unlock();
            
            // 发送消息（这里需要实际的套接字发送实现）
            std::string serialized = serializeMessage(message);
            
            // 更新统计信息
            updateStats(serialized.length(), 0);
            
            lock.lock();
        }
    }
}

void WebSocketServer::handleNewConnection(uint64_t connection_id, const std::string& remote_address) {
    auto connection = std::make_shared<ClientConnection>(connection_id, remote_address);
    
    {
        std::lock_guard<std::mutex> lock(m_connections_mutex);
        m_connections[connection_id] = connection;
    }
    
    {
        std::lock_guard<std::mutex> lock(m_stats_mutex);
        m_stats.total_connections++;
        m_stats.active_connections++;
    }
    
    if (m_event_listener) {
        m_event_listener->onClientConnected(connection_id, remote_address);
    }
    
    LOG_INFO(core::LogComponent::WEBSOCKET, 
            "新客户端连接，ID: " + std::to_string(connection_id) + 
            "，地址: " + remote_address);
}

void WebSocketServer::handleClientDisconnection(uint64_t connection_id, const std::string& reason) {
    {
        std::lock_guard<std::mutex> lock(m_connections_mutex);
        auto it = m_connections.find(connection_id);
        if (it != m_connections.end()) {
            if (it->second->state == WebSocketConnectionState::AUTHENTICATED) {
                std::lock_guard<std::mutex> stats_lock(m_stats_mutex);
                m_stats.authenticated_connections--;
            }
            m_connections.erase(it);
        }
    }
    
    {
        std::lock_guard<std::mutex> lock(m_stats_mutex);
        m_stats.active_connections--;
    }
    
    if (m_event_listener) {
        m_event_listener->onClientDisconnected(connection_id, reason);
    }
    
    LOG_INFO(core::LogComponent::WEBSOCKET, 
            "客户端断开连接，ID: " + std::to_string(connection_id) + 
            "，原因: " + reason);
}

void WebSocketServer::handleReceivedMessage(uint64_t connection_id, const std::string& raw_message) {
    try {
        auto message = deserializeMessage(raw_message);
        
        {
            std::lock_guard<std::mutex> lock(m_connections_mutex);
            auto it = m_connections.find(connection_id);
            if (it != m_connections.end()) {
                it->second->messages_received++;
                it->second->bytes_received += raw_message.length();
                it->second->last_heartbeat = std::chrono::steady_clock::now();
            }
        }
        
        // 根据消息类型处理
        switch (message.type) {
            case WebSocketMessageType::AUTH:
                handleAuthMessage(connection_id, message.payload);
                break;
            case WebSocketMessageType::SUBSCRIPTION:
                handleSubscriptionMessage(connection_id, message.payload);
                break;
            case WebSocketMessageType::HEARTBEAT:
                handleHeartbeatMessage(connection_id);
                break;
            default:
                LOG_WARNING(core::LogComponent::WEBSOCKET, 
                           "收到未知类型消息，连接ID: " + std::to_string(connection_id));
                break;
        }
        
        if (m_event_listener) {
            m_event_listener->onMessageReceived(connection_id, message);
        }
        
        updateStats(0, raw_message.length());
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::WEBSOCKET, 
                 "处理消息异常，连接ID: " + std::to_string(connection_id) + 
                 "，错误: " + e.what());
    }
}

void WebSocketServer::handleAuthMessage(uint64_t connection_id, const std::string& auth_payload) {
    try {
        json auth_data = json::parse(auth_payload);
        std::string token = auth_data.value("token", std::string(""));
        
        std::string user_id, user_role;
        bool auth_success = validateJwtToken(token, user_id, user_role);
        
        {
            std::lock_guard<std::mutex> lock(m_connections_mutex);
            auto it = m_connections.find(connection_id);
            if (it != m_connections.end()) {
                if (auth_success) {
                    it->second->state = WebSocketConnectionState::AUTHENTICATED;
                    it->second->user_id = user_id;
                    it->second->user_role = user_role;
                    
                    std::lock_guard<std::mutex> stats_lock(m_stats_mutex);
                    m_stats.authenticated_connections++;
                } else {
                    std::lock_guard<std::mutex> stats_lock(m_stats_mutex);
                    m_stats.authentication_failures++;
                }
            }
        }
        
        if (m_event_listener) {
            m_event_listener->onClientAuthenticated(connection_id, user_id, auth_success);
        }
        
        // 发送认证结果
        json response;
        response["type"] = "auth_response";
        response["success"] = auth_success;
        response["message"] = auth_success ? "认证成功" : "认证失败";
        
        WebSocketMessage response_msg(WebSocketMessageType::AUTH, response.dump());
        sendMessage(connection_id, response_msg);
        
        LOG_INFO(core::LogComponent::WEBSOCKET, 
                "客户端认证" + std::string(auth_success ? "成功" : "失败") + 
                "，连接ID: " + std::to_string(connection_id) + 
                "，用户: " + user_id);
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::WEBSOCKET, 
                 "处理认证消息异常: " + std::string(e.what()));
    }
}

void WebSocketServer::handleSubscriptionMessage(uint64_t connection_id, const std::string& subscription_payload) {
    try {
        json sub_data = json::parse(subscription_payload);
        std::string action = sub_data.value("action", std::string(""));
        std::vector<std::string> events = sub_data.value("events", std::vector<std::string>());
        
        {
            std::lock_guard<std::mutex> lock(m_connections_mutex);
            auto it = m_connections.find(connection_id);
            if (it != m_connections.end() && it->second->state == WebSocketConnectionState::AUTHENTICATED) {
                if (action == "subscribe") {
                    for (const auto& event : events) {
                        it->second->subscriptions.insert(event);
                    }
                } else if (action == "unsubscribe") {
                    for (const auto& event : events) {
                        it->second->subscriptions.erase(event);
                    }
                }
            }
        }
        
        LOG_DEBUG(core::LogComponent::WEBSOCKET, 
                 "处理订阅消息，连接ID: " + std::to_string(connection_id) + 
                 "，操作: " + action);
        
    } catch (const std::exception& e) {
        LOG_ERROR(core::LogComponent::WEBSOCKET, 
                 "处理订阅消息异常: " + std::string(e.what()));
    }
}

void WebSocketServer::handleHeartbeatMessage(uint64_t connection_id) {
    {
        std::lock_guard<std::mutex> lock(m_connections_mutex);
        auto it = m_connections.find(connection_id);
        if (it != m_connections.end()) {
            it->second->last_heartbeat = std::chrono::steady_clock::now();
        }
    }
    
    // 发送心跳响应
    auto heartbeat_response = createHeartbeatMessage();
    sendMessage(connection_id, heartbeat_response);
}

bool WebSocketServer::validateJwtToken(const std::string& token, std::string& user_id, std::string& user_role) {
    // 简化的JWT验证实现
    // 在实际项目中，这里应该使用专业的JWT库进行验证
    
    if (token.empty()) {
        return false;
    }
    
    // 模拟JWT解析
    if (token == "admin_token") {
        user_id = "admin";
        user_role = "administrator";
        return true;
    } else if (token == "operator_token") {
        user_id = "operator";
        user_role = "operator";
        return true;
    } else if (token == "viewer_token") {
        user_id = "viewer";
        user_role = "viewer";
        return true;
    }
    
    return false;
}

WebSocketMessage WebSocketServer::createStatusUpdateMessage() {
    json status_data;
    
    if (m_timing_service) {
        try {
            auto system_status = m_timing_service->getSystemStatus();
            
            status_data["type"] = "status_update";
            status_data["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            
            status_data["data"]["state"] = core::ClockStateToString(system_status.current_state);
            status_data["data"]["active_source"] = core::TimeSourceToString(system_status.active_source);
            status_data["data"]["uptime_seconds"] = system_status.uptime_seconds;
            status_data["data"]["health"] = core::SystemHealthToString(system_status.health);
            
            // 添加时间源信息
            json sources = json::array();
            for (const auto& source : system_status.sources) {
                json source_info;
                source_info["type"] = core::TimeSourceToString(source.type);
                source_info["status"] = core::TimeSourceStatusToString(source.status);
                source_info["quality"] = source.quality.confidence;
                source_info["accuracy_ns"] = source.quality.accuracy_ns;
                sources.push_back(source_info);
            }
            status_data["data"]["sources"] = sources;
            
        } catch (const std::exception& e) {
            LOG_ERROR(core::LogComponent::WEBSOCKET, 
                     "创建状态更新消息异常: " + std::string(e.what()));
            
            status_data["type"] = "error";
            status_data["message"] = "获取系统状态失败";
        }
    } else {
        status_data["type"] = "error";
        status_data["message"] = "授时服务不可用";
    }
    
    return WebSocketMessage(WebSocketMessageType::STATUS_UPDATE, status_data.dump());
}

WebSocketMessage WebSocketServer::createAlarmMessage(const std::string& severity, 
                                                    const std::string& component, 
                                                    const std::string& message) {
    json alarm_data;
    alarm_data["type"] = "alarm";
    alarm_data["severity"] = severity;
    alarm_data["component"] = component;
    alarm_data["message"] = message;
    alarm_data["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    return WebSocketMessage(WebSocketMessageType::ALARM, alarm_data.dump());
}

WebSocketMessage WebSocketServer::createHeartbeatMessage() {
    json heartbeat_data;
    heartbeat_data["type"] = "heartbeat";
    heartbeat_data["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    return WebSocketMessage(WebSocketMessageType::HEARTBEAT, heartbeat_data.dump());
}

std::string WebSocketServer::serializeMessage(const WebSocketMessage& message) {
    json msg_json;
    
    // 消息类型转换
    switch (message.type) {
        case WebSocketMessageType::AUTH:
            msg_json["type"] = "auth";
            break;
        case WebSocketMessageType::STATUS_UPDATE:
            msg_json["type"] = "status_update";
            break;
        case WebSocketMessageType::ALARM:
            msg_json["type"] = "alarm";
            break;
        case WebSocketMessageType::METRICS:
            msg_json["type"] = "metrics";
            break;
        case WebSocketMessageType::LOG_EVENT:
            msg_json["type"] = "log_event";
            break;
        case WebSocketMessageType::HEARTBEAT:
            msg_json["type"] = "heartbeat";
            break;
        case WebSocketMessageType::SUBSCRIPTION:
            msg_json["type"] = "subscription";
            break;
        case WebSocketMessageType::ERROR:
            msg_json["type"] = "error";
            break;
    }
    
    // 如果payload已经是JSON，直接合并
    try {
        json payload_json = json::parse(message.payload);
        for (auto& [key, value] : payload_json.items()) {
            msg_json[key] = value;
        }
    } catch (const std::exception&) {
        // 如果不是JSON，作为普通字符串处理
        msg_json["payload"] = message.payload;
    }
    
    msg_json["server_timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
        message.timestamp.time_since_epoch()).count();
    
    return msg_json.dump();
}

WebSocketMessage WebSocketServer::deserializeMessage(const std::string& json_str) {
    json msg_json = json::parse(json_str);
    
    WebSocketMessageType type = WebSocketMessageType::ERROR;
    std::string type_str = msg_json.value("type", std::string(""));
    
    if (type_str == "auth") {
        type = WebSocketMessageType::AUTH;
    } else if (type_str == "status_update") {
        type = WebSocketMessageType::STATUS_UPDATE;
    } else if (type_str == "alarm") {
        type = WebSocketMessageType::ALARM;
    } else if (type_str == "metrics") {
        type = WebSocketMessageType::METRICS;
    } else if (type_str == "log_event") {
        type = WebSocketMessageType::LOG_EVENT;
    } else if (type_str == "heartbeat") {
        type = WebSocketMessageType::HEARTBEAT;
    } else if (type_str == "subscription") {
        type = WebSocketMessageType::SUBSCRIPTION;
    }
    
    // 提取payload
    std::string payload;
    if (msg_json.contains("payload")) {
        payload = msg_json["payload"].get_string();
    } else {
        // 移除type字段，其余作为payload
        msg_json.erase("type");
        payload = msg_json.dump();
    }
    
    return WebSocketMessage(type, payload);
}

void WebSocketServer::updateStats(uint64_t bytes_sent, uint64_t bytes_received, double latency_ms) {
    std::lock_guard<std::mutex> lock(m_stats_mutex);
    
    if (bytes_sent > 0) {
        m_stats.messages_sent++;
        m_stats.bytes_sent += bytes_sent;
    }
    
    if (bytes_received > 0) {
        m_stats.messages_received++;
        m_stats.bytes_received += bytes_received;
    }
    
    if (latency_ms > 0) {
        // 简化的平均延迟计算
        m_stats.average_latency_ms = (m_stats.average_latency_ms + latency_ms) / 2.0;
    }
}

void WebSocketServer::cleanupTimeoutConnections() {
    auto now = std::chrono::steady_clock::now();
    std::vector<uint64_t> timeout_connections;
    
    {
        std::lock_guard<std::mutex> lock(m_connections_mutex);
        
        for (const auto& [connection_id, connection] : m_connections) {
            auto time_since_heartbeat = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - connection->last_heartbeat).count();
            
            if (time_since_heartbeat > m_config.heartbeat_timeout_ms) {
                timeout_connections.push_back(connection_id);
            }
        }
    }
    
    // 断开超时连接
    for (uint64_t connection_id : timeout_connections) {
        disconnectClient(connection_id, "heartbeat_timeout");
        
        std::lock_guard<std::mutex> lock(m_stats_mutex);
        m_stats.heartbeat_timeouts++;
    }
    
    if (!timeout_connections.empty()) {
        LOG_INFO(core::LogComponent::WEBSOCKET, 
                "清理 " + std::to_string(timeout_connections.size()) + " 个超时连接");
    }
}

bool WebSocketServer::isConnectionAuthenticated(uint64_t connection_id) const {
    std::lock_guard<std::mutex> lock(m_connections_mutex);
    
    auto it = m_connections.find(connection_id);
    return it != m_connections.end() && it->second->state == WebSocketConnectionState::AUTHENTICATED;
}

bool WebSocketServer::isConnectionSubscribed(uint64_t connection_id, const std::string& event_type) const {
    std::lock_guard<std::mutex> lock(m_connections_mutex);
    
    auto it = m_connections.find(connection_id);
    if (it == m_connections.end()) {
        return false;
    }
    
    return it->second->subscriptions.find(event_type) != it->second->subscriptions.end();
}

} // namespace api
} // namespace timing_server