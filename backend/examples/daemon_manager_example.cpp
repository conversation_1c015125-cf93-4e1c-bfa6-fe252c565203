#include "core/daemon_manager.h"
#include "core/logger.h"
#include "core/error_handler.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace timing_server::core;

/**
 * @brief DaemonManager优化示例
 * 
 * 展示了以下优化特性：
 * 1. 专业日志系统集成
 * 2. 进程组管理
 * 3. SIGCHLD信号处理
 * 4. 增强的错误反馈机制
 * 5. 错误处理和自动恢复
 */

void StatusChangeCallback(DaemonType type, DaemonStatus old_status, DaemonStatus new_status) {
    LOG_INFO(LogComponent::DAEMON_MANAGER, 
             "守护进程状态变化: " + DaemonTypeToString(type) + 
             " " + DaemonStatusToString(old_status) + " -> " + DaemonStatusToString(new_status));
}

void HealthStatusCallback(SystemHealth health) {
    LOG_INFO(LogComponent::SYSTEM, 
             "系统健康状态变化: " + SystemHealthToString(health));
}

int main() {
    std::cout << "=== DaemonManager优化示例 ===" << std::endl;
    
    // 1. 初始化日志系统
    auto& logger = Logger::GetInstance();
    logger.Initialize();
    logger.SetLogLevel(LogLevel::DEBUG);
    
    // 添加控制台输出（带颜色）
    logger.AddOutput(std::make_unique<ConsoleLogOutput>(true));
    
    // 添加文件输出（带轮转）
    LogRotationConfig rotation_config;
    rotation_config.max_file_size_mb = 10;
    rotation_config.max_files = 5;
    rotation_config.enable_compression = true;
    logger.AddOutput(std::make_unique<FileLogOutput>("/tmp/daemon_manager_example.log", rotation_config));
    
    LOG_INFO(LogComponent::SYSTEM, "日志系统初始化完成");
    
    // 2. 初始化错误处理系统
    auto& error_handler = ErrorHandler::GetInstance();
    error_handler.Initialize();
    error_handler.SetAutoRecoveryEnabled(true);
    
    // 注册健康状态变化回调
    error_handler.GetHealthMonitor().RegisterHealthCheckCallback(HealthStatusCallback);
    
    LOG_INFO(LogComponent::SYSTEM, "错误处理系统初始化完成");
    
    // 3. 创建和初始化DaemonManager
    DaemonManager daemon_manager;
    
    if (!daemon_manager.Initialize()) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, "DaemonManager初始化失败");
        return 1;
    }
    
    // 设置状态变化回调
    daemon_manager.SetStatusChangeCallback(StatusChangeCallback);
    
    // 4. 启动DaemonManager
    if (!daemon_manager.Start()) {
        LOG_ERROR(LogComponent::DAEMON_MANAGER, "DaemonManager启动失败");
        return 1;
    }
    
    // 5. 演示守护进程管理
    LOG_INFO(LogComponent::SYSTEM, "开始演示守护进程管理功能");
    
    // 尝试启动PTP4L守护进程
    LOG_INFO(LogComponent::SYSTEM, "启动PTP4L守护进程...");
    if (daemon_manager.StartDaemon(DaemonType::PTP4L)) {
        LOG_INFO(LogComponent::SYSTEM, "PTP4L启动成功");
        
        // 等待一段时间
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 获取守护进程信息
        auto daemon_info = daemon_manager.GetDaemonInfo(DaemonType::PTP4L);
        LOG_INFO(LogComponent::SYSTEM, 
                 "PTP4L状态: " + DaemonStatusToString(daemon_info.status) + 
                 ", PID: " + std::to_string(daemon_info.process_id));
        
        // 停止守护进程
        LOG_INFO(LogComponent::SYSTEM, "停止PTP4L守护进程...");
        if (daemon_manager.StopDaemon(DaemonType::PTP4L)) {
            LOG_INFO(LogComponent::SYSTEM, "PTP4L停止成功");
        } else {
            LOG_ERROR(LogComponent::SYSTEM, "PTP4L停止失败");
        }
    } else {
        LOG_WARNING(LogComponent::SYSTEM, "PTP4L启动失败（可能是可执行文件不存在）");
    }
    
    // 6. 演示配置更新
    LOG_INFO(LogComponent::SYSTEM, "演示配置更新功能");
    
    PtpConfig ptp_config;
    ptp_config.clock_class = 6;
    ptp_config.clock_accuracy = 0x21;
    ptp_config.priority1 = 1;
    ptp_config.network_interface = "eth0";
    ptp_config.network_transport = "UDPv4";
    ptp_config.delay_mechanism = "E2E";
    
    if (daemon_manager.UpdatePtpConfig(ptp_config)) {
        LOG_INFO(LogComponent::SYSTEM, "PTP配置更新成功");
    } else {
        LOG_ERROR(LogComponent::SYSTEM, "PTP配置更新失败");
    }
    
    // 7. 演示动态配置调整
    LOG_INFO(LogComponent::SYSTEM, "演示动态配置调整");
    
    if (daemon_manager.UpdateConfigForClockState(ClockState::LOCKED, TimeSource::GNSS)) {
        LOG_INFO(LogComponent::SYSTEM, "根据时钟状态更新配置成功");
    } else {
        LOG_ERROR(LogComponent::SYSTEM, "根据时钟状态更新配置失败");
    }
    
    // 8. 演示错误处理
    LOG_INFO(LogComponent::SYSTEM, "演示错误处理功能");
    
    // 模拟一个守护进程错误
    uint64_t error_id = error_handler.ReportError(
        ErrorType::DAEMON_CRASH,
        ErrorSeverity::HIGH,
        LogComponent::PTP4L,
        "PTP4L守护进程意外退出",
        "进程收到SIGTERM信号"
    );
    
    LOG_INFO(LogComponent::SYSTEM, "报告错误，ID: " + std::to_string(error_id));
    
    // 等待自动恢复处理
    std::this_thread::sleep_for(std::chrono::seconds(1));
    
    // 获取错误信息
    auto error_info = error_handler.GetError(error_id);
    if (error_info) {
        LOG_INFO(LogComponent::SYSTEM, 
                 "错误状态: " + ErrorStatusToString(error_info->status));
    }
    
    // 9. 演示系统健康监控
    LOG_INFO(LogComponent::SYSTEM, "演示系统健康监控");
    
    auto& health_monitor = error_handler.GetHealthMonitor();
    SystemHealth current_health = health_monitor.GetSystemHealth();
    LOG_INFO(LogComponent::SYSTEM, 
             "当前系统健康状态: " + SystemHealthToString(current_health));
    
    auto health_report = health_monitor.GetHealthReport();
    for (const auto& item : health_report) {
        LOG_DEBUG(LogComponent::SYSTEM, 
                 "健康指标 " + item.first + ": " + item.second);
    }
    
    // 10. 演示日志搜索功能
    LOG_INFO(LogComponent::SYSTEM, "演示日志搜索功能");
    
    LogFilter filter;
    filter.min_level = LogLevel::INFO;
    filter.components = {LogComponent::DAEMON_MANAGER, LogComponent::SYSTEM};
    filter.max_results = 10;
    
    auto search_results = logger.SearchLogs(filter);
    LOG_INFO(LogComponent::SYSTEM, 
             "搜索到 " + std::to_string(search_results.size()) + " 条日志记录");
    
    // 11. 获取错误统计
    auto error_stats = error_handler.GetErrorStatistics();
    LOG_INFO(LogComponent::SYSTEM, "错误统计:");
    for (const auto& stat : error_stats) {
        LOG_INFO(LogComponent::SYSTEM, 
                 "  " + stat.first + ": " + std::to_string(stat.second));
    }
    
    // 12. 等待一段时间观察系统运行
    LOG_INFO(LogComponent::SYSTEM, "系统运行中，等待5秒...");
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // 13. 清理和关闭
    LOG_INFO(LogComponent::SYSTEM, "开始系统清理...");
    
    daemon_manager.Stop();
    error_handler.Shutdown();
    logger.Shutdown();
    
    std::cout << "=== 示例程序结束 ===" << std::endl;
    return 0;
}

/**
 * 编译命令示例：
 * g++ -std=c++17 -I../include -L../lib \
 *     daemon_manager_example.cpp \
 *     -ltiming_server_core -lpthread -o daemon_manager_example
 * 
 * 运行前设置测试模式：
 * export TIMING_SERVER_TEST_MODE=1
 * ./daemon_manager_example
 */