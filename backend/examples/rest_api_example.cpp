/**
 * @file rest_api_example.cpp
 * @brief REST API服务器示例程序
 * 
 * 演示如何使用REST API服务器提供授时系统的HTTP接口
 */

#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <csignal>
#include <atomic>

// 包含API相关头文件
#include <api/timing_service.h>
#include <api/rest_server.h>
#include <core/logger.h>
#include <core/types.h>

using namespace timing_server;

// 全局变量用于信号处理
std::atomic<bool> g_running{true};
std::unique_ptr<api::RestServer> g_server;

/**
 * @brief 信号处理函数
 * 处理SIGINT和SIGTERM信号，优雅关闭服务器
 */
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在关闭服务器..." << std::endl;
    g_running.store(false);
    
    if (g_server) {
        g_server->stop();
    }
}

/**
 * @brief 简化的授时服务实现
 * 用于演示API功能
 */
class MockTimingService : public api::TimingService {
public:
    core::SystemStatus getSystemStatus() override {
        core::SystemStatus status;
        status.current_state = core::ClockState::LOCKED;
        status.active_source = core::TimeSource::GNSS;
        status.health = core::SystemHealth::HEALTHY;
        status.uptime_seconds = 3600; // 1小时
        status.cpu_usage_percent = 2.5;
        status.memory_usage_mb = 48;
        status.version = "1.0.0-demo";
        status.platform = PLATFORM_NAME;
        
        // 添加GNSS时间源
        core::TimeSourceInfo gnss_source;
        gnss_source.type = core::TimeSource::GNSS;
        gnss_source.status = core::TimeSourceStatus::ACTIVE;
        gnss_source.priority = 1;
        gnss_source.last_update_ns = core::GetCurrentTimestampNs();
        gnss_source.quality.accuracy_ns = 45.0;
        gnss_source.quality.stability_ppm = 1.2e-12;
        gnss_source.quality.confidence = 98;
        gnss_source.quality.is_traceable = true;
        gnss_source.quality.reference = "GPS";
        gnss_source.properties["satellites"] = "14";
        gnss_source.properties["signal_strength"] = "-140";
        
        status.sources.push_back(gnss_source);
        return status;
    }
    
    std::string getPtpConfig() override {
        return R"({
            "domain": 0,
            "priority1": 128,
            "priority2": 128,
            "clock_class": 6,
            "clock_accuracy": 33,
            "interface": "eth0"
        })";
    }
    
    bool updatePtpConfig(const std::string& config_json) override {
        std::cout << "更新PTP配置: " << config_json << std::endl;
        return true;
    }
    
    std::string getNtpConfig() override {
        return R"({
            "stratum": 1,
            "reference_id": "GPS",
            "server_port": 123,
            "max_clients": 1000
        })";
    }
    
    bool updateNtpConfig(const std::string& config_json) override {
        std::cout << "更新NTP配置: " << config_json << std::endl;
        return true;
    }
    
    LogQueryResult queryLogs(const LogQueryParams& params) override {
        LogQueryResult result;
        
        // 创建示例日志条目
        LogEntry entry;
        entry.timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
        entry.level = "INFO";
        entry.component = "GNSS";
        entry.message = "GPS信号锁定成功";
        entry.context["satellites"] = "14";
        entry.context["snr"] = "-140";
        
        result.logs.push_back(entry);
        result.total_entries = 1;
        result.current_page = params.page;
        result.total_pages = 1;
        
        return result;
    }
    
    HealthStatus getHealthStatus() override {
        HealthStatus status;
        status.status = "HEALTHY";
        status.timestamp = core::TimestampToIsoString(core::GetCurrentTimestampNs());
        status.uptime_seconds = 3600.0;
        status.components["timing_engine"] = "HEALTHY";
        status.components["gnss_receiver"] = "HEALTHY";
        status.components["ptp4l"] = "HEALTHY";
        status.components["chrony"] = "HEALTHY";
        return status;
    }
    
    bool restartSystem() override {
        std::cout << "系统重启请求已接收" << std::endl;
        return true;
    }
    
    PerformanceMetrics getMetrics(const std::string& from, const std::string& to) override {
        PerformanceMetrics metrics;
        metrics.current_accuracy_ns = 42.5;
        metrics.average_accuracy_ns = 45.2;
        metrics.max_accuracy_ns = 48.7;
        metrics.allan_deviation_1s = 1.1e-12;
        metrics.allan_deviation_10s = 8.2e-13;
        metrics.allan_deviation_100s = 6.5e-13;
        metrics.state_transitions = 3;
        metrics.error_count = 0;
        metrics.packets_processed = 125000;
        metrics.average_response_time_ms = 2.8;
        metrics.start_time = from;
        metrics.end_time = to;
        return metrics;
    }
    
    std::string validateConfig(const std::string& config_type, const std::string& config_json) override {
        if (config_type == "ptp" || config_type == "ntp") {
            return ""; // 验证通过
        }
        return "不支持的配置类型: " + config_type;
    }
    
    std::string getConfigSchema(const std::string& config_type) override {
        if (config_type == "ptp") {
            return R"({"type": "object", "properties": {"domain": {"type": "integer"}}})";
        }
        return "";
    }
};

/**
 * @brief 打印使用说明
 */
void printUsage(const char* program_name) {
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -p, --port PORT    HTTP服务器端口 (默认: 8080)" << std::endl;
    std::cout << "  -h, --help         显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << program_name << " -p 8080" << std::endl;
    std::cout << std::endl;
    std::cout << "API端点:" << std::endl;
    std::cout << "  GET  /api/v1/status        - 获取系统状态" << std::endl;
    std::cout << "  GET  /api/v1/config/ptp    - 获取PTP配置" << std::endl;
    std::cout << "  PUT  /api/v1/config/ptp    - 更新PTP配置" << std::endl;
    std::cout << "  GET  /api/v1/config/ntp    - 获取NTP配置" << std::endl;
    std::cout << "  PUT  /api/v1/config/ntp    - 更新NTP配置" << std::endl;
    std::cout << "  GET  /api/v1/logs          - 查询系统日志" << std::endl;
    std::cout << "  GET  /api/v1/health        - 系统健康检查" << std::endl;
    std::cout << "  GET  /api/v1/metrics       - 获取性能指标" << std::endl;
    std::cout << "  POST /api/v1/system/restart - 重启系统服务" << std::endl;
}

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    uint16_t port = 8080;
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-h" || arg == "--help") {
            printUsage(argv[0]);
            return 0;
        } else if (arg == "-p" || arg == "--port") {
            if (i + 1 < argc) {
                port = static_cast<uint16_t>(std::stoi(argv[++i]));
            } else {
                std::cerr << "错误: -p/--port 需要端口号参数" << std::endl;
                return 1;
            }
        } else {
            std::cerr << "错误: 未知参数 " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }
    
    // 设置信号处理
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);
    
    std::cout << "=== 授时系统REST API服务器示例 ===" << std::endl;
    std::cout << "平台: " << PLATFORM_NAME << std::endl;
    std::cout << "端口: " << port << std::endl;
    
#ifdef USE_OATPP_PLACEHOLDER
    std::cout << "注意: 使用占位实现（oatpp未安装）" << std::endl;
#else
    std::cout << "使用完整oatpp实现" << std::endl;
#endif
    
    try {
        // 创建模拟授时服务
        auto timing_service = std::make_shared<MockTimingService>();
        
        // 创建REST API服务器
        g_server = std::make_unique<api::RestServer>(port, timing_service);
        
        // 启动服务器
        if (!g_server->start()) {
            std::cerr << "错误: 无法启动REST API服务器" << std::endl;
            return 1;
        }
        
        std::cout << "REST API服务器已启动，监听端口: " << port << std::endl;
        std::cout << "按 Ctrl+C 停止服务器" << std::endl;
        
#ifndef USE_OATPP_PLACEHOLDER
        std::cout << std::endl;
        std::cout << "测试命令:" << std::endl;
        std::cout << "  curl http://localhost:" << port << "/api/v1/status" << std::endl;
        std::cout << "  curl http://localhost:" << port << "/api/v1/health" << std::endl;
        std::cout << "  curl http://localhost:" << port << "/api/v1/config/ptp" << std::endl;
#endif
        
        // 主循环
        while (g_running.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
#ifndef USE_OATPP_PLACEHOLDER
            // 定期打印服务器统计信息
            static auto last_stats_time = std::chrono::steady_clock::now();
            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(now - last_stats_time).count() >= 30) {
                auto stats = g_server->getStats();
                std::cout << "服务器统计 - 总请求: " << stats.total_requests 
                         << ", 成功: " << stats.successful_requests 
                         << ", 失败: " << stats.failed_requests 
                         << ", 活跃连接: " << stats.active_connections << std::endl;
                last_stats_time = now;
            }
#endif
        }
        
        std::cout << "正在关闭服务器..." << std::endl;
        g_server->stop();
        
    } catch (const std::exception& e) {
        std::cerr << "异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "REST API服务器示例程序已退出" << std::endl;
    return 0;
}