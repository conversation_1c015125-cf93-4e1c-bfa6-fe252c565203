#include <iostream>
#include <memory>
#include <api/auth_manager.h>
#include <core/logger.h>

using namespace timing_server::api;
using namespace timing_server::core;

/**
 * @brief 认证系统示例程序
 * 演示JWT认证、用户管理和权限控制的基本功能
 */
int main() {
    std::cout << "=== 授时服务器认证系统示例 ===" << std::endl;
    
    // 初始化日志系统
    auto& logger = Logger::GetInstance();
    logger.Initialize();
    logger.SetLogLevel(LogLevel::INFO);
    
    // 添加控制台输出
    logger.AddOutput(std::make_unique<ConsoleLogOutput>(true));
    
    try {
        // 创建认证管理器
        auto auth_manager = std::make_shared<AuthManager>("demo_secret_key_2024", 3600, 86400);
        
        std::cout << "\n1. 初始化认证管理器..." << std::endl;
        if (!auth_manager->initialize()) {
            std::cerr << "认证管理器初始化失败" << std::endl;
            return 1;
        }
        
        // 演示用户认证
        std::cout << "\n2. 用户认证演示..." << std::endl;
        std::cout << "尝试使用默认管理员账户登录 (admin/admin123)" << std::endl;
        
        auto tokens = auth_manager->authenticate("admin", "admin123", "127.0.0.1", "demo-client/1.0");
        
        if (tokens.first.empty()) {
            std::cerr << "认证失败" << std::endl;
            return 1;
        }
        
        std::cout << "认证成功！" << std::endl;
        std::cout << "访问令牌: " << tokens.first.substr(0, 50) << "..." << std::endl;
        std::cout << "刷新令牌: " << tokens.second.substr(0, 50) << "..." << std::endl;
        
        // 验证令牌
        std::cout << "\n3. 令牌验证演示..." << std::endl;
        auto token_info = auth_manager->validateToken(tokens.first);
        
        if (token_info) {
            std::cout << "令牌验证成功！" << std::endl;
            std::cout << "用户名: " << token_info->username << std::endl;
            std::cout << "角色: " << AuthManager::roleToString(token_info->role) << std::endl;
            std::cout << "权限数量: " << token_info->permissions.size() << std::endl;
            
            std::cout << "权限列表: ";
            for (const auto& perm : token_info->permissions) {
                std::cout << AuthManager::permissionToString(perm) << " ";
            }
            std::cout << std::endl;
        } else {
            std::cerr << "令牌验证失败" << std::endl;
            return 1;
        }
        
        // 演示权限检查
        std::cout << "\n4. 权限检查演示..." << std::endl;
        std::cout << "admin用户权限检查:" << std::endl;
        std::cout << "- 读取状态权限: " << (auth_manager->hasPermission("admin", Permission::READ_STATUS) ? "有" : "无") << std::endl;
        std::cout << "- 写入配置权限: " << (auth_manager->hasPermission("admin", Permission::WRITE_CONFIG) ? "有" : "无") << std::endl;
        std::cout << "- 用户管理权限: " << (auth_manager->hasPermission("admin", Permission::MANAGE_USERS) ? "有" : "无") << std::endl;
        
        // 演示用户管理
        std::cout << "\n5. 用户管理演示..." << std::endl;
        
        // 创建操作员用户
        std::cout << "创建操作员用户 'operator'..." << std::endl;
        if (auth_manager->createUser("operator", "op123456", UserRole::OPERATOR, "admin", "127.0.0.1")) {
            std::cout << "操作员用户创建成功" << std::endl;
        } else {
            std::cout << "操作员用户创建失败" << std::endl;
        }
        
        // 创建查看者用户
        std::cout << "创建查看者用户 'viewer'..." << std::endl;
        if (auth_manager->createUser("viewer", "view123", UserRole::VIEWER, "admin", "127.0.0.1")) {
            std::cout << "查看者用户创建成功" << std::endl;
        } else {
            std::cout << "查看者用户创建失败" << std::endl;
        }
        
        // 获取用户列表
        std::cout << "\n6. 用户列表演示..." << std::endl;
        auto user_list = auth_manager->getUserList(1, 10);
        std::cout << "系统中共有 " << user_list.second << " 个用户:" << std::endl;
        
        for (const auto& user : user_list.first) {
            std::cout << "- " << user.username << " (" << AuthManager::roleToString(user.role) << ")" 
                      << " [" << (user.is_active ? "激活" : "禁用") << "]" << std::endl;
        }
        
        // 演示不同角色的权限
        std::cout << "\n7. 不同角色权限演示..." << std::endl;
        
        std::cout << "operator用户权限:" << std::endl;
        std::cout << "- 读取状态权限: " << (auth_manager->hasPermission("operator", Permission::READ_STATUS) ? "有" : "无") << std::endl;
        std::cout << "- 写入配置权限: " << (auth_manager->hasPermission("operator", Permission::WRITE_CONFIG) ? "有" : "无") << std::endl;
        std::cout << "- 用户管理权限: " << (auth_manager->hasPermission("operator", Permission::MANAGE_USERS) ? "有" : "无") << std::endl;
        
        std::cout << "viewer用户权限:" << std::endl;
        std::cout << "- 读取状态权限: " << (auth_manager->hasPermission("viewer", Permission::READ_STATUS) ? "有" : "无") << std::endl;
        std::cout << "- 写入配置权限: " << (auth_manager->hasPermission("viewer", Permission::WRITE_CONFIG) ? "有" : "无") << std::endl;
        std::cout << "- 用户管理权限: " << (auth_manager->hasPermission("viewer", Permission::MANAGE_USERS) ? "有" : "无") << std::endl;
        
        // 演示令牌刷新
        std::cout << "\n8. 令牌刷新演示..." << std::endl;
        auto new_tokens = auth_manager->refreshToken(tokens.second, "127.0.0.1");
        
        if (!new_tokens.first.empty()) {
            std::cout << "令牌刷新成功！" << std::endl;
            std::cout << "新访问令牌: " << new_tokens.first.substr(0, 50) << "..." << std::endl;
        } else {
            std::cout << "令牌刷新失败" << std::endl;
        }
        
        // 演示审计日志
        std::cout << "\n9. 审计日志演示..." << std::endl;
        
        // 记录一些审计事件
        auth_manager->logAudit("admin", "VIEW_DASHBOARD", "/dashboard", "127.0.0.1", "demo-client/1.0", true, "查看系统仪表盘");
        auth_manager->logAudit("operator", "UPDATE_CONFIG", "/api/v1/config/ptp", "*************", "web-client/2.0", true, "更新PTP配置");
        auth_manager->logAudit("viewer", "ACCESS_DENIED", "/api/v1/config/ptp", "*************", "mobile-app/1.5", false, "权限不足");
        
        // 获取审计日志
        auto audit_logs = auth_manager->getAuditLogs(1, 10);
        std::cout << "最近的审计日志 (" << audit_logs.first.size() << " 条):" << std::endl;
        
        for (const auto& log : audit_logs.first) {
            std::cout << "- [" << log.timestamp << "] " << log.username 
                      << " " << log.action << " " << log.resource 
                      << " from " << log.ip_address 
                      << " [" << (log.success ? "成功" : "失败") << "]" << std::endl;
            if (!log.details.empty()) {
                std::cout << "  详情: " << log.details << std::endl;
            }
        }
        
        // 演示用户登出
        std::cout << "\n10. 用户登出演示..." << std::endl;
        if (auth_manager->logout(tokens.first, "127.0.0.1")) {
            std::cout << "用户登出成功" << std::endl;
        } else {
            std::cout << "用户登出失败" << std::endl;
        }
        
        // 演示活跃会话管理
        std::cout << "\n11. 会话管理演示..." << std::endl;
        
        // 重新登录创建会话
        auto session_tokens = auth_manager->authenticate("operator", "op123456", "*************", "session-demo/1.0");
        if (!session_tokens.first.empty()) {
            std::cout << "operator用户登录成功，创建新会话" << std::endl;
        }
        
        // 获取活跃会话
        auto active_sessions = auth_manager->getActiveSessions();
        std::cout << "当前活跃会话数量: " << active_sessions.size() << std::endl;
        
        for (const auto& session : active_sessions) {
            std::cout << "- " << session.username << " from " << session.ip_address 
                      << " (最后活动: " << session.last_activity << ")" << std::endl;
        }
        
        // 清理过期令牌
        std::cout << "\n12. 清理过期令牌..." << std::endl;
        auth_manager->cleanupExpiredTokens();
        std::cout << "过期令牌清理完成" << std::endl;
        
        std::cout << "\n=== 认证系统演示完成 ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "演示过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    // 关闭日志系统
    logger.Shutdown();
    
    return 0;
}