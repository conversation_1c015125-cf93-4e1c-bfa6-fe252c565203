/**
 * @file websocket_server_example.cpp
 * @brief WebSocket服务器使用示例
 * 
 * 本示例演示如何使用WebSocket服务器进行实时通信，包括：
 * - 服务器启动和配置
 * - 客户端连接管理
 * - 消息广播和订阅
 * - 实时状态推送
 * - 性能监控
 */

#include <api/websocket_server.h>
#include <api/websocket_event_listener.h>
#include <api/timing_service.h>
#include <core/logger.h>
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>

using namespace timing_server::api;
using namespace timing_server::core;

/**
 * @brief 示例授时服务实现
 */
class ExampleTimingService : public TimingService {
public:
    SystemStatus getSystemStatus() override {
        SystemStatus status;
        status.current_state = ClockState::LOCKED;
        status.active_source = TimeSource::GNSS;
        status.uptime_seconds = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        status.health = SystemHealth::HEALTHY;
        
        // 添加模拟时间源
        TimeSourceInfo gnss_source;
        gnss_source.type = TimeSource::GNSS;
        gnss_source.status = TimeSourceStatus::ACTIVE;
        gnss_source.quality.accuracy_ns = 50.0;
        gnss_source.quality.stability_ppm = 1e-12;
        gnss_source.quality.confidence = 95;
        gnss_source.quality.is_traceable = true;
        gnss_source.priority = 1;
        gnss_source.last_update_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        status.sources.push_back(gnss_source);
        
        return status;
    }
    
    std::string getPtpConfig() override { return R"({"domain": 0, "priority1": 128})"; }
    bool updatePtpConfig(const std::string&) override { return true; }
    std::string getNtpConfig() override { return R"({"stratum": 1, "reference_id": "GPS"})"; }
    bool updateNtpConfig(const std::string&) override { return true; }
    
    LogQueryResult queryLogs(const LogQueryParams&) override {
        LogQueryResult result;
        result.total_entries = 0;
        result.current_page = 1;
        result.total_pages = 1;
        return result;
    }
    
    HealthStatus getHealthStatus() override {
        HealthStatus health;
        health.status = "HEALTHY";
        health.uptime_seconds = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - start_time_).count();
        return health;
    }
    
    bool restartSystem() override { return true; }
    
    PerformanceMetrics getMetrics(const std::string&, const std::string&) override {
        PerformanceMetrics metrics;
        metrics.current_accuracy_ns = 50.0;
        metrics.average_accuracy_ns = 45.0;
        metrics.max_accuracy_ns = 100.0;
        metrics.allan_deviation_1s = 1e-12;
        return metrics;
    }
    
    std::string validateConfig(const std::string&, const std::string&) override { return ""; }
    std::string getConfigSchema(const std::string&) override { return "{}"; }

private:
    std::chrono::steady_clock::time_point start_time_ = std::chrono::steady_clock::now();
};

/**
 * @brief 全局变量用于信号处理
 */
std::unique_ptr<WebSocketServer> g_websocket_server;
bool g_running = true;

/**
 * @brief 信号处理函数
 */
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在关闭服务器..." << std::endl;
    g_running = false;
    
    if (g_websocket_server) {
        g_websocket_server->stop();
    }
}

/**
 * @brief 演示基本WebSocket服务器功能
 */
void demonstrateBasicFunctionality() {
    std::cout << "\n=== WebSocket服务器基本功能演示 ===" << std::endl;
    
    // 创建授时服务
    auto timing_service = std::make_shared<ExampleTimingService>();
    
    // 配置WebSocket服务器
    WebSocketServerConfig config;
    config.port = 8081;
    config.max_connections = 100;
    config.heartbeat_interval_ms = 30000;
    config.heartbeat_timeout_ms = 60000;
    config.message_queue_size = 1000;
    config.require_authentication = true;
    config.bind_address = "0.0.0.0";
    
    // 创建WebSocket服务器
    auto websocket_server = std::make_unique<WebSocketServer>(config, timing_service);
    g_websocket_server = std::move(websocket_server);
    
    // 创建事件监听器
    auto event_listener = std::make_shared<WebSocketEventListener>();
    g_websocket_server->setEventListener(event_listener);
    
    // 启动服务器
    if (!g_websocket_server->start()) {
        std::cerr << "启动WebSocket服务器失败" << std::endl;
        return;
    }
    
    std::cout << "WebSocket服务器已启动，监听端口: " << config.port << std::endl;
    std::cout << "最大连接数: " << config.max_connections << std::endl;
    std::cout << "心跳间隔: " << config.heartbeat_interval_ms << "ms" << std::endl;
    
    // 启动实时状态推送
    g_websocket_server->startStatusPushing();
    std::cout << "实时状态推送已启动" << std::endl;
}

/**
 * @brief 演示消息广播功能
 */
void demonstrateMessageBroadcast() {
    std::cout << "\n=== 消息广播功能演示 ===" << std::endl;
    
    if (!g_websocket_server || !g_websocket_server->isRunning()) {
        std::cout << "WebSocket服务器未运行" << std::endl;
        return;
    }
    
    // 创建不同类型的消息
    std::vector<std::pair<WebSocketMessage, std::string>> test_messages = {
        {
            WebSocketMessage(WebSocketMessageType::STATUS_UPDATE, 
                           R"({"state": "LOCKED", "accuracy_ns": 50.0, "active_source": "GNSS"})"),
            "status_update"
        },
        {
            WebSocketMessage(WebSocketMessageType::ALARM, 
                           R"({"severity": "WARNING", "component": "GNSS", "message": "信号质量下降"})"),
            "alarm"
        },
        {
            WebSocketMessage(WebSocketMessageType::METRICS, 
                           R"({"cpu_usage": 15.2, "memory_usage": 45.6, "accuracy_ns": 48.3})"),
            "metrics"
        }
    };
    
    // 广播测试消息
    for (const auto& [message, event_type] : test_messages) {
        uint32_t sent_count = g_websocket_server->broadcastMessage(message, event_type);
        std::cout << "广播 " << event_type << " 消息给 " << sent_count << " 个客户端" << std::endl;
        
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
}

/**
 * @brief 演示服务器统计信息
 */
void demonstrateServerStats() {
    std::cout << "\n=== 服务器统计信息演示 ===" << std::endl;
    
    if (!g_websocket_server || !g_websocket_server->isRunning()) {
        std::cout << "WebSocket服务器未运行" << std::endl;
        return;
    }
    
    auto stats = g_websocket_server->getStats();
    
    std::cout << "服务器统计信息:" << std::endl;
    std::cout << "  总连接数: " << stats.total_connections << std::endl;
    std::cout << "  活跃连接数: " << stats.active_connections << std::endl;
    std::cout << "  已认证连接数: " << stats.authenticated_connections << std::endl;
    std::cout << "  发送消息数: " << stats.messages_sent << std::endl;
    std::cout << "  接收消息数: " << stats.messages_received << std::endl;
    std::cout << "  发送字节数: " << stats.bytes_sent << std::endl;
    std::cout << "  接收字节数: " << stats.bytes_received << std::endl;
    std::cout << "  心跳超时数: " << stats.heartbeat_timeouts << std::endl;
    std::cout << "  认证失败数: " << stats.authentication_failures << std::endl;
    std::cout << "  平均延迟: " << stats.average_latency_ms << "ms" << std::endl;
    
    auto uptime = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now() - stats.start_time).count();
    std::cout << "  运行时间: " << uptime << "秒" << std::endl;
}

/**
 * @brief 演示连接管理
 */
void demonstrateConnectionManagement() {
    std::cout << "\n=== 连接管理演示 ===" << std::endl;
    
    if (!g_websocket_server || !g_websocket_server->isRunning()) {
        std::cout << "WebSocket服务器未运行" << std::endl;
        return;
    }
    
    auto connections = g_websocket_server->getActiveConnections();
    std::cout << "当前活跃连接数: " << connections.size() << std::endl;
    
    for (const auto& conn : connections) {
        std::cout << "连接 " << conn.connection_id << ":" << std::endl;
        std::cout << "  地址: " << conn.remote_address << std::endl;
        std::cout << "  状态: " << static_cast<int>(conn.state) << std::endl;
        std::cout << "  用户: " << conn.user_id << std::endl;
        std::cout << "  角色: " << conn.user_role << std::endl;
        std::cout << "  发送消息数: " << conn.messages_sent << std::endl;
        std::cout << "  接收消息数: " << conn.messages_received << std::endl;
        
        auto connect_duration = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - conn.connect_time).count();
        std::cout << "  连接时长: " << connect_duration << "秒" << std::endl;
        
        std::cout << "  订阅事件: ";
        for (const auto& subscription : conn.subscriptions) {
            std::cout << subscription << " ";
        }
        std::cout << std::endl;
    }
}

/**
 * @brief 演示性能测试
 */
void demonstratePerformanceTest() {
    std::cout << "\n=== 性能测试演示 ===" << std::endl;
    
    if (!g_websocket_server || !g_websocket_server->isRunning()) {
        std::cout << "WebSocket服务器未运行" << std::endl;
        return;
    }
    
    const int num_messages = 1000;
    std::cout << "开始性能测试，发送 " << num_messages << " 条消息..." << std::endl;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_messages; ++i) {
        WebSocketMessage message(WebSocketMessageType::STATUS_UPDATE, 
                               R"({"test_id": )" + std::to_string(i) + 
                               R"(, "timestamp": )" + std::to_string(
                                   std::chrono::duration_cast<std::chrono::milliseconds>(
                                       std::chrono::system_clock::now().time_since_epoch()).count()) + "}");
        
        g_websocket_server->broadcastMessage(message, "performance_test");
        
        // 每100条消息显示进度
        if ((i + 1) % 100 == 0) {
            std::cout << "已发送 " << (i + 1) << " 条消息" << std::endl;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    double messages_per_second = (num_messages * 1000.0) / duration.count();
    
    std::cout << "性能测试完成:" << std::endl;
    std::cout << "  总时间: " << duration.count() << "ms" << std::endl;
    std::cout << "  消息处理速度: " << messages_per_second << " 消息/秒" << std::endl;
    std::cout << "  平均每条消息耗时: " << (duration.count() / (double)num_messages) << "ms" << std::endl;
}

/**
 * @brief 主函数
 */
int main() {
    std::cout << "WebSocket服务器示例程序" << std::endl;
    std::cout << "========================" << std::endl;
    
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 演示基本功能
        demonstrateBasicFunctionality();
        
        // 等待一段时间让服务器完全启动
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        // 主循环，定期执行演示功能
        int demo_cycle = 0;
        while (g_running && g_websocket_server && g_websocket_server->isRunning()) {
            demo_cycle++;
            std::cout << "\n--- 演示周期 " << demo_cycle << " ---" << std::endl;
            
            // 演示消息广播
            demonstrateMessageBroadcast();
            
            // 演示服务器统计
            demonstrateServerStats();
            
            // 演示连接管理
            demonstrateConnectionManagement();
            
            // 每5个周期执行一次性能测试
            if (demo_cycle % 5 == 0) {
                demonstratePerformanceTest();
            }
            
            // 等待下一个演示周期
            for (int i = 0; i < 10 && g_running; ++i) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n程序正常退出" << std::endl;
    return 0;
}

/**
 * @brief 编译和运行说明
 * 
 * 编译命令:
 * g++ -std=c++17 -I../include -I../../../include \
 *     websocket_server_example.cpp \
 *     ../src/api/websocket_server.cpp \
 *     ../src/api/websocket_event_listener.cpp \
 *     ../src/core/logger.cpp \
 *     -lpthread -lssl -lcrypto -o websocket_server_example
 * 
 * 运行命令:
 * ./websocket_server_example
 * 
 * 测试WebSocket连接:
 * 可以使用WebSocket客户端工具连接到 ws://localhost:8081
 * 
 * 认证消息格式:
 * {"type": "auth", "token": "admin_token"}
 * 
 * 订阅消息格式:
 * {"type": "subscription", "action": "subscribe", "events": ["status_update", "alarm"]}
 * 
 * 心跳消息格式:
 * {"type": "heartbeat"}
 */