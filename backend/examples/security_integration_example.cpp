/**
 * @file security_integration_example.cpp
 * @brief 安全管理器集成示例
 * 
 * 本示例展示如何将SecurityManager集成到现有的API服务器中，
 * 实现完整的安全防护功能，包括：
 * - IP白名单/黑名单控制
 * - 访问频率限制
 * - 暴力破解攻击检测
 * - HTTPS/TLS支持
 * - 安全事件监控和日志记录
 */

#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <signal.h>

#include <api/security_manager.h>
#include <api/auth_manager.h>
#include <core/logger.h>
#include <core/types.h>

using namespace timing_server::api;
using namespace timing_server::core;

// 全局变量用于优雅关闭
std::atomic<bool> g_shutdown_requested{false};
std::unique_ptr<SecurityManager> g_security_manager;
std::unique_ptr<AuthManager> g_auth_manager;

// 信号处理函数
void signalHandler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在关闭服务器..." << std::endl;
    g_shutdown_requested.store(true);
}

/**
 * @brief 模拟HTTP请求处理器
 * 展示如何在请求处理过程中集成安全检查
 */
class SecureHttpRequestHandler {
public:
    SecureHttpRequestHandler(SecurityManager* security_mgr, AuthManager* auth_mgr)
        : m_security_manager(security_mgr)
        , m_auth_manager(auth_mgr) {}
    
    /**
     * @brief 处理HTTP请求
     * @param client_ip 客户端IP地址
     * @param user_agent 用户代理字符串
     * @param method HTTP方法
     * @param path 请求路径
     * @param headers HTTP头部
     * @param body 请求体
     * @return HTTP响应
     */
    std::string handleRequest(const std::string& client_ip,
                             const std::string& user_agent,
                             const std::string& method,
                             const std::string& path,
                             const std::map<std::string, std::string>& headers,
                             const std::string& body) {
        
        // 1. 检查IP访问权限
        if (!m_security_manager->isIpAllowed(client_ip)) {
            LOG_WARNING(LogComponent::API_SERVER, 
                       "IP访问被拒绝: " + client_ip + " -> " + path);
            
            m_security_manager->recordAccess(client_ip, user_agent, path, false);
            return createErrorResponse(403, "Forbidden", "IP address blocked");
        }
        
        // 2. 检查访问频率限制
        if (!m_security_manager->checkRateLimit(client_ip, path)) {
            LOG_WARNING(LogComponent::API_SERVER, 
                       "访问频率超限: " + client_ip + " -> " + path);
            
            m_security_manager->recordAccess(client_ip, user_agent, path, false);
            return createErrorResponse(429, "Too Many Requests", "Rate limit exceeded");
        }
        
        // 3. 检查用户认证（对于需要认证的端点）
        std::string username;
        if (requiresAuthentication(path)) {
            auto auth_header = headers.find("Authorization");
            if (auth_header == headers.end()) {
                LOG_WARNING(LogComponent::API_SERVER, 
                           "缺少认证头: " + client_ip + " -> " + path);
                
                m_security_manager->recordAccess(client_ip, user_agent, path, false);
                return createErrorResponse(401, "Unauthorized", "Authentication required");
            }
            
            // 解析Bearer令牌
            std::string token = extractBearerToken(auth_header->second);
            if (token.empty()) {
                LOG_WARNING(LogComponent::API_SERVER, 
                           "无效的认证格式: " + client_ip + " -> " + path);
                
                m_security_manager->recordAccess(client_ip, user_agent, path, false);
                return createErrorResponse(401, "Unauthorized", "Invalid authentication format");
            }
            
            // 验证令牌
            auto token_info = m_auth_manager->validateToken(token);
            if (!token_info) {
                LOG_WARNING(LogComponent::API_SERVER, 
                           "令牌验证失败: " + client_ip + " -> " + path);
                
                // 记录认证失败的安全事件
                m_security_manager->recordSecurityEvent({
                    GetCurrentTimestampNs() / 1000000,
                    SecurityEventType::AUTHENTICATION_FAILED,
                    client_ip,
                    user_agent,
                    "",
                    path,
                    "JWT令牌验证失败",
                    5
                });
                
                m_security_manager->recordAccess(client_ip, user_agent, path, false, "");
                return createErrorResponse(401, "Unauthorized", "Invalid or expired token");
            }
            
            username = token_info->username;
            
            // 4. 检查用户权限
            if (!checkPermission(username, path, method)) {
                LOG_WARNING(LogComponent::API_SERVER, 
                           "权限不足: " + username + "@" + client_ip + " -> " + method + " " + path);
                
                // 记录授权失败的安全事件
                m_security_manager->recordSecurityEvent({
                    GetCurrentTimestampNs() / 1000000,
                    SecurityEventType::AUTHORIZATION_DENIED,
                    client_ip,
                    user_agent,
                    username,
                    path,
                    "用户权限不足: " + method + " " + path,
                    6
                });
                
                m_security_manager->recordAccess(client_ip, user_agent, path, false, username);
                return createErrorResponse(403, "Forbidden", "Insufficient permissions");
            }
        }
        
        // 5. 处理具体的API请求
        std::string response;
        bool success = false;
        
        try {
            if (path == "/api/v1/status") {
                response = handleStatusRequest();
                success = true;
            } else if (path == "/api/v1/config" && method == "GET") {
                response = handleGetConfigRequest(username);
                success = true;
            } else if (path == "/api/v1/config" && method == "PUT") {
                response = handlePutConfigRequest(username, body);
                success = true;
            } else if (path == "/api/v1/auth/login" && method == "POST") {
                response = handleLoginRequest(client_ip, user_agent, body);
                success = true;
            } else if (path == "/api/v1/security/events" && method == "GET") {
                response = handleSecurityEventsRequest(username);
                success = true;
            } else if (path == "/api/v1/security/whitelist" && method == "POST") {
                response = handleWhitelistRequest(username, body);
                success = true;
            } else {
                response = createErrorResponse(404, "Not Found", "Endpoint not found");
                success = false;
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::API_SERVER, 
                     "请求处理异常: " + std::string(e.what()));
            
            response = createErrorResponse(500, "Internal Server Error", "Request processing failed");
            success = false;
        }
        
        // 6. 记录访问日志
        m_security_manager->recordAccess(client_ip, user_agent, path, success, username);
        
        // 7. 记录审计日志（对于重要操作）
        if (success && isAuditableAction(path, method)) {
            m_auth_manager->logAudit(username, method + " " + path, path, 
                                   client_ip, user_agent, success, "");
        }
        
        LOG_INFO(LogComponent::API_SERVER, 
                "请求处理完成: " + client_ip + " -> " + method + " " + path + 
                " (" + (success ? "成功" : "失败") + ")");
        
        return response;
    }

private:
    SecurityManager* m_security_manager;
    AuthManager* m_auth_manager;
    
    bool requiresAuthentication(const std::string& path) {
        // 定义需要认证的端点
        std::vector<std::string> auth_required_paths = {
            "/api/v1/config",
            "/api/v1/system",
            "/api/v1/users",
            "/api/v1/security"
        };
        
        for (const auto& auth_path : auth_required_paths) {
            if (path.find(auth_path) == 0) {
                return true;
            }
        }
        
        return false;
    }
    
    std::string extractBearerToken(const std::string& auth_header) {
        const std::string bearer_prefix = "Bearer ";
        if (auth_header.substr(0, bearer_prefix.length()) == bearer_prefix) {
            return auth_header.substr(bearer_prefix.length());
        }
        return "";
    }
    
    bool checkPermission(const std::string& username, const std::string& path, const std::string& method) {
        // 简化的权限检查逻辑
        if (path.find("/api/v1/config") == 0 && method == "PUT") {
            return m_auth_manager->hasPermission(username, Permission::WRITE_CONFIG);
        }
        
        if (path.find("/api/v1/security") == 0) {
            return m_auth_manager->hasPermission(username, Permission::MANAGE_USERS);
        }
        
        return m_auth_manager->hasPermission(username, Permission::READ_STATUS);
    }
    
    bool isAuditableAction(const std::string& path, const std::string& method) {
        // 定义需要审计的操作
        return (method == "PUT" || method == "POST" || method == "DELETE") &&
               (path.find("/api/v1/config") == 0 || 
                path.find("/api/v1/users") == 0 ||
                path.find("/api/v1/security") == 0);
    }
    
    std::string createErrorResponse(int code, const std::string& status, const std::string& message) {
        return "HTTP/1.1 " + std::to_string(code) + " " + status + "\r\n"
               "Content-Type: application/json\r\n"
               "Connection: close\r\n\r\n"
               "{\"error\":{\"code\":" + std::to_string(code) + 
               ",\"message\":\"" + message + "\"}}";
    }
    
    std::string handleStatusRequest() {
        return "HTTP/1.1 200 OK\r\n"
               "Content-Type: application/json\r\n\r\n"
               "{\"status\":\"ok\",\"timestamp\":" + std::to_string(GetCurrentTimestampNs()) + "}";
    }
    
    std::string handleGetConfigRequest(const std::string& username) {
        return "HTTP/1.1 200 OK\r\n"
               "Content-Type: application/json\r\n\r\n"
               "{\"config\":{\"user\":\"" + username + "\"}}";
    }
    
    std::string handlePutConfigRequest(const std::string& username, const std::string& body) {
        // 这里应该解析和验证配置
        return "HTTP/1.1 200 OK\r\n"
               "Content-Type: application/json\r\n\r\n"
               "{\"result\":\"config updated by " + username + "\"}";
    }
    
    std::string handleLoginRequest(const std::string& client_ip, 
                                  const std::string& user_agent, 
                                  const std::string& body) {
        // 简化的登录处理
        // 实际实现应该解析JSON body获取用户名和密码
        auto tokens = m_auth_manager->authenticate("admin", "password", client_ip, user_agent);
        
        if (!tokens.first.empty()) {
            return "HTTP/1.1 200 OK\r\n"
                   "Content-Type: application/json\r\n\r\n"
                   "{\"access_token\":\"" + tokens.first + 
                   "\",\"refresh_token\":\"" + tokens.second + "\"}";
        } else {
            return createErrorResponse(401, "Unauthorized", "Invalid credentials");
        }
    }
    
    std::string handleSecurityEventsRequest(const std::string& username) {
        auto events = m_security_manager->getSecurityEvents(50);
        
        std::string json_events = "[";
        for (size_t i = 0; i < events.size(); i++) {
            if (i > 0) json_events += ",";
            json_events += "{\"type\":\"" + SecurityManager::securityEventTypeToString(events[i].type) + 
                          "\",\"ip\":\"" + events[i].ip_address + 
                          "\",\"timestamp\":" + std::to_string(events[i].timestamp) + 
                          ",\"severity\":" + std::to_string(events[i].severity) + "}";
        }
        json_events += "]";
        
        return "HTTP/1.1 200 OK\r\n"
               "Content-Type: application/json\r\n\r\n"
               "{\"events\":" + json_events + "}";
    }
    
    std::string handleWhitelistRequest(const std::string& username, const std::string& body) {
        // 简化的白名单管理
        // 实际实现应该解析JSON body获取IP地址
        std::string ip = "*************"; // 从body解析
        
        if (m_security_manager->addToWhitelist(ip)) {
            return "HTTP/1.1 200 OK\r\n"
                   "Content-Type: application/json\r\n\r\n"
                   "{\"result\":\"IP added to whitelist\",\"ip\":\"" + ip + "\"}";
        } else {
            return createErrorResponse(400, "Bad Request", "Failed to add IP to whitelist");
        }
    }
};

/**
 * @brief 安全监控线程
 * 定期检查安全状态并生成报告
 */
void securityMonitoringThread() {
    LOG_INFO(LogComponent::API_SERVER, "安全监控线程已启动");
    
    while (!g_shutdown_requested.load()) {
        try {
            // 获取最近的安全事件
            auto events = g_security_manager->getSecurityEvents(10);
            
            // 统计不同类型的事件
            std::map<SecurityEventType, int> event_counts;
            for (const auto& event : events) {
                event_counts[event.type]++;
            }
            
            // 检查是否有严重的安全事件
            bool has_critical_events = false;
            for (const auto& event : events) {
                if (event.severity >= 8) {
                    has_critical_events = true;
                    LOG_CRITICAL(LogComponent::API_SERVER, 
                                "严重安全事件: " + SecurityManager::securityEventTypeToString(event.type) + 
                                " from " + event.ip_address);
                }
            }
            
            // 获取IP统计信息
            auto ip_stats = g_security_manager->getAllIpStats(20);
            
            // 检查是否有异常的IP活动
            for (const auto& stats : ip_stats) {
                if (stats.failed_attempts > 5) {
                    LOG_WARNING(LogComponent::API_SERVER, 
                               "IP异常活动: " + stats.ip_address + 
                               " 失败次数: " + std::to_string(stats.failed_attempts));
                }
            }
            
            // 每分钟检查一次
            std::this_thread::sleep_for(std::chrono::minutes(1));
            
        } catch (const std::exception& e) {
            LOG_ERROR(LogComponent::API_SERVER, 
                     "安全监控线程异常: " + std::string(e.what()));
            std::this_thread::sleep_for(std::chrono::seconds(30));
        }
    }
    
    LOG_INFO(LogComponent::API_SERVER, "安全监控线程已退出");
}

/**
 * @brief 主函数
 */
int main() {
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    std::cout << "=== 安全管理器集成示例 ===" << std::endl;
    std::cout << "本示例展示如何将SecurityManager集成到API服务器中" << std::endl;
    
    try {
        // 1. 初始化安全管理器
        g_security_manager = std::make_unique<SecurityManager>();
        if (!g_security_manager->initialize("platform/config/security.json")) {
            std::cerr << "安全管理器初始化失败" << std::endl;
            return 1;
        }
        
        // 2. 初始化认证管理器
        g_auth_manager = std::make_unique<AuthManager>("your-jwt-secret-key", 3600, 86400);
        if (!g_auth_manager->initialize()) {
            std::cerr << "认证管理器初始化失败" << std::endl;
            return 1;
        }
        
        // 3. 创建请求处理器
        SecureHttpRequestHandler request_handler(g_security_manager.get(), g_auth_manager.get());
        
        // 4. 启动安全监控线程
        std::thread monitoring_thread(securityMonitoringThread);
        
        // 5. 配置一些测试数据
        std::cout << "\n配置测试环境..." << std::endl;
        
        // 添加一些IP到白名单
        g_security_manager->addToWhitelist("127.0.0.1");
        g_security_manager->addToWhitelist("***********/24");
        
        // 添加一些已知的恶意IP到黑名单
        g_security_manager->addToBlacklist("********", 0, "已知恶意IP");
        
        // 6. 模拟一些HTTP请求
        std::cout << "\n开始模拟HTTP请求..." << std::endl;
        
        // 正常请求
        std::cout << "\n--- 正常请求 ---" << std::endl;
        auto response1 = request_handler.handleRequest(
            "*************", "Mozilla/5.0", "GET", "/api/v1/status", {}, "");
        std::cout << "响应: " << response1.substr(0, 50) << "..." << std::endl;
        
        // 需要认证的请求（无令牌）
        std::cout << "\n--- 无认证请求 ---" << std::endl;
        auto response2 = request_handler.handleRequest(
            "*************", "Mozilla/5.0", "GET", "/api/v1/config", {}, "");
        std::cout << "响应: " << response2.substr(0, 50) << "..." << std::endl;
        
        // 登录请求
        std::cout << "\n--- 登录请求 ---" << std::endl;
        auto response3 = request_handler.handleRequest(
            "*************", "Mozilla/5.0", "POST", "/api/v1/auth/login", 
            {}, "{\"username\":\"admin\",\"password\":\"password\"}");
        std::cout << "响应: " << response3.substr(0, 100) << "..." << std::endl;
        
        // 黑名单IP请求
        std::cout << "\n--- 黑名单IP请求 ---" << std::endl;
        auto response4 = request_handler.handleRequest(
            "********", "Mozilla/5.0", "GET", "/api/v1/status", {}, "");
        std::cout << "响应: " << response4.substr(0, 50) << "..." << std::endl;
        
        // 可疑用户代理请求
        std::cout << "\n--- 可疑用户代理请求 ---" << std::endl;
        auto response5 = request_handler.handleRequest(
            "*************", "sqlmap/1.0", "GET", "/api/v1/status", {}, "");
        std::cout << "响应: " << response5.substr(0, 50) << "..." << std::endl;
        
        // 模拟暴力破解攻击
        std::cout << "\n--- 模拟暴力破解攻击 ---" << std::endl;
        for (int i = 0; i < 15; i++) {
            request_handler.handleRequest(
                "*************", "Mozilla/5.0", "POST", "/api/v1/auth/login",
                {}, "{\"username\":\"admin\",\"password\":\"wrong\"}");
        }
        
        // 检查攻击IP是否被自动阻断
        auto response6 = request_handler.handleRequest(
            "*************", "Mozilla/5.0", "GET", "/api/v1/status", {}, "");
        std::cout << "暴力破解IP后续请求响应: " << response6.substr(0, 50) << "..." << std::endl;
        
        // 7. 显示安全统计信息
        std::cout << "\n=== 安全统计信息 ===" << std::endl;
        
        auto events = g_security_manager->getSecurityEvents(20);
        std::cout << "安全事件数量: " << events.size() << std::endl;
        
        for (const auto& event : events) {
            std::cout << "- " << SecurityManager::securityEventTypeToString(event.type) 
                     << " from " << event.ip_address 
                     << " (严重程度: " << event.severity << ")" << std::endl;
        }
        
        auto ip_stats = g_security_manager->getAllIpStats(10);
        std::cout << "\nIP访问统计 (前10个):" << std::endl;
        for (const auto& stats : ip_stats) {
            std::cout << "- " << stats.ip_address 
                     << " 请求: " << stats.request_count 
                     << " 失败: " << stats.failed_attempts;
            if (stats.is_whitelisted) std::cout << " [白名单]";
            if (stats.is_blacklisted) std::cout << " [黑名单]";
            std::cout << std::endl;
        }
        
        auto whitelist = g_security_manager->getWhitelist();
        std::cout << "\n白名单 (" << whitelist.size() << "个):" << std::endl;
        for (const auto& ip : whitelist) {
            std::cout << "- " << ip << std::endl;
        }
        
        auto blacklist = g_security_manager->getBlacklist();
        std::cout << "\n黑名单 (" << blacklist.size() << "个):" << std::endl;
        for (const auto& ip : blacklist) {
            std::cout << "- " << ip << std::endl;
        }
        
        // 8. 等待用户输入或信号
        std::cout << "\n服务器正在运行，按Ctrl+C退出..." << std::endl;
        
        while (!g_shutdown_requested.load()) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
        // 9. 优雅关闭
        std::cout << "\n正在关闭服务器..." << std::endl;
        
        if (monitoring_thread.joinable()) {
            monitoring_thread.join();
        }
        
        g_auth_manager.reset();
        g_security_manager.reset();
        
        std::cout << "服务器已关闭" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}