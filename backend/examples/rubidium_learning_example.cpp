/**
 * @file rubidium_learning_example.cpp
 * @brief 铷钟学习数据存储系统使用示例
 * 
 * 本示例演示如何使用铷钟学习数据存储系统进行数据的保存、加载、
 * 分析和管理等操作。
 */

#include <iostream>
#include <thread>
#include <chrono>
#include <iomanip>

#include "core/rubidium_learning.h"
#include "core/logger.h"

using namespace timing_server::core;

/**
 * @brief 创建模拟的铷钟学习数据
 * @param base_offset 基础频率偏移
 * @param temperature 温度
 * @param learning_hours 学习时长（小时）
 * @return 铷钟学习数据
 */
RubidiumLearningData CreateSimulatedLearningData(double base_offset, 
                                                double temperature, 
                                                uint32_t learning_hours) {
    RubidiumLearningData data;
    
    // 基本信息
    data.timestamp_ns = GetCurrentTimestampNs();
    data.learning_duration_ns = static_cast<uint64_t>(learning_hours) * 3600ULL * 1000000000ULL;
    data.sample_count = learning_hours * 3600; // 每秒一个采样
    
    // 频率特性（模拟真实铷钟特性）
    data.base_frequency_hz = 10000000.0; // 10MHz
    data.frequency_offset_ppm = base_offset;
    data.frequency_drift_rate_ppm_per_hour = 0.001 + (rand() % 100) * 0.00001;
    data.frequency_stability_ppm = 1e-12 + (rand() % 100) * 1e-15;
    
    // 温度特性
    data.reference_temperature_c = temperature;
    data.temperature_coefficient_ppm_per_c = -0.5 - (rand() % 100) * 0.001;
    data.temperature_coefficient_2nd_ppm_per_c2 = -0.01 - (rand() % 100) * 0.0001;
    
    // 老化特性
    data.aging_rate_ppm_per_day = 0.01 + (rand() % 100) * 0.0001;
    data.aging_acceleration_factor = 1.0 + (rand() % 100) * 0.001;
    data.operating_days = 30 + rand() % 365;
    
    // 环境影响
    data.humidity_coefficient_ppm_per_percent = (rand() % 100) * 0.00001;
    data.pressure_coefficient_ppm_per_hpa = (rand() % 100) * 0.000001;
    data.vibration_sensitivity_ppm_per_g = (rand() % 100) * 0.0001;
    
    // Allan偏差（模拟典型铷钟性能）
    data.allan_deviation_1s = 1e-12 + (rand() % 100) * 1e-15;
    data.allan_deviation_10s = 5e-13 + (rand() % 100) * 1e-16;
    data.allan_deviation_100s = 2e-13 + (rand() % 100) * 1e-16;
    data.allan_deviation_1000s = 1e-13 + (rand() % 100) * 1e-17;
    
    // 质量指标
    data.confidence_level = 0.8 + (rand() % 20) * 0.01; // 80%-99%
    data.prediction_accuracy = 0.85 + (rand() % 15) * 0.01; // 85%-99%
    data.validation_score = 70 + rand() % 30; // 70-99分
    
    // 校正参数
    data.phase_correction_ns = (rand() % 100 - 50) * 0.1; // ±5ns
    data.frequency_correction_ppm = (rand() % 100 - 50) * 0.001; // ±0.05ppm
    data.correction_enabled = (rand() % 2) == 1;
    
    return data;
}

/**
 * @brief 演示基本的数据保存和加载操作
 */
void DemonstrateBasicOperations() {
    std::cout << "\n=== 基本操作演示 ===\n";
    
    // 创建存储管理器
    std::string storage_path = "/tmp/rubidium_learning_demo";
    std::string device_id = "DEMO_RB_001";
    
    RubidiumLearningStorage storage(storage_path, device_id);
    
    // 初始化存储系统
    auto result = storage.Initialize("RB123456", "2.1.0");
    if (result != RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cerr << "存储系统初始化失败: " << StorageResultToString(result) << std::endl;
        return;
    }
    
    std::cout << "存储系统初始化成功\n";
    
    // 创建并保存学习数据
    RubidiumLearningData learning_data = CreateSimulatedLearningData(0.123, 25.5, 24);
    
    result = storage.SaveLearningData(learning_data, true);
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "学习数据保存成功\n";
        std::cout << "数据质量评分: " << learning_data.CalculateQualityScore() << "/100\n";
    } else {
        std::cerr << "学习数据保存失败: " << StorageResultToString(result) << std::endl;
    }
    
    // 加载最新的学习数据
    auto [load_result, loaded_data] = storage.GetLatestLearningData();
    if (load_result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "最新学习数据加载成功\n";
        std::cout << "频率偏移: " << std::fixed << std::setprecision(6) 
                  << loaded_data.frequency_offset_ppm << " ppm\n";
        std::cout << "参考温度: " << loaded_data.reference_temperature_c << " °C\n";
        std::cout << "学习时长: " << (loaded_data.learning_duration_ns / 3600000000000ULL) << " 小时\n";
    } else {
        std::cerr << "加载学习数据失败: " << StorageResultToString(load_result) << std::endl;
    }
    
    storage.Shutdown();
}

/**
 * @brief 演示学习历史管理
 */
void DemonstrateLearningHistory() {
    std::cout << "\n=== 学习历史管理演示 ===\n";
    
    std::string storage_path = "/tmp/rubidium_learning_demo";
    std::string device_id = "DEMO_RB_001";
    
    RubidiumLearningStorage storage(storage_path, device_id);
    auto result = storage.Initialize("RB123456", "2.1.0");
    
    if (result != RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cerr << "存储系统初始化失败\n";
        return;
    }
    
    // 模拟一周的学习数据（每天一次）
    std::cout << "生成一周的模拟学习数据...\n";
    
    for (int day = 0; day < 7; ++day) {
        // 模拟温度变化和频率漂移
        double temperature = 25.0 + day * 0.5 + (rand() % 100 - 50) * 0.01;
        double frequency_offset = 0.1 + day * 0.001 + (rand() % 100 - 50) * 0.0001;
        
        RubidiumLearningData data = CreateSimulatedLearningData(frequency_offset, temperature, 24);
        
        result = storage.SaveLearningData(data, false);
        if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
            std::cout << "第" << (day + 1) << "天数据保存成功 - "
                      << "频率偏移: " << std::fixed << std::setprecision(6) << frequency_offset 
                      << " ppm, 温度: " << std::setprecision(1) << temperature << " °C\n";
        }
        
        // 短暂延迟以确保时间戳不同
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 加载完整的学习历史
    auto [load_result, history] = storage.LoadLearningHistory();
    if (load_result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "\n学习历史加载成功:\n";
        std::cout << "设备ID: " << history.device_id << "\n";
        std::cout << "序列号: " << history.serial_number << "\n";
        std::cout << "固件版本: " << history.firmware_version << "\n";
        std::cout << "总记录数: " << history.learning_records.size() << "\n";
        
        // 分析趋势
        auto trends = history.AnalyzeTrends();
        std::cout << "\n趋势分析结果:\n";
        std::cout << "频率漂移趋势: " << std::scientific << trends.frequency_drift_trend << "\n";
        std::cout << "温度趋势: " << trends.temperature_trend << "\n";
        std::cout << "老化趋势: " << trends.aging_trend << "\n";
        std::cout << "稳定性趋势: " << trends.stability_trend << "\n";
    }
    
    storage.Shutdown();
}

/**
 * @brief 演示数据分析功能
 */
void DemonstrateDataAnalysis() {
    std::cout << "\n=== 数据分析演示 ===\n";
    
    std::string storage_path = "/tmp/rubidium_learning_demo";
    std::string device_id = "DEMO_RB_001";
    
    auto storage = std::make_shared<RubidiumLearningStorage>(storage_path, device_id);
    auto result = storage->Initialize("RB123456", "2.1.0");
    
    if (result != RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cerr << "存储系统初始化失败\n";
        return;
    }
    
    // 确保有学习数据
    RubidiumLearningData data = CreateSimulatedLearningData(0.15, 26.0, 48);
    data.temperature_coefficient_ppm_per_c = -0.6;
    data.frequency_drift_rate_ppm_per_hour = 0.002;
    data.aging_rate_ppm_per_day = 0.015;
    
    storage->SaveLearningData(data, false);
    
    // 创建分析器
    RubidiumLearningAnalyzer analyzer(storage);
    
    // 预测1小时后的频率偏移
    uint64_t future_time = GetCurrentTimestampNs() + 3600ULL * 1000000000ULL; // 1小时后
    double predicted_offset_25c = analyzer.PredictFrequencyOffset(future_time, 25.0);
    double predicted_offset_30c = analyzer.PredictFrequencyOffset(future_time, 30.0);
    
    std::cout << "频率偏移预测 (1小时后):\n";
    std::cout << "  25°C: " << std::fixed << std::setprecision(6) << predicted_offset_25c << " ppm\n";
    std::cout << "  30°C: " << std::fixed << std::setprecision(6) << predicted_offset_30c << " ppm\n";
    
    // 计算温度补偿
    double compensation_25_to_30 = analyzer.CalculateTemperatureCompensation(30.0, 25.0);
    double compensation_25_to_20 = analyzer.CalculateTemperatureCompensation(20.0, 25.0);
    
    std::cout << "\n温度补偿计算:\n";
    std::cout << "  25°C → 30°C: " << std::fixed << std::setprecision(6) << compensation_25_to_30 << " ppm\n";
    std::cout << "  25°C → 20°C: " << std::fixed << std::setprecision(6) << compensation_25_to_20 << " ppm\n";
    
    storage->Shutdown();
}

/**
 * @brief 演示备份和恢复功能
 */
void DemonstrateBackupRestore() {
    std::cout << "\n=== 备份和恢复演示 ===\n";
    
    std::string storage_path = "/tmp/rubidium_learning_demo";
    std::string device_id = "DEMO_RB_001";
    
    RubidiumLearningStorage storage(storage_path, device_id);
    auto result = storage.Initialize("RB123456", "2.1.0");
    
    if (result != RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cerr << "存储系统初始化失败\n";
        return;
    }
    
    // 保存原始数据
    RubidiumLearningData original_data = CreateSimulatedLearningData(0.200, 24.0, 36);
    result = storage.SaveLearningData(original_data, true);
    
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "原始数据保存成功 - 频率偏移: " 
                  << std::fixed << std::setprecision(6) << original_data.frequency_offset_ppm << " ppm\n";
    }
    
    // 创建备份
    std::string backup_path = storage_path + "/manual_backup.dat";
    result = storage.CreateBackup(backup_path);
    
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "备份创建成功: " << backup_path << "\n";
    }
    
    // 保存新数据（模拟数据更新）
    RubidiumLearningData new_data = CreateSimulatedLearningData(0.350, 27.0, 48);
    result = storage.SaveLearningData(new_data, false);
    
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "新数据保存成功 - 频率偏移: " 
                  << std::fixed << std::setprecision(6) << new_data.frequency_offset_ppm << " ppm\n";
    }
    
    // 验证当前数据
    auto [load_result, current_data] = storage.GetLatestLearningData();
    if (load_result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "当前数据频率偏移: " 
                  << std::fixed << std::setprecision(6) << current_data.frequency_offset_ppm << " ppm\n";
    }
    
    // 从备份恢复
    std::cout << "从备份恢复数据...\n";
    result = storage.RestoreFromBackup(backup_path);
    
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "备份恢复成功\n";
        
        // 验证恢复的数据
        auto [restore_result, restored_data] = storage.GetLatestLearningData();
        if (restore_result == RubidiumLearningStorage::StorageResult::SUCCESS) {
            std::cout << "恢复后数据频率偏移: " 
                      << std::fixed << std::setprecision(6) << restored_data.frequency_offset_ppm << " ppm\n";
        }
    }
    
    storage.Shutdown();
}

/**
 * @brief 演示存储统计和管理功能
 */
void DemonstrateStorageManagement() {
    std::cout << "\n=== 存储管理演示 ===\n";
    
    std::string storage_path = "/tmp/rubidium_learning_demo";
    std::string device_id = "DEMO_RB_001";
    
    RubidiumLearningStorage storage(storage_path, device_id);
    auto result = storage.Initialize("RB123456", "2.1.0");
    
    if (result != RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cerr << "存储系统初始化失败\n";
        return;
    }
    
    // 获取存储统计信息
    auto stats = storage.GetStatistics();
    
    std::cout << "存储统计信息:\n";
    std::cout << "  总记录数: " << stats.total_records << "\n";
    std::cout << "  文件大小: " << stats.file_size_bytes << " 字节\n";
    std::cout << "  平均质量评分: " << std::fixed << std::setprecision(1) 
              << stats.average_quality_score << "/100\n";
    std::cout << "  备份数量: " << stats.backup_count << "\n";
    
    if (stats.oldest_record_time > 0) {
        std::cout << "  最旧记录: " << TimestampToIsoString(stats.oldest_record_time) << "\n";
        std::cout << "  最新记录: " << TimestampToIsoString(stats.newest_record_time) << "\n";
    }
    
    // 验证数据完整性
    std::cout << "\n验证数据完整性...\n";
    result = storage.VerifyIntegrity(false);
    
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "数据完整性验证通过\n";
    } else {
        std::cout << "数据完整性验证失败: " << StorageResultToString(result) << "\n";
    }
    
    // 导出数据为JSON
    std::string export_path = storage_path + "/export_demo.json";
    result = storage.ExportToJson(export_path, false);
    
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "数据导出成功: " << export_path << "\n";
    }
    
    // 压缩存储
    std::cout << "压缩存储文件...\n";
    result = storage.CompactStorage();
    
    if (result == RubidiumLearningStorage::StorageResult::SUCCESS) {
        std::cout << "存储压缩完成\n";
    }
    
    storage.Shutdown();
}

int main() {
    std::cout << "铷钟学习数据存储系统演示程序\n";
    std::cout << "================================\n";
    
    // 初始化日志系统
    Logger::GetInstance().Initialize();
    Logger::GetInstance().SetLogLevel(LogLevel::INFO);
    Logger::GetInstance().AddOutput(std::make_unique<ConsoleLogOutput>(true));
    
    try {
        // 演示各种功能
        DemonstrateBasicOperations();
        DemonstrateLearningHistory();
        DemonstrateDataAnalysis();
        DemonstrateBackupRestore();
        DemonstrateStorageManagement();
        
        std::cout << "\n=== 演示完成 ===\n";
        std::cout << "所有功能演示成功完成！\n";
        
    } catch (const std::exception& e) {
        std::cerr << "演示过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    // 关闭日志系统
    Logger::GetInstance().Shutdown();
    
    return 0;
}