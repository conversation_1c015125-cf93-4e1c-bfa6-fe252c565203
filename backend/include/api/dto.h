#pragma once

#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/Types.hpp"
#include "oatpp/core/data/mapping/type/Object.hpp"
#include "oatpp/core/data/mapping/type/List.hpp"
#include "oatpp/core/data/mapping/type/Primitive.hpp"
#include <core/types.h>

#include OATPP_CODEGEN_BEGIN(DTO)

namespace timing_server {
namespace api {

/**
 * @brief 时间质量指标DTO
 * 用于API响应中的时间质量信息传输
 */
class TimeQualityDto : public oatpp::DTO {
    DTO_INIT(TimeQualityDto, DTO)
    
    DTO_FIELD(Float64, accuracy_ns, "accuracy_ns");         // 时间精度（纳秒）
    DTO_FIELD(Float64, stability_ppm, "stability_ppm");     // 频率稳定度（ppm）
    DTO_FIELD(UInt32, confidence, "confidence");            // 置信度（0-100）
    DTO_FIELD(Boolean, is_traceable, "is_traceable");       // 是否可追溯到UTC
    DTO_FIELD(String, reference, "reference");              // 参考源标识
};

/**
 * @brief 时间源信息DTO
 * 用于传输单个时间源的状态信息
 */
class TimeSourceInfoDto : public oatpp::DTO {
    DTO_INIT(TimeSourceInfoDto, DTO)
    
    DTO_FIELD(String, type, "type");                        // 时间源类型
    DTO_FIELD(String, status, "status");                    // 当前状态
    DTO_FIELD(Object<TimeQualityDto>, quality, "quality");  // 质量指标
    DTO_FIELD(UInt32, priority, "priority");                // 优先级
    DTO_FIELD(String, last_update, "last_update");          // 最后更新时间（ISO格式）
    DTO_FIELD(Fields<String>, properties, "properties");    // 扩展属性
};

/**
 * @brief 系统状态DTO
 * 用于/api/v1/status端点的响应
 */
class SystemStatusDto : public oatpp::DTO {
    DTO_INIT(SystemStatusDto, DTO)
    
    // 系统基本信息
    DTO_FIELD(String, state, "state");                      // 当前时钟状态
    DTO_FIELD(UInt64, uptime_seconds, "uptime_seconds");    // 系统运行时间
    DTO_FIELD(String, version, "version");                  // 软件版本
    DTO_FIELD(String, platform, "platform");               // 运行平台
    DTO_FIELD(Float64, cpu_usage_percent, "cpu_usage_percent");     // CPU使用率
    DTO_FIELD(UInt64, memory_usage_mb, "memory_usage_mb");   // 内存使用量
    DTO_FIELD(String, health, "health");                    // 系统健康状况
    
    // 授时相关信息
    DTO_FIELD(String, active_source, "active_source");      // 当前活跃时间源
    DTO_FIELD(Float64, accuracy_ns, "accuracy_ns");         // 当前精度
    DTO_FIELD(Float64, phase_offset_ns, "phase_offset_ns"); // 相位偏移
    DTO_FIELD(Float64, frequency_offset_ppm, "frequency_offset_ppm"); // 频率偏移
    
    // 时间源列表
    DTO_FIELD(List<Object<TimeSourceInfoDto>>, sources, "sources");
};

/**
 * @brief PTP配置DTO
 * 用于PTP配置的读取和更新
 */
class PtpConfigDto : public oatpp::DTO {
    DTO_INIT(PtpConfigDto, DTO)
    
    DTO_FIELD(UInt32, domain, "domain");                    // PTP域
    DTO_FIELD(UInt32, priority1, "priority1");              // 优先级1
    DTO_FIELD(UInt32, priority2, "priority2");              // 优先级2
    DTO_FIELD(UInt32, clock_class, "clock_class");          // 时钟等级
    DTO_FIELD(UInt32, clock_accuracy, "clock_accuracy");    // 时钟精度
    DTO_FIELD(UInt32, offset_scaled_log_variance, "offset_scaled_log_variance"); // 偏移缩放对数方差
    DTO_FIELD(String, interface, "interface");              // 网络接口
    DTO_FIELD(String, network_transport, "network_transport"); // 网络传输类型
    DTO_FIELD(String, delay_mechanism, "delay_mechanism");  // 延迟机制
    DTO_FIELD(Int32, announce_interval, "announce_interval"); // 公告间隔
    DTO_FIELD(Int32, sync_interval, "sync_interval");       // 同步间隔
    DTO_FIELD(Int32, delay_req_interval, "delay_req_interval"); // 延迟请求间隔
};

/**
 * @brief NTP配置DTO
 * 用于NTP配置的读取和更新
 */
class NtpConfigDto : public oatpp::DTO {
    DTO_INIT(NtpConfigDto, DTO)
    
    DTO_FIELD(UInt32, stratum, "stratum");                  // NTP层级
    DTO_FIELD(String, reference_id, "reference_id");        // 参考源ID
    DTO_FIELD(String, server_address, "server_address");    // 服务器地址
    DTO_FIELD(UInt32, server_port, "server_port");          // 服务器端口
    DTO_FIELD(List<String>, allowed_networks, "allowed_networks"); // 允许的网络
    DTO_FIELD(UInt32, max_clients, "max_clients");          // 最大客户端数
    DTO_FIELD(Int32, poll_interval, "poll_interval");       // 轮询间隔
    DTO_FIELD(Int32, precision, "precision");               // 精度
};

/**
 * @brief 日志条目DTO
 * 用于单个日志条目的传输
 */
class LogEntryDto : public oatpp::DTO {
    DTO_INIT(LogEntryDto, DTO)
    
    DTO_FIELD(String, timestamp, "timestamp");              // 时间戳（ISO格式）
    DTO_FIELD(String, level, "level");                      // 日志级别
    DTO_FIELD(String, component, "component");              // 组件名称
    DTO_FIELD(String, message, "message");                  // 日志消息
    DTO_FIELD(Fields<String>, context, "context");          // 上下文信息
};

/**
 * @brief 分页信息DTO
 * 用于分页查询的元数据
 */
class PaginationDto : public oatpp::DTO {
    DTO_INIT(PaginationDto, DTO)
    
    DTO_FIELD(UInt32, current_page, "current_page");        // 当前页码
    DTO_FIELD(UInt32, total_pages, "total_pages");          // 总页数
    DTO_FIELD(UInt32, total_entries, "total_entries");      // 总条目数
    DTO_FIELD(UInt32, entries_per_page, "entries_per_page"); // 每页条目数
};

/**
 * @brief 日志查询响应DTO
 * 用于/api/v1/logs端点的响应
 */
class LogEntriesDto : public oatpp::DTO {
    DTO_INIT(LogEntriesDto, DTO)
    
    DTO_FIELD(List<Object<LogEntryDto>>, logs, "logs");     // 日志条目列表
    DTO_FIELD(Object<PaginationDto>, pagination, "pagination"); // 分页信息
};

/**
 * @brief 健康状态DTO
 * 用于/api/v1/health端点的响应
 */
class HealthStatusDto : public oatpp::DTO {
    DTO_INIT(HealthStatusDto, DTO)
    
    DTO_FIELD(String, status, "status");                    // 整体健康状态
    DTO_FIELD(String, timestamp, "timestamp");              // 检查时间戳
    DTO_FIELD(Fields<String>, components, "components");     // 各组件状态
    DTO_FIELD(Float64, uptime_seconds, "uptime_seconds");   // 运行时间
};

/**
 * @brief 性能指标DTO
 * 用于/api/v1/metrics端点的响应
 */
class MetricsDto : public oatpp::DTO {
    DTO_INIT(MetricsDto, DTO)
    
    // 时间精度指标
    DTO_FIELD(Float64, current_accuracy_ns, "current_accuracy_ns");
    DTO_FIELD(Float64, average_accuracy_ns, "average_accuracy_ns");
    DTO_FIELD(Float64, max_accuracy_ns, "max_accuracy_ns");
    
    // 频率稳定度指标
    DTO_FIELD(Float64, allan_deviation_1s, "allan_deviation_1s");
    DTO_FIELD(Float64, allan_deviation_10s, "allan_deviation_10s");
    DTO_FIELD(Float64, allan_deviation_100s, "allan_deviation_100s");
    
    // 系统性能指标
    DTO_FIELD(UInt64, state_transitions, "state_transitions");
    DTO_FIELD(UInt64, error_count, "error_count");
    DTO_FIELD(UInt64, packets_processed, "packets_processed");
    DTO_FIELD(Float64, average_response_time_ms, "average_response_time_ms");
    
    DTO_FIELD(String, start_time, "start_time");            // 统计开始时间
    DTO_FIELD(String, end_time, "end_time");                // 统计结束时间
};

/**
 * @brief 标准错误响应DTO
 * 用于所有API错误响应的统一格式
 */
class ErrorResponseDto : public oatpp::DTO {
    DTO_INIT(ErrorResponseDto, DTO)
    
    DTO_FIELD(String, code, "code");                        // 错误代码
    DTO_FIELD(String, message, "message");                  // 错误消息
    DTO_FIELD(Fields<String>, details, "details");          // 错误详情
    DTO_FIELD(String, timestamp, "timestamp");              // 错误时间戳
    DTO_FIELD(String, request_id, "request_id");            // 请求ID
};

/**
 * @brief 操作结果DTO
 * 用于操作成功的响应
 */
class OperationResultDto : public oatpp::DTO {
    DTO_INIT(OperationResultDto, DTO)
    
    DTO_FIELD(Boolean, success, "success");                 // 操作是否成功
    DTO_FIELD(String, message, "message");                  // 结果消息
    DTO_FIELD(String, timestamp, "timestamp");              // 操作时间戳
    DTO_FIELD(Fields<String>, data, "data");                // 附加数据
};

/**
 * @brief 登录请求DTO
 * 用于用户身份认证请求
 */
class LoginRequestDto : public oatpp::DTO {
    DTO_INIT(LoginRequestDto, DTO)
    
    DTO_FIELD(String, username, "username");                // 用户名
    DTO_FIELD(String, password, "password");                // 密码
    DTO_FIELD(Boolean, remember_me, "remember_me");         // 记住我选项
};

/**
 * @brief 用户信息DTO
 * 用于传输用户基本信息
 */
class UserInfoDto : public oatpp::DTO {
    DTO_INIT(UserInfoDto, DTO)
    
    DTO_FIELD(String, username, "username");                // 用户名
    DTO_FIELD(String, role, "role");                        // 用户角色
    DTO_FIELD(List<String>, permissions, "permissions");    // 权限列表
    DTO_FIELD(String, created_at, "created_at");            // 创建时间
    DTO_FIELD(String, last_login, "last_login");            // 最后登录时间
    DTO_FIELD(Boolean, is_active, "is_active");             // 是否激活
};

/**
 * @brief 认证响应DTO
 * 用于登录成功后的响应
 */
class AuthResponseDto : public oatpp::DTO {
    DTO_INIT(AuthResponseDto, DTO)
    
    DTO_FIELD(String, access_token, "access_token");        // 访问令牌
    DTO_FIELD(String, refresh_token, "refresh_token");      // 刷新令牌
    DTO_FIELD(String, token_type, "token_type");            // 令牌类型
    DTO_FIELD(UInt32, expires_in, "expires_in");            // 过期时间（秒）
    DTO_FIELD(Object<UserInfoDto>, user, "user");           // 用户信息
};

/**
 * @brief 刷新令牌请求DTO
 * 用于令牌刷新请求
 */
class RefreshTokenDto : public oatpp::DTO {
    DTO_INIT(RefreshTokenDto, DTO)
    
    DTO_FIELD(String, refresh_token, "refresh_token");      // 刷新令牌
};

/**
 * @brief 用户创建请求DTO
 * 用于创建新用户
 */
class CreateUserRequestDto : public oatpp::DTO {
    DTO_INIT(CreateUserRequestDto, DTO)
    
    DTO_FIELD(String, username, "username");                // 用户名
    DTO_FIELD(String, password, "password");                // 密码
    DTO_FIELD(String, role, "role");                        // 用户角色
    DTO_FIELD(Boolean, is_active, "is_active");             // 是否激活
};

/**
 * @brief 用户更新请求DTO
 * 用于更新用户信息
 */
class UpdateUserRequestDto : public oatpp::DTO {
    DTO_INIT(UpdateUserRequestDto, DTO)
    
    DTO_FIELD(String, password, "password");                // 新密码（可选）
    DTO_FIELD(String, role, "role");                        // 新角色（可选）
    DTO_FIELD(Boolean, is_active, "is_active");             // 激活状态（可选）
};

/**
 * @brief 用户列表响应DTO
 * 用于获取用户列表
 */
class UserListDto : public oatpp::DTO {
    DTO_INIT(UserListDto, DTO)
    
    DTO_FIELD(List<Object<UserInfoDto>>, users, "users");   // 用户列表
    DTO_FIELD(Object<PaginationDto>, pagination, "pagination"); // 分页信息
};

/**
 * @brief 审计日志条目DTO
 * 用于安全审计日志
 */
class AuditLogEntryDto : public oatpp::DTO {
    DTO_INIT(AuditLogEntryDto, DTO)
    
    DTO_FIELD(String, timestamp, "timestamp");              // 时间戳
    DTO_FIELD(String, username, "username");                // 用户名
    DTO_FIELD(String, action, "action");                    // 操作类型
    DTO_FIELD(String, resource, "resource");                // 资源路径
    DTO_FIELD(String, ip_address, "ip_address");            // IP地址
    DTO_FIELD(String, user_agent, "user_agent");            // 用户代理
    DTO_FIELD(Boolean, success, "success");                 // 操作是否成功
    DTO_FIELD(String, details, "details");                  // 详细信息
};

/**
 * @brief 审计日志响应DTO
 * 用于审计日志查询响应
 */
class AuditLogDto : public oatpp::DTO {
    DTO_INIT(AuditLogDto, DTO)
    
    DTO_FIELD(List<Object<AuditLogEntryDto>>, logs, "logs"); // 审计日志列表
    DTO_FIELD(Object<PaginationDto>, pagination, "pagination"); // 分页信息
};

} // namespace api
} // namespace timing_server

#include OATPP_CODEGEN_END(DTO)