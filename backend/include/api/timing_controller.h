#pragma once

#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "api/dto.h"
#include <core/types.h>
#include <memory>

#include OATPP_CODEGEN_BEGIN(ApiController)

namespace timing_server {
namespace api {

// 前向声明
class TimingService;

/**
 * @brief 授时系统REST API控制器
 * 实现完整的RESTful API端点，提供系统状态查询、配置管理等功能
 */
class TimingController : public oatpp::web::server::api::ApiController {
public:
    /**
     * @brief 构造函数
     * @param objectMapper JSON对象映射器
     * @param timing_service 授时服务实例
     */
    TimingController(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
                    std::shared_ptr<TimingService> timing_service);

    static std::shared_ptr<TimingController> createShared(
        OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
        std::shared_ptr<TimingService> timing_service);

    /**
     * @brief 获取系统完整状态信息
     * GET /api/v1/status
     * @return 系统状态DTO
     */
    ENDPOINT("GET", "/api/v1/status", getSystemStatus) {
        return createDtoResponse(Status::CODE_200, getSystemStatusImpl());
    }

    /**
     * @brief 获取当前PTP配置
     * GET /api/v1/config/ptp
     * @return PTP配置DTO
     */
    ENDPOINT("GET", "/api/v1/config/ptp", getPtpConfig) {
        return createDtoResponse(Status::CODE_200, getPtpConfigImpl());
    }

    /**
     * @brief 更新PTP配置
     * PUT /api/v1/config/ptp
     * @param dto PTP配置DTO
     * @return 更新后的PTP配置DTO
     */
    ENDPOINT("PUT", "/api/v1/config/ptp", updatePtpConfig,
             BODY_DTO(Object<PtpConfigDto>, dto)) {
        auto result = updatePtpConfigImpl(dto);
        if (result) {
            return createDtoResponse(Status::CODE_200, result);
        }
        return createErrorResponse(Status::CODE_400, "INVALID_CONFIG", "PTP配置更新失败");
    }

    /**
     * @brief 获取当前NTP配置
     * GET /api/v1/config/ntp
     * @return NTP配置DTO
     */
    ENDPOINT("GET", "/api/v1/config/ntp", getNtpConfig) {
        return createDtoResponse(Status::CODE_200, getNtpConfigImpl());
    }

    /**
     * @brief 更新NTP配置
     * PUT /api/v1/config/ntp
     * @param dto NTP配置DTO
     * @return 更新后的NTP配置DTO
     */
    ENDPOINT("PUT", "/api/v1/config/ntp", updateNtpConfig,
             BODY_DTO(Object<NtpConfigDto>, dto)) {
        auto result = updateNtpConfigImpl(dto);
        if (result) {
            return createDtoResponse(Status::CODE_200, result);
        }
        return createErrorResponse(Status::CODE_400, "INVALID_CONFIG", "NTP配置更新失败");
    }

    /**
     * @brief 获取系统日志
     * GET /api/v1/logs?level=INFO&page=1&limit=100&from=2024-01-01&to=2024-01-02
     * @param level 日志级别过滤
     * @param page 页码
     * @param limit 每页条目数
     * @param from 开始时间
     * @param to 结束时间
     * @return 日志条目列表DTO
     */
    ENDPOINT("GET", "/api/v1/logs", getLogs,
             QUERY(String, level, "level"),
             QUERY(UInt32, page, "page"),
             QUERY(UInt32, limit, "limit"),
             QUERY(String, from, "from"),
             QUERY(String, to, "to")) {
        return createDtoResponse(Status::CODE_200, 
                               getLogsImpl(level, page, limit, from, to));
    }

    /**
     * @brief 系统健康检查
     * GET /api/v1/health
     * @return 健康状态DTO
     */
    ENDPOINT("GET", "/api/v1/health", getHealth) {
        return createDtoResponse(Status::CODE_200, getHealthImpl());
    }

    /**
     * @brief 重启系统服务
     * POST /api/v1/system/restart
     * @return 操作结果DTO
     */
    ENDPOINT("POST", "/api/v1/system/restart", restartSystem) {
        auto result = restartSystemImpl();
        if (result->success) {
            return createDtoResponse(Status::CODE_202, result);
        }
        return createErrorResponse(Status::CODE_500, "RESTART_FAILED", "系统重启失败");
    }

    /**
     * @brief 获取性能指标
     * GET /api/v1/metrics?from=2024-01-01T00:00:00Z&to=2024-01-01T23:59:59Z
     * @param from 开始时间（ISO格式）
     * @param to 结束时间（ISO格式）
     * @return 性能指标DTO
     */
    ENDPOINT("GET", "/api/v1/metrics", getMetrics,
             QUERY(String, from, "from"),
             QUERY(String, to, "to")) {
        return createDtoResponse(Status::CODE_200, getMetricsImpl(from, to));
    }

    /**
     * @brief 验证配置
     * POST /api/v1/config/validate
     * @param config_type 配置类型（ptp/ntp）
     * @param dto 配置数据
     * @return 验证结果DTO
     */
    ENDPOINT("POST", "/api/v1/config/validate", validateConfig,
             QUERY(String, config_type, "type"),
             BODY_STRING(String, dto)) {
        auto result = validateConfigImpl(config_type, dto);
        if (result->success) {
            return createDtoResponse(Status::CODE_200, result);
        }
        return createErrorResponse(Status::CODE_422, "VALIDATION_FAILED", "配置验证失败");
    }

    /**
     * @brief 获取配置模式
     * GET /api/v1/config/schema?type=ptp
     * @param config_type 配置类型
     * @return JSON模式字符串
     */
    ENDPOINT("GET", "/api/v1/config/schema", getConfigSchema,
             QUERY(String, config_type, "type")) {
        auto schema = getConfigSchemaImpl(config_type);
        if (schema && !schema->empty()) {
            return createResponse(Status::CODE_200, *schema);
        }
        return createErrorResponse(Status::CODE_404, "SCHEMA_NOT_FOUND", "配置模式未找到");
    }

private:
    std::shared_ptr<TimingService> m_timing_service;

    // 实现方法声明
    oatpp::Object<SystemStatusDto> getSystemStatusImpl();
    oatpp::Object<PtpConfigDto> getPtpConfigImpl();
    oatpp::Object<PtpConfigDto> updatePtpConfigImpl(const oatpp::Object<PtpConfigDto>& dto);
    oatpp::Object<NtpConfigDto> getNtpConfigImpl();
    oatpp::Object<NtpConfigDto> updateNtpConfigImpl(const oatpp::Object<NtpConfigDto>& dto);
    oatpp::Object<LogEntriesDto> getLogsImpl(const oatpp::String& level,
                                            const oatpp::UInt32& page,
                                            const oatpp::UInt32& limit,
                                            const oatpp::String& from,
                                            const oatpp::String& to);
    oatpp::Object<HealthStatusDto> getHealthImpl();
    oatpp::Object<OperationResultDto> restartSystemImpl();
    oatpp::Object<MetricsDto> getMetricsImpl(const oatpp::String& from,
                                            const oatpp::String& to);
    oatpp::Object<OperationResultDto> validateConfigImpl(const oatpp::String& config_type,
                                                        const oatpp::String& dto);
    std::shared_ptr<oatpp::String> getConfigSchemaImpl(const oatpp::String& config_type);

    // 辅助方法
    std::shared_ptr<OutgoingResponse> createErrorResponse(const Status& status,
                                                        const std::string& code,
                                                        const std::string& message);
    
    std::string generateRequestId();
};

} // namespace api
} // namespace timing_server

#include OATPP_CODEGEN_END(ApiController)