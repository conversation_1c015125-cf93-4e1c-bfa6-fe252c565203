#pragma once

#include <memory>
#include <atomic>
#include <string>
#include <api/timing_service.h>

namespace timing_server {
namespace api {

/**
 * @brief REST API服务器类
 * 提供HTTP REST API服务，支持授时系统的各种操作
 */
class RestServer {
public:
    /**
     * @brief 构造函数
     * @param port HTTP服务器端口
     * @param timing_service 授时服务实例
     */
    RestServer(uint16_t port, std::shared_ptr<TimingService> timing_service = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~RestServer();
    
    /**
     * @brief 启动REST服务器
     * @return 是否启动成功
     */
    bool start();
    
    /**
     * @brief 停止REST服务器
     */
    void stop();
    
    /**
     * @brief 检查服务器是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const;
    
    /**
     * @brief 服务器统计信息
     */
    struct ServerStats {
        uint64_t total_requests = 0;
        uint64_t successful_requests = 0;
        uint64_t failed_requests = 0;
        double average_response_time_ms = 0.0;
        uint64_t active_connections = 0;
    };
    
    /**
     * @brief 获取服务器统计信息
     * @return 服务器统计信息
     */
    ServerStats getStats() const;

private:
    uint16_t m_port;
    std::shared_ptr<TimingService> m_timing_service;
    std::atomic<bool> m_running{false};
};

} // namespace api
} // namespace timing_server 