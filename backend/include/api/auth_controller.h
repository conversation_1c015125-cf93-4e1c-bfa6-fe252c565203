#pragma once

#include "api/auth_manager.h"
#include <memory>

#ifdef USE_OATPP_PLACEHOLDER
// 占位实现的简化声明
#else
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/macro/component.hpp"
#include "api/dto.h"
#include OATPP_CODEGEN_BEGIN(ApiController)
#endif

namespace timing_server {
namespace api {

#ifdef USE_OATPP_PLACEHOLDER

/**
 * @brief 认证控制器（占位实现）
 * 当oatpp不可用时的简化实现
 */
class AuthController {
public:
    AuthController(std::shared_ptr<void> objectMapper,
                   std::shared_ptr<AuthManager> auth_manager);
    
    static std::shared_ptr<AuthController> createShared(
        std::shared_ptr<void> objectMapper,
        std::shared_ptr<AuthManager> auth_manager);
    
    std::string handleLogin(const std::string& username, const std::string& password);
    bool validateRequest(const std::string& token);

private:
    std::shared_ptr<AuthManager> m_auth_manager;
};

#else

/**
 * @brief 认证控制器
 * 处理用户认证、授权和用户管理相关的API端点
 */
class AuthController : public oatpp::web::server::api::ApiController {
public:
    /**
     * @brief 构造函数
     * @param objectMapper JSON对象映射器
     * @param auth_manager 认证管理器实例
     */
    AuthController(OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
                   std::shared_ptr<AuthManager> auth_manager);

    static std::shared_ptr<AuthController> createShared(
        OATPP_COMPONENT(std::shared_ptr<ObjectMapper>, objectMapper),
        std::shared_ptr<AuthManager> auth_manager);

    /**
     * @brief 用户登录
     * POST /api/v1/auth/login
     * @param dto 登录请求DTO
     * @param request HTTP请求对象（用于获取IP地址和User-Agent）
     * @return 认证响应DTO
     */
    ENDPOINT("POST", "/api/v1/auth/login", login,
             BODY_DTO(Object<LoginRequestDto>, dto),
             REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        return loginImpl(dto, request);
    }

    /**
     * @brief 刷新访问令牌
     * POST /api/v1/auth/refresh
     * @param dto 刷新令牌请求DTO
     * @param request HTTP请求对象
     * @return 认证响应DTO
     */
    ENDPOINT("POST", "/api/v1/auth/refresh", refreshToken,
             BODY_DTO(Object<RefreshTokenDto>, dto),
             REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        return refreshTokenImpl(dto, request);
    }

    /**
     * @brief 用户登出
     * POST /api/v1/auth/logout
     * @param authorization 授权头
     * @param request HTTP请求对象
     * @return 操作结果DTO
     */
    ENDPOINT("POST", "/api/v1/auth/logout", logout,
             HEADER(String, authorization, "Authorization"),
             REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        return logoutImpl(authorization, request);
    }

    /**
     * @brief 获取当前用户信息
     * GET /api/v1/auth/me
     * @param authorization 授权头
     * @return 用户信息DTO
     */
    ENDPOINT("GET", "/api/v1/auth/me", getCurrentUser,
             HEADER(String, authorization, "Authorization")) {
        return getCurrentUserImpl(authorization);
    }

    /**
     * @brief 创建新用户（需要管理员权限）
     * POST /api/v1/users
     * @param dto 创建用户请求DTO
     * @param authorization 授权头
     * @param request HTTP请求对象
     * @return 用户信息DTO
     */
    ENDPOINT("POST", "/api/v1/users", createUser,
             BODY_DTO(Object<CreateUserRequestDto>, dto),
             HEADER(String, authorization, "Authorization"),
             REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        return createUserImpl(dto, authorization, request);
    }

    /**
     * @brief 获取用户列表（需要管理员权限）
     * GET /api/v1/users?page=1&limit=20
     * @param authorization 授权头
     * @param page 页码
     * @param limit 每页数量
     * @return 用户列表DTO
     */
    ENDPOINT("GET", "/api/v1/users", getUserList,
             HEADER(String, authorization, "Authorization"),
             QUERY(UInt32, page, "page"),
             QUERY(UInt32, limit, "limit")) {
        return getUserListImpl(authorization, page, limit);
    }

    /**
     * @brief 获取指定用户信息（需要管理员权限）
     * GET /api/v1/users/{username}
     * @param username 用户名
     * @param authorization 授权头
     * @return 用户信息DTO
     */
    ENDPOINT("GET", "/api/v1/users/{username}", getUser,
             PATH(String, username),
             HEADER(String, authorization, "Authorization")) {
        return getUserImpl(username, authorization);
    }

    /**
     * @brief 更新用户信息（需要管理员权限）
     * PUT /api/v1/users/{username}
     * @param username 用户名
     * @param dto 更新用户请求DTO
     * @param authorization 授权头
     * @param request HTTP请求对象
     * @return 用户信息DTO
     */
    ENDPOINT("PUT", "/api/v1/users/{username}", updateUser,
             PATH(String, username),
             BODY_DTO(Object<UpdateUserRequestDto>, dto),
             HEADER(String, authorization, "Authorization"),
             REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        return updateUserImpl(username, dto, authorization, request);
    }

    /**
     * @brief 删除用户（需要管理员权限）
     * DELETE /api/v1/users/{username}
     * @param username 用户名
     * @param authorization 授权头
     * @param request HTTP请求对象
     * @return 操作结果DTO
     */
    ENDPOINT("DELETE", "/api/v1/users/{username}", deleteUser,
             PATH(String, username),
             HEADER(String, authorization, "Authorization"),
             REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        return deleteUserImpl(username, authorization, request);
    }

    /**
     * @brief 获取审计日志（需要管理员权限）
     * GET /api/v1/audit?page=1&limit=100&username=&action=&from=&to=
     * @param authorization 授权头
     * @param page 页码
     * @param limit 每页数量
     * @param username 用户名过滤
     * @param action 操作类型过滤
     * @param from 开始时间
     * @param to 结束时间
     * @return 审计日志DTO
     */
    ENDPOINT("GET", "/api/v1/audit", getAuditLogs,
             HEADER(String, authorization, "Authorization"),
             QUERY(UInt32, page, "page"),
             QUERY(UInt32, limit, "limit"),
             QUERY(String, username, "username"),
             QUERY(String, action, "action"),
             QUERY(String, from, "from"),
             QUERY(String, to, "to")) {
        return getAuditLogsImpl(authorization, page, limit, username, action, from, to);
    }

    /**
     * @brief 获取活跃会话列表（需要管理员权限）
     * GET /api/v1/sessions
     * @param authorization 授权头
     * @return 会话信息列表
     */
    ENDPOINT("GET", "/api/v1/sessions", getActiveSessions,
             HEADER(String, authorization, "Authorization")) {
        return getActiveSessionsImpl(authorization);
    }

    /**
     * @brief 终止用户会话（需要管理员权限）
     * DELETE /api/v1/sessions/{username}
     * @param username 用户名
     * @param authorization 授权头
     * @param request HTTP请求对象
     * @return 操作结果DTO
     */
    ENDPOINT("DELETE", "/api/v1/sessions/{username}", terminateUserSessions,
             PATH(String, username),
             HEADER(String, authorization, "Authorization"),
             REQUEST(std::shared_ptr<IncomingRequest>, request)) {
        return terminateUserSessionsImpl(username, authorization, request);
    }

private:
    std::shared_ptr<AuthManager> m_auth_manager;

    // 实现方法声明
    std::shared_ptr<OutgoingResponse> loginImpl(const oatpp::Object<LoginRequestDto>& dto,
                                               const std::shared_ptr<IncomingRequest>& request);
    
    std::shared_ptr<OutgoingResponse> refreshTokenImpl(const oatpp::Object<RefreshTokenDto>& dto,
                                                      const std::shared_ptr<IncomingRequest>& request);
    
    std::shared_ptr<OutgoingResponse> logoutImpl(const oatpp::String& authorization,
                                                const std::shared_ptr<IncomingRequest>& request);
    
    std::shared_ptr<OutgoingResponse> getCurrentUserImpl(const oatpp::String& authorization);
    
    std::shared_ptr<OutgoingResponse> createUserImpl(const oatpp::Object<CreateUserRequestDto>& dto,
                                                    const oatpp::String& authorization,
                                                    const std::shared_ptr<IncomingRequest>& request);
    
    std::shared_ptr<OutgoingResponse> getUserListImpl(const oatpp::String& authorization,
                                                     const oatpp::UInt32& page,
                                                     const oatpp::UInt32& limit);
    
    std::shared_ptr<OutgoingResponse> getUserImpl(const oatpp::String& username,
                                                 const oatpp::String& authorization);
    
    std::shared_ptr<OutgoingResponse> updateUserImpl(const oatpp::String& username,
                                                    const oatpp::Object<UpdateUserRequestDto>& dto,
                                                    const oatpp::String& authorization,
                                                    const std::shared_ptr<IncomingRequest>& request);
    
    std::shared_ptr<OutgoingResponse> deleteUserImpl(const oatpp::String& username,
                                                    const oatpp::String& authorization,
                                                    const std::shared_ptr<IncomingRequest>& request);
    
    std::shared_ptr<OutgoingResponse> getAuditLogsImpl(const oatpp::String& authorization,
                                                      const oatpp::UInt32& page,
                                                      const oatpp::UInt32& limit,
                                                      const oatpp::String& username,
                                                      const oatpp::String& action,
                                                      const oatpp::String& from,
                                                      const oatpp::String& to);
    
    std::shared_ptr<OutgoingResponse> getActiveSessionsImpl(const oatpp::String& authorization);
    
    std::shared_ptr<OutgoingResponse> terminateUserSessionsImpl(const oatpp::String& username,
                                                               const oatpp::String& authorization,
                                                               const std::shared_ptr<IncomingRequest>& request);

    // 辅助方法
    std::string extractToken(const oatpp::String& authorization);
    std::string getClientIpAddress(const std::shared_ptr<IncomingRequest>& request);
    std::string getUserAgent(const std::shared_ptr<IncomingRequest>& request);
    std::shared_ptr<OutgoingResponse> createUnauthorizedResponse(const std::string& message = "未授权访问");
    std::shared_ptr<OutgoingResponse> createForbiddenResponse(const std::string& message = "权限不足");
    std::shared_ptr<OutgoingResponse> createErrorResponse(const Status& status,
                                                        const std::string& code,
                                                        const std::string& message);
    oatpp::Object<UserInfoDto> convertUserInfo(const UserInfo& user);
    uint64_t parseTimestamp(const std::string& time_str);
};

#endif // USE_OATPP_PLACEHOLDER

} // namespace api
} // namespace timing_server

#ifdef USE_OATPP_PLACEHOLDER
// 占位实现不需要oatpp codegen
#else
#include OATPP_CODEGEN_END(ApiController)
#endif