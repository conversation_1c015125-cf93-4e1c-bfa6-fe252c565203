#pragma once

#include <api/websocket_server.h>
#include <core/logger.h>
#include <memory>

namespace timing_server {
namespace api {

/**
 * @brief WebSocket事件监听器实现
 * 处理WebSocket连接事件并与系统其他组件集成
 */
class WebSocketEventListener : public IWebSocketEventListener {
public:
    /**
     * @brief 构造函数
     */
    WebSocketEventListener();

    /**
     * @brief 析构函数
     */
    ~WebSocketEventListener() override = default;

    /**
     * @brief 客户端连接事件
     * @param connection_id 连接ID
     * @param remote_address 客户端地址
     */
    void onClientConnected(uint64_t connection_id, const std::string& remote_address) override;

    /**
     * @brief 客户端断开事件
     * @param connection_id 连接ID
     * @param reason 断开原因
     */
    void onClientDisconnected(uint64_t connection_id, const std::string& reason) override;

    /**
     * @brief 客户端认证事件
     * @param connection_id 连接ID
     * @param user_id 用户ID
     * @param success 认证是否成功
     */
    void onClientAuthenticated(uint64_t connection_id, const std::string& user_id, bool success) override;

    /**
     * @brief 消息接收事件
     * @param connection_id 连接ID
     * @param message 消息内容
     */
    void onMessageReceived(uint64_t connection_id, const WebSocketMessage& message) override;

    /**
     * @brief 获取连接统计信息
     * @return 连接统计信息
     */
    struct ConnectionStats {
        uint64_t total_connections = 0;
        uint64_t successful_authentications = 0;
        uint64_t failed_authentications = 0;
        uint64_t messages_processed = 0;
    };

    ConnectionStats getConnectionStats() const;

private:
    mutable std::mutex m_stats_mutex;
    ConnectionStats m_stats;

    /**
     * @brief 记录安全事件
     * @param event_type 事件类型
     * @param connection_id 连接ID
     * @param details 事件详情
     */
    void logSecurityEvent(const std::string& event_type, uint64_t connection_id, const std::string& details);
};

} // namespace api
} // namespace timing_server