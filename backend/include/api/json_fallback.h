#pragma once

// 简化的JSON处理实现
// 为了简化依赖管理，总是使用内置实现

#include <string>
#include <map>
#include <vector>
#include <sstream>

namespace nlohmann {

/**
 * @brief 简化的JSON类实现
 * 仅提供WebSocket服务器所需的基本功能
 */
class json {
public:
    enum class value_t {
        null,
        object,
        array,
        string,
        number_integer,
        number_float,
        boolean
    };

    json() : type_(value_t::null) {}
    json(const std::string& str) : type_(value_t::string), string_value_(str) {}
    json(const char* str) : type_(value_t::string), string_value_(str) {}
    json(int val) : type_(value_t::number_integer), int_value_(val) {}
    json(uint32_t val) : type_(value_t::number_integer), int_value_(static_cast<int>(val)) {}
    json(uint64_t val) : type_(value_t::number_integer), int_value_(static_cast<int>(val)) {}
    json(long long val) : type_(value_t::number_integer), int_value_(static_cast<int>(val)) {}
    json(double val) : type_(value_t::number_float), float_value_(val) {}
    json(bool val) : type_(value_t::boolean), bool_value_(val) {}

    // 赋值操作符
    json& operator=(const std::string& str) {
        type_ = value_t::string;
        string_value_ = str;
        return *this;
    }
    
    json& operator=(int val) {
        type_ = value_t::number_integer;
        int_value_ = val;
        return *this;
    }
    
    json& operator=(uint32_t val) {
        type_ = value_t::number_integer;
        int_value_ = static_cast<int>(val);
        return *this;
    }
    
    json& operator=(uint64_t val) {
        type_ = value_t::number_integer;
        int_value_ = static_cast<int>(val);
        return *this;
    }
    
    json& operator=(long long val) {
        type_ = value_t::number_integer;
        int_value_ = static_cast<int>(val);
        return *this;
    }
    
    json& operator=(double val) {
        type_ = value_t::number_float;
        float_value_ = val;
        return *this;
    }
    
    json& operator=(bool val) {
        type_ = value_t::boolean;
        bool_value_ = val;
        return *this;
    }

    // 对象操作
    json& operator[](const std::string& key) {
        if (type_ != value_t::object) {
            type_ = value_t::object;
            object_value_.clear();
        }
        return object_value_[key];
    }
    
    const json& operator[](const std::string& key) const {
        static json null_json;
        if (type_ != value_t::object) {
            return null_json;
        }
        auto it = object_value_.find(key);
        if (it == object_value_.end()) {
            return null_json;
        }
        return it->second;
    }

    // 数组操作
    void push_back(const json& item) {
        if (type_ != value_t::array) {
            type_ = value_t::array;
            array_value_.clear();
        }
        array_value_.push_back(item);
    }

    // 类型检查
    bool is_string() const { return type_ == value_t::string; }
    bool is_number() const { return type_ == value_t::number_integer || type_ == value_t::number_float; }
    bool is_boolean() const { return type_ == value_t::boolean; }
    bool is_object() const { return type_ == value_t::object; }
    bool is_array() const { return type_ == value_t::array; }

    // 值获取
    std::string get_string() const { return string_value_; }
    int get_int() const { return int_value_; }
    double get_double() const { return float_value_; }
    bool get_bool() const { return bool_value_; }

    // 特化的值获取方法
    std::string value(const std::string& key, const std::string& default_value) const {
        if (type_ != value_t::object) return default_value;
        auto it = object_value_.find(key);
        if (it == object_value_.end()) return default_value;
        return it->second.is_string() ? it->second.get_string() : default_value;
    }
    
    int value(const std::string& key, int default_value) const {
        if (type_ != value_t::object) return default_value;
        auto it = object_value_.find(key);
        if (it == object_value_.end()) return default_value;
        return it->second.is_number() ? it->second.get_int() : default_value;
    }
    
    std::vector<std::string> value(const std::string& key, const std::vector<std::string>& default_value) const {
        if (type_ != value_t::object) return default_value;
        auto it = object_value_.find(key);
        if (it == object_value_.end()) return default_value;
        
        std::vector<std::string> result;
        if (it->second.is_array()) {
            for (const auto& item : it->second.array_value_) {
                if (item.is_string()) {
                    result.push_back(item.get_string());
                }
            }
        }
        return result.empty() ? default_value : result;
    }

    // 检查是否包含键
    bool contains(const std::string& key) const {
        return type_ == value_t::object && object_value_.find(key) != object_value_.end();
    }

    // 删除键
    void erase(const std::string& key) {
        if (type_ == value_t::object) {
            object_value_.erase(key);
        }
    }

    // 迭代器支持
    auto items() const {
        return object_value_;
    }

    // 序列化为字符串
    std::string dump() const {
        std::ostringstream oss;
        serialize(oss);
        return oss.str();
    }

    // 静态解析函数
    static json parse(const std::string& str) {
        // 简化的JSON解析实现
        json result;
        if (str.empty() || str == "null") {
            return result;
        }
        
        // 简单的字符串检测
        if (str.front() == '"' && str.back() == '"') {
            result.type_ = value_t::string;
            result.string_value_ = str.substr(1, str.length() - 2);
        } else if (str == "true") {
            result.type_ = value_t::boolean;
            result.bool_value_ = true;
        } else if (str == "false") {
            result.type_ = value_t::boolean;
            result.bool_value_ = false;
        } else if (str.front() == '{') {
            result.type_ = value_t::object;
            // 简化的对象解析（实际项目中需要完整的JSON解析器）
        } else if (str.front() == '[') {
            result.type_ = value_t::array;
            // 简化的数组解析
        } else {
            // 尝试解析为数字
            try {
                if (str.find('.') != std::string::npos) {
                    result.type_ = value_t::number_float;
                    result.float_value_ = std::stod(str);
                } else {
                    result.type_ = value_t::number_integer;
                    result.int_value_ = std::stoi(str);
                }
            } catch (...) {
                // 解析失败，作为字符串处理
                result.type_ = value_t::string;
                result.string_value_ = str;
            }
        }
        
        return result;
    }

    // 静态数组创建
    static json array() {
        json result;
        result.type_ = value_t::array;
        return result;
    }

private:
    value_t type_;
    std::string string_value_;
    int int_value_ = 0;
    double float_value_ = 0.0;
    bool bool_value_ = false;
    std::map<std::string, json> object_value_;
    std::vector<json> array_value_;

    void serialize(std::ostringstream& oss) const {
        switch (type_) {
            case value_t::null:
                oss << "null";
                break;
            case value_t::string:
                oss << "\"" << string_value_ << "\"";
                break;
            case value_t::number_integer:
                oss << int_value_;
                break;
            case value_t::number_float:
                oss << float_value_;
                break;
            case value_t::boolean:
                oss << (bool_value_ ? "true" : "false");
                break;
            case value_t::object: {
                oss << "{";
                bool first = true;
                for (const auto& [key, value] : object_value_) {
                    if (!first) oss << ",";
                    oss << "\"" << key << "\":";
                    value.serialize(oss);
                    first = false;
                }
                oss << "}";
                break;
            }
            case value_t::array:
                oss << "[";
                for (size_t i = 0; i < array_value_.size(); ++i) {
                    if (i > 0) oss << ",";
                    array_value_[i].serialize(oss);
                }
                oss << "]";
                break;
        }
    }
};

} // namespace nlohmann

