#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <mutex>
#include <chrono>
#include <atomic>
#include <thread>
#include <core/types.h>

namespace timing_server {
namespace api {

/**
 * @brief 安全事件类型枚举
 * 定义系统中的安全事件类型
 */
enum class SecurityEventType {
    RATE_LIMIT_EXCEEDED,        // 访问频率超限
    IP_BLOCKED,                 // IP地址被阻断
    INVALID_TOKEN,              // 无效令牌
    AUTHENTICATION_FAILED,      // 认证失败
    AUTHORIZATION_DENIED,       // 授权拒绝
    SUSPICIOUS_ACTIVITY,        // 可疑活动
    BRUTE_FORCE_ATTACK,        // 暴力破解攻击
    UNKNOWN_USER_AGENT,        // 未知用户代理
    MALFORMED_REQUEST          // 恶意请求
};

/**
 * @brief 安全事件结构
 * 记录安全相关事件的详细信息
 */
struct SecurityEvent {
    uint64_t timestamp;             // 事件时间戳
    SecurityEventType type;         // 事件类型
    std::string ip_address;         // 源IP地址
    std::string user_agent;         // 用户代理
    std::string username;           // 用户名（如果有）
    std::string resource;           // 访问的资源
    std::string details;            // 详细信息
    uint32_t severity;              // 严重程度（1-10）
};

/**
 * @brief IP访问统计结构
 * 记录IP地址的访问统计信息
 */
struct IpAccessStats {
    std::string ip_address;         // IP地址
    uint32_t request_count;         // 请求次数
    uint32_t failed_attempts;      // 失败尝试次数
    uint64_t first_seen;           // 首次访问时间
    uint64_t last_seen;            // 最后访问时间
    uint64_t blocked_until;        // 阻断截止时间（0表示未阻断）
    bool is_whitelisted;           // 是否在白名单中
    bool is_blacklisted;           // 是否在黑名单中
};

/**
 * @brief 速率限制配置
 * 定义访问频率限制的参数
 */
struct RateLimitConfig {
    uint32_t max_requests;          // 最大请求数
    uint32_t time_window_seconds;   // 时间窗口（秒）
    uint32_t burst_limit;           // 突发限制
    uint32_t block_duration_seconds; // 阻断持续时间（秒）
    bool enable_progressive_delay;   // 是否启用渐进延迟
};

/**
 * @brief TLS配置结构
 * 定义HTTPS/TLS相关配置
 */
struct TlsConfig {
    std::string cert_file;          // 证书文件路径
    std::string key_file;           // 私钥文件路径
    std::string ca_file;            // CA证书文件路径
    std::vector<std::string> cipher_suites; // 支持的密码套件
    std::string min_tls_version;    // 最小TLS版本
    bool require_client_cert;       // 是否需要客户端证书
    bool enable_ocsp_stapling;      // 是否启用OCSP装订
};

/**
 * @brief 安全管理器类
 * 负责系统的安全防护功能
 */
class SecurityManager {
public:
    /**
     * @brief 构造函数
     */
    SecurityManager();

    /**
     * @brief 析构函数
     */
    ~SecurityManager();

    /**
     * @brief 初始化安全管理器
     * @param config_file_path 配置文件路径
     * @return 是否初始化成功
     */
    bool initialize(const std::string& config_file_path = "");

    /**
     * @brief 关闭安全管理器
     */
    void shutdown();

    /**
     * @brief 检查IP地址是否被允许访问
     * @param ip_address IP地址
     * @return 是否允许访问
     */
    bool isIpAllowed(const std::string& ip_address);

    /**
     * @brief 检查访问频率是否超限
     * @param ip_address IP地址
     * @param resource 访问的资源
     * @return 是否超限
     */
    bool checkRateLimit(const std::string& ip_address, const std::string& resource = "");

    /**
     * @brief 记录访问请求
     * @param ip_address IP地址
     * @param user_agent 用户代理
     * @param resource 访问的资源
     * @param success 是否成功
     * @param username 用户名（可选）
     */
    void recordAccess(const std::string& ip_address,
                     const std::string& user_agent,
                     const std::string& resource,
                     bool success,
                     const std::string& username = "");

    /**
     * @brief 添加IP到白名单
     * @param ip_address IP地址或CIDR网段
     * @return 是否添加成功
     */
    bool addToWhitelist(const std::string& ip_address);

    /**
     * @brief 从白名单移除IP
     * @param ip_address IP地址或CIDR网段
     * @return 是否移除成功
     */
    bool removeFromWhitelist(const std::string& ip_address);

    /**
     * @brief 添加IP到黑名单
     * @param ip_address IP地址或CIDR网段
     * @param duration_seconds 阻断持续时间（0表示永久）
     * @param reason 阻断原因
     * @return 是否添加成功
     */
    bool addToBlacklist(const std::string& ip_address,
                       uint32_t duration_seconds = 0,
                       const std::string& reason = "");

    /**
     * @brief 从黑名单移除IP
     * @param ip_address IP地址或CIDR网段
     * @return 是否移除成功
     */
    bool removeFromBlacklist(const std::string& ip_address);

    /**
     * @brief 获取白名单列表
     * @return IP地址列表
     */
    std::vector<std::string> getWhitelist() const;

    /**
     * @brief 获取黑名单列表
     * @return IP地址列表
     */
    std::vector<std::string> getBlacklist() const;

    /**
     * @brief 检测可疑活动
     * @param ip_address IP地址
     * @param user_agent 用户代理
     * @param resource 访问的资源
     * @return 是否检测到可疑活动
     */
    bool detectSuspiciousActivity(const std::string& ip_address,
                                 const std::string& user_agent,
                                 const std::string& resource);

    /**
     * @brief 记录安全事件
     * @param event 安全事件
     */
    void recordSecurityEvent(const SecurityEvent& event);

    /**
     * @brief 获取安全事件列表
     * @param limit 最大返回数量
     * @param event_type 事件类型过滤（可选）
     * @return 安全事件列表
     */
    std::vector<SecurityEvent> getSecurityEvents(uint32_t limit = 100,
                                                SecurityEventType event_type = SecurityEventType::RATE_LIMIT_EXCEEDED) const;

    /**
     * @brief 获取IP访问统计
     * @param ip_address IP地址
     * @return IP访问统计信息
     */
    std::unique_ptr<IpAccessStats> getIpStats(const std::string& ip_address) const;

    /**
     * @brief 获取所有IP访问统计
     * @param limit 最大返回数量
     * @return IP访问统计列表
     */
    std::vector<IpAccessStats> getAllIpStats(uint32_t limit = 100) const;

    /**
     * @brief 设置速率限制配置
     * @param config 速率限制配置
     */
    void setRateLimitConfig(const RateLimitConfig& config);

    /**
     * @brief 获取速率限制配置
     * @return 速率限制配置
     */
    RateLimitConfig getRateLimitConfig() const;

    /**
     * @brief 设置TLS配置
     * @param config TLS配置
     */
    void setTlsConfig(const TlsConfig& config);

    /**
     * @brief 获取TLS配置
     * @return TLS配置
     */
    TlsConfig getTlsConfig() const;

    /**
     * @brief 验证TLS证书
     * @param cert_file 证书文件路径
     * @param key_file 私钥文件路径
     * @return 是否验证成功
     */
    bool validateTlsCertificate(const std::string& cert_file,
                               const std::string& key_file) const;

    /**
     * @brief 生成自签名证书
     * @param cert_file 证书文件路径
     * @param key_file 私钥文件路径
     * @param common_name 通用名称
     * @param days_valid 有效天数
     * @return 是否生成成功
     */
    bool generateSelfSignedCertificate(const std::string& cert_file,
                                      const std::string& key_file,
                                      const std::string& common_name,
                                      uint32_t days_valid = 365) const;

    /**
     * @brief 清理过期数据
     * 定期调用以清理过期的统计数据和事件记录
     */
    void cleanupExpiredData();

    /**
     * @brief 启用/禁用安全防护
     * @param enabled 是否启用
     */
    void setSecurityEnabled(bool enabled);

    /**
     * @brief 检查安全防护是否启用
     * @return 是否启用
     */
    bool isSecurityEnabled() const;

    /**
     * @brief 安全事件类型转字符串
     * @param type 事件类型
     * @return 事件类型字符串
     */
    static std::string securityEventTypeToString(SecurityEventType type);

private:
    bool m_enabled;                             // 是否启用安全防护
    RateLimitConfig m_rate_limit_config;        // 速率限制配置
    TlsConfig m_tls_config;                     // TLS配置
    
    std::unordered_set<std::string> m_whitelist; // IP白名单
    std::unordered_set<std::string> m_blacklist; // IP黑名单
    std::unordered_map<std::string, IpAccessStats> m_ip_stats; // IP访问统计
    std::vector<SecurityEvent> m_security_events; // 安全事件记录
    
    mutable std::mutex m_whitelist_mutex;       // 白名单互斥锁
    mutable std::mutex m_blacklist_mutex;       // 黑名单互斥锁
    mutable std::mutex m_stats_mutex;           // 统计数据互斥锁
    mutable std::mutex m_events_mutex;          // 事件记录互斥锁
    
    std::atomic<bool> m_shutdown_requested;     // 关闭请求标志
    std::unique_ptr<std::thread> m_cleanup_thread; // 清理线程

    /**
     * @brief 检查IP地址是否匹配CIDR网段
     * @param ip_address IP地址
     * @param cidr CIDR网段
     * @return 是否匹配
     */
    bool matchesCidr(const std::string& ip_address, const std::string& cidr) const;

    /**
     * @brief 解析CIDR网段
     * @param cidr CIDR网段字符串
     * @param network 网络地址（输出）
     * @param prefix_length 前缀长度（输出）
     * @return 是否解析成功
     */
    bool parseCidr(const std::string& cidr, uint32_t& network, uint32_t& prefix_length) const;

    /**
     * @brief IP地址字符串转数值
     * @param ip_str IP地址字符串
     * @return IP地址数值
     */
    uint32_t ipStringToNumber(const std::string& ip_str) const;

    /**
     * @brief 检查是否为暴力破解攻击
     * @param ip_address IP地址
     * @return 是否为暴力破解攻击
     */
    bool isBruteForceAttack(const std::string& ip_address) const;

    /**
     * @brief 检查用户代理是否可疑
     * @param user_agent 用户代理字符串
     * @return 是否可疑
     */
    bool isSuspiciousUserAgent(const std::string& user_agent) const;

    /**
     * @brief 清理线程函数
     */
    void cleanupThreadFunc();

    /**
     * @brief 加载配置文件
     * @param config_file_path 配置文件路径
     * @return 是否加载成功
     */
    bool loadConfig(const std::string& config_file_path);

    /**
     * @brief 保存配置文件
     * @param config_file_path 配置文件路径
     * @return 是否保存成功
     */
    bool saveConfig(const std::string& config_file_path) const;
};

} // namespace api
} // namespace timing_server