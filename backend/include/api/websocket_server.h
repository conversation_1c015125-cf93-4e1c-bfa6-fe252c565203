#pragma once

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <thread>
#include <mutex>
#include <atomic>
#include <condition_variable>
#include <functional>
#include <queue>
#include <chrono>
#include <core/types.h>
#include <core/logger.h>
#include <api/timing_service.h>

namespace timing_server {
namespace api {

/**
 * @brief WebSocket消息类型枚举
 */
enum class WebSocketMessageType {
    AUTH,                   // 认证消息
    STATUS_UPDATE,          // 状态更新
    ALARM,                  // 告警事件
    METRICS,                // 性能指标
    LOG_EVENT,              // 日志事件
    HEARTBEAT,              // 心跳消息
    SUBSCRIPTION,           // 订阅管理
    ERROR                   // 错误消息
};

/**
 * @brief WebSocket连接状态枚举
 */
enum class WebSocketConnectionState {
    CONNECTING,             // 连接中
    CONNECTED,              // 已连接
    AUTHENTICATED,          // 已认证
    DISCONNECTED,           // 已断开
    ERROR_STATE             // 错误状态
};

/**
 * @brief WebSocket消息结构
 */
struct WebSocketMessage {
    WebSocketMessageType type;
    std::string payload;
    std::chrono::steady_clock::time_point timestamp;
    
    WebSocketMessage(WebSocketMessageType t, const std::string& p) 
        : type(t), payload(p), timestamp(std::chrono::steady_clock::now()) {}
};

/**
 * @brief 客户端连接信息
 */
struct ClientConnection {
    uint64_t connection_id;                     // 连接ID
    WebSocketConnectionState state;             // 连接状态
    std::string remote_address;                 // 客户端地址
    std::chrono::steady_clock::time_point connect_time; // 连接时间
    std::chrono::steady_clock::time_point last_heartbeat; // 最后心跳时间
    std::unordered_set<std::string> subscriptions; // 订阅的事件类型
    std::string user_id;                        // 用户ID（认证后）
    std::string user_role;                      // 用户角色
    
    // 连接统计
    uint64_t messages_sent = 0;
    uint64_t messages_received = 0;
    uint64_t bytes_sent = 0;
    uint64_t bytes_received = 0;
    
    ClientConnection(uint64_t id, const std::string& addr) 
        : connection_id(id), state(WebSocketConnectionState::CONNECTING), 
          remote_address(addr), connect_time(std::chrono::steady_clock::now()),
          last_heartbeat(std::chrono::steady_clock::now()) {}
};

/**
 * @brief WebSocket服务器配置
 */
struct WebSocketServerConfig {
    uint16_t port = 8081;                       // 监听端口
    uint32_t max_connections = 100;             // 最大连接数
    uint32_t heartbeat_interval_ms = 30000;     // 心跳间隔（毫秒）
    uint32_t heartbeat_timeout_ms = 60000;      // 心跳超时（毫秒）
    uint32_t message_queue_size = 1000;         // 消息队列大小
    uint32_t max_message_size = 65536;          // 最大消息大小
    bool require_authentication = true;         // 是否需要认证
    std::string bind_address = "0.0.0.0";      // 绑定地址
};

/**
 * @brief WebSocket服务器统计信息
 */
struct WebSocketServerStats {
    uint64_t total_connections = 0;             // 总连接数
    uint64_t active_connections = 0;            // 活跃连接数
    uint64_t authenticated_connections = 0;     // 已认证连接数
    uint64_t messages_sent = 0;                 // 发送消息数
    uint64_t messages_received = 0;             // 接收消息数
    uint64_t bytes_sent = 0;                    // 发送字节数
    uint64_t bytes_received = 0;                // 接收字节数
    uint64_t heartbeat_timeouts = 0;            // 心跳超时数
    uint64_t authentication_failures = 0;       // 认证失败数
    double average_latency_ms = 0.0;            // 平均延迟
    std::chrono::steady_clock::time_point start_time; // 启动时间
};

/**
 * @brief WebSocket事件监听器接口
 */
class IWebSocketEventListener {
public:
    virtual ~IWebSocketEventListener() = default;
    
    /**
     * @brief 客户端连接事件
     * @param connection_id 连接ID
     * @param remote_address 客户端地址
     */
    virtual void onClientConnected(uint64_t connection_id, const std::string& remote_address) = 0;
    
    /**
     * @brief 客户端断开事件
     * @param connection_id 连接ID
     * @param reason 断开原因
     */
    virtual void onClientDisconnected(uint64_t connection_id, const std::string& reason) = 0;
    
    /**
     * @brief 客户端认证事件
     * @param connection_id 连接ID
     * @param user_id 用户ID
     * @param success 认证是否成功
     */
    virtual void onClientAuthenticated(uint64_t connection_id, const std::string& user_id, bool success) = 0;
    
    /**
     * @brief 消息接收事件
     * @param connection_id 连接ID
     * @param message 消息内容
     */
    virtual void onMessageReceived(uint64_t connection_id, const WebSocketMessage& message) = 0;
};

/**
 * @brief WebSocket服务器实现
 * 提供高性能、低延迟的实时通信服务，支持并发连接管理、消息广播、事件订阅等功能
 */
class WebSocketServer {
public:
    /**
     * @brief 构造函数
     * @param config 服务器配置
     * @param timing_service 授时服务实例
     */
    explicit WebSocketServer(const WebSocketServerConfig& config, 
                           std::shared_ptr<TimingService> timing_service = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~WebSocketServer();

    /**
     * @brief 启动WebSocket服务器
     * @return 是否启动成功
     */
    bool start();

    /**
     * @brief 停止WebSocket服务器
     */
    void stop();

    /**
     * @brief 检查服务器是否正在运行
     * @return 是否正在运行
     */
    bool isRunning() const { return m_running.load(); }

    /**
     * @brief 广播消息给所有已认证的客户端
     * @param message 要广播的消息
     * @param event_type 事件类型（用于订阅过滤）
     * @return 成功发送的客户端数量
     */
    uint32_t broadcastMessage(const WebSocketMessage& message, const std::string& event_type = "");

    /**
     * @brief 发送消息给特定客户端
     * @param connection_id 连接ID
     * @param message 要发送的消息
     * @return 是否发送成功
     */
    bool sendMessage(uint64_t connection_id, const WebSocketMessage& message);

    /**
     * @brief 获取服务器统计信息
     * @return 服务器统计信息
     */
    WebSocketServerStats getStats() const;

    /**
     * @brief 获取活跃连接列表
     * @return 连接信息列表
     */
    std::vector<ClientConnection> getActiveConnections() const;

    /**
     * @brief 断开特定客户端连接
     * @param connection_id 连接ID
     * @param reason 断开原因
     * @return 是否断开成功
     */
    bool disconnectClient(uint64_t connection_id, const std::string& reason = "server_disconnect");

    /**
     * @brief 设置事件监听器
     * @param listener 事件监听器
     */
    void setEventListener(std::shared_ptr<IWebSocketEventListener> listener);

    /**
     * @brief 启动实时状态推送
     * 定期推送系统状态更新给订阅的客户端
     */
    void startStatusPushing();

    /**
     * @brief 停止实时状态推送
     */
    void stopStatusPushing();

    // 以下方法主要用于测试
    /**
     * @brief 序列化消息为JSON（测试用）
     */
    std::string serializeMessage(const WebSocketMessage& message);

    /**
     * @brief 反序列化JSON消息（测试用）
     */
    WebSocketMessage deserializeMessage(const std::string& json_str);

    /**
     * @brief 创建心跳消息（测试用）
     */
    WebSocketMessage createHeartbeatMessage();

    /**
     * @brief 创建告警消息（测试用）
     */
    WebSocketMessage createAlarmMessage(const std::string& severity, 
                                      const std::string& component, 
                                      const std::string& message);

private:
    WebSocketServerConfig m_config;
    std::shared_ptr<TimingService> m_timing_service;
    std::shared_ptr<IWebSocketEventListener> m_event_listener;

    // 服务器状态
    std::atomic<bool> m_running{false};
    std::atomic<bool> m_stop_requested{false};
    std::atomic<uint64_t> m_next_connection_id{1};

    // 线程管理
    std::unique_ptr<std::thread> m_server_thread;
    std::unique_ptr<std::thread> m_heartbeat_thread;
    std::unique_ptr<std::thread> m_status_push_thread;
    std::unique_ptr<std::thread> m_message_processor_thread;

    // 连接管理
    mutable std::mutex m_connections_mutex;
    std::unordered_map<uint64_t, std::shared_ptr<ClientConnection>> m_connections;

    // 消息队列
    mutable std::mutex m_message_queue_mutex;
    std::condition_variable m_message_queue_cv;
    std::queue<std::pair<uint64_t, WebSocketMessage>> m_message_queue;

    // 统计信息
    mutable std::mutex m_stats_mutex;
    WebSocketServerStats m_stats;

    // 状态推送控制
    std::atomic<bool> m_status_pushing{false};
    std::chrono::steady_clock::time_point m_last_status_push;

    /**
     * @brief 服务器主循环
     */
    void serverLoop();

    /**
     * @brief 心跳检测循环
     */
    void heartbeatLoop();

    /**
     * @brief 状态推送循环
     */
    void statusPushLoop();

    /**
     * @brief 消息处理循环
     */
    void messageProcessorLoop();

    /**
     * @brief 处理新客户端连接
     * @param connection_id 连接ID
     * @param remote_address 客户端地址
     */
    void handleNewConnection(uint64_t connection_id, const std::string& remote_address);

    /**
     * @brief 处理客户端断开
     * @param connection_id 连接ID
     * @param reason 断开原因
     */
    void handleClientDisconnection(uint64_t connection_id, const std::string& reason);

    /**
     * @brief 处理接收到的消息
     * @param connection_id 连接ID
     * @param raw_message 原始消息
     */
    void handleReceivedMessage(uint64_t connection_id, const std::string& raw_message);

    /**
     * @brief 处理认证消息
     * @param connection_id 连接ID
     * @param auth_payload 认证载荷
     */
    void handleAuthMessage(uint64_t connection_id, const std::string& auth_payload);

    /**
     * @brief 处理订阅消息
     * @param connection_id 连接ID
     * @param subscription_payload 订阅载荷
     */
    void handleSubscriptionMessage(uint64_t connection_id, const std::string& subscription_payload);

    /**
     * @brief 处理心跳消息
     * @param connection_id 连接ID
     */
    void handleHeartbeatMessage(uint64_t connection_id);

    /**
     * @brief 验证JWT令牌
     * @param token JWT令牌
     * @param user_id 输出用户ID
     * @param user_role 输出用户角色
     * @return 是否验证成功
     */
    bool validateJwtToken(const std::string& token, std::string& user_id, std::string& user_role);

    /**
     * @brief 创建状态更新消息
     * @return 状态更新消息
     */
    WebSocketMessage createStatusUpdateMessage();



    /**
     * @brief 更新统计信息
     * @param bytes_sent 发送字节数
     * @param bytes_received 接收字节数
     * @param latency_ms 延迟（毫秒）
     */
    void updateStats(uint64_t bytes_sent, uint64_t bytes_received, double latency_ms = 0.0);

    /**
     * @brief 清理超时连接
     */
    void cleanupTimeoutConnections();

    /**
     * @brief 检查连接是否已认证
     * @param connection_id 连接ID
     * @return 是否已认证
     */
    bool isConnectionAuthenticated(uint64_t connection_id) const;

    /**
     * @brief 检查连接是否订阅了特定事件
     * @param connection_id 连接ID
     * @param event_type 事件类型
     * @return 是否已订阅
     */
    bool isConnectionSubscribed(uint64_t connection_id, const std::string& event_type) const;
};

} // namespace api
} // namespace timing_server