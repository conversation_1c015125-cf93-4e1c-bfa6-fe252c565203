#pragma once

#include "core/precision_monitor.h"
#include "core/alarm_system.h"
#include <memory>
#include <string>
#include <vector>

namespace timing_server {
namespace api {

/**
 * @brief 精度监控API数据传输对象
 */
struct PrecisionMeasurementDto {
    uint64_t timestamp_ns;
    std::string source;
    std::string system_state;
    double absolute_accuracy_ns;
    double phase_offset_ns;
    double frequency_offset_ppm;
    double allan_deviation_1s;
    double allan_deviation_10s;
    double allan_deviation_100s;
    uint32_t gnss_satellites;
    double gnss_snr_db;
    double rubidium_temperature;
    double system_temperature;
    double cpu_usage_percent;
    uint64_t memory_usage_mb;
    uint32_t network_latency_us;
    uint32_t overall_quality_score;
    bool meets_spec_requirements;
    
    /**
     * @brief 从核心数据结构转换
     */
    static PrecisionMeasurementDto FromCore(const core::PrecisionMeasurement& measurement);
    
    /**
     * @brief 转换为JSON字符串
     */
    std::string ToJson() const;
};

/**
 * @brief 精度趋势分析DTO
 */
struct PrecisionTrendDto {
    uint64_t analysis_period_hours;
    double accuracy_trend_ns_per_hour;
    double frequency_drift_ppm_per_hour;
    double stability_trend_coefficient;
    double mean_accuracy_ns;
    double std_deviation_accuracy_ns;
    double min_accuracy_ns;
    double max_accuracy_ns;
    double predicted_accuracy_1h_ns;
    double predicted_accuracy_24h_ns;
    double confidence_level;
    bool trend_is_stable;
    bool within_specification;
    std::vector<std::string> recommendations;
    
    /**
     * @brief 从核心数据结构转换
     */
    static PrecisionTrendDto FromCore(const core::PrecisionTrend& trend);
    
    /**
     * @brief 转换为JSON字符串
     */
    std::string ToJson() const;
};

/**
 * @brief 系统健康评分DTO
 */
struct SystemHealthScoreDto {
    uint32_t overall_score;
    uint32_t timing_accuracy_score;
    uint32_t signal_quality_score;
    uint32_t system_stability_score;
    uint32_t hardware_health_score;
    uint32_t performance_score;
    std::string health_status;
    std::vector<std::string> issues;
    std::vector<std::string> warnings;
    std::vector<std::string> suggestions;
    bool needs_calibration;
    bool needs_maintenance;
    uint32_t days_until_maintenance;
    
    /**
     * @brief 从核心数据结构转换
     */
    static SystemHealthScoreDto FromCore(const core::SystemHealthScore& score);
    
    /**
     * @brief 转换为JSON字符串
     */
    std::string ToJson() const;
};

/**
 * @brief 告警事件DTO
 */
struct AlarmEventDto {
    uint64_t id;
    uint64_t timestamp_ns;
    std::string type;
    std::string level;
    std::string status;
    std::string title;
    std::string description;
    std::string source_component;
    double threshold_value;
    double current_value;
    std::string unit;
    uint32_t occurrence_count;
    uint64_t first_occurrence_ns;
    uint64_t last_occurrence_ns;
    uint64_t acknowledged_time_ns;
    uint64_t resolved_time_ns;
    std::string acknowledged_by;
    std::string resolution_notes;
    
    /**
     * @brief 从核心数据结构转换
     */
    static AlarmEventDto FromCore(const core::AlarmEvent& alarm);
    
    /**
     * @brief 转换为JSON字符串
     */
    std::string ToJson() const;
};

/**
 * @brief 告警统计DTO
 */
struct AlarmStatisticsDto {
    uint64_t total_alarms;
    uint64_t active_alarms;
    uint64_t acknowledged_alarms;
    uint64_t resolved_alarms;
    uint64_t suppressed_alarms;
    double average_resolution_time_minutes;
    double average_acknowledgment_time_minutes;
    uint64_t notifications_sent;
    uint64_t notification_failures;
    
    /**
     * @brief 从核心数据结构转换
     */
    static AlarmStatisticsDto FromCore(const core::AlarmStatistics& stats);
    
    /**
     * @brief 转换为JSON字符串
     */
    std::string ToJson() const;
};

/**
 * @brief 精度监控API控制器
 * 提供精度监控和告警系统的REST API接口
 */
class PrecisionApiController {
public:
    /**
     * @brief 构造函数
     * @param precision_monitor 精度监控器实例
     * @param alarm_system 告警系统实例
     */
    PrecisionApiController(std::shared_ptr<core::IPrecisionMonitor> precision_monitor,
                          std::shared_ptr<core::IAlarmSystem> alarm_system);
    
    /**
     * @brief 析构函数
     */
    ~PrecisionApiController() = default;
    
    /**
     * @brief 获取当前精度测量
     * @return JSON格式的精度测量数据
     */
    std::string GetCurrentMeasurement();
    
    /**
     * @brief 获取历史精度测量数据
     * @param start_time 开始时间（纳秒时间戳）
     * @param end_time 结束时间（纳秒时间戳）
     * @param max_samples 最大样本数
     * @return JSON格式的历史测量数据列表
     */
    std::string GetHistoricalMeasurements(uint64_t start_time, uint64_t end_time, uint32_t max_samples = 1000);
    
    /**
     * @brief 获取精度趋势分析
     * @param period_hours 分析周期（小时）
     * @return JSON格式的趋势分析数据
     */
    std::string GetPrecisionTrend(uint32_t period_hours = 24);
    
    /**
     * @brief 获取系统健康评分
     * @return JSON格式的健康评分数据
     */
    std::string GetSystemHealthScore();
    
    /**
     * @brief 获取预测性维护建议
     * @return JSON格式的维护建议数据
     */
    std::string GetMaintenanceAdvice();
    
    /**
     * @brief 检查精度要求
     * @param required_accuracy_ns 要求的精度（纳秒）
     * @return JSON格式的检查结果
     */
    std::string CheckAccuracyRequirement(double required_accuracy_ns);
    
    /**
     * @brief 设置监控级别
     * @param level 监控级别字符串
     * @return JSON格式的操作结果
     */
    std::string SetMonitoringLevel(const std::string& level);
    
    /**
     * @brief 导出测量数据
     * @param format 导出格式（"csv" 或 "json"）
     * @return JSON格式的操作结果，包含下载链接
     */
    std::string ExportMeasurementData(const std::string& format = "csv");
    
    /**
     * @brief 获取活跃告警列表
     * @return JSON格式的活跃告警列表
     */
    std::string GetActiveAlarms();
    
    /**
     * @brief 获取历史告警列表
     * @param start_time 开始时间（纳秒时间戳）
     * @param end_time 结束时间（纳秒时间戳）
     * @param max_count 最大数量
     * @return JSON格式的历史告警列表
     */
    std::string GetHistoricalAlarms(uint64_t start_time, uint64_t end_time, uint32_t max_count = 1000);
    
    /**
     * @brief 确认告警
     * @param alarm_id 告警ID
     * @param acknowledged_by 确认人
     * @param notes 确认备注
     * @return JSON格式的操作结果
     */
    std::string AcknowledgeAlarm(uint64_t alarm_id, const std::string& acknowledged_by, 
                                const std::string& notes = "");
    
    /**
     * @brief 解决告警
     * @param alarm_id 告警ID
     * @param resolution_notes 解决备注
     * @return JSON格式的操作结果
     */
    std::string ResolveAlarm(uint64_t alarm_id, const std::string& resolution_notes = "");
    
    /**
     * @brief 抑制告警
     * @param alarm_id 告警ID
     * @param duration_seconds 抑制持续时间（秒）
     * @return JSON格式的操作结果
     */
    std::string SuppressAlarm(uint64_t alarm_id, uint32_t duration_seconds);
    
    /**
     * @brief 获取告警统计信息
     * @return JSON格式的告警统计数据
     */
    std::string GetAlarmStatistics();
    
    /**
     * @brief 清除历史数据
     * @param data_type 数据类型（"measurements" 或 "alarms"）
     * @param older_than_hours 清除多少小时前的数据
     * @return JSON格式的操作结果
     */
    std::string ClearHistoricalData(const std::string& data_type, uint32_t older_than_hours = 168);

private:
    /**
     * @brief 创建成功响应
     * @param data 数据内容
     * @return JSON格式的成功响应
     */
    std::string CreateSuccessResponse(const std::string& data);
    
    /**
     * @brief 创建错误响应
     * @param error_code 错误代码
     * @param error_message 错误消息
     * @return JSON格式的错误响应
     */
    std::string CreateErrorResponse(const std::string& error_code, const std::string& error_message);
    
    /**
     * @brief 将监控级别字符串转换为枚举
     * @param level_str 级别字符串
     * @return 监控级别枚举
     */
    core::MonitoringLevel ParseMonitoringLevel(const std::string& level_str);
    
    /**
     * @brief 生成唯一的导出文件名
     * @param format 文件格式
     * @return 文件名
     */
    std::string GenerateExportFilename(const std::string& format);

private:
    std::shared_ptr<core::IPrecisionMonitor> precision_monitor_;
    std::shared_ptr<core::IAlarmSystem> alarm_system_;
};

} // namespace api
} // namespace timing_server