#pragma once
#include <core/types.h>
#include <memory>
#include <string>
#include <vector>

namespace timing_server {
namespace api {

/**
 * @brief 授时服务接口
 * 为REST API控制器提供业务逻辑接口，连接API层和核心服务层
 */
class TimingService {
public:
    virtual ~TimingService() = default;

    /**
     * @brief 获取系统完整状态
     * @return 系统状态结构
     */
    virtual core::SystemStatus getSystemStatus() = 0;

    /**
     * @brief 获取当前PTP配置
     * @return PTP配置字符串（JSON格式）
     */
    virtual std::string getPtpConfig() = 0;

    /**
     * @brief 更新PTP配置
     * @param config_json PTP配置JSON字符串
     * @return 是否更新成功
     */
    virtual bool updatePtpConfig(const std::string& config_json) = 0;

    /**
     * @brief 获取当前NTP配置
     * @return NTP配置字符串（JSON格式）
     */
    virtual std::string getNtpConfig() = 0;

    /**
     * @brief 更新NTP配置
     * @param config_json NTP配置JSON字符串
     * @return 是否更新成功
     */
    virtual bool updateNtpConfig(const std::string& config_json) = 0;

    /**
     * @brief 日志查询参数结构
     */
    struct LogQueryParams {
        std::string level;          // 日志级别过滤
        uint32_t page = 1;          // 页码
        uint32_t limit = 100;       // 每页条目数
        std::string from;           // 开始时间
        std::string to;             // 结束时间
    };

    /**
     * @brief 日志条目结构
     */
    struct LogEntry {
        std::string timestamp;      // 时间戳
        std::string level;          // 日志级别
        std::string component;      // 组件名称
        std::string message;        // 日志消息
        std::map<std::string, std::string> context; // 上下文信息
    };

    /**
     * @brief 日志查询结果结构
     */
    struct LogQueryResult {
        std::vector<LogEntry> logs; // 日志条目列表
        uint32_t total_entries;     // 总条目数
        uint32_t current_page;      // 当前页码
        uint32_t total_pages;       // 总页数
    };

    /**
     * @brief 查询系统日志
     * @param params 查询参数
     * @return 日志查询结果
     */
    virtual LogQueryResult queryLogs(const LogQueryParams& params) = 0;

    /**
     * @brief 系统健康状态结构
     */
    struct HealthStatus {
        std::string status;         // 整体健康状态
        std::string timestamp;      // 检查时间戳
        std::map<std::string, std::string> components; // 各组件状态
        double uptime_seconds;      // 运行时间
    };

    /**
     * @brief 获取系统健康状态
     * @return 健康状态结构
     */
    virtual HealthStatus getHealthStatus() = 0;

    /**
     * @brief 重启系统服务
     * @return 是否重启成功
     */
    virtual bool restartSystem() = 0;

    /**
     * @brief 性能指标结构
     */
    struct PerformanceMetrics {
        // 时间精度指标
        double current_accuracy_ns;
        double average_accuracy_ns;
        double max_accuracy_ns;
        
        // 频率稳定度指标
        double allan_deviation_1s;
        double allan_deviation_10s;
        double allan_deviation_100s;
        
        // 系统性能指标
        uint64_t state_transitions;
        uint64_t error_count;
        uint64_t packets_processed;
        double average_response_time_ms;
        
        std::string start_time;     // 统计开始时间
        std::string end_time;       // 统计结束时间
    };

    /**
     * @brief 获取性能指标
     * @param from 开始时间（ISO格式）
     * @param to 结束时间（ISO格式）
     * @return 性能指标结构
     */
    virtual PerformanceMetrics getMetrics(const std::string& from, const std::string& to) = 0;

    /**
     * @brief 验证配置
     * @param config_type 配置类型（ptp/ntp）
     * @param config_json 配置JSON字符串
     * @return 验证结果消息，空字符串表示验证通过
     */
    virtual std::string validateConfig(const std::string& config_type, const std::string& config_json) = 0;

    /**
     * @brief 获取配置模式
     * @param config_type 配置类型
     * @return JSON模式字符串
     */
    virtual std::string getConfigSchema(const std::string& config_type) = 0;
};

// 前向声明
namespace core {
    class TimingEngine;
    class ConfigManager;
    class DatabaseManager;
}

/**
 * @brief 创建授时服务实例的工厂函数
 * @param timing_engine 授时引擎实例
 * @param config_manager 配置管理器实例
 * @param database_manager 数据库管理器实例
 * @return 授时服务实例
 */
std::shared_ptr<TimingService> createTimingService(
    std::shared_ptr<core::TimingEngine> timing_engine,
    std::shared_ptr<core::ConfigManager> config_manager,
    std::shared_ptr<core::DatabaseManager> database_manager);

} // namespace api
} // namespace timing_server