#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <chrono>
#include <mutex>
#include <core/types.h>

namespace timing_server {
namespace api {

/**
 * @brief 用户角色枚举
 * 定义系统中的用户角色类型
 */
enum class UserRole {
    VIEWER = 1,         // 查看者 - 只读权限
    OPERATOR = 2,       // 操作员 - 配置权限
    ADMINISTRATOR = 3   // 管理员 - 完全权限
};

/**
 * @brief 权限枚举
 * 定义系统中的具体权限
 */
enum class Permission {
    READ_STATUS = 1,        // 读取系统状态
    READ_LOGS = 2,          // 读取日志
    READ_CONFIG = 4,        // 读取配置
    WRITE_CONFIG = 8,       // 修改配置
    CONTROL_SYSTEM = 16,    // 控制系统启停
    MANAGE_USERS = 32,      // 用户管理
    VIEW_AUDIT = 64         // 查看审计日志
};

/**
 * @brief 用户信息结构
 * 存储用户的基本信息和权限
 */
struct UserInfo {
    std::string username;           // 用户名
    std::string password_hash;      // 密码哈希
    UserRole role;                  // 用户角色
    std::vector<Permission> permissions; // 权限列表
    uint64_t created_at;           // 创建时间戳
    uint64_t last_login;           // 最后登录时间戳
    bool is_active;                // 是否激活
    std::string salt;              // 密码盐值
};

/**
 * @brief JWT令牌信息结构
 * 存储JWT令牌的相关信息
 */
struct TokenInfo {
    std::string username;           // 用户名
    UserRole role;                  // 用户角色
    std::vector<Permission> permissions; // 权限列表
    uint64_t issued_at;            // 签发时间
    uint64_t expires_at;           // 过期时间
    std::string token_id;          // 令牌ID
};

/**
 * @brief 会话信息结构
 * 存储用户会话的相关信息
 */
struct SessionInfo {
    std::string username;           // 用户名
    std::string ip_address;         // IP地址
    std::string user_agent;         // 用户代理
    uint64_t created_at;           // 创建时间
    uint64_t last_activity;        // 最后活动时间
    bool is_active;                // 是否活跃
};

/**
 * @brief 审计日志条目结构
 * 存储安全审计日志信息
 */
struct AuditLogEntry {
    uint64_t timestamp;             // 时间戳
    std::string username;           // 用户名
    std::string action;             // 操作类型
    std::string resource;           // 资源路径
    std::string ip_address;         // IP地址
    std::string user_agent;         // 用户代理
    bool success;                   // 操作是否成功
    std::string details;            // 详细信息
};

/**
 * @brief 认证管理器类
 * 负责用户认证、授权、会话管理和安全审计
 */
class AuthManager {
public:
    /**
     * @brief 构造函数
     * @param jwt_secret JWT签名密钥
     * @param token_expiry_seconds 令牌过期时间（秒）
     * @param refresh_token_expiry_seconds 刷新令牌过期时间（秒）
     */
    AuthManager(const std::string& jwt_secret, 
                uint32_t token_expiry_seconds = 3600,
                uint32_t refresh_token_expiry_seconds = 86400);

    /**
     * @brief 析构函数
     */
    ~AuthManager();

    /**
     * @brief 初始化认证管理器
     * 创建默认管理员用户和数据库表
     * @return 是否初始化成功
     */
    bool initialize();

    /**
     * @brief 用户登录认证
     * @param username 用户名
     * @param password 密码
     * @param ip_address 客户端IP地址
     * @param user_agent 用户代理字符串
     * @return 认证成功返回访问令牌和刷新令牌，失败返回空字符串
     */
    std::pair<std::string, std::string> authenticate(const std::string& username,
                                                     const std::string& password,
                                                     const std::string& ip_address,
                                                     const std::string& user_agent);

    /**
     * @brief 验证访问令牌
     * @param token JWT访问令牌
     * @return 令牌有效返回令牌信息，无效返回nullptr
     */
    std::unique_ptr<TokenInfo> validateToken(const std::string& token);

    /**
     * @brief 刷新访问令牌
     * @param refresh_token 刷新令牌
     * @param ip_address 客户端IP地址
     * @return 成功返回新的访问令牌和刷新令牌，失败返回空字符串
     */
    std::pair<std::string, std::string> refreshToken(const std::string& refresh_token,
                                                     const std::string& ip_address);

    /**
     * @brief 用户登出
     * @param token 访问令牌
     * @param ip_address 客户端IP地址
     * @return 是否登出成功
     */
    bool logout(const std::string& token, const std::string& ip_address);

    /**
     * @brief 检查用户权限
     * @param username 用户名
     * @param permission 需要检查的权限
     * @return 是否具有该权限
     */
    bool hasPermission(const std::string& username, Permission permission);

    /**
     * @brief 创建新用户
     * @param username 用户名
     * @param password 密码
     * @param role 用户角色
     * @param creator 创建者用户名
     * @param ip_address 创建者IP地址
     * @return 是否创建成功
     */
    bool createUser(const std::string& username,
                   const std::string& password,
                   UserRole role,
                   const std::string& creator,
                   const std::string& ip_address);

    /**
     * @brief 更新用户信息
     * @param username 用户名
     * @param new_password 新密码（空字符串表示不更改）
     * @param new_role 新角色
     * @param is_active 是否激活
     * @param updater 更新者用户名
     * @param ip_address 更新者IP地址
     * @return 是否更新成功
     */
    bool updateUser(const std::string& username,
                   const std::string& new_password,
                   UserRole new_role,
                   bool is_active,
                   const std::string& updater,
                   const std::string& ip_address);

    /**
     * @brief 删除用户
     * @param username 用户名
     * @param deleter 删除者用户名
     * @param ip_address 删除者IP地址
     * @return 是否删除成功
     */
    bool deleteUser(const std::string& username,
                   const std::string& deleter,
                   const std::string& ip_address);

    /**
     * @brief 获取用户信息
     * @param username 用户名
     * @return 用户信息，不存在返回nullptr
     */
    std::unique_ptr<UserInfo> getUserInfo(const std::string& username);

    /**
     * @brief 获取用户列表
     * @param page 页码（从1开始）
     * @param limit 每页数量
     * @return 用户信息列表和总数
     */
    std::pair<std::vector<UserInfo>, uint32_t> getUserList(uint32_t page, uint32_t limit);

    /**
     * @brief 记录审计日志
     * @param username 用户名
     * @param action 操作类型
     * @param resource 资源路径
     * @param ip_address IP地址
     * @param user_agent 用户代理
     * @param success 操作是否成功
     * @param details 详细信息
     */
    void logAudit(const std::string& username,
                 const std::string& action,
                 const std::string& resource,
                 const std::string& ip_address,
                 const std::string& user_agent,
                 bool success,
                 const std::string& details = "");

    /**
     * @brief 获取审计日志
     * @param page 页码（从1开始）
     * @param limit 每页数量
     * @param username 用户名过滤（空字符串表示不过滤）
     * @param action 操作类型过滤（空字符串表示不过滤）
     * @param from_time 开始时间戳（0表示不限制）
     * @param to_time 结束时间戳（0表示不限制）
     * @return 审计日志列表和总数
     */
    std::pair<std::vector<AuditLogEntry>, uint32_t> getAuditLogs(
        uint32_t page, uint32_t limit,
        const std::string& username = "",
        const std::string& action = "",
        uint64_t from_time = 0,
        uint64_t to_time = 0);

    /**
     * @brief 获取活跃会话列表
     * @return 活跃会话信息列表
     */
    std::vector<SessionInfo> getActiveSessions();

    /**
     * @brief 强制结束用户会话
     * @param username 用户名
     * @param admin_username 管理员用户名
     * @param ip_address 管理员IP地址
     * @return 是否成功结束会话
     */
    bool terminateUserSessions(const std::string& username,
                              const std::string& admin_username,
                              const std::string& ip_address);

    /**
     * @brief 清理过期令牌和会话
     * 定期调用以清理过期的令牌和会话信息
     */
    void cleanupExpiredTokens();

    /**
     * @brief 获取角色对应的权限列表
     * @param role 用户角色
     * @return 权限列表
     */
    static std::vector<Permission> getRolePermissions(UserRole role);

    /**
     * @brief 角色转字符串
     * @param role 用户角色
     * @return 角色字符串
     */
    static std::string roleToString(UserRole role);

    /**
     * @brief 字符串转角色
     * @param role_str 角色字符串
     * @return 用户角色
     */
    static UserRole stringToRole(const std::string& role_str);

    /**
     * @brief 权限转字符串
     * @param permission 权限
     * @return 权限字符串
     */
    static std::string permissionToString(Permission permission);

private:
    std::string m_jwt_secret;                           // JWT签名密钥
    uint32_t m_token_expiry_seconds;                    // 访问令牌过期时间
    uint32_t m_refresh_token_expiry_seconds;            // 刷新令牌过期时间
    
    std::unordered_map<std::string, UserInfo> m_users; // 用户信息缓存
    std::unordered_map<std::string, SessionInfo> m_sessions; // 会话信息
    std::vector<AuditLogEntry> m_audit_logs;           // 审计日志缓存
    
    mutable std::mutex m_users_mutex;                   // 用户信息互斥锁
    mutable std::mutex m_sessions_mutex;                // 会话信息互斥锁
    mutable std::mutex m_audit_mutex;                   // 审计日志互斥锁

    /**
     * @brief 生成密码哈希
     * @param password 明文密码
     * @param salt 盐值
     * @return 密码哈希
     */
    std::string hashPassword(const std::string& password, const std::string& salt);

    /**
     * @brief 生成随机盐值
     * @return 盐值字符串
     */
    std::string generateSalt();

    /**
     * @brief 验证密码
     * @param password 明文密码
     * @param hash 密码哈希
     * @param salt 盐值
     * @return 是否匹配
     */
    bool verifyPassword(const std::string& password, 
                       const std::string& hash, 
                       const std::string& salt);

    /**
     * @brief 生成JWT令牌
     * @param username 用户名
     * @param role 用户角色
     * @param permissions 权限列表
     * @param expires_in 过期时间（秒）
     * @return JWT令牌字符串
     */
    std::string generateJWT(const std::string& username,
                           UserRole role,
                           const std::vector<Permission>& permissions,
                           uint32_t expires_in);

    /**
     * @brief 解析JWT令牌
     * @param token JWT令牌
     * @return 令牌信息，无效返回nullptr
     */
    std::unique_ptr<TokenInfo> parseJWT(const std::string& token);

    /**
     * @brief 生成随机令牌ID
     * @return 令牌ID字符串
     */
    std::string generateTokenId();

    /**
     * @brief 加载用户数据
     * 从数据库或配置文件加载用户信息
     * @return 是否加载成功
     */
    bool loadUsers();

    /**
     * @brief 保存用户数据
     * 将用户信息保存到数据库或配置文件
     * @return 是否保存成功
     */
    bool saveUsers();

    /**
     * @brief 创建默认管理员用户
     * @return 是否创建成功
     */
    bool createDefaultAdmin();
};

} // namespace api
} // namespace timing_server