#pragma once

#include "core/types.h"
#include "core/logger.h"
#include <string>
#include <memory>
#include <vector>
#include <map>
#include <functional>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>
#include <queue>

namespace timing_server {
namespace core {

/**
 * @brief 错误类型枚举
 * 定义系统中可能出现的各种错误类型
 */
enum class ErrorType {
    // 硬件相关错误
    HARDWARE_FAULT,         // 硬件故障
    GNSS_SIGNAL_LOST,       // GNSS信号丢失
    RUBIDIUM_FAULT,         // 铷钟故障
    RTC_FAULT,              // RTC故障
    PPS_SIGNAL_LOST,        // PPS信号丢失
    FREQ_REF_LOST,          // 频率基准丢失
    NETWORK_INTERFACE_DOWN, // 网络接口故障
    
    // 软件相关错误
    CONFIG_ERROR,           // 配置错误
    DAEMON_CRASH,           // 守护进程崩溃
    API_ERROR,              // API错误
    DATABASE_ERROR,         // 数据库错误
    FILE_SYSTEM_ERROR,      // 文件系统错误
    MEMORY_ERROR,           // 内存错误
    
    // 系统相关错误
    SYSTEM_OVERLOAD,        // 系统过载
    TEMPERATURE_ALARM,      // 温度告警
    POWER_SUPPLY_FAULT,     // 电源故障
    CLOCK_SYNC_LOST,        // 时钟同步丢失
    HOLDOVER_TIMEOUT,       // 守时超时
    
    // 网络相关错误
    NETWORK_TIMEOUT,        // 网络超时
    PTP_SYNC_LOST,          // PTP同步丢失
    NTP_SYNC_LOST,          // NTP同步丢失
    
    // 用户相关错误
    AUTHENTICATION_FAILED,  // 认证失败
    AUTHORIZATION_DENIED,   // 授权拒绝
    INVALID_REQUEST,        // 无效请求
    
    // 未知错误
    UNKNOWN_ERROR           // 未知错误
};

/**
 * @brief 错误严重程度枚举
 * 定义错误的严重程度级别
 */
enum class ErrorSeverity {
    LOW,        // 低 - 不影响核心功能
    MEDIUM,     // 中 - 影响部分功能
    HIGH,       // 高 - 影响核心功能
    CRITICAL    // 严重 - 系统无法正常工作
};

/**
 * @brief 错误恢复策略枚举
 * 定义系统对不同错误的恢复策略
 */
enum class RecoveryStrategy {
    IGNORE,             // 忽略错误
    RETRY,              // 重试操作
    RESTART_COMPONENT,  // 重启组件
    FAILOVER,           // 故障切换
    DEGRADE_SERVICE,    // 降级服务
    SHUTDOWN_SYSTEM,    // 关闭系统
    MANUAL_INTERVENTION // 需要人工干预
};

/**
 * @brief 错误状态枚举
 * 定义错误的处理状态
 */
enum class ErrorStatus {
    NEW,            // 新错误
    ACKNOWLEDGED,   // 已确认
    IN_PROGRESS,    // 处理中
    RESOLVED,       // 已解决
    FAILED,         // 处理失败
    IGNORED         // 已忽略
};

/**
 * @brief 错误信息结构
 * 包含错误的完整信息
 */
struct ErrorInfo {
    uint64_t error_id;                          // 错误ID
    uint64_t timestamp_ns;                      // 发生时间戳
    ErrorType type;                             // 错误类型
    ErrorSeverity severity;                     // 严重程度
    LogComponent component;                     // 发生组件
    std::string message;                        // 错误消息
    std::string details;                        // 详细信息
    std::map<std::string, std::string> context; // 上下文信息
    ErrorStatus status;                         // 处理状态
    RecoveryStrategy recovery_strategy;         // 恢复策略
    uint32_t retry_count;                       // 重试次数
    uint64_t last_retry_time_ns;               // 最后重试时间
    uint64_t resolved_time_ns;                 // 解决时间
    std::string resolution_details;             // 解决详情
    
    /**
     * @brief 默认构造函数
     */
    ErrorInfo() : error_id(0), timestamp_ns(0), type(ErrorType::UNKNOWN_ERROR),
                  severity(ErrorSeverity::LOW), component(LogComponent::SYSTEM),
                  status(ErrorStatus::NEW), recovery_strategy(RecoveryStrategy::IGNORE),
                  retry_count(0), last_retry_time_ns(0), resolved_time_ns(0) {}
    
    /**
     * @brief 构造函数
     */
    ErrorInfo(ErrorType err_type, ErrorSeverity sev, LogComponent comp, 
              const std::string& msg, const std::string& detail = "")
        : type(err_type), severity(sev), component(comp), message(msg), details(detail),
          status(ErrorStatus::NEW), recovery_strategy(RecoveryStrategy::IGNORE),
          retry_count(0), last_retry_time_ns(0), resolved_time_ns(0) {
        
        error_id = GenerateErrorId();
        timestamp_ns = GetCurrentTimestampNs();
    }
    
private:
    static uint64_t GenerateErrorId();
};

/**
 * @brief 错误恢复配置
 * 定义不同错误类型的恢复策略配置
 */
struct ErrorRecoveryConfig {
    RecoveryStrategy strategy;                  // 恢复策略
    uint32_t max_retry_count;                  // 最大重试次数
    uint32_t retry_interval_ms;                // 重试间隔（毫秒）
    uint32_t timeout_ms;                       // 超时时间（毫秒）
    bool enable_auto_recovery;                 // 是否启用自动恢复
    std::vector<ErrorType> dependent_errors;   // 依赖的错误类型
    std::string custom_handler;                // 自定义处理器名称
};

/**
 * @brief 系统健康监控配置
 * 定义系统健康监控的各项参数
 */
struct HealthMonitorConfig {
    uint32_t check_interval_ms;                // 检查间隔（毫秒）
    uint32_t cpu_threshold_percent;            // CPU使用率阈值
    uint32_t memory_threshold_percent;         // 内存使用率阈值
    double temperature_threshold_celsius;      // 温度阈值（摄氏度）
    uint32_t disk_usage_threshold_percent;     // 磁盘使用率阈值
    uint32_t network_timeout_ms;               // 网络超时时间
    bool enable_proactive_monitoring;          // 是否启用主动监控
    std::vector<std::string> monitored_processes; // 监控的进程列表
};

/**
 * @brief 告警配置
 * 定义告警的触发条件和通知方式
 */
struct AlarmConfig {
    bool enable_email_notification;            // 启用邮件通知
    bool enable_sms_notification;              // 启用短信通知
    bool enable_webhook_notification;          // 启用Webhook通知
    std::vector<std::string> email_recipients; // 邮件接收者
    std::vector<std::string> sms_recipients;   // 短信接收者
    std::string webhook_url;                   // Webhook URL
    uint32_t notification_cooldown_ms;         // 通知冷却时间
    std::map<ErrorSeverity, bool> severity_filters; // 严重程度过滤
};

/**
 * @brief 错误恢复处理器接口
 * 定义错误恢复处理的抽象接口
 */
class IErrorRecoveryHandler {
public:
    virtual ~IErrorRecoveryHandler() = default;
    
    /**
     * @brief 处理错误恢复
     * @param error_info 错误信息
     * @return 是否成功恢复
     */
    virtual bool HandleRecovery(ErrorInfo& error_info) = 0;
    
    /**
     * @brief 获取处理器名称
     */
    virtual std::string GetHandlerName() const = 0;
    
    /**
     * @brief 检查是否可以处理指定类型的错误
     */
    virtual bool CanHandle(ErrorType error_type) const = 0;
};

/**
 * @brief 重试恢复处理器
 * 实现简单的重试恢复策略
 */
class RetryRecoveryHandler : public IErrorRecoveryHandler {
public:
    explicit RetryRecoveryHandler(const ErrorRecoveryConfig& config);
    
    bool HandleRecovery(ErrorInfo& error_info) override;
    std::string GetHandlerName() const override { return "RetryRecoveryHandler"; }
    bool CanHandle(ErrorType error_type) const override;

private:
    ErrorRecoveryConfig config_;
    std::function<bool(const ErrorInfo&)> retry_function_;
    
    bool RetryGnssInitialization();
    bool RetryNetworkConnection();
    bool RetryConfigLoad();
    bool RetryApiService();
};

/**
 * @brief 故障切换恢复处理器
 * 实现故障切换恢复策略
 */
class FailoverRecoveryHandler : public IErrorRecoveryHandler {
public:
    explicit FailoverRecoveryHandler(const ErrorRecoveryConfig& config);
    
    bool HandleRecovery(ErrorInfo& error_info) override;
    std::string GetHandlerName() const override { return "FailoverRecoveryHandler"; }
    bool CanHandle(ErrorType error_type) const override;

private:
    ErrorRecoveryConfig config_;
    
    bool PerformTimeSourceFailover(const ErrorInfo& error_info);
    bool PerformNetworkInterfaceFailover(const ErrorInfo& error_info);
    bool PerformServiceFailover(const ErrorInfo& error_info);
};

/**
 * @brief 组件重启恢复处理器
 * 实现组件重启恢复策略
 */
class RestartRecoveryHandler : public IErrorRecoveryHandler {
public:
    explicit RestartRecoveryHandler(const ErrorRecoveryConfig& config);
    
    bool HandleRecovery(ErrorInfo& error_info) override;
    std::string GetHandlerName() const override { return "RestartRecoveryHandler"; }
    bool CanHandle(ErrorType error_type) const override;

private:
    ErrorRecoveryConfig config_;
    
    bool RestartDaemon(LogComponent component);
    bool RestartService(const std::string& service_name);
    bool RestartHardwareInterface(LogComponent component);
};

/**
 * @brief 系统健康监控器
 * 监控系统健康状况并主动发现问题
 */
class SystemHealthMonitor {
public:
    explicit SystemHealthMonitor(const HealthMonitorConfig& config);
    ~SystemHealthMonitor();
    
    /**
     * @brief 启动健康监控
     */
    bool Start();
    
    /**
     * @brief 停止健康监控
     */
    void Stop();
    
    /**
     * @brief 获取当前系统健康状态
     */
    SystemHealth GetSystemHealth() const;
    
    /**
     * @brief 获取详细的健康报告
     */
    std::map<std::string, std::string> GetHealthReport() const;
    
    /**
     * @brief 注册健康检查回调
     */
    void RegisterHealthCheckCallback(const std::function<void(SystemHealth)>& callback);

private:
    HealthMonitorConfig config_;
    std::atomic<bool> running_;
    std::atomic<SystemHealth> current_health_;
    std::unique_ptr<std::thread> monitor_thread_;
    mutable std::mutex health_data_mutex_;
    std::map<std::string, std::string> health_data_;
    std::vector<std::function<void(SystemHealth)>> health_callbacks_;
    
    /**
     * @brief 监控线程函数
     */
    void MonitorThreadFunc();
    
    /**
     * @brief 检查CPU使用率
     */
    double CheckCpuUsage();
    
    /**
     * @brief 检查内存使用率
     */
    double CheckMemoryUsage();
    
    /**
     * @brief 检查磁盘使用率
     */
    double CheckDiskUsage();
    
    /**
     * @brief 检查系统温度
     */
    double CheckSystemTemperature();
    
    /**
     * @brief 检查网络连接
     */
    bool CheckNetworkConnectivity();
    
    /**
     * @brief 检查进程状态
     */
    bool CheckProcessStatus(const std::string& process_name);
    
    /**
     * @brief 更新系统健康状态
     */
    void UpdateSystemHealth();
    
    /**
     * @brief 通知健康状态变化
     */
    void NotifyHealthChange(SystemHealth new_health);
};

/**
 * @brief 错误处理器类
 * 系统错误处理的核心类，负责错误分类、恢复和告警
 */
class ErrorHandler {
public:
    /**
     * @brief 获取单例实例
     */
    static ErrorHandler& GetInstance();
    
    /**
     * @brief 析构函数
     */
    ~ErrorHandler();
    
    /**
     * @brief 初始化错误处理器
     * @param config_file_path 配置文件路径
     */
    bool Initialize(const std::string& config_file_path = "");
    
    /**
     * @brief 关闭错误处理器
     */
    void Shutdown();
    
    /**
     * @brief 报告错误
     * @param error_type 错误类型
     * @param severity 严重程度
     * @param component 发生组件
     * @param message 错误消息
     * @param details 详细信息
     * @param context 上下文信息
     * @return 错误ID
     */
    uint64_t ReportError(ErrorType error_type, ErrorSeverity severity, 
                        LogComponent component, const std::string& message,
                        const std::string& details = "",
                        const std::map<std::string, std::string>& context = {});
    
    /**
     * @brief 确认错误
     * @param error_id 错误ID
     * @param user_id 用户ID
     */
    bool AcknowledgeError(uint64_t error_id, const std::string& user_id = "");
    
    /**
     * @brief 解决错误
     * @param error_id 错误ID
     * @param resolution_details 解决详情
     */
    bool ResolveError(uint64_t error_id, const std::string& resolution_details = "");
    
    /**
     * @brief 获取错误信息
     * @param error_id 错误ID
     */
    std::shared_ptr<ErrorInfo> GetError(uint64_t error_id) const;
    
    /**
     * @brief 获取活跃错误列表
     * @param severity_filter 严重程度过滤
     */
    std::vector<std::shared_ptr<ErrorInfo>> GetActiveErrors(
        ErrorSeverity severity_filter = ErrorSeverity::LOW) const;
    
    /**
     * @brief 获取错误历史
     * @param start_time 开始时间
     * @param end_time 结束时间
     * @param max_count 最大数量
     */
    std::vector<std::shared_ptr<ErrorInfo>> GetErrorHistory(
        uint64_t start_time = 0, uint64_t end_time = UINT64_MAX, size_t max_count = 1000) const;
    
    /**
     * @brief 注册自定义恢复处理器
     * @param handler 恢复处理器
     */
    void RegisterRecoveryHandler(std::unique_ptr<IErrorRecoveryHandler> handler);
    
    /**
     * @brief 设置错误恢复配置
     * @param error_type 错误类型
     * @param config 恢复配置
     */
    void SetRecoveryConfig(ErrorType error_type, const ErrorRecoveryConfig& config);
    
    /**
     * @brief 启用/禁用自动恢复
     * @param enable 是否启用
     */
    void SetAutoRecoveryEnabled(bool enable);
    
    /**
     * @brief 获取系统健康监控器
     */
    SystemHealthMonitor& GetHealthMonitor();
    
    /**
     * @brief 触发手动恢复
     * @param error_id 错误ID
     */
    bool TriggerManualRecovery(uint64_t error_id);
    
    /**
     * @brief 获取错误统计信息
     */
    std::map<std::string, uint64_t> GetErrorStatistics() const;

private:
    ErrorHandler() = default;
    ErrorHandler(const ErrorHandler&) = delete;
    ErrorHandler& operator=(const ErrorHandler&) = delete;
    
    // 错误存储和管理
    mutable std::mutex errors_mutex_;
    std::map<uint64_t, std::shared_ptr<ErrorInfo>> active_errors_;
    std::vector<std::shared_ptr<ErrorInfo>> error_history_;
    size_t max_history_size_;
    
    // 恢复处理器管理
    std::vector<std::unique_ptr<IErrorRecoveryHandler>> recovery_handlers_;
    std::map<ErrorType, ErrorRecoveryConfig> recovery_configs_;
    std::atomic<bool> auto_recovery_enabled_;
    
    // 异步处理
    std::atomic<bool> shutdown_requested_;
    std::queue<uint64_t> recovery_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::unique_ptr<std::thread> recovery_thread_;
    
    // 系统健康监控
    std::unique_ptr<SystemHealthMonitor> health_monitor_;
    
    // 告警配置
    AlarmConfig alarm_config_;
    
    /**
     * @brief 恢复处理线程函数
     */
    void RecoveryThreadFunc();
    
    /**
     * @brief 处理错误恢复
     * @param error_id 错误ID
     */
    void ProcessErrorRecovery(uint64_t error_id);
    
    /**
     * @brief 发送告警通知
     * @param error_info 错误信息
     */
    void SendAlarmNotification(const ErrorInfo& error_info);
    
    /**
     * @brief 加载配置
     * @param config_file_path 配置文件路径
     */
    bool LoadConfig(const std::string& config_file_path);
    
    /**
     * @brief 初始化默认恢复处理器
     */
    void InitializeDefaultHandlers();
    
    /**
     * @brief 初始化默认恢复配置
     */
    void InitializeDefaultRecoveryConfigs();
    
    /**
     * @brief 清理过期的错误记录
     */
    void CleanupExpiredErrors();
};

// 便利宏定义
#define REPORT_ERROR(type, severity, component, message) \
    timing_server::core::ErrorHandler::GetInstance().ReportError( \
        type, severity, component, message, "", {})

#define REPORT_ERROR_WITH_DETAILS(type, severity, component, message, details) \
    timing_server::core::ErrorHandler::GetInstance().ReportError( \
        type, severity, component, message, details, {})

#define REPORT_ERROR_WITH_CONTEXT(type, severity, component, message, context) \
    timing_server::core::ErrorHandler::GetInstance().ReportError( \
        type, severity, component, message, "", context)

/**
 * @brief 错误类型转换为字符串
 */
std::string ErrorTypeToString(ErrorType type);

/**
 * @brief 字符串转换为错误类型
 */
ErrorType StringToErrorType(const std::string& str);

/**
 * @brief 错误严重程度转换为字符串
 */
std::string ErrorSeverityToString(ErrorSeverity severity);

/**
 * @brief 字符串转换为错误严重程度
 */
ErrorSeverity StringToErrorSeverity(const std::string& str);

/**
 * @brief 恢复策略转换为字符串
 */
std::string RecoveryStrategyToString(RecoveryStrategy strategy);

/**
 * @brief 字符串转换为恢复策略
 */
RecoveryStrategy StringToRecoveryStrategy(const std::string& str);

/**
 * @brief 错误状态转换为字符串
 */
std::string ErrorStatusToString(ErrorStatus status);

/**
 * @brief 字符串转换为错误状态
 */
ErrorStatus StringToErrorStatus(const std::string& str);

} // namespace core
} // namespace timing_server