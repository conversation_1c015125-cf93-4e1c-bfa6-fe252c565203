#pragma once

#include "types.h"
#include <string>
#include <sstream>
#include <iomanip>

namespace timing_server {
namespace core {

/**
 * @brief JSON序列化工具类
 * 提供核心数据结构的JSON序列化和反序列化功能
 */
class JsonSerializer {
public:
    /**
     * @brief 将TimeQuality结构序列化为JSON字符串
     * @param quality 时间质量结构
     * @return JSON字符串
     */
    static std::string SerializeTimeQuality(const TimeQuality& quality);
    
    /**
     * @brief 从JSON字符串反序列化TimeQuality结构
     * @param json JSON字符串
     * @return TimeQuality结构
     */
    static TimeQuality DeserializeTimeQuality(const std::string& json);
    
    /**
     * @brief 将TimeData结构序列化为JSON字符串
     * @param data 时间数据结构
     * @return JSON字符串
     */
    static std::string SerializeTimeData(const TimeData& data);
    
    /**
     * @brief 从JSON字符串反序列化TimeData结构
     * @param json JSON字符串
     * @return TimeData结构
     */
    static TimeData DeserializeTimeData(const std::string& json);
    
    /**
     * @brief 将TimeSourceInfo结构序列化为JSON字符串
     * @param info 时间源信息结构
     * @return JSON字符串
     */
    static std::string SerializeTimeSourceInfo(const TimeSourceInfo& info);
    
    /**
     * @brief 从JSON字符串反序列化TimeSourceInfo结构
     * @param json JSON字符串
     * @return TimeSourceInfo结构
     */
    static TimeSourceInfo DeserializeTimeSourceInfo(const std::string& json);
    
    /**
     * @brief 将SystemStatus结构序列化为JSON字符串
     * @param status 系统状态结构
     * @return JSON字符串
     */
    static std::string SerializeSystemStatus(const SystemStatus& status);
    
    /**
     * @brief 从JSON字符串反序列化SystemStatus结构
     * @param json JSON字符串
     * @return SystemStatus结构
     */
    static SystemStatus DeserializeSystemStatus(const std::string& json);
    
    /**
     * @brief 将TimingConfig结构序列化为JSON字符串
     * @param config 授时配置结构
     * @return JSON字符串
     */
    static std::string SerializeTimingConfig(const TimingConfig& config);
    
    /**
     * @brief 从JSON字符串反序列化TimingConfig结构
     * @param json JSON字符串
     * @return TimingConfig结构
     */
    static TimingConfig DeserializeTimingConfig(const std::string& json);

private:
    /**
     * @brief 转义JSON字符串中的特殊字符
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    static std::string EscapeJsonString(const std::string& str);
    
    /**
     * @brief 解析JSON字符串中的字符串值
     * @param json JSON字符串
     * @param key 键名
     * @return 字符串值
     */
    static std::string ParseJsonString(const std::string& json, const std::string& key);
    
    /**
     * @brief 解析JSON字符串中的数值
     * @param json JSON字符串
     * @param key 键名
     * @return 数值
     */
    static double ParseJsonNumber(const std::string& json, const std::string& key);
    
    /**
     * @brief 解析JSON字符串中的整数值
     * @param json JSON字符串
     * @param key 键名
     * @return 整数值
     */
    static uint64_t ParseJsonUint64(const std::string& json, const std::string& key);
    
    /**
     * @brief 解析JSON字符串中的布尔值
     * @param json JSON字符串
     * @param key 键名
     * @return 布尔值
     */
    static bool ParseJsonBool(const std::string& json, const std::string& key);
};

/**
 * @brief 二进制序列化工具类
 * 提供高效的二进制序列化功能，用于性能关键场景
 */
class BinarySerializer {
public:
    /**
     * @brief 将TimeData结构序列化为二进制数据
     * @param data 时间数据结构
     * @param buffer 输出缓冲区
     * @param buffer_size 缓冲区大小
     * @return 实际写入的字节数，失败返回0
     */
    static size_t SerializeTimeData(const TimeData& data, uint8_t* buffer, size_t buffer_size);
    
    /**
     * @brief 从二进制数据反序列化TimeData结构
     * @param buffer 输入缓冲区
     * @param buffer_size 缓冲区大小
     * @param data 输出的时间数据结构
     * @return 实际读取的字节数，失败返回0
     */
    static size_t DeserializeTimeData(const uint8_t* buffer, size_t buffer_size, TimeData& data);
    
    /**
     * @brief 将SystemStatus结构序列化为二进制数据
     * @param status 系统状态结构
     * @param buffer 输出缓冲区
     * @param buffer_size 缓冲区大小
     * @return 实际写入的字节数，失败返回0
     */
    static size_t SerializeSystemStatus(const SystemStatus& status, uint8_t* buffer, size_t buffer_size);
    
    /**
     * @brief 从二进制数据反序列化SystemStatus结构
     * @param buffer 输入缓冲区
     * @param buffer_size 缓冲区大小
     * @param status 输出的系统状态结构
     * @return 实际读取的字节数，失败返回0
     */
    static size_t DeserializeSystemStatus(const uint8_t* buffer, size_t buffer_size, SystemStatus& status);

private:
    /**
     * @brief 写入字符串到二进制缓冲区
     * @param buffer 缓冲区指针
     * @param str 字符串
     * @return 写入的字节数
     */
    static size_t WriteString(uint8_t*& buffer, const std::string& str);
    
    /**
     * @brief 从二进制缓冲区读取字符串
     * @param buffer 缓冲区指针
     * @param str 输出字符串
     * @return 读取的字节数
     */
    static size_t ReadString(const uint8_t*& buffer, std::string& str);
    
    /**
     * @brief 写入数值到二进制缓冲区
     * @param buffer 缓冲区指针
     * @param value 数值
     * @return 写入的字节数
     */
    template<typename T>
    static size_t WriteValue(uint8_t*& buffer, const T& value);
    
    /**
     * @brief 从二进制缓冲区读取数值
     * @param buffer 缓冲区指针
     * @param value 输出数值
     * @return 读取的字节数
     */
    template<typename T>
    static size_t ReadValue(const uint8_t*& buffer, T& value);
};

} // namespace core
} // namespace timing_server