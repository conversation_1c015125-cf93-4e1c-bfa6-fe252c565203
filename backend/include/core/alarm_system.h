#pragma once

#include "core/types.h"
#include "core/precision_monitor.h"
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <vector>
#include <queue>
#include <functional>
#include <map>
#include <chrono>

namespace timing_server {
namespace core {

/**
 * @brief 告警级别枚举
 * 定义不同严重程度的告警级别
 */
enum class AlarmLevel {
    INFO,       // 信息级别 - 一般信息通知
    WARNING,    // 警告级别 - 需要关注但不紧急
    ERROR,      // 错误级别 - 需要及时处理
    CRITICAL    // 严重级别 - 需要立即处理
};

/**
 * @brief 告警类型枚举
 * 定义不同类型的告警
 */
enum class AlarmType {
    // 精度相关告警
    ACCURACY_DEGRADED,          // 精度下降
    PHASE_OFFSET_HIGH,          // 相位偏移过大
    FREQUENCY_OFFSET_HIGH,      // 频率偏移过大
    ALLAN_DEVIATION_HIGH,       // Allan偏差过大
    
    // 信号质量告警
    GNSS_SIGNAL_WEAK,          // GNSS信号弱
    GNSS_SATELLITES_LOW,       // GNSS卫星数量不足
    GNSS_SIGNAL_LOST,          // GNSS信号丢失
    
    // 硬件相关告警
    RUBIDIUM_TEMPERATURE_HIGH,  // 铷钟温度过高
    RUBIDIUM_FAULT,            // 铷钟故障
    RTC_FAULT,                 // RTC故障
    HARDWARE_FAILURE,          // 硬件故障
    
    // 系统性能告警
    CPU_USAGE_HIGH,            // CPU使用率过高
    MEMORY_USAGE_HIGH,         // 内存使用率过高
    DISK_USAGE_HIGH,           // 磁盘使用率过高
    NETWORK_LATENCY_HIGH,      // 网络延迟过高
    
    // 状态相关告警
    STATE_TRANSITION_FAILED,    // 状态转换失败
    HOLDOVER_TIMEOUT,          // 守时超时
    CALIBRATION_REQUIRED,      // 需要校准
    MAINTENANCE_REQUIRED,      // 需要维护
    
    // 配置相关告警
    CONFIG_ERROR,              // 配置错误
    THRESHOLD_EXCEEDED,        // 阈值超限
    
    UNKNOWN_ALARM              // 未知告警
};

/**
 * @brief 告警状态枚举
 * 定义告警的处理状态
 */
enum class AlarmStatus {
    ACTIVE,         // 活跃状态 - 告警正在发生
    ACKNOWLEDGED,   // 已确认状态 - 告警已被确认但未解决
    RESOLVED,       // 已解决状态 - 告警已解决
    SUPPRESSED      // 已抑制状态 - 告警被临时抑制
};

/**
 * @brief 通知方式枚举
 * 定义不同的告警通知方式
 */
enum class NotificationMethod {
    LOG_ONLY,       // 仅记录日志
    EMAIL,          // 电子邮件通知
    SMS,            // 短信通知
    WEBHOOK,        // Webhook回调
    SNMP_TRAP,      // SNMP陷阱
    SYSLOG,         // 系统日志
    CONSOLE         // 控制台输出
};

/**
 * @brief 告警事件结构
 * 包含单个告警事件的完整信息
 */
struct AlarmEvent {
    uint64_t id;                        // 告警唯一标识
    uint64_t timestamp_ns;              // 告警时间戳（纳秒）
    AlarmType type;                     // 告警类型
    AlarmLevel level;                   // 告警级别
    AlarmStatus status;                 // 告警状态
    
    std::string title;                  // 告警标题
    std::string description;            // 告警描述
    std::string source_component;       // 源组件
    
    // 告警上下文信息
    std::map<std::string, std::string> context; // 上下文数据
    double threshold_value;             // 阈值
    double current_value;               // 当前值
    std::string unit;                   // 单位
    
    // 处理信息
    uint64_t acknowledged_time_ns;      // 确认时间
    uint64_t resolved_time_ns;          // 解决时间
    std::string acknowledged_by;        // 确认人
    std::string resolution_notes;       // 解决备注
    
    // 重复告警信息
    uint32_t occurrence_count;          // 发生次数
    uint64_t first_occurrence_ns;       // 首次发生时间
    uint64_t last_occurrence_ns;        // 最后发生时间
    
    /**
     * @brief 默认构造函数
     */
    AlarmEvent() : id(0), timestamp_ns(0), type(AlarmType::UNKNOWN_ALARM),
                   level(AlarmLevel::INFO), status(AlarmStatus::ACTIVE),
                   threshold_value(0.0), current_value(0.0),
                   acknowledged_time_ns(0), resolved_time_ns(0),
                   occurrence_count(1), first_occurrence_ns(0), last_occurrence_ns(0) {}
    
    /**
     * @brief 带参数构造函数
     */
    AlarmEvent(AlarmType alarm_type, AlarmLevel alarm_level, const std::string& alarm_title)
        : id(0), timestamp_ns(GetCurrentTimestampNs()), type(alarm_type),
          level(alarm_level), status(AlarmStatus::ACTIVE), title(alarm_title),
          threshold_value(0.0), current_value(0.0),
          acknowledged_time_ns(0), resolved_time_ns(0),
          occurrence_count(1), first_occurrence_ns(timestamp_ns), last_occurrence_ns(timestamp_ns) {}
};

/**
 * @brief 告警规则结构
 * 定义告警触发的条件和行为
 */
struct AlarmRule {
    uint64_t rule_id;                   // 规则唯一标识
    std::string rule_name;              // 规则名称
    AlarmType alarm_type;               // 告警类型
    AlarmLevel alarm_level;             // 告警级别
    bool enabled;                       // 是否启用
    
    // 触发条件
    std::string metric_name;            // 监控指标名称
    std::string condition;              // 条件表达式（如 ">", "<", "=="）
    double threshold_value;             // 阈值
    uint32_t duration_seconds;          // 持续时间（秒）
    uint32_t evaluation_interval_seconds; // 评估间隔（秒）
    
    // 抑制条件
    uint32_t suppression_duration_seconds; // 抑制持续时间
    uint32_t max_occurrences_per_hour;   // 每小时最大发生次数
    
    // 通知配置
    std::vector<NotificationMethod> notification_methods; // 通知方式
    std::vector<std::string> notification_targets;        // 通知目标
    
    // 自动处理
    bool auto_acknowledge;              // 是否自动确认
    bool auto_resolve;                  // 是否自动解决
    uint32_t auto_resolve_timeout_seconds; // 自动解决超时时间
    
    /**
     * @brief 默认构造函数
     */
    AlarmRule() : rule_id(0), alarm_type(AlarmType::UNKNOWN_ALARM),
                  alarm_level(AlarmLevel::INFO), enabled(true),
                  threshold_value(0.0), duration_seconds(0),
                  evaluation_interval_seconds(60),
                  suppression_duration_seconds(300),
                  max_occurrences_per_hour(10),
                  auto_acknowledge(false), auto_resolve(false),
                  auto_resolve_timeout_seconds(3600) {}
};

/**
 * @brief 告警统计信息结构
 * 提供告警系统的统计数据
 */
struct AlarmStatistics {
    uint64_t total_alarms;              // 总告警数
    uint64_t active_alarms;             // 活跃告警数
    uint64_t acknowledged_alarms;       // 已确认告警数
    uint64_t resolved_alarms;           // 已解决告警数
    uint64_t suppressed_alarms;         // 已抑制告警数
    
    // 按级别统计
    std::map<AlarmLevel, uint64_t> alarms_by_level;
    
    // 按类型统计
    std::map<AlarmType, uint64_t> alarms_by_type;
    
    // 时间统计
    double average_resolution_time_minutes; // 平均解决时间
    double average_acknowledgment_time_minutes; // 平均确认时间
    
    // 性能统计
    uint64_t notifications_sent;        // 发送的通知数
    uint64_t notification_failures;     // 通知失败数
    
    /**
     * @brief 默认构造函数
     */
    AlarmStatistics() : total_alarms(0), active_alarms(0), acknowledged_alarms(0),
                       resolved_alarms(0), suppressed_alarms(0),
                       average_resolution_time_minutes(0.0),
                       average_acknowledgment_time_minutes(0.0),
                       notifications_sent(0), notification_failures(0) {}
};

/**
 * @brief 告警通知接口
 * 定义告警通知的抽象接口
 */
class IAlarmNotifier {
public:
    virtual ~IAlarmNotifier() = default;
    
    /**
     * @brief 发送告警通知
     * @param alarm 告警事件
     * @param targets 通知目标列表
     * @return 发送是否成功
     */
    virtual bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) = 0;
    
    /**
     * @brief 获取通知方式
     * @return 通知方式
     */
    virtual NotificationMethod GetNotificationMethod() const = 0;
    
    /**
     * @brief 检查通知器是否可用
     * @return 是否可用
     */
    virtual bool IsAvailable() const = 0;
};

/**
 * @brief 告警系统接口
 * 定义告警系统的核心功能
 */
class IAlarmSystem {
public:
    virtual ~IAlarmSystem() = default;
    
    /**
     * @brief 启动告警系统
     * @return 启动是否成功
     */
    virtual bool Start() = 0;
    
    /**
     * @brief 停止告警系统
     * @return 停止是否成功
     */
    virtual bool Stop() = 0;
    
    /**
     * @brief 触发告警
     * @param alarm 告警事件
     * @return 告警ID
     */
    virtual uint64_t TriggerAlarm(const AlarmEvent& alarm) = 0;
    
    /**
     * @brief 确认告警
     * @param alarm_id 告警ID
     * @param acknowledged_by 确认人
     * @param notes 确认备注
     * @return 确认是否成功
     */
    virtual bool AcknowledgeAlarm(uint64_t alarm_id, const std::string& acknowledged_by, 
                                 const std::string& notes = "") = 0;
    
    /**
     * @brief 解决告警
     * @param alarm_id 告警ID
     * @param resolution_notes 解决备注
     * @return 解决是否成功
     */
    virtual bool ResolveAlarm(uint64_t alarm_id, const std::string& resolution_notes = "") = 0;
    
    /**
     * @brief 抑制告警
     * @param alarm_id 告警ID
     * @param duration_seconds 抑制持续时间
     * @return 抑制是否成功
     */
    virtual bool SuppressAlarm(uint64_t alarm_id, uint32_t duration_seconds) = 0;
    
    /**
     * @brief 获取活跃告警列表
     * @return 活跃告警列表
     */
    virtual std::vector<AlarmEvent> GetActiveAlarms() = 0;
    
    /**
     * @brief 获取历史告警列表
     * @param start_time 开始时间
     * @param end_time 结束时间
     * @param max_count 最大数量
     * @return 历史告警列表
     */
    virtual std::vector<AlarmEvent> GetHistoricalAlarms(uint64_t start_time, uint64_t end_time, 
                                                        uint32_t max_count = 1000) = 0;
    
    /**
     * @brief 获取告警统计信息
     * @return 告警统计信息
     */
    virtual AlarmStatistics GetAlarmStatistics() = 0;
    
    /**
     * @brief 添加告警规则
     * @param rule 告警规则
     * @return 规则ID
     */
    virtual uint64_t AddAlarmRule(const AlarmRule& rule) = 0;
    
    /**
     * @brief 删除告警规则
     * @param rule_id 规则ID
     * @return 删除是否成功
     */
    virtual bool RemoveAlarmRule(uint64_t rule_id) = 0;
    
    /**
     * @brief 更新告警规则
     * @param rule 告警规则
     * @return 更新是否成功
     */
    virtual bool UpdateAlarmRule(const AlarmRule& rule) = 0;
    
    /**
     * @brief 获取所有告警规则
     * @return 告警规则列表
     */
    virtual std::vector<AlarmRule> GetAlarmRules() = 0;
    
    /**
     * @brief 注册通知器
     * @param notifier 通知器实例
     */
    virtual void RegisterNotifier(std::shared_ptr<IAlarmNotifier> notifier) = 0;
    
    /**
     * @brief 设置告警回调函数
     * @param callback 回调函数
     */
    virtual void SetAlarmCallback(std::function<void(const AlarmEvent&)> callback) = 0;
};

/**
 * @brief 智能告警系统实现类
 * 实现完整的告警管理功能
 */
class SmartAlarmSystem : public IAlarmSystem {
public:
    /**
     * @brief 构造函数
     * @param precision_monitor 精度监控器实例
     */
    explicit SmartAlarmSystem(std::shared_ptr<IPrecisionMonitor> precision_monitor);
    
    /**
     * @brief 析构函数
     */
    ~SmartAlarmSystem() override;
    
    // IAlarmSystem接口实现
    bool Start() override;
    bool Stop() override;
    uint64_t TriggerAlarm(const AlarmEvent& alarm) override;
    bool AcknowledgeAlarm(uint64_t alarm_id, const std::string& acknowledged_by, 
                         const std::string& notes = "") override;
    bool ResolveAlarm(uint64_t alarm_id, const std::string& resolution_notes = "") override;
    bool SuppressAlarm(uint64_t alarm_id, uint32_t duration_seconds) override;
    std::vector<AlarmEvent> GetActiveAlarms() override;
    std::vector<AlarmEvent> GetHistoricalAlarms(uint64_t start_time, uint64_t end_time, 
                                               uint32_t max_count = 1000) override;
    AlarmStatistics GetAlarmStatistics() override;
    uint64_t AddAlarmRule(const AlarmRule& rule) override;
    bool RemoveAlarmRule(uint64_t rule_id) override;
    bool UpdateAlarmRule(const AlarmRule& rule) override;
    std::vector<AlarmRule> GetAlarmRules() override;
    void RegisterNotifier(std::shared_ptr<IAlarmNotifier> notifier) override;
    void SetAlarmCallback(std::function<void(const AlarmEvent&)> callback) override;
    
    /**
     * @brief 清除历史告警
     * @param older_than_hours 清除多少小时前的告警
     */
    void ClearHistoricalAlarms(uint32_t older_than_hours = 168); // 默认7天
    
    /**
     * @brief 导出告警数据
     * @param filename 导出文件名
     * @param format 导出格式（"csv", "json"）
     * @return 导出是否成功
     */
    bool ExportAlarmData(const std::string& filename, const std::string& format = "csv");
    
    /**
     * @brief 创建默认告警规则
     */
    void CreateDefaultAlarmRules();

private:
    /**
     * @brief 监控线程函数
     */
    void MonitoringThread();
    
    /**
     * @brief 评估告警规则
     * @param measurement 精度测量数据
     */
    void EvaluateAlarmRules(const PrecisionMeasurement& measurement);
    
    /**
     * @brief 检查单个告警规则
     * @param rule 告警规则
     * @param measurement 精度测量数据
     * @return 是否触发告警
     */
    bool CheckAlarmRule(const AlarmRule& rule, const PrecisionMeasurement& measurement);
    
    /**
     * @brief 处理告警事件
     * @param alarm 告警事件
     */
    void ProcessAlarmEvent(AlarmEvent& alarm);
    
    /**
     * @brief 发送告警通知
     * @param alarm 告警事件
     */
    void SendAlarmNotifications(const AlarmEvent& alarm);
    
    /**
     * @brief 检查告警抑制
     * @param alarm 告警事件
     * @return 是否被抑制
     */
    bool IsAlarmSuppressed(const AlarmEvent& alarm);
    
    /**
     * @brief 更新告警统计
     * @param alarm 告警事件
     */
    void UpdateAlarmStatistics(const AlarmEvent& alarm);
    
    /**
     * @brief 自动解决过期告警
     */
    void AutoResolveExpiredAlarms();
    
    /**
     * @brief 生成告警ID
     * @return 新的告警ID
     */
    uint64_t GenerateAlarmId();
    
    /**
     * @brief 获取指标值
     * @param metric_name 指标名称
     * @param measurement 测量数据
     * @return 指标值
     */
    double GetMetricValue(const std::string& metric_name, const PrecisionMeasurement& measurement);
    
    /**
     * @brief 精度测量回调函数
     * @param measurement 测量数据
     */
    void OnPrecisionMeasurement(const PrecisionMeasurement& measurement);

private:
    // 核心组件
    std::shared_ptr<IPrecisionMonitor> precision_monitor_;
    
    // 线程管理
    std::atomic<bool> running_;
    std::thread monitoring_thread_;
    std::condition_variable monitoring_cv_;
    std::mutex monitoring_mutex_;
    
    // 告警数据管理
    mutable std::mutex alarms_mutex_;
    std::map<uint64_t, AlarmEvent> active_alarms_;
    std::queue<AlarmEvent> alarm_history_;
    static constexpr size_t MAX_HISTORY_SIZE = 10000;
    
    // 告警规则管理
    mutable std::mutex rules_mutex_;
    std::map<uint64_t, AlarmRule> alarm_rules_;
    std::map<uint64_t, std::chrono::steady_clock::time_point> rule_last_triggered_;
    
    // 通知器管理
    std::vector<std::shared_ptr<IAlarmNotifier>> notifiers_;
    std::mutex notifiers_mutex_;
    
    // 统计信息
    AlarmStatistics statistics_;
    mutable std::mutex statistics_mutex_;
    
    // 回调函数
    std::function<void(const AlarmEvent&)> alarm_callback_;
    std::mutex callback_mutex_;
    
    // ID生成器
    std::atomic<uint64_t> next_alarm_id_;
    std::atomic<uint64_t> next_rule_id_;
    
    // 监控间隔
    static constexpr uint32_t MONITORING_INTERVAL_MS = 1000;
};

/**
 * @brief 告警级别转换为字符串
 * @param level 告警级别
 * @return 级别字符串
 */
inline std::string AlarmLevelToString(AlarmLevel level) {
    switch (level) {
        case AlarmLevel::INFO: return "INFO";
        case AlarmLevel::WARNING: return "WARNING";
        case AlarmLevel::ERROR: return "ERROR";
        case AlarmLevel::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 告警类型转换为字符串
 * @param type 告警类型
 * @return 类型字符串
 */
inline std::string AlarmTypeToString(AlarmType type) {
    switch (type) {
        case AlarmType::ACCURACY_DEGRADED: return "ACCURACY_DEGRADED";
        case AlarmType::PHASE_OFFSET_HIGH: return "PHASE_OFFSET_HIGH";
        case AlarmType::FREQUENCY_OFFSET_HIGH: return "FREQUENCY_OFFSET_HIGH";
        case AlarmType::GNSS_SIGNAL_WEAK: return "GNSS_SIGNAL_WEAK";
        case AlarmType::GNSS_SATELLITES_LOW: return "GNSS_SATELLITES_LOW";
        case AlarmType::RUBIDIUM_TEMPERATURE_HIGH: return "RUBIDIUM_TEMPERATURE_HIGH";
        case AlarmType::CPU_USAGE_HIGH: return "CPU_USAGE_HIGH";
        case AlarmType::MEMORY_USAGE_HIGH: return "MEMORY_USAGE_HIGH";
        case AlarmType::HOLDOVER_TIMEOUT: return "HOLDOVER_TIMEOUT";
        default: return "UNKNOWN_ALARM";
    }
}

/**
 * @brief 告警状态转换为字符串
 * @param status 告警状态
 * @return 状态字符串
 */
inline std::string AlarmStatusToString(AlarmStatus status) {
    switch (status) {
        case AlarmStatus::ACTIVE: return "ACTIVE";
        case AlarmStatus::ACKNOWLEDGED: return "ACKNOWLEDGED";
        case AlarmStatus::RESOLVED: return "RESOLVED";
        case AlarmStatus::SUPPRESSED: return "SUPPRESSED";
        default: return "UNKNOWN";
    }
}

} // namespace core
} // namespace timing_server