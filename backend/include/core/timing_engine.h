#pragma once

#include "core/types.h"
#include "core/clock_state_machine.h"
#include "core/disciplining_algorithm.h"
#include "hal/interfaces.h"
#include "hal/hal_factory.h"
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <map>
#include <vector>
#include <chrono>

namespace timing_server {
namespace core {

/**
 * @brief 授时引擎接口
 * 定义授时引擎的核心功能接口
 */
class ITimingEngine {
public:
    virtual ~ITimingEngine() = default;
    
    /**
     * @brief 启动授时引擎
     * @return 启动是否成功
     */
    virtual bool Start() = 0;
    
    /**
     * @brief 停止授时引擎
     * @return 停止是否成功
     */
    virtual bool Stop() = 0;
    
    /**
     * @brief 获取当前状态
     * @return 当前时钟状态
     */
    virtual ClockState GetCurrentState() = 0;
    
    /**
     * @brief 获取系统状态
     * @return 完整的系统状态信息
     */
    virtual SystemStatus GetSystemStatus() = 0;
    
    /**
     * @brief 配置时间源
     * @param config 时间源配置
     * @return 配置是否成功
     */
    virtual bool ConfigureTimeSource(const TimeSourcePriority& config) = 0;
    
    /**
     * @brief 获取当前活跃时间源
     * @return 当前使用的时间源类型
     */
    virtual TimeSource GetActiveTimeSource() = 0;
    
    /**
     * @brief 获取时间源信息
     * @param source 时间源类型
     * @return 时间源详细信息
     */
    virtual TimeSourceInfo GetTimeSourceInfo(TimeSource source) = 0;
    
    /**
     * @brief 获取所有时间源信息
     * @return 所有时间源信息列表
     */
    virtual std::vector<TimeSourceInfo> GetAllTimeSourceInfo() = 0;
};

/**
 * @brief 时间源管理器类
 * 负责管理和监控所有时间源的状态
 */
class TimeSourceManager {
public:
    /**
     * @brief 构造函数
     * @param hal_factory HAL工厂实例
     */
    explicit TimeSourceManager(std::shared_ptr<hal::I_HalFactory> hal_factory);
    
    /**
     * @brief 析构函数
     */
    ~TimeSourceManager();
    
    /**
     * @brief 初始化时间源管理器
     * @return 初始化是否成功
     */
    bool Initialize();
    
    /**
     * @brief 启动时间源监控
     * @return 启动是否成功
     */
    bool Start();
    
    /**
     * @brief 停止时间源监控
     * @return 停止是否成功
     */
    bool Stop();
    
    /**
     * @brief 获取时间源信息
     * @param source 时间源类型
     * @return 时间源信息
     */
    TimeSourceInfo GetTimeSourceInfo(TimeSource source);
    
    /**
     * @brief 获取所有时间源信息
     * @return 所有时间源信息列表
     */
    std::vector<TimeSourceInfo> GetAllTimeSourceInfo();
    
    /**
     * @brief 选择最佳时间源
     * @param priorities 时间源优先级配置
     * @return 选择的时间源类型
     */
    TimeSource SelectBestTimeSource(const TimeSourcePriority& priorities);
    
    /**
     * @brief 检查时间源是否可用
     * @param source 时间源类型
     * @return 是否可用
     */
    bool IsTimeSourceAvailable(TimeSource source);
    
    /**
     * @brief 设置时间源状态变化回调
     * @param callback 回调函数
     */
    void SetStatusChangeCallback(std::function<void(TimeSource, TimeSourceStatus, TimeSourceStatus)> callback);

private:
    /**
     * @brief 监控线程函数
     */
    void MonitoringThread();
    
    /**
     * @brief 更新时间源状态
     * @param source 时间源类型
     */
    void UpdateTimeSourceStatus(TimeSource source);
    
    /**
     * @brief 评估时间源质量
     * @param source 时间源类型
     * @return 时间质量指标
     */
    TimeQuality EvaluateTimeSourceQuality(TimeSource source);
    
    /**
     * @brief 通知状态变化
     * @param source 时间源类型
     * @param old_status 旧状态
     * @param new_status 新状态
     */
    void NotifyStatusChange(TimeSource source, TimeSourceStatus old_status, TimeSourceStatus new_status);

private:
    std::shared_ptr<hal::I_HalFactory> hal_factory_;
    
    // HAL设备实例
    std::unique_ptr<hal::I_GnssReceiver> gnss_receiver_;
    std::unique_ptr<hal::I_PpsInput> pps_input_;
    std::unique_ptr<hal::I_AtomicClock> atomic_clock_;
    std::unique_ptr<hal::I_FrequencyInput> frequency_input_;
    std::unique_ptr<hal::I_HighPrecisionRtc> rtc_;
    std::unique_ptr<hal::I_NetworkInterface> network_interface_;
    
    // 时间源状态管理
    mutable std::mutex sources_mutex_;
    std::map<TimeSource, TimeSourceInfo> time_sources_;
    
    // 监控线程管理
    std::atomic<bool> running_;
    std::thread monitoring_thread_;
    std::condition_variable monitoring_cv_;
    std::mutex monitoring_mutex_;
    
    // 状态变化回调
    std::function<void(TimeSource, TimeSourceStatus, TimeSourceStatus)> status_change_callback_;
    
    // 监控间隔（毫秒）
    static constexpr uint32_t MONITORING_INTERVAL_MS = 1000;
};

/**
 * @brief 授时引擎主控制器类
 * 协调所有时间源和状态机，实现完整的授时功能
 */
class TimingEngine : public ITimingEngine, public IStateMachineListener {
public:
    /**
     * @brief 构造函数
     * @param config 授时配置
     */
    explicit TimingEngine(const TimingConfig& config = TimingConfig{});
    
    /**
     * @brief 析构函数
     */
    ~TimingEngine() override;
    
    // ITimingEngine接口实现
    bool Start() override;
    bool Stop() override;
    ClockState GetCurrentState() override;
    SystemStatus GetSystemStatus() override;
    bool ConfigureTimeSource(const TimeSourcePriority& config) override;
    TimeSource GetActiveTimeSource() override;
    TimeSourceInfo GetTimeSourceInfo(TimeSource source) override;
    std::vector<TimeSourceInfo> GetAllTimeSourceInfo() override;
    
    // IStateMachineListener接口实现
    void OnStateEntered(ClockState state, ClockState previous_state) override;
    void OnStateExited(ClockState state, ClockState next_state) override;
    void OnEventProcessed(const StateMachineEvent& event, ClockState current_state) override;
    void OnTransitionFailed(const StateMachineEvent& event, ClockState current_state, TransitionResult result) override;
    
    /**
     * @brief 设置配置
     * @param config 新的授时配置
     * @return 设置是否成功
     */
    bool SetConfiguration(const TimingConfig& config);
    
    /**
     * @brief 获取配置
     * @return 当前授时配置
     */
    const TimingConfig& GetConfiguration() const;
    
    /**
     * @brief 获取性能指标
     * @return 性能指标结构
     */
    PerformanceMetrics GetPerformanceMetrics() const;
    
    /**
     * @brief 手动触发状态机事件
     * @param event 要触发的事件
     * @return 事件处理是否成功
     */
    bool TriggerEvent(ClockEvent event);
    
    /**
     * @brief 强制切换时间源
     * @param source 目标时间源
     * @return 切换是否成功
     */
    bool ForceTimeSourceSwitch(TimeSource source);
    
    /**
     * @brief 获取运行时间
     * @return 运行时间（秒）
     */
    uint64_t GetUptime() const;
    
    /**
     * @brief 检查系统健康状况
     * @return 系统健康状态
     */
    SystemHealth CheckSystemHealth() const;
    
    /**
     * @brief 获取驯服状态
     * @return 驯服状态信息
     */
    DiscipliningStatus GetDiscipliningStatus() const;
    
    /**
     * @brief 获取铷钟学习数据
     * @return 铷钟学习数据
     */
    RubidiumLearningData GetRubidiumLearningData() const;
    
    /**
     * @brief 获取守时预测
     * @return 守时预测数据
     */
    HoldoverPrediction GetHoldoverPrediction() const;
    
    /**
     * @brief 获取频率校正值
     * @return 当前频率校正值（ppm）
     */
    double GetFrequencyCorrection() const;
    
    /**
     * @brief 获取相位校正值
     * @return 当前相位校正值（纳秒）
     */
    double GetPhaseCorrection() const;

private:
    /**
     * @brief 初始化授时引擎
     * @return 初始化是否成功
     */
    bool Initialize();
    
    /**
     * @brief 主控制循环
     */
    void ControlLoop();
    
    /**
     * @brief 处理时间源状态变化
     * @param source 时间源类型
     * @param old_status 旧状态
     * @param new_status 新状态
     */
    void HandleTimeSourceStatusChange(TimeSource source, TimeSourceStatus old_status, TimeSourceStatus new_status);
    
    /**
     * @brief 执行时间源切换
     * @param new_source 新的时间源
     * @return 切换是否成功
     */
    bool SwitchTimeSource(TimeSource new_source);
    
    /**
     * @brief 更新系统状态
     */
    void UpdateSystemStatus();
    
    /**
     * @brief 处理FREE_RUN状态逻辑
     */
    void HandleFreeRunState();
    
    /**
     * @brief 处理DISCIPLINING状态逻辑
     */
    void HandleDiscipliningState();
    
    /**
     * @brief 处理LOCKED状态逻辑
     */
    void HandleLockedState();
    
    /**
     * @brief 处理HOLDOVER状态逻辑
     */
    void HandleHoldoverState();
    
    /**
     * @brief 检查GNSS信号状态
     * @return GNSS信号是否有效
     */
    bool CheckGnssSignal();
    
    /**
     * @brief 检查驯服收敛状态
     * @return 是否已收敛
     */
    bool CheckDiscipliningConvergence();
    
    /**
     * @brief 检查守时超时
     * @return 是否超时
     */
    bool CheckHoldoverTimeout();
    
    /**
     * @brief 记录性能指标
     */
    void RecordPerformanceMetrics();
    
    /**
     * @brief 计算系统资源使用情况
     */
    void CalculateResourceUsage();

private:
    // 核心组件
    std::unique_ptr<ClockStateMachine> state_machine_;
    std::unique_ptr<TimeSourceManager> time_source_manager_;
    std::unique_ptr<ClockDiscipliningManager> disciplining_manager_;
    std::shared_ptr<hal::I_HalFactory> hal_factory_;
    
    // 配置和状态
    TimingConfig config_;
    SystemStatus system_status_;
    TimeSource active_time_source_;
    
    // 线程管理
    std::atomic<bool> running_;
    std::thread control_thread_;
    mutable std::mutex status_mutex_;
    std::condition_variable control_cv_;
    std::mutex control_mutex_;
    
    // 性能监控
    PerformanceMetrics performance_metrics_;
    std::chrono::steady_clock::time_point start_time_;
    mutable std::mutex metrics_mutex_;
    
    // 控制循环间隔（毫秒）
    static constexpr uint32_t CONTROL_LOOP_INTERVAL_MS = 100;
    
    // 系统版本信息
    static constexpr const char* VERSION = "1.0.0";
};

} // namespace core
} // namespace timing_server