#pragma once

#include "core/types.h"
#include <memory>

// Forward declarations
namespace timing_server {
namespace core {
class ITimingEngine;
}
}
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <vector>
#include <queue>
#include <functional>
#include <chrono>
#include <map>

namespace timing_server {
namespace core {

/**
 * @brief 精度监控级别枚举
 * 定义不同级别的精度监控
 */
enum class MonitoringLevel {
    BASIC,      // 基础监控 - 基本精度指标
    STANDARD,   // 标准监控 - 包含趋势分析
    ADVANCED,   // 高级监控 - 包含预测分析
    DIAGNOSTIC  // 诊断监控 - 最详细的监控信息
};

/**
 * @brief 精度测量数据结构
 * 包含单次精度测量的完整信息
 */
struct PrecisionMeasurement {
    uint64_t timestamp_ns;              // 测量时间戳（纳秒）
    TimeSource source;                  // 时间源类型
    ClockState system_state;            // 系统状态
    
    // 精度指标
    double absolute_accuracy_ns;        // 绝对精度（纳秒）
    double phase_offset_ns;             // 相位偏移（纳秒）
    double frequency_offset_ppm;        // 频率偏移（ppm）
    double allan_deviation_1s;          // 1秒Allan偏差
    double allan_deviation_10s;         // 10秒Allan偏差
    double allan_deviation_100s;        // 100秒Allan偏差
    
    // 信号质量指标
    uint32_t gnss_satellites;           // GNSS卫星数量
    double gnss_snr_db;                 // GNSS信噪比（dB）
    double rubidium_temperature;        // 铷钟温度（摄氏度）
    double system_temperature;          // 系统温度（摄氏度）
    
    // 系统性能指标
    double cpu_usage_percent;           // CPU使用率
    uint64_t memory_usage_mb;           // 内存使用量（MB）
    uint32_t network_latency_us;        // 网络延迟（微秒）
    
    // 质量评分
    uint32_t overall_quality_score;     // 总体质量评分（0-100）
    bool meets_spec_requirements;       // 是否满足规格要求
    
    /**
     * @brief 默认构造函数
     */
    PrecisionMeasurement() : timestamp_ns(0), source(TimeSource::SYSTEM_CLOCK),
                           system_state(ClockState::FREE_RUN), absolute_accuracy_ns(0.0),
                           phase_offset_ns(0.0), frequency_offset_ppm(0.0),
                           allan_deviation_1s(0.0), allan_deviation_10s(0.0),
                           allan_deviation_100s(0.0), gnss_satellites(0),
                           gnss_snr_db(0.0), rubidium_temperature(0.0),
                           system_temperature(0.0), cpu_usage_percent(0.0),
                           memory_usage_mb(0), network_latency_us(0),
                           overall_quality_score(0), meets_spec_requirements(false) {}
};

/**
 * @brief 精度趋势分析数据
 * 包含历史数据的趋势分析结果
 */
struct PrecisionTrend {
    uint64_t analysis_period_hours;     // 分析周期（小时）
    
    // 精度趋势
    double accuracy_trend_ns_per_hour;  // 精度趋势（纳秒/小时）
    double frequency_drift_ppm_per_hour; // 频率漂移趋势（ppm/小时）
    double stability_trend_coefficient; // 稳定性趋势系数
    
    // 统计数据
    double mean_accuracy_ns;            // 平均精度
    double std_deviation_accuracy_ns;   // 精度标准差
    double min_accuracy_ns;             // 最小精度
    double max_accuracy_ns;             // 最大精度
    
    // 预测数据
    double predicted_accuracy_1h_ns;    // 1小时后预测精度
    double predicted_accuracy_24h_ns;   // 24小时后预测精度
    double confidence_level;            // 预测置信度（0-1）
    
    // 健康评估
    bool trend_is_stable;               // 趋势是否稳定
    bool within_specification;          // 是否在规格范围内
    std::vector<std::string> recommendations; // 优化建议
};

/**
 * @brief 系统健康评分结构
 * 提供系统整体健康状况的量化评估
 */
struct SystemHealthScore {
    uint32_t overall_score;             // 总体评分（0-100）
    
    // 分项评分
    uint32_t timing_accuracy_score;     // 时间精度评分
    uint32_t signal_quality_score;      // 信号质量评分
    uint32_t system_stability_score;    // 系统稳定性评分
    uint32_t hardware_health_score;     // 硬件健康评分
    uint32_t performance_score;         // 性能评分
    
    // 健康状态
    SystemHealth health_status;         // 健康状态
    std::vector<std::string> issues;    // 发现的问题
    std::vector<std::string> warnings;  // 警告信息
    std::vector<std::string> suggestions; // 改进建议
    
    // 维护建议
    bool needs_calibration;             // 是否需要校准
    bool needs_maintenance;             // 是否需要维护
    uint32_t days_until_maintenance;    // 距离维护的天数
    
    /**
     * @brief 默认构造函数
     */
    SystemHealthScore() : overall_score(0), timing_accuracy_score(0),
                         signal_quality_score(0), system_stability_score(0),
                         hardware_health_score(0), performance_score(0),
                         health_status(SystemHealth::ERROR),
                         needs_calibration(false), needs_maintenance(false),
                         days_until_maintenance(0) {}
};

/**
 * @brief 预测性维护建议结构
 * 基于历史数据和趋势分析提供维护建议
 */
struct PredictiveMaintenanceAdvice {
    uint64_t analysis_timestamp_ns;     // 分析时间戳
    
    // 维护预测
    uint32_t estimated_days_to_failure; // 预计故障天数
    double failure_probability;        // 故障概率（0-1）
    std::string primary_concern;       // 主要关注点
    
    // 组件状态预测
    struct ComponentPrediction {
        std::string component_name;     // 组件名称
        uint32_t health_percentage;     // 健康百分比
        uint32_t estimated_lifetime_days; // 预计寿命（天）
        std::string maintenance_action; // 建议维护动作
        bool urgent;                   // 是否紧急
    };
    
    std::vector<ComponentPrediction> component_predictions; // 组件预测列表
    
    // 维护建议
    std::vector<std::string> immediate_actions;  // 立即行动建议
    std::vector<std::string> scheduled_actions;  // 计划行动建议
    std::vector<std::string> preventive_actions; // 预防性行动建议
    
    // 性能优化建议
    std::vector<std::string> performance_optimizations; // 性能优化建议
    std::vector<std::string> configuration_changes;     // 配置更改建议
};

/**
 * @brief 精度监控接口
 * 定义精度监控系统的核心功能
 */
class IPrecisionMonitor {
public:
    virtual ~IPrecisionMonitor() = default;
    
    /**
     * @brief 启动精度监控
     * @return 启动是否成功
     */
    virtual bool Start() = 0;
    
    /**
     * @brief 停止精度监控
     * @return 停止是否成功
     */
    virtual bool Stop() = 0;
    
    /**
     * @brief 设置监控级别
     * @param level 监控级别
     */
    virtual void SetMonitoringLevel(MonitoringLevel level) = 0;
    
    /**
     * @brief 获取当前精度测量
     * @return 当前精度测量数据
     */
    virtual PrecisionMeasurement GetCurrentMeasurement() = 0;
    
    /**
     * @brief 获取历史测量数据
     * @param start_time 开始时间（纳秒）
     * @param end_time 结束时间（纳秒）
     * @param max_samples 最大样本数
     * @return 历史测量数据列表
     */
    virtual std::vector<PrecisionMeasurement> GetHistoricalMeasurements(
        uint64_t start_time, uint64_t end_time, uint32_t max_samples = 1000) = 0;
    
    /**
     * @brief 获取精度趋势分析
     * @param period_hours 分析周期（小时）
     * @return 趋势分析结果
     */
    virtual PrecisionTrend GetPrecisionTrend(uint32_t period_hours = 24) = 0;
    
    /**
     * @brief 获取系统健康评分
     * @return 系统健康评分
     */
    virtual SystemHealthScore GetSystemHealthScore() = 0;
    
    /**
     * @brief 获取预测性维护建议
     * @return 维护建议
     */
    virtual PredictiveMaintenanceAdvice GetMaintenanceAdvice() = 0;
    
    /**
     * @brief 检查是否满足精度要求
     * @param required_accuracy_ns 要求的精度（纳秒）
     * @return 是否满足要求
     */
    virtual bool MeetsAccuracyRequirement(double required_accuracy_ns) = 0;
    
    /**
     * @brief 设置测量回调函数
     * @param callback 回调函数
     */
    virtual void SetMeasurementCallback(std::function<void(const PrecisionMeasurement&)> callback) = 0;
};

/**
 * @brief 精度监控实现类
 * 实现完整的精度监控功能
 */
class PrecisionMonitor : public IPrecisionMonitor {
public:
    /**
     * @brief 构造函数
     * @param timing_engine 授时引擎实例
     * @param config 告警阈值配置
     */
    explicit PrecisionMonitor(std::shared_ptr<ITimingEngine> timing_engine,
                             const AlarmThresholds& config = AlarmThresholds{});
    
    /**
     * @brief 析构函数
     */
    ~PrecisionMonitor() override;
    
    // IPrecisionMonitor接口实现
    bool Start() override;
    bool Stop() override;
    void SetMonitoringLevel(MonitoringLevel level) override;
    PrecisionMeasurement GetCurrentMeasurement() override;
    std::vector<PrecisionMeasurement> GetHistoricalMeasurements(
        uint64_t start_time, uint64_t end_time, uint32_t max_samples = 1000) override;
    PrecisionTrend GetPrecisionTrend(uint32_t period_hours = 24) override;
    SystemHealthScore GetSystemHealthScore() override;
    PredictiveMaintenanceAdvice GetMaintenanceAdvice() override;
    bool MeetsAccuracyRequirement(double required_accuracy_ns) override;
    void SetMeasurementCallback(std::function<void(const PrecisionMeasurement&)> callback) override;
    
    /**
     * @brief 设置告警阈值配置
     * @param config 新的告警阈值配置
     */
    void SetAlarmThresholds(const AlarmThresholds& config);
    
    /**
     * @brief 获取告警阈值配置
     * @return 当前告警阈值配置
     */
    const AlarmThresholds& GetAlarmThresholds() const;
    
    /**
     * @brief 强制执行一次测量
     * @return 测量结果
     */
    PrecisionMeasurement ForceMeasurement();
    
    /**
     * @brief 清除历史数据
     * @param older_than_hours 清除多少小时前的数据
     */
    void ClearHistoricalData(uint32_t older_than_hours = 168); // 默认7天
    
    /**
     * @brief 导出测量数据
     * @param filename 导出文件名
     * @param format 导出格式（"csv", "json"）
     * @return 导出是否成功
     */
    bool ExportMeasurementData(const std::string& filename, const std::string& format = "csv");

private:
    /**
     * @brief 监控线程函数
     */
    void MonitoringThread();
    
    /**
     * @brief 执行精度测量
     * @return 测量结果
     */
    PrecisionMeasurement PerformMeasurement();
    
    /**
     * @brief 计算Allan偏差
     * @param measurements 测量数据
     * @param tau_seconds 积分时间（秒）
     * @return Allan偏差值
     */
    double CalculateAllanDeviation(const std::vector<PrecisionMeasurement>& measurements, 
                                  uint32_t tau_seconds);
    
    /**
     * @brief 分析精度趋势
     * @param measurements 测量数据
     * @param period_hours 分析周期
     * @return 趋势分析结果
     */
    PrecisionTrend AnalyzePrecisionTrend(const std::vector<PrecisionMeasurement>& measurements,
                                        uint32_t period_hours);
    
    /**
     * @brief 计算系统健康评分
     * @param current_measurement 当前测量数据
     * @return 健康评分
     */
    SystemHealthScore CalculateHealthScore(const PrecisionMeasurement& current_measurement);
    
    /**
     * @brief 生成预测性维护建议
     * @param trend 趋势分析数据
     * @param health_score 健康评分
     * @return 维护建议
     */
    PredictiveMaintenanceAdvice GenerateMaintenanceAdvice(const PrecisionTrend& trend,
                                                         const SystemHealthScore& health_score);
    
    /**
     * @brief 获取系统资源使用情况
     * @param cpu_usage CPU使用率输出
     * @param memory_usage 内存使用量输出
     */
    void GetSystemResourceUsage(double& cpu_usage, uint64_t& memory_usage);
    
    /**
     * @brief 获取CPU使用率
     * @return CPU使用率百分比
     */
    double GetCpuUsage();
    
    /**
     * @brief 获取内存使用量
     * @return 内存使用量（MB）
     */
    uint64_t GetMemoryUsage();
    
    /**
     * @brief 获取网络延迟
     * @return 网络延迟（微秒）
     */
    uint32_t GetNetworkLatency();
    
    /**
     * @brief 存储测量数据
     * @param measurement 测量数据
     */
    void StoreMeasurement(const PrecisionMeasurement& measurement);
    
    /**
     * @brief 通知测量回调
     * @param measurement 测量数据
     */
    void NotifyMeasurementCallback(const PrecisionMeasurement& measurement);

private:
    // 核心组件
    std::shared_ptr<ITimingEngine> timing_engine_;
    AlarmThresholds alarm_thresholds_;
    MonitoringLevel monitoring_level_;
    
    // 系统资源监控
    std::unique_ptr<class CachedSystemResourceMonitor> system_resource_monitor_;
    
    // 线程管理
    std::atomic<bool> running_;
    std::thread monitoring_thread_;
    std::condition_variable monitoring_cv_;
    std::mutex monitoring_mutex_;
    
    // 数据存储
    mutable std::mutex measurements_mutex_;
    std::queue<PrecisionMeasurement> measurement_history_;
    static constexpr size_t MAX_HISTORY_SIZE = 86400; // 24小时，每秒一个测量
    
    // 回调函数
    std::function<void(const PrecisionMeasurement&)> measurement_callback_;
    std::mutex callback_mutex_;
    
    // 监控间隔
    std::chrono::milliseconds monitoring_interval_;
    
    // 性能统计
    uint64_t total_measurements_;
    uint64_t failed_measurements_;
    std::chrono::steady_clock::time_point start_time_;
    
    // 默认监控间隔（毫秒）
    static constexpr uint32_t DEFAULT_MONITORING_INTERVAL_MS = 1000;
    
    // 精度要求常量
    static constexpr double SPEC_ACCURACY_LOCKED_NS = 50.0;     // GNSS锁定状态精度要求
    static constexpr double SPEC_ACCURACY_HOLDOVER_NS = 1000.0; // 守时状态精度要求
};

} // namespace core
} // namespace timing_server