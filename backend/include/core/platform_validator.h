#pragma once

#include "hal/platform_detector.h"
#include "hal/hal_factory.h"
#include "core/types.h"
#include <memory>
#include <vector>
#include <string>
#include <chrono>
#include <functional>
#include <map>

namespace timing_server {
namespace core {

/**
 * @brief 平台验证结果类型
 */
enum class ValidationResultType {
    SUCCESS,        // 验证成功
    WARNING,        // 警告（功能可用但有限制）
    ERROR,          // 错误（功能不可用）
    CRITICAL        // 严重错误（平台不兼容）
};

/**
 * @brief 单项验证结果
 */
struct ValidationItem {
    std::string test_name;              // 测试名称
    ValidationResultType result_type;   // 结果类型
    std::string message;                // 结果消息
    std::string details;                // 详细信息
    double execution_time_ms;           // 执行时间（毫秒）
    std::map<std::string, std::string> metrics; // 性能指标
};

/**
 * @brief 平台验证报告
 */
struct PlatformValidationReport {
    hal::PlatformInfo platform_info;           // 平台信息
    std::vector<ValidationItem> validation_items; // 验证项目
    bool overall_success;                       // 总体验证结果
    double total_execution_time_ms;             // 总执行时间
    std::string summary;                        // 验证摘要
    std::vector<std::string> recommendations;   // 优化建议
    
    // 统计信息
    size_t success_count = 0;
    size_t warning_count = 0;
    size_t error_count = 0;
    size_t critical_count = 0;
};

/**
 * @brief 性能基准测试结果
 */
struct PerformanceBenchmark {
    std::string benchmark_name;         // 基准测试名称
    double average_latency_ns;          // 平均延迟（纳秒）
    double max_latency_ns;              // 最大延迟（纳秒）
    double min_latency_ns;              // 最小延迟（纳秒）
    double throughput_ops_per_sec;      // 吞吐量（操作/秒）
    double cpu_usage_percent;           // CPU使用率
    double memory_usage_mb;             // 内存使用量（MB）
    std::map<std::string, double> custom_metrics; // 自定义指标
};

/**
 * @brief 平台性能基准报告
 */
struct PlatformBenchmarkReport {
    hal::PlatformInfo platform_info;           // 平台信息
    std::vector<PerformanceBenchmark> benchmarks; // 基准测试结果
    std::string performance_grade;              // 性能等级（A/B/C/D）
    std::vector<std::string> optimization_tips; // 优化建议
    double overall_score;                       // 总体评分（0-100）
};

/**
 * @brief 平台验证器类
 * 负责验证不同平台的功能完整性
 */
class PlatformValidator {
public:
    /**
     * @brief 构造函数
     * @param hal_factory HAL工厂实例
     */
    explicit PlatformValidator(std::shared_ptr<hal::I_HalFactory> hal_factory);
    
    /**
     * @brief 执行完整的平台验证
     * @return 平台验证报告
     */
    PlatformValidationReport ValidatePlatform();
    
    /**
     * @brief 验证Linux x86_64平台功能
     * @return 验证结果
     */
    std::vector<ValidationItem> ValidateLinuxX86_64();
    
    /**
     * @brief 验证龙芯LoongArch64平台功能
     * @return 验证结果
     */
    std::vector<ValidationItem> ValidateLoongArch64();
    
    /**
     * @brief 验证macOS开发环境Mock HAL功能
     * @return 验证结果
     */
    std::vector<ValidationItem> ValidateMacOSMockHal();
    
    /**
     * @brief 执行性能基准测试
     * @return 平台性能基准报告
     */
    PlatformBenchmarkReport RunBenchmarks();
    
    /**
     * @brief 生成平台特定的优化建议
     * @param platform_type 平台类型
     * @return 优化建议列表
     */
    std::vector<std::string> GenerateOptimizationRecommendations(hal::PlatformType platform_type);

private:
    std::shared_ptr<hal::I_HalFactory> hal_factory_;
    hal::PlatformInfo platform_info_;
    
    // 基础验证测试
    ValidationItem ValidateHalFactoryCreation();
    ValidationItem ValidateGnssReceiver();
    ValidationItem ValidatePpsInput();
    ValidationItem ValidateAtomicClock();
    ValidationItem ValidateFrequencyInput();
    ValidationItem ValidateRtc();
    ValidationItem ValidateNetworkInterface();
    
    // 平台特定验证测试
    ValidationItem ValidateLinuxDeviceAccess();
    ValidationItem ValidateLinuxPermissions();
    ValidationItem ValidateLinuxKernelFeatures();
    ValidationItem ValidateLoongArchOptimizations();
    ValidationItem ValidateMockDataSources();
    ValidationItem ValidateMacOSDevelopmentFeatures();
    
    // 性能基准测试
    PerformanceBenchmark BenchmarkTimeStampAccuracy();
    PerformanceBenchmark BenchmarkMemoryUsage();
    PerformanceBenchmark BenchmarkCpuPerformance();
    PerformanceBenchmark BenchmarkIoThroughput();
    PerformanceBenchmark BenchmarkNetworkLatency();
    
    // 辅助函数
    double MeasureExecutionTime(std::function<void()> func);
    ValidationItem MeasureValidationTime(std::function<ValidationItem()> func);
    ValidationItem CreateValidationItem(const std::string& test_name, 
                                      ValidationResultType result_type,
                                      const std::string& message,
                                      const std::string& details = "");
    std::string GetPerformanceGrade(double score);
    void UpdateStatistics(PlatformValidationReport& report);
};

/**
 * @brief 平台性能基准测试器
 * 专门负责性能基准测试
 */
class PlatformBenchmark {
public:
    /**
     * @brief 构造函数
     * @param hal_factory HAL工厂实例
     */
    explicit PlatformBenchmark(std::shared_ptr<hal::I_HalFactory> hal_factory);

private:
    std::shared_ptr<hal::I_HalFactory> hal_factory_;
    hal::PlatformInfo platform_info_;
    
    // 性能基准测试
    PerformanceBenchmark BenchmarkTimeStampAccuracy();
    PerformanceBenchmark BenchmarkMemoryUsage();
    PerformanceBenchmark BenchmarkCpuPerformance();
    PerformanceBenchmark BenchmarkIoThroughput();
    PerformanceBenchmark BenchmarkNetworkLatency();
    
    std::string GetPerformanceGrade(double score);
};

/**
 * @brief 平台兼容性测试套件
 * 提供完整的多平台兼容性验证功能
 */
class PlatformCompatibilityTestSuite {
public:
    /**
     * @brief 运行完整的兼容性测试
     * @return 是否所有测试都通过
     */
    static bool RunFullCompatibilityTest();
    
    /**
     * @brief 生成兼容性测试报告
     * @param output_file 输出文件路径
     * @return 是否成功生成报告
     */
    static bool GenerateCompatibilityReport(const std::string& output_file);
    
    /**
     * @brief 验证交叉编译兼容性
     * @return 验证结果
     */
    static bool ValidateCrossCompilation();
    
    /**
     * @brief 验证运行时稳定性
     * @param duration_minutes 测试持续时间（分钟）
     * @return 稳定性测试结果
     */
    static bool ValidateRuntimeStability(int duration_minutes = 30);

private:
    static void PrintValidationReport(const PlatformValidationReport& report);
    static void PrintBenchmarkReport(const PlatformBenchmarkReport& report);
    static bool SaveReportToFile(const std::string& content, const std::string& filename);
};

} // namespace core
} // namespace timing_server