#pragma once

#include <string>
#include <memory>
#include <mutex>
#include <fstream>
#include <vector>
#include <map>
#include <functional>
#include <atomic>
#include <thread>
#include <queue>
#include <condition_variable>
#include <chrono>
#include <sstream>
#include <iomanip>

namespace timing_server {
namespace core {

/**
 * @brief 日志级别枚举
 * 定义系统支持的日志级别，按严重程度递增
 */
enum class LogLevel {
    TRACE = 0,      // 跟踪级别 - 最详细的调试信息
    DEBUG = 1,      // 调试级别 - 调试信息
    INFO = 2,       // 信息级别 - 一般信息
    WARNING = 3,    // 警告级别 - 警告信息
    ERROR = 4,      // 错误级别 - 错误信息
    CRITICAL = 5    // 严重级别 - 严重错误信息
};

/**
 * @brief 日志组件枚举
 * 定义系统中的各个组件，用于日志分类
 */
enum class LogComponent {
    SYSTEM,         // 系统组件
    TIMING_ENGINE,  // 授时引擎
    STATE_MACHINE,  // 状态机
    HAL_GNSS,       // GNSS硬件抽象层
    HAL_RUBIDIUM,   // 铷钟硬件抽象层
    HAL_RTC,        // RTC硬件抽象层
    HAL_PPS,        // PPS硬件抽象层
    HAL_FREQ,       // 频率输入硬件抽象层
    HAL_NETWORK,    // 网络接口硬件抽象层
    DAEMON_MANAGER, // 守护进程管理器
    CONFIG_MANAGER, // 配置管理器
    API_SERVER,     // API服务器
    WEBSOCKET,      // WebSocket服务
    AUTH_MANAGER,   // 认证管理器
    PTP4L,          // ptp4l守护进程
    CHRONY,         // chrony守护进程
    TS2PHC          // ts2phc守护进程
};

/**
 * @brief 日志条目结构
 * 包含单条日志记录的完整信息
 */
struct LogEntry {
    uint64_t timestamp_ns;              // 时间戳（纳秒）
    LogLevel level;                     // 日志级别
    LogComponent component;             // 组件标识
    std::string message;                // 日志消息
    std::string file;                   // 源文件名
    int line;                          // 源文件行号
    std::string function;              // 函数名
    std::map<std::string, std::string> context; // 上下文信息
    
    /**
     * @brief 默认构造函数
     */
    LogEntry() : timestamp_ns(0), level(LogLevel::INFO), component(LogComponent::SYSTEM), line(0) {}
    
    /**
     * @brief 构造函数
     */
    LogEntry(LogLevel lvl, LogComponent comp, const std::string& msg, 
             const std::string& file_name = "", int line_num = 0, const std::string& func_name = "")
        : level(lvl), component(comp), message(msg), file(file_name), line(line_num), function(func_name) {
        timestamp_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::high_resolution_clock::now().time_since_epoch()).count();
    }
};

/**
 * @brief 日志过滤器结构
 * 用于日志搜索和过滤功能
 */
struct LogFilter {
    LogLevel min_level = LogLevel::TRACE;       // 最小日志级别
    LogLevel max_level = LogLevel::CRITICAL;    // 最大日志级别
    std::vector<LogComponent> components;       // 组件过滤列表（空表示所有组件）
    uint64_t start_time_ns = 0;                // 开始时间（纳秒）
    uint64_t end_time_ns = UINT64_MAX;         // 结束时间（纳秒）
    std::string message_pattern;               // 消息模式匹配
    size_t max_results = 1000;                 // 最大结果数量
    
    /**
     * @brief 检查日志条目是否匹配过滤条件
     */
    bool Matches(const LogEntry& entry) const;
};

/**
 * @brief 日志轮转配置
 * 定义日志文件轮转和压缩策略
 */
struct LogRotationConfig {
    size_t max_file_size_mb = 100;             // 单个日志文件最大大小（MB）
    size_t max_files = 10;                     // 保留的日志文件数量
    bool enable_compression = true;             // 是否启用压缩
    std::string compression_format = "gzip";    // 压缩格式
    uint32_t rotation_check_interval_s = 60;   // 轮转检查间隔（秒）
};

/**
 * @brief 日志输出接口
 * 定义日志输出的抽象接口
 */
class ILogOutput {
public:
    virtual ~ILogOutput() = default;
    
    /**
     * @brief 写入日志条目
     * @param entry 日志条目
     */
    virtual void WriteLog(const LogEntry& entry) = 0;
    
    /**
     * @brief 刷新输出缓冲区
     */
    virtual void Flush() = 0;
    
    /**
     * @brief 关闭输出
     */
    virtual void Close() = 0;
};

/**
 * @brief 文件日志输出实现
 * 将日志写入文件，支持轮转和压缩
 */
class FileLogOutput : public ILogOutput {
public:
    /**
     * @brief 构造函数
     * @param file_path 日志文件路径
     * @param rotation_config 轮转配置
     */
    FileLogOutput(const std::string& file_path, const LogRotationConfig& rotation_config);
    
    /**
     * @brief 析构函数
     */
    ~FileLogOutput() override;
    
    // ILogOutput接口实现
    void WriteLog(const LogEntry& entry) override;
    void Flush() override;
    void Close() override;
    
    /**
     * @brief 获取当前日志文件大小
     */
    size_t GetCurrentFileSize() const;
    
    /**
     * @brief 手动触发日志轮转
     */
    void RotateNow();

private:
    std::string file_path_;                     // 日志文件路径
    LogRotationConfig rotation_config_;         // 轮转配置
    std::unique_ptr<std::ofstream> file_stream_; // 文件流
    mutable std::mutex file_mutex_;             // 文件操作互斥锁
    std::atomic<bool> shutdown_requested_;      // 关闭请求标志
    std::unique_ptr<std::thread> rotation_thread_; // 轮转检查线程
    
    /**
     * @brief 检查是否需要轮转
     */
    bool NeedsRotation() const;
    
    /**
     * @brief 执行日志轮转
     */
    void PerformRotation();
    
    /**
     * @brief 压缩旧日志文件
     */
    void CompressOldFiles();
    
    /**
     * @brief 轮转检查线程函数
     */
    void RotationThreadFunc();
    
    /**
     * @brief 格式化日志条目为字符串
     */
    std::string FormatLogEntry(const LogEntry& entry) const;
};

/**
 * @brief 控制台日志输出实现
 * 将日志输出到控制台，支持颜色显示
 */
class ConsoleLogOutput : public ILogOutput {
public:
    /**
     * @brief 构造函数
     * @param enable_colors 是否启用颜色显示
     */
    explicit ConsoleLogOutput(bool enable_colors = true);
    
    // ILogOutput接口实现
    void WriteLog(const LogEntry& entry) override;
    void Flush() override;
    void Close() override;

private:
    bool enable_colors_;                        // 是否启用颜色
    mutable std::mutex console_mutex_;          // 控制台输出互斥锁
    
    /**
     * @brief 获取日志级别对应的颜色代码
     */
    std::string GetColorCode(LogLevel level) const;
    
    /**
     * @brief 格式化日志条目为彩色字符串
     */
    std::string FormatLogEntry(const LogEntry& entry) const;
};

/**
 * @brief 系统日志输出实现
 * 将日志输出到系统日志（syslog）
 */
class SyslogOutput : public ILogOutput {
public:
    /**
     * @brief 构造函数
     * @param ident 系统日志标识
     */
    explicit SyslogOutput(const std::string& ident = "timing-server");
    
    /**
     * @brief 析构函数
     */
    ~SyslogOutput() override;
    
    // ILogOutput接口实现
    void WriteLog(const LogEntry& entry) override;
    void Flush() override;
    void Close() override;

private:
    std::string ident_;                         // 系统日志标识
    bool syslog_opened_;                        // 系统日志是否已打开
    
    /**
     * @brief 将日志级别转换为syslog优先级
     */
    int LogLevelToSyslogPriority(LogLevel level) const;
};

/**
 * @brief 日志管理器类
 * 提供统一的日志记录接口，支持多种输出方式
 */
class Logger {
public:
    /**
     * @brief 获取单例实例
     */
    static Logger& GetInstance();
    
    /**
     * @brief 析构函数
     */
    ~Logger();
    
    /**
     * @brief 初始化日志系统
     * @param config_file_path 配置文件路径（可选）
     */
    bool Initialize(const std::string& config_file_path = "");
    
    /**
     * @brief 关闭日志系统
     */
    void Shutdown();
    
    /**
     * @brief 设置全局日志级别
     * @param level 日志级别
     */
    void SetLogLevel(LogLevel level);
    
    /**
     * @brief 获取全局日志级别
     */
    LogLevel GetLogLevel() const;
    
    /**
     * @brief 设置组件日志级别
     * @param component 组件
     * @param level 日志级别
     */
    void SetComponentLogLevel(LogComponent component, LogLevel level);
    
    /**
     * @brief 添加日志输出
     * @param output 日志输出实现
     */
    void AddOutput(std::unique_ptr<ILogOutput> output);
    
    /**
     * @brief 清除所有日志输出
     */
    void ClearOutputs();
    
    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param component 组件
     * @param message 消息
     * @param file 源文件名
     * @param line 源文件行号
     * @param function 函数名
     */
    void Log(LogLevel level, LogComponent component, const std::string& message,
             const std::string& file = "", int line = 0, const std::string& function = "");
    
    /**
     * @brief 记录带上下文的日志
     * @param level 日志级别
     * @param component 组件
     * @param message 消息
     * @param context 上下文信息
     * @param file 源文件名
     * @param line 源文件行号
     * @param function 函数名
     */
    void LogWithContext(LogLevel level, LogComponent component, const std::string& message,
                       const std::map<std::string, std::string>& context,
                       const std::string& file = "", int line = 0, const std::string& function = "");
    
    /**
     * @brief 搜索日志
     * @param filter 过滤条件
     * @return 匹配的日志条目列表
     */
    std::vector<LogEntry> SearchLogs(const LogFilter& filter) const;
    
    /**
     * @brief 获取最近的日志条目
     * @param count 条目数量
     * @param component 组件过滤（可选）
     * @return 日志条目列表
     */
    std::vector<LogEntry> GetRecentLogs(size_t count, LogComponent component = LogComponent::SYSTEM) const;
    
    /**
     * @brief 刷新所有输出
     */
    void FlushAll();
    
    /**
     * @brief 启用/禁用异步日志记录
     * @param enable 是否启用
     */
    void SetAsyncLogging(bool enable);
    
    /**
     * @brief 检查指定级别和组件的日志是否启用
     */
    bool IsLogEnabled(LogLevel level, LogComponent component) const;

private:
    Logger() = default;
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    LogLevel global_log_level_;                 // 全局日志级别
    std::map<LogComponent, LogLevel> component_log_levels_; // 组件日志级别
    std::vector<std::unique_ptr<ILogOutput>> outputs_; // 日志输出列表
    mutable std::mutex outputs_mutex_;          // 输出列表互斥锁
    
    // 异步日志相关
    bool async_logging_enabled_;                // 是否启用异步日志
    std::queue<LogEntry> log_queue_;           // 日志队列
    std::mutex queue_mutex_;                   // 队列互斥锁
    std::condition_variable queue_condition_;   // 队列条件变量
    std::atomic<bool> shutdown_requested_;      // 关闭请求标志
    std::unique_ptr<std::thread> worker_thread_; // 工作线程
    
    // 日志历史记录（用于搜索功能）
    mutable std::mutex history_mutex_;          // 历史记录互斥锁
    std::vector<LogEntry> log_history_;        // 日志历史记录
    size_t max_history_size_;                  // 最大历史记录大小
    
    /**
     * @brief 异步日志工作线程函数
     */
    void AsyncWorkerThread();
    
    /**
     * @brief 处理日志条目
     */
    void ProcessLogEntry(const LogEntry& entry);
    
    /**
     * @brief 添加到历史记录
     */
    void AddToHistory(const LogEntry& entry);
    
    /**
     * @brief 加载配置
     */
    bool LoadConfig(const std::string& config_file_path);
};

// 便利宏定义
#define LOG_TRACE(component, message) \
    timing_server::core::Logger::GetInstance().Log( \
        timing_server::core::LogLevel::TRACE, component, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_DEBUG(component, message) \
    timing_server::core::Logger::GetInstance().Log( \
        timing_server::core::LogLevel::DEBUG, component, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_INFO(component, message) \
    timing_server::core::Logger::GetInstance().Log( \
        timing_server::core::LogLevel::INFO, component, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_WARNING(component, message) \
    timing_server::core::Logger::GetInstance().Log( \
        timing_server::core::LogLevel::WARNING, component, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_ERROR(component, message) \
    timing_server::core::Logger::GetInstance().Log( \
        timing_server::core::LogLevel::ERROR, component, message, __FILE__, __LINE__, __FUNCTION__)

#define LOG_CRITICAL(component, message) \
    timing_server::core::Logger::GetInstance().Log( \
        timing_server::core::LogLevel::CRITICAL, component, message, __FILE__, __LINE__, __FUNCTION__)

// 带上下文的日志宏
#define LOG_WITH_CONTEXT(level, component, message, context) \
    timing_server::core::Logger::GetInstance().LogWithContext( \
        level, component, message, context, __FILE__, __LINE__, __FUNCTION__)

/**
 * @brief 日志级别转换为字符串
 */
std::string LogLevelToString(LogLevel level);

/**
 * @brief 字符串转换为日志级别
 */
LogLevel StringToLogLevel(const std::string& str);

/**
 * @brief 日志组件转换为字符串
 */
std::string LogComponentToString(LogComponent component);

/**
 * @brief 字符串转换为日志组件
 */
LogComponent StringToLogComponent(const std::string& str);

} // namespace core
} // namespace timing_server