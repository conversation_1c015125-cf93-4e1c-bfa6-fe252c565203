#pragma once

#include "core/types.h"
#include <vector>
#include <memory>
#include <chrono>

namespace timing_server {
namespace core {

/**
 * @brief GNSS信号质量指标结构
 * 包含GNSS信号的详细质量评估信息
 */
struct GnssSignalQuality {
    uint32_t satellite_count;          // 可见卫星数量
    double signal_strength_dbm;        // 信号强度 (dBm)
    double position_dilution;          // 位置精度因子 (PDOP)
    double time_dilution;              // 时间精度因子 (TDOP)
    double horizontal_accuracy_m;      // 水平精度 (米)
    double vertical_accuracy_m;        // 垂直精度 (米)
    double time_accuracy_ns;           // 时间精度 (纳秒)
    bool is_differential_mode;         // 是否差分模式
    uint32_t fix_quality;              // 定位质量 (0=无效, 1=GPS, 2=DGPS)
    uint64_t last_update_time_ns;      // 最后更新时间
    
    /**
     * @brief 默认构造函数
     */
    GnssSignalQuality() 
        : satellite_count(0), signal_strength_dbm(-999.0), position_dilution(99.9)
        , time_dilution(99.9), horizontal_accuracy_m(999.0), vertical_accuracy_m(999.0)
        , time_accuracy_ns(1000000.0), is_differential_mode(false), fix_quality(0)
        , last_update_time_ns(0) {}
};

/**
 * @brief 驯服收敛指标结构
 * 用于评估时钟驯服过程的收敛状态
 */
struct DiscipliningConvergence {
    double phase_error_ns;             // 相位误差 (纳秒)
    double frequency_error_ppm;        // 频率误差 (ppm)
    double phase_error_trend;          // 相位误差趋势
    double frequency_error_trend;      // 频率误差趋势
    double convergence_rate;           // 收敛速率
    uint32_t stable_samples;           // 稳定样本数
    uint32_t total_samples;            // 总样本数
    bool is_converged;                 // 是否已收敛
    uint64_t convergence_start_time_ns; // 收敛开始时间
    uint64_t convergence_duration_ns;  // 收敛持续时间
    
    /**
     * @brief 默认构造函数
     */
    DiscipliningConvergence()
        : phase_error_ns(0.0), frequency_error_ppm(0.0), phase_error_trend(0.0)
        , frequency_error_trend(0.0), convergence_rate(0.0), stable_samples(0)
        , total_samples(0), is_converged(false), convergence_start_time_ns(0)
        , convergence_duration_ns(0) {}
};

/**
 * @brief 守时质量指标结构
 * 用于评估守时模式下的时间精度保持能力
 */
struct HoldoverQuality {
    double initial_frequency_offset_ppm; // 初始频率偏移
    double current_frequency_offset_ppm; // 当前频率偏移
    double frequency_drift_rate_ppm_per_hour; // 频率漂移率
    double temperature_coefficient;      // 温度系数
    double aging_rate_ppm_per_day;      // 老化率
    double predicted_accuracy_ns;       // 预测精度
    uint64_t holdover_start_time_ns;    // 守时开始时间
    uint64_t holdover_duration_ns;      // 守时持续时间
    uint32_t quality_score;             // 质量评分 (0-100)
    bool is_within_spec;                // 是否在规格范围内
    
    /**
     * @brief 默认构造函数
     */
    HoldoverQuality()
        : initial_frequency_offset_ppm(0.0), current_frequency_offset_ppm(0.0)
        , frequency_drift_rate_ppm_per_hour(0.0), temperature_coefficient(0.0)
        , aging_rate_ppm_per_day(0.0), predicted_accuracy_ns(0.0)
        , holdover_start_time_ns(0), holdover_duration_ns(0), quality_score(0)
        , is_within_spec(false) {}
};

/**
 * @brief 信号质量评估器接口
 * 定义信号质量评估的通用接口
 */
class ISignalQualityEvaluator {
public:
    virtual ~ISignalQualityEvaluator() = default;
    
    /**
     * @brief 评估GNSS信号质量
     * @param gnss_data GNSS原始数据
     * @return GNSS信号质量指标
     */
    virtual GnssSignalQuality EvaluateGnssQuality(const std::string& gnss_data) = 0;
    
    /**
     * @brief 检查信号是否可用
     * @param quality 信号质量指标
     * @return 信号是否可用
     */
    virtual bool IsSignalUsable(const GnssSignalQuality& quality) = 0;
    
    /**
     * @brief 获取信号质量评分
     * @param quality 信号质量指标
     * @return 质量评分 (0-100)
     */
    virtual uint32_t GetQualityScore(const GnssSignalQuality& quality) = 0;
};

/**
 * @brief 驯服收敛评估器接口
 * 定义驯服收敛评估的通用接口
 */
class IDiscipliningEvaluator {
public:
    virtual ~IDiscipliningEvaluator() = default;
    
    /**
     * @brief 添加测量样本
     * @param phase_error_ns 相位误差 (纳秒)
     * @param frequency_error_ppm 频率误差 (ppm)
     */
    virtual void AddSample(double phase_error_ns, double frequency_error_ppm) = 0;
    
    /**
     * @brief 评估收敛状态
     * @return 驯服收敛指标
     */
    virtual DiscipliningConvergence EvaluateConvergence() = 0;
    
    /**
     * @brief 检查是否已收敛
     * @return 是否已收敛
     */
    virtual bool IsConverged() = 0;
    
    /**
     * @brief 重置评估器
     */
    virtual void Reset() = 0;
};

/**
 * @brief 守时质量评估器接口
 * 定义守时质量评估的通用接口
 */
class IHoldoverEvaluator {
public:
    virtual ~IHoldoverEvaluator() = default;
    
    /**
     * @brief 开始守时评估
     * @param initial_frequency_offset 初始频率偏移
     */
    virtual void StartHoldover(double initial_frequency_offset) = 0;
    
    /**
     * @brief 更新守时状态
     * @param current_frequency_offset 当前频率偏移
     * @param temperature 当前温度
     */
    virtual void UpdateHoldover(double current_frequency_offset, double temperature) = 0;
    
    /**
     * @brief 评估守时质量
     * @return 守时质量指标
     */
    virtual HoldoverQuality EvaluateHoldover() = 0;
    
    /**
     * @brief 检查守时是否超时
     * @param max_holdover_hours 最大守时时间 (小时)
     * @return 是否超时
     */
    virtual bool IsHoldoverTimeout(uint32_t max_holdover_hours) = 0;
    
    /**
     * @brief 停止守时评估
     */
    virtual void StopHoldover() = 0;
};

/**
 * @brief GNSS信号质量评估器实现
 * 实现GNSS信号质量的具体评估算法
 */
class GnssSignalQualityEvaluator : public ISignalQualityEvaluator {
public:
    /**
     * @brief 构造函数
     */
    GnssSignalQualityEvaluator();
    
    /**
     * @brief 析构函数
     */
    ~GnssSignalQualityEvaluator() override = default;
    
    /**
     * @brief 评估GNSS信号质量
     */
    GnssSignalQuality EvaluateGnssQuality(const std::string& gnss_data) override;
    
    /**
     * @brief 检查信号是否可用
     */
    bool IsSignalUsable(const GnssSignalQuality& quality) override;
    
    /**
     * @brief 获取信号质量评分
     */
    uint32_t GetQualityScore(const GnssSignalQuality& quality) override;
    
    /**
     * @brief 设置质量阈值
     * @param min_satellites 最小卫星数
     * @param min_signal_strength 最小信号强度
     * @param max_pdop 最大PDOP值
     */
    void SetQualityThresholds(uint32_t min_satellites, double min_signal_strength, double max_pdop);

private:
    /**
     * @brief 解析NMEA数据
     * @param nmea_sentence NMEA语句
     * @return 解析是否成功
     */
    bool ParseNmeaData(const std::string& nmea_sentence, GnssSignalQuality& quality);
    
    /**
     * @brief 解析GGA语句
     * @param gga_sentence GGA语句
     * @param quality 质量指标输出
     * @return 解析是否成功
     */
    bool ParseGgaSentence(const std::string& gga_sentence, GnssSignalQuality& quality);
    
    /**
     * @brief 解析GSA语句
     * @param gsa_sentence GSA语句
     * @param quality 质量指标输出
     * @return 解析是否成功
     */
    bool ParseGsaSentence(const std::string& gsa_sentence, GnssSignalQuality& quality);
    
    /**
     * @brief 计算质量评分
     * @param quality 质量指标
     * @return 评分 (0-100)
     */
    uint32_t CalculateQualityScore(const GnssSignalQuality& quality);

private:
    uint32_t min_satellites_;           // 最小卫星数阈值
    double min_signal_strength_;        // 最小信号强度阈值
    double max_pdop_;                   // 最大PDOP阈值
    std::vector<GnssSignalQuality> history_; // 历史质量数据
};

/**
 * @brief 驯服收敛评估器实现
 * 实现时钟驯服收敛的具体评估算法
 */
class DiscipliningConvergenceEvaluator : public IDiscipliningEvaluator {
public:
    /**
     * @brief 构造函数
     * @param convergence_threshold_ns 收敛阈值 (纳秒)
     * @param min_stable_samples 最小稳定样本数
     */
    explicit DiscipliningConvergenceEvaluator(double convergence_threshold_ns = 50.0, uint32_t min_stable_samples = 10);
    
    /**
     * @brief 析构函数
     */
    ~DiscipliningConvergenceEvaluator() override = default;
    
    /**
     * @brief 添加测量样本
     */
    void AddSample(double phase_error_ns, double frequency_error_ppm) override;
    
    /**
     * @brief 评估收敛状态
     */
    DiscipliningConvergence EvaluateConvergence() override;
    
    /**
     * @brief 检查是否已收敛
     */
    bool IsConverged() override;
    
    /**
     * @brief 重置评估器
     */
    void Reset() override;

private:
    /**
     * @brief 计算趋势
     * @param samples 样本数据
     * @return 趋势值
     */
    double CalculateTrend(const std::vector<double>& samples);
    
    /**
     * @brief 计算标准差
     * @param samples 样本数据
     * @return 标准差
     */
    double CalculateStandardDeviation(const std::vector<double>& samples);

private:
    double convergence_threshold_ns_;   // 收敛阈值
    uint32_t min_stable_samples_;       // 最小稳定样本数
    uint32_t max_samples_;              // 最大样本数
    
    std::vector<double> phase_errors_;  // 相位误差历史
    std::vector<double> frequency_errors_; // 频率误差历史
    std::vector<uint64_t> timestamps_;  // 时间戳历史
    
    DiscipliningConvergence current_state_; // 当前收敛状态
};

/**
 * @brief 守时质量评估器实现
 * 实现守时质量的具体评估算法
 */
class HoldoverQualityEvaluator : public IHoldoverEvaluator {
public:
    /**
     * @brief 构造函数
     */
    HoldoverQualityEvaluator();
    
    /**
     * @brief 析构函数
     */
    ~HoldoverQualityEvaluator() override = default;
    
    /**
     * @brief 开始守时评估
     */
    void StartHoldover(double initial_frequency_offset) override;
    
    /**
     * @brief 更新守时状态
     */
    void UpdateHoldover(double current_frequency_offset, double temperature) override;
    
    /**
     * @brief 评估守时质量
     */
    HoldoverQuality EvaluateHoldover() override;
    
    /**
     * @brief 检查守时是否超时
     */
    bool IsHoldoverTimeout(uint32_t max_holdover_hours) override;
    
    /**
     * @brief 停止守时评估
     */
    void StopHoldover() override;

private:
    /**
     * @brief 预测频率漂移
     * @param duration_hours 预测时长 (小时)
     * @return 预测的频率偏移
     */
    double PredictFrequencyDrift(double duration_hours);
    
    /**
     * @brief 计算质量评分
     * @return 质量评分 (0-100)
     */
    uint32_t CalculateQualityScore();

private:
    HoldoverQuality current_quality_;   // 当前守时质量
    std::vector<std::pair<uint64_t, double>> frequency_history_; // 频率历史
    std::vector<std::pair<uint64_t, double>> temperature_history_; // 温度历史
    bool is_holdover_active_;           // 守时是否激活
};

} // namespace core
} // namespace timing_server