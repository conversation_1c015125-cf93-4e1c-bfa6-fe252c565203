#pragma once

#include <string>
#include <vector>
#include <memory>
#include <mutex>
#include <atomic>
#include <fstream>
#include <map>
#include <chrono>
#include <functional>

#include "types.h"
#include "logger.h"

namespace timing_server {
namespace core {

/**
 * @brief 铷钟学习数据结构
 * 包含铷钟特性学习的所有关键参数
 */
struct RubidiumLearningData {
    // 基本信息
    uint64_t timestamp_ns;              // 记录时间戳（纳秒）
    uint64_t learning_duration_ns;      // 学习持续时间（纳秒）
    uint32_t sample_count;              // 采样点数量
    
    // 频率特性参数
    double base_frequency_hz;           // 基准频率（Hz）
    double frequency_offset_ppm;        // 频率偏移（ppm）
    double frequency_drift_rate_ppm_per_hour; // 频率漂移率（ppm/小时）
    double frequency_stability_ppm;     // 频率稳定度（ppm）
    
    // 温度补偿参数
    double reference_temperature_c;     // 参考温度（摄氏度）
    double temperature_coefficient_ppm_per_c; // 温度系数（ppm/°C）
    double temperature_coefficient_2nd_ppm_per_c2; // 二次温度系数（ppm/°C²）
    
    // 老化特性参数
    double aging_rate_ppm_per_day;      // 老化率（ppm/天）
    double aging_acceleration_factor;   // 老化加速因子
    uint32_t operating_days;            // 运行天数
    
    // 环境影响参数
    double humidity_coefficient_ppm_per_percent; // 湿度系数（ppm/%RH）
    double pressure_coefficient_ppm_per_hpa;     // 气压系数（ppm/hPa）
    double vibration_sensitivity_ppm_per_g;      // 振动敏感度（ppm/g）
    
    // 统计信息
    double allan_deviation_1s;          // 1秒Allan偏差
    double allan_deviation_10s;         // 10秒Allan偏差
    double allan_deviation_100s;        // 100秒Allan偏差
    double allan_deviation_1000s;       // 1000秒Allan偏差
    
    // 质量指标
    double confidence_level;            // 置信度（0.0-1.0）
    double prediction_accuracy;         // 预测精度
    uint32_t validation_score;          // 验证评分（0-100）
    
    // 校正参数
    double phase_correction_ns;         // 相位校正（纳秒）
    double frequency_correction_ppm;    // 频率校正（ppm）
    bool correction_enabled;            // 是否启用校正
    
    /**
     * @brief 默认构造函数
     */
    RubidiumLearningData();
    
    /**
     * @brief 验证数据完整性
     * @return 数据是否有效
     */
    bool IsValid() const;
    
    /**
     * @brief 计算数据质量评分
     * @return 质量评分（0-100）
     */
    uint32_t CalculateQualityScore() const;
    
    /**
     * @brief 获取数据摘要字符串
     * @return 数据摘要
     */
    std::string GetSummary() const;
};

/**
 * @brief 铷钟学习历史记录
 * 包含多个时间点的学习数据
 */
struct RubidiumLearningHistory {
    std::string device_id;              // 设备标识
    std::string serial_number;          // 序列号
    std::string firmware_version;       // 固件版本
    uint64_t first_learning_time;       // 首次学习时间
    uint64_t last_update_time;          // 最后更新时间
    std::vector<RubidiumLearningData> learning_records; // 学习记录列表
    
    /**
     * @brief 获取最新的学习数据
     * @return 最新学习数据指针，无数据返回nullptr
     */
    const RubidiumLearningData* GetLatestData() const;
    
    /**
     * @brief 获取指定时间范围内的学习数据
     * @param start_time 开始时间
     * @param end_time 结束时间
     * @return 学习数据列表
     */
    std::vector<RubidiumLearningData> GetDataInRange(uint64_t start_time, uint64_t end_time) const;
    
    /**
     * @brief 计算学习数据趋势
     * @return 趋势分析结果
     */
    struct TrendAnalysis {
        double frequency_drift_trend;    // 频率漂移趋势
        double temperature_trend;        // 温度趋势
        double aging_trend;             // 老化趋势
        double stability_trend;         // 稳定性趋势
    };
    
    TrendAnalysis AnalyzeTrends() const;
};

/**
 * @brief 二进制数据文件格式定义
 */
struct RubidiumDataFileHeader {
    static constexpr uint32_t MAGIC_NUMBER = 0x52554249; // "RUBI"
    static constexpr uint32_t VERSION = 1;
    
    uint32_t magic;                     // 魔数
    uint32_t version;                   // 版本号
    uint32_t header_size;               // 头部大小
    uint32_t record_count;              // 记录数量
    uint64_t creation_time;             // 创建时间
    uint64_t last_modified_time;        // 最后修改时间
    uint32_t checksum;                  // 校验和
    char device_id[64];                 // 设备ID
    char serial_number[32];             // 序列号
    char firmware_version[16];          // 固件版本
    uint8_t reserved[64];               // 保留字段
    
    /**
     * @brief 计算头部校验和
     */
    uint32_t CalculateChecksum() const;
    
    /**
     * @brief 验证头部完整性
     */
    bool IsValid() const;
};

/**
 * @brief 铷钟学习数据存储管理器
 * 负责铷钟学习数据的持久化存储和管理
 */
class RubidiumLearningStorage {
public:
    /**
     * @brief 存储操作结果枚举
     */
    enum class StorageResult {
        SUCCESS,                        // 操作成功
        ERROR_FILE_NOT_FOUND,          // 文件未找到
        ERROR_FILE_CORRUPTED,          // 文件损坏
        ERROR_INVALID_FORMAT,          // 格式无效
        ERROR_CHECKSUM_MISMATCH,       // 校验和不匹配
        ERROR_VERSION_MISMATCH,        // 版本不匹配
        ERROR_WRITE_FAILED,            // 写入失败
        ERROR_READ_FAILED,             // 读取失败
        ERROR_INSUFFICIENT_SPACE,      // 空间不足
        ERROR_PERMISSION_DENIED,       // 权限不足
        ERROR_BACKUP_FAILED,           // 备份失败
        ERROR_INVALID_PARAMETER        // 无效参数
    };
    
    /**
     * @brief 构造函数
     * @param storage_path 存储路径
     * @param device_id 设备ID
     */
    RubidiumLearningStorage(const std::string& storage_path, const std::string& device_id);
    
    /**
     * @brief 析构函数
     */
    ~RubidiumLearningStorage();
    
    /**
     * @brief 初始化存储系统
     * @param serial_number 设备序列号
     * @param firmware_version 固件版本
     */
    StorageResult Initialize(const std::string& serial_number, const std::string& firmware_version);
    
    /**
     * @brief 关闭存储系统
     */
    void Shutdown();
    
    /**
     * @brief 保存学习数据
     * @param data 学习数据
     * @param create_backup 是否创建备份
     */
    StorageResult SaveLearningData(const RubidiumLearningData& data, bool create_backup = true);
    
    /**
     * @brief 加载学习历史
     * @return 学习历史数据
     */
    std::pair<StorageResult, RubidiumLearningHistory> LoadLearningHistory();
    
    /**
     * @brief 获取最新的学习数据
     * @return 最新学习数据
     */
    std::pair<StorageResult, RubidiumLearningData> GetLatestLearningData();
    
    /**
     * @brief 删除过期的学习数据
     * @param retention_days 保留天数
     */
    StorageResult CleanupExpiredData(uint32_t retention_days);
    
    /**
     * @brief 压缩存储文件
     */
    StorageResult CompactStorage();
    
    /**
     * @brief 验证存储文件完整性
     * @param repair_if_corrupted 如果损坏是否尝试修复
     */
    StorageResult VerifyIntegrity(bool repair_if_corrupted = false);
    
    /**
     * @brief 创建备份文件
     * @param backup_path 备份路径
     */
    StorageResult CreateBackup(const std::string& backup_path);
    
    /**
     * @brief 从备份恢复
     * @param backup_path 备份路径
     */
    StorageResult RestoreFromBackup(const std::string& backup_path);
    
    /**
     * @brief 导出学习数据为JSON格式
     * @param export_path 导出路径
     * @param include_raw_data 是否包含原始数据
     */
    StorageResult ExportToJson(const std::string& export_path, bool include_raw_data = false);
    
    /**
     * @brief 从JSON格式导入学习数据
     * @param import_path 导入路径
     * @param merge_with_existing 是否与现有数据合并
     */
    StorageResult ImportFromJson(const std::string& import_path, bool merge_with_existing = true);
    
    /**
     * @brief 获取存储统计信息
     */
    struct StorageStatistics {
        uint32_t total_records;         // 总记录数
        uint64_t file_size_bytes;       // 文件大小
        uint64_t oldest_record_time;    // 最旧记录时间
        uint64_t newest_record_time;    // 最新记录时间
        double average_quality_score;   // 平均质量评分
        uint32_t backup_count;          // 备份数量
    };
    
    StorageStatistics GetStatistics();
    
    /**
     * @brief 设置自动备份间隔
     * @param interval_hours 备份间隔（小时）
     */
    void SetAutoBackupInterval(uint32_t interval_hours);
    
    /**
     * @brief 启用/禁用数据压缩
     * @param enable 是否启用
     */
    void SetCompressionEnabled(bool enable);
    
    /**
     * @brief 设置最大存储记录数
     * @param max_records 最大记录数
     */
    void SetMaxRecords(uint32_t max_records);

private:
    std::string storage_path_;          // 存储路径
    std::string device_id_;             // 设备ID
    std::string serial_number_;         // 序列号
    std::string firmware_version_;      // 固件版本
    
    mutable std::mutex storage_mutex_;  // 存储操作互斥锁
    std::atomic<bool> initialized_;     // 初始化标志
    
    // 配置参数
    uint32_t max_records_;              // 最大记录数
    uint32_t auto_backup_interval_hours_; // 自动备份间隔
    bool compression_enabled_;          // 是否启用压缩
    
    // 缓存数据
    std::unique_ptr<RubidiumLearningHistory> cached_history_; // 缓存的历史数据
    uint64_t cache_last_update_;        // 缓存最后更新时间
    
    /**
     * @brief 读取文件头部
     * @param file 文件流
     * @param header 头部结构
     */
    StorageResult ReadFileHeader(std::ifstream& file, RubidiumDataFileHeader& header);
    
    /**
     * @brief 写入文件头部
     * @param file 文件流
     * @param header 头部结构
     */
    StorageResult WriteFileHeader(std::ofstream& file, const RubidiumDataFileHeader& header);
    
    /**
     * @brief 读取学习数据记录
     * @param file 文件流
     * @param data 数据结构
     */
    StorageResult ReadLearningRecord(std::ifstream& file, RubidiumLearningData& data);
    
    /**
     * @brief 写入学习数据记录
     * @param file 文件流
     * @param data 数据结构
     */
    StorageResult WriteLearningRecord(std::ofstream& file, const RubidiumLearningData& data);
    
    /**
     * @brief 计算数据校验和
     * @param data 数据指针
     * @param size 数据大小
     */
    uint32_t CalculateChecksum(const void* data, size_t size);
    
    /**
     * @brief 创建存储目录
     */
    StorageResult CreateStorageDirectory();
    
    /**
     * @brief 获取主存储文件路径
     */
    std::string GetMainFilePath() const;
    
    /**
     * @brief 获取备份文件路径
     * @param backup_index 备份索引
     */
    std::string GetBackupFilePath(uint32_t backup_index = 0) const;
    
    /**
     * @brief 获取临时文件路径
     */
    std::string GetTempFilePath() const;
    
    /**
     * @brief 更新缓存数据
     */
    void UpdateCache(const RubidiumLearningHistory& history);
    
    /**
     * @brief 清理缓存
     */
    void ClearCache();
    
    /**
     * @brief 执行自动备份
     */
    void PerformAutoBackup();
    
    /**
     * @brief 压缩数据
     * @param input 输入数据
     * @param output 输出数据
     */
    bool CompressData(const std::vector<uint8_t>& input, std::vector<uint8_t>& output);
    
    /**
     * @brief 解压数据
     * @param input 输入数据
     * @param output 输出数据
     */
    bool DecompressData(const std::vector<uint8_t>& input, std::vector<uint8_t>& output);
};

/**
 * @brief 铷钟学习数据分析器
 * 提供学习数据的分析和预测功能
 */
class RubidiumLearningAnalyzer {
public:
    /**
     * @brief 构造函数
     * @param storage 存储管理器
     */
    explicit RubidiumLearningAnalyzer(std::shared_ptr<RubidiumLearningStorage> storage);
    
    /**
     * @brief 预测未来频率偏移
     * @param prediction_time_ns 预测时间点
     * @param temperature_c 预期温度
     * @return 预测的频率偏移（ppm）
     */
    double PredictFrequencyOffset(uint64_t prediction_time_ns, double temperature_c);
    
    /**
     * @brief 计算温度补偿值
     * @param current_temperature_c 当前温度
     * @param reference_temperature_c 参考温度
     * @return 温度补偿值（ppm）
     */
    double CalculateTemperatureCompensation(double current_temperature_c, double reference_temperature_c);
    
    /**
     * @brief 评估学习数据质量
     * @param data 学习数据
     * @return 质量评估结果
     */
    struct QualityAssessment {
        uint32_t overall_score;         // 总体评分（0-100）
        double data_completeness;       // 数据完整性（0.0-1.0）
        double measurement_accuracy;    // 测量精度
        double temporal_consistency;    // 时间一致性
        std::vector<std::string> issues; // 发现的问题
        std::vector<std::string> recommendations; // 改进建议
    };
    
    QualityAssessment AssessDataQuality(const RubidiumLearningData& data);
    
    /**
     * @brief 生成学习报告
     * @param include_charts 是否包含图表数据
     * @return 学习报告（JSON格式）
     */
    std::string GenerateLearningReport(bool include_charts = false);
    
    /**
     * @brief 检测异常数据
     * @param threshold 异常检测阈值
     * @return 异常数据列表
     */
    std::vector<RubidiumLearningData> DetectAnomalies(double threshold = 3.0);
    
    /**
     * @brief 优化学习参数
     * @return 优化建议
     */
    struct OptimizationSuggestion {
        uint32_t recommended_learning_duration_hours; // 推荐学习时长
        uint32_t recommended_sample_interval_seconds;  // 推荐采样间隔
        double recommended_temperature_range_c;        // 推荐温度范围
        std::vector<std::string> optimization_tips;   // 优化提示
    };
    
    OptimizationSuggestion OptimizeLearningParameters();

private:
    std::shared_ptr<RubidiumLearningStorage> storage_; // 存储管理器
    
    /**
     * @brief 计算线性回归
     * @param x_values X值列表
     * @param y_values Y值列表
     * @return 回归系数（斜率和截距）
     */
    std::pair<double, double> CalculateLinearRegression(const std::vector<double>& x_values,
                                                       const std::vector<double>& y_values);
    
    /**
     * @brief 计算Allan偏差
     * @param frequency_data 频率数据
     * @param tau_seconds 积分时间
     * @return Allan偏差值
     */
    double CalculateAllanDeviation(const std::vector<double>& frequency_data, double tau_seconds);
    
    /**
     * @brief 检测数据趋势
     * @param data_points 数据点
     * @return 趋势斜率
     */
    double DetectTrend(const std::vector<std::pair<uint64_t, double>>& data_points);
};

/**
 * @brief 存储结果转换为字符串
 */
std::string StorageResultToString(RubidiumLearningStorage::StorageResult result);

/**
 * @brief 便利宏：记录铷钟学习数据
 */
#define RECORD_RUBIDIUM_LEARNING(storage, data) \
    do { \
        auto result = (storage)->SaveLearningData(data); \
        if (result != timing_server::core::RubidiumLearningStorage::StorageResult::SUCCESS) { \
            LOG_ERROR(timing_server::core::LogComponent::HAL_RUBIDIUM, \
                     "保存铷钟学习数据失败: " + timing_server::core::StorageResultToString(result)); \
        } \
    } while(0)

} // namespace core
} // namespace timing_server