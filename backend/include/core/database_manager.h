#pragma once

#include <string>
#include <memory>
#include <vector>
#include <map>
#include <mutex>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <queue>
#include <functional>
#include <sqlite3.h>

#include "types.h"
#include "logger.h"

namespace timing_server {
namespace core {

/**
 * @brief 数据库操作结果枚举
 * 定义数据库操作的各种结果状态
 */
enum class DatabaseResult {
    SUCCESS,                    // 操作成功
    ERROR_OPEN_FAILED,         // 数据库打开失败
    ERROR_PREPARE_FAILED,      // SQL语句准备失败
    ERROR_EXECUTE_FAILED,      // SQL执行失败
    ERROR_TRANSACTION_FAILED,  // 事务失败
    ERROR_CONSTRAINT_VIOLATION, // 约束违反
    ERROR_DISK_FULL,           // 磁盘空间不足
    ERROR_CORRUPTED,           // 数据库损坏
    ERROR_BUSY,                // 数据库忙碌
    ERROR_INVALID_PARAMETER,   // 无效参数
    ERROR_NOT_INITIALIZED      // 未初始化
};

/**
 * @brief 性能指标数据结构
 * 用于存储系统性能指标到数据库
 */
struct MetricRecord {
    uint64_t timestamp_ns;      // 时间戳（纳秒）
    std::string metric_type;    // 指标类型（如"phase_offset", "frequency_offset"等）
    std::string source;         // 数据源（如"GNSS", "RUBIDIUM"等）
    double value;               // 指标值
    std::string unit;           // 单位（如"ns", "ppm"等）
    int quality;                // 质量评分（0-100）
    
    MetricRecord() : timestamp_ns(0), value(0.0), quality(100) {}
    
    MetricRecord(const std::string& type, const std::string& src, double val, 
                const std::string& unit_str, int qual = 100)
        : metric_type(type), source(src), value(val), unit(unit_str), quality(qual) {
        timestamp_ns = GetCurrentTimestampNs();
    }
};

/**
 * @brief 事件日志数据结构
 * 用于存储系统事件到数据库
 */
struct EventRecord {
    uint64_t timestamp_ns;      // 时间戳（纳秒）
    std::string level;          // 日志级别
    std::string component;      // 组件名称
    std::string event_type;     // 事件类型
    std::string message;        // 事件消息
    std::string context;        // 上下文信息（JSON格式）
    
    EventRecord() : timestamp_ns(0) {}
    
    EventRecord(const std::string& lvl, const std::string& comp, 
               const std::string& type, const std::string& msg, 
               const std::string& ctx = "")
        : level(lvl), component(comp), event_type(type), message(msg), context(ctx) {
        timestamp_ns = GetCurrentTimestampNs();
    }
    
    // 从LogEntry构造
    explicit EventRecord(const LogEntry& log_entry);
};

/**
 * @brief 状态转换记录结构
 * 用于存储时钟状态转换历史
 */
struct StateTransitionRecord {
    uint64_t timestamp_ns;      // 时间戳（纳秒）
    std::string from_state;     // 源状态
    std::string to_state;       // 目标状态
    std::string trigger_event;  // 触发事件
    uint64_t duration_ms;       // 持续时间（毫秒）
    bool success;               // 是否成功
    
    StateTransitionRecord() : timestamp_ns(0), duration_ms(0), success(false) {}
    
    StateTransitionRecord(const std::string& from, const std::string& to, 
                         const std::string& trigger, uint64_t dur, bool succ)
        : from_state(from), to_state(to), trigger_event(trigger), 
          duration_ms(dur), success(succ) {
        timestamp_ns = GetCurrentTimestampNs();
    }
};

/**
 * @brief 数据库查询过滤器
 * 用于查询历史数据时的过滤条件
 */
struct DatabaseFilter {
    uint64_t start_time_ns = 0;         // 开始时间
    uint64_t end_time_ns = UINT64_MAX;  // 结束时间
    std::vector<std::string> sources;   // 数据源过滤
    std::vector<std::string> types;     // 类型过滤
    size_t limit = 1000;                // 结果数量限制
    bool ascending = false;             // 是否升序排列
    
    /**
     * @brief 构建WHERE子句
     */
    std::string BuildWhereClause() const;
};

/**
 * @brief 数据库连接池
 * 管理多个SQLite连接以支持并发访问
 */
class DatabaseConnectionPool {
public:
    /**
     * @brief 构造函数
     * @param db_path 数据库文件路径
     * @param pool_size 连接池大小
     */
    DatabaseConnectionPool(const std::string& db_path, size_t pool_size = 5);
    
    /**
     * @brief 析构函数
     */
    ~DatabaseConnectionPool();
    
    /**
     * @brief 初始化连接池
     */
    DatabaseResult Initialize();
    
    /**
     * @brief 关闭连接池
     */
    void Shutdown();
    
    /**
     * @brief 获取数据库连接
     * @param timeout_ms 超时时间（毫秒）
     * @return 数据库连接指针，失败返回nullptr
     */
    sqlite3* GetConnection(int timeout_ms = 5000);
    
    /**
     * @brief 归还数据库连接
     * @param conn 数据库连接
     */
    void ReturnConnection(sqlite3* conn);
    
    /**
     * @brief 获取连接池状态
     */
    struct PoolStatus {
        size_t total_connections;
        size_t available_connections;
        size_t active_connections;
    };
    
    PoolStatus GetStatus() const;

private:
    std::string db_path_;                       // 数据库路径
    size_t pool_size_;                          // 连接池大小
    std::queue<sqlite3*> available_connections_; // 可用连接队列
    mutable std::mutex pool_mutex_;             // 连接池互斥锁
    std::condition_variable pool_condition_;    // 连接池条件变量
    std::atomic<bool> initialized_;             // 初始化标志
    std::atomic<bool> shutdown_requested_;      // 关闭请求标志
    
    /**
     * @brief 创建新的数据库连接
     */
    sqlite3* CreateConnection();
    
    /**
     * @brief 关闭数据库连接
     */
    void CloseConnection(sqlite3* conn);
};

/**
 * @brief 数据库管理器类
 * 提供统一的数据库操作接口，支持性能指标和事件日志存储
 */
class DatabaseManager {
public:
    /**
     * @brief 获取单例实例
     */
    static DatabaseManager& GetInstance();
    
    /**
     * @brief 析构函数
     */
    ~DatabaseManager();
    
    /**
     * @brief 初始化数据库管理器
     * @param db_path 数据库文件路径
     * @param pool_size 连接池大小
     */
    DatabaseResult Initialize(const std::string& db_path, size_t pool_size = 5);
    
    /**
     * @brief 关闭数据库管理器
     */
    void Shutdown();
    
    /**
     * @brief 创建数据库表结构
     */
    DatabaseResult CreateTables();
    
    // 性能指标相关操作
    
    /**
     * @brief 插入性能指标记录
     * @param record 指标记录
     */
    DatabaseResult InsertMetric(const MetricRecord& record);
    
    /**
     * @brief 批量插入性能指标记录
     * @param records 指标记录列表
     */
    DatabaseResult InsertMetrics(const std::vector<MetricRecord>& records);
    
    /**
     * @brief 查询性能指标记录
     * @param filter 查询过滤器
     * @return 指标记录列表
     */
    std::vector<MetricRecord> QueryMetrics(const DatabaseFilter& filter);
    
    /**
     * @brief 获取指标统计信息
     * @param metric_type 指标类型
     * @param source 数据源
     * @param start_time 开始时间
     * @param end_time 结束时间
     * @return 统计信息（平均值、最大值、最小值等）
     */
    struct MetricStatistics {
        double average;
        double maximum;
        double minimum;
        double std_deviation;
        size_t count;
    };
    
    MetricStatistics GetMetricStatistics(const std::string& metric_type,
                                       const std::string& source,
                                       uint64_t start_time,
                                       uint64_t end_time);
    
    // 事件日志相关操作
    
    /**
     * @brief 插入事件记录
     * @param record 事件记录
     */
    DatabaseResult InsertEvent(const EventRecord& record);
    
    /**
     * @brief 批量插入事件记录
     * @param records 事件记录列表
     */
    DatabaseResult InsertEvents(const std::vector<EventRecord>& records);
    
    /**
     * @brief 查询事件记录
     * @param filter 查询过滤器
     * @return 事件记录列表
     */
    std::vector<EventRecord> QueryEvents(const DatabaseFilter& filter);
    
    // 状态转换相关操作
    
    /**
     * @brief 插入状态转换记录
     * @param record 状态转换记录
     */
    DatabaseResult InsertStateTransition(const StateTransitionRecord& record);
    
    /**
     * @brief 查询状态转换记录
     * @param filter 查询过滤器
     * @return 状态转换记录列表
     */
    std::vector<StateTransitionRecord> QueryStateTransitions(const DatabaseFilter& filter);
    
    // 数据清理和归档
    
    /**
     * @brief 清理过期数据
     * @param retention_hours 数据保留时间（小时）
     * @param table_name 表名（空表示所有表）
     */
    DatabaseResult CleanupExpiredData(uint32_t retention_hours, 
                                     const std::string& table_name = "");
    
    /**
     * @brief 压缩数据库
     */
    DatabaseResult CompactDatabase();
    
    /**
     * @brief 备份数据库
     * @param backup_path 备份文件路径
     */
    DatabaseResult BackupDatabase(const std::string& backup_path);
    
    /**
     * @brief 获取数据库统计信息
     */
    struct DatabaseStatistics {
        size_t total_metrics;
        size_t total_events;
        size_t total_state_transitions;
        uint64_t database_size_bytes;
        uint64_t oldest_record_timestamp;
        uint64_t newest_record_timestamp;
    };
    
    DatabaseStatistics GetDatabaseStatistics();
    
    /**
     * @brief 启用/禁用异步写入
     * @param enable 是否启用
     */
    void SetAsyncWriting(bool enable);
    
    /**
     * @brief 设置批量写入大小
     * @param batch_size 批量大小
     */
    void SetBatchSize(size_t batch_size);
    
    /**
     * @brief 刷新待写入的数据
     */
    void FlushPendingWrites();

private:
    DatabaseManager() = default;
    DatabaseManager(const DatabaseManager&) = delete;
    DatabaseManager& operator=(const DatabaseManager&) = delete;
    
    std::unique_ptr<DatabaseConnectionPool> connection_pool_; // 连接池
    std::atomic<bool> initialized_;                          // 初始化标志
    
    // 异步写入相关
    bool async_writing_enabled_;                             // 是否启用异步写入
    size_t batch_size_;                                      // 批量写入大小
    std::queue<MetricRecord> pending_metrics_;               // 待写入指标队列
    std::queue<EventRecord> pending_events_;                 // 待写入事件队列
    std::queue<StateTransitionRecord> pending_transitions_;  // 待写入状态转换队列
    std::mutex pending_mutex_;                               // 待写入数据互斥锁
    std::condition_variable pending_condition_;              // 待写入数据条件变量
    std::atomic<bool> shutdown_requested_;                   // 关闭请求标志
    std::unique_ptr<std::thread> writer_thread_;            // 异步写入线程
    
    /**
     * @brief 异步写入线程函数
     */
    void AsyncWriterThread();
    
    /**
     * @brief 执行批量写入
     */
    void PerformBatchWrite();
    
    /**
     * @brief 执行SQL语句
     * @param sql SQL语句
     * @param bind_func 参数绑定函数
     * @return 操作结果
     */
    DatabaseResult ExecuteSQL(const std::string& sql, 
                             std::function<void(sqlite3_stmt*)> bind_func = nullptr);
    
    /**
     * @brief 执行查询SQL
     * @param sql SQL语句
     * @param bind_func 参数绑定函数
     * @param result_func 结果处理函数
     * @return 操作结果
     */
    DatabaseResult ExecuteQuery(const std::string& sql,
                               std::function<void(sqlite3_stmt*)> bind_func,
                               std::function<void(sqlite3_stmt*)> result_func);
    
    /**
     * @brief 开始事务
     */
    DatabaseResult BeginTransaction(sqlite3* conn);
    
    /**
     * @brief 提交事务
     */
    DatabaseResult CommitTransaction(sqlite3* conn);
    
    /**
     * @brief 回滚事务
     */
    DatabaseResult RollbackTransaction(sqlite3* conn);
    
    /**
     * @brief 检查表是否存在
     */
    bool TableExists(const std::string& table_name);
    
    /**
     * @brief 获取数据库文件大小
     */
    uint64_t GetDatabaseFileSize();
};

/**
 * @brief 数据库结果转换为字符串
 */
std::string DatabaseResultToString(DatabaseResult result);

/**
 * @brief 便利宏：记录性能指标到数据库
 */
#define RECORD_METRIC(type, source, value, unit) \
    timing_server::core::DatabaseManager::GetInstance().InsertMetric( \
        timing_server::core::MetricRecord(type, source, value, unit))

/**
 * @brief 便利宏：记录事件到数据库
 */
#define RECORD_EVENT(level, component, type, message) \
    timing_server::core::DatabaseManager::GetInstance().InsertEvent( \
        timing_server::core::EventRecord(level, component, type, message))

} // namespace core
} // namespace timing_server