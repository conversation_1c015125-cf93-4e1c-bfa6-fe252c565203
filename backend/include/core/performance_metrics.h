#pragma once

#include "core/types.h"
#include <memory>

// Forward declarations
namespace timing_server {
namespace core {
class ITimingEngine;
}
}
#include <thread>
#include <atomic>
#include <mutex>
#include <vector>
#include <map>
#include <queue>
#include <chrono>
#include <functional>

namespace timing_server {
namespace core {

/**
 * @brief 性能指标类型枚举
 */
enum class MetricType {
    COUNTER,        // 计数器 - 单调递增
    GAUGE,          // 仪表 - 可增可减的瞬时值
    HISTOGRAM,      // 直方图 - 值的分布
    SUMMARY         // 摘要 - 统计摘要
};

/**
 * @brief 性能指标数据点
 */
struct MetricDataPoint {
    uint64_t timestamp_ns;      // 时间戳（纳秒）
    double value;               // 指标值
    std::map<std::string, std::string> labels; // 标签
    
    /**
     * @brief 默认构造函数
     */
    MetricDataPoint() : timestamp_ns(0), value(0.0) {}
    
    /**
     * @brief 带参数构造函数
     */
    MetricDataPoint(double val, const std::map<std::string, std::string>& lbls = {})
        : timestamp_ns(GetCurrentTimestampNs()), value(val), labels(lbls) {}
};

/**
 * @brief 直方图桶配置
 */
struct HistogramBucket {
    double upper_bound;         // 上界
    uint64_t count;             // 计数
    
    HistogramBucket(double bound) : upper_bound(bound), count(0) {}
};

/**
 * @brief 性能指标定义
 */
struct MetricDefinition {
    std::string name;           // 指标名称
    std::string description;    // 指标描述
    MetricType type;            // 指标类型
    std::string unit;           // 单位
    std::vector<std::string> label_names; // 标签名称列表
    
    // 直方图特定配置
    std::vector<double> histogram_buckets; // 直方图桶边界
    
    /**
     * @brief 默认构造函数
     */
    MetricDefinition() : type(MetricType::GAUGE) {}
    
    /**
     * @brief 带参数构造函数
     */
    MetricDefinition(const std::string& metric_name, const std::string& desc, 
                    MetricType metric_type, const std::string& metric_unit = "")
        : name(metric_name), description(desc), type(metric_type), unit(metric_unit) {}
};

/**
 * @brief 性能指标实例
 */
class PerformanceMetric {
public:
    /**
     * @brief 构造函数
     * @param definition 指标定义
     */
    explicit PerformanceMetric(const MetricDefinition& definition);
    
    /**
     * @brief 析构函数
     */
    ~PerformanceMetric() = default;
    
    /**
     * @brief 获取指标定义
     * @return 指标定义
     */
    const MetricDefinition& GetDefinition() const { return definition_; }
    
    /**
     * @brief 设置指标值（仪表类型）
     * @param value 指标值
     * @param labels 标签
     */
    void Set(double value, const std::map<std::string, std::string>& labels = {});
    
    /**
     * @brief 增加指标值（计数器类型）
     * @param increment 增量
     * @param labels 标签
     */
    void Add(double increment = 1.0, const std::map<std::string, std::string>& labels = {});
    
    /**
     * @brief 观察值（直方图和摘要类型）
     * @param value 观察值
     * @param labels 标签
     */
    void Observe(double value, const std::map<std::string, std::string>& labels = {});
    
    /**
     * @brief 获取当前值
     * @param labels 标签
     * @return 当前值
     */
    double GetValue(const std::map<std::string, std::string>& labels = {}) const;
    
    /**
     * @brief 获取所有数据点
     * @return 数据点列表
     */
    std::vector<MetricDataPoint> GetAllDataPoints() const;
    
    /**
     * @brief 获取直方图数据
     * @param labels 标签
     * @return 直方图桶列表
     */
    std::vector<HistogramBucket> GetHistogramBuckets(const std::map<std::string, std::string>& labels = {}) const;
    
    /**
     * @brief 重置指标
     */
    void Reset();

private:
    /**
     * @brief 生成标签键
     * @param labels 标签
     * @return 标签键字符串
     */
    std::string GenerateLabelKey(const std::map<std::string, std::string>& labels) const;

private:
    MetricDefinition definition_;
    mutable std::mutex data_mutex_;
    
    // 不同类型指标的数据存储
    std::map<std::string, double> gauge_values_;                    // 仪表值
    std::map<std::string, double> counter_values_;                  // 计数器值
    std::map<std::string, std::vector<HistogramBucket>> histogram_buckets_; // 直方图桶
    std::map<std::string, std::vector<double>> summary_values_;     // 摘要值
    
    // 历史数据（用于趋势分析）
    std::queue<MetricDataPoint> historical_data_;
    static constexpr size_t MAX_HISTORICAL_SIZE = 1000;
};

/**
 * @brief 性能指标收集器
 */
class PerformanceMetricsCollector {
public:
    /**
     * @brief 构造函数
     */
    PerformanceMetricsCollector();
    
    /**
     * @brief 析构函数
     */
    ~PerformanceMetricsCollector();
    
    /**
     * @brief 启动收集器
     * @return 启动是否成功
     */
    bool Start();
    
    /**
     * @brief 停止收集器
     * @return 停止是否成功
     */
    bool Stop();
    
    /**
     * @brief 注册指标
     * @param definition 指标定义
     * @return 指标实例
     */
    std::shared_ptr<PerformanceMetric> RegisterMetric(const MetricDefinition& definition);
    
    /**
     * @brief 获取指标
     * @param name 指标名称
     * @return 指标实例
     */
    std::shared_ptr<PerformanceMetric> GetMetric(const std::string& name);
    
    /**
     * @brief 获取所有指标
     * @return 指标列表
     */
    std::vector<std::shared_ptr<PerformanceMetric>> GetAllMetrics();
    
    /**
     * @brief 收集系统指标
     */
    void CollectSystemMetrics();
    
    /**
     * @brief 导出指标数据
     * @param format 导出格式（"prometheus", "json", "csv"）
     * @return 导出的数据字符串
     */
    std::string ExportMetrics(const std::string& format = "prometheus");
    
    /**
     * @brief 设置收集间隔
     * @param interval_ms 收集间隔（毫秒）
     */
    void SetCollectionInterval(uint32_t interval_ms);

private:
    /**
     * @brief 收集线程函数
     */
    void CollectionThread();
    
    /**
     * @brief 创建默认指标
     */
    void CreateDefaultMetrics();
    
    /**
     * @brief 导出Prometheus格式
     * @return Prometheus格式字符串
     */
    std::string ExportPrometheusFormat();
    
    /**
     * @brief 导出JSON格式
     * @return JSON格式字符串
     */
    std::string ExportJsonFormat();
    
    /**
     * @brief 导出CSV格式
     * @return CSV格式字符串
     */
    std::string ExportCsvFormat();

private:
    // 线程管理
    std::atomic<bool> running_;
    std::thread collection_thread_;
    std::condition_variable collection_cv_;
    std::mutex collection_mutex_;
    
    // 指标管理
    std::map<std::string, std::shared_ptr<PerformanceMetric>> metrics_;
    std::mutex metrics_mutex_;
    
    // 收集配置
    std::chrono::milliseconds collection_interval_;
    
    // 默认收集间隔
    static constexpr uint32_t DEFAULT_COLLECTION_INTERVAL_MS = 5000; // 5秒
};

/**
 * @brief 性能指标管理器（单例）
 */
class MetricsManager {
public:
    /**
     * @brief 获取单例实例
     * @return 单例实例
     */
    static MetricsManager& GetInstance();
    
    /**
     * @brief 获取收集器
     * @return 收集器实例
     */
    std::shared_ptr<PerformanceMetricsCollector> GetCollector() { return collector_; }
    
    /**
     * @brief 初始化指标系统
     * @return 初始化是否成功
     */
    bool Initialize();
    
    /**
     * @brief 关闭指标系统
     */
    void Shutdown();

private:
    /**
     * @brief 私有构造函数
     */
    MetricsManager() = default;
    
    /**
     * @brief 私有析构函数
     */
    ~MetricsManager() = default;
    
    // 禁用拷贝和赋值
    MetricsManager(const MetricsManager&) = delete;
    MetricsManager& operator=(const MetricsManager&) = delete;

private:
    std::shared_ptr<PerformanceMetricsCollector> collector_;
    std::mutex manager_mutex_;
    bool initialized_ = false;
};

// 便利宏定义
#define METRICS_COUNTER(name, description, unit) \
    MetricsManager::GetInstance().GetCollector()->RegisterMetric( \
        MetricDefinition(name, description, MetricType::COUNTER, unit))

#define METRICS_GAUGE(name, description, unit) \
    MetricsManager::GetInstance().GetCollector()->RegisterMetric( \
        MetricDefinition(name, description, MetricType::GAUGE, unit))

#define METRICS_HISTOGRAM(name, description, unit, buckets) \
    [&]() { \
        MetricDefinition def(name, description, MetricType::HISTOGRAM, unit); \
        def.histogram_buckets = buckets; \
        return MetricsManager::GetInstance().GetCollector()->RegisterMetric(def); \
    }()

} // namespace core
} // namespace timing_server