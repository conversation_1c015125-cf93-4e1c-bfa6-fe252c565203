#pragma once

#include "core/types.h"
#include <functional>
#include <memory>
#include <mutex>
#include <map>
#include <vector>
#include <chrono>

// 前向声明
namespace timing_server {
namespace core {
    struct GnssSignalQuality;
    struct HoldoverQuality;
    class GnssSignalQualityEvaluator;
    class DiscipliningConvergenceEvaluator;
    class HoldoverQualityEvaluator;
}
}

namespace timing_server {
namespace core {

/**
 * @brief 状态转换结果枚举
 * 描述状态转换操作的结果
 */
enum class TransitionResult {
    SUCCESS,        // 转换成功
    INVALID,        // 无效转换
    BLOCKED,        // 转换被阻止
    ERROR          // 转换错误
};

/**
 * @brief 状态转换条件结构
 * 定义状态转换的条件和约束
 */
struct TransitionCondition {
    ClockState from_state;              // 源状态
    ClockState to_state;                // 目标状态
    ClockEvent trigger_event;           // 触发事件
    std::function<bool()> guard;        // 守护条件函数
    std::function<void()> action;       // 转换动作函数
    uint32_t min_duration_ms;          // 最小状态持续时间
    std::string description;            // 转换描述
};

/**
 * @brief 状态信息结构
 * 包含状态的详细信息和统计数据
 */
struct StateInfo {
    ClockState state;                   // 状态类型
    uint64_t enter_time_ns;            // 进入时间
    uint64_t duration_ns;              // 持续时间
    uint32_t enter_count;              // 进入次数
    uint64_t total_duration_ns;        // 总持续时间
    std::string last_transition_reason; // 最后转换原因
};

/**
 * @brief 状态机事件结构
 * 包含事件的完整信息
 */
struct StateMachineEvent {
    ClockEvent event;                   // 事件类型
    uint64_t timestamp_ns;             // 事件时间戳
    std::map<std::string, std::string> context; // 事件上下文
    std::string description;            // 事件描述
    
    StateMachineEvent(ClockEvent evt, const std::string& desc = "") 
        : event(evt), timestamp_ns(GetCurrentTimestampNs()), description(desc) {}
};

/**
 * @brief 状态机监听器接口
 * 用于监听状态机的状态变化和事件
 */
class IStateMachineListener {
public:
    virtual ~IStateMachineListener() = default;
    
    /**
     * @brief 状态进入回调
     * @param state 新状态
     * @param previous_state 前一个状态
     */
    virtual void OnStateEntered(ClockState state, ClockState previous_state) = 0;
    
    /**
     * @brief 状态退出回调
     * @param state 退出的状态
     * @param next_state 下一个状态
     */
    virtual void OnStateExited(ClockState state, ClockState next_state) = 0;
    
    /**
     * @brief 事件处理回调
     * @param event 处理的事件
     * @param current_state 当前状态
     */
    virtual void OnEventProcessed(const StateMachineEvent& event, ClockState current_state) = 0;
    
    /**
     * @brief 转换失败回调
     * @param event 触发事件
     * @param current_state 当前状态
     * @param result 转换结果
     */
    virtual void OnTransitionFailed(const StateMachineEvent& event, ClockState current_state, TransitionResult result) = 0;
};

/**
 * @brief 时钟状态机类
 * 实现四状态时钟驯服状态机的核心逻辑
 */
class ClockStateMachine {
public:
    /**
     * @brief 构造函数
     * @param initial_state 初始状态，默认为FREE_RUN
     */
    explicit ClockStateMachine(ClockState initial_state = ClockState::FREE_RUN);
    
    /**
     * @brief 析构函数
     */
    ~ClockStateMachine();
    
    /**
     * @brief 初始化状态机
     * 设置状态转换规则和初始状态
     * @return 初始化是否成功
     */
    bool Initialize();
    
    /**
     * @brief 启动状态机
     * @return 启动是否成功
     */
    bool Start();
    
    /**
     * @brief 停止状态机
     * @return 停止是否成功
     */
    bool Stop();
    
    /**
     * @brief 处理事件
     * 根据当前状态和事件类型执行相应的状态转换
     * @param event 要处理的事件
     * @return 转换结果
     */
    TransitionResult ProcessEvent(const StateMachineEvent& event);
    
    /**
     * @brief 强制转换到指定状态
     * 用于系统重置或紧急情况
     * @param target_state 目标状态
     * @param reason 转换原因
     * @return 转换是否成功
     */
    bool ForceTransition(ClockState target_state, const std::string& reason = "");
    
    /**
     * @brief 获取当前状态
     * @return 当前时钟状态
     */
    ClockState GetCurrentState() const;
    
    /**
     * @brief 获取状态信息
     * @param state 要查询的状态
     * @return 状态信息
     */
    StateInfo GetStateInfo(ClockState state) const;
    
    /**
     * @brief 获取所有状态信息
     * @return 所有状态的信息映射
     */
    std::map<ClockState, StateInfo> GetAllStateInfo() const;
    
    /**
     * @brief 检查状态转换是否有效
     * @param from_state 源状态
     * @param to_state 目标状态
     * @param event 触发事件
     * @return 转换是否有效
     */
    bool IsTransitionValid(ClockState from_state, ClockState to_state, ClockEvent event) const;
    
    /**
     * @brief 获取状态持续时间
     * @param state 要查询的状态，默认为当前状态
     * @return 状态持续时间（纳秒）
     */
    uint64_t GetStateDuration(ClockState state = ClockState::FREE_RUN) const;
    
    /**
     * @brief 添加状态机监听器
     * @param listener 监听器指针
     */
    void AddListener(std::shared_ptr<IStateMachineListener> listener);
    
    /**
     * @brief 移除状态机监听器
     * @param listener 监听器指针
     */
    void RemoveListener(std::shared_ptr<IStateMachineListener> listener);
    
    /**
     * @brief 保存状态到持久化存储
     * @param file_path 保存文件路径
     * @return 保存是否成功
     */
    bool SaveState(const std::string& file_path) const;
    
    /**
     * @brief 从持久化存储恢复状态
     * @param file_path 状态文件路径
     * @return 恢复是否成功
     */
    bool RestoreState(const std::string& file_path);
    
    /**
     * @brief 重置状态机
     * 清除所有统计信息并回到初始状态
     */
    void Reset();
    
    /**
     * @brief 获取状态机统计信息
     * @return 统计信息字符串
     */
    std::string GetStatistics() const;
    
    /**
     * @brief 设置状态转换守护条件
     * @param from_state 源状态
     * @param to_state 目标状态
     * @param event 触发事件
     * @param guard 守护条件函数
     */
    void SetTransitionGuard(ClockState from_state, ClockState to_state, ClockEvent event, std::function<bool()> guard);
    
    /**
     * @brief 设置状态转换动作
     * @param from_state 源状态
     * @param to_state 目标状态
     * @param event 触发事件
     * @param action 转换动作函数
     */
    void SetTransitionAction(ClockState from_state, ClockState to_state, ClockEvent event, std::function<void()> action);
    
    /**
     * @brief 评估GNSS信号质量
     * @param gnss_data GNSS数据
     * @return 信号质量指标
     */
    GnssSignalQuality EvaluateGnssSignal(const std::string& gnss_data);
    
    /**
     * @brief 添加驯服测量样本
     * @param phase_error_ns 相位误差 (纳秒)
     * @param frequency_error_ppm 频率误差 (ppm)
     */
    void AddDiscipliningSample(double phase_error_ns, double frequency_error_ppm);
    
    /**
     * @brief 检查驯服是否收敛
     * @return 是否已收敛
     */
    bool IsDiscipliningConverged();
    
    /**
     * @brief 开始守时评估
     * @param initial_frequency_offset 初始频率偏移
     */
    void StartHoldoverEvaluation(double initial_frequency_offset);
    
    /**
     * @brief 更新守时状态
     * @param current_frequency_offset 当前频率偏移
     * @param temperature 当前温度
     */
    void UpdateHoldoverStatus(double current_frequency_offset, double temperature);
    
    /**
     * @brief 检查守时是否超时
     * @param max_hours 最大守时小时数
     * @return 是否超时
     */
    bool IsHoldoverTimeout(uint32_t max_hours);
    
    /**
     * @brief 获取当前守时质量
     * @return 守时质量指标
     */
    HoldoverQuality GetHoldoverQuality();

private:
    /**
     * @brief 初始化状态转换表
     * 定义所有有效的状态转换规则
     */
    void InitializeTransitionTable();
    
    /**
     * @brief 执行状态转换
     * @param target_state 目标状态
     * @param event 触发事件
     * @param reason 转换原因
     * @return 转换结果
     */
    TransitionResult ExecuteTransition(ClockState target_state, const StateMachineEvent& event, const std::string& reason = "");
    
    /**
     * @brief 进入状态
     * @param state 要进入的状态
     * @param previous_state 前一个状态
     */
    void EnterState(ClockState state, ClockState previous_state);
    
    /**
     * @brief 退出状态
     * @param state 要退出的状态
     * @param next_state 下一个状态
     */
    void ExitState(ClockState state, ClockState next_state);
    
    /**
     * @brief 通知监听器状态进入
     * @param state 新状态
     * @param previous_state 前一个状态
     */
    void NotifyStateEntered(ClockState state, ClockState previous_state);
    
    /**
     * @brief 通知监听器状态退出
     * @param state 退出的状态
     * @param next_state 下一个状态
     */
    void NotifyStateExited(ClockState state, ClockState next_state);
    
    /**
     * @brief 通知监听器事件处理
     * @param event 处理的事件
     */
    void NotifyEventProcessed(const StateMachineEvent& event);
    
    /**
     * @brief 通知监听器转换失败
     * @param event 触发事件
     * @param result 转换结果
     */
    void NotifyTransitionFailed(const StateMachineEvent& event, TransitionResult result);
    
    /**
     * @brief 查找转换条件
     * @param from_state 源状态
     * @param to_state 目标状态
     * @param event 触发事件
     * @return 转换条件指针，如果未找到返回nullptr
     */
    const TransitionCondition* FindTransitionCondition(ClockState from_state, ClockState to_state, ClockEvent event) const;
    
    /**
     * @brief 检查状态转换是否有效（内部方法，不加锁）
     * @param from_state 源状态
     * @param to_state 目标状态
     * @param event 触发事件
     * @return 转换是否有效
     */
    bool IsTransitionValidInternal(ClockState from_state, ClockState to_state, ClockEvent event) const;
    
    /**
     * @brief 更新状态统计信息
     * @param state 要更新的状态
     */
    void UpdateStateStatistics(ClockState state);

private:
    mutable std::mutex mutex_;                              // 线程安全互斥锁
    ClockState current_state_;                              // 当前状态
    ClockState initial_state_;                              // 初始状态
    bool is_running_;                                       // 运行状态标志
    
    std::map<ClockState, StateInfo> state_info_;           // 状态信息映射
    std::vector<TransitionCondition> transition_table_;    // 状态转换表
    std::vector<std::weak_ptr<IStateMachineListener>> listeners_; // 监听器列表
    
    uint64_t start_time_ns_;                               // 状态机启动时间
    uint32_t total_transitions_;                           // 总转换次数
    mutable std::string last_error_;                       // 最后错误信息
    
    // 质量评估器
    std::unique_ptr<GnssSignalQualityEvaluator> gnss_evaluator_;
    std::unique_ptr<DiscipliningConvergenceEvaluator> disciplining_evaluator_;
    std::unique_ptr<HoldoverQualityEvaluator> holdover_evaluator_;
};

/**
 * @brief 将转换结果转换为字符串
 * @param result 转换结果
 * @return 结果字符串
 */
inline std::string TransitionResultToString(TransitionResult result) {
    switch (result) {
        case TransitionResult::SUCCESS: return "SUCCESS";
        case TransitionResult::INVALID: return "INVALID";
        case TransitionResult::BLOCKED: return "BLOCKED";
        case TransitionResult::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 将时钟事件转换为字符串
 * @param event 时钟事件
 * @return 事件字符串
 */
inline std::string ClockEventToString(ClockEvent event) {
    switch (event) {
        case ClockEvent::GNSS_SIGNAL_ACQUIRED: return "GNSS_SIGNAL_ACQUIRED";
        case ClockEvent::GNSS_SIGNAL_LOST: return "GNSS_SIGNAL_LOST";
        case ClockEvent::CONVERGENCE_ACHIEVED: return "CONVERGENCE_ACHIEVED";
        case ClockEvent::HOLDOVER_TIMEOUT: return "HOLDOVER_TIMEOUT";
        case ClockEvent::HARDWARE_FAULT: return "HARDWARE_FAULT";
        case ClockEvent::SYSTEM_RESTART: return "SYSTEM_RESTART";
        case ClockEvent::MANUAL_RESET: return "MANUAL_RESET";
        default: return "UNKNOWN";
    }
}

} // namespace core
} // namespace timing_server