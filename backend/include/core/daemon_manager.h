#pragma once

#include "core/types.h"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>

namespace timing_server {
namespace core {

/**
 * @brief 守护进程类型枚举
 * 定义系统管理的守护进程类型
 */
enum class DaemonType {
    PTP4L,      // PTP特级主时钟守护进程
    CHRONY,     // NTP Stratum 1守护进程
    TS2PHC      // 时间戳同步守护进程
};

/**
 * @brief 守护进程状态枚举
 * 描述守护进程的当前运行状态
 */
enum class DaemonStatus {
    STOPPED,    // 已停止
    STARTING,   // 启动中
    RUNNING,    // 运行中
    STOPPING,   // 停止中
    ERROR,      // 错误状态
    CRASHED     // 崩溃状态
};

/**
 * @brief 守护进程配置结构
 * 包含守护进程的配置参数
 */
struct DaemonConfig {
    std::string executable_path;        // 可执行文件路径
    std::vector<std::string> arguments; // 命令行参数
    std::string config_file_path;       // 配置文件路径
    std::string working_directory;      // 工作目录
    std::string log_file_path;         // 日志文件路径
    std::map<std::string, std::string> environment; // 环境变量
    bool auto_restart;                  // 是否自动重启
    uint32_t restart_delay_ms;         // 重启延迟（毫秒）
    uint32_t max_restart_attempts;     // 最大重启尝试次数
    uint32_t health_check_interval_ms; // 健康检查间隔（毫秒）
};

/**
 * @brief PTP配置参数结构
 * 用于生成ptp4l配置文件
 */
struct PtpConfig {
    uint8_t domain_number;              // PTP域号
    uint8_t priority1;                  // 优先级1
    uint8_t priority2;                  // 优先级2
    uint8_t clock_class;                // 时钟等级
    uint8_t clock_accuracy;             // 时钟精度
    uint16_t offset_scaled_log_variance; // 偏移缩放对数方差
    std::string network_interface;      // 网络接口名称
    std::string network_transport;      // 网络传输类型（L2/UDPv4/UDPv6）
    std::string delay_mechanism;        // 延迟机制（E2E/P2P）
    int8_t announce_interval;           // 公告间隔（log2秒）
    int8_t sync_interval;               // 同步间隔（log2秒）
    int8_t delay_req_interval;          // 延迟请求间隔（log2秒）
    bool master_only;                   // 仅主时钟模式
    bool slave_only;                    // 仅从时钟模式
    bool two_step_flag;                 // 两步标志
    uint8_t announce_receipt_timeout;   // 公告接收超时
    uint8_t sync_receipt_timeout;       // 同步接收超时
    int logging_level;                  // 日志级别
    bool use_syslog;                    // 使用系统日志
};

/**
 * @brief NTP配置参数结构
 * 用于生成chrony配置文件
 */
struct NtpConfig {
    uint8_t stratum;                    // NTP层级
    std::string reference_id;           // 参考源标识
    std::string phc_device;             // PHC设备路径
    std::string gnss_shm_segment;       // GNSS共享内存段
    double gnss_offset;                 // GNSS偏移校正
    std::string bind_address;           // 绑定地址
    uint16_t port;                      // 服务端口
    std::vector<std::string> allowed_networks; // 允许的网络
    std::vector<std::string> denied_networks;  // 拒绝的网络
    uint32_t client_log_limit;          // 客户端日志限制
    uint32_t rate_limit_interval;       // 频率限制间隔
    uint32_t rate_limit_burst;          // 频率限制突发
    uint32_t rate_limit_leak;           // 频率限制泄漏
    bool lock_all;                      // 锁定内存
    int sched_priority;                 // 调度优先级
    double max_update_skew;             // 最大更新偏斜
    double make_step_threshold;         // 步进阈值
    uint32_t make_step_limit;           // 步进限制
    std::string log_dir;                // 日志目录
    bool log_tracking;                  // 记录跟踪日志
    bool log_measurements;              // 记录测量日志
    bool log_statistics;                // 记录统计日志
    double log_change_threshold;        // 日志变化阈值
};

/**
 * @brief TS2PHC配置参数结构
 * 用于生成ts2phc配置文件
 */
struct Ts2phcConfig {
    std::string pps_device;             // PPS设备路径
    std::string network_interface;      // 网络接口名称
    std::string extts_polarity;         // 外部时间戳极性（rising/falling）
    int64_t extts_correction;           // 外部时间戳校正值（纳秒）
    uint32_t pin_index;                 // 引脚索引
    uint64_t pulse_width;               // 脉冲宽度（纳秒）
    uint32_t dpll_pin;                  // DPLL引脚
    std::string dpll_state;             // DPLL状态（automatic/manual）
    int logging_level;                  // 日志级别
    bool use_syslog;                    // 使用系统日志
    bool verbose;                       // 详细输出
};

/**
 * @brief 守护进程信息结构
 * 包含守护进程的运行时信息
 */
struct DaemonInfo {
    DaemonType type;                    // 守护进程类型
    DaemonStatus status;                // 当前状态
    pid_t process_id;                   // 进程ID
    uint64_t start_time_ns;            // 启动时间戳
    uint64_t last_health_check_ns;     // 最后健康检查时间
    uint32_t restart_count;            // 重启次数
    std::string last_error;            // 最后错误信息
    DaemonConfig config;               // 配置信息
    std::map<std::string, std::string> runtime_info; // 运行时信息
};

/**
 * @brief 守护进程管理器接口
 * 定义守护进程管理的基本操作
 */
class IDaemonManager {
public:
    virtual ~IDaemonManager() = default;
    
    /**
     * @brief 初始化守护进程管理器
     * @return 成功返回true，失败返回false
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 启动守护进程管理器
     * @return 成功返回true，失败返回false
     */
    virtual bool Start() = 0;
    
    /**
     * @brief 停止守护进程管理器
     * @return 成功返回true，失败返回false
     */
    virtual bool Stop() = 0;
    
    /**
     * @brief 启动指定的守护进程
     * @param type 守护进程类型
     * @return 成功返回true，失败返回false
     */
    virtual bool StartDaemon(DaemonType type) = 0;
    
    /**
     * @brief 停止指定的守护进程
     * @param type 守护进程类型
     * @return 成功返回true，失败返回false
     */
    virtual bool StopDaemon(DaemonType type) = 0;
    
    /**
     * @brief 重启指定的守护进程
     * @param type 守护进程类型
     * @return 成功返回true，失败返回false
     */
    virtual bool RestartDaemon(DaemonType type) = 0;
    
    /**
     * @brief 获取守护进程信息
     * @param type 守护进程类型
     * @return 守护进程信息
     */
    virtual DaemonInfo GetDaemonInfo(DaemonType type) = 0;
    
    /**
     * @brief 获取所有守护进程信息
     * @return 所有守护进程信息列表
     */
    virtual std::vector<DaemonInfo> GetAllDaemonInfo() = 0;
    
    /**
     * @brief 更新PTP配置
     * @param config PTP配置参数
     * @return 成功返回true，失败返回false
     */
    virtual bool UpdatePtpConfig(const PtpConfig& config) = 0;
    
    /**
     * @brief 更新NTP配置
     * @param config NTP配置参数
     * @return 成功返回true，失败返回false
     */
    virtual bool UpdateNtpConfig(const NtpConfig& config) = 0;
    
    /**
     * @brief 更新TS2PHC配置
     * @param config TS2PHC配置参数
     * @return 成功返回true，失败返回false
     */
    virtual bool UpdateTs2phcConfig(const Ts2phcConfig& config) = 0;
    
    /**
     * @brief 检测并解决守护进程冲突
     * @return 成功返回true，失败返回false
     */
    virtual bool ResolveConflicts() = 0;
    
    /**
     * @brief 设置状态变化回调函数
     * @param callback 回调函数
     */
    virtual void SetStatusChangeCallback(std::function<void(DaemonType, DaemonStatus, DaemonStatus)> callback) = 0;
};

/**
 * @brief 守护进程管理器实现类
 * 管理ptp4l、chrony、ts2phc等守护进程
 */
class DaemonManager : public IDaemonManager {
public:
    /**
     * @brief 构造函数
     */
    DaemonManager();
    
    /**
     * @brief 析构函数
     */
    virtual ~DaemonManager();
    
    // IDaemonManager接口实现
    bool Initialize() override;
    bool Start() override;
    bool Stop() override;
    bool StartDaemon(DaemonType type) override;
    bool StopDaemon(DaemonType type) override;
    bool RestartDaemon(DaemonType type) override;
    DaemonInfo GetDaemonInfo(DaemonType type) override;
    std::vector<DaemonInfo> GetAllDaemonInfo() override;
    bool UpdatePtpConfig(const PtpConfig& config) override;
    bool UpdateNtpConfig(const NtpConfig& config) override;
    bool UpdateTs2phcConfig(const Ts2phcConfig& config) override;
    bool ResolveConflicts() override;
    void SetStatusChangeCallback(std::function<void(DaemonType, DaemonStatus, DaemonStatus)> callback) override;
    
    /**
     * @brief 根据时钟状态动态更新配置
     * @param clock_state 当前时钟状态
     * @param active_source 当前活跃时间源
     * @return 成功返回true，失败返回false
     */
    bool UpdateConfigForClockState(ClockState clock_state, TimeSource active_source);

private:
    // 内部方法
    bool InitializeDaemonConfigs();
    bool CreateConfigDirectories();
    DaemonConfig CreatePtp4lConfig();
    DaemonConfig CreateChronyConfig();
    DaemonConfig CreateTs2phcConfig();
    
    bool GeneratePtp4lConfigFile(const PtpConfig& config);
    bool GenerateChronyConfigFile(const NtpConfig& config);
    bool GenerateTs2phcConfigFile(const Ts2phcConfig& config);
    
    bool StartDaemonProcess(DaemonType type);
    bool StopDaemonProcess(DaemonType type);
    bool KillDaemonProcess(DaemonType type);
    bool IsDaemonRunning(DaemonType type);
    pid_t GetDaemonPid(DaemonType type);
    
    void MonitoringThread();
    void HealthCheckThread();
    void UpdateDaemonStatus(DaemonType type);
    void HandleDaemonCrash(DaemonType type);
    void NotifyStatusChange(DaemonType type, DaemonStatus old_status, DaemonStatus new_status);
    
    bool CheckPortConflicts();
    bool CheckResourceConflicts();
    bool ResolvePortConflict(DaemonType daemon1, DaemonType daemon2);
    
    // 冲突检测辅助函数
    bool IsPortInUse(uint16_t port);
    std::string GetProcessUsingPort(uint16_t port);
    bool IsProcessRunning(const std::string& process_name);
    std::vector<pid_t> FindProcessesByName(const std::string& process_name);
    
    std::string DaemonTypeToString(DaemonType type);
    std::string DaemonStatusToString(DaemonStatus status);
    
    // 配置生成辅助函数
    PtpConfig GenerateOptimalPtpConfig(ClockState clock_state, TimeSource active_source);
    NtpConfig GenerateOptimalNtpConfig(ClockState clock_state, TimeSource active_source);
    Ts2phcConfig GenerateOptimalTs2phcConfig(ClockState clock_state, TimeSource active_source);
    
    // 故障分析和恢复策略
    std::string AnalyzeCrashReason(DaemonType type);
    bool ImplementDegradationStrategy(DaemonType type, const std::string& crash_reason);
    bool PerformHealthCheck(DaemonType type);
    bool CheckPtp4lHealth();
    bool CheckChronyHealth();
    bool CheckTs2phcHealth();
    
    // SIGCHLD信号处理
    bool InstallSigchldHandler();
    void ProcessChildExit(pid_t pid, int status);

public:
    void HandleSigchld();
    
    // 进程组管理
    bool StartDaemonProcessWithGroup(DaemonType type);
    bool StopDaemonProcessGroup(DaemonType type);
    bool KillDaemonProcessGroup(DaemonType type);
    
    // 成员变量
    std::map<DaemonType, DaemonInfo> daemons_;          // 守护进程信息映射
    std::map<DaemonType, PtpConfig> ptp_configs_;       // PTP配置映射
    std::map<DaemonType, NtpConfig> ntp_configs_;       // NTP配置映射
    std::map<DaemonType, Ts2phcConfig> ts2phc_configs_; // TS2PHC配置映射
    
    std::atomic<bool> running_;                         // 运行状态标志
    std::atomic<bool> sigchld_installed_;               // SIGCHLD处理器安装标志
    std::thread monitoring_thread_;                     // 监控线程
    std::thread health_check_thread_;                   // 健康检查线程
    std::mutex daemons_mutex_;                          // 守护进程信息互斥锁
    std::mutex config_mutex_;                           // 配置互斥锁
    std::condition_variable monitoring_cv_;             // 监控条件变量
    std::condition_variable health_check_cv_;           // 健康检查条件变量
    
    std::function<void(DaemonType, DaemonStatus, DaemonStatus)> status_change_callback_; // 状态变化回调
    
    // 配置路径
    std::string config_dir_;                            // 配置目录
    std::string log_dir_;                               // 日志目录
    std::string pid_dir_;                               // PID文件目录
    
    // 监控参数
    static constexpr uint32_t MONITORING_INTERVAL_MS = 1000;    // 监控间隔（毫秒）
    static constexpr uint32_t HEALTH_CHECK_INTERVAL_MS = 5000;  // 健康检查间隔（毫秒）
    static constexpr uint32_t DEFAULT_RESTART_DELAY_MS = 5000;  // 默认重启延迟（毫秒）
    static constexpr uint32_t MAX_RESTART_ATTEMPTS = 3;         // 最大重启尝试次数
};

/**
 * @brief 将守护进程类型转换为字符串
 * @param type 守护进程类型
 * @return 类型字符串
 */
inline std::string DaemonTypeToString(DaemonType type) {
    switch (type) {
        case DaemonType::PTP4L: return "PTP4L";
        case DaemonType::CHRONY: return "CHRONY";
        case DaemonType::TS2PHC: return "TS2PHC";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 将守护进程状态转换为字符串
 * @param status 守护进程状态
 * @return 状态字符串
 */
inline std::string DaemonStatusToString(DaemonStatus status) {
    switch (status) {
        case DaemonStatus::STOPPED: return "STOPPED";
        case DaemonStatus::STARTING: return "STARTING";
        case DaemonStatus::RUNNING: return "RUNNING";
        case DaemonStatus::STOPPING: return "STOPPING";
        case DaemonStatus::ERROR: return "ERROR";
        case DaemonStatus::CRASHED: return "CRASHED";
        default: return "UNKNOWN";
    }
}

} // namespace core
} // namespace timing_server