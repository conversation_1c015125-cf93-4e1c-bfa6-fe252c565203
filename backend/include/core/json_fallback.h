#pragma once

// 简化的JSON实现，用于在没有jsoncpp时提供基本功能
#ifdef USE_JSONCPP_PLACEHOLDER

#include <string>
#include <map>
#include <vector>
#include <sstream>

namespace Json {

class Value {
public:
    enum ValueType {
        nullValue = 0,
        intValue,
        uintValue,
        realValue,
        stringValue,
        booleanValue,
        arrayValue,
        objectValue
    };

    Value() : type_(nullValue) {}
    Value(int val) : type_(intValue), int_value_(val) {}
    Value(unsigned int val) : type_(uintValue), uint_value_(val) {}
    Value(uint64_t val) : type_(uintValue), uint_value_(static_cast<unsigned int>(val)) {}
    Value(double val) : type_(realValue), real_value_(val) {}
    Value(const std::string& val) : type_(stringValue), string_value_(val) {}
    Value(const char* val) : type_(stringValue), string_value_(val) {}
    Value(bool val) : type_(booleanValue), bool_value_(val) {}
    Value(ValueType type) : type_(type) {
        if (type == arrayValue) {
            array_value_.clear();
        } else if (type == objectValue) {
            object_value_.clear();
        }
    }

    // 类型检查
    bool isNull() const { return type_ == nullValue; }
    bool isInt() const { return type_ == intValue; }
    bool isUInt() const { return type_ == uintValue; }
    bool isDouble() const { return type_ == realValue; }
    bool isString() const { return type_ == stringValue; }
    bool isBool() const { return type_ == booleanValue; }
    bool isArray() const { return type_ == arrayValue; }
    bool isObject() const { return type_ == objectValue; }

    // 值访问
    int asInt() const { return (type_ == intValue) ? int_value_ : 0; }
    unsigned int asUInt() const { return (type_ == uintValue) ? uint_value_ : 0; }
    double asDouble() const { return (type_ == realValue) ? real_value_ : 0.0; }
    std::string asString() const { return (type_ == stringValue) ? string_value_ : ""; }
    bool asBool() const { return (type_ == booleanValue) ? bool_value_ : false; }

    // 数组操作
    void append(const Value& value) {
        if (type_ != arrayValue) {
            type_ = arrayValue;
            array_value_.clear();
        }
        array_value_.push_back(value);
    }

    size_t size() const {
        if (type_ == arrayValue) return array_value_.size();
        if (type_ == objectValue) return object_value_.size();
        return 0;
    }

    // 对象操作
    Value& operator[](const std::string& key) {
        if (type_ != objectValue) {
            type_ = objectValue;
            object_value_.clear();
        }
        return object_value_[key];
    }

    const Value& operator[](const std::string& key) const {
        static Value null_value;
        if (type_ == objectValue) {
            auto it = object_value_.find(key);
            return (it != object_value_.end()) ? it->second : null_value;
        }
        return null_value;
    }

    // 赋值操作
    Value& operator=(int val) { type_ = intValue; int_value_ = val; return *this; }
    Value& operator=(unsigned int val) { type_ = uintValue; uint_value_ = val; return *this; }
    Value& operator=(uint64_t val) { type_ = uintValue; uint_value_ = static_cast<unsigned int>(val); return *this; }
    Value& operator=(double val) { type_ = realValue; real_value_ = val; return *this; }
    Value& operator=(const std::string& val) { type_ = stringValue; string_value_ = val; return *this; }
    Value& operator=(bool val) { type_ = booleanValue; bool_value_ = val; return *this; }

private:
    ValueType type_;
    union {
        int int_value_;
        unsigned int uint_value_;
        double real_value_;
        bool bool_value_;
    };
    std::string string_value_;
    std::vector<Value> array_value_;
    std::map<std::string, Value> object_value_;
};

// 简化的Writer
class StreamWriterBuilder {
public:
    StreamWriterBuilder() = default;
};

// 类型别名
using UInt64 = uint64_t;

// 静态常量
static const Value::ValueType arrayValue = Value::ValueType::arrayValue;
static const Value::ValueType objectValue = Value::ValueType::objectValue;

std::string writeString(const StreamWriterBuilder& builder, const Value& root);

} // namespace Json

#else
// 使用真实的jsoncpp
#include <json/json.h>
#endif // USE_JSONCPP_PLACEHOLDER