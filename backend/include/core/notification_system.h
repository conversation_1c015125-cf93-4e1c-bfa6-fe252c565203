#pragma once

#include "core/alarm_system.h"
#include <string>
#include <vector>
#include <memory>
#include <map>

namespace timing_server {
namespace core {

/**
 * @brief 日志通知器实现
 * 将告警信息记录到系统日志中
 */
class LogNotifier : public IAlarmNotifier {
public:
    /**
     * @brief 构造函数
     */
    LogNotifier();
    
    /**
     * @brief 析构函数
     */
    ~LogNotifier() override = default;
    
    // IAlarmNotifier接口实现
    bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) override;
    NotificationMethod GetNotificationMethod() const override;
    bool IsAvailable() const override;
};

/**
 * @brief 控制台通知器实现
 * 将告警信息输出到控制台
 */
class ConsoleNotifier : public IAlarmNotifier {
public:
    /**
     * @brief 构造函数
     */
    ConsoleNotifier();
    
    /**
     * @brief 析构函数
     */
    ~ConsoleNotifier() override = default;
    
    // IAlarmNotifier接口实现
    bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) override;
    NotificationMethod GetNotificationMethod() const override;
    bool IsAvailable() const override;

private:
    /**
     * @brief 获取告警级别的颜色代码
     * @param level 告警级别
     * @return ANSI颜色代码
     */
    std::string GetLevelColor(AlarmLevel level) const;
    
    /**
     * @brief 重置颜色代码
     * @return ANSI重置代码
     */
    std::string GetResetColor() const;
};

/**
 * @brief 系统日志通知器实现
 * 将告警信息发送到系统syslog
 */
class SyslogNotifier : public IAlarmNotifier {
public:
    /**
     * @brief 构造函数
     * @param ident 系统日志标识符
     */
    explicit SyslogNotifier(const std::string& ident = "timing-server");
    
    /**
     * @brief 析构函数
     */
    ~SyslogNotifier() override;
    
    // IAlarmNotifier接口实现
    bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) override;
    NotificationMethod GetNotificationMethod() const override;
    bool IsAvailable() const override;

private:
    /**
     * @brief 将告警级别转换为syslog优先级
     * @param level 告警级别
     * @return syslog优先级
     */
    int GetSyslogPriority(AlarmLevel level) const;
    
    std::string ident_;
    bool syslog_opened_;
};

/**
 * @brief Webhook通知器实现
 * 通过HTTP POST发送告警信息到指定URL
 */
class WebhookNotifier : public IAlarmNotifier {
public:
    /**
     * @brief 构造函数
     * @param webhook_url Webhook URL
     * @param timeout_seconds 超时时间（秒）
     */
    explicit WebhookNotifier(const std::string& webhook_url, uint32_t timeout_seconds = 10);
    
    /**
     * @brief 析构函数
     */
    ~WebhookNotifier() override = default;
    
    // IAlarmNotifier接口实现
    bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) override;
    NotificationMethod GetNotificationMethod() const override;
    bool IsAvailable() const override;
    
    /**
     * @brief 设置HTTP头
     * @param headers HTTP头映射
     */
    void SetHeaders(const std::map<std::string, std::string>& headers);
    
    /**
     * @brief 设置认证信息
     * @param username 用户名
     * @param password 密码
     */
    void SetAuthentication(const std::string& username, const std::string& password);

private:
    /**
     * @brief 将告警事件转换为JSON格式
     * @param alarm 告警事件
     * @return JSON字符串
     */
    std::string AlarmToJson(const AlarmEvent& alarm) const;
    
    /**
     * @brief 发送HTTP POST请求
     * @param url 目标URL
     * @param data 请求数据
     * @param headers HTTP头
     * @return 是否成功
     */
    bool SendHttpPost(const std::string& url, const std::string& data, 
                     const std::map<std::string, std::string>& headers) const;
    
    std::string webhook_url_;
    uint32_t timeout_seconds_;
    std::map<std::string, std::string> headers_;
    std::string username_;
    std::string password_;
};

/**
 * @brief 电子邮件通知器实现
 * 通过SMTP发送告警邮件
 */
class EmailNotifier : public IAlarmNotifier {
public:
    /**
     * @brief SMTP配置结构
     */
    struct SmtpConfig {
        std::string server;         // SMTP服务器地址
        uint16_t port;             // SMTP端口
        std::string username;       // 用户名
        std::string password;       // 密码
        bool use_tls;              // 是否使用TLS
        std::string from_address;   // 发件人地址
        std::string from_name;      // 发件人姓名
        
        SmtpConfig() : port(587), use_tls(true) {}
    };
    
    /**
     * @brief 构造函数
     * @param config SMTP配置
     */
    explicit EmailNotifier(const SmtpConfig& config);
    
    /**
     * @brief 析构函数
     */
    ~EmailNotifier() override = default;
    
    // IAlarmNotifier接口实现
    bool SendNotification(const AlarmEvent& alarm, const std::vector<std::string>& targets) override;
    NotificationMethod GetNotificationMethod() const override;
    bool IsAvailable() const override;

private:
    /**
     * @brief 生成邮件主题
     * @param alarm 告警事件
     * @return 邮件主题
     */
    std::string GenerateSubject(const AlarmEvent& alarm) const;
    
    /**
     * @brief 生成邮件正文
     * @param alarm 告警事件
     * @return 邮件正文
     */
    std::string GenerateBody(const AlarmEvent& alarm) const;
    
    /**
     * @brief 发送SMTP邮件
     * @param to_addresses 收件人地址列表
     * @param subject 邮件主题
     * @param body 邮件正文
     * @return 是否成功
     */
    bool SendSmtpEmail(const std::vector<std::string>& to_addresses, 
                      const std::string& subject, const std::string& body) const;
    
    SmtpConfig config_;
};

/**
 * @brief 通知系统工厂类
 * 用于创建和管理各种通知器实例
 */
class NotificationFactory {
public:
    /**
     * @brief 创建日志通知器
     * @return 日志通知器实例
     */
    static std::shared_ptr<IAlarmNotifier> CreateLogNotifier();
    
    /**
     * @brief 创建控制台通知器
     * @return 控制台通知器实例
     */
    static std::shared_ptr<IAlarmNotifier> CreateConsoleNotifier();
    
    /**
     * @brief 创建系统日志通知器
     * @param ident 系统日志标识符
     * @return 系统日志通知器实例
     */
    static std::shared_ptr<IAlarmNotifier> CreateSyslogNotifier(const std::string& ident = "timing-server");
    
    /**
     * @brief 创建Webhook通知器
     * @param webhook_url Webhook URL
     * @param timeout_seconds 超时时间
     * @return Webhook通知器实例
     */
    static std::shared_ptr<IAlarmNotifier> CreateWebhookNotifier(const std::string& webhook_url, 
                                                                uint32_t timeout_seconds = 10);
    
    /**
     * @brief 创建邮件通知器
     * @param config SMTP配置
     * @return 邮件通知器实例
     */
    static std::shared_ptr<IAlarmNotifier> CreateEmailNotifier(const EmailNotifier::SmtpConfig& config);
    
    /**
     * @brief 创建默认通知器集合
     * @return 默认通知器列表
     */
    static std::vector<std::shared_ptr<IAlarmNotifier>> CreateDefaultNotifiers();
};

} // namespace core
} // namespace timing_server