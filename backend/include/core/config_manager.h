#pragma once

#include "types.h"
#include <string>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <atomic>
#include <vector>

namespace timing_server {
namespace core {

/**
 * @brief 配置变更回调函数类型
 * 当配置发生变化时调用此回调函数
 */
using ConfigChangeCallback = std::function<void(const TimingConfig& old_config, const TimingConfig& new_config)>;

/**
 * @brief 配置验证结果结构
 * 包含验证结果和错误信息
 */
struct ConfigValidationResult {
    bool is_valid;                          // 配置是否有效
    std::vector<std::string> errors;        // 错误信息列表
    std::vector<std::string> warnings;      // 警告信息列表
    
    /**
     * @brief 默认构造函数，创建有效的验证结果
     */
    ConfigValidationResult() : is_valid(true) {}
    
    /**
     * @brief 添加错误信息
     * @param error 错误描述
     */
    void AddError(const std::string& error) {
        errors.push_back(error);
        is_valid = false;
    }
    
    /**
     * @brief 添加警告信息
     * @param warning 警告描述
     */
    void AddWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
    
    /**
     * @brief 获取所有错误和警告的摘要
     * @return 摘要字符串
     */
    std::string GetSummary() const;
};

/**
 * @brief 配置管理器类
 * 负责配置文件的加载、保存、验证和热重载
 */
class ConfigManager {
public:
    /**
     * @brief 构造函数
     * @param config_file_path 配置文件路径
     */
    explicit ConfigManager(const std::string& config_file_path);
    
    /**
     * @brief 析构函数
     */
    ~ConfigManager();
    
    /**
     * @brief 初始化配置管理器
     * @return 成功返回true，失败返回false
     */
    bool Initialize();
    
    /**
     * @brief 关闭配置管理器
     */
    void Shutdown();
    
    /**
     * @brief 加载配置文件
     * @return 成功返回true，失败返回false
     */
    bool LoadConfig();
    
    /**
     * @brief 保存配置到文件
     * @param config 要保存的配置
     * @return 成功返回true，失败返回false
     */
    bool SaveConfig(const TimingConfig& config);
    
    /**
     * @brief 获取当前配置
     * @return 当前配置的副本
     */
    TimingConfig GetConfig() const;
    
    /**
     * @brief 更新配置
     * @param new_config 新配置
     * @return 验证结果
     */
    ConfigValidationResult UpdateConfig(const TimingConfig& new_config);
    
    /**
     * @brief 验证配置
     * @param config 要验证的配置
     * @return 验证结果
     */
    static ConfigValidationResult ValidateConfig(const TimingConfig& config);
    
    /**
     * @brief 获取默认配置
     * @return 默认配置
     */
    static TimingConfig GetDefaultConfig();
    
    /**
     * @brief 注册配置变更回调
     * @param callback 回调函数
     * @return 回调ID，用于取消注册
     */
    uint32_t RegisterChangeCallback(const ConfigChangeCallback& callback);
    
    /**
     * @brief 取消注册配置变更回调
     * @param callback_id 回调ID
     */
    void UnregisterChangeCallback(uint32_t callback_id);
    
    /**
     * @brief 启用配置热重载
     * @param enable 是否启用
     */
    void EnableHotReload(bool enable);
    
    /**
     * @brief 检查配置文件是否已修改
     * @return 如果文件已修改返回true
     */
    bool IsConfigFileModified() const;
    
    /**
     * @brief 重置配置为默认值
     * @return 成功返回true，失败返回false
     */
    bool ResetToDefault();
    
    /**
     * @brief 导出配置为JSON字符串
     * @param pretty_print 是否格式化输出
     * @return JSON字符串
     */
    std::string ExportConfigJson(bool pretty_print = true) const;
    
    /**
     * @brief 从JSON字符串导入配置
     * @param json_str JSON字符串
     * @return 验证结果
     */
    ConfigValidationResult ImportConfigJson(const std::string& json_str);

private:
    /**
     * @brief 配置文件监控线程函数
     */
    void FileWatcherThread();
    
    /**
     * @brief 通知所有注册的回调函数
     * @param old_config 旧配置
     * @param new_config 新配置
     */
    void NotifyCallbacks(const TimingConfig& old_config, const TimingConfig& new_config);
    
    /**
     * @brief 获取文件最后修改时间
     * @param file_path 文件路径
     * @return 修改时间戳，失败返回0
     */
    static uint64_t GetFileModificationTime(const std::string& file_path);
    
    /**
     * @brief 创建配置文件的备份
     * @return 成功返回true，失败返回false
     */
    bool CreateConfigBackup();
    
    /**
     * @brief 从备份恢复配置文件
     * @return 成功返回true，失败返回false
     */
    bool RestoreFromBackup();
    
    /**
     * @brief 解析JSON配置文件
     * @param json_content JSON内容
     * @param config 输出配置
     * @return 成功返回true，失败返回false
     */
    static bool ParseJsonConfig(const std::string& json_content, TimingConfig& config);
    
    /**
     * @brief 生成JSON配置内容
     * @param config 配置对象
     * @param pretty_print 是否格式化
     * @return JSON字符串
     */
    static std::string GenerateJsonConfig(const TimingConfig& config, bool pretty_print = true);

private:
    std::string config_file_path_;              // 配置文件路径
    mutable std::mutex config_mutex_;           // 配置访问互斥锁
    TimingConfig current_config_;               // 当前配置
    uint64_t last_file_modification_time_;      // 文件最后修改时间
    
    // 热重载相关
    std::atomic<bool> hot_reload_enabled_;      // 是否启用热重载
    std::atomic<bool> shutdown_requested_;      // 是否请求关闭
    std::unique_ptr<std::thread> file_watcher_thread_; // 文件监控线程
    
    // 回调管理
    std::mutex callbacks_mutex_;                // 回调列表互斥锁
    std::map<uint32_t, ConfigChangeCallback> callbacks_; // 回调函数映射
    uint32_t next_callback_id_;                 // 下一个回调ID
    
    // 备份管理
    std::string backup_file_path_;              // 备份文件路径
    static constexpr size_t MAX_BACKUP_COUNT = 5; // 最大备份数量
};

/**
 * @brief 配置验证器类
 * 提供详细的配置验证功能
 */
class ConfigValidator {
public:
    /**
     * @brief 验证时间源优先级配置
     * @param priorities 优先级配置
     * @param result 验证结果
     */
    static void ValidateTimeSourcePriorities(const TimeSourcePriority& priorities, ConfigValidationResult& result);
    
    /**
     * @brief 验证驯服参数配置
     * @param discipline 驯服参数
     * @param result 验证结果
     */
    static void ValidateDiscipliningParameters(const DiscipliningParameters& discipline, ConfigValidationResult& result);
    
    /**
     * @brief 验证守时参数配置
     * @param holdover 守时参数
     * @param result 验证结果
     */
    static void ValidateHoldoverParameters(const HoldoverParameters& holdover, ConfigValidationResult& result);
    
    /**
     * @brief 验证告警阈值配置
     * @param alarms 告警阈值
     * @param result 验证结果
     */
    static void ValidateAlarmThresholds(const AlarmThresholds& alarms, ConfigValidationResult& result);
    
    /**
     * @brief 验证配置的逻辑一致性
     * @param config 完整配置
     * @param result 验证结果
     */
    static void ValidateConfigConsistency(const TimingConfig& config, ConfigValidationResult& result);

private:
    /**
     * @brief 验证数值范围
     * @param value 数值
     * @param min_val 最小值
     * @param max_val 最大值
     * @param field_name 字段名
     * @param result 验证结果
     */
    template<typename T>
    static void ValidateRange(T value, T min_val, T max_val, const std::string& field_name, ConfigValidationResult& result);
    
    /**
     * @brief 验证正数
     * @param value 数值
     * @param field_name 字段名
     * @param result 验证结果
     */
    template<typename T>
    static void ValidatePositive(T value, const std::string& field_name, ConfigValidationResult& result);
};

} // namespace core
} // namespace timing_server