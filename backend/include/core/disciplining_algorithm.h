#pragma once

#include "core/types.h"
#include "hal/interfaces.h"
#include <memory>
#include <vector>
#include <mutex>
#include <chrono>
#include <deque>
#include <functional>

namespace timing_server {
namespace core {

/**
 * @brief 驯服算法类型枚举
 * 定义不同的时钟驯服算法类型
 */
enum class DiscipliningAlgorithmType {
    PLL,        // 锁相环算法 - 适用于相位跟踪
    FLL,        // 锁频环算法 - 适用于频率跟踪
    HYBRID      // 混合算法 - 结合PLL和FLL优势
};

/**
 * @brief 驯服状态枚举
 * 描述驯服算法的当前工作状态
 */
enum class DiscipliningState {
    INITIALIZING,   // 初始化状态
    ACQUIRING,      // 信号捕获状态
    TRACKING,       // 跟踪状态
    CONVERGED,      // 收敛状态
    HOLDOVER        // 守时状态
};

/**
 * @brief 时间测量数据结构
 * 包含时间测量的原始数据和质量指标
 */
struct TimeMeasurement {
    uint64_t timestamp_ns;          // 测量时间戳（纳秒）
    double phase_offset_ns;         // 相位偏移（纳秒）
    double frequency_offset_ppm;    // 频率偏移（ppm）
    double measurement_noise_ns;    // 测量噪声（纳秒）
    TimeSource source;              // 测量来源
    bool is_valid;                  // 测量有效性
    
    TimeMeasurement() : timestamp_ns(0), phase_offset_ns(0.0), frequency_offset_ppm(0.0),
                       measurement_noise_ns(0.0), source(TimeSource::SYSTEM_CLOCK), is_valid(false) {}
};

/**
 * @brief 驯服算法状态结构
 * 包含驯服算法的内部状态信息
 */
struct DiscipliningStatus {
    DiscipliningState state;            // 当前状态
    DiscipliningAlgorithmType algorithm; // 算法类型
    double current_phase_error_ns;      // 当前相位误差（纳秒）
    double current_frequency_error_ppm; // 当前频率误差（ppm）
    double loop_bandwidth_hz;           // 环路带宽（Hz）
    double time_constant_s;             // 时间常数（秒）
    uint64_t convergence_time_s;        // 收敛时间（秒）
    bool is_converged;                  // 是否已收敛
    uint64_t last_update_ns;           // 最后更新时间
};

/**
 * @brief 铷钟学习数据结构
 * 存储铷钟特性学习的结果数据
 */
struct RubidiumLearningData {
    // 频率老化特性
    double aging_rate_ppm_per_day;      // 老化率（ppm/天）
    double aging_acceleration;          // 老化加速度
    
    // 温度补偿特性
    double temperature_coefficient;     // 温度系数（ppm/°C）
    double temperature_reference;       // 参考温度（°C）
    
    // 学习统计信息
    uint64_t learning_duration_hours;   // 学习时长（小时）
    uint64_t sample_count;              // 样本数量
    double confidence_level;            // 置信度（0-1）
    uint64_t last_update_ns;           // 最后更新时间
    
    // 预测模型参数
    std::vector<double> model_coefficients; // 模型系数
    double prediction_accuracy;         // 预测精度
    
    RubidiumLearningData() : aging_rate_ppm_per_day(0.0), aging_acceleration(0.0),
                            temperature_coefficient(0.0), temperature_reference(25.0),
                            learning_duration_hours(0), sample_count(0), confidence_level(0.0),
                            last_update_ns(0), prediction_accuracy(0.0) {}
};

/**
 * @brief 守时预测数据结构
 * 用于守时期间的时间预测和补偿
 */
struct HoldoverPrediction {
    double predicted_frequency_drift_ppm;   // 预测频率漂移（ppm）
    double predicted_phase_drift_ns;        // 预测相位漂移（纳秒）
    double prediction_uncertainty_ns;       // 预测不确定度（纳秒）
    uint64_t prediction_horizon_hours;      // 预测时间范围（小时）
    double temperature_compensation_ppm;    // 温度补偿（ppm）
    uint64_t prediction_timestamp_ns;       // 预测时间戳
    
    HoldoverPrediction() : predicted_frequency_drift_ppm(0.0), predicted_phase_drift_ns(0.0),
                          prediction_uncertainty_ns(0.0), prediction_horizon_hours(0),
                          temperature_compensation_ppm(0.0), prediction_timestamp_ns(0) {}
};

/**
 * @brief PLL/FLL驯服算法接口
 * 定义时钟驯服算法的核心接口
 */
class IDiscipliningAlgorithm {
public:
    virtual ~IDiscipliningAlgorithm() = default;
    
    /**
     * @brief 初始化驯服算法
     * @param config 驯服参数配置
     * @return 初始化是否成功
     */
    virtual bool Initialize(const DiscipliningParameters& config) = 0;
    
    /**
     * @brief 处理时间测量数据
     * @param measurement 时间测量数据
     * @return 处理是否成功
     */
    virtual bool ProcessMeasurement(const TimeMeasurement& measurement) = 0;
    
    /**
     * @brief 获取频率校正值
     * @return 频率校正值（ppm）
     */
    virtual double GetFrequencyCorrection() = 0;
    
    /**
     * @brief 获取相位校正值
     * @return 相位校正值（纳秒）
     */
    virtual double GetPhaseCorrection() = 0;
    
    /**
     * @brief 检查是否已收敛
     * @return 收敛状态
     */
    virtual bool IsConverged() = 0;
    
    /**
     * @brief 获取驯服状态
     * @return 驯服状态信息
     */
    virtual DiscipliningStatus GetStatus() = 0;
    
    /**
     * @brief 重置驯服算法
     */
    virtual void Reset() = 0;
};

/**
 * @brief 混合PLL/FLL驯服算法实现
 * 结合锁相环和锁频环的优势，实现高精度时钟驯服
 * 
 * 算法原理：
 * 1. 初始阶段使用FLL快速捕获频率
 * 2. 频率稳定后切换到PLL进行精确相位跟踪
 * 3. 动态调整环路参数以优化性能
 */
class HybridDiscipliningAlgorithm : public IDiscipliningAlgorithm {
public:
    /**
     * @brief 构造函数
     */
    HybridDiscipliningAlgorithm();
    
    /**
     * @brief 析构函数
     */
    ~HybridDiscipliningAlgorithm() override;
    
    // IDiscipliningAlgorithm接口实现
    bool Initialize(const DiscipliningParameters& config) override;
    bool ProcessMeasurement(const TimeMeasurement& measurement) override;
    double GetFrequencyCorrection() override;
    double GetPhaseCorrection() override;
    bool IsConverged() override;
    DiscipliningStatus GetStatus() override;
    void Reset() override;
    
    /**
     * @brief 设置算法类型
     * @param type 算法类型
     */
    void SetAlgorithmType(DiscipliningAlgorithmType type);
    
    /**
     * @brief 获取环路带宽
     * @return 当前环路带宽（Hz）
     */
    double GetLoopBandwidth() const;
    
    /**
     * @brief 设置环路带宽
     * @param bandwidth 环路带宽（Hz）
     */
    void SetLoopBandwidth(double bandwidth);

private:
    /**
     * @brief PLL算法处理
     * @param measurement 时间测量数据
     */
    void ProcessPLL(const TimeMeasurement& measurement);
    
    /**
     * @brief FLL算法处理
     * @param measurement 时间测量数据
     */
    void ProcessFLL(const TimeMeasurement& measurement);
    
    /**
     * @brief 混合算法处理
     * @param measurement 时间测量数据
     */
    void ProcessHybrid(const TimeMeasurement& measurement);
    
    /**
     * @brief 更新环路滤波器
     * @param phase_error 相位误差
     * @param frequency_error 频率误差
     */
    void UpdateLoopFilter(double phase_error, double frequency_error);
    
    /**
     * @brief 检查收敛条件
     * @return 是否满足收敛条件
     */
    bool CheckConvergence();
    
    /**
     * @brief 动态调整环路参数
     */
    void AdaptLoopParameters();
    
    /**
     * @brief 计算测量噪声
     * @param measurements 测量数据队列
     * @return 噪声估计值
     */
    double EstimateNoise(const std::deque<TimeMeasurement>& measurements);

private:
    // 算法配置
    DiscipliningParameters config_;
    DiscipliningStatus status_;
    
    // 环路滤波器状态
    double integrator_state_;           // 积分器状态
    double proportional_gain_;          // 比例增益
    double integral_gain_;              // 积分增益
    double loop_bandwidth_;             // 环路带宽
    double damping_factor_;             // 阻尼因子
    
    // 测量数据缓存
    std::deque<TimeMeasurement> measurement_history_;
    static constexpr size_t MAX_HISTORY_SIZE = 1000;
    
    // 收敛检测
    std::deque<double> phase_error_history_;
    std::deque<double> frequency_error_history_;
    static constexpr size_t CONVERGENCE_WINDOW_SIZE = 100;
    
    // 线程安全
    mutable std::mutex algorithm_mutex_;
    
    // 统计信息
    uint64_t measurement_count_;
    uint64_t convergence_start_time_;
};

/**
 * @brief 铷钟学习算法类
 * 负责学习铷钟的特性参数，包括老化率和温度系数
 * 
 * 学习原理：
 * 1. 在GNSS锁定期间，持续比较铷钟频率与GNSS参考
 * 2. 使用最小二乘法拟合老化曲线和温度特性
 * 3. 建立预测模型，用于守时期间的频率补偿
 */
class RubidiumLearningAlgorithm {
public:
    /**
     * @brief 构造函数
     */
    RubidiumLearningAlgorithm();
    
    /**
     * @brief 析构函数
     */
    ~RubidiumLearningAlgorithm();
    
    /**
     * @brief 初始化学习算法
     * @param config 守时参数配置
     * @return 初始化是否成功
     */
    bool Initialize(const HoldoverParameters& config);
    
    /**
     * @brief 添加学习样本
     * @param frequency_offset 频率偏移（ppm）
     * @param temperature 温度（摄氏度）
     * @param timestamp_ns 时间戳（纳秒）
     * @return 添加是否成功
     */
    bool AddSample(double frequency_offset, double temperature, uint64_t timestamp_ns);
    
    /**
     * @brief 获取学习数据
     * @return 铷钟学习数据
     */
    RubidiumLearningData GetLearningData() const;
    
    /**
     * @brief 预测频率偏移
     * @param temperature 当前温度（摄氏度）
     * @param time_since_reference_hours 距离参考时间的小时数
     * @return 预测的频率偏移（ppm）
     */
    double PredictFrequencyOffset(double temperature, double time_since_reference_hours) const;
    
    /**
     * @brief 获取温度补偿值
     * @param current_temperature 当前温度（摄氏度）
     * @return 温度补偿值（ppm）
     */
    double GetTemperatureCompensation(double current_temperature) const;
    
    /**
     * @brief 保存学习数据到文件
     * @param filename 文件名
     * @return 保存是否成功
     */
    bool SaveLearningData(const std::string& filename) const;
    
    /**
     * @brief 从文件加载学习数据
     * @param filename 文件名
     * @return 加载是否成功
     */
    bool LoadLearningData(const std::string& filename);
    
    /**
     * @brief 重置学习数据
     */
    void Reset();

private:
    /**
     * @brief 学习样本数据结构
     */
    struct LearningSample {
        double frequency_offset_ppm;    // 频率偏移
        double temperature;             // 温度
        uint64_t timestamp_ns;         // 时间戳
        
        LearningSample(double freq, double temp, uint64_t ts)
            : frequency_offset_ppm(freq), temperature(temp), timestamp_ns(ts) {}
    };
    
    /**
     * @brief 更新老化模型
     */
    void UpdateAgingModel();
    
    /**
     * @brief 更新温度模型
     */
    void UpdateTemperatureModel();
    
    /**
     * @brief 计算线性回归
     * @param x_values X值数组
     * @param y_values Y值数组
     * @param slope 输出斜率
     * @param intercept 输出截距
     * @param correlation 输出相关系数
     * @return 计算是否成功
     */
    bool CalculateLinearRegression(const std::vector<double>& x_values,
                                  const std::vector<double>& y_values,
                                  double& slope, double& intercept, double& correlation) const;
    
    /**
     * @brief 计算预测精度
     * @return 预测精度（ppm）
     */
    double CalculatePredictionAccuracy() const;

private:
    // 配置参数
    HoldoverParameters config_;
    
    // 学习数据
    RubidiumLearningData learning_data_;
    std::vector<LearningSample> samples_;
    
    // 模型参数
    double aging_slope_;                // 老化斜率
    double aging_intercept_;            // 老化截距
    double temperature_slope_;          // 温度斜率
    double temperature_intercept_;      // 温度截距
    
    // 统计信息
    uint64_t reference_timestamp_;      // 参考时间戳
    double reference_temperature_;      // 参考温度
    
    // 线程安全
    mutable std::mutex learning_mutex_;
    
    // 最大样本数量
    static constexpr size_t MAX_SAMPLES = 100000;
    
    // 最小学习时间（小时）
    static constexpr uint64_t MIN_LEARNING_HOURS = 24;
};

/**
 * @brief 守时预测算法类
 * 在守时期间提供时间预测和补偿机制
 * 
 * 预测原理：
 * 1. 基于铷钟学习数据预测频率漂移
 * 2. 考虑温度变化的影响进行补偿
 * 3. 提供不确定度估计和置信区间
 */
class HoldoverPredictionAlgorithm {
public:
    /**
     * @brief 构造函数
     */
    HoldoverPredictionAlgorithm();
    
    /**
     * @brief 析构函数
     */
    ~HoldoverPredictionAlgorithm();
    
    /**
     * @brief 初始化预测算法
     * @param learning_data 铷钟学习数据
     * @param config 守时参数配置
     * @return 初始化是否成功
     */
    bool Initialize(const RubidiumLearningData& learning_data, const HoldoverParameters& config);
    
    /**
     * @brief 开始守时预测
     * @param initial_frequency_offset 初始频率偏移（ppm）
     * @param initial_temperature 初始温度（摄氏度）
     * @return 开始是否成功
     */
    bool StartHoldover(double initial_frequency_offset, double initial_temperature);
    
    /**
     * @brief 更新温度信息
     * @param current_temperature 当前温度（摄氏度）
     */
    void UpdateTemperature(double current_temperature);
    
    /**
     * @brief 获取当前预测
     * @return 守时预测数据
     */
    HoldoverPrediction GetCurrentPrediction() const;
    
    /**
     * @brief 获取频率校正值
     * @return 频率校正值（ppm）
     */
    double GetFrequencyCorrection() const;
    
    /**
     * @brief 获取相位校正值
     * @return 相位校正值（纳秒）
     */
    double GetPhaseCorrection() const;
    
    /**
     * @brief 获取预测精度
     * @param holdover_duration_hours 守时时长（小时）
     * @return 预测精度（纳秒）
     */
    double GetPredictionAccuracy(double holdover_duration_hours) const;
    
    /**
     * @brief 停止守时预测
     */
    void StopHoldover();

private:
    /**
     * @brief 更新预测模型
     */
    void UpdatePrediction();
    
    /**
     * @brief 计算老化补偿
     * @param elapsed_hours 经过的小时数
     * @return 老化补偿值（ppm）
     */
    double CalculateAgingCompensation(double elapsed_hours) const;
    
    /**
     * @brief 计算温度补偿
     * @param current_temperature 当前温度
     * @return 温度补偿值（ppm）
     */
    double CalculateTemperatureCompensation(double current_temperature) const;
    
    /**
     * @brief 计算不确定度
     * @param elapsed_hours 经过的小时数
     * @return 不确定度（纳秒）
     */
    double CalculateUncertainty(double elapsed_hours) const;

private:
    // 学习数据和配置
    RubidiumLearningData learning_data_;
    HoldoverParameters config_;
    
    // 守时状态
    bool is_holdover_active_;
    uint64_t holdover_start_time_;
    double initial_frequency_offset_;
    double initial_temperature_;
    double current_temperature_;
    
    // 预测结果
    HoldoverPrediction current_prediction_;
    
    // 累积误差
    double accumulated_phase_error_ns_;
    double accumulated_frequency_error_ppm_;
    
    // 线程安全
    mutable std::mutex prediction_mutex_;
};

/**
 * @brief 时钟驯服管理器类
 * 统一管理所有驯服算法，协调GNSS驯服和守时预测
 */
class ClockDiscipliningManager {
public:
    /**
     * @brief 构造函数
     */
    ClockDiscipliningManager();
    
    /**
     * @brief 析构函数
     */
    ~ClockDiscipliningManager();
    
    /**
     * @brief 初始化驯服管理器
     * @param config 授时配置
     * @return 初始化是否成功
     */
    bool Initialize(const TimingConfig& config);
    
    /**
     * @brief 开始GNSS驯服
     * @return 开始是否成功
     */
    bool StartGnssDisciplining();
    
    /**
     * @brief 停止GNSS驯服
     */
    void StopGnssDisciplining();
    
    /**
     * @brief 开始守时模式
     * @return 开始是否成功
     */
    bool StartHoldover();
    
    /**
     * @brief 停止守时模式
     */
    void StopHoldover();
    
    /**
     * @brief 处理时间测量
     * @param measurement 时间测量数据
     * @return 处理是否成功
     */
    bool ProcessTimeMeasurement(const TimeMeasurement& measurement);
    
    /**
     * @brief 更新铷钟温度
     * @param temperature 当前温度（摄氏度）
     */
    void UpdateRubidiumTemperature(double temperature);
    
    /**
     * @brief 获取频率校正值
     * @return 频率校正值（ppm）
     */
    double GetFrequencyCorrection() const;
    
    /**
     * @brief 获取相位校正值
     * @return 相位校正值（纳秒）
     */
    double GetPhaseCorrection() const;
    
    /**
     * @brief 检查驯服是否收敛
     * @return 收敛状态
     */
    bool IsDiscipliningConverged() const;
    
    /**
     * @brief 获取驯服状态
     * @return 驯服状态信息
     */
    DiscipliningStatus GetDiscipliningStatus() const;
    
    /**
     * @brief 获取铷钟学习数据
     * @return 学习数据
     */
    RubidiumLearningData GetRubidiumLearningData() const;
    
    /**
     * @brief 获取守时预测
     * @return 守时预测数据
     */
    HoldoverPrediction GetHoldoverPrediction() const;
    
    /**
     * @brief 设置状态变化回调
     * @param callback 回调函数
     */
    void SetStatusChangeCallback(std::function<void(DiscipliningState, DiscipliningState)> callback);

private:
    /**
     * @brief 通知状态变化
     * @param old_state 旧状态
     * @param new_state 新状态
     */
    void NotifyStatusChange(DiscipliningState old_state, DiscipliningState new_state);

private:
    // 算法实例
    std::unique_ptr<HybridDiscipliningAlgorithm> disciplining_algorithm_;
    std::unique_ptr<RubidiumLearningAlgorithm> learning_algorithm_;
    std::unique_ptr<HoldoverPredictionAlgorithm> prediction_algorithm_;
    
    // 配置
    TimingConfig config_;
    
    // 状态管理
    DiscipliningState current_state_;
    bool is_gnss_disciplining_active_;
    bool is_holdover_active_;
    
    // 状态变化回调
    std::function<void(DiscipliningState, DiscipliningState)> status_change_callback_;
    
    // 线程安全
    mutable std::mutex manager_mutex_;
};

} // namespace core
} // namespace timing_server