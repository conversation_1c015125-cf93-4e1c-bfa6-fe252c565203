#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <map>
#include <chrono>
#include <sstream>
#include <iomanip>

namespace timing_server {
namespace core {

/**
 * @brief 时钟状态枚举
 * 定义时钟驯服状态机的四个主要状态
 */
enum class ClockState {
    FREE_RUN,       // 自由运行状态 - 使用RTC初始时间
    DISCIPLINING,   // 驯服中状态 - 正在校准PHC
    LOCKED,         // 锁定状态 - 最佳工作状态
    HOLDOVER        // 守时状态 - 使用铷钟维持精度
};

/**
 * @brief 时间源类型枚举
 * 定义系统支持的各种时间源
 */
enum class TimeSource {
    GNSS,           // GNSS接收机（GPS/北斗/GLONASS等）
    RUBIDIUM,       // 铷原子钟
    RTC,            // 高精度实时时钟
    EXTERNAL_PPS,   // 外部1PPS信号
    EXTERNAL_10MHZ, // 外部10MHz频率基准
    PHC,            // 网卡PTP硬件时钟
    SYSTEM_CLOCK    // 系统时钟
};

/**
 * @brief 时间源状态枚举
 * 描述时间源的当前工作状态
 */
enum class TimeSourceStatus {
    ACTIVE,         // 活跃状态 - 正在使用
    STANDBY,        // 待机状态 - 可用但未使用
    FAULT,          // 故障状态 - 检测到错误
    UNAVAILABLE     // 不可用状态 - 硬件不存在或初始化失败
};

/**
 * @brief 系统健康状态枚举
 * 描述整个系统的健康状况
 */
enum class SystemHealth {
    HEALTHY,        // 健康状态 - 所有功能正常
    WARNING,        // 警告状态 - 存在非关键问题
    ERROR,          // 错误状态 - 存在影响功能的问题
    CRITICAL        // 严重状态 - 系统功能严重受损
};

/**
 * @brief 时钟事件类型枚举
 * 定义可能触发状态机转换的事件类型
 */
enum class ClockEvent {
    GNSS_SIGNAL_ACQUIRED,   // GNSS信号获取
    GNSS_SIGNAL_LOST,       // GNSS信号丢失
    CONVERGENCE_ACHIEVED,   // 驯服收敛完成
    HOLDOVER_TIMEOUT,       // 守时超时
    HARDWARE_FAULT,         // 硬件故障
    SYSTEM_RESTART,         // 系统重启
    MANUAL_RESET           // 手动重置
};

/**
 * @brief 时间质量指标结构
 * 描述时间源的精度和稳定性指标
 */
struct TimeQuality {
    double accuracy_ns;         // 时间精度（纳秒）
    double stability_ppm;       // 频率稳定度（ppm）
    uint32_t confidence;        // 置信度（0-100）
    bool is_traceable;         // 是否可追溯到UTC
    std::string reference;      // 参考源标识
};

/**
 * @brief 时间源信息结构
 * 包含时间源的完整状态信息
 */
struct TimeSourceInfo {
    TimeSource type;            // 时间源类型
    TimeSourceStatus status;    // 当前状态
    TimeQuality quality;        // 质量指标
    uint32_t priority;          // 优先级（数值越小优先级越高）
    uint64_t last_update_ns;   // 最后更新时间戳
    std::map<std::string, std::string> properties; // 扩展属性
};

/**
 * @brief 系统状态结构
 * 包含整个授时系统的完整状态信息
 */
struct SystemStatus {
    ClockState current_state;           // 当前时钟状态
    TimeSource active_source;           // 当前活跃时间源
    std::vector<TimeSourceInfo> sources; // 所有时间源状态
    SystemHealth health;                // 系统健康状况
    uint64_t uptime_seconds;           // 系统运行时间
    double cpu_usage_percent;          // CPU使用率
    uint64_t memory_usage_mb;          // 内存使用量
    std::string version;               // 软件版本
    std::string platform;              // 运行平台
};

/**
 * @brief 时间源优先级配置
 * 定义各时间源的优先级和使用策略
 */
struct TimeSourcePriority {
    std::map<TimeSource, uint32_t> priorities; // 优先级映射
    bool auto_failover;                        // 是否自动故障切换
    uint32_t failover_delay_ms;               // 故障切换延迟
};

/**
 * @brief 驯服参数配置
 * 定义时钟驯服算法的关键参数
 */
struct DiscipliningParameters {
    double convergence_threshold_ns;    // 收敛阈值（纳秒）
    uint32_t convergence_time_s;       // 收敛时间（秒）
    double phase_gain;                 // 相位增益
    double frequency_gain;             // 频率增益
    uint32_t measurement_interval_ms;  // 测量间隔（毫秒）
};

/**
 * @brief 守时参数配置
 * 定义守时模式的关键参数
 */
struct HoldoverParameters {
    uint32_t max_holdover_hours;       // 最大守时时间（小时）
    double frequency_drift_limit_ppm;  // 频率漂移限制（ppm）
    uint32_t learning_duration_hours;  // 学习时间（小时）
    bool enable_temperature_compensation; // 是否启用温度补偿
};

/**
 * @brief 告警阈值配置
 * 定义各种告警的触发阈值
 */
struct AlarmThresholds {
    // 时间精度告警
    double phase_offset_warning_ns;     // 相位偏移警告阈值
    double phase_offset_critical_ns;    // 相位偏移严重阈值
    double frequency_offset_warning_ppm; // 频率偏移警告阈值
    double frequency_offset_critical_ppm; // 频率偏移严重阈值
    
    // 信号质量告警
    uint32_t gnss_satellites_warning;   // GNSS卫星数警告阈值
    uint32_t gnss_satellites_critical;  // GNSS卫星数严重阈值
    double gnss_snr_warning_db;        // 信噪比警告阈值
    double gnss_snr_critical_db;       // 信噪比严重阈值
    
    // 系统资源告警
    double cpu_usage_warning;          // CPU使用率警告阈值
    double memory_usage_warning;       // 内存使用率警告阈值
    double temperature_warning;        // 温度警告阈值
    double temperature_critical;       // 温度严重阈值
};

/**
 * @brief 授时配置结构
 * 包含系统的完整配置参数
 */
struct TimingConfig {
    TimeSourcePriority priorities;      // 时间源优先级配置
    DiscipliningParameters discipline;  // 驯服参数配置
    HoldoverParameters holdover;        // 守时参数配置
    AlarmThresholds alarms;            // 告警阈值配置
    std::string config_version;        // 配置版本
    uint64_t last_modified;            // 最后修改时间
};

/**
 * @brief 时间数据结构
 * 包含时间戳和相关的质量指标
 */
struct TimeData {
    uint64_t timestamp_ns;              // 纳秒级时间戳（自Unix纪元）
    double frequency_offset_ppm;        // 频率偏移（ppm）
    double phase_offset_ns;             // 相位偏移（纳秒）
    TimeQuality quality;                // 时间质量指标
    TimeSource source;                  // 时间源标识
    uint64_t measurement_time_ns;       // 测量时间戳
    bool is_valid;                      // 数据有效性标志
    
    /**
     * @brief 默认构造函数
     */
    TimeData() : timestamp_ns(0), frequency_offset_ppm(0.0), phase_offset_ns(0.0),
                 source(TimeSource::SYSTEM_CLOCK), measurement_time_ns(0), is_valid(false) {}
    
    /**
     * @brief 带参数构造函数
     */
    TimeData(uint64_t ts, TimeSource src, const TimeQuality& qual) 
        : timestamp_ns(ts), frequency_offset_ppm(0.0), phase_offset_ns(0.0),
          quality(qual), source(src), measurement_time_ns(ts), is_valid(true) {}
};

/**
 * @brief 性能指标结构
 * 用于监控和分析系统性能
 */
struct PerformanceMetrics {
    // 时间精度指标
    double current_accuracy_ns;         // 当前精度
    double average_accuracy_ns;         // 平均精度
    double max_accuracy_ns;            // 最大精度偏差
    
    // 频率稳定度指标
    double allan_deviation_1s;          // 1秒Allan偏差
    double allan_deviation_10s;         // 10秒Allan偏差
    double allan_deviation_100s;        // 100秒Allan偏差
    
    // 系统性能指标
    uint64_t state_transitions;        // 状态转换次数
    uint64_t error_count;              // 错误计数
    uint64_t packets_processed;        // 处理的数据包数
    double average_response_time_ms;    // 平均响应时间
};

/**
 * @brief 将时钟状态转换为字符串
 * @param state 时钟状态
 * @return 状态字符串
 */
inline std::string ClockStateToString(ClockState state) {
    switch (state) {
        case ClockState::FREE_RUN: return "FREE_RUN";
        case ClockState::DISCIPLINING: return "DISCIPLINING";
        case ClockState::LOCKED: return "LOCKED";
        case ClockState::HOLDOVER: return "HOLDOVER";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 将时间源类型转换为字符串
 * @param source 时间源类型
 * @return 时间源字符串
 */
inline std::string TimeSourceToString(TimeSource source) {
    switch (source) {
        case TimeSource::GNSS: return "GNSS";
        case TimeSource::RUBIDIUM: return "RUBIDIUM";
        case TimeSource::RTC: return "RTC";
        case TimeSource::EXTERNAL_PPS: return "EXTERNAL_PPS";
        case TimeSource::EXTERNAL_10MHZ: return "EXTERNAL_10MHZ";
        case TimeSource::PHC: return "PHC";
        case TimeSource::SYSTEM_CLOCK: return "SYSTEM_CLOCK";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 将字符串转换为时间源类型
 * @param str 时间源字符串
 * @return 时间源类型
 */
inline TimeSource StringToTimeSource(const std::string& str) {
    if (str == "GNSS") return TimeSource::GNSS;
    if (str == "RUBIDIUM") return TimeSource::RUBIDIUM;
    if (str == "RTC") return TimeSource::RTC;
    if (str == "EXTERNAL_PPS") return TimeSource::EXTERNAL_PPS;
    if (str == "EXTERNAL_10MHZ") return TimeSource::EXTERNAL_10MHZ;
    if (str == "PHC") return TimeSource::PHC;
    if (str == "SYSTEM_CLOCK") return TimeSource::SYSTEM_CLOCK;
    return TimeSource::SYSTEM_CLOCK; // 默认值
}

/**
 * @brief 将时间源状态转换为字符串
 * @param status 时间源状态
 * @return 状态字符串
 */
inline std::string TimeSourceStatusToString(TimeSourceStatus status) {
    switch (status) {
        case TimeSourceStatus::ACTIVE: return "ACTIVE";
        case TimeSourceStatus::STANDBY: return "STANDBY";
        case TimeSourceStatus::FAULT: return "FAULT";
        case TimeSourceStatus::UNAVAILABLE: return "UNAVAILABLE";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 将字符串转换为时间源状态
 * @param str 状态字符串
 * @return 时间源状态
 */
inline TimeSourceStatus StringToTimeSourceStatus(const std::string& str) {
    if (str == "ACTIVE") return TimeSourceStatus::ACTIVE;
    if (str == "STANDBY") return TimeSourceStatus::STANDBY;
    if (str == "FAULT") return TimeSourceStatus::FAULT;
    if (str == "UNAVAILABLE") return TimeSourceStatus::UNAVAILABLE;
    return TimeSourceStatus::UNAVAILABLE; // 默认值
}

/**
 * @brief 将系统健康状态转换为字符串
 * @param health 系统健康状态
 * @return 健康状态字符串
 */
inline std::string SystemHealthToString(SystemHealth health) {
    switch (health) {
        case SystemHealth::HEALTHY: return "HEALTHY";
        case SystemHealth::WARNING: return "WARNING";
        case SystemHealth::ERROR: return "ERROR";
        case SystemHealth::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 将字符串转换为系统健康状态
 * @param str 健康状态字符串
 * @return 系统健康状态
 */
inline SystemHealth StringToSystemHealth(const std::string& str) {
    if (str == "HEALTHY") return SystemHealth::HEALTHY;
    if (str == "WARNING") return SystemHealth::WARNING;
    if (str == "ERROR") return SystemHealth::ERROR;
    if (str == "CRITICAL") return SystemHealth::CRITICAL;
    return SystemHealth::ERROR; // 默认值
}

/**
 * @brief 获取当前时间戳（纳秒）
 * @return 当前时间戳
 */
inline uint64_t GetCurrentTimestampNs() {
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();
}

/**
 * @brief 将纳秒时间戳转换为ISO 8601格式字符串
 * @param timestamp_ns 纳秒时间戳
 * @return ISO 8601格式时间字符串
 */
inline std::string TimestampToIsoString(uint64_t timestamp_ns) {
    auto seconds = timestamp_ns / 1000000000ULL;
    auto nanoseconds = timestamp_ns % 1000000000ULL;
    
    std::time_t time = static_cast<std::time_t>(seconds);
    std::tm* utc_tm = std::gmtime(&time);
    
    std::ostringstream oss;
    oss << std::put_time(utc_tm, "%Y-%m-%dT%H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(9) << nanoseconds << "Z";
    
    return oss.str();
}

} // namespace core
} // namespace timing_server