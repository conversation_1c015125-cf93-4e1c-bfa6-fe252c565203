#pragma once

#include <cstdint>
#include <memory>
#include <chrono>

namespace timing_server {
namespace core {

/**
 * @brief 系统资源监控接口
 * 抽象不同平台的系统资源获取方法
 */
class ISystemResourceMonitor {
public:
    virtual ~ISystemResourceMonitor() = default;
    
    /**
     * @brief 获取CPU使用率
     * @return CPU使用率百分比 (0.0-100.0)
     */
    virtual double GetCpuUsage() = 0;
    
    /**
     * @brief 获取内存使用量
     * @return 内存使用量（MB）
     */
    virtual uint64_t GetMemoryUsage() = 0;
    
    /**
     * @brief 获取网络延迟
     * @return 网络延迟（微秒）
     */
    virtual uint32_t GetNetworkLatency() = 0;
};

/**
 * @brief 缓存系统资源监控器
 * 提供带缓存的系统资源监控功能，避免频繁系统调用
 */
class CachedSystemResourceMonitor {
public:
    explicit CachedSystemResourceMonitor(std::unique_ptr<ISystemResourceMonitor> monitor);
    
    /**
     * @brief 获取CPU使用率（带缓存）
     * @return CPU使用率百分比
     */
    double GetCpuUsage();
    
    /**
     * @brief 获取内存使用量（带缓存）
     * @return 内存使用量（MB）
     */
    uint64_t GetMemoryUsage();
    
    /**
     * @brief 获取网络延迟（带缓存）
     * @return 网络延迟（微秒）
     */
    uint32_t GetNetworkLatency();

private:
    std::unique_ptr<ISystemResourceMonitor> monitor_;
    
    // CPU缓存
    double cached_cpu_usage_;
    std::chrono::steady_clock::time_point last_cpu_measurement_;
    static constexpr std::chrono::seconds CPU_CACHE_DURATION{5};
    
    // 内存缓存
    uint64_t cached_memory_usage_;
    std::chrono::steady_clock::time_point last_memory_measurement_;
    static constexpr std::chrono::seconds MEMORY_CACHE_DURATION{10};
    
    // 网络延迟缓存
    uint32_t cached_network_latency_;
    std::chrono::steady_clock::time_point last_network_measurement_;
    static constexpr std::chrono::seconds NETWORK_CACHE_DURATION{30};
};

/**
 * @brief 创建平台特定的系统资源监控器
 * @return 系统资源监控器实例
 */
std::unique_ptr<ISystemResourceMonitor> CreateSystemResourceMonitor();

} // namespace core
} // namespace timing_server