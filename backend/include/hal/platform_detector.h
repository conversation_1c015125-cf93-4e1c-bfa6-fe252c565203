#pragma once

#include <string>

namespace timing_server {
namespace hal {

/**
 * @brief 平台类型枚举
 * 定义系统支持的目标平台类型
 */
enum class PlatformType {
    LINUX_X86_64,      // Linux x86_64平台
    LINUX_LOONGARCH64, // Linux 龙芯LoongArch64平台
    MACOS_X86_64,      // macOS x86_64平台（开发环境）
    MACOS_ARM64,       // macOS ARM64平台（开发环境）
    UNKNOWN            // 未知平台
};

/**
 * @brief 平台信息结构
 * 包含检测到的平台详细信息
 */
struct PlatformInfo {
    PlatformType type;          // 平台类型
    std::string os_name;        // 操作系统名称
    std::string architecture;   // 处理器架构
    std::string kernel_version; // 内核版本
    bool is_development_env;    // 是否为开发环境
    std::string description;    // 平台描述
};

/**
 * @brief 平台检测器类
 * 负责自动检测当前运行平台并选择合适的HAL实现
 */
class PlatformDetector {
public:
    /**
     * @brief 检测当前平台
     * 通过系统调用和编译时宏定义检测当前运行平台
     * @return 平台信息结构
     */
    static PlatformInfo DetectPlatform();
    
    /**
     * @brief 获取平台类型字符串
     * @param type 平台类型
     * @return 平台类型的字符串表示
     */
    static std::string PlatformTypeToString(PlatformType type);
    
    /**
     * @brief 判断是否为Linux平台
     * @param type 平台类型
     * @return 是否为Linux平台
     */
    static bool IsLinuxPlatform(PlatformType type);
    
    /**
     * @brief 判断是否为macOS平台
     * @param type 平台类型
     * @return 是否为macOS平台
     */
    static bool IsMacOSPlatform(PlatformType type);
    
    /**
     * @brief 判断是否需要使用Mock HAL
     * @param type 平台类型
     * @return 是否需要使用Mock HAL实现
     */
    static bool RequiresMockHal(PlatformType type);

private:
    /**
     * @brief 检测操作系统类型
     * @return 操作系统名称
     */
    static std::string DetectOperatingSystem();
    
    /**
     * @brief 检测处理器架构
     * @return 处理器架构字符串
     */
    static std::string DetectArchitecture();
    
    /**
     * @brief 检测内核版本
     * @return 内核版本字符串
     */
    static std::string DetectKernelVersion();
    
    /**
     * @brief 判断是否为开发环境
     * 通过环境变量和文件系统特征判断
     * @return 是否为开发环境
     */
    static bool IsDevEnvironment();
};

} // namespace hal
} // namespace timing_server