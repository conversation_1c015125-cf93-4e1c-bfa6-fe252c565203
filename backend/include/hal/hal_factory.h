#pragma once

#include "hal/interfaces.h"
#include "hal/platform_detector.h"
#include <memory>
#include <string>
#include <stdexcept>

namespace timing_server {
namespace hal {

/**
 * @brief HAL错误类型枚举
 * 定义HAL层可能出现的错误类型
 */
enum class HalErrorType {
    PLATFORM_NOT_SUPPORTED,    // 平台不支持
    FACTORY_CREATION_FAILED,    // 工厂创建失败
    DEVICE_INITIALIZATION_FAILED, // 设备初始化失败
    DEVICE_NOT_FOUND,          // 设备未找到
    PERMISSION_DENIED,         // 权限不足
    RESOURCE_BUSY,             // 资源忙碌
    CONFIGURATION_ERROR,       // 配置错误
    UNKNOWN_ERROR              // 未知错误
};

/**
 * @brief HAL异常类
 * 用于HAL层的统一错误处理和中文错误描述
 */
class HalException : public std::runtime_error {
public:
    /**
     * @brief 构造HAL异常
     * @param error_type 错误类型
     * @param message 错误消息（中文）
     * @param details 详细信息（可选）
     */
    HalException(HalErrorType error_type, const std::string& message, 
                 const std::string& details = "");
    
    /**
     * @brief 获取错误类型
     * @return 错误类型枚举
     */
    HalErrorType GetErrorType() const { return error_type_; }
    
    /**
     * @brief 获取详细信息
     * @return 详细错误信息
     */
    const std::string& GetDetails() const { return details_; }
    
    /**
     * @brief 获取错误类型的中文描述
     * @param error_type 错误类型
     * @return 中文错误描述
     */
    static std::string GetErrorTypeDescription(HalErrorType error_type);

private:
    HalErrorType error_type_;
    std::string details_;
};

/**
 * @brief HAL工厂管理器类
 * 负责根据平台自动选择和创建合适的HAL工厂实例
 */
class HalFactoryManager {
public:
    /**
     * @brief 获取单例实例
     * @return HAL工厂管理器单例
     */
    static HalFactoryManager& GetInstance();
    
    /**
     * @brief 初始化HAL工厂
     * 自动检测平台并创建相应的HAL工厂实例
     * @return 初始化是否成功
     */
    bool Initialize();
    
    /**
     * @brief 获取HAL工厂实例
     * @return HAL工厂智能指针
     * @throws HalException 如果工厂未初始化或创建失败
     */
    std::shared_ptr<I_HalFactory> GetFactory();
    
    /**
     * @brief 获取平台信息
     * @return 当前平台信息
     */
    const PlatformInfo& GetPlatformInfo() const { return platform_info_; }
    
    /**
     * @brief 检查是否已初始化
     * @return 初始化状态
     */
    bool IsInitialized() const { return initialized_; }
    
    /**
     * @brief 清理资源
     * 释放HAL工厂和相关资源
     */
    void Cleanup();

private:
    HalFactoryManager() = default;
    ~HalFactoryManager() = default;
    
    // 禁用拷贝构造和赋值
    HalFactoryManager(const HalFactoryManager&) = delete;
    HalFactoryManager& operator=(const HalFactoryManager&) = delete;
    
    /**
     * @brief 创建Linux HAL工厂
     * @return Linux HAL工厂智能指针
     */
    std::shared_ptr<I_HalFactory> CreateLinuxFactory();
    
    /**
     * @brief 创建Mock HAL工厂
     * @return Mock HAL工厂智能指针
     */
    std::shared_ptr<I_HalFactory> CreateMockFactory();
    
    bool initialized_ = false;
    PlatformInfo platform_info_;
    std::shared_ptr<I_HalFactory> factory_;
};

/**
 * @brief HAL工厂创建函数
 * 便捷函数，用于快速获取HAL工厂实例
 * @return HAL工厂智能指针
 * @throws HalException 如果创建失败
 */
std::shared_ptr<I_HalFactory> CreateHalFactory();

/**
 * @brief 验证HAL工厂功能
 * 测试HAL工厂是否能正常创建各种设备实例
 * @param factory HAL工厂实例
 * @return 验证结果和详细信息
 */
struct HalValidationResult {
    bool success;                           // 验证是否成功
    std::vector<std::string> errors;        // 错误列表
    std::vector<std::string> warnings;      // 警告列表
    std::string summary;                    // 验证摘要
};

HalValidationResult ValidateHalFactory(std::shared_ptr<I_HalFactory> factory);



} // namespace hal
} // namespace timing_server