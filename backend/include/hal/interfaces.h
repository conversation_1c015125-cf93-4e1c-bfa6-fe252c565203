#pragma once

#include <memory>
#include <string>
#include <vector>
#include <cstdint>
#include <functional>

namespace timing_server {
namespace hal {

// 前向声明
struct TimeData;
struct SatelliteInfo;
struct ClockHealth;
struct PHCStatus;
struct PTPConfig;

/**
 * @brief 时间数据结构
 * 包含纳秒级时间戳和相关质量指标
 */
struct TimeData {
    uint64_t timestamp_ns;      // 纳秒级时间戳
    double frequency_offset;    // 频率偏移 (ppm)
    double phase_offset;        // 相位偏移 (ns)
    uint32_t quality;          // 时间质量指标 (0-100)
    bool is_valid;             // 有效性标志
};

/**
 * @brief 卫星信息结构
 * 包含GNSS卫星状态和信号质量信息
 */
struct SatelliteInfo {
    uint32_t satellite_count;   // 可见卫星数量
    double signal_strength_db;  // 信号强度 (dB)
    bool is_locked;            // 是否锁定
    std::string fix_type;      // 定位类型 (2D/3D)
};

/**
 * @brief 时钟健康状态
 * 描述原子钟或其他时钟源的健康状况
 */
struct ClockHealth {
    double temperature;         // 温度 (摄氏度)
    double frequency_offset;    // 频率偏移 (ppm)
    bool is_healthy;           // 健康状态
    std::string status_message; // 状态描述
};

/**
 * @brief PHC状态信息
 * 网卡PTP硬件时钟的状态信息
 */
struct PHCStatus {
    bool is_synchronized;       // 是否同步
    double offset_ns;          // 偏移量 (纳秒)
    uint32_t clock_class;      // PTP时钟等级
    std::string interface_name; // 网络接口名称
};

/**
 * @brief PTP配置参数
 * PTP协议的配置参数
 */
struct PTPConfig {
    uint32_t domain;           // PTP域
    uint32_t priority1;        // 优先级1
    uint32_t priority2;        // 优先级2
    uint32_t clock_class;      // 时钟等级
    uint32_t clock_accuracy;   // 时钟精度
    std::string interface;     // 网络接口
};

/**
 * @brief GNSS接收机抽象接口
 * 提供GNSS接收机的统一访问接口，支持NMEA数据读取和卫星信息获取
 */
class I_GnssReceiver {
public:
    virtual ~I_GnssReceiver() = default;
    
    /**
     * @brief 初始化GNSS接收机
     * @return 初始化是否成功
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 读取NMEA语句
     * @return NMEA语句字符串，如果没有数据则返回空字符串
     */
    virtual std::string ReadNmeaSentence() = 0;
    
    /**
     * @brief 检查GNSS信号是否有效
     * @return 信号有效性
     */
    virtual bool IsSignalValid() = 0;
    
    /**
     * @brief 获取卫星信息
     * @return 卫星信息结构
     */
    virtual SatelliteInfo GetSatelliteInfo() = 0;
    
    /**
     * @brief 关闭GNSS接收机
     */
    virtual void Close() = 0;
};

/**
 * @brief PPS输入抽象接口
 * 提供1PPS信号的统一访问接口，用于高精度时间同步
 */
class I_PpsInput {
public:
    virtual ~I_PpsInput() = default;
    
    /**
     * @brief 初始化PPS输入
     * @return 初始化是否成功
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 等待PPS信号边沿
     * @param timeout_ms 超时时间（毫秒）
     * @return 是否检测到PPS边沿
     */
    virtual bool WaitForPpsEdge(int timeout_ms) = 0;
    
    /**
     * @brief 获取最后一次PPS时间戳
     * @return 纳秒级时间戳
     */
    virtual uint64_t GetLastPpsTimestamp() = 0;
    
    /**
     * @brief 关闭PPS输入
     */
    virtual void Close() = 0;
};

/**
 * @brief 原子钟抽象接口
 * 提供铷原子钟等高精度振荡器的统一访问接口
 */
class I_AtomicClock {
public:
    virtual ~I_AtomicClock() = default;
    
    /**
     * @brief 初始化原子钟
     * @return 初始化是否成功
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 获取原子钟状态
     * @return 时钟健康状态
     */
    virtual ClockHealth GetStatus() = 0;
    
    /**
     * @brief 设置频率校正
     * @param ppm 频率校正值（ppm）
     * @return 设置是否成功
     */
    virtual bool SetFrequencyCorrection(double ppm) = 0;
    
    /**
     * @brief 获取频率偏移
     * @return 当前频率偏移（ppm）
     */
    virtual double GetFrequencyOffset() = 0;
    
    /**
     * @brief 获取时钟健康状况
     * @return 健康状况结构
     */
    virtual ClockHealth GetHealth() = 0;
    
    /**
     * @brief 关闭原子钟
     */
    virtual void Close() = 0;
};

/**
 * @brief 频率输入抽象接口
 * 提供外部10MHz频率基准的统一访问接口
 */
class I_FrequencyInput {
public:
    virtual ~I_FrequencyInput() = default;
    
    /**
     * @brief 初始化频率输入
     * @return 初始化是否成功
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 测量频率
     * @return 测量到的频率值（Hz）
     */
    virtual double MeasureFrequency() = 0;
    
    /**
     * @brief 检查信号是否存在
     * @return 信号存在性
     */
    virtual bool IsSignalPresent() = 0;
    
    /**
     * @brief 关闭频率输入
     */
    virtual void Close() = 0;
};

/**
 * @brief 高精度RTC抽象接口
 * 提供高精度实时时钟的统一访问接口
 */
class I_HighPrecisionRtc {
public:
    virtual ~I_HighPrecisionRtc() = default;
    
    /**
     * @brief 初始化RTC
     * @return 初始化是否成功
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 获取RTC时间
     * @return 时间结构（秒和纳秒）
     */
    virtual timespec GetTime() = 0;
    
    /**
     * @brief 设置RTC时间
     * @param ts 要设置的时间
     * @return 设置是否成功
     */
    virtual bool SetTime(const timespec& ts) = 0;
    
    /**
     * @brief 检查RTC是否有效
     * @return RTC有效性
     */
    virtual bool IsValid() = 0;
    
    /**
     * @brief 关闭RTC
     */
    virtual void Close() = 0;
};

/**
 * @brief 网络接口抽象接口
 * 提供网卡PTP硬件时钟的统一访问接口
 */
class I_NetworkInterface {
public:
    virtual ~I_NetworkInterface() = default;
    
    /**
     * @brief 初始化网络接口
     * @return 初始化是否成功
     */
    virtual bool Initialize() = 0;
    
    /**
     * @brief 获取PHC时间
     * @return PHC时间戳
     */
    virtual timespec GetPHCTime() = 0;
    
    /**
     * @brief 设置PHC时间
     * @param ts 要设置的时间
     * @return 设置是否成功
     */
    virtual bool SetPHCTime(const timespec& ts) = 0;
    
    /**
     * @brief 配置PTP参数
     * @param config PTP配置参数
     * @return 配置是否成功
     */
    virtual bool ConfigurePTP(const PTPConfig& config) = 0;
    
    /**
     * @brief 获取PHC状态
     * @return PHC状态信息
     */
    virtual PHCStatus GetPHCStatus() = 0;
    
    /**
     * @brief 关闭网络接口
     */
    virtual void Close() = 0;
};

/**
 * @brief HAL工厂抽象接口
 * 用于创建平台特定的HAL实现，支持运行时多态
 */
class I_HalFactory {
public:
    virtual ~I_HalFactory() = default;
    
    /**
     * @brief 创建GNSS接收机实例
     * @return GNSS接收机智能指针
     */
    virtual std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() = 0;
    
    /**
     * @brief 创建PPS输入实例
     * @return PPS输入智能指针
     */
    virtual std::unique_ptr<I_PpsInput> CreatePpsInput() = 0;
    
    /**
     * @brief 创建原子钟实例
     * @return 原子钟智能指针
     */
    virtual std::unique_ptr<I_AtomicClock> CreateAtomicClock() = 0;
    
    /**
     * @brief 创建频率输入实例
     * @return 频率输入智能指针
     */
    virtual std::unique_ptr<I_FrequencyInput> CreateFrequencyInput() = 0;
    
    /**
     * @brief 创建高精度RTC实例
     * @return RTC智能指针
     */
    virtual std::unique_ptr<I_HighPrecisionRtc> CreateRtc() = 0;
    
    /**
     * @brief 创建网络接口实例
     * @return 网络接口智能指针
     */
    virtual std::unique_ptr<I_NetworkInterface> CreateNetworkInterface() = 0;
};

} // namespace hal
} // namespace timing_server