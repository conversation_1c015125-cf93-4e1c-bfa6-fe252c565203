#pragma once

#include "hal/interfaces.h"
#include <memory>
#include <string>
#include <vector>
#include <atomic>
#include <mutex>
#include <thread>
#include <chrono>

namespace timing_server {
namespace hal {

// 前向声明Linux HAL实现类
class LinuxGnssReceiver;
class LinuxPpsInput;
class LinuxAtomicClock;
class LinuxFrequencyInput;
class LinuxHighPrecisionRtc;
class LinuxNetworkInterface;

/**
 * @brief Linux GNSS接收机实现
 * 通过串口设备读取NMEA数据，支持多种厂商的GNSS接收机
 */
class LinuxGnssReceiver : public I_GnssReceiver {
public:
    explicit LinuxGnssReceiver(const std::string& device_path = "/dev/ttyS0", 
                              int baudrate = 9600);
    ~LinuxGnssReceiver() override;
    
    bool Initialize() override;
    std::string ReadNmeaSentence() override;
    bool IsSignalValid() override;
    SatelliteInfo GetSatelliteInfo() override;
    void Close() override;

private:
    std::string device_path_;
    int baudrate_;
    int serial_fd_;
    std::atomic<bool> running_;
    std::unique_ptr<std::thread> receive_thread_;
    std::mutex data_mutex_;
    std::vector<std::string> nmea_buffer_;
    std::mutex status_mutex_;
    SatelliteInfo satellite_info_;
    bool signal_valid_;
    std::chrono::steady_clock::time_point last_valid_data_;
};

/**
 * @brief Linux PPS输入实现
 * 使用Linux PPS API捕获高精度1PPS时间戳
 */
class LinuxPpsInput : public I_PpsInput {
public:
    explicit LinuxPpsInput(const std::string& device_path = "/dev/pps0");
    ~LinuxPpsInput() override;
    
    bool Initialize() override;
    bool WaitForPpsEdge(int timeout_ms) override;
    uint64_t GetLastPpsTimestamp() override;
    void Close() override;

private:
    std::string device_path_;
    int pps_fd_;
    bool device_initialized_;
    std::mutex timestamp_mutex_;
    uint64_t last_timestamp_ns_;
    std::atomic<uint64_t> pps_event_count_;
};

/**
 * @brief Linux原子钟实现
 * 通过SPI/I2C接口控制铷原子钟
 */
class LinuxAtomicClock : public I_AtomicClock {
public:
    enum class InterfaceType { SPI, I2C, UART };
    
    explicit LinuxAtomicClock(InterfaceType interface_type = InterfaceType::SPI,
                             const std::string& device_path = "/dev/spidev0.0",
                             uint8_t device_address = 0x48);
    ~LinuxAtomicClock() override;
    
    bool Initialize() override;
    ClockHealth GetStatus() override;
    bool SetFrequencyCorrection(double ppm) override;
    double GetFrequencyOffset() override;
    ClockHealth GetHealth() override;
    void Close() override;

private:
    InterfaceType interface_type_;
    std::string device_path_;
    uint8_t device_address_;
    int device_fd_;
    std::atomic<bool> initialized_;
    std::mutex status_mutex_;
    ClockHealth current_health_;
    double frequency_offset_ppm_;
};

/**
 * @brief Linux频率输入实现
 * 测量外部10MHz频率基准信号
 */
class LinuxFrequencyInput : public I_FrequencyInput {
public:
    enum class MeasurementMethod { GPIO_EDGE_COUNTING, HARDWARE_COUNTER, TIMER_CAPTURE };
    
    explicit LinuxFrequencyInput(MeasurementMethod method = MeasurementMethod::GPIO_EDGE_COUNTING,
                                const std::string& device_path = "/sys/class/gpio/gpio18",
                                int gpio_pin = 18);
    ~LinuxFrequencyInput() override;
    
    bool Initialize() override;
    double MeasureFrequency() override;
    bool IsSignalPresent() override;
    void Close() override;

private:
    MeasurementMethod method_;
    std::string device_path_;
    int gpio_pin_;
    std::atomic<bool> measuring_;
    std::mutex frequency_mutex_;
    double current_frequency_;
    std::atomic<bool> signal_present_;
};

/**
 * @brief Linux高精度RTC实现
 * 访问系统高精度实时时钟
 */
class LinuxHighPrecisionRtc : public I_HighPrecisionRtc {
public:
    explicit LinuxHighPrecisionRtc(const std::string& device_path = "/dev/rtc0");
    ~LinuxHighPrecisionRtc() override;
    
    bool Initialize() override;
    timespec GetTime() override;
    bool SetTime(const timespec& ts) override;
    bool IsValid() override;
    void Close() override;

private:
    std::string device_path_;
    int rtc_fd_;
    bool device_initialized_;
    std::mutex time_mutex_;
    bool time_valid_;
    std::atomic<uint64_t> read_count_;
};

/**
 * @brief Linux网络接口实现
 * 操作网卡PTP硬件时钟(PHC)
 */
class LinuxNetworkInterface : public I_NetworkInterface {
public:
    explicit LinuxNetworkInterface(const std::string& interface_name = "eth0");
    ~LinuxNetworkInterface() override;
    
    bool Initialize() override;
    timespec GetPHCTime() override;
    bool SetPHCTime(const timespec& ts) override;
    bool ConfigurePTP(const PTPConfig& config) override;
    PHCStatus GetPHCStatus() override;
    void Close() override;

private:
    std::string interface_name_;
    int interface_index_;
    int socket_fd_;
    std::string phc_device_path_;
    int phc_fd_;
    bool device_initialized_;
    std::mutex config_mutex_;
    PTPConfig current_config_;
    std::mutex status_mutex_;
    PHCStatus current_status_;
};

} // namespace hal
} // namespace timing_server