#pragma once

#include "hal/interfaces.h"
#include <memory>

namespace timing_server {
namespace hal {

/**
 * @brief Mock HAL工厂实现类
 * 
 * 这个工厂类负责创建所有Mock硬件抽象层实现，主要用于macOS开发环境和测试场景。
 */
class MockHalFactory : public I_HalFactory {
public:
    /**
     * @brief 构造函数
     */
    MockHalFactory();
    
    /**
     * @brief 析构函数
     */
    ~MockHalFactory() override;
    
    // I_HalFactory接口实现
    std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() override;
    std::unique_ptr<I_PpsInput> CreatePpsInput() override;
    std::unique_ptr<I_AtomicClock> CreateAtomicClock() override;
    std::unique_ptr<I_FrequencyInput> CreateFrequencyInput() override;
    std::unique_ptr<I_HighPrecisionRtc> CreateRtc() override;
    std::unique_ptr<I_NetworkInterface> CreateNetworkInterface() override;
    
    /**
     * @brief 验证工厂功能
     * @return 验证是否成功
     */
    bool ValidateFactory();
    
    /**
     * @brief 获取工厂信息
     * @return 工厂描述信息
     */
    std::string GetFactoryInfo() const;
};

} // namespace hal
} // namespace timing_server