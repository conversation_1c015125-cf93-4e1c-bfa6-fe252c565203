# 安装系统改进文档

## 概述

本文档描述了高精度授时服务器系统在CMakeLists.txt中新增的安装功能，这些改进大大简化了系统的部署和管理流程。

## 新增安装功能

### 1. 配置文件安装

```cmake
# 安装配置文件
install(DIRECTORY platform/config/
    DESTINATION etc/timing-server
    FILES_MATCHING PATTERN "*.json" PATTERN "*.conf"
)
```

**功能说明**:
- 自动安装所有配置文件模板到系统配置目录
- 支持JSON和CONF格式的配置文件
- 安装路径: `${CMAKE_INSTALL_PREFIX}/etc/timing-server/`

### 2. systemd服务文件安装

```cmake
# 安装systemd服务文件
install(FILES platform/systemd/timing-server.service
    DESTINATION lib/systemd/system
    PERMISSIONS OWNER_READ OWNER_WRITE GROUP_READ WORLD_READ
)
```

**功能说明**:
- 自动安装systemd服务定义文件
- 设置正确的文件权限
- 安装路径: `${CMAKE_INSTALL_PREFIX}/lib/systemd/system/`

### 3. 管理脚本安装

```cmake
# 安装脚本文件
install(PROGRAMS 
    platform/install.sh
    platform/uninstall.sh
    platform/timing-server-wrapper.sh
    platform/system-monitor.sh
    DESTINATION share/timing-server/scripts
)
```

**功能说明**:
- 安装所有管理和监控脚本
- 自动设置可执行权限
- 安装路径: `${CMAKE_INSTALL_PREFIX}/share/timing-server/scripts/`

### 4. 符号链接创建

```cmake
# 创建符号链接到系统路径
install(CODE "
    message(STATUS \"创建脚本符号链接...\")
    execute_process(COMMAND \${CMAKE_COMMAND} -E create_symlink
        \${CMAKE_INSTALL_PREFIX}/share/timing-server/scripts/timing-server-wrapper.sh
        \${CMAKE_INSTALL_PREFIX}/bin/timing-server-wrapper
    )
    execute_process(COMMAND \${CMAKE_COMMAND} -E create_symlink
        \${CMAKE_INSTALL_PREFIX}/share/timing-server/scripts/system-monitor.sh
        \${CMAKE_INSTALL_PREFIX}/bin/timing-server-monitor
    )
")
```

**功能说明**:
- 在系统PATH中创建便捷的命令别名
- 用户可直接使用`timing-server-wrapper`和`timing-server-monitor`命令
- 提高系统管理的便利性

### 5. Web界面文件安装

```cmake
# 安装Web界面文件（如果存在）
if(EXISTS \${CMAKE_SOURCE_DIR}/frontend/dist)
    install(DIRECTORY frontend/dist/
        DESTINATION share/timing-server/web
        FILES_MATCHING 
        PATTERN "*.html"
        PATTERN "*.js"
        PATTERN "*.css"
        PATTERN "*.ico"
        PATTERN "*.png"
        PATTERN "*.svg"
    )
endif()
```

**功能说明**:
- 条件性安装前端构建产物
- 支持所有常见的Web资源文件类型
- 安装路径: `${CMAKE_INSTALL_PREFIX}/share/timing-server/web/`

### 6. 运行时目录结构创建

```cmake
# 创建运行时目录结构
install(DIRECTORY DESTINATION var/lib/timing-server)
install(DIRECTORY DESTINATION var/log/timing-server)
install(DIRECTORY DESTINATION etc/timing-server/ssl)
```

**功能说明**:
- 自动创建系统运行所需的目录结构
- 数据目录: `${CMAKE_INSTALL_PREFIX}/var/lib/timing-server/`
- 日志目录: `${CMAKE_INSTALL_PREFIX}/var/log/timing-server/`
- SSL证书目录: `${CMAKE_INSTALL_PREFIX}/etc/timing-server/ssl/`

## 安装目录结构

完整的安装目录结构如下：

```
${CMAKE_INSTALL_PREFIX}/
├── bin/
│   ├── timing-server                    # 主程序二进制文件
│   ├── timing-server-wrapper           # 包装脚本符号链接
│   └── timing-server-monitor           # 监控脚本符号链接
├── etc/timing-server/                   # 配置文件目录
│   ├── config.json                      # 主配置文件
│   ├── security.json                    # 安全配置文件
│   ├── users.json                       # 用户配置文件
│   └── ssl/                             # SSL证书目录
├── lib/systemd/system/
│   └── timing-server.service           # systemd服务文件
├── share/timing-server/
│   ├── scripts/                         # 管理脚本目录
│   │   ├── install.sh                   # 安装脚本
│   │   ├── uninstall.sh                 # 卸载脚本
│   │   ├── timing-server-wrapper.sh    # 服务包装脚本
│   │   └── system-monitor.sh           # 系统监控脚本
│   └── web/                             # Web界面文件（可选）
│       ├── index.html                   # 主页面
│       ├── assets/                      # 静态资源
│       └── ...                          # 其他Web文件
└── var/
    ├── lib/timing-server/               # 数据存储目录
    │   ├── database.db                  # SQLite数据库
    │   ├── rubidium_learning.dat        # 铷钟学习数据
    │   └── backups/                     # 配置备份
    └── log/timing-server/               # 日志存储目录
        ├── timing-server.log            # 主程序日志
        ├── api.log                      # API访问日志
        └── audit.log                    # 审计日志
```

## 使用方法

### 标准安装流程

```bash
# 1. 构建系统
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/usr/local

# 2. 编译
make -j$(nproc)

# 3. 安装
sudo make install

# 4. 创建系统用户（如果需要）
sudo groupadd --system timing-server
sudo useradd --system --gid timing-server --home-dir /usr/local/var/lib/timing-server \
    --shell /bin/false timing-server

# 5. 设置权限
sudo chown timing-server:timing-server /usr/local/var/lib/timing-server
sudo chown timing-server:timing-server /usr/local/var/log/timing-server
sudo chown root:timing-server /usr/local/etc/timing-server
sudo chmod 750 /usr/local/etc/timing-server

# 6. 启用服务
sudo systemctl daemon-reload
sudo systemctl enable timing-server
sudo systemctl start timing-server
```

### 便捷命令使用

安装完成后，可以使用以下便捷命令：

```bash
# 系统监控
sudo timing-server-monitor status
sudo timing-server-monitor monitor
sudo timing-server-monitor check

# 服务管理
sudo timing-server-wrapper --daemon --headless
sudo timing-server-wrapper --stop
sudo timing-server-wrapper --restart
```

### Web界面访问

如果安装了前端文件，可以通过以下方式访问：

1. **通过后端API服务器**（推荐）:
   - HTTP: http://localhost:8080/web/
   - HTTPS: https://localhost:8443/web/

2. **直接访问文件**:
   - 文件路径: `/usr/local/share/timing-server/web/index.html`

## 优势和改进

### 1. 简化部署流程
- 一条`make install`命令完成所有安装
- 自动创建必要的目录结构
- 自动设置正确的文件权限

### 2. 标准化安装路径
- 遵循Linux文件系统层次标准(FHS)
- 支持自定义安装前缀
- 便于系统管理和维护

### 3. 提高用户体验
- 提供便捷的命令别名
- 自动安装Web界面文件
- 完整的服务集成

### 4. 增强可维护性
- 统一的安装和卸载流程
- 清晰的目录结构
- 完整的文件权限管理

### 5. 支持多种部署场景
- 开发环境快速部署
- 生产环境标准化安装
- 容器化部署支持

## 与现有安装脚本的关系

新的CMake安装功能与现有的`platform/install.sh`脚本形成互补：

- **CMake安装**: 适用于从源码构建的标准化安装
- **install.sh脚本**: 适用于预编译包的快速部署和特殊配置

两种方式都支持，用户可根据具体需求选择合适的安装方法。

## 注意事项

### 1. 权限要求
- 安装过程需要root权限
- 运行时使用专用的timing-server用户

### 2. 依赖检查
- 确保系统已安装必要的依赖库
- 检查systemd服务支持

### 3. 配置管理
- 安装后需要根据实际硬件配置修改配置文件
- 注意SSL证书的配置和权限

### 4. 服务启动
- 首次安装后需要手动启动服务
- 检查服务状态和日志输出

## 总结

新增的CMake安装功能大大简化了高精度授时服务器系统的部署流程，提供了标准化、自动化的安装体验。这些改进不仅提高了系统的易用性，也为后续的维护和升级奠定了良好的基础。