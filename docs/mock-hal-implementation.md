# Mock HAL实现文档

## 概述

Mock HAL（硬件抽象层）实现为macOS开发环境和测试场景提供完整的硬件模拟功能。它实现了所有HAL接口，提供逼真的硬件行为模拟，支持完整的开发和测试流程。

## 实现的Mock组件

### 1. MockGnssReceiver - GNSS接收机模拟

**功能特性：**
- 支持从文件读取预录制的NMEA数据
- 动态生成标准NMEA语句（GPRMC、GPGGA、GPGSV）
- 模拟多种卫星状态和信号质量变化
- 支持信号丢失和恢复场景

**主要参数：**
- 卫星数量：4-16颗（可配置）
- 信号强度：-140dB到-160dB
- 定位类型：2D/3D
- 数据更新率：约10Hz

**使用方法：**
```cpp
auto gnss = factory->CreateGnssReceiver();
gnss->Initialize();
std::string nmea = gnss->ReadNmeaSentence();
SatelliteInfo info = gnss->GetSatelliteInfo();
```

### 2. MockPpsInput - PPS信号输入模拟

**功能特性：**
- 生成精确的1Hz脉冲信号
- 可配置的信号抖动（默认±100ns）
- 模拟信号质量变化和丢失
- 支持高精度时间戳（纳秒级）

**主要参数：**
- 信号频率：1Hz（精确）
- 抖动范围：50-250ns（可配置）
- 信号质量：0-100%
- 时间戳精度：纳秒级

**使用方法：**
```cpp
auto pps = factory->CreatePpsInput();
pps->Initialize();
bool detected = pps->WaitForPpsEdge(1000);
uint64_t timestamp = pps->GetLastPpsTimestamp();
```

### 3. MockAtomicClock - 原子钟模拟

**功能特性：**
- 模拟铷原子钟的温度特性和控制
- 支持频率校正和学习算法
- 模拟预热过程（约15分钟）
- 包含温度漂移和老化效应

**主要参数：**
- 工作温度：65-75°C
- 频率稳定度：1×10⁻¹¹ 到 1×10⁻¹²
- 温度系数：约1×10⁻¹⁰/°C
- 老化率：<5×10⁻¹¹/月

**使用方法：**
```cpp
auto clock = factory->CreateAtomicClock();
clock->Initialize();
ClockHealth health = clock->GetStatus();
clock->SetFrequencyCorrection(1e-7);  // 0.1ppm校正
```

### 4. MockFrequencyInput - 频率输入模拟

**功能特性：**
- 模拟10MHz频率基准信号
- 提供频率测量和统计分析
- 支持信号质量评估
- 模拟频率漂移和相位噪声

**主要参数：**
- 标称频率：10.000000 MHz
- 频率精度：±1×10⁻⁹ 到 ±1×10⁻¹²
- 短期稳定度：1×10⁻¹¹ (1秒)
- 测量精度：Hz级

**使用方法：**
```cpp
auto freq = factory->CreateFrequencyInput();
freq->Initialize();
bool present = freq->IsSignalPresent();
double frequency = freq->MeasureFrequency();
```

### 5. MockHighPrecisionRtc - 高精度RTC模拟

**功能特性：**
- 模拟电池供电的RTC芯片
- 支持时间设置和校准
- 模拟温度漂移和精度变化
- 包含电池状态监控

**主要参数：**
- 时间精度：±20ppm (约±1.7秒/天)
- 温度系数：±0.04ppm/°C
- 电池寿命：>10年（模拟）
- 分辨率：纳秒级

**使用方法：**
```cpp
auto rtc = factory->CreateRtc();
rtc->Initialize();
timespec time = rtc->GetTime();
rtc->SetTime(new_time);
```

### 6. MockNetworkInterface - 网络接口模拟

**功能特性：**
- 模拟网卡PTP硬件时钟(PHC)
- 支持PTP协议配置管理
- 模拟硬件时间戳和同步状态
- 包含网络质量变化模拟

**主要参数：**
- 时间戳精度：±10ns
- 同步精度：±50ns (局域网)
- PTP支持：IEEE 1588-2008
- 频率精度：±50ppb

**使用方法：**
```cpp
auto net = factory->CreateNetworkInterface();
net->Initialize();
timespec phc_time = net->GetPHCTime();
net->ConfigurePTP(ptp_config);
PHCStatus status = net->GetPHCStatus();
```

## Mock HAL工厂

### MockHalFactory类

**功能：**
- 统一创建所有Mock设备实例
- 提供工厂验证功能
- 支持参数配置和定制
- 确保Mock实现的一致性

**使用方法：**
```cpp
// 通过HAL管理器自动创建
auto factory = CreateHalFactory();  // 在macOS上自动选择Mock工厂

// 创建各种设备
auto gnss = factory->CreateGnssReceiver();
auto pps = factory->CreatePpsInput();
auto clock = factory->CreateAtomicClock();
// ... 其他设备
```

## 配置和定制

### NMEA数据文件

Mock GNSS接收机支持从文件读取NMEA数据：

**文件位置：** `backend/mock_data/nmea_sample.txt`

**文件格式：**
```
# 注释行以#开头
$GPRMC,123519.00,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A
$GPGGA,123519.00,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47
# 更多NMEA语句...
```

### 模拟参数配置

每个Mock设备都支持参数配置：

```cpp
// GNSS接收机参数
gnss->SetSimulationParameters(12, -142.0, true);  // 12颗卫星，-142dB，信号有效

// PPS输入参数
pps->SetSignalParameters(100.0, 95, true);  // 100ns抖动，95%质量，信号存在

// 原子钟参数
clock->SetSimulationParameters(70.0, 1e-10, 1e-12);  // 70°C，温度系数，老化率

// 频率输入参数
freq->SetSimulationParameters(1e-9, 1e-11, true);  // 1ppb精度，1e-11稳定度

// RTC参数
rtc->SetSimulationParameters(20.0, 0.04, true);  // 20ppm精度，温度系数，有电池

// 网络接口参数
net->SetSimulationParameters("eth0", 10.0, 50.0);  // eth0，10ns时间戳精度，50ns同步精度
```

## 故障模拟

Mock HAL支持各种故障场景模拟：

```cpp
// GNSS信号丢失
gnss->SimulateFault("signal_loss");

// PPS信号异常
pps->SimulateFault("large_jitter");

// 原子钟过热
clock->SimulateFault("overheat");

// 频率基准漂移
freq->SimulateAnomaly("frequency_drift");

// RTC电池不足
rtc->SimulateFault("battery_low");

// 网络同步丢失
net->SimulateFault("sync_loss");
```

## 测试和验证

### 单元测试

使用提供的测试程序验证Mock HAL功能：

```bash
# 编译测试程序
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Debug
make

# 运行测试
./backend/tests/test_mock_hal
```

### 集成测试

Mock HAL可以与核心授时引擎集成测试：

```cpp
// 创建HAL工厂
auto factory = CreateHalFactory();

// 创建授时引擎并使用Mock HAL
TimingEngine engine(factory);
engine.Initialize();
engine.Start();
```

## 开发指南

### 添加新的Mock设备

1. 创建新的Mock类，继承相应的HAL接口
2. 实现所有虚函数，添加详细的中文注释
3. 在MockHalFactory中添加创建方法
4. 更新测试程序和文档

### 扩展现有Mock功能

1. 在相应的Mock类中添加新方法
2. 更新模拟参数和配置选项
3. 添加相应的测试用例
4. 更新文档说明

### 调试和日志

Mock HAL提供详细的调试输出：

```cpp
// 启用详细日志
std::cout.rdbuf();  // 确保输出到控制台

// 各种状态信息会自动输出
// 可以通过重定向控制日志级别
```

## 注意事项

1. **线程安全**：Mock实现使用了多线程，注意线程安全
2. **资源管理**：确保正确调用Close()方法清理资源
3. **时间精度**：Mock实现基于系统时钟，精度受限于操作系统
4. **性能影响**：Mock实现包含随机数生成和模拟计算，可能影响性能
5. **平台兼容**：主要针对macOS开发环境，其他平台可能需要调整

## 未来改进

1. 支持更多GNSS系统（北斗、GLONASS、Galileo）
2. 增加更复杂的信号传播模型
3. 支持配置文件驱动的参数设置
4. 添加图形化的状态监控界面
5. 支持网络模拟和分布式测试