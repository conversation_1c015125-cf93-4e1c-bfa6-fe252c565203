# HAL架构设计文档

## 概述

硬件抽象层（Hardware Abstraction Layer, HAL）是授时服务器系统的核心组件之一，负责为上层应用提供统一的硬件访问接口，屏蔽不同平台和硬件的差异。HAL采用工厂模式和接口抽象设计，支持运行时多态，实现了真正的跨平台兼容性。

## 设计目标

### 1. 跨平台兼容性
- **Linux x86_64**: 生产环境部署，直接操作真实硬件
- **Linux LoongArch64**: 龙芯平台部署，支持国产化硬件
- **macOS**: 开发环境，使用Mock实现进行开发和测试

### 2. 硬件抽象统一性
- 为所有硬件设备提供统一的C++接口
- 隐藏平台特定的系统调用和驱动差异
- 支持热插拔和动态设备发现

### 3. 可测试性
- 提供完整的Mock实现用于单元测试
- 支持故障注入和边界条件测试
- 独立的接口测试框架

## 架构组件

### 1. 核心接口层

#### I_GnssReceiver - GNSS接收机接口
```cpp
class I_GnssReceiver {
public:
    virtual bool Initialize() = 0;                    // 初始化设备
    virtual std::string ReadNmeaSentence() = 0;       // 读取NMEA数据
    virtual bool IsSignalValid() = 0;                 // 检查信号有效性
    virtual SatelliteInfo GetSatelliteInfo() = 0;     // 获取卫星信息
    virtual void Close() = 0;                         // 关闭设备
};
```

**功能说明**：
- 支持多种GNSS系统（GPS、北斗、GLONASS、Galileo）
- 提供标准NMEA 0183协议数据解析
- 实时监控卫星锁定状态和信号质量

#### I_PpsInput - 1PPS信号输入接口
```cpp
class I_PpsInput {
public:
    virtual bool Initialize() = 0;                    // 初始化PPS输入
    virtual bool WaitForPpsEdge(int timeout_ms) = 0;  // 等待PPS边沿
    virtual uint64_t GetLastPpsTimestamp() = 0;       // 获取最后时间戳
    virtual void Close() = 0;                         // 关闭PPS输入
};
```

**功能说明**：
- 支持上升沿和下降沿检测
- 纳秒级时间戳精度
- 可配置的超时和中断处理

#### I_AtomicClock - 原子钟接口
```cpp
class I_AtomicClock {
public:
    virtual bool Initialize() = 0;                    // 初始化原子钟
    virtual ClockHealth GetStatus() = 0;              // 获取时钟状态
    virtual bool SetFrequencyCorrection(double ppm) = 0; // 设置频率校正
    virtual double GetFrequencyOffset() = 0;          // 获取频率偏移
    virtual ClockHealth GetHealth() = 0;              // 获取健康状况
    virtual void Close() = 0;                         // 关闭原子钟
};
```

**功能说明**：
- 支持铷原子钟和铯原子钟
- 温度补偿和老化校正
- 实时健康监控和故障检测

#### I_FrequencyInput - 频率输入接口
```cpp
class I_FrequencyInput {
public:
    virtual bool Initialize() = 0;                    // 初始化频率输入
    virtual double MeasureFrequency() = 0;            // 测量频率
    virtual bool IsSignalPresent() = 0;               // 检查信号存在
    virtual void Close() = 0;                         // 关闭频率输入
};
```

**功能说明**：
- 支持10MHz外部频率基准输入
- 高精度频率测量（ppb级别）
- 信号质量监控

#### I_HighPrecisionRtc - 高精度RTC接口
```cpp
class I_HighPrecisionRtc {
public:
    virtual bool Initialize() = 0;                    // 初始化RTC
    virtual timespec GetTime() = 0;                   // 获取RTC时间
    virtual bool SetTime(const timespec& ts) = 0;     // 设置RTC时间
    virtual bool IsValid() = 0;                       // 检查RTC有效性
    virtual void Close() = 0;                         // 关闭RTC
};
```

**功能说明**：
- 电池备份的高精度实时时钟
- 微秒级时间精度
- 掉电保持和自动校准

#### I_NetworkInterface - 网络接口
```cpp
class I_NetworkInterface {
public:
    virtual bool Initialize() = 0;                    // 初始化网络接口
    virtual timespec GetPHCTime() = 0;                // 获取PHC时间
    virtual bool SetPHCTime(const timespec& ts) = 0;  // 设置PHC时间
    virtual bool ConfigurePTP(const PTPConfig& config) = 0; // 配置PTP
    virtual PHCStatus GetPHCStatus() = 0;             // 获取PHC状态
    virtual void Close() = 0;                         // 关闭网络接口
};
```

**功能说明**：
- 支持PTP硬件时间戳
- Intel E810等高端网卡支持
- DPLL和频率合成器控制

### 2. 平台检测层

#### PlatformDetector - 平台检测器
```cpp
class PlatformDetector {
public:
    static PlatformInfo DetectPlatform();             // 检测当前平台
    static std::string PlatformTypeToString(PlatformType type); // 类型转字符串
    static bool IsLinuxPlatform(PlatformType type);   // 判断Linux平台
    static bool IsMacOSPlatform(PlatformType type);   // 判断macOS平台
    static bool RequiresMockHal(PlatformType type);   // 判断需要Mock HAL
};
```

**检测机制**：
- 使用`uname()`系统调用获取系统信息
- 通过编译时宏和运行时检测确定平台类型
- 自动识别开发环境和生产环境

**支持的平台类型**：
```cpp
enum class PlatformType {
    LINUX_X86_64,      // Linux x86_64平台
    LINUX_LOONGARCH64, // Linux 龙芯LoongArch64平台
    MACOS_X86_64,      // macOS x86_64平台（开发环境）
    MACOS_ARM64,       // macOS ARM64平台（开发环境）
    UNKNOWN            // 未知平台
};
```

### 3. 工厂管理层

#### HalFactoryManager - HAL工厂管理器
```cpp
class HalFactoryManager {
public:
    static HalFactoryManager& GetInstance();          // 获取单例实例
    bool Initialize();                                // 初始化HAL工厂
    std::shared_ptr<I_HalFactory> GetFactory();       // 获取工厂实例
    const PlatformInfo& GetPlatformInfo() const;      // 获取平台信息
    bool IsInitialized() const;                       // 检查初始化状态
    void Cleanup();                                   // 清理资源
};
```

**工厂选择逻辑**：
1. 检测当前运行平台
2. 根据平台类型选择相应的HAL实现：
   - Linux平台 → LinuxHalFactory
   - macOS平台 → MockHalFactory
   - 龙芯平台 → LinuxHalFactory（带龙芯优化）
3. 创建并验证工厂实例
4. 提供统一的设备创建接口

#### I_HalFactory - HAL工厂接口
```cpp
class I_HalFactory {
public:
    virtual std::unique_ptr<I_GnssReceiver> CreateGnssReceiver() = 0;
    virtual std::unique_ptr<I_PpsInput> CreatePpsInput() = 0;
    virtual std::unique_ptr<I_AtomicClock> CreateAtomicClock() = 0;
    virtual std::unique_ptr<I_FrequencyInput> CreateFrequencyInput() = 0;
    virtual std::unique_ptr<I_HighPrecisionRtc> CreateRtc() = 0;
    virtual std::unique_ptr<I_NetworkInterface> CreateNetworkInterface() = 0;
};
```

### 4. 错误处理层

#### HalException - HAL异常类
```cpp
class HalException : public std::runtime_error {
public:
    HalException(HalErrorType error_type, const std::string& message, 
                 const std::string& details = "");
    HalErrorType GetErrorType() const;
    const std::string& GetDetails() const;
    static std::string GetErrorTypeDescription(HalErrorType error_type);
};
```

**错误类型定义**：
```cpp
enum class HalErrorType {
    PLATFORM_NOT_SUPPORTED,    // 平台不支持
    FACTORY_CREATION_FAILED,    // 工厂创建失败
    DEVICE_INITIALIZATION_FAILED, // 设备初始化失败
    DEVICE_NOT_FOUND,          // 设备未找到
    PERMISSION_DENIED,         // 权限不足
    RESOURCE_BUSY,             // 资源忙碌
    CONFIGURATION_ERROR,       // 配置错误
    UNKNOWN_ERROR              // 未知错误
};
```

**中文错误描述**：
- 所有错误类型都提供详细的中文描述
- 支持错误上下文和详细信息
- 便于调试和问题定位

## 平台实现策略

### 1. Linux平台实现

#### 设备访问方式
- **GNSS接收机**: 通过串口设备文件（`/dev/ttyS*`, `/dev/ttyUSB*`）
- **PPS输入**: 使用Linux PPS API（`/dev/pps*`）
- **原子钟**: 通过SPI/I2C设备文件（`/dev/spidev*`, `/dev/i2c-*`）
- **频率输入**: GPIO或专用计数器设备
- **RTC**: 通过RTC设备文件（`/dev/rtc*`）
- **网络接口**: ethtool API和PHC设备（`/dev/ptp*`）

#### 权限要求
- 需要root权限或相应的设备组权限
- 支持udev规则配置自动权限分配
- 实时调度权限（SCHED_FIFO）

### 2. macOS平台实现（Mock）

#### Mock数据源
- **GNSS数据**: 从预录制的NMEA文件读取
- **PPS信号**: 使用系统时钟模拟1PPS边沿
- **原子钟**: 模拟温度和频率漂移特性
- **频率输入**: 生成带噪声的10MHz信号
- **RTC**: 使用系统时间加偏移
- **网络接口**: 模拟PHC和PTP状态

#### 开发特性
- 支持多种故障场景模拟
- 可配置的设备参数和行为
- 实时性能指标统计

### 3. 龙芯平台实现

#### 特殊考虑
- 基于Linux HAL实现，添加龙芯特定优化
- 支持龙芯处理器的特殊指令集
- 针对龙芯硬件平台的性能调优
- 国产化硬件设备的特殊支持

## 使用示例

### 1. 基本使用流程
```cpp
#include "hal/hal_factory.h"

int main() {
    try {
        // 创建HAL工厂
        auto factory = CreateHalFactory();
        
        // 创建GNSS接收机
        auto gnss = factory->CreateGnssReceiver();
        if (gnss->Initialize()) {
            // 读取NMEA数据
            auto nmea = gnss->ReadNmeaSentence();
            std::cout << "NMEA: " << nmea << std::endl;
            
            // 获取卫星信息
            auto sat_info = gnss->GetSatelliteInfo();
            std::cout << "卫星数量: " << sat_info.satellite_count << std::endl;
        }
        
        // 创建PPS输入
        auto pps = factory->CreatePpsInput();
        if (pps->Initialize()) {
            // 等待PPS信号
            if (pps->WaitForPpsEdge(1000)) {
                auto timestamp = pps->GetLastPpsTimestamp();
                std::cout << "PPS时间戳: " << timestamp << std::endl;
            }
        }
        
    } catch (const HalException& e) {
        std::cerr << "HAL错误: " << e.what() << std::endl;
        std::cerr << "错误类型: " << HalException::GetErrorTypeDescription(e.GetErrorType()) << std::endl;
    }
    
    return 0;
}
```

### 2. 工厂管理器使用
```cpp
#include "hal/hal_factory.h"

void InitializeHalSystem() {
    auto& manager = HalFactoryManager::GetInstance();
    
    if (!manager.Initialize()) {
        throw std::runtime_error("HAL系统初始化失败");
    }
    
    const auto& platform_info = manager.GetPlatformInfo();
    std::cout << "运行平台: " << platform_info.description << std::endl;
    
    auto factory = manager.GetFactory();
    // 使用工厂创建设备...
}
```

### 3. 错误处理示例
```cpp
try {
    auto gnss = factory->CreateGnssReceiver();
    gnss->Initialize();
} catch (const HalException& e) {
    switch (e.GetErrorType()) {
        case HalErrorType::DEVICE_NOT_FOUND:
            std::cerr << "GNSS设备未找到，请检查硬件连接" << std::endl;
            break;
        case HalErrorType::PERMISSION_DENIED:
            std::cerr << "权限不足，请使用sudo运行或配置设备权限" << std::endl;
            break;
        case HalErrorType::DEVICE_INITIALIZATION_FAILED:
            std::cerr << "设备初始化失败: " << e.GetDetails() << std::endl;
            break;
        default:
            std::cerr << "未知HAL错误: " << e.what() << std::endl;
    }
}
```

## 测试框架

### 1. 单元测试
- 使用Google Test框架
- Mock对象测试接口正确性
- 平台检测功能验证
- 错误处理机制测试

### 2. 集成测试
- HAL工厂创建和验证
- 设备实例化测试
- 跨平台兼容性验证

### 3. 性能测试
- 设备操作延迟测量
- 内存使用情况监控
- 并发访问性能测试

## 扩展性设计

### 1. 新硬件支持
- 实现相应的HAL接口
- 添加到工厂创建逻辑
- 更新平台检测机制

### 2. 新平台支持
- 添加平台类型枚举
- 实现平台特定的HAL工厂
- 更新构建系统配置

### 3. 功能扩展
- 接口可以添加新的虚函数
- 保持向后兼容性
- 版本化的接口设计

## 最佳实践

### 1. 资源管理
- 使用RAII模式管理设备资源
- 智能指针自动内存管理
- 异常安全的资源清理

### 2. 错误处理
- 使用异常而非错误码
- 提供详细的错误上下文
- 中文错误消息便于调试

### 3. 性能优化
- 避免不必要的内存分配
- 使用移动语义减少拷贝
- 合理的缓存和批处理

### 4. 线程安全
- HAL接口不保证线程安全
- 上层应用负责同步控制
- 工厂管理器使用单例模式

## 总结

HAL架构为授时服务器系统提供了强大的跨平台硬件抽象能力，通过工厂模式和接口抽象实现了真正的平台无关性。完善的错误处理机制和测试框架确保了系统的可靠性和可维护性。这个设计为后续的Mock HAL和Linux HAL实现奠定了坚实的基础。