# 高精度授时服务器系统部署指南

## 概述

本文档详细介绍了高精度授时服务器系统的部署过程，包括系统要求、安装步骤、配置说明和故障排除指南。系统支持Linux x86_64和龙芯LoongArch64平台的生产部署，以及macOS平台的开发环境。

## 系统要求

### 硬件要求

#### 最低配置
- **CPU**: 双核 2.0GHz 或更高
- **内存**: 2GB RAM
- **存储**: 10GB 可用磁盘空间
- **网络**: 千兆以太网接口（支持硬件时间戳）

#### 推荐配置
- **CPU**: 四核 3.0GHz 或更高
- **内存**: 4GB RAM 或更高
- **存储**: 50GB SSD存储
- **网络**: Intel E810系列网卡（支持PTP硬件时间戳和DPLL）

#### 专用硬件
- **GNSS接收机**: 支持NMEA输出和1PPS信号
- **铷原子钟**: 高稳定度频率基准
- **高精度RTC**: 电池备份实时时钟
- **外部参考**: 1PPS和10MHz信号输入

### 软件要求

#### Linux x86_64平台
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **内核版本**: 5.4+ （支持PTP硬件时间戳）
- **编译器**: GCC 9+ 或 Clang 10+
- **CMake**: 3.16+
- **依赖库**:
  - libsqlite3-dev
  - libssl-dev
  - libnlohmann-json3-dev
  - chrony
  - linuxptp
  - gpsd

#### 龙芯LoongArch64平台
- **操作系统**: 龙芯Linux发行版
- **内核版本**: 支持LoongArch64的内核
- **交叉编译工具链**: LoongArch64 GCC工具链
- **其他要求**: 与Linux x86_64相同

#### macOS开发环境
- **操作系统**: macOS 11.0+
- **开发工具**: Xcode Command Line Tools
- **包管理器**: Homebrew
- **依赖库**: 通过Homebrew安装

## 快速安装

### 使用预构建包安装

#### 1. 下载部署包
```bash
# 从GitHub Releases下载最新版本
wget https://github.com/timing-server/timing-server-system/releases/latest/download/timing-server-system-1.0.0-linux-x86_64.tar.gz

# 或下载龙芯版本
wget https://github.com/timing-server/timing-server-system/releases/latest/download/timing-server-system-1.0.0-loongarch64.tar.gz
```

#### 2. 解压并安装
```bash
# 解压部署包
tar -xzf timing-server-system-1.0.0-linux-x86_64.tar.gz
cd timing-server-system-1.0.0-linux-x86_64

# 运行安装脚本
sudo ./install.sh
```

#### 3. 配置系统
```bash
# 编辑配置文件
sudo nano /etc/timing-server/config.json

# 启动服务
sudo systemctl start timing-server
sudo systemctl enable timing-server
```

### 使用包管理器安装

#### Ubuntu/Debian系统
```bash
# 安装deb包
sudo dpkg -i timing-server-system-1.0.0-linux-x86_64.deb

# 安装依赖（如果有缺失）
sudo apt-get install -f
```

#### CentOS/RHEL系统
```bash
# 安装rpm包
sudo rpm -ivh timing-server-system-1.0.0-linux-x86_64.rpm

# 或使用yum/dnf
sudo yum install timing-server-system-1.0.0-linux-x86_64.rpm
```

### 使用自解压安装脚本
```bash
# 下载并运行自解压安装脚本
wget https://github.com/timing-server/timing-server-system/releases/latest/download/timing-server-system-1.0.0-linux-x86_64-installer.sh
sudo bash timing-server-system-1.0.0-linux-x86_64-installer.sh
```

## 从源码构建安装

### 1. 准备构建环境

#### Linux x86_64
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    cmake \
    git \
    libsqlite3-dev \
    libssl-dev \
    libnlohmann-json3-dev \
    pkg-config

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y \
    cmake \
    git \
    sqlite-devel \
    openssl-devel \
    nlohmann-json-devel \
    pkgconfig
```

#### 龙芯LoongArch64交叉编译
```bash
# 安装交叉编译工具链
# 请从龙芯官方获取工具链并安装到 /opt/loongarch64-linux-gnu

# 验证工具链安装
/opt/loongarch64-linux-gnu/bin/loongarch64-linux-gnu-gcc --version
```

#### macOS开发环境
```bash
# 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装依赖
brew install cmake sqlite3 openssl nlohmann-json pkg-config
```

### 2. 获取源码
```bash
git clone https://github.com/timing-server/timing-server-system.git
cd timing-server-system
```

### 3. 构建项目

#### 本地构建
```bash
# Linux/macOS本地构建
./build_scripts/build.sh native Release

# 或手动构建
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

#### 龙芯交叉编译
```bash
# 龙芯LoongArch64交叉编译
./build_scripts/build.sh loongarch64 Release

# 或手动交叉编译
mkdir build-loongarch64 && cd build-loongarch64
cmake .. -DCMAKE_TOOLCHAIN_FILE=../build_scripts/toolchains/loongarch64-linux-gnu.cmake -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

### 4. 创建部署包
```bash
# 创建所有类型的部署包
./build_scripts/create_deployment_package.sh linux Release all ./packages

# 仅创建tar.gz包
./build_scripts/create_deployment_package.sh linux Release tar ./packages
```

### 5. 安装构建结果
```bash
cd build-linux-Release
sudo make install

# 或使用生成的部署包
cd packages
tar -xzf timing-server-system-*.tar.gz
cd timing-server-system-*
sudo ./install.sh
```

## 配置说明

### 主配置文件

配置文件位置：`/etc/timing-server/config.json`

```json
{
  "system": {
    "log_level": "INFO",
    "data_dir": "/var/lib/timing-server",
    "pid_file": "/var/run/timing-server.pid",
    "user": "timing-server",
    "group": "timing-server"
  },
  "timing": {
    "sources": {
      "gnss": {
        "enabled": true,
        "device": "/dev/ttyS0",
        "baudrate": 9600,
        "priority": 1,
        "timeout_ms": 5000
      },
      "rubidium": {
        "enabled": true,
        "device": "/dev/spidev0.0",
        "priority": 2,
        "learning_duration_hours": 72
      },
      "external_pps": {
        "enabled": false,
        "device": "/dev/pps0",
        "priority": 3
      },
      "external_10mhz": {
        "enabled": false,
        "device": "/dev/freq0",
        "priority": 4
      },
      "rtc": {
        "enabled": true,
        "device": "/dev/rtc0",
        "priority": 5
      }
    },
    "disciplining": {
      "convergence_threshold_ns": 50.0,
      "holdover_timeout_hours": 24,
      "phase_lock_threshold_ns": 100.0,
      "frequency_lock_threshold_ppm": 0.01
    },
    "state_machine": {
      "startup_delay_seconds": 30,
      "state_transition_timeout_seconds": 300,
      "health_check_interval_seconds": 60
    }
  },
  "network": {
    "ptp": {
      "enabled": true,
      "interface": "eth0",
      "domain": 0,
      "priority1": 1,
      "priority2": 1,
      "announce_interval": 1,
      "sync_interval": 0,
      "delay_req_interval": 0
    },
    "ntp": {
      "enabled": true,
      "port": 123,
      "bind_address": "0.0.0.0",
      "allowed_networks": [
        "***********/24",
        "10.0.0.0/8"
      ],
      "max_clients": 1000,
      "stratum": 1
    }
  },
  "api": {
    "http": {
      "enabled": true,
      "port": 8080,
      "bind_address": "0.0.0.0"
    },
    "https": {
      "enabled": false,
      "port": 8443,
      "cert_file": "/etc/timing-server/ssl/server.crt",
      "key_file": "/etc/timing-server/ssl/server.key"
    },
    "websocket": {
      "enabled": true,
      "port": 8081
    },
    "cors": {
      "enabled": true,
      "allowed_origins": ["*"],
      "allowed_methods": ["GET", "POST", "PUT", "DELETE"],
      "allowed_headers": ["Content-Type", "Authorization"]
    }
  },
  "security": {
    "authentication": {
      "enabled": true,
      "jwt_secret": "your-secret-key-change-this",
      "token_expiry_hours": 24,
      "refresh_token_expiry_days": 30
    },
    "authorization": {
      "default_role": "viewer",
      "roles": {
        "viewer": ["read_status", "read_logs"],
        "operator": ["read_status", "read_logs", "write_config"],
        "administrator": ["read_status", "read_logs", "write_config", "control_system", "admin_users"]
      }
    },
    "access_control": {
      "allowed_ips": [],
      "blocked_ips": [],
      "rate_limiting": {
        "enabled": true,
        "requests_per_minute": 60,
        "burst_size": 10
      }
    }
  },
  "logging": {
    "level": "INFO",
    "file": {
      "enabled": true,
      "path": "/var/log/timing-server/timing-server.log",
      "max_size_mb": 100,
      "max_files": 10,
      "compress": true
    },
    "console": {
      "enabled": false,
      "colored": true
    },
    "syslog": {
      "enabled": true,
      "facility": "daemon",
      "ident": "timing-server"
    }
  },
  "database": {
    "path": "/var/lib/timing-server/timing-server.db",
    "backup": {
      "enabled": true,
      "interval_hours": 24,
      "keep_backups": 7
    },
    "maintenance": {
      "vacuum_interval_days": 7,
      "analyze_interval_days": 1
    }
  },
  "monitoring": {
    "metrics": {
      "enabled": true,
      "collection_interval_seconds": 1,
      "retention_days": 30
    },
    "alarms": {
      "enabled": true,
      "thresholds": {
        "phase_offset_warning_ns": 100.0,
        "phase_offset_critical_ns": 500.0,
        "frequency_offset_warning_ppm": 0.01,
        "frequency_offset_critical_ppm": 0.1,
        "gnss_satellites_warning": 6,
        "gnss_satellites_critical": 4,
        "cpu_usage_warning": 80.0,
        "memory_usage_warning": 80.0,
        "disk_usage_warning": 85.0
      }
    }
  }
}
```

### 硬件检测配置

系统会自动生成硬件检测配置文件：`/etc/timing-server/hardware-detection.json`

```json
{
  "platform": "linux",
  "build_type": "Release",
  "version": "1.0.0",
  "auto_detection": {
    "gnss_devices": ["/dev/ttyS0", "/dev/ttyUSB0"],
    "pps_devices": ["/dev/pps0"],
    "rtc_devices": ["/dev/rtc0"],
    "network_interfaces": ["eth0", "eth1"]
  },
  "detected_hardware": {
    "gnss": {
      "device": "/dev/ttyS0",
      "manufacturer": "u-blox",
      "model": "NEO-8M",
      "firmware": "3.01"
    },
    "network": {
      "interface": "eth0",
      "driver": "e1000e",
      "ptp_support": true,
      "hardware_timestamping": true
    }
  }
}
```

### systemd服务配置

服务文件位置：`/lib/systemd/system/timing-server.service`

```ini
[Unit]
Description=高精度授时服务器系统
Documentation=https://github.com/timing-server/timing-server-system
After=network.target time-sync.target
Wants=network.target

[Service]
Type=forking
User=timing-server
Group=timing-server
ExecStart=/usr/local/bin/timing-server --daemon --config /etc/timing-server/config.json
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/var/run/timing-server.pid
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/timing-server /var/log/timing-server /var/run
PrivateTmp=true
PrivateDevices=false
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=512M
CPUQuota=200%

[Install]
WantedBy=multi-user.target
```

## 服务管理

### 基本服务操作
```bash
# 启动服务
sudo systemctl start timing-server

# 停止服务
sudo systemctl stop timing-server

# 重启服务
sudo systemctl restart timing-server

# 重新加载配置
sudo systemctl reload timing-server

# 查看服务状态
sudo systemctl status timing-server

# 启用开机自启动
sudo systemctl enable timing-server

# 禁用开机自启动
sudo systemctl disable timing-server
```

### 日志查看
```bash
# 查看实时日志
sudo journalctl -u timing-server -f

# 查看最近的日志
sudo journalctl -u timing-server -n 100

# 查看特定时间段的日志
sudo journalctl -u timing-server --since "2024-01-01 00:00:00" --until "2024-01-01 23:59:59"

# 查看应用程序日志文件
sudo tail -f /var/log/timing-server/timing-server.log
```

### 配置验证
```bash
# 验证配置文件语法
timing-server --config /etc/timing-server/config.json --validate

# 测试配置并显示详细信息
timing-server --config /etc/timing-server/config.json --test --verbose
```

## 性能调优

### 系统级优化

#### 1. 内核参数调优
```bash
# 编辑 /etc/sysctl.conf
sudo nano /etc/sysctl.conf

# 添加以下参数
# 网络性能优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.core.netdev_max_backlog = 5000

# 时间同步优化
kernel.sched_rt_runtime_us = 950000
kernel.sched_rt_period_us = 1000000

# 应用设置
sudo sysctl -p
```

#### 2. CPU调度优化
```bash
# 设置CPU调度策略
echo 'GRUB_CMDLINE_LINUX="isolcpus=2,3 nohz_full=2,3 rcu_nocbs=2,3"' | sudo tee -a /etc/default/grub
sudo update-grub
sudo reboot
```

#### 3. 中断亲和性设置
```bash
# 将网络中断绑定到特定CPU
echo 2 | sudo tee /proc/irq/24/smp_affinity

# 或使用irqbalance服务
sudo systemctl enable irqbalance
sudo systemctl start irqbalance
```

### 应用级优化

#### 1. 内存锁定
```bash
# 在配置文件中启用内存锁定
{
  "system": {
    "memory_lock": true,
    "mlockall": true
  }
}
```

#### 2. 实时调度
```bash
# 设置实时调度优先级
{
  "system": {
    "scheduler": {
      "policy": "SCHED_FIFO",
      "priority": 50
    }
  }
}
```

#### 3. 数据库优化
```bash
# SQLite优化设置
{
  "database": {
    "pragma": {
      "journal_mode": "WAL",
      "synchronous": "NORMAL",
      "cache_size": 10000,
      "temp_store": "MEMORY"
    }
  }
}
```

## 监控和维护

### 系统监控

#### 1. 性能指标监控
```bash
# 查看系统状态
curl http://localhost:8080/api/v1/status

# 查看性能指标
curl http://localhost:8080/api/v1/metrics

# 查看健康状态
curl http://localhost:8080/api/v1/health
```

#### 2. 日志监控
```bash
# 监控错误日志
sudo tail -f /var/log/timing-server/timing-server.log | grep ERROR

# 监控系统日志
sudo journalctl -u timing-server -f | grep -E "(ERROR|CRITICAL)"
```

#### 3. 资源使用监控
```bash
# CPU和内存使用
top -p $(pgrep timing-server)

# 网络连接
netstat -tulpn | grep timing-server

# 磁盘使用
du -sh /var/lib/timing-server/
df -h /var/log/timing-server/
```

### 定期维护

#### 1. 数据库维护
```bash
# 数据库清理脚本
#!/bin/bash
sqlite3 /var/lib/timing-server/timing-server.db << EOF
VACUUM;
ANALYZE;
DELETE FROM metrics WHERE timestamp < datetime('now', '-30 days');
DELETE FROM events WHERE timestamp < datetime('now', '-90 days');
EOF
```

#### 2. 日志轮转
```bash
# 配置logrotate
sudo nano /etc/logrotate.d/timing-server

/var/log/timing-server/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 timing-server timing-server
    postrotate
        systemctl reload timing-server
    endscript
}
```

#### 3. 备份脚本
```bash
#!/bin/bash
# 备份脚本 /usr/local/bin/timing-server-backup.sh

BACKUP_DIR="/var/backups/timing-server"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

# 备份配置文件
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" /etc/timing-server/

# 备份数据库
sqlite3 /var/lib/timing-server/timing-server.db ".backup $BACKUP_DIR/database_$DATE.db"

# 备份学习数据
cp -r /var/lib/timing-server/learning/ "$BACKUP_DIR/learning_$DATE/" 2>/dev/null || true

# 清理旧备份（保留7天）
find "$BACKUP_DIR" -type f -mtime +7 -delete

echo "备份完成: $BACKUP_DIR"
```

## 故障排除

### 常见问题

#### 1. 服务启动失败

**症状**: systemctl start timing-server 失败

**排查步骤**:
```bash
# 查看详细错误信息
sudo systemctl status timing-server -l

# 查看启动日志
sudo journalctl -u timing-server --since "5 minutes ago"

# 检查配置文件语法
timing-server --config /etc/timing-server/config.json --validate

# 检查权限
ls -la /etc/timing-server/
ls -la /var/lib/timing-server/
ls -la /var/log/timing-server/
```

**常见解决方案**:
- 修复配置文件语法错误
- 检查文件权限和所有权
- 确保依赖服务已启动
- 检查端口是否被占用

#### 2. GNSS信号无法获取

**症状**: 系统显示GNSS状态为"UNAVAILABLE"

**排查步骤**:
```bash
# 检查设备文件
ls -la /dev/ttyS* /dev/ttyUSB*

# 测试串口通信
sudo minicom -D /dev/ttyS0 -b 9600

# 检查GNSS数据
sudo cat /dev/ttyS0

# 查看gpsd状态
sudo systemctl status gpsd
sudo gpsmon
```

**常见解决方案**:
- 检查GNSS天线连接
- 确认串口设备路径正确
- 检查波特率设置
- 验证GNSS接收机工作状态

#### 3. PTP同步异常

**症状**: PTP客户端无法同步或精度差

**排查步骤**:
```bash
# 检查PTP4L状态
sudo systemctl status ptp4l

# 查看PTP4L日志
sudo journalctl -u ptp4l -f

# 检查网络接口PTP支持
ethtool -T eth0

# 测试PTP通信
sudo ptp4l -i eth0 -m -s

# 检查PHC状态
sudo phc_ctl /dev/ptp0 get
```

**常见解决方案**:
- 确认网卡支持硬件时间戳
- 检查网络配置和连接
- 验证PTP配置参数
- 检查防火墙设置

#### 4. 内存泄漏问题

**症状**: 系统内存使用持续增长

**排查步骤**:
```bash
# 监控内存使用
watch -n 1 'ps aux | grep timing-server'

# 使用valgrind检测
sudo valgrind --tool=memcheck --leak-check=full timing-server --config /etc/timing-server/config.json

# 检查系统内存
free -h
cat /proc/meminfo
```

**常见解决方案**:
- 重启服务释放内存
- 检查配置中的缓存设置
- 升级到最新版本
- 调整内存限制参数

#### 5. 数据库锁定问题

**症状**: 数据库操作超时或失败

**排查步骤**:
```bash
# 检查数据库文件
ls -la /var/lib/timing-server/timing-server.db*

# 检查数据库完整性
sqlite3 /var/lib/timing-server/timing-server.db "PRAGMA integrity_check;"

# 查看数据库锁定状态
sudo lsof /var/lib/timing-server/timing-server.db
```

**常见解决方案**:
- 停止服务并检查数据库
- 恢复数据库备份
- 清理WAL文件
- 调整数据库配置

### 诊断工具

#### 1. 系统诊断脚本
```bash
#!/bin/bash
# 系统诊断脚本 /usr/local/bin/timing-server-diag.sh

echo "高精度授时服务器系统诊断报告"
echo "================================="
echo "生成时间: $(date)"
echo ""

# 系统信息
echo "系统信息:"
echo "--------"
uname -a
cat /etc/os-release | head -5
echo ""

# 服务状态
echo "服务状态:"
echo "--------"
systemctl status timing-server --no-pager
echo ""

# 硬件检测
echo "硬件检测:"
echo "--------"
echo "GNSS设备:"
ls -la /dev/ttyS* /dev/ttyUSB* 2>/dev/null || echo "未找到GNSS设备"
echo ""
echo "PPS设备:"
ls -la /dev/pps* 2>/dev/null || echo "未找到PPS设备"
echo ""
echo "RTC设备:"
ls -la /dev/rtc* 2>/dev/null || echo "未找到RTC设备"
echo ""
echo "网络接口:"
ip link show | grep -E "^[0-9]+:"
echo ""

# 网络时间同步
echo "网络时间同步:"
echo "------------"
echo "PTP4L状态:"
systemctl status ptp4l --no-pager -l | head -10
echo ""
echo "Chrony状态:"
systemctl status chrony --no-pager -l | head -10
echo ""

# 资源使用
echo "资源使用:"
echo "--------"
echo "CPU和内存:"
ps aux | grep timing-server | grep -v grep
echo ""
echo "磁盘使用:"
df -h /var/lib/timing-server /var/log/timing-server
echo ""

# 网络连接
echo "网络连接:"
echo "--------"
netstat -tulpn | grep -E "(8080|8081|8443|123|319|320)"
echo ""

# 最近错误
echo "最近错误:"
echo "--------"
journalctl -u timing-server --since "1 hour ago" | grep -E "(ERROR|CRITICAL)" | tail -10
echo ""

echo "诊断完成"
```

#### 2. 性能测试脚本
```bash
#!/bin/bash
# 性能测试脚本 /usr/local/bin/timing-server-perftest.sh

echo "高精度授时服务器性能测试"
echo "========================"

# API响应时间测试
echo "API响应时间测试:"
for i in {1..10}; do
    time curl -s http://localhost:8080/api/v1/status > /dev/null
done

# 并发连接测试
echo ""
echo "并发连接测试:"
ab -n 1000 -c 10 http://localhost:8080/api/v1/status

# WebSocket连接测试
echo ""
echo "WebSocket连接测试:"
# 这里可以添加WebSocket测试代码

echo "性能测试完成"
```

## 安全配置

### SSL/TLS配置

#### 1. 生成SSL证书
```bash
# 生成自签名证书（测试用）
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/timing-server/ssl/server.key \
    -out /etc/timing-server/ssl/server.crt \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=TimingServer/CN=localhost"

# 设置权限
sudo chown timing-server:timing-server /etc/timing-server/ssl/*
sudo chmod 600 /etc/timing-server/ssl/server.key
sudo chmod 644 /etc/timing-server/ssl/server.crt
```

#### 2. 配置HTTPS
```json
{
  "api": {
    "https": {
      "enabled": true,
      "port": 8443,
      "cert_file": "/etc/timing-server/ssl/server.crt",
      "key_file": "/etc/timing-server/ssl/server.key",
      "protocols": ["TLSv1.2", "TLSv1.3"],
      "ciphers": [
        "ECDHE-RSA-AES256-GCM-SHA384",
        "ECDHE-RSA-AES128-GCM-SHA256"
      ]
    }
  }
}
```

### 防火墙配置

#### 1. UFW配置（Ubuntu）
```bash
# 启用防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许授时服务端口
sudo ufw allow 123/udp comment "NTP"
sudo ufw allow 319/udp comment "PTP Event"
sudo ufw allow 320/udp comment "PTP General"

# 允许管理接口
sudo ufw allow 8080/tcp comment "HTTP API"
sudo ufw allow 8443/tcp comment "HTTPS API"
sudo ufw allow 8081/tcp comment "WebSocket"

# 查看规则
sudo ufw status numbered
```

#### 2. iptables配置
```bash
# 允许NTP
sudo iptables -A INPUT -p udp --dport 123 -j ACCEPT

# 允许PTP
sudo iptables -A INPUT -p udp --dport 319 -j ACCEPT
sudo iptables -A INPUT -p udp --dport 320 -j ACCEPT

# 允许管理接口
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8443 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8081 -j ACCEPT

# 保存规则
sudo iptables-save > /etc/iptables/rules.v4
```

### 用户权限管理

#### 1. 创建管理用户
```bash
# 通过API创建用户
curl -X POST http://localhost:8080/api/v1/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "username": "operator",
    "password": "secure_password",
    "role": "operator",
    "email": "<EMAIL>"
  }'
```

#### 2. 配置角色权限
```json
{
  "security": {
    "authorization": {
      "roles": {
        "viewer": {
          "permissions": ["read_status", "read_logs"],
          "description": "只读权限"
        },
        "operator": {
          "permissions": ["read_status", "read_logs", "write_config"],
          "description": "操作员权限"
        },
        "administrator": {
          "permissions": ["*"],
          "description": "管理员权限"
        }
      }
    }
  }
}
```

## 升级和迁移

### 版本升级

#### 1. 备份当前系统
```bash
# 停止服务
sudo systemctl stop timing-server

# 备份配置和数据
sudo tar -czf /tmp/timing-server-backup-$(date +%Y%m%d).tar.gz \
    /etc/timing-server \
    /var/lib/timing-server \
    /var/log/timing-server
```

#### 2. 安装新版本
```bash
# 下载新版本
wget https://github.com/timing-server/timing-server-system/releases/latest/download/timing-server-system-1.1.0-linux-x86_64.tar.gz

# 解压并安装
tar -xzf timing-server-system-1.1.0-linux-x86_64.tar.gz
cd timing-server-system-1.1.0-linux-x86_64
sudo ./install.sh
```

#### 3. 配置迁移
```bash
# 检查配置兼容性
timing-server --config /etc/timing-server/config.json --validate --version-check

# 迁移配置（如果需要）
timing-server --migrate-config /etc/timing-server/config.json
```

#### 4. 验证升级
```bash
# 启动服务
sudo systemctl start timing-server

# 检查版本
curl http://localhost:8080/api/v1/version

# 验证功能
curl http://localhost:8080/api/v1/status
```

### 平台迁移

#### 从x86_64迁移到LoongArch64

1. **准备目标系统**
```bash
# 在LoongArch64系统上安装依赖
sudo apt-get install -y sqlite3 libsqlite3-0 openssl chrony linuxptp gpsd
```

2. **迁移配置和数据**
```bash
# 在源系统上导出配置
sudo tar -czf timing-server-migration.tar.gz \
    /etc/timing-server \
    /var/lib/timing-server

# 传输到目标系统
scp timing-server-migration.tar.gz user@loongarch-server:/tmp/
```

3. **在目标系统上安装**
```bash
# 安装LoongArch64版本
sudo dpkg -i timing-server-system-1.0.0-loongarch64.deb

# 恢复配置和数据
sudo tar -xzf /tmp/timing-server-migration.tar.gz -C /

# 调整配置（如果需要）
sudo nano /etc/timing-server/config.json

# 启动服务
sudo systemctl start timing-server
```

## 附录

### A. 端口列表

| 端口 | 协议 | 服务 | 说明 |
|------|------|------|------|
| 123 | UDP | NTP | 网络时间协议 |
| 319 | UDP | PTP Event | PTP事件消息 |
| 320 | UDP | PTP General | PTP通用消息 |
| 8080 | TCP | HTTP API | REST API接口 |
| 8081 | TCP | WebSocket | 实时数据推送 |
| 8443 | TCP | HTTPS API | 加密REST API接口 |

### B. 文件路径

| 路径 | 用途 |
|------|------|
| `/usr/local/bin/timing-server` | 主程序 |
| `/etc/timing-server/config.json` | 主配置文件 |
| `/etc/timing-server/ssl/` | SSL证书目录 |
| `/var/lib/timing-server/` | 数据目录 |
| `/var/log/timing-server/` | 日志目录 |
| `/lib/systemd/system/timing-server.service` | systemd服务文件 |

### C. 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `TIMING_SERVER_CONFIG` | 配置文件路径 | `/etc/timing-server/config.json` |
| `TIMING_SERVER_DATA_DIR` | 数据目录 | `/var/lib/timing-server` |
| `TIMING_SERVER_LOG_LEVEL` | 日志级别 | `INFO` |
| `TIMING_SERVER_PID_FILE` | PID文件路径 | `/var/run/timing-server.pid` |

### D. 信号处理

| 信号 | 行为 |
|------|------|
| `SIGTERM` | 优雅关闭 |
| `SIGINT` | 立即关闭 |
| `SIGHUP` | 重新加载配置 |
| `SIGUSR1` | 切换日志级别 |
| `SIGUSR2` | 生成状态报告 |

### E. 性能基准

| 指标 | 目标值 | 测量方法 |
|------|--------|----------|
| 绝对时间精度 | ±50ns | GNSS锁定状态下的UTC偏差 |
| 守时精度 | ±1μs/24h | 无GNSS信号时的时间漂移 |
| API响应时间 | <10ms | HTTP GET /api/v1/status |
| WebSocket延迟 | <50ms | 状态更新推送延迟 |
| 内存使用 | <100MB | 稳定运行时的RSS |
| CPU使用率 | <5% | 正常负载下的平均值 |

---

**文档版本**: 1.0.0  
**最后更新**: 2024年1月  
**维护者**: Timing Server Team  
**联系方式**: <EMAIL>