# CI/CD 和自动化部署指南

## 概述

本文档详细介绍了高精度授时服务器系统的持续集成/持续部署(CI/CD)流程和自动化部署机制。系统支持多平台自动构建、测试和部署，确保代码质量和发布可靠性。

## CI/CD 架构

### 流水线概览

```mermaid
graph TB
    subgraph "代码提交"
        A[开发者提交代码] --> B[GitHub Repository]
    end
    
    subgraph "CI/CD Pipeline"
        B --> C[代码质量检查]
        C --> D[多平台构建]
        D --> E[自动化测试]
        E --> F[安全扫描]
        F --> G[部署包创建]
        G --> H[发布部署]
    end
    
    subgraph "构建平台"
        D --> D1[Linux x86_64]
        D --> D2[LoongArch64]
        D --> D3[macOS Dev]
    end
    
    subgraph "测试类型"
        E --> E1[单元测试]
        E --> E2[集成测试]
        E --> E3[性能测试]
    end
    
    subgraph "部署环境"
        H --> H1[测试环境]
        H --> H2[预生产环境]
        H --> H3[生产环境]
    end
```

### 触发条件

- **推送到主分支**: 触发完整的CI/CD流程
- **推送到开发分支**: 触发构建和测试
- **创建Pull Request**: 触发代码检查和测试
- **创建版本标签**: 触发发布流程
- **手动触发**: 支持手动启动特定流程

## GitHub Actions 工作流

### 工作流文件结构

```
.github/workflows/
├── ci-cd.yml              # 主CI/CD流水线
├── security-scan.yml      # 安全扫描
├── performance-test.yml   # 性能测试
└── release.yml           # 发布流程
```

### 主要作业(Jobs)

#### 1. 代码质量检查 (code-quality)

**功能**: 检查代码格式、静态分析、项目结构验证

**步骤**:
- 代码格式检查 (clang-format)
- 静态代码分析 (cppcheck)
- 项目结构验证
- 依赖安全检查

**运行条件**: 所有推送和PR

#### 2. 多平台构建 (build-*)

**支持平台**:
- Linux x86_64 (Ubuntu Latest)
- LoongArch64 (交叉编译)
- macOS (最新版本)

**构建矩阵**:
```yaml
strategy:
  matrix:
    platform: [linux-x86_64, loongarch64, macos]
    build_type: [Debug, Release]
```

**构建步骤**:
1. 环境准备和依赖安装
2. 工具链配置
3. 项目构建
4. 单元测试执行
5. 部署包创建

#### 3. 前端构建 (build-frontend)

**技术栈**: Vue.js 3 + TypeScript + Vite

**构建步骤**:
1. Node.js环境设置
2. 依赖安装 (npm ci)
3. 代码检查 (ESLint + TypeScript)
4. 生产构建
5. 构建产物上传

#### 4. 集成测试 (integration-test)

**测试内容**:
- API接口测试
- WebSocket连接测试
- 数据库集成测试
- 服务间通信测试

#### 5. 性能测试 (performance-test)

**测试指标**:
- API响应时间
- 并发处理能力
- 内存使用情况
- CPU性能表现

#### 6. 安全扫描 (security-scan)

**扫描工具**:
- CodeQL静态分析
- 依赖漏洞扫描
- 容器安全检查
- 密钥泄露检测

#### 7. 发布部署 (release)

**发布条件**: 版本标签推送 (v*.*.*)

**发布步骤**:
1. 收集所有构建产物
2. 生成发布说明
3. 创建GitHub Release
4. 部署到各环境

## 自动化构建系统

### 构建脚本

#### 主构建脚本 (build.sh)

**用法**:
```bash
./build_scripts/build.sh [平台] [构建类型] [清理构建]
```

**支持平台**:
- `native`: 本地平台自动检测
- `linux`: Linux x86_64
- `loongarch64`: 龙芯LoongArch64交叉编译
- `darwin`: macOS开发环境

**构建类型**:
- `Debug`: 调试版本，包含调试信息和测试
- `Release`: 发布版本，优化编译

**特性**:
- 自动平台检测
- 并行编译支持
- 错误处理和日志
- 构建产物验证

#### 部署包创建脚本 (create_deployment_package.sh)

**功能**:
- 创建标准化部署包
- 自动硬件检测配置
- 多种包格式支持 (tar.gz, deb, rpm, installer)
- 安装/卸载脚本生成

**用法**:
```bash
./build_scripts/create_deployment_package.sh [平台] [构建类型] [包类型] [输出目录]
```

**包类型**:
- `tar`: tar.gz压缩包
- `deb`: Debian/Ubuntu包
- `rpm`: RedHat/CentOS包
- `installer`: 自解压安装脚本
- `all`: 所有类型

#### 测试执行脚本 (run_tests.sh)

**测试类型**:
- `unit`: 单元测试
- `integration`: 集成测试
- `performance`: 性能测试
- `coverage`: 测试覆盖率
- `memory`: 内存泄漏检测
- `static`: 静态代码分析

**用法**:
```bash
./build_scripts/run_tests.sh [测试类型] [平台] [构建类型] [详细输出]
```

### 交叉编译支持

#### LoongArch64 工具链

**工具链文件**: `build_scripts/toolchains/loongarch64-linux-gnu.cmake`

**配置要点**:
- 编译器路径设置
- 系统根目录配置
- 架构特定编译标志
- 链接器选项优化

**安装工具链**:
```bash
# 下载龙芯工具链（示例）
wget https://github.com/loongson/build-tools/releases/latest/download/loongarch64-clfs-system.tar.xz

# 解压到标准位置
sudo tar -xf loongarch64-clfs-system.tar.xz -C /opt/

# 验证安装
/opt/loongarch64-linux-gnu/bin/loongarch64-linux-gnu-gcc --version
```

## 自动化测试

### 测试框架

#### C++ 测试框架
- **单元测试**: Google Test (gtest)
- **集成测试**: 自定义测试框架
- **性能测试**: Google Benchmark
- **内存检测**: Valgrind + AddressSanitizer

#### 前端测试框架
- **单元测试**: Vitest
- **组件测试**: Vue Test Utils
- **端到端测试**: Playwright
- **代码检查**: ESLint + TypeScript

### 测试策略

#### 测试金字塔

```
    /\
   /E2E\     <- 端到端测试 (少量)
  /______\
 /        \
/Integration\ <- 集成测试 (适量)
\____________/
\            /
 \   Unit   /  <- 单元测试 (大量)
  \________/
```

#### 测试覆盖率目标

- **单元测试覆盖率**: ≥80%
- **集成测试覆盖率**: ≥60%
- **关键路径覆盖率**: 100%

### 测试环境

#### 测试数据管理

**Mock数据**:
- GNSS模拟数据
- 网络时间戳数据
- 硬件状态数据
- 配置文件模板

**测试数据库**:
- SQLite内存数据库
- 预填充测试数据
- 事务回滚支持

#### 测试隔离

- 独立的测试环境
- 容器化测试执行
- 并行测试支持
- 资源清理机制

## 部署自动化

### 部署策略

#### 蓝绿部署

```mermaid
graph LR
    A[负载均衡器] --> B[蓝色环境 v1.0]
    A -.-> C[绿色环境 v1.1]
    
    D[部署新版本] --> C
    E[切换流量] --> A
    F[验证新版本] --> G[完成部署]
```

**优势**:
- 零停机部署
- 快速回滚能力
- 风险最小化

#### 滚动部署

**适用场景**: 多实例部署环境

**部署步骤**:
1. 逐个更新实例
2. 健康检查验证
3. 流量逐步切换
4. 监控和回滚

### 环境管理

#### 环境分类

1. **开发环境 (Development)**
   - 开发者本地环境
   - 功能开发和调试
   - Mock数据和服务

2. **测试环境 (Testing)**
   - 自动化测试执行
   - 集成测试验证
   - 性能基准测试

3. **预生产环境 (Staging)**
   - 生产环境镜像
   - 发布前最终验证
   - 用户验收测试

4. **生产环境 (Production)**
   - 正式服务环境
   - 高可用性配置
   - 监控和告警

#### 配置管理

**环境配置**:
```bash
environments/
├── development/
│   ├── config.json
│   └── docker-compose.yml
├── testing/
│   ├── config.json
│   └── k8s-manifests/
├── staging/
│   ├── config.json
│   └── terraform/
└── production/
    ├── config.json
    └── ansible/
```

**配置原则**:
- 环境特定配置分离
- 敏感信息加密存储
- 版本控制管理
- 自动化配置部署

### 容器化部署

#### Docker 支持

**Dockerfile 示例**:
```dockerfile
FROM ubuntu:20.04

# 安装依赖
RUN apt-get update && apt-get install -y \
    sqlite3 libsqlite3-0 \
    openssl libssl1.1 \
    chrony linuxptp gpsd

# 复制应用程序
COPY timing-server /usr/local/bin/
COPY config/ /etc/timing-server/

# 设置权限
RUN useradd -r timing-server
RUN chown -R timing-server:timing-server /etc/timing-server

# 暴露端口
EXPOSE 8080 8081 123/udp 319/udp 320/udp

# 启动命令
CMD ["/usr/local/bin/timing-server", "--config", "/etc/timing-server/config.json"]
```

#### Kubernetes 部署

**部署清单示例**:
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: timing-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: timing-server
  template:
    metadata:
      labels:
        app: timing-server
    spec:
      containers:
      - name: timing-server
        image: timing-server:latest
        ports:
        - containerPort: 8080
        - containerPort: 8081
        env:
        - name: CONFIG_PATH
          value: "/etc/timing-server/config.json"
        volumeMounts:
        - name: config
          mountPath: /etc/timing-server
        - name: data
          mountPath: /var/lib/timing-server
      volumes:
      - name: config
        configMap:
          name: timing-server-config
      - name: data
        persistentVolumeClaim:
          claimName: timing-server-data
```

## 版本管理

### 版本控制策略

#### Git Flow 工作流

```mermaid
gitgraph
    commit id: "Initial"
    branch develop
    checkout develop
    commit id: "Feature A"
    commit id: "Feature B"
    branch feature/new-api
    checkout feature/new-api
    commit id: "API Dev"
    checkout develop
    merge feature/new-api
    checkout main
    merge develop
    commit id: "Release v1.1.0" tag: "v1.1.0"
    branch hotfix/critical-bug
    checkout hotfix/critical-bug
    commit id: "Bug Fix"
    checkout main
    merge hotfix/critical-bug
    commit id: "Hotfix v1.1.1" tag: "v1.1.1"
```

#### 分支策略

- **main**: 生产就绪代码
- **develop**: 开发集成分支
- **feature/***: 功能开发分支
- **release/***: 发布准备分支
- **hotfix/***: 紧急修复分支

### 语义化版本

**版本格式**: `MAJOR.MINOR.PATCH`

- **MAJOR**: 不兼容的API变更
- **MINOR**: 向后兼容的功能新增
- **PATCH**: 向后兼容的问题修复

**版本管理脚本**:
```bash
# 升级版本
./build_scripts/release_management.sh bump 1.2.0

# 创建发布
./build_scripts/release_management.sh release 1.2.0

# 回滚版本
./build_scripts/release_management.sh rollback 1.1.0
```

### 发布流程

#### 自动发布

**触发条件**: 推送版本标签

**发布步骤**:
1. 验证版本格式
2. 执行完整构建
3. 运行所有测试
4. 创建部署包
5. 生成发布说明
6. 创建GitHub Release
7. 部署到环境

#### 手动发布

**发布检查清单**:
- [ ] 代码审查完成
- [ ] 所有测试通过
- [ ] 文档更新完成
- [ ] 变更日志更新
- [ ] 安全扫描通过
- [ ] 性能测试通过
- [ ] 备份当前版本

## 监控和告警

### CI/CD 监控

#### 构建监控

**监控指标**:
- 构建成功率
- 构建时间趋势
- 测试通过率
- 部署频率

**告警条件**:
- 构建失败
- 测试覆盖率下降
- 部署失败
- 性能回归

#### 部署监控

**监控内容**:
- 部署成功率
- 部署时间
- 回滚频率
- 环境健康状态

### 质量门禁

#### 代码质量门禁

- 代码覆盖率 ≥ 80%
- 静态分析无严重问题
- 安全扫描通过
- 性能测试通过

#### 发布质量门禁

- 所有测试通过
- 文档更新完成
- 安全审查通过
- 性能基准达标

## 故障处理

### 构建失败处理

#### 常见构建问题

1. **依赖问题**:
   - 检查依赖版本
   - 更新包管理器缓存
   - 验证依赖可用性

2. **编译错误**:
   - 检查代码语法
   - 验证编译器版本
   - 检查头文件路径

3. **测试失败**:
   - 分析测试日志
   - 检查测试环境
   - 验证测试数据

#### 故障恢复流程

1. **问题识别**: 分析构建日志
2. **影响评估**: 确定影响范围
3. **快速修复**: 应用临时解决方案
4. **根因分析**: 找出根本原因
5. **永久修复**: 实施长期解决方案
6. **预防措施**: 避免类似问题

### 部署失败处理

#### 回滚策略

**自动回滚条件**:
- 健康检查失败
- 关键指标异常
- 用户报告严重问题

**回滚步骤**:
1. 停止新版本部署
2. 切换到上一版本
3. 验证服务恢复
4. 通知相关人员
5. 分析失败原因

## 最佳实践

### 开发最佳实践

1. **代码提交**:
   - 小而频繁的提交
   - 清晰的提交信息
   - 代码审查流程

2. **测试驱动**:
   - 先写测试后写代码
   - 保持高测试覆盖率
   - 持续重构改进

3. **文档维护**:
   - 及时更新文档
   - 代码注释完整
   - API文档同步

### 运维最佳实践

1. **监控告警**:
   - 全面的监控覆盖
   - 合理的告警阈值
   - 快速响应机制

2. **备份恢复**:
   - 定期备份验证
   - 快速恢复能力
   - 灾难恢复演练

3. **安全管理**:
   - 定期安全扫描
   - 及时更新补丁
   - 访问权限控制

### 团队协作

1. **沟通协调**:
   - 定期团队会议
   - 问题及时沟通
   - 知识分享机制

2. **技能提升**:
   - 持续学习培训
   - 技术分享交流
   - 最佳实践总结

## 工具和资源

### 开发工具

- **IDE**: VS Code, CLion
- **版本控制**: Git, GitHub
- **构建工具**: CMake, Make
- **包管理**: apt, yum, brew

### CI/CD 工具

- **CI平台**: GitHub Actions
- **构建工具**: Docker, Buildx
- **测试工具**: gtest, Valgrind
- **部署工具**: Ansible, Terraform

### 监控工具

- **应用监控**: Prometheus, Grafana
- **日志分析**: ELK Stack
- **告警通知**: AlertManager
- **性能分析**: Jaeger, Zipkin

### 文档资源

- **官方文档**: https://docs.timing-server.com
- **API文档**: https://api.timing-server.com
- **开发指南**: https://dev.timing-server.com
- **社区论坛**: https://forum.timing-server.com

---

**文档版本**: 1.0.0  
**最后更新**: 2024年1月  
**维护团队**: DevOps Team  
**联系方式**: <EMAIL>