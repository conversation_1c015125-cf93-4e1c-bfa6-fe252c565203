# 高精度授时服务器系统用户手册

## 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [系统配置](#系统配置)
4. [Web管理界面](#web管理界面)
5. [API接口使用](#api接口使用)
6. [监控和告警](#监控和告警)
7. [故障排除](#故障排除)
8. [维护指南](#维护指南)
9. [常见问题](#常见问题)
10. [技术支持](#技术支持)

## 系统概述

### 什么是高精度授时服务器系统

高精度授时服务器系统是一个专业的时间同步解决方案，旨在为网络环境提供高精度、高可靠性的时间基准服务。系统集成了多种时间源，包括GNSS卫星导航系统、铷原子钟、高精度RTC等，通过智能的时钟驯服算法实现纳秒级的时间精度。

### 主要特性

- **高精度时间同步**: 在GNSS锁定状态下提供±50ns的绝对时间精度
- **多时间源融合**: 支持GNSS、铷原子钟、RTC、外部1PPS和10MHz信号
- **标准协议支持**: 同时提供PTP特级主时钟和NTP Stratum 1服务
- **智能守时功能**: 在GNSS信号中断时提供±1μs/24h的守时精度
- **现代化管理界面**: 基于Vue.js的响应式Web管理界面
- **多平台支持**: 支持Linux x86_64和龙芯LoongArch64平台

### 应用场景

- **电信运营商**: 为5G基站提供高精度时间同步
- **金融交易**: 为高频交易系统提供时间戳服务
- **电力系统**: 为智能电网提供时间基准
- **科研院所**: 为精密测量和实验提供时间参考
- **数据中心**: 为分布式系统提供统一时间基准

## 快速开始

### 系统要求

#### 硬件要求
- CPU: 双核2.0GHz或更高
- 内存: 2GB RAM或更高
- 存储: 10GB可用空间
- 网络: 千兆以太网（支持硬件时间戳）

#### 软件要求
- 操作系统: Ubuntu 20.04+, CentOS 8+, 或龙芯Linux
- 内核版本: 5.4+（支持PTP硬件时间戳）
- 浏览器: Chrome 90+, Firefox 88+, Safari 14+

### 安装步骤

#### 1. 下载系统
```bash
# 从官方网站下载最新版本
wget https://releases.timing-server.com/latest/timing-server-system-linux-x86_64.tar.gz

# 或从GitHub下载
wget https://github.com/timing-server/timing-server-system/releases/latest/download/timing-server-system-linux-x86_64.tar.gz
```

#### 2. 解压安装包
```bash
tar -xzf timing-server-system-linux-x86_64.tar.gz
cd timing-server-system-linux-x86_64
```

#### 3. 运行安装脚本
```bash
sudo ./install.sh
```

安装脚本会自动：
- 检测系统硬件配置
- 安装必要的依赖包
- 创建系统用户和目录
- 配置systemd服务
- 生成初始配置文件

#### 4. 启动服务
```bash
# 启动服务
sudo systemctl start timing-server

# 设置开机自启动
sudo systemctl enable timing-server

# 检查服务状态
sudo systemctl status timing-server
```

#### 5. 访问管理界面
打开浏览器，访问 `http://服务器IP:8080`

默认登录信息：
- 用户名: `admin`
- 密码: `admin123`

**重要**: 首次登录后请立即修改默认密码！

---

**文档版本**: 1.0.0  
**最后更新**: 2024年1月  
**适用版本**: 高精度授时服务器系统 v1.0.0+  
**维护团队**: Timing Server Development Team