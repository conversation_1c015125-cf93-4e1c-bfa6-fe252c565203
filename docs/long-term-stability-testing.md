# 长期稳定性测试指南

## 概述

本文档描述了高精度授时服务器系统的长期稳定性测试框架，专门用于验证系统在7x24小时连续运行中的稳定性和可靠性。

## 测试目标

根据任务13.2的要求，长期稳定性测试需要验证以下核心指标：

1. **7x24小时连续运行测试，验证99.9%系统可用性目标** (需求11.2)
2. **验证内存泄漏和资源清理机制，确保<100MB内存占用** (需求11.3)
3. **测试各种异常情况和恢复能力，包含硬件故障和网络中断** (需求13.1)
4. **优化系统性能和资源使用效率，确保<5% CPU使用率** (需求13.2)

## 测试架构

### 长期稳定性测试框架

```
长期稳定性测试框架
├── 连续运行测试
│   ├── 系统可用性监控
│   ├── 健康检查机制
│   └── 故障检测和记录
├── 资源使用监控
│   ├── CPU使用率监控
│   ├── 内存使用监控
│   ├── 文件描述符监控
│   └── 网络资源监控
├── 异常处理测试
│   ├── 硬件故障模拟
│   ├── 网络中断模拟
│   ├── 资源压力测试
│   └── 恢复能力验证
└── 性能优化验证
    ├── 负载测试
    ├── 响应时间监控
    ├── 吞吐量测试
    └── 资源效率分析
```

## 测试用例详述

### 1. 连续运行测试 (test_long_term_stability.cpp)

#### 1.1 7x24小时连续运行测试
```cpp
TEST_F(LongTermStabilityTest, ContinuousOperationTest)
```

**测试目标**: 验证99.9%系统可用性目标

**测试配置**:
- 默认测试时长: 24小时（可通过环境变量调整）
- 健康检查间隔: 30秒
- 指标收集间隔: 5分钟
- 故障模拟频率: 随机（1/1000概率）

**测试步骤**:
1. 启动系统并初始化监控
2. 定期执行健康检查
3. 收集系统性能指标
4. 模拟随机故障
5. 执行内存压力测试
6. 验证最终可用性指标

**验收标准**:
- 系统可用性 > 99.9%
- 平均CPU使用率 < 5%
- 平均内存使用 < 100MB
- 峰值内存使用 < 120MB

#### 1.2 内存泄漏检测测试
```cpp
TEST_F(LongTermStabilityTest, MemoryLeakDetectionTest)
```

**测试目标**: 验证内存泄漏和资源清理机制

**测试方法**:
- 执行1000个测试周期
- 每个周期100次操作
- 定期检查内存使用情况
- 分析内存增长趋势

**验收标准**:
- 内存增长 < 20%
- 最终内存使用 < 100MB
- 无明显内存泄漏迹象

#### 1.3 异常处理和恢复测试
```cpp
TEST_F(LongTermStabilityTest, ExceptionHandlingAndRecoveryTest)
```

**测试目标**: 测试各种异常情况和恢复能力

**异常场景**:
- GNSS信号丢失
- 铷钟故障
- API过载
- 内存压力
- 配置损坏

**验收标准**:
- 异常恢复成功率 > 80%
- 严重错误数 < 总错误数的10%
- 恢复时间 < 30秒

#### 1.4 系统性能优化验证测试
```cpp
TEST_F(LongTermStabilityTest, SystemPerformanceOptimizationTest)
```

**测试目标**: 优化系统性能和资源使用效率

**负载级别**:
- 10 req/s (轻负载)
- 50 req/s (中负载)
- 100 req/s (重负载)
- 200 req/s (极限负载)

**验收标准**:
- 平均CPU使用率 < 5%
- 峰值CPU使用率 < 15%
- 平均内存使用 < 100MB
- 峰值内存使用 < 120MB

## 测试执行流程

### 1. 环境准备

```bash
# 构建测试版本
./build_scripts/build.sh auto Debug

# 检查长期稳定性测试可执行文件
ls build-*-Debug/test_long_term_stability
```

### 2. 执行长期稳定性测试

#### 2.1 快速测试（1小时）
```bash
# 1小时快速验证测试
./build_scripts/run_long_term_tests.sh 1 auto Debug 300 true
```

#### 2.2 标准测试（24小时）
```bash
# 24小时标准稳定性测试
./build_scripts/run_long_term_tests.sh 24 auto Debug 300 true
```

#### 2.3 完整测试（7天）
```bash
# 7天完整稳定性测试
./build_scripts/run_long_term_tests.sh 168 linux Release 600 true
```

#### 2.4 自定义测试
```bash
# 使用环境变量控制测试参数
export LONG_TERM_TEST_HOURS=48
export STABILITY_TEST_LOG=/var/log/stability_test.log

cd build-auto-Debug
./test_long_term_stability
```

### 3. 实时监控

测试执行期间会生成实时监控页面：

```bash
# 查看实时监控页面
open build-auto-Debug/long_term_stability_*/realtime_status.html
```

监控页面包含：
- 系统可用性实时指标
- CPU和内存使用趋势
- 运行时间统计
- 最新系统状态

### 4. 测试报告分析

测试完成后会生成详细报告：

```bash
# 查看最终测试报告
open build-auto-Debug/long_term_stability_*/final_stability_report.html

# 查看详细日志
cat build-auto-Debug/long_term_stability_*/stability_test.log
cat build-auto-Debug/long_term_stability_*/system_monitoring.log
cat build-auto-Debug/long_term_stability_*/performance_metrics.log
```

## 性能基准和验收标准

### 系统可用性要求

| 指标 | 要求 | 测试方法 | 计算公式 |
|------|------|----------|----------|
| 系统可用性 | > 99.9% | 健康检查统计 | (成功检查数/总检查数) × 100% |
| MTBF | > 8760小时 | 故障间隔统计 | 总运行时间/故障次数 |
| MTTR | < 5分钟 | 恢复时间统计 | 平均故障恢复时间 |

### 资源使用要求

| 资源类型 | 要求 | 监控方法 | 验证标准 |
|----------|------|----------|----------|
| CPU使用率 | < 5% | 系统监控 | 长期平均值 |
| 内存使用 | < 100MB | 进程监控 | 峰值和平均值 |
| 文件描述符 | 稳定 | 系统统计 | 无泄漏增长 |
| 网络连接 | 稳定 | 连接监控 | 无异常增长 |

### 性能响应要求

| 性能指标 | 要求 | 测试负载 | 验证方法 |
|----------|------|----------|----------|
| API响应时间 | < 10ms | 正常负载 | 平均响应时间 |
| 并发处理能力 | > 100 req/s | 并发测试 | 吞吐量统计 |
| 故障恢复时间 | < 30秒 | 故障模拟 | 恢复时间统计 |

## 测试数据和监控指标

### 系统监控数据格式

```csv
时间戳,CPU使用率(%),内存使用(MB),磁盘使用(%),网络接收(KB/s),网络发送(KB/s),进程数,文件描述符数
2024-01-01 12:00:00,3.2,78,45,12.5,8.3,156,89
```

### 性能指标数据格式

```csv
时间戳,API响应时间(ms),系统状态,活跃连接数,错误计数
2024-01-01 12:00:00,5.2,LOCKED,10,0
```

### 长期指标统计

```cpp
struct LongTermMetrics {
    // 可用性指标
    uint64_t total_checks;
    uint64_t successful_checks;
    double availability_percent;
    
    // 性能指标
    std::vector<double> cpu_samples;
    std::vector<size_t> memory_samples;
    std::vector<std::chrono::microseconds> response_time_samples;
    
    // 资源使用指标
    size_t peak_memory_kb;
    size_t peak_threads;
    size_t peak_file_descriptors;
    
    // 错误和恢复指标
    uint64_t total_errors;
    uint64_t recovered_errors;
    uint64_t critical_errors;
};
```

## 故障模拟和恢复测试

### 硬件故障模拟

#### GNSS信号丢失模拟
```cpp
bool SimulateGnssSignalLoss() {
    // 设置GNSS信号无效
    EXPECT_CALL(*mock_gnss, IsSignalValid())
        .WillRepeatedly(Return(false));
    
    // 等待系统检测并切换到守时模式
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    // 恢复GNSS信号
    EXPECT_CALL(*mock_gnss, IsSignalValid())
        .WillRepeatedly(Return(true));
    
    return true; // 恢复成功
}
```

#### 铷钟故障模拟
```cpp
bool SimulateRubidiumFault() {
    // 设置铷钟故障状态
    EXPECT_CALL(*mock_rubidium, GetStatus())
        .WillRepeatedly(Return(hal::ClockState::FAULT));
    
    // 等待系统检测并处理故障
    std::this_thread::sleep_for(std::chrono::seconds(15));
    
    // 恢复铷钟正常状态
    EXPECT_CALL(*mock_rubidium, GetStatus())
        .WillRepeatedly(Return(hal::ClockState::LOCKED));
    
    return true; // 恢复成功
}
```

### 软件异常模拟

#### API过载模拟
```cpp
bool SimulateApiOverload() {
    // 生成大量并发请求
    std::vector<std::thread> overload_threads;
    
    for (int i = 0; i < 100; ++i) {
        overload_threads.emplace_back([&]() {
            for (int j = 0; j < 100; ++j) {
                timing_service_->GetSystemStatus();
            }
        });
    }
    
    // 等待过载测试完成
    for (auto& thread : overload_threads) {
        thread.join();
    }
    
    return true; // 系统应该能够处理过载
}
```

#### 内存压力模拟
```cpp
bool SimulateMemoryPressure() {
    // 分配大量内存块
    std::vector<std::vector<char>> pressure_blocks;
    
    for (int i = 0; i < 50; ++i) {
        pressure_blocks.emplace_back(1024 * 1024); // 1MB块
        std::fill(pressure_blocks.back().begin(), 
                 pressure_blocks.back().end(), 
                 static_cast<char>(i % 256));
    }
    
    // 在内存压力下执行操作
    std::this_thread::sleep_for(std::chrono::seconds(10));
    
    // 释放内存
    pressure_blocks.clear();
    
    return true; // 内存压力测试完成
}
```

## 测试环境配置

### 系统要求

**最低配置**:
- CPU: 2核心
- 内存: 4GB
- 磁盘: 10GB可用空间
- 网络: 稳定的网络连接

**推荐配置**:
- CPU: 4核心或更多
- 内存: 8GB或更多
- 磁盘: 50GB可用空间（用于日志存储）
- 网络: 千兆网络连接

### 环境变量配置

```bash
# 测试时长控制
export LONG_TERM_TEST_HOURS=24

# 日志文件路径
export STABILITY_TEST_LOG=/var/log/timing_server/stability.log

# 监控间隔设置
export MONITORING_INTERVAL=300

# 测试模式设置
export TEST_MODE=production
```

### Docker环境支持

```dockerfile
# Dockerfile for long-term stability testing
FROM ubuntu:20.04

RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    libgtest-dev \
    libgmock-dev \
    && rm -rf /var/lib/apt/lists/*

COPY . /app
WORKDIR /app

RUN ./build_scripts/build.sh linux Release
CMD ["./build_scripts/run_long_term_tests.sh", "24", "linux", "Release"]
```

## 持续集成配置

### GitHub Actions配置

```yaml
name: Long-term Stability Tests

on:
  schedule:
    - cron: '0 0 * * 0'  # 每周日运行
  workflow_dispatch:
    inputs:
      duration:
        description: 'Test duration in hours'
        required: true
        default: '24'

jobs:
  stability-test:
    runs-on: ubuntu-latest
    timeout-minutes: 1500  # 25小时超时
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgtest-dev libgmock-dev
    
    - name: Build project
      run: ./build_scripts/build.sh linux Release
    
    - name: Run stability tests
      run: |
        duration=${{ github.event.inputs.duration || '24' }}
        ./build_scripts/run_long_term_tests.sh $duration linux Release 300 true
    
    - name: Upload test reports
      uses: actions/upload-artifact@v2
      if: always()
      with:
        name: stability-test-reports
        path: build-linux-Release/long_term_stability_*
```

## 故障排除指南

### 常见问题和解决方案

#### 1. 测试进程意外终止
**症状**: 测试进程在运行过程中突然停止
**可能原因**: 
- 系统资源不足
- 内存泄漏导致OOM
- 信号处理问题

**解决方案**:
```bash
# 检查系统资源
free -h
df -h

# 检查进程状态
ps aux | grep test_long_term_stability

# 查看系统日志
dmesg | tail -50
journalctl -u timing-server --since "1 hour ago"
```

#### 2. 内存使用持续增长
**症状**: 内存使用量持续增长，超过预期阈值
**可能原因**:
- 内存泄漏
- 缓存未正确清理
- 第三方库问题

**解决方案**:
```bash
# 使用valgrind检测内存泄漏
valgrind --tool=memcheck --leak-check=full ./test_long_term_stability

# 使用AddressSanitizer
export ASAN_OPTIONS=detect_leaks=1
./test_long_term_stability
```

#### 3. CPU使用率过高
**症状**: CPU使用率持续超过5%阈值
**可能原因**:
- 算法效率问题
- 无限循环
- 过度的系统调用

**解决方案**:
```bash
# 使用perf分析CPU使用
perf record -g ./test_long_term_stability
perf report

# 使用top监控进程
top -p $(pgrep test_long_term_stability)
```

#### 4. 测试报告生成失败
**症状**: 测试完成但报告文件缺失或损坏
**可能原因**:
- 磁盘空间不足
- 权限问题
- 脚本错误

**解决方案**:
```bash
# 检查磁盘空间
df -h

# 检查权限
ls -la build-*/long_term_stability_*

# 手动生成报告
./build_scripts/run_long_term_tests.sh 0 auto Debug 300 true
```

## 总结

长期稳定性测试框架提供了全面的系统可靠性验证能力，确保：

1. **高可用性**: 验证99.9%系统可用性目标
2. **资源优化**: 确保CPU和内存使用符合要求
3. **故障恢复**: 验证各种异常情况的处理能力
4. **长期稳定**: 通过连续运行测试验证系统稳定性

通过执行这些长期稳定性测试，可以确信系统已准备好在生产环境中提供可靠的高精度授时服务。