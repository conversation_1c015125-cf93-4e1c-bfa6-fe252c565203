#include "backend/include/core/clock_state_machine.h"
#include "backend/include/core/signal_quality_evaluator.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace timing_server::core;

/**
 * @brief 演示状态机监听器
 */
class DemoStateMachineListener : public IStateMachineListener {
public:
    void OnStateEntered(ClockState state, ClockState previous_state) override {
        std::cout << "🔄 状态转换: " << ClockStateToString(previous_state) 
                  << " -> " << ClockStateToString(state) << std::endl;
    }
    
    void OnStateExited(ClockState state, ClockState next_state) override {
        // 可以在这里添加状态退出处理
    }
    
    void OnEventProcessed(const StateMachineEvent& event, ClockState current_state) override {
        std::cout << "✅ 事件处理成功: " << ClockEventToString(event.event) 
                  << " (当前状态: " << ClockStateToString(current_state) << ")" << std::endl;
    }
    
    void OnTransitionFailed(const StateMachineEvent& event, ClockState current_state, TransitionResult result) override {
        std::cout << "❌ 状态转换失败: " << ClockEventToString(event.event) 
                  << " (原因: " << TransitionResultToString(result) << ")" << std::endl;
    }
};

int main() {
    std::cout << "=== 高精度授时服务器时钟状态机演示 ===" << std::endl;
    std::cout << std::endl;
    
    // 创建状态机
    auto state_machine = std::make_unique<ClockStateMachine>(ClockState::FREE_RUN);
    auto listener = std::make_shared<DemoStateMachineListener>();
    
    // 初始化和启动
    if (!state_machine->Initialize()) {
        std::cerr << "状态机初始化失败" << std::endl;
        return 1;
    }
    
    state_machine->AddListener(listener);
    
    if (!state_machine->Start()) {
        std::cerr << "状态机启动失败" << std::endl;
        return 1;
    }
    
    std::cout << "📍 初始状态: " << ClockStateToString(state_machine->GetCurrentState()) << std::endl;
    std::cout << std::endl;
    
    // 演示1: GNSS信号获取 (FREE_RUN -> DISCIPLINING)
    std::cout << "🛰️  模拟GNSS信号获取..." << std::endl;
    StateMachineEvent gnss_acquired(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号获取");
    gnss_acquired.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,08,0.9,545.4,M,46.9,M,,*47\r\n"
        "$GPGSA,A,3,04,05,09,12,,,,,,,,,1.5,0.9,1.2*39\r\n";
    
    state_machine->ProcessEvent(gnss_acquired);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 演示2: 驯服过程
    std::cout << "⚙️  开始驯服过程..." << std::endl;
    for (int i = 0; i < 20; ++i) {
        double phase_error = 200.0 * std::exp(-i * 0.25); // 指数衰减
        double freq_error = 0.2 * std::exp(-i * 0.2);
        
        state_machine->AddDiscipliningSample(phase_error, freq_error);
        
        if (i % 5 == 0) {
            std::cout << "   样本 " << (i+1) << ": 相位误差 = " << std::fixed << std::setprecision(1) 
                      << phase_error << " ns, 频率误差 = " << freq_error << " ppm" << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 检查收敛状态
    if (state_machine->IsDiscipliningConverged()) {
        std::cout << "✅ 驯服已收敛!" << std::endl;
    } else {
        std::cout << "⏳ 驯服尚未收敛，继续等待..." << std::endl;
    }
    
    // 演示3: 驯服收敛 (DISCIPLINING -> LOCKED)
    std::cout << "🔒 驯服收敛完成..." << std::endl;
    StateMachineEvent convergence(ClockEvent::CONVERGENCE_ACHIEVED, "驯服收敛完成");
    state_machine->ProcessEvent(convergence);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 演示4: GNSS信号丢失 (LOCKED -> HOLDOVER)
    std::cout << "📡 模拟GNSS信号丢失..." << std::endl;
    StateMachineEvent signal_lost(ClockEvent::GNSS_SIGNAL_LOST, "GNSS信号丢失");
    signal_lost.context["frequency_offset"] = "0.001"; // 1 ppb
    state_machine->ProcessEvent(signal_lost);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 演示5: 守时过程
    std::cout << "🕐 开始守时过程..." << std::endl;
    for (int i = 0; i < 10; ++i) {
        double freq_offset = 0.001 + i * 0.0001; // 模拟频率漂移
        double temperature = 25.0 + i * 0.2;     // 模拟温度变化
        
        state_machine->UpdateHoldoverStatus(freq_offset, temperature);
        
        if (i % 3 == 0) {
            HoldoverQuality quality = state_machine->GetHoldoverQuality();
            std::cout << "   守时质量评分: " << quality.quality_score 
                      << ", 预测精度: " << std::fixed << std::setprecision(1) 
                      << quality.predicted_accuracy_ns << " ns" << std::endl;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    
    // 演示6: GNSS信号恢复 (HOLDOVER -> DISCIPLINING)
    std::cout << "🛰️  GNSS信号恢复..." << std::endl;
    StateMachineEvent signal_recovered(ClockEvent::GNSS_SIGNAL_ACQUIRED, "GNSS信号恢复");
    signal_recovered.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,2,10,0.8,545.4,M,46.9,M,,*47\r\n";
    state_machine->ProcessEvent(signal_recovered);
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 演示7: 再次收敛
    std::cout << "⚙️  重新驯服..." << std::endl;
    for (int i = 0; i < 15; ++i) {
        state_machine->AddDiscipliningSample(50.0, 0.01);
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    
    std::cout << "🔒 再次收敛..." << std::endl;
    state_machine->ProcessEvent(StateMachineEvent(ClockEvent::CONVERGENCE_ACHIEVED, "再次收敛"));
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // 显示最终统计信息
    std::cout << std::endl;
    std::cout << "=== 状态机统计信息 ===" << std::endl;
    std::cout << state_machine->GetStatistics() << std::endl;
    
    // 演示8: 测试低质量信号被阻止
    std::cout << "=== 测试信号质量检查 ===" << std::endl;
    
    // 重置到FREE_RUN状态
    state_machine->Reset();
    state_machine->Start();
    
    std::cout << "🔄 重置到初始状态: " << ClockStateToString(state_machine->GetCurrentState()) << std::endl;
    
    // 尝试低质量GNSS信号
    std::cout << "❌ 尝试低质量GNSS信号..." << std::endl;
    StateMachineEvent poor_signal(ClockEvent::GNSS_SIGNAL_ACQUIRED, "低质量GNSS信号");
    poor_signal.context["gnss_data"] = 
        "$GPGGA,123519,4807.038,N,01131.000,E,0,02,9.9,545.4,M,46.9,M,,*47\r\n";
    
    TransitionResult result = state_machine->ProcessEvent(poor_signal);
    std::cout << "转换结果: " << TransitionResultToString(result) << std::endl;
    std::cout << "当前状态: " << ClockStateToString(state_machine->GetCurrentState()) << std::endl;
    
    // 清理
    state_machine->Stop();
    
    std::cout << std::endl;
    std::cout << "=== 演示完成 ===" << std::endl;
    
    return 0;
}