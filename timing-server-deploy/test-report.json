{"timestamp": "2025-07-26T12:59:52.224Z", "results": [{"timestamp": "2025-07-26T12:59:45.176Z", "type": "info", "message": "=== 开始运行高精度授时服务器系统测试套件 ==="}, {"timestamp": "2025-07-26T12:59:45.179Z", "type": "info", "message": ""}, {"timestamp": "2025-07-26T12:59:45.179Z", "type": "info", "message": "\n--- 测试: API服务器连接 ---"}, {"timestamp": "2025-07-26T12:59:45.180Z", "type": "info", "message": "测试API服务器连接..."}, {"timestamp": "2025-07-26T12:59:45.188Z", "type": "success", "message": "✅ API服务器连接成功"}, {"timestamp": "2025-07-26T12:59:46.189Z", "type": "info", "message": "\n--- 测试: 用户认证 ---"}, {"timestamp": "2025-07-26T12:59:46.189Z", "type": "info", "message": "测试用户认证..."}, {"timestamp": "2025-07-26T12:59:46.191Z", "type": "success", "message": "✅ 用户认证成功"}, {"timestamp": "2025-07-26T12:59:46.191Z", "type": "info", "message": "   Token: mock-jwt-token-17535..."}, {"timestamp": "2025-07-26T12:59:47.192Z", "type": "info", "message": "\n--- 测试: 系统状态API ---"}, {"timestamp": "2025-07-26T12:59:47.193Z", "type": "info", "message": "测试系统状态API..."}, {"timestamp": "2025-07-26T12:59:47.195Z", "type": "success", "message": "✅ 系统状态API测试成功"}, {"timestamp": "2025-07-26T12:59:47.195Z", "type": "info", "message": "   系统状态: normal"}, {"timestamp": "2025-07-26T12:59:47.195Z", "type": "info", "message": "   运行时间: 15天 8小时 32分钟"}, {"timestamp": "2025-07-26T12:59:48.197Z", "type": "info", "message": "\n--- 测试: 时间源信息API ---"}, {"timestamp": "2025-07-26T12:59:48.197Z", "type": "info", "message": "测试时间源信息API..."}, {"timestamp": "2025-07-26T12:59:48.199Z", "type": "success", "message": "✅ 时间源信息API测试成功"}, {"timestamp": "2025-07-26T12:59:48.200Z", "type": "info", "message": "   当前时间源: GPS"}, {"timestamp": "2025-07-26T12:59:48.200Z", "type": "info", "message": "   精度: ±50ns"}, {"timestamp": "2025-07-26T12:59:49.201Z", "type": "info", "message": "\n--- 测试: 信号输出API ---"}, {"timestamp": "2025-07-26T12:59:49.201Z", "type": "info", "message": "测试信号输出API..."}, {"timestamp": "2025-07-26T12:59:49.204Z", "type": "success", "message": "✅ 信号输出API测试成功"}, {"timestamp": "2025-07-26T12:59:49.204Z", "type": "info", "message": "   总输出通道: 4"}, {"timestamp": "2025-07-26T12:59:49.204Z", "type": "info", "message": "   活跃通道: 3"}, {"timestamp": "2025-07-26T12:59:50.206Z", "type": "info", "message": "\n--- 测试: WebSocket连接 ---"}, {"timestamp": "2025-07-26T12:59:50.206Z", "type": "info", "message": "测试WebSocket连接..."}, {"timestamp": "2025-07-26T12:59:50.212Z", "type": "success", "message": "✅ WebSocket连接建立成功"}, {"timestamp": "2025-07-26T12:59:50.213Z", "type": "success", "message": "✅ WebSocket消息接收成功"}, {"timestamp": "2025-07-26T12:59:50.213Z", "type": "info", "message": "   消息类型: welcome"}, {"timestamp": "2025-07-26T12:59:50.214Z", "type": "success", "message": "✅ WebSocket消息接收成功"}, {"timestamp": "2025-07-26T12:59:50.214Z", "type": "info", "message": "   消息类型: status_update"}, {"timestamp": "2025-07-26T12:59:50.214Z", "type": "info", "message": "   系统状态: normal"}, {"timestamp": "2025-07-26T12:59:50.214Z", "type": "success", "message": "✅ WebSocket消息接收成功"}, {"timestamp": "2025-07-26T12:59:50.214Z", "type": "info", "message": "   消息类型: pong"}, {"timestamp": "2025-07-26T12:59:50.215Z", "type": "info", "message": "WebSocket连接已关闭"}, {"timestamp": "2025-07-26T12:59:51.215Z", "type": "info", "message": "\n--- 测试: 前端页面访问 ---"}, {"timestamp": "2025-07-26T12:59:51.215Z", "type": "info", "message": "测试前端页面访问..."}, {"timestamp": "2025-07-26T12:59:51.222Z", "type": "error", "message": "❌ 前端页面访问失败: 404"}, {"timestamp": "2025-07-26T12:59:52.224Z", "type": "info", "message": "\n=== 测试结果汇总 ==="}, {"timestamp": "2025-07-26T12:59:52.224Z", "type": "info", "message": "总测试数: 7"}, {"timestamp": "2025-07-26T12:59:52.224Z", "type": "warn", "message": "通过测试: 6"}, {"timestamp": "2025-07-26T12:59:52.224Z", "type": "error", "message": "失败测试: 1"}, {"timestamp": "2025-07-26T12:59:52.224Z", "type": "warn", "message": "成功率: 85.7%"}], "summary": {"total": 11, "passed": 9, "failed": 2}}