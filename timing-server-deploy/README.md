# 高精度授时服务器系统 - 本地部署指南

## 📋 概述

高精度授时服务器系统是一个专业的时间同步解决方案，提供±50ns的绝对精度和±1μs的计时精度。本系统支持多种时间同步协议（PTP、NTP），具备完整的Web管理界面和实时监控功能。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   HTTP API      │    │  WebSocket API  │
│   (Port 3000)   │    │   (Port 8080)   │    │   (Port 8081)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │            Timing Server Core                   │
         │         (高精度授时服务器核心)                    │
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │         Hardware Abstraction Layer              │
         │              (硬件抽象层)                        │
         └─────────────────────────────────────────────────┘
```

## 📁 目录结构

```
timing-server-deploy/
├── bin/                    # 可执行文件
│   ├── timing-server       # 主授时服务器
│   ├── simple_http_server  # HTTP API服务器
│   ├── websocket-server-example # WebSocket服务器
│   ├── hal-demo           # 硬件抽象层演示
│   ├── auth-example       # 认证示例
│   └── daemon-manager-example # 守护进程管理器
├── config/                 # 配置文件
│   ├── production.json     # 生产环境配置
│   ├── environment.env     # 环境变量配置
│   └── com.timing-server.plist # macOS服务配置
├── frontend/               # Web前端文件
│   ├── dist/              # 构建后的前端文件
│   ├── node_modules/      # Node.js依赖
│   └── package.json       # 前端包配置
├── scripts/                # 管理脚本
│   ├── timing-server-control.sh # 主控制脚本
│   ├── install.sh         # 安装脚本
│   ├── install-service.sh # 服务安装脚本
│   ├── monitor.sh         # 监控脚本
│   ├── logrotate.sh       # 日志轮转脚本
│   └── setup-cron.sh      # 定时任务设置
├── logs/                   # 日志文件
│   ├── archive/           # 归档日志
│   ├── timing-server.log  # 主服务日志
│   ├── http-api.log       # API服务日志
│   ├── websocket.log      # WebSocket日志
│   └── frontend.log       # 前端服务日志
├── data/                   # 数据文件
│   ├── pids/              # 进程ID文件
│   ├── backups/           # 数据备份
│   └── cache/             # 缓存文件
└── backup/                 # 系统备份
```

## 🚀 快速开始

### 1. 系统要求

- **操作系统**: macOS 10.15+ 或 Linux (Ubuntu 18.04+, CentOS 7+)
- **Node.js**: 16.0+ 
- **内存**: 最小 2GB，推荐 4GB+
- **磁盘空间**: 最小 1GB，推荐 5GB+
- **网络**: 需要访问端口 3000, 8080, 8081

### 2. 一键安装

```bash
# 进入部署目录
cd ~/timing-server-deploy

# 运行安装脚本
./scripts/install.sh
```

### 3. 启动服务

```bash
# 启动所有服务
./scripts/timing-server-control.sh start

# 或使用快捷命令
timing-server start
```

### 4. 访问系统

- **Web界面**: http://localhost:3000
- **API文档**: http://localhost:8080/api/docs
- **WebSocket**: ws://localhost:8081

**默认登录凭据**:
- 用户名: `admin`
- 密码: `admin123`

## 🔧 详细配置

### 环境变量配置

编辑 `config/environment.env` 文件：

```bash
# 系统配置
TIMING_SERVER_HOME=/Users/<USER>/timing-server-deploy
TIMING_SERVER_TEST_MODE=0  # 生产环境设为0

# 服务端口
TIMING_SERVER_API_PORT=8080
TIMING_SERVER_WEBSOCKET_PORT=8081
TIMING_SERVER_FRONTEND_PORT=3000

# 安全配置
JWT_SECRET=your-production-jwt-secret-key-change-this-in-production
```

### 生产环境配置

编辑 `config/production.json` 文件：

```json
{
  "system": {
    "environment": "production",
    "debug_mode": false,
    "log_level": "INFO"
  },
  "security": {
    "require_https": true,
    "jwt_secret": "your-production-secret",
    "max_login_attempts": 5
  },
  "hardware": {
    "use_mock_hal": false,  // 生产环境使用真实硬件
    "devices": {
      "gnss": {
        "enabled": true,
        "device_path": "/dev/ttyUSB0"
      }
    }
  }
}
```

## 🎛️ 服务管理

### 基本命令

```bash
# 启动所有服务
timing-server start

# 停止所有服务
timing-server stop

# 重启所有服务
timing-server restart

# 查看服务状态
timing-server status

# 查看帮助
timing-server help
```

### 单独服务控制

```bash
# 启动单个服务
timing-server start-timing    # 主授时服务器
timing-server start-api       # HTTP API服务器
timing-server start-websocket # WebSocket服务器
timing-server start-frontend  # 前端服务器
```

### 系统服务

#### macOS (LaunchAgent)

```bash
# 安装系统服务
./scripts/install-service.sh

# 管理服务
launchctl start com.timing-server
launchctl stop com.timing-server
launchctl unload ~/Library/LaunchAgents/com.timing-server.plist
```

#### Linux (systemd)

```bash
# 安装系统服务
sudo ./scripts/install-service.sh

# 管理服务
sudo systemctl start timing-server
sudo systemctl stop timing-server
sudo systemctl enable timing-server  # 开机自启
sudo systemctl status timing-server
```

## 📊 监控和日志

### 系统监控

```bash
# 运行监控检查
./scripts/monitor.sh

# 生成监控报告
./scripts/monitor.sh --report

# 查看监控日志
tail -f logs/monitor.log
```

### 日志管理

```bash
# 手动轮转日志
./scripts/logrotate.sh

# 查看各服务日志
tail -f logs/timing-server.log
tail -f logs/http-api.log
tail -f logs/websocket.log
tail -f logs/frontend.log
```

### 定时任务

```bash
# 设置定时任务
./scripts/setup-cron.sh setup

# 查看定时任务
./scripts/setup-cron.sh show

# 删除定时任务
./scripts/setup-cron.sh remove
```

## 🔒 安全配置

### 1. 更改默认密码

首次登录后，请立即更改默认管理员密码。

### 2. JWT密钥配置

```bash
# 生成新的JWT密钥
openssl rand -base64 32

# 更新配置文件
vim config/environment.env
# 修改: JWT_SECRET=新生成的密钥
```

### 3. HTTPS配置

生产环境建议启用HTTPS：

```json
{
  "security": {
    "require_https": true,
    "ssl_cert_path": "/path/to/cert.pem",
    "ssl_key_path": "/path/to/key.pem"
  }
}
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :8080
   lsof -i :8081
   
   # 查看错误日志
   tail -f logs/timing-server.log
   ```

2. **前端无法访问**
   ```bash
   # 检查前端服务状态
   timing-server status
   
   # 重启前端服务
   timing-server start-frontend
   ```

3. **API认证失败**
   ```bash
   # 检查JWT配置
   grep JWT_SECRET config/environment.env
   
   # 重启API服务
   timing-server start-api
   ```

### 日志位置

- 主服务日志: `logs/timing-server.log`
- API服务日志: `logs/http-api.log`
- WebSocket日志: `logs/websocket.log`
- 前端服务日志: `logs/frontend.log`
- 监控日志: `logs/monitor.log`
- 告警日志: `logs/alerts.log`

## 📈 性能优化

### 系统调优

1. **增加文件描述符限制**
   ```bash
   ulimit -n 65536
   ```

2. **优化网络参数**
   ```bash
   # Linux系统
   echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
   echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
   sysctl -p
   ```

3. **调整日志级别**
   ```json
   {
     "system": {
       "log_level": "WARN"  // 生产环境减少日志输出
     }
   }
   ```

## 🔄 备份和恢复

### 自动备份

系统会自动备份：
- 配置文件 (每天3点)
- 数据库文件 (每小时)
- 日志文件 (轮转时)

### 手动备份

```bash
# 备份整个系统
tar -czf timing-server-backup-$(date +%Y%m%d).tar.gz \
    -C ~ timing-server-deploy/

# 备份配置文件
tar -czf config-backup-$(date +%Y%m%d).tar.gz \
    -C ~/timing-server-deploy config/
```

### 恢复系统

```bash
# 停止服务
timing-server stop

# 恢复备份
tar -xzf timing-server-backup-YYYYMMDD.tar.gz -C ~

# 启动服务
timing-server start
```

## 📞 技术支持

如需技术支持，请提供以下信息：

1. 系统版本和操作系统信息
2. 错误日志内容
3. 系统配置文件
4. 监控报告

## 📋 运维检查清单

### 日常检查 (每天)

- [ ] 检查所有服务运行状态
- [ ] 查看系统资源使用情况
- [ ] 检查错误日志
- [ ] 验证Web界面可访问性
- [ ] 检查API响应时间

### 周检查

- [ ] 审查监控报告
- [ ] 检查磁盘空间使用
- [ ] 验证备份完整性
- [ ] 更新系统补丁
- [ ] 检查安全日志

### 月检查

- [ ] 性能基准测试
- [ ] 配置文件审查
- [ ] 用户权限审计
- [ ] 灾难恢复测试
- [ ] 文档更新

---

**版本**: v1.0.0
**更新日期**: 2024-07-26
**维护者**: 高精度授时服务器开发团队
