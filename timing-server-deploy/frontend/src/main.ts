import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import { useAuthStore } from './stores/auth'

// 导入全局样式
import './styles/main.scss'

console.log('Starting app initialization...')

const app = createApp(App)
const pinia = createPinia()

console.log('Created app and pinia instances')

// 安装插件
app.use(pinia)
app.use(router)

console.log('Installed plugins')

// 初始化认证状态
try {
  const authStore = useAuthStore()
  console.log('Created auth store')
  
  authStore.checkAuthStatus()
  console.log('Auth status checked successfully')
} catch (error) {
  console.error('Auth initialization failed:', error)
}

// 挂载应用
try {
  app.mount('#app')
  console.log('App mounted successfully')
} catch (error) {
  console.error('App mount failed:', error)
  // 显示错误信息到页面
  document.body.innerHTML = `
    <div style="padding: 20px; color: red; font-family: monospace;">
      <h2>应用启动失败</h2>
      <p>错误信息: ${error}</p>
      <p>请检查浏览器控制台获取更多信息</p>
    </div>
  `
}
