<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>高精度授时服务器系统 - API连接测试</h1>
    
    <div id="results"></div>
    
    <button onclick="testAPI()">测试API连接</button>
    <button onclick="testLogin()">测试登录</button>
    <button onclick="testWebSocket()">测试WebSocket</button>
    
    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(div);
        }
        
        async function testAPI() {
            addResult('开始测试API连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/v1/auth/login', {
                    method: 'OPTIONS'
                });
                addResult(`API服务器响应: ${response.status}`, 'success');
            } catch (error) {
                addResult(`API连接失败: ${error.message}`, 'error');
            }
        }
        
        async function testLogin() {
            addResult('开始测试登录...', 'info');
            
            try {
                const response = await fetch('http://localhost:8080/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`登录成功: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`登录失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`登录请求失败: ${error.message}`, 'error');
            }
        }
        
        function testWebSocket() {
            addResult('开始测试WebSocket连接...', 'info');
            
            try {
                const ws = new WebSocket('ws://localhost:8081');
                
                ws.onopen = function() {
                    addResult('WebSocket连接成功', 'success');
                    ws.close();
                };
                
                ws.onerror = function(error) {
                    addResult(`WebSocket连接失败: ${error}`, 'error');
                };
                
                ws.onclose = function() {
                    addResult('WebSocket连接已关闭', 'info');
                };
                
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        addResult('WebSocket连接超时', 'error');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                addResult(`WebSocket测试失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            addResult('页面加载完成，开始自动测试...', 'info');
            testAPI();
        };
    </script>
</body>
</html>
