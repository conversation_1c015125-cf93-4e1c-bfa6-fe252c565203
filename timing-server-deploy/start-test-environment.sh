#!/bin/bash

# 启动完整测试环境脚本
# 启动所有必要的服务进行前后端测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# PID文件目录
PID_DIR="./pids"
mkdir -p "$PID_DIR"

# 清理函数
cleanup() {
    log_info "正在停止所有测试服务..."
    
    # 停止所有后台进程
    for pid_file in "$PID_DIR"/*.pid; do
        if [ -f "$pid_file" ]; then
            pid=$(cat "$pid_file")
            service_name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                log_info "停止服务: $service_name (PID: $pid)"
                kill "$pid"
                sleep 1
                
                # 强制杀死如果还在运行
                if kill -0 "$pid" 2>/dev/null; then
                    kill -9 "$pid"
                fi
            fi
            
            rm -f "$pid_file"
        fi
    done
    
    log_success "所有测试服务已停止"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -i ":$port" > /dev/null 2>&1; then
        log_warn "端口 $port 已被占用 ($service_name)"
        return 1
    fi
    return 0
}

# 启动服务并记录PID
start_service() {
    local service_name=$1
    local command=$2
    local port=$3
    local log_file="logs/${service_name}.log"
    
    log_info "启动服务: $service_name"
    
    # 检查端口
    if ! check_port "$port" "$service_name"; then
        log_error "端口 $port 被占用，无法启动 $service_name"
        return 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动服务
    eval "$command" > "$log_file" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo "$pid" > "$PID_DIR/${service_name}.pid"
    
    # 等待服务启动
    sleep 2
    
    # 检查服务是否正常启动
    if kill -0 "$pid" 2>/dev/null; then
        log_success "$service_name 启动成功 (PID: $pid, 端口: $port)"
        return 0
    else
        log_error "$service_name 启动失败"
        rm -f "$PID_DIR/${service_name}.pid"
        return 1
    fi
}

# 等待服务就绪
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 服务就绪..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "$service_name 服务就绪"
            return 0
        fi
        
        sleep 1
        attempt=$((attempt + 1))
    done
    
    log_error "$service_name 服务启动超时"
    return 1
}

# 主函数
main() {
    echo "========================================"
    echo "  高精度授时服务器系统 - 测试环境启动"
    echo "========================================"
    echo ""
    
    log_info "开始启动测试环境..."
    
    # 检查依赖
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装Node.js依赖..."
        npm install ws
    fi
    
    # 启动各个服务
    log_info "启动后端服务..."
    
    # 1. 启动HTTP API服务器
    if start_service "http-api" "node simple_http_server.js" 8080; then
        wait_for_service "http://localhost:8080/api/v1/system/status" "HTTP API"
    else
        log_error "HTTP API服务器启动失败"
        exit 1
    fi
    
    # 2. 启动WebSocket服务器
    if start_service "websocket" "node websocket_server.js" 8081; then
        sleep 3  # WebSocket服务器需要更多时间启动
        log_success "WebSocket服务器启动完成"
    else
        log_error "WebSocket服务器启动失败"
        exit 1
    fi
    
    # 3. 启动配置测试API服务器
    if start_service "config-api" "node config-test-server.js" 8082; then
        wait_for_service "http://localhost:8082/api/v1/config/all" "配置API"
    else
        log_error "配置API服务器启动失败"
        exit 1
    fi
    
    # 4. 检查前端服务器
    log_info "检查前端服务器..."
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务器运行正常"
    else
        log_warn "前端服务器未运行，请手动启动: cd ../frontend && npm run dev"
    fi
    
    echo ""
    log_success "测试环境启动完成！"
    echo ""
    echo "服务状态:"
    echo "  HTTP API服务器:    http://localhost:8080"
    echo "  WebSocket服务器:   ws://localhost:8081"
    echo "  配置API服务器:     http://localhost:8082"
    echo "  前端服务器:        http://localhost:3000"
    echo ""
    echo "日志文件位置: ./logs/"
    echo "PID文件位置:  ./pids/"
    echo ""
    echo "运行测试命令:"
    echo "  后端API测试:       node test-suite.js"
    echo "  前端集成测试:      node frontend-integration-test.js"
    echo "  完整测试套件:      ./run-all-tests.sh"
    echo ""
    echo "按 Ctrl+C 停止所有服务"
    
    # 保持脚本运行
    while true; do
        sleep 10
        
        # 检查服务状态
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                pid=$(cat "$pid_file")
                service_name=$(basename "$pid_file" .pid)
                
                if ! kill -0 "$pid" 2>/dev/null; then
                    log_error "服务 $service_name 意外停止"
                    rm -f "$pid_file"
                fi
            fi
        done
    done
}

# 运行主函数
main "$@"
