<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.timing-server</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/Users/<USER>/timing-server-deploy/scripts/timing-server-control.sh</string>
        <string>start</string>
    </array>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <dict>
        <key>SuccessfulExit</key>
        <false/>
    </dict>
    
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/timing-server-deploy</string>
    
    <key>StandardOutPath</key>
    <string>/Users/<USER>/timing-server-deploy/logs/launchd.out</string>
    
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/timing-server-deploy/logs/launchd.err</string>
    
    <key>EnvironmentVariables</key>
    <dict>
        <key>TIMING_SERVER_HOME</key>
        <string>/Users/<USER>/timing-server-deploy</string>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin</string>
    </dict>
    
    <key>ProcessType</key>
    <string>Background</string>
    
    <key>ThrottleInterval</key>
    <integer>10</integer>
    
    <key>ExitTimeOut</key>
    <integer>30</integer>
</dict>
</plist>
