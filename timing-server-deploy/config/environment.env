# 高精度授时服务器系统环境变量配置
# Production Environment Configuration

# 系统配置
TIMING_SERVER_HOME=/Users/<USER>/timing-server-deploy
TIMING_SERVER_CONFIG=${TIMING_SERVER_HOME}/config/production.json
TIMING_SERVER_LOG_DIR=${TIMING_SERVER_HOME}/logs
TIMING_SERVER_DATA_DIR=${TIMING_SERVER_HOME}/data
TIMING_SERVER_TEST_MODE=0

# 服务端口配置
TIMING_SERVER_API_PORT=8080
TIMING_SERVER_WEBSOCKET_PORT=8081
TIMING_SERVER_FRONTEND_PORT=3000

# 安全配置
JWT_SECRET=your-production-jwt-secret-key-change-this-in-production
JWT_EXPIRY=3600
REFRESH_TOKEN_EXPIRY=86400

# 数据库配置
DATABASE_PATH=${TIMING_SERVER_DATA_DIR}/timing_server.db
DATABASE_BACKUP_DIR=${TIMING_SERVER_DATA_DIR}/backups

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=${TIMING_SERVER_LOG_DIR}/timing-server.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# 硬件配置
HAL_PLATFORM=macos
USE_MOCK_HAL=1
GNSS_DEVICE=/dev/ttyUSB0
PPS_DEVICE=/dev/pps0
RTC_DEVICE=/dev/rtc0

# 网络配置
BIND_ADDRESS=0.0.0.0
ENABLE_CORS=1
CORS_ORIGINS=http://localhost:3000,https://localhost:3000

# 监控配置
ENABLE_METRICS=1
METRICS_INTERVAL=10
HEALTH_CHECK_INTERVAL=30

# 性能配置
MAX_CONNECTIONS=1000
REQUEST_TIMEOUT=30
WORKER_THREADS=4

# 开发/调试配置（生产环境应设为0）
DEBUG_MODE=0
VERBOSE_LOGGING=0
ENABLE_PROFILING=0

# 备份配置
BACKUP_INTERVAL=3600
MAX_BACKUP_FILES=10
BACKUP_COMPRESSION=1

# 告警配置
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK=
SMTP_SERVER=localhost
SMTP_PORT=587
