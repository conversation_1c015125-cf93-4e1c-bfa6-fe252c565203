{"system": {"name": "高精度授时服务器系统", "version": "1.0.0", "environment": "production", "debug_mode": false, "log_level": "INFO"}, "server": {"bind_address": "0.0.0.0", "api_port": 8080, "websocket_port": 8081, "frontend_port": 3000, "max_connections": 1000, "request_timeout": 30, "enable_cors": true, "cors_origins": ["http://localhost:3000", "https://localhost:3000"]}, "security": {"require_https": false, "jwt_secret": "your-production-jwt-secret-key-change-this", "jwt_expiry": 3600, "refresh_token_expiry": 86400, "max_login_attempts": 5, "lockout_duration": 300, "password_min_length": 8, "enable_audit_log": true}, "database": {"type": "sqlite", "path": "/Users/<USER>/timing-server-deploy/data/timing_server.db", "backup_interval": 3600, "max_backup_files": 10}, "logging": {"level": "INFO", "file_path": "/Users/<USER>/timing-server-deploy/logs/timing-server.log", "max_file_size": "100MB", "max_files": 10, "enable_console": true, "enable_syslog": false}, "hardware": {"platform": "macos", "use_mock_hal": true, "devices": {"gnss": {"enabled": false, "device_path": "/dev/ttyUSB0", "baud_rate": 9600, "protocol": "NMEA"}, "pps": {"enabled": false, "device_path": "/dev/pps0", "edge": "rising"}, "atomic_clock": {"enabled": false, "type": "rubidium", "device_path": "/dev/ttyUSB1"}, "frequency_input": {"enabled": false, "frequency": 10000000, "device_path": "/dev/ttyUSB2"}, "rtc": {"enabled": false, "device_path": "/dev/rtc0", "sync_interval": 60}, "network": {"enabled": true, "interface": "en0", "enable_ptp": true, "enable_ntp": true}}}, "timing": {"sync_interval": 1, "accuracy_target": 5e-08, "stability_target": 1e-06, "holdover_duration": 3600, "leap_second_handling": true}, "monitoring": {"enable_metrics": true, "metrics_interval": 10, "health_check_interval": 30, "alert_thresholds": {"timing_accuracy": 1e-07, "memory_usage": 80, "cpu_usage": 80, "disk_usage": 90}}, "services": {"timing_server": {"enabled": true, "auto_start": true, "restart_on_failure": true, "max_restarts": 5}, "websocket_server": {"enabled": true, "auto_start": true, "restart_on_failure": true, "max_restarts": 5}, "http_api_server": {"enabled": true, "auto_start": true, "restart_on_failure": true, "max_restarts": 5}, "frontend_server": {"enabled": true, "auto_start": true, "restart_on_failure": true, "max_restarts": 5}}}