#!/usr/bin/env node

/**
 * 高精度授时服务器系统 - 综合测试套件
 * 包含前端、后端、WebSocket、API接口的全面测试
 */

const http = require('http');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

class TimingServerTestSuite {
    constructor() {
        this.testResults = [];
        this.config = {
            apiServer: 'http://localhost:8080',
            wsServer: 'ws://localhost:8081',
            frontend: 'http://localhost:3000'
        };
        this.authToken = null;
    }

    // 输出测试结果
    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const colors = {
            info: '\x1b[36m',    // 青色
            success: '\x1b[32m', // 绿色
            error: '\x1b[31m',   // 红色
            warn: '\x1b[33m',    // 黄色
            reset: '\x1b[0m'     // 重置
        };
        
        console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
        this.testResults.push({ timestamp, type, message });
    }

    // HTTP请求工具
    async makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            };

            const req = http.request(requestOptions, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
                    } catch (e) {
                        resolve({ status: res.statusCode, data, headers: res.headers });
                    }
                });
            });

            req.on('error', reject);
            
            if (options.body) {
                req.write(JSON.stringify(options.body));
            }
            
            req.end();
        });
    }

    // 测试API服务器连接
    async testApiServerConnection() {
        this.log('测试API服务器连接...', 'info');
        try {
            const response = await this.makeRequest(`${this.config.apiServer}/api/v1/system/status`);
            if (response.status === 200 || response.status === 401) {
                this.log('✅ API服务器连接成功', 'success');
                return true;
            } else {
                this.log(`❌ API服务器响应异常: ${response.status}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ API服务器连接失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试用户认证
    async testAuthentication() {
        this.log('测试用户认证...', 'info');
        try {
            const response = await this.makeRequest(`${this.config.apiServer}/api/v1/auth/login`, {
                method: 'POST',
                body: { username: 'admin', password: 'admin123' }
            });

            if (response.status === 200 && response.data.success) {
                this.authToken = response.data.data.token;
                this.log('✅ 用户认证成功', 'success');
                this.log(`   Token: ${this.authToken.substring(0, 20)}...`, 'info');
                return true;
            } else {
                this.log(`❌ 用户认证失败: ${response.data?.error?.message || '未知错误'}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ 认证请求失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试系统状态API
    async testSystemStatusAPI() {
        this.log('测试系统状态API...', 'info');
        try {
            const response = await this.makeRequest(`${this.config.apiServer}/api/v1/system/status`, {
                headers: { Authorization: `Bearer ${this.authToken}` }
            });

            if (response.status === 200 && response.data.success) {
                this.log('✅ 系统状态API测试成功', 'success');
                this.log(`   系统状态: ${response.data.data.status}`, 'info');
                this.log(`   运行时间: ${response.data.data.uptime}`, 'info');
                return true;
            } else {
                this.log(`❌ 系统状态API测试失败: ${response.status}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ 系统状态API请求失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试时间源信息API
    async testTimeSourceAPI() {
        this.log('测试时间源信息API...', 'info');
        try {
            const response = await this.makeRequest(`${this.config.apiServer}/api/v1/timesource/info`, {
                headers: { Authorization: `Bearer ${this.authToken}` }
            });

            if (response.status === 200 && response.data.success) {
                this.log('✅ 时间源信息API测试成功', 'success');
                this.log(`   当前时间源: ${response.data.data.currentSource}`, 'info');
                this.log(`   精度: ${response.data.data.precision}`, 'info');
                return true;
            } else {
                this.log(`❌ 时间源信息API测试失败: ${response.status}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ 时间源信息API请求失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试信号输出API
    async testSignalOutputAPI() {
        this.log('测试信号输出API...', 'info');
        try {
            const response = await this.makeRequest(`${this.config.apiServer}/api/v1/signal/output`, {
                headers: { Authorization: `Bearer ${this.authToken}` }
            });

            if (response.status === 200 && response.data.success) {
                this.log('✅ 信号输出API测试成功', 'success');
                this.log(`   总输出通道: ${response.data.data.totalOutputs}`, 'info');
                this.log(`   活跃通道: ${response.data.data.activeOutputs}`, 'info');
                return true;
            } else {
                this.log(`❌ 信号输出API测试失败: ${response.status}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ 信号输出API请求失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试WebSocket连接
    async testWebSocketConnection() {
        this.log('测试WebSocket连接...', 'info');
        return new Promise((resolve) => {
            try {
                const ws = new WebSocket(this.config.wsServer);
                let messageReceived = false;
                
                const timeout = setTimeout(() => {
                    if (!messageReceived) {
                        this.log('❌ WebSocket连接超时', 'error');
                        ws.close();
                        resolve(false);
                    }
                }, 10000);

                ws.on('open', () => {
                    this.log('✅ WebSocket连接建立成功', 'success');
                    ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
                });

                ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        messageReceived = true;
                        this.log('✅ WebSocket消息接收成功', 'success');
                        this.log(`   消息类型: ${message.type}`, 'info');
                        if (message.system_status) {
                            this.log(`   系统状态: ${message.system_status}`, 'info');
                        }
                        clearTimeout(timeout);
                        ws.close();
                        resolve(true);
                    } catch (e) {
                        this.log(`⚠️  WebSocket消息解析失败: ${e.message}`, 'warn');
                    }
                });

                ws.on('error', (error) => {
                    this.log(`❌ WebSocket连接错误: ${error.message}`, 'error');
                    clearTimeout(timeout);
                    resolve(false);
                });

                ws.on('close', () => {
                    this.log('WebSocket连接已关闭', 'info');
                });

            } catch (error) {
                this.log(`❌ WebSocket测试失败: ${error.message}`, 'error');
                resolve(false);
            }
        });
    }

    // 测试前端页面访问
    async testFrontendAccess() {
        this.log('测试前端页面访问...', 'info');
        try {
            const response = await this.makeRequest(this.config.frontend);
            if (response.status === 200) {
                this.log('✅ 前端页面访问成功', 'success');
                return true;
            } else {
                this.log(`❌ 前端页面访问失败: ${response.status}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ 前端页面请求失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 运行所有测试
    async runAllTests() {
        this.log('=== 开始运行高精度授时服务器系统测试套件 ===', 'info');
        this.log('', 'info');

        const tests = [
            { name: 'API服务器连接', test: () => this.testApiServerConnection() },
            { name: '用户认证', test: () => this.testAuthentication() },
            { name: '系统状态API', test: () => this.testSystemStatusAPI() },
            { name: '时间源信息API', test: () => this.testTimeSourceAPI() },
            { name: '信号输出API', test: () => this.testSignalOutputAPI() },
            { name: 'WebSocket连接', test: () => this.testWebSocketConnection() },
            { name: '前端页面访问', test: () => this.testFrontendAccess() }
        ];

        let passedTests = 0;
        let totalTests = tests.length;

        for (const { name, test } of tests) {
            this.log(`\n--- 测试: ${name} ---`, 'info');
            try {
                const result = await test();
                if (result) {
                    passedTests++;
                }
            } catch (error) {
                this.log(`❌ 测试执行异常: ${error.message}`, 'error');
            }
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
        }

        this.log('\n=== 测试结果汇总 ===', 'info');
        this.log(`总测试数: ${totalTests}`, 'info');
        this.log(`通过测试: ${passedTests}`, passedTests === totalTests ? 'success' : 'warn');
        this.log(`失败测试: ${totalTests - passedTests}`, totalTests - passedTests === 0 ? 'success' : 'error');
        this.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 
                 passedTests === totalTests ? 'success' : 'warn');

        // 保存测试报告
        await this.saveTestReport();

        return passedTests === totalTests;
    }

    // 保存测试报告
    async saveTestReport() {
        const reportPath = path.join(__dirname, 'test-report.json');
        const report = {
            timestamp: new Date().toISOString(),
            results: this.testResults,
            summary: {
                total: this.testResults.filter(r => r.type === 'success' || r.type === 'error').length,
                passed: this.testResults.filter(r => r.type === 'success').length,
                failed: this.testResults.filter(r => r.type === 'error').length
            }
        };

        try {
            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
            this.log(`测试报告已保存: ${reportPath}`, 'info');
        } catch (error) {
            this.log(`保存测试报告失败: ${error.message}`, 'error');
        }
    }
}

// 主函数
async function main() {
    const testSuite = new TimingServerTestSuite();
    
    try {
        const success = await testSuite.runAllTests();
        process.exit(success ? 0 : 1);
    } catch (error) {
        console.error('测试套件执行失败:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = TimingServerTestSuite;
