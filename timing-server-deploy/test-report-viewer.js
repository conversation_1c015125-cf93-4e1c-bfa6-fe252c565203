#!/usr/bin/env node

/**
 * 测试报告查看器
 * 生成HTML格式的测试报告
 */

const fs = require('fs');
const path = require('path');

class TestReportViewer {
    constructor() {
        this.reports = [];
    }

    // 加载测试报告
    loadReports() {
        const reportFiles = [
            'test-report.json',
            'frontend-test-report.json'
        ];

        for (const file of reportFiles) {
            if (fs.existsSync(file)) {
                try {
                    const data = JSON.parse(fs.readFileSync(file, 'utf8'));
                    this.reports.push({
                        name: file.replace('.json', ''),
                        data: data
                    });
                    console.log(`✅ 加载报告: ${file}`);
                } catch (error) {
                    console.log(`❌ 加载报告失败: ${file} - ${error.message}`);
                }
            }
        }
    }

    // 生成HTML报告
    generateHtmlReport() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const reportFile = `test-report-${timestamp}.html`;

        let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度授时服务器系统 - 测试报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; font-weight: 700; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .content { padding: 40px; }
        .summary { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .summary-card { 
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }
        .summary-card:hover { transform: translateY(-5px); }
        .summary-card.success { border-left-color: #28a745; }
        .summary-card.error { border-left-color: #dc3545; }
        .summary-card.info { border-left-color: #007bff; }
        .summary-card h3 { font-size: 1.2rem; margin-bottom: 10px; color: #333; }
        .summary-card .value { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
        .summary-card .label { color: #666; font-size: 0.9rem; }
        .report-section { 
            background: white;
            border-radius: 15px;
            margin-bottom: 30px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .section-header { 
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        .section-header h2 { color: #333; font-size: 1.5rem; }
        .section-content { padding: 30px; }
        .test-item { 
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid;
            background: #f8f9fa;
        }
        .test-item.success { border-left-color: #28a745; background: #d4edda; }
        .test-item.error { border-left-color: #dc3545; background: #f8d7da; }
        .test-item.warn { border-left-color: #ffc107; background: #fff3cd; }
        .test-item.info { border-left-color: #17a2b8; background: #d1ecf1; }
        .test-time { font-size: 0.85rem; color: #666; margin-bottom: 5px; }
        .test-message { font-weight: 500; }
        .stats { 
            display: flex; 
            justify-content: space-around; 
            text-align: center;
            margin: 20px 0;
        }
        .stat { padding: 15px; }
        .stat-value { font-size: 1.8rem; font-weight: bold; }
        .stat-label { color: #666; font-size: 0.9rem; margin-top: 5px; }
        .footer { 
            text-align: center; 
            padding: 30px;
            background: #f8f9fa;
            color: #666;
        }
        @media (max-width: 768px) {
            .container { margin: 10px; border-radius: 15px; }
            .header { padding: 30px 20px; }
            .header h1 { font-size: 2rem; }
            .content { padding: 20px; }
            .summary { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕐 高精度授时服务器系统</h1>
            <p>测试报告 - ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="content">`;

        // 生成总体统计
        let totalTests = 0;
        let totalPassed = 0;
        let totalFailed = 0;

        for (const report of this.reports) {
            if (report.data.summary) {
                totalTests += report.data.summary.total || 0;
                totalPassed += report.data.summary.passed || report.data.summary.success || 0;
                totalFailed += report.data.summary.failed || report.data.summary.errors || 0;
            }
        }

        const successRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0;

        html += `
            <div class="summary">
                <div class="summary-card info">
                    <h3>总测试数</h3>
                    <div class="value">${totalTests}</div>
                    <div class="label">Total Tests</div>
                </div>
                <div class="summary-card success">
                    <h3>通过测试</h3>
                    <div class="value">${totalPassed}</div>
                    <div class="label">Passed Tests</div>
                </div>
                <div class="summary-card error">
                    <h3>失败测试</h3>
                    <div class="value">${totalFailed}</div>
                    <div class="label">Failed Tests</div>
                </div>
                <div class="summary-card ${successRate >= 80 ? 'success' : successRate >= 60 ? 'info' : 'error'}">
                    <h3>成功率</h3>
                    <div class="value">${successRate}%</div>
                    <div class="label">Success Rate</div>
                </div>
            </div>`;

        // 生成各个报告的详细信息
        for (const report of this.reports) {
            const reportName = report.name === 'test-report' ? '后端API测试' : '前端集成测试';
            
            html += `
            <div class="report-section">
                <div class="section-header">
                    <h2>${reportName}</h2>
                </div>
                <div class="section-content">`;

            if (report.data.results && Array.isArray(report.data.results)) {
                for (const result of report.data.results) {
                    const typeClass = result.type === 'success' ? 'success' : 
                                    result.type === 'error' ? 'error' : 
                                    result.type === 'warn' ? 'warn' : 'info';
                    
                    html += `
                    <div class="test-item ${typeClass}">
                        <div class="test-time">${new Date(result.timestamp).toLocaleString('zh-CN')}</div>
                        <div class="test-message">${result.message}</div>
                    </div>`;
                }
            }

            html += `
                </div>
            </div>`;
        }

        html += `
        </div>
        
        <div class="footer">
            <p>报告生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            <p>高精度授时服务器系统 v1.0.0</p>
        </div>
    </div>
</body>
</html>`;

        fs.writeFileSync(reportFile, html);
        console.log(`✅ HTML测试报告已生成: ${reportFile}`);
        return reportFile;
    }

    // 生成控制台报告
    generateConsoleReport() {
        console.log('\n' + '='.repeat(60));
        console.log('           高精度授时服务器系统 - 测试报告');
        console.log('='.repeat(60));

        for (const report of this.reports) {
            const reportName = report.name === 'test-report' ? '后端API测试' : '前端集成测试';
            console.log(`\n📊 ${reportName}`);
            console.log('-'.repeat(40));

            if (report.data.summary) {
                const summary = report.data.summary;
                console.log(`总测试数: ${summary.total || 0}`);
                console.log(`通过测试: ${summary.passed || summary.success || 0}`);
                console.log(`失败测试: ${summary.failed || summary.errors || 0}`);
                
                const total = summary.total || 0;
                const passed = summary.passed || summary.success || 0;
                const rate = total > 0 ? ((passed / total) * 100).toFixed(1) : 0;
                console.log(`成功率: ${rate}%`);
            }

            // 显示最近的几个测试结果
            if (report.data.results && Array.isArray(report.data.results)) {
                console.log('\n最近测试结果:');
                const recentResults = report.data.results.slice(-5);
                for (const result of recentResults) {
                    const icon = result.type === 'success' ? '✅' : 
                               result.type === 'error' ? '❌' : 
                               result.type === 'warn' ? '⚠️' : 'ℹ️';
                    console.log(`  ${icon} ${result.message}`);
                }
            }
        }

        console.log('\n' + '='.repeat(60));
    }

    // 运行报告生成
    run() {
        console.log('🔍 正在加载测试报告...');
        this.loadReports();

        if (this.reports.length === 0) {
            console.log('❌ 未找到测试报告文件');
            return;
        }

        console.log(`✅ 成功加载 ${this.reports.length} 个测试报告`);

        // 生成控制台报告
        this.generateConsoleReport();

        // 生成HTML报告
        const htmlFile = this.generateHtmlReport();
        
        console.log(`\n📄 查看详细报告: open ${htmlFile}`);
    }
}

// 主函数
function main() {
    const viewer = new TestReportViewer();
    viewer.run();
}

if (require.main === module) {
    main();
}

module.exports = TestReportViewer;
