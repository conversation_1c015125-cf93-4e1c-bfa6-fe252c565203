#!/bin/bash

# 高精度授时服务器系统 - 完整测试套件运行脚本
# 包含前端、后端、WebSocket、配置功能的全面测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查测试依赖..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 安装测试依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装测试依赖..."
        npm install ws puppeteer
    fi
    
    log_success "依赖检查完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务运行状态..."
    
    # 检查HTTP API服务器
    if curl -s http://localhost:8080/api/v1/system/status > /dev/null 2>&1; then
        log_success "HTTP API服务器运行正常"
    else
        log_error "HTTP API服务器未运行"
        return 1
    fi
    
    # 检查WebSocket服务器
    if lsof -i :8081 > /dev/null 2>&1; then
        log_success "WebSocket服务器运行正常"
    else
        log_error "WebSocket服务器未运行"
        return 1
    fi
    
    # 检查前端服务器
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务器运行正常"
    else
        log_error "前端服务器未运行"
        return 1
    fi
    
    return 0
}

# 运行后端API测试
run_backend_tests() {
    log_info "开始运行后端API测试..."
    
    if node test-suite.js; then
        log_success "后端API测试通过"
        return 0
    else
        log_error "后端API测试失败"
        return 1
    fi
}

# 运行前端配置测试
run_frontend_tests() {
    log_info "开始运行前端配置测试..."
    
    # 检查是否有显示环境（用于Puppeteer）
    if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
        log_warn "未检测到图形环境，跳过前端UI测试"
        return 0
    fi
    
    if node frontend-config-test.js; then
        log_success "前端配置测试通过"
        return 0
    else
        log_error "前端配置测试失败"
        return 1
    fi
}

# 运行WebSocket连接测试
run_websocket_tests() {
    log_info "开始运行WebSocket连接测试..."
    
    # 创建WebSocket测试脚本
    cat > websocket-test.js << 'EOF'
const WebSocket = require('ws');

async function testWebSocket() {
    return new Promise((resolve) => {
        const ws = new WebSocket('ws://localhost:8081');
        let connected = false;
        
        const timeout = setTimeout(() => {
            if (!connected) {
                console.log('❌ WebSocket连接超时');
                ws.close();
                resolve(false);
            }
        }, 5000);
        
        ws.on('open', () => {
            connected = true;
            console.log('✅ WebSocket连接成功');
            ws.send(JSON.stringify({ type: 'test', timestamp: Date.now() }));
        });
        
        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log('✅ 收到WebSocket消息:', message.type);
                clearTimeout(timeout);
                ws.close();
                resolve(true);
            } catch (e) {
                console.log('⚠️  WebSocket消息解析失败');
            }
        });
        
        ws.on('error', (error) => {
            console.log('❌ WebSocket连接错误:', error.message);
            clearTimeout(timeout);
            resolve(false);
        });
    });
}

testWebSocket().then(success => {
    process.exit(success ? 0 : 1);
});
EOF
    
    if node websocket-test.js; then
        log_success "WebSocket连接测试通过"
        rm -f websocket-test.js
        return 0
    else
        log_error "WebSocket连接测试失败"
        rm -f websocket-test.js
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    log_info "开始运行性能测试..."
    
    # 创建性能测试脚本
    cat > performance-test.js << 'EOF'
const http = require('http');

async function performanceTest() {
    const startTime = Date.now();
    const requests = 10;
    let completed = 0;
    let errors = 0;
    
    console.log(`开始性能测试: ${requests} 个并发请求`);
    
    const promises = Array.from({ length: requests }, (_, i) => {
        return new Promise((resolve) => {
            const req = http.get('http://localhost:8080/api/v1/system/status', (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    completed++;
                    resolve({ success: true, status: res.statusCode });
                });
            });
            
            req.on('error', (error) => {
                errors++;
                resolve({ success: false, error: error.message });
            });
            
            req.setTimeout(5000, () => {
                errors++;
                req.destroy();
                resolve({ success: false, error: 'timeout' });
            });
        });
    });
    
    const results = await Promise.all(promises);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`性能测试结果:`);
    console.log(`  总请求数: ${requests}`);
    console.log(`  成功请求: ${completed}`);
    console.log(`  失败请求: ${errors}`);
    console.log(`  总耗时: ${duration}ms`);
    console.log(`  平均响应时间: ${(duration / requests).toFixed(2)}ms`);
    console.log(`  成功率: ${((completed / requests) * 100).toFixed(1)}%`);
    
    return errors === 0;
}

performanceTest().then(success => {
    process.exit(success ? 0 : 1);
});
EOF
    
    if node performance-test.js; then
        log_success "性能测试通过"
        rm -f performance-test.js
        return 0
    else
        log_error "性能测试失败"
        rm -f performance-test.js
        return 1
    fi
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    local report_file="test-report-$(date +%Y%m%d-%H%M%S).html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度授时服务器系统测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .test-section { margin-bottom: 20px; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warn { border-left-color: #ffc107; }
        .timestamp { color: #666; font-size: 0.9em; }
        pre { background: #f1f1f1; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>高精度授时服务器系统测试报告</h1>
            <p class="timestamp">生成时间: $(date '+%Y-%m-%d %H:%M:%S')</p>
        </div>
        
        <div class="test-section success">
            <h3>✅ 测试概览</h3>
            <p>系统各组件测试已完成，详细结果请查看各个测试报告文件。</p>
        </div>
        
        <div class="test-section">
            <h3>📊 测试覆盖范围</h3>
            <ul>
                <li>HTTP API服务器连接和响应测试</li>
                <li>WebSocket实时通信测试</li>
                <li>用户认证和授权测试</li>
                <li>前端页面加载和交互测试</li>
                <li>配置保存和加载功能测试</li>
                <li>系统性能和并发测试</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>📁 相关文件</h3>
            <ul>
                <li><code>test-report.json</code> - 后端API测试详细报告</li>
                <li><code>frontend-test-report.json</code> - 前端功能测试报告</li>
                <li><code>timing-server-deploy/logs/</code> - 系统运行日志</li>
            </ul>
        </div>
    </div>
</body>
</html>
EOF
    
    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "  高精度授时服务器系统 - 完整测试套件"
    echo "========================================"
    echo ""
    
    local test_passed=0
    local test_total=0
    
    # 检查依赖
    check_dependencies
    
    # 检查服务状态
    if ! check_services; then
        log_error "服务检查失败，请确保所有服务正在运行"
        exit 1
    fi
    
    # 运行各项测试
    tests=(
        "run_backend_tests:后端API测试"
        "run_websocket_tests:WebSocket测试"
        "run_performance_tests:性能测试"
        "run_frontend_tests:前端测试"
    )
    
    for test_info in "${tests[@]}"; do
        IFS=':' read -r test_func test_name <<< "$test_info"
        test_total=$((test_total + 1))
        
        log_info "开始执行: $test_name"
        if $test_func; then
            test_passed=$((test_passed + 1))
            log_success "$test_name 通过"
        else
            log_error "$test_name 失败"
        fi
        echo ""
    done
    
    # 生成报告
    generate_report
    
    # 输出总结
    echo "========================================"
    echo "测试结果汇总:"
    echo "  总测试数: $test_total"
    echo "  通过测试: $test_passed"
    echo "  失败测试: $((test_total - test_passed))"
    echo "  成功率: $(( (test_passed * 100) / test_total ))%"
    echo "========================================"
    
    if [ $test_passed -eq $test_total ]; then
        log_success "所有测试通过！"
        exit 0
    else
        log_error "部分测试失败，请检查日志"
        exit 1
    fi
}

# 运行主函数
main "$@"
