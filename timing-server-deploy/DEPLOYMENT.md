# 高精度授时服务器系统 - 部署手册

## 🎯 部署概述

本文档详细描述了高精度授时服务器系统的完整部署流程，包括环境准备、安装配置、服务启动和验证测试。

## 📋 部署前检查清单

### 硬件要求

- [ ] CPU: 2核心以上，推荐4核心
- [ ] 内存: 最小2GB，推荐4GB以上
- [ ] 存储: 最小1GB可用空间，推荐5GB以上
- [ ] 网络: 稳定的网络连接，支持NTP/PTP协议

### 软件要求

- [ ] 操作系统: macOS 10.15+ 或 Linux (Ubuntu 18.04+, CentOS 7+)
- [ ] Node.js: 版本16.0或更高
- [ ] npm: 版本8.0或更高
- [ ] 编译工具: GCC/Clang, CMake 3.15+

### 网络要求

- [ ] 端口3000: Web前端服务
- [ ] 端口8080: HTTP API服务
- [ ] 端口8081: WebSocket服务
- [ ] 防火墙配置允许上述端口访问

## 🚀 部署步骤

### 第一步: 环境准备

1. **创建部署用户**
   ```bash
   # Linux系统
   sudo useradd -m -s /bin/bash timing-server
   sudo usermod -aG sudo timing-server
   
   # 切换到部署用户
   su - timing-server
   ```

2. **安装依赖软件**
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install -y nodejs npm build-essential cmake git
   
   # CentOS/RHEL
   sudo yum install -y nodejs npm gcc-c++ cmake3 git
   
   # macOS (使用Homebrew)
   brew install node cmake
   ```

3. **验证环境**
   ```bash
   node --version    # 应显示v16.0+
   npm --version     # 应显示8.0+
   cmake --version   # 应显示3.15+
   ```

### 第二步: 获取部署包

1. **复制部署文件**
   ```bash
   # 假设部署包已准备在 ~/timing-server-deploy
   cd ~
   ls -la timing-server-deploy/
   ```

2. **验证文件完整性**
   ```bash
   cd timing-server-deploy
   
   # 检查关键目录
   ls -la bin/ config/ scripts/ frontend/
   
   # 检查可执行文件
   file bin/timing-server bin/simple_http_server
   ```

### 第三步: 运行安装脚本

1. **执行安装**
   ```bash
   cd ~/timing-server-deploy
   ./scripts/install.sh
   ```

2. **安装过程验证**
   - 检查系统要求 ✓
   - 创建目录结构 ✓
   - 设置文件权限 ✓
   - 安装前端依赖 ✓
   - 初始化数据库 ✓
   - 创建快捷命令 ✓

### 第四步: 配置系统

1. **编辑生产配置**
   ```bash
   vim config/production.json
   ```
   
   关键配置项：
   ```json
   {
     "system": {
       "environment": "production",
       "debug_mode": false
     },
     "security": {
       "jwt_secret": "更改为安全的密钥",
       "require_https": true
     },
     "hardware": {
       "use_mock_hal": false  // 生产环境使用真实硬件
     }
   }
   ```

2. **配置环境变量**
   ```bash
   vim config/environment.env
   ```
   
   重要变量：
   ```bash
   TIMING_SERVER_TEST_MODE=0
   JWT_SECRET=your-production-secret-key
   USE_MOCK_HAL=0
   ```

### 第五步: 启动服务

1. **首次启动**
   ```bash
   ./scripts/timing-server-control.sh start
   ```

2. **验证服务状态**
   ```bash
   ./scripts/timing-server-control.sh status
   ```

3. **检查日志**
   ```bash
   tail -f logs/timing-server.log
   tail -f logs/http-api.log
   tail -f logs/websocket.log
   tail -f logs/frontend.log
   ```

### 第六步: 功能验证

1. **Web界面测试**
   ```bash
   # 访问 http://localhost:3000
   curl -I http://localhost:3000
   ```

2. **API接口测试**
   ```bash
   # 测试登录接口
   curl -X POST http://localhost:8080/api/v1/auth/login \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123"}'
   ```

3. **WebSocket连接测试**
   ```bash
   # 使用websocat或类似工具测试
   # websocat ws://localhost:8081
   ```

## 🔧 高级配置

### SSL/TLS配置

1. **生成SSL证书**
   ```bash
   # 自签名证书（仅用于测试）
   openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
   
   # 移动到配置目录
   mv cert.pem key.pem config/
   ```

2. **更新配置**
   ```json
   {
     "security": {
       "require_https": true,
       "ssl_cert_path": "/path/to/cert.pem",
       "ssl_key_path": "/path/to/key.pem"
     }
   }
   ```

### 数据库配置

1. **SQLite配置（默认）**
   ```json
   {
     "database": {
       "type": "sqlite",
       "path": "/path/to/timing_server.db"
     }
   }
   ```

2. **PostgreSQL配置（可选）**
   ```json
   {
     "database": {
       "type": "postgresql",
       "host": "localhost",
       "port": 5432,
       "database": "timing_server",
       "username": "timing_user",
       "password": "secure_password"
     }
   }
   ```

### 负载均衡配置

1. **Nginx配置示例**
   ```nginx
   upstream timing_server_backend {
       server 127.0.0.1:8080;
   }
   
   upstream timing_server_websocket {
       server 127.0.0.1:8081;
   }
   
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://127.0.0.1:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
       
       location /api/ {
           proxy_pass http://timing_server_backend;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
       
       location /ws {
           proxy_pass http://timing_server_websocket;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";
       }
   }
   ```

## 🔄 系统服务配置

### 安装系统服务

```bash
# 安装为系统服务
./scripts/install-service.sh

# 启用开机自启动
# macOS
launchctl load ~/Library/LaunchAgents/com.timing-server.plist

# Linux
sudo systemctl enable timing-server
```

### 服务管理

```bash
# 启动服务
sudo systemctl start timing-server

# 停止服务
sudo systemctl stop timing-server

# 重启服务
sudo systemctl restart timing-server

# 查看状态
sudo systemctl status timing-server

# 查看日志
sudo journalctl -u timing-server -f
```

## 📊 监控配置

### 设置监控

```bash
# 安装监控脚本
./scripts/setup-cron.sh setup

# 手动运行监控
./scripts/monitor.sh

# 生成监控报告
./scripts/monitor.sh --report
```

### 告警配置

1. **邮件告警**
   ```bash
   # 配置SMTP设置
   vim config/environment.env
   
   # 添加邮件配置
   ALERT_EMAIL=<EMAIL>
   SMTP_SERVER=smtp.example.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=password
   ```

2. **Webhook告警**
   ```bash
   # 配置Webhook URL
   ALERT_WEBHOOK=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
   ```

## 🔒 安全加固

### 系统安全

1. **防火墙配置**
   ```bash
   # Ubuntu/Debian
   sudo ufw allow 3000/tcp
   sudo ufw allow 8080/tcp
   sudo ufw allow 8081/tcp
   sudo ufw enable
   
   # CentOS/RHEL
   sudo firewall-cmd --permanent --add-port=3000/tcp
   sudo firewall-cmd --permanent --add-port=8080/tcp
   sudo firewall-cmd --permanent --add-port=8081/tcp
   sudo firewall-cmd --reload
   ```

2. **用户权限**
   ```bash
   # 设置适当的文件权限
   chmod 750 ~/timing-server-deploy/scripts/
   chmod 640 ~/timing-server-deploy/config/*.json
   chmod 600 ~/timing-server-deploy/config/*.env
   ```

### 应用安全

1. **更改默认密码**
   - 首次登录后立即更改admin密码
   - 使用强密码策略

2. **JWT密钥管理**
   ```bash
   # 生成安全的JWT密钥
   openssl rand -base64 32
   
   # 更新配置
   vim config/environment.env
   ```

## 🧪 部署验证

### 功能测试

1. **基础功能测试**
   ```bash
   # 测试脚本
   cat > test_deployment.sh << 'EOF'
   #!/bin/bash
   
   echo "测试Web界面..."
   curl -s -o /dev/null -w "%{http_code}" http://localhost:3000
   
   echo "测试API接口..."
   curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/v1/health
   
   echo "测试WebSocket..."
   # WebSocket测试需要专门工具
   
   echo "测试完成"
   EOF
   
   chmod +x test_deployment.sh
   ./test_deployment.sh
   ```

2. **性能测试**
   ```bash
   # 使用ab进行简单压力测试
   ab -n 1000 -c 10 http://localhost:8080/api/v1/health
   ```

### 监控验证

1. **检查监控指标**
   ```bash
   ./scripts/monitor.sh --report
   cat logs/monitor_report_*.txt
   ```

2. **验证告警机制**
   ```bash
   # 模拟高CPU使用率
   # 检查是否触发告警
   ```

## 📝 部署检查清单

### 部署完成检查

- [ ] 所有服务正常启动
- [ ] Web界面可正常访问
- [ ] API接口响应正常
- [ ] WebSocket连接正常
- [ ] 日志文件正常生成
- [ ] 监控脚本正常运行
- [ ] 系统服务配置正确
- [ ] 安全配置已应用
- [ ] 备份机制已设置
- [ ] 文档已更新

### 上线前检查

- [ ] 性能测试通过
- [ ] 安全扫描通过
- [ ] 备份恢复测试通过
- [ ] 监控告警测试通过
- [ ] 用户培训完成
- [ ] 运维文档准备完成

---

**部署版本**: v1.0.0  
**文档更新**: 2024-07-26  
**部署负责人**: 系统管理员
