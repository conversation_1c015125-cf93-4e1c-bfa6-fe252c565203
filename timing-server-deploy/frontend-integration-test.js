#!/usr/bin/env node

/**
 * 前端集成测试脚本
 * 测试前端与后端API的集成，包括配置保存、加载、验证等功能
 */

const http = require('http');
const WebSocket = require('ws');

class FrontendIntegrationTest {
    constructor() {
        this.testResults = [];
        this.apiBase = 'http://localhost:8080';
        this.configApiBase = 'http://localhost:8082';
        this.wsUrl = 'ws://localhost:8081';
        this.authToken = null;
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            error: '\x1b[31m',
            warn: '\x1b[33m',
            reset: '\x1b[0m'
        };
        
        console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
        this.testResults.push({ timestamp, type, message });
    }

    // HTTP请求工具
    async makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            };

            const req = http.request(requestOptions, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
                    } catch (e) {
                        resolve({ status: res.statusCode, data, headers: res.headers });
                    }
                });
            });

            req.on('error', reject);
            
            if (options.body) {
                req.write(JSON.stringify(options.body));
            }
            
            req.end();
        });
    }

    // 测试用户认证
    async testAuthentication() {
        this.log('测试用户认证...', 'info');
        try {
            const response = await this.makeRequest(`${this.apiBase}/api/v1/auth/login`, {
                method: 'POST',
                body: { username: 'admin', password: 'admin123' }
            });

            if (response.status === 200 && response.data.success) {
                this.authToken = response.data.data.token;
                this.log('✅ 用户认证成功', 'success');
                return true;
            } else {
                this.log(`❌ 用户认证失败: ${response.data?.error?.message || '未知错误'}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ 认证请求失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试GNSS配置功能
    async testGnssConfiguration() {
        this.log('测试GNSS配置功能...', 'info');
        
        try {
            // 1. 获取当前配置
            const getResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/gnss`);
            if (getResponse.status !== 200) {
                this.log('❌ 获取GNSS配置失败', 'error');
                return false;
            }
            
            this.log('✅ 成功获取GNSS配置', 'success');
            
            // 2. 更新配置
            const newConfig = {
                enabled: true,
                port: '/dev/ttyUSB1',
                baudRate: 230400,
                protocol: 'UBX',
                updateRate: 2
            };
            
            const postResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/gnss`, {
                method: 'POST',
                body: newConfig
            });
            
            if (postResponse.status === 200 && postResponse.data.success) {
                this.log('✅ GNSS配置保存成功', 'success');
                this.log(`   端口: ${newConfig.port}`, 'info');
                this.log(`   波特率: ${newConfig.baudRate}`, 'info');
                
                // 3. 测试配置
                const testResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/test`, {
                    method: 'POST',
                    body: { type: 'gnss', config: newConfig }
                });
                
                if (testResponse.status === 200) {
                    const testResult = testResponse.data.data;
                    this.log(`✅ GNSS配置测试完成: ${testResult.message}`, testResult.success ? 'success' : 'warn');
                    if (testResult.details.satellites) {
                        this.log(`   卫星数量: ${testResult.details.satellites}`, 'info');
                    }
                }
                
                return true;
            } else {
                this.log('❌ GNSS配置保存失败', 'error');
                return false;
            }
            
        } catch (error) {
            this.log(`❌ GNSS配置测试失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试PTP配置功能
    async testPtpConfiguration() {
        this.log('测试PTP配置功能...', 'info');
        
        try {
            const newConfig = {
                enabled: true,
                domain: 1,
                priority1: 64,
                priority2: 64,
                interface: 'eth1'
            };
            
            const response = await this.makeRequest(`${this.configApiBase}/api/v1/config/ptp`, {
                method: 'POST',
                body: newConfig
            });
            
            if (response.status === 200 && response.data.success) {
                this.log('✅ PTP配置保存成功', 'success');
                
                // 测试配置
                const testResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/test`, {
                    method: 'POST',
                    body: { type: 'ptp', config: newConfig }
                });
                
                if (testResponse.status === 200) {
                    const testResult = testResponse.data.data;
                    this.log(`✅ PTP配置测试完成: ${testResult.message}`, testResult.success ? 'success' : 'warn');
                }
                
                return true;
            } else {
                this.log('❌ PTP配置保存失败', 'error');
                return false;
            }
            
        } catch (error) {
            this.log(`❌ PTP配置测试失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试NTP配置功能
    async testNtpConfiguration() {
        this.log('测试NTP配置功能...', 'info');
        
        try {
            const newConfig = {
                enabled: true,
                servers: ['ntp1.aliyun.com', 'ntp2.aliyun.com'],
                port: 123,
                stratum: 3
            };
            
            const response = await this.makeRequest(`${this.configApiBase}/api/v1/config/ntp`, {
                method: 'POST',
                body: newConfig
            });
            
            if (response.status === 200 && response.data.success) {
                this.log('✅ NTP配置保存成功', 'success');
                
                // 测试配置
                const testResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/test`, {
                    method: 'POST',
                    body: { type: 'ntp', config: newConfig }
                });
                
                if (testResponse.status === 200) {
                    const testResult = testResponse.data.data;
                    this.log(`✅ NTP配置测试完成: ${testResult.message}`, testResult.success ? 'success' : 'warn');
                }
                
                return true;
            } else {
                this.log('❌ NTP配置保存失败', 'error');
                return false;
            }
            
        } catch (error) {
            this.log(`❌ NTP配置测试失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试TOD配置功能
    async testTodConfiguration() {
        this.log('测试TOD配置功能...', 'info');
        
        try {
            const newConfig = {
                enabled: true,
                format: 'IRIG-B',
                baudRate: 19200,
                outputRate: 10
            };
            
            const response = await this.makeRequest(`${this.configApiBase}/api/v1/config/tod`, {
                method: 'POST',
                body: newConfig
            });
            
            if (response.status === 200 && response.data.success) {
                this.log('✅ TOD配置保存成功', 'success');
                
                // 测试配置
                const testResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/test`, {
                    method: 'POST',
                    body: { type: 'tod', config: newConfig }
                });
                
                if (testResponse.status === 200) {
                    const testResult = testResponse.data.data;
                    this.log(`✅ TOD配置测试完成: ${testResult.message}`, testResult.success ? 'success' : 'warn');
                }
                
                return true;
            } else {
                this.log('❌ TOD配置保存失败', 'error');
                return false;
            }
            
        } catch (error) {
            this.log(`❌ TOD配置测试失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试配置数据完整性
    async testConfigIntegrity() {
        this.log('测试配置数据完整性...', 'info');
        
        try {
            const response = await this.makeRequest(`${this.configApiBase}/api/v1/config/all`);
            
            if (response.status === 200 && response.data.success) {
                const config = response.data.data;
                
                // 检查必要的配置项
                const requiredConfigs = ['gnss', 'ptp', 'ntp', 'tod'];
                let allPresent = true;
                
                for (const configType of requiredConfigs) {
                    if (!config[configType]) {
                        this.log(`❌ 缺少${configType.toUpperCase()}配置`, 'error');
                        allPresent = false;
                    } else {
                        this.log(`✅ ${configType.toUpperCase()}配置完整`, 'success');
                    }
                }
                
                return allPresent;
            } else {
                this.log('❌ 获取配置数据失败', 'error');
                return false;
            }
            
        } catch (error) {
            this.log(`❌ 配置完整性测试失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 测试WebSocket实时更新
    async testWebSocketUpdates() {
        this.log('测试WebSocket实时更新...', 'info');
        
        return new Promise((resolve) => {
            try {
                const ws = new WebSocket(this.wsUrl);
                let updateReceived = false;
                
                const timeout = setTimeout(() => {
                    if (!updateReceived) {
                        this.log('❌ WebSocket更新超时', 'error');
                        ws.close();
                        resolve(false);
                    }
                }, 8000);

                ws.on('open', () => {
                    this.log('✅ WebSocket连接建立', 'success');
                    // 发送配置更新请求
                    ws.send(JSON.stringify({ 
                        type: 'config_update_request',
                        timestamp: Date.now() 
                    }));
                });

                ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        if (message.type === 'status_update' || message.type === 'config_update') {
                            updateReceived = true;
                            this.log('✅ 收到WebSocket更新消息', 'success');
                            this.log(`   消息类型: ${message.type}`, 'info');
                            clearTimeout(timeout);
                            ws.close();
                            resolve(true);
                        }
                    } catch (e) {
                        this.log(`⚠️  WebSocket消息解析失败: ${e.message}`, 'warn');
                    }
                });

                ws.on('error', (error) => {
                    this.log(`❌ WebSocket错误: ${error.message}`, 'error');
                    clearTimeout(timeout);
                    resolve(false);
                });

            } catch (error) {
                this.log(`❌ WebSocket测试失败: ${error.message}`, 'error');
                resolve(false);
            }
        });
    }

    // 运行所有测试
    async runAllTests() {
        this.log('=== 开始前端集成测试 ===', 'info');
        
        const tests = [
            { name: '用户认证', test: () => this.testAuthentication() },
            { name: 'GNSS配置功能', test: () => this.testGnssConfiguration() },
            { name: 'PTP配置功能', test: () => this.testPtpConfiguration() },
            { name: 'NTP配置功能', test: () => this.testNtpConfiguration() },
            { name: 'TOD配置功能', test: () => this.testTodConfiguration() },
            { name: '配置数据完整性', test: () => this.testConfigIntegrity() },
            { name: 'WebSocket实时更新', test: () => this.testWebSocketUpdates() }
        ];

        let passedTests = 0;
        let totalTests = tests.length;

        for (const { name, test } of tests) {
            this.log(`\n--- 测试: ${name} ---`, 'info');
            try {
                const result = await test();
                if (result) {
                    passedTests++;
                }
            } catch (error) {
                this.log(`❌ 测试执行异常: ${error.message}`, 'error');
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        this.log('\n=== 前端集成测试结果 ===', 'info');
        this.log(`总测试数: ${totalTests}`, 'info');
        this.log(`通过测试: ${passedTests}`, passedTests === totalTests ? 'success' : 'warn');
        this.log(`失败测试: ${totalTests - passedTests}`, totalTests - passedTests === 0 ? 'success' : 'error');
        this.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 
                 passedTests === totalTests ? 'success' : 'warn');

        return passedTests === totalTests;
    }
}

// 主函数
async function main() {
    const tester = new FrontendIntegrationTest();
    
    try {
        const success = await tester.runAllTests();
        process.exit(success ? 0 : 1);
    } catch (error) {
        console.error('前端集成测试失败:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = FrontendIntegrationTest;
