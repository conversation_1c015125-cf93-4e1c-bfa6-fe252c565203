#!/usr/bin/env node

/**
 * 前端配置功能测试脚本
 * 测试各个页面的配置保存、加载、验证功能
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class FrontendConfigTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = [];
        this.baseUrl = 'http://localhost:3000';
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            error: '\x1b[31m',
            warn: '\x1b[33m',
            reset: '\x1b[0m'
        };
        
        console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
        this.testResults.push({ timestamp, type, message });
    }

    async init() {
        this.log('初始化浏览器...', 'info');
        this.browser = await puppeteer.launch({ 
            headless: false, // 设置为false以便观察测试过程
            defaultViewport: { width: 1280, height: 720 }
        });
        this.page = await this.browser.newPage();
        
        // 设置页面错误监听
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                this.log(`页面控制台错误: ${msg.text()}`, 'error');
            }
        });
    }

    async login() {
        this.log('执行登录...', 'info');
        await this.page.goto(`${this.baseUrl}/login`);
        await this.page.waitForSelector('input[type="text"]', { timeout: 5000 });
        
        await this.page.type('input[type="text"]', 'admin');
        await this.page.type('input[type="password"]', 'admin123');
        await this.page.click('button[type="submit"]');
        
        // 等待跳转到主页
        await this.page.waitForNavigation({ timeout: 10000 });
        this.log('✅ 登录成功', 'success');
    }

    async testGnssConfiguration() {
        this.log('测试GNSS配置页面...', 'info');
        
        try {
            await this.page.goto(`${this.baseUrl}/gnss`);
            await this.page.waitForSelector('.gnss-config', { timeout: 5000 });
            
            // 测试配置修改
            const testConfig = {
                enabled: true,
                port: '/dev/ttyUSB0',
                baudRate: 115200,
                protocol: 'NMEA',
                updateRate: 1
            };
            
            // 填写配置
            if (await this.page.$('input[name="port"]')) {
                await this.page.evaluate((config) => {
                    const portInput = document.querySelector('input[name="port"]');
                    if (portInput) portInput.value = config.port;
                    
                    const baudSelect = document.querySelector('select[name="baudRate"]');
                    if (baudSelect) baudSelect.value = config.baudRate;
                    
                    const protocolSelect = document.querySelector('select[name="protocol"]');
                    if (protocolSelect) protocolSelect.value = config.protocol;
                    
                    const updateInput = document.querySelector('input[name="updateRate"]');
                    if (updateInput) updateInput.value = config.updateRate;
                }, testConfig);
                
                // 点击保存按钮
                const saveButton = await this.page.$('.save-btn');
                if (saveButton) {
                    await saveButton.click();
                    await this.page.waitForTimeout(1000);
                    this.log('✅ GNSS配置保存测试成功', 'success');
                } else {
                    this.log('⚠️  未找到GNSS保存按钮', 'warn');
                }
            } else {
                this.log('⚠️  GNSS配置表单未完全加载', 'warn');
            }
            
        } catch (error) {
            this.log(`❌ GNSS配置测试失败: ${error.message}`, 'error');
        }
    }

    async testPtpConfiguration() {
        this.log('测试PTP配置页面...', 'info');
        
        try {
            await this.page.goto(`${this.baseUrl}/ptp`);
            await this.page.waitForSelector('.ptp-config', { timeout: 5000 });
            
            // 测试PTP配置
            const testConfig = {
                enabled: true,
                domain: 0,
                priority1: 128,
                priority2: 128,
                interface: 'eth0'
            };
            
            await this.page.evaluate((config) => {
                const domainInput = document.querySelector('input[name="domain"]');
                if (domainInput) domainInput.value = config.domain;
                
                const priority1Input = document.querySelector('input[name="priority1"]');
                if (priority1Input) priority1Input.value = config.priority1;
                
                const priority2Input = document.querySelector('input[name="priority2"]');
                if (priority2Input) priority2Input.value = config.priority2;
                
                const interfaceSelect = document.querySelector('select[name="interface"]');
                if (interfaceSelect) interfaceSelect.value = config.interface;
            }, testConfig);
            
            const saveButton = await this.page.$('.save-btn');
            if (saveButton) {
                await saveButton.click();
                await this.page.waitForTimeout(1000);
                this.log('✅ PTP配置保存测试成功', 'success');
            } else {
                this.log('⚠️  未找到PTP保存按钮', 'warn');
            }
            
        } catch (error) {
            this.log(`❌ PTP配置测试失败: ${error.message}`, 'error');
        }
    }

    async testNtpConfiguration() {
        this.log('测试NTP配置页面...', 'info');
        
        try {
            await this.page.goto(`${this.baseUrl}/ntp`);
            await this.page.waitForSelector('.ntp-config', { timeout: 5000 });
            
            // 测试NTP配置
            const testConfig = {
                enabled: true,
                servers: ['pool.ntp.org', 'time.google.com'],
                port: 123,
                stratum: 2
            };
            
            await this.page.evaluate((config) => {
                const portInput = document.querySelector('input[name="port"]');
                if (portInput) portInput.value = config.port;
                
                const stratumInput = document.querySelector('input[name="stratum"]');
                if (stratumInput) stratumInput.value = config.stratum;
            }, testConfig);
            
            const saveButton = await this.page.$('.save-btn');
            if (saveButton) {
                await saveButton.click();
                await this.page.waitForTimeout(1000);
                this.log('✅ NTP配置保存测试成功', 'success');
            } else {
                this.log('⚠️  未找到NTP保存按钮', 'warn');
            }
            
        } catch (error) {
            this.log(`❌ NTP配置测试失败: ${error.message}`, 'error');
        }
    }

    async testTodConfiguration() {
        this.log('测试TOD配置页面...', 'info');
        
        try {
            await this.page.goto(`${this.baseUrl}/tod`);
            await this.page.waitForSelector('.tod-config', { timeout: 5000 });
            
            // 测试TOD配置
            const testConfig = {
                enabled: true,
                format: 'ASCII',
                baudRate: 9600,
                outputRate: 1
            };
            
            await this.page.evaluate((config) => {
                const formatSelect = document.querySelector('select[name="format"]');
                if (formatSelect) formatSelect.value = config.format;
                
                const baudSelect = document.querySelector('select[name="baudRate"]');
                if (baudSelect) baudSelect.value = config.baudRate;
                
                const rateInput = document.querySelector('input[name="outputRate"]');
                if (rateInput) rateInput.value = config.outputRate;
            }, testConfig);
            
            const saveButton = await this.page.$('.save-btn');
            if (saveButton) {
                await saveButton.click();
                await this.page.waitForTimeout(1000);
                this.log('✅ TOD配置保存测试成功', 'success');
            } else {
                this.log('⚠️  未找到TOD保存按钮', 'warn');
            }
            
        } catch (error) {
            this.log(`❌ TOD配置测试失败: ${error.message}`, 'error');
        }
    }

    async testNavigationAndUI() {
        this.log('测试导航和UI交互...', 'info');
        
        try {
            // 测试所有导航项
            const navItems = [
                { path: '/', name: '系统仪表盘' },
                { path: '/gnss', name: 'GNSS接收' },
                { path: '/ptp', name: 'PTP服务' },
                { path: '/ntp', name: 'NTP服务' },
                { path: '/tod', name: 'TOD设置' },
                { path: '/performance', name: '信号输出状态' },
                { path: '/config', name: '系统资源' },
                { path: '/logs', name: '系统日志' }
            ];
            
            for (const item of navItems) {
                await this.page.goto(`${this.baseUrl}${item.path}`);
                await this.page.waitForTimeout(1000);
                
                // 检查页面标题
                const title = await this.page.$eval('h1, h2', el => el.textContent).catch(() => '');
                if (title) {
                    this.log(`✅ ${item.name} 页面加载成功`, 'success');
                } else {
                    this.log(`⚠️  ${item.name} 页面标题未找到`, 'warn');
                }
            }
            
        } catch (error) {
            this.log(`❌ 导航测试失败: ${error.message}`, 'error');
        }
    }

    async testWebSocketConnection() {
        this.log('测试前端WebSocket连接...', 'info');
        
        try {
            await this.page.goto(`${this.baseUrl}/`);
            await this.page.waitForTimeout(3000);
            
            // 检查WebSocket连接状态
            const wsStatus = await this.page.evaluate(() => {
                // 检查系统store中的连接状态
                return window.__VUE_DEVTOOLS_GLOBAL_HOOK__ ? 'DevTools可用' : '正常运行';
            });
            
            this.log(`✅ WebSocket连接状态检查完成: ${wsStatus}`, 'success');
            
        } catch (error) {
            this.log(`❌ WebSocket连接测试失败: ${error.message}`, 'error');
        }
    }

    async runAllTests() {
        this.log('=== 开始前端配置功能测试 ===', 'info');
        
        try {
            await this.init();
            await this.login();
            
            const tests = [
                () => this.testNavigationAndUI(),
                () => this.testGnssConfiguration(),
                () => this.testPtpConfiguration(),
                () => this.testNtpConfiguration(),
                () => this.testTodConfiguration(),
                () => this.testWebSocketConnection()
            ];
            
            for (const test of tests) {
                await test();
                await this.page.waitForTimeout(2000);
            }
            
            this.log('=== 前端测试完成 ===', 'success');
            
        } catch (error) {
            this.log(`❌ 测试执行失败: ${error.message}`, 'error');
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
        
        // 保存测试报告
        await this.saveTestReport();
    }

    async saveTestReport() {
        const reportPath = path.join(__dirname, 'frontend-test-report.json');
        const report = {
            timestamp: new Date().toISOString(),
            results: this.testResults,
            summary: {
                total: this.testResults.length,
                success: this.testResults.filter(r => r.type === 'success').length,
                errors: this.testResults.filter(r => r.type === 'error').length,
                warnings: this.testResults.filter(r => r.type === 'warn').length
            }
        };

        try {
            fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
            this.log(`前端测试报告已保存: ${reportPath}`, 'info');
        } catch (error) {
            this.log(`保存测试报告失败: ${error.message}`, 'error');
        }
    }
}

// 主函数
async function main() {
    const tester = new FrontendConfigTest();
    await tester.runAllTests();
}

if (require.main === module) {
    main();
}

module.exports = FrontendConfigTest;
