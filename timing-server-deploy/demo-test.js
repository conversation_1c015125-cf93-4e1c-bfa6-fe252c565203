#!/usr/bin/env node

/**
 * 高精度授时服务器系统 - 完整功能演示测试
 * 展示前端配置修改、保存、API交互等所有功能
 */

const http = require('http');
const WebSocket = require('ws');

class SystemDemo {
    constructor() {
        this.apiBase = 'http://localhost:8080';
        this.configApiBase = 'http://localhost:8082';
        this.wsUrl = 'ws://localhost:8081';
        this.authToken = null;
    }

    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            error: '\x1b[31m',
            warn: '\x1b[33m',
            header: '\x1b[35m',
            reset: '\x1b[0m'
        };
        
        console.log(`${colors[type]}${message}${colors.reset}`);
    }

    // HTTP请求工具
    async makeRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const requestOptions = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            };

            const req = http.request(requestOptions, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({ status: res.statusCode, data: jsonData });
                    } catch (e) {
                        resolve({ status: res.statusCode, data });
                    }
                });
            });

            req.on('error', reject);
            
            if (options.body) {
                req.write(JSON.stringify(options.body));
            }
            
            req.end();
        });
    }

    // 演示用户登录
    async demoLogin() {
        this.log('\n🔐 演示用户登录功能', 'header');
        this.log('正在使用默认管理员账户登录...', 'info');
        
        try {
            const response = await this.makeRequest(`${this.apiBase}/api/v1/auth/login`, {
                method: 'POST',
                body: { username: 'admin', password: 'admin123' }
            });

            if (response.status === 200 && response.data.success) {
                this.authToken = response.data.data.token;
                this.log('✅ 登录成功！', 'success');
                this.log(`   用户名: admin`, 'info');
                this.log(`   Token: ${this.authToken.substring(0, 20)}...`, 'info');
                return true;
            } else {
                this.log('❌ 登录失败', 'error');
                return false;
            }
        } catch (error) {
            this.log(`❌ 登录请求失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 演示GNSS配置修改
    async demoGnssConfig() {
        this.log('\n🛰️ 演示GNSS接收器配置修改', 'header');
        
        try {
            // 1. 获取当前配置
            this.log('1. 获取当前GNSS配置...', 'info');
            const getResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/gnss`);
            
            if (getResponse.status === 200) {
                this.log('✅ 成功获取当前配置', 'success');
                const currentConfig = getResponse.data.data;
                this.log(`   当前端口: ${currentConfig.port}`, 'info');
                this.log(`   当前波特率: ${currentConfig.baudRate}`, 'info');
                this.log(`   当前协议: ${currentConfig.protocol}`, 'info');
            }
            
            // 2. 修改配置
            this.log('\n2. 修改GNSS配置...', 'info');
            const newConfig = {
                enabled: true,
                port: '/dev/ttyUSB2',
                baudRate: 460800,
                protocol: 'UBX',
                updateRate: 5,
                satellites: {
                    gps: true,
                    glonass: true,
                    galileo: true,
                    beidou: true
                }
            };
            
            const postResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/gnss`, {
                method: 'POST',
                body: newConfig
            });
            
            if (postResponse.status === 200) {
                this.log('✅ GNSS配置保存成功！', 'success');
                this.log(`   新端口: ${newConfig.port}`, 'info');
                this.log(`   新波特率: ${newConfig.baudRate}`, 'info');
                this.log(`   新协议: ${newConfig.protocol}`, 'info');
                this.log(`   更新频率: ${newConfig.updateRate}Hz`, 'info');
            }
            
            // 3. 测试配置
            this.log('\n3. 测试GNSS配置...', 'info');
            const testResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/test`, {
                method: 'POST',
                body: { type: 'gnss', config: newConfig }
            });
            
            if (testResponse.status === 200) {
                const testResult = testResponse.data.data;
                if (testResult.success) {
                    this.log('✅ GNSS配置测试成功！', 'success');
                    this.log(`   卫星数量: ${testResult.details.satellites}`, 'info');
                    this.log(`   信号强度: ${testResult.details.signalStrength}%`, 'info');
                } else {
                    this.log('⚠️ GNSS配置测试失败', 'warn');
                    this.log(`   原因: ${testResult.message}`, 'warn');
                }
            }
            
        } catch (error) {
            this.log(`❌ GNSS配置演示失败: ${error.message}`, 'error');
        }
    }

    // 演示PTP配置修改
    async demoPtpConfig() {
        this.log('\n⏱️ 演示PTP服务配置修改', 'header');
        
        try {
            const newConfig = {
                enabled: true,
                domain: 2,
                priority1: 64,
                priority2: 64,
                interface: 'eth0',
                announceInterval: 2,
                syncInterval: 1,
                delayReqInterval: 1
            };
            
            this.log('正在保存PTP配置...', 'info');
            const response = await this.makeRequest(`${this.configApiBase}/api/v1/config/ptp`, {
                method: 'POST',
                body: newConfig
            });
            
            if (response.status === 200) {
                this.log('✅ PTP配置保存成功！', 'success');
                this.log(`   PTP域: ${newConfig.domain}`, 'info');
                this.log(`   优先级1: ${newConfig.priority1}`, 'info');
                this.log(`   网络接口: ${newConfig.interface}`, 'info');
                
                // 测试PTP配置
                this.log('\n正在测试PTP配置...', 'info');
                const testResponse = await this.makeRequest(`${this.configApiBase}/api/v1/config/test`, {
                    method: 'POST',
                    body: { type: 'ptp', config: newConfig }
                });
                
                if (testResponse.status === 200) {
                    const testResult = testResponse.data.data;
                    if (testResult.success) {
                        this.log('✅ PTP配置测试成功！', 'success');
                        this.log(`   主时钟: ${testResult.details.masterClock}`, 'info');
                        this.log(`   时间偏差: ${testResult.details.offset}`, 'info');
                    } else {
                        this.log('⚠️ PTP配置测试失败', 'warn');
                    }
                }
            }
            
        } catch (error) {
            this.log(`❌ PTP配置演示失败: ${error.message}`, 'error');
        }
    }

    // 演示WebSocket实时通信
    async demoWebSocket() {
        this.log('\n🔄 演示WebSocket实时通信', 'header');
        
        return new Promise((resolve) => {
            try {
                const ws = new WebSocket(this.wsUrl);
                let messageCount = 0;
                
                const timeout = setTimeout(() => {
                    this.log('⏰ WebSocket演示完成', 'info');
                    ws.close();
                    resolve();
                }, 10000);

                ws.on('open', () => {
                    this.log('✅ WebSocket连接建立成功', 'success');
                    this.log('正在监听实时数据...', 'info');
                    
                    // 发送测试消息
                    ws.send(JSON.stringify({ 
                        type: 'demo_request',
                        message: '演示客户端连接',
                        timestamp: Date.now() 
                    }));
                });

                ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);
                        messageCount++;
                        
                        if (messageCount <= 3) { // 只显示前3条消息
                            this.log(`📨 收到消息 #${messageCount}:`, 'info');
                            this.log(`   类型: ${message.type}`, 'info');
                            if (message.system_status) {
                                this.log(`   系统状态: ${message.system_status}`, 'info');
                            }
                            if (message.current_source) {
                                this.log(`   时间源: ${message.current_source}`, 'info');
                            }
                            if (message.precision) {
                                this.log(`   精度: ${message.precision}`, 'info');
                            }
                        }
                        
                        if (messageCount >= 3) {
                            clearTimeout(timeout);
                            this.log('✅ WebSocket实时通信演示完成', 'success');
                            ws.close();
                            resolve();
                        }
                    } catch (e) {
                        this.log(`⚠️ 消息解析失败: ${e.message}`, 'warn');
                    }
                });

                ws.on('error', (error) => {
                    this.log(`❌ WebSocket错误: ${error.message}`, 'error');
                    clearTimeout(timeout);
                    resolve();
                });

            } catch (error) {
                this.log(`❌ WebSocket演示失败: ${error.message}`, 'error');
                resolve();
            }
        });
    }

    // 演示系统状态查询
    async demoSystemStatus() {
        this.log('\n📊 演示系统状态查询', 'header');
        
        try {
            const response = await this.makeRequest(`${this.apiBase}/api/v1/system/status`, {
                headers: { Authorization: `Bearer ${this.authToken}` }
            });

            if (response.status === 200 && response.data.success) {
                this.log('✅ 系统状态查询成功', 'success');
                const status = response.data.data;
                this.log(`   系统状态: ${status.status}`, 'info');
                this.log(`   运行时间: ${status.uptime}`, 'info');
                this.log(`   CPU使用率: ${status.cpu_usage}%`, 'info');
                this.log(`   内存使用率: ${status.memory_usage}%`, 'info');
            } else {
                this.log('❌ 系统状态查询失败', 'error');
            }
        } catch (error) {
            this.log(`❌ 系统状态查询失败: ${error.message}`, 'error');
        }
    }

    // 运行完整演示
    async runDemo() {
        this.log('🎯 高精度授时服务器系统 - 功能演示开始', 'header');
        this.log('=' .repeat(60), 'header');
        
        // 1. 用户登录
        const loginSuccess = await this.demoLogin();
        if (!loginSuccess) {
            this.log('❌ 登录失败，演示终止', 'error');
            return;
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 2. 系统状态查询
        await this.demoSystemStatus();
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 3. GNSS配置演示
        await this.demoGnssConfig();
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 4. PTP配置演示
        await this.demoPtpConfig();
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 5. WebSocket实时通信演示
        await this.demoWebSocket();
        
        this.log('\n🎉 功能演示完成！', 'header');
        this.log('=' .repeat(60), 'header');
        this.log('\n演示内容包括:', 'info');
        this.log('✅ 用户认证和登录', 'success');
        this.log('✅ 系统状态查询', 'success');
        this.log('✅ GNSS接收器配置修改和测试', 'success');
        this.log('✅ PTP服务配置修改和测试', 'success');
        this.log('✅ WebSocket实时数据通信', 'success');
        this.log('\n前端页面地址: http://localhost:3000', 'info');
        this.log('API文档地址: http://localhost:8080/api/v1', 'info');
        this.log('配置API地址: http://localhost:8082/api/v1/config', 'info');
    }
}

// 主函数
async function main() {
    const demo = new SystemDemo();
    await demo.runDemo();
}

if (require.main === module) {
    main();
}

module.exports = SystemDemo;
