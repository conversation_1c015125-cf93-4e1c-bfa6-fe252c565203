#!/usr/bin/env node

/**
 * 配置测试API服务器
 * 为前端配置功能提供模拟API接口
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

class ConfigTestServer {
    constructor(port = 8082) {
        this.port = port;
        this.configData = {
            gnss: {
                enabled: true,
                port: '/dev/ttyUSB0',
                baudRate: 115200,
                protocol: 'NMEA',
                updateRate: 1,
                satellites: {
                    gps: true,
                    glonass: true,
                    galileo: true,
                    beidou: true
                }
            },
            ptp: {
                enabled: true,
                domain: 0,
                priority1: 128,
                priority2: 128,
                interface: 'eth0',
                announceInterval: 1,
                syncInterval: 0,
                delayReqInterval: 0
            },
            ntp: {
                enabled: true,
                servers: [
                    'pool.ntp.org',
                    'time.google.com',
                    'time.cloudflare.com'
                ],
                port: 123,
                stratum: 2,
                pollInterval: 6
            },
            tod: {
                enabled: true,
                format: 'ASCII',
                baudRate: 9600,
                outputRate: 1,
                timeZone: 'UTC',
                daylightSaving: false
            }
        };
        
        this.server = null;
    }

    // 处理CORS
    setCorsHeaders(res) {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    }

    // 解析请求体
    parseRequestBody(req) {
        return new Promise((resolve, reject) => {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });
            req.on('end', () => {
                try {
                    resolve(body ? JSON.parse(body) : {});
                } catch (error) {
                    reject(error);
                }
            });
        });
    }

    // 发送JSON响应
    sendJsonResponse(res, statusCode, data) {
        res.writeHead(statusCode, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(data));
    }

    // 处理GNSS配置
    async handleGnssConfig(req, res, method) {
        if (method === 'GET') {
            this.sendJsonResponse(res, 200, {
                success: true,
                data: this.configData.gnss
            });
        } else if (method === 'POST') {
            try {
                const newConfig = await this.parseRequestBody(req);
                this.configData.gnss = { ...this.configData.gnss, ...newConfig };
                
                // 模拟保存延迟
                await new Promise(resolve => setTimeout(resolve, 500));
                
                this.sendJsonResponse(res, 200, {
                    success: true,
                    message: 'GNSS配置保存成功',
                    data: this.configData.gnss
                });
                
                console.log('GNSS配置已更新:', newConfig);
            } catch (error) {
                this.sendJsonResponse(res, 400, {
                    success: false,
                    error: '配置数据格式错误'
                });
            }
        }
    }

    // 处理PTP配置
    async handlePtpConfig(req, res, method) {
        if (method === 'GET') {
            this.sendJsonResponse(res, 200, {
                success: true,
                data: this.configData.ptp
            });
        } else if (method === 'POST') {
            try {
                const newConfig = await this.parseRequestBody(req);
                this.configData.ptp = { ...this.configData.ptp, ...newConfig };
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                this.sendJsonResponse(res, 200, {
                    success: true,
                    message: 'PTP配置保存成功',
                    data: this.configData.ptp
                });
                
                console.log('PTP配置已更新:', newConfig);
            } catch (error) {
                this.sendJsonResponse(res, 400, {
                    success: false,
                    error: '配置数据格式错误'
                });
            }
        }
    }

    // 处理NTP配置
    async handleNtpConfig(req, res, method) {
        if (method === 'GET') {
            this.sendJsonResponse(res, 200, {
                success: true,
                data: this.configData.ntp
            });
        } else if (method === 'POST') {
            try {
                const newConfig = await this.parseRequestBody(req);
                this.configData.ntp = { ...this.configData.ntp, ...newConfig };
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                this.sendJsonResponse(res, 200, {
                    success: true,
                    message: 'NTP配置保存成功',
                    data: this.configData.ntp
                });
                
                console.log('NTP配置已更新:', newConfig);
            } catch (error) {
                this.sendJsonResponse(res, 400, {
                    success: false,
                    error: '配置数据格式错误'
                });
            }
        }
    }

    // 处理TOD配置
    async handleTodConfig(req, res, method) {
        if (method === 'GET') {
            this.sendJsonResponse(res, 200, {
                success: true,
                data: this.configData.tod
            });
        } else if (method === 'POST') {
            try {
                const newConfig = await this.parseRequestBody(req);
                this.configData.tod = { ...this.configData.tod, ...newConfig };
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                this.sendJsonResponse(res, 200, {
                    success: true,
                    message: 'TOD配置保存成功',
                    data: this.configData.tod
                });
                
                console.log('TOD配置已更新:', newConfig);
            } catch (error) {
                this.sendJsonResponse(res, 400, {
                    success: false,
                    error: '配置数据格式错误'
                });
            }
        }
    }

    // 处理配置测试
    async handleConfigTest(req, res) {
        try {
            const { type, config } = await this.parseRequestBody(req);
            
            // 模拟测试延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 模拟测试结果
            const testResult = {
                success: Math.random() > 0.2, // 80% 成功率
                message: '',
                details: {}
            };
            
            switch (type) {
                case 'gnss':
                    testResult.message = testResult.success ? 'GNSS接收器连接成功' : 'GNSS接收器连接失败';
                    testResult.details = {
                        satellites: testResult.success ? 12 : 0,
                        signalStrength: testResult.success ? 85 : 0
                    };
                    break;
                case 'ptp':
                    testResult.message = testResult.success ? 'PTP服务测试成功' : 'PTP服务测试失败';
                    testResult.details = {
                        masterClock: testResult.success ? '192.168.1.100' : null,
                        offset: testResult.success ? '±10ns' : null
                    };
                    break;
                case 'ntp':
                    testResult.message = testResult.success ? 'NTP服务器连接成功' : 'NTP服务器连接失败';
                    testResult.details = {
                        servers: testResult.success ? 3 : 0,
                        stratum: testResult.success ? 2 : null
                    };
                    break;
                case 'tod':
                    testResult.message = testResult.success ? 'TOD输出测试成功' : 'TOD输出测试失败';
                    testResult.details = {
                        outputFormat: testResult.success ? config.format : null,
                        baudRate: testResult.success ? config.baudRate : null
                    };
                    break;
            }
            
            this.sendJsonResponse(res, 200, {
                success: true,
                data: testResult
            });
            
            console.log(`${type.toUpperCase()}配置测试:`, testResult.success ? '成功' : '失败');
            
        } catch (error) {
            this.sendJsonResponse(res, 400, {
                success: false,
                error: '测试请求格式错误'
            });
        }
    }

    // 请求处理器
    async handleRequest(req, res) {
        const url = new URL(req.url, `http://localhost:${this.port}`);
        const method = req.method;
        
        // 设置CORS头
        this.setCorsHeaders(res);
        
        // 处理OPTIONS请求
        if (method === 'OPTIONS') {
            res.writeHead(200);
            res.end();
            return;
        }
        
        console.log(`${method} ${url.pathname}`);
        
        try {
            // 路由处理
            if (url.pathname === '/api/v1/config/gnss') {
                await this.handleGnssConfig(req, res, method);
            } else if (url.pathname === '/api/v1/config/ptp') {
                await this.handlePtpConfig(req, res, method);
            } else if (url.pathname === '/api/v1/config/ntp') {
                await this.handleNtpConfig(req, res, method);
            } else if (url.pathname === '/api/v1/config/tod') {
                await this.handleTodConfig(req, res, method);
            } else if (url.pathname === '/api/v1/config/test') {
                await this.handleConfigTest(req, res);
            } else if (url.pathname === '/api/v1/config/all') {
                this.sendJsonResponse(res, 200, {
                    success: true,
                    data: this.configData
                });
            } else {
                this.sendJsonResponse(res, 404, {
                    success: false,
                    error: '接口不存在'
                });
            }
        } catch (error) {
            console.error('请求处理错误:', error);
            this.sendJsonResponse(res, 500, {
                success: false,
                error: '服务器内部错误'
            });
        }
    }

    // 启动服务器
    start() {
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });
        
        this.server.listen(this.port, () => {
            console.log(`配置测试API服务器已启动: http://localhost:${this.port}`);
            console.log('可用接口:');
            console.log('  GET/POST /api/v1/config/gnss - GNSS配置');
            console.log('  GET/POST /api/v1/config/ptp  - PTP配置');
            console.log('  GET/POST /api/v1/config/ntp  - NTP配置');
            console.log('  GET/POST /api/v1/config/tod  - TOD配置');
            console.log('  POST     /api/v1/config/test - 配置测试');
            console.log('  GET      /api/v1/config/all  - 所有配置');
        });
        
        return this.server;
    }

    // 停止服务器
    stop() {
        if (this.server) {
            this.server.close();
            console.log('配置测试API服务器已停止');
        }
    }
}

// 主函数
function main() {
    const server = new ConfigTestServer(8082);
    server.start();
    
    // 优雅关闭
    process.on('SIGINT', () => {
        console.log('\n收到停止信号，正在关闭服务器...');
        server.stop();
        process.exit(0);
    });
}

if (require.main === module) {
    main();
}

module.exports = ConfigTestServer;
