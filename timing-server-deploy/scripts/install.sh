#!/bin/bash

# 高精度授时服务器系统安装脚本
# Timing Server Installation Script v1.0.0

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(dirname "$SCRIPT_DIR")"
INSTALL_USER=$(whoami)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "darwin"* ]]; then
        log_info "检测到 macOS 系统"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        log_info "检测到 Linux 系统"
    else
        log_warn "未知操作系统: $OSTYPE"
    fi
    
    # 检查Node.js
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version)
        log_info "Node.js 版本: $NODE_VERSION"
    else
        log_error "未找到 Node.js，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if command -v npm >/dev/null 2>&1; then
        NPM_VERSION=$(npm --version)
        log_info "npm 版本: $NPM_VERSION"
    else
        log_error "未找到 npm，请先安装 npm"
        exit 1
    fi
    
    # 检查磁盘空间
    AVAILABLE_SPACE=$(df -h "$DEPLOY_DIR" | awk 'NR==2 {print $4}')
    log_info "可用磁盘空间: $AVAILABLE_SPACE"
}

# 创建必要的目录
create_directories() {
    log_info "创建系统目录..."
    
    mkdir -p "$DEPLOY_DIR"/{bin,config,logs,data,scripts,frontend,backup}
    mkdir -p "$DEPLOY_DIR/data"/{pids,backups,cache}
    mkdir -p "$DEPLOY_DIR/logs"/{archive}
    
    log_info "目录结构创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 设置脚本执行权限
    chmod +x "$DEPLOY_DIR/scripts"/*.sh
    chmod +x "$DEPLOY_DIR/bin"/*
    
    # 设置目录权限
    chmod 755 "$DEPLOY_DIR"/{bin,config,scripts}
    chmod 750 "$DEPLOY_DIR"/{logs,data,backup}
    chmod 700 "$DEPLOY_DIR/data/pids"
    
    # 设置配置文件权限
    chmod 640 "$DEPLOY_DIR/config"/*.json
    chmod 640 "$DEPLOY_DIR/config"/*.env
    
    log_info "权限设置完成"
}

# 安装前端依赖
install_frontend_deps() {
    log_info "安装前端依赖..."
    
    cd "$DEPLOY_DIR/frontend"
    
    if [ ! -d "node_modules" ]; then
        log_info "安装 npm 依赖包..."
        npm install
    else
        log_info "npm 依赖包已存在"
    fi
    
    log_info "前端依赖安装完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 创建数据库目录
    mkdir -p "$DEPLOY_DIR/data"
    
    # 这里可以添加数据库初始化逻辑
    # 目前使用SQLite，会在首次运行时自动创建
    
    log_info "数据库初始化完成"
}

# 创建系统服务（可选）
create_system_service() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        create_launchd_service
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        create_systemd_service
    fi
}

# 创建 macOS LaunchDaemon
create_launchd_service() {
    log_info "创建 macOS LaunchDaemon 服务..."
    
    SERVICE_FILE="$HOME/Library/LaunchAgents/com.timing-server.plist"
    
    cat > "$SERVICE_FILE" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.timing-server</string>
    <key>ProgramArguments</key>
    <array>
        <string>$DEPLOY_DIR/scripts/timing-server-control.sh</string>
        <string>start</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>WorkingDirectory</key>
    <string>$DEPLOY_DIR</string>
    <key>StandardOutPath</key>
    <string>$DEPLOY_DIR/logs/launchd.out</string>
    <key>StandardErrorPath</key>
    <string>$DEPLOY_DIR/logs/launchd.err</string>
</dict>
</plist>
EOF
    
    log_info "LaunchDaemon 服务文件已创建: $SERVICE_FILE"
    log_info "使用以下命令管理服务:"
    log_info "  启动: launchctl load $SERVICE_FILE"
    log_info "  停止: launchctl unload $SERVICE_FILE"
}

# 创建 Linux systemd 服务
create_systemd_service() {
    log_info "创建 systemd 服务..."
    
    SERVICE_FILE="/etc/systemd/system/timing-server.service"
    
    sudo tee "$SERVICE_FILE" > /dev/null << EOF
[Unit]
Description=高精度授时服务器系统
After=network.target

[Service]
Type=forking
User=$INSTALL_USER
Group=$INSTALL_USER
WorkingDirectory=$DEPLOY_DIR
ExecStart=$DEPLOY_DIR/scripts/timing-server-control.sh start
ExecStop=$DEPLOY_DIR/scripts/timing-server-control.sh stop
ExecReload=$DEPLOY_DIR/scripts/timing-server-control.sh restart
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    
    log_info "systemd 服务已创建: $SERVICE_FILE"
    log_info "使用以下命令管理服务:"
    log_info "  启动: sudo systemctl start timing-server"
    log_info "  停止: sudo systemctl stop timing-server"
    log_info "  开机自启: sudo systemctl enable timing-server"
}

# 创建快捷命令
create_shortcuts() {
    log_info "创建快捷命令..."
    
    # 创建符号链接到用户bin目录
    USER_BIN="$HOME/.local/bin"
    mkdir -p "$USER_BIN"
    
    ln -sf "$DEPLOY_DIR/scripts/timing-server-control.sh" "$USER_BIN/timing-server"
    
    # 检查PATH
    if [[ ":$PATH:" != *":$USER_BIN:"* ]]; then
        log_warn "请将 $USER_BIN 添加到您的 PATH 环境变量中"
        log_warn "在 ~/.bashrc 或 ~/.zshrc 中添加: export PATH=\"\$PATH:$USER_BIN\""
    fi
    
    log_info "快捷命令创建完成，可以使用 'timing-server' 命令"
}

# 显示安装完成信息
show_completion_info() {
    echo ""
    echo "========================================"
    log_info "高精度授时服务器系统安装完成！"
    echo "========================================"
    echo ""
    echo "安装目录: $DEPLOY_DIR"
    echo "配置文件: $DEPLOY_DIR/config/"
    echo "日志目录: $DEPLOY_DIR/logs/"
    echo "数据目录: $DEPLOY_DIR/data/"
    echo ""
    echo "启动命令:"
    echo "  $DEPLOY_DIR/scripts/timing-server-control.sh start"
    echo ""
    echo "或使用快捷命令:"
    echo "  timing-server start"
    echo ""
    echo "Web界面: http://localhost:3000"
    echo "默认登录: admin / admin123"
    echo ""
    echo "更多帮助:"
    echo "  timing-server help"
    echo ""
}

# 主安装流程
main() {
    echo "高精度授时服务器系统安装程序"
    echo "============================="
    echo ""
    
    check_requirements
    create_directories
    set_permissions
    install_frontend_deps
    init_database
    
    # 询问是否创建系统服务
    read -p "是否创建系统服务以支持开机自启动？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_system_service
    fi
    
    create_shortcuts
    show_completion_info
    
    # 询问是否立即启动
    read -p "是否立即启动服务？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        "$DEPLOY_DIR/scripts/timing-server-control.sh" start
    fi
}

# 运行安装
main "$@"
