#!/bin/bash

# 日志轮转脚本
# Log Rotation Script for Timing Server

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$DEPLOY_DIR/logs"
ARCHIVE_DIR="$LOG_DIR/archive"

# 配置参数
MAX_LOG_SIZE="100M"  # 最大日志文件大小
MAX_LOG_FILES=10     # 保留的日志文件数量
COMPRESS_LOGS=true   # 是否压缩旧日志

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 创建归档目录
mkdir -p "$ARCHIVE_DIR"

# 获取文件大小（字节）
get_file_size() {
    local file="$1"
    if [ -f "$file" ]; then
        stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0
    else
        echo 0
    fi
}

# 转换大小单位到字节
size_to_bytes() {
    local size="$1"
    local number=$(echo "$size" | sed 's/[^0-9]*//g')
    local unit=$(echo "$size" | sed 's/[0-9]*//g' | tr '[:lower:]' '[:upper:]')
    
    case "$unit" in
        "K"|"KB") echo $((number * 1024)) ;;
        "M"|"MB") echo $((number * 1024 * 1024)) ;;
        "G"|"GB") echo $((number * 1024 * 1024 * 1024)) ;;
        *) echo "$number" ;;
    esac
}

# 轮转单个日志文件
rotate_log() {
    local log_file="$1"
    local base_name=$(basename "$log_file")
    local current_size=$(get_file_size "$log_file")
    local max_size_bytes=$(size_to_bytes "$MAX_LOG_SIZE")
    
    if [ ! -f "$log_file" ]; then
        return 0
    fi
    
    if [ "$current_size" -lt "$max_size_bytes" ]; then
        return 0
    fi
    
    log_info "轮转日志文件: $log_file (当前大小: $(($current_size / 1024 / 1024))MB)"
    
    # 移动现有的编号日志文件
    for i in $(seq $((MAX_LOG_FILES - 1)) -1 1); do
        local old_file="$ARCHIVE_DIR/${base_name}.$i"
        local new_file="$ARCHIVE_DIR/${base_name}.$((i + 1))"
        
        if [ -f "$old_file" ]; then
            if [ "$COMPRESS_LOGS" = true ] && [[ ! "$old_file" =~ \.gz$ ]]; then
                gzip "$old_file" && mv "${old_file}.gz" "${new_file}.gz"
            else
                mv "$old_file" "$new_file"
            fi
        fi
    done
    
    # 移动当前日志文件
    local archived_file="$ARCHIVE_DIR/${base_name}.1"
    cp "$log_file" "$archived_file"
    
    # 清空当前日志文件（保持文件句柄）
    > "$log_file"
    
    # 压缩归档文件
    if [ "$COMPRESS_LOGS" = true ]; then
        gzip "$archived_file"
        log_info "已压缩归档文件: ${archived_file}.gz"
    fi
    
    # 删除超出保留数量的旧文件
    local max_archive_num=$((MAX_LOG_FILES + 1))
    for ext in "" ".gz"; do
        local old_archive="$ARCHIVE_DIR/${base_name}.${max_archive_num}${ext}"
        if [ -f "$old_archive" ]; then
            rm -f "$old_archive"
            log_info "删除旧归档文件: $old_archive"
        fi
    done
}

# 清理空日志文件
cleanup_empty_logs() {
    log_info "清理空日志文件..."
    
    find "$LOG_DIR" -name "*.log" -size 0 -mtime +1 -delete 2>/dev/null || true
    find "$ARCHIVE_DIR" -name "*.log" -size 0 -delete 2>/dev/null || true
}

# 生成日志统计报告
generate_log_stats() {
    local stats_file="$LOG_DIR/log_stats.txt"
    
    {
        echo "日志统计报告 - $(date)"
        echo "=========================="
        echo ""
        
        echo "当前日志文件:"
        find "$LOG_DIR" -maxdepth 1 -name "*.log" -exec ls -lh {} \; | while read -r line; do
            echo "  $line"
        done
        
        echo ""
        echo "归档日志文件:"
        find "$ARCHIVE_DIR" -name "*.log*" -exec ls -lh {} \; | while read -r line; do
            echo "  $line"
        done
        
        echo ""
        echo "磁盘使用情况:"
        du -sh "$LOG_DIR" 2>/dev/null || echo "  无法获取磁盘使用信息"
        
        echo ""
        echo "最近的错误日志 (最近100行):"
        if [ -f "$LOG_DIR/timing-server.log" ]; then
            tail -100 "$LOG_DIR/timing-server.log" | grep -i "error\|fail\|exception" | tail -10 || echo "  无错误日志"
        else
            echo "  日志文件不存在"
        fi
        
    } > "$stats_file"
    
    log_info "日志统计报告已生成: $stats_file"
}

# 主函数
main() {
    log_info "开始日志轮转..."
    
    # 轮转各个日志文件
    rotate_log "$LOG_DIR/timing-server.log"
    rotate_log "$LOG_DIR/http-api.log"
    rotate_log "$LOG_DIR/websocket.log"
    rotate_log "$LOG_DIR/frontend.log"
    rotate_log "$LOG_DIR/launchd.out"
    rotate_log "$LOG_DIR/launchd.err"
    
    # 清理空文件
    cleanup_empty_logs
    
    # 生成统计报告
    generate_log_stats
    
    log_info "日志轮转完成"
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
