#!/bin/bash

# 高精度授时服务器系统控制脚本
# Timing Server Control Script v1.0.0

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(dirname "$SCRIPT_DIR")"
BIN_DIR="$DEPLOY_DIR/bin"
CONFIG_DIR="$DEPLOY_DIR/config"
LOG_DIR="$DEPLOY_DIR/logs"
DATA_DIR="$DEPLOY_DIR/data"

# 加载环境变量
if [ -f "$CONFIG_DIR/environment.env" ]; then
    source "$CONFIG_DIR/environment.env"
fi

# PID文件路径
PID_DIR="$DATA_DIR/pids"
mkdir -p "$PID_DIR"

TIMING_SERVER_PID="$PID_DIR/timing-server.pid"
HTTP_API_PID="$PID_DIR/http-api.pid"
WEBSOCKET_PID="$PID_DIR/websocket.pid"
FRONTEND_PID="$PID_DIR/frontend.pid"

# 日志文件
MAIN_LOG="$LOG_DIR/timing-server.log"
HTTP_LOG="$LOG_DIR/http-api.log"
WEBSOCKET_LOG="$LOG_DIR/websocket.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    if [ "$DEBUG_MODE" = "1" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
    fi
}

# 检查进程是否运行
is_running() {
    local pid_file="$1"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动主授时服务器
start_timing_server() {
    log_info "启动主授时服务器..."
    
    if is_running "$TIMING_SERVER_PID"; then
        log_warn "主授时服务器已在运行"
        return 0
    fi
    
    cd "$BIN_DIR"
    nohup ./timing-server -c "$CONFIG_DIR/production.json" >> "$MAIN_LOG" 2>&1 &
    echo $! > "$TIMING_SERVER_PID"
    
    sleep 2
    if is_running "$TIMING_SERVER_PID"; then
        log_info "主授时服务器启动成功 (PID: $(cat $TIMING_SERVER_PID))"
        return 0
    else
        log_error "主授时服务器启动失败"
        return 1
    fi
}

# 启动HTTP API服务器
start_http_api() {
    log_info "启动HTTP API服务器..."
    
    if is_running "$HTTP_API_PID"; then
        log_warn "HTTP API服务器已在运行"
        return 0
    fi
    
    cd "$BIN_DIR"
    nohup ./simple_http_server >> "$HTTP_LOG" 2>&1 &
    echo $! > "$HTTP_API_PID"
    
    sleep 2
    if is_running "$HTTP_API_PID"; then
        log_info "HTTP API服务器启动成功 (PID: $(cat $HTTP_API_PID))"
        return 0
    else
        log_error "HTTP API服务器启动失败"
        return 1
    fi
}

# 启动WebSocket服务器
start_websocket() {
    log_info "启动WebSocket服务器..."

    if is_running "$WEBSOCKET_PID"; then
        log_warn "WebSocket服务器已在运行"
        return 0
    fi

    cd "$SCRIPT_DIR"
    nohup node websocket_server.js >> "$WEBSOCKET_LOG" 2>&1 &
    echo $! > "$WEBSOCKET_PID"

    sleep 2
    if is_running "$WEBSOCKET_PID"; then
        log_info "WebSocket服务器启动成功 (PID: $(cat $WEBSOCKET_PID))"
        return 0
    else
        log_error "WebSocket服务器启动失败"
        return 1
    fi
}

# 启动前端服务器
start_frontend() {
    log_info "启动前端服务器..."
    
    if is_running "$FRONTEND_PID"; then
        log_warn "前端服务器已在运行"
        return 0
    fi
    
    cd "$DEPLOY_DIR/frontend"
    nohup npm run dev >> "$FRONTEND_LOG" 2>&1 &
    echo $! > "$FRONTEND_PID"
    
    sleep 3
    if is_running "$FRONTEND_PID"; then
        log_info "前端服务器启动成功 (PID: $(cat $FRONTEND_PID))"
        return 0
    else
        log_error "前端服务器启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    local service_name="$1"
    local pid_file="$2"
    
    log_info "停止${service_name}..."
    
    if is_running "$pid_file"; then
        local pid=$(cat "$pid_file")
        kill "$pid"
        
        # 等待进程结束
        local count=0
        while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        if kill -0 "$pid" 2>/dev/null; then
            log_warn "${service_name}未能正常停止，强制终止"
            kill -9 "$pid"
        fi
        
        rm -f "$pid_file"
        log_info "${service_name}已停止"
    else
        log_warn "${service_name}未在运行"
    fi
}

# 启动所有服务
start_all() {
    log_info "启动高精度授时服务器系统..."
    
    start_timing_server
    start_http_api
    start_websocket
    start_frontend
    
    log_info "系统启动完成！"
    log_info "Web界面: http://localhost:3000"
    log_info "API接口: http://localhost:8080"
    log_info "WebSocket: ws://localhost:8081"
}

# 停止所有服务
stop_all() {
    log_info "停止高精度授时服务器系统..."
    
    stop_service "前端服务器" "$FRONTEND_PID"
    stop_service "WebSocket服务器" "$WEBSOCKET_PID"
    stop_service "HTTP API服务器" "$HTTP_API_PID"
    stop_service "主授时服务器" "$TIMING_SERVER_PID"
    
    log_info "系统已停止"
}

# 重启所有服务
restart_all() {
    log_info "重启高精度授时服务器系统..."
    stop_all
    sleep 3
    start_all
}

# 显示服务状态
show_status() {
    echo "高精度授时服务器系统状态"
    echo "=========================="
    
    if is_running "$TIMING_SERVER_PID"; then
        echo -e "主授时服务器:    ${GREEN}运行中${NC} (PID: $(cat $TIMING_SERVER_PID))"
    else
        echo -e "主授时服务器:    ${RED}已停止${NC}"
    fi
    
    if is_running "$HTTP_API_PID"; then
        echo -e "HTTP API服务器:  ${GREEN}运行中${NC} (PID: $(cat $HTTP_API_PID))"
    else
        echo -e "HTTP API服务器:  ${RED}已停止${NC}"
    fi
    
    if is_running "$WEBSOCKET_PID"; then
        echo -e "WebSocket服务器: ${GREEN}运行中${NC} (PID: $(cat $WEBSOCKET_PID))"
    else
        echo -e "WebSocket服务器: ${RED}已停止${NC}"
    fi
    
    if is_running "$FRONTEND_PID"; then
        echo -e "前端服务器:      ${GREEN}运行中${NC} (PID: $(cat $FRONTEND_PID))"
    else
        echo -e "前端服务器:      ${RED}已停止${NC}"
    fi
    
    echo ""
    echo "访问地址:"
    echo "  Web界面: http://localhost:3000"
    echo "  API接口: http://localhost:8080"
    echo "  WebSocket: ws://localhost:8081"
}

# 显示帮助信息
show_help() {
    echo "高精度授时服务器系统控制脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|help}"
    echo ""
    echo "命令:"
    echo "  start   - 启动所有服务"
    echo "  stop    - 停止所有服务"
    echo "  restart - 重启所有服务"
    echo "  status  - 显示服务状态"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "单独服务控制:"
    echo "  start-timing    - 启动主授时服务器"
    echo "  start-api       - 启动HTTP API服务器"
    echo "  start-websocket - 启动WebSocket服务器"
    echo "  start-frontend  - 启动前端服务器"
}

# 主逻辑
case "$1" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        restart_all
        ;;
    status)
        show_status
        ;;
    start-timing)
        start_timing_server
        ;;
    start-api)
        start_http_api
        ;;
    start-websocket)
        start_websocket
        ;;
    start-frontend)
        start_frontend
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|help}"
        exit 1
        ;;
esac

exit 0
