#!/bin/bash

# 系统监控脚本
# System Monitoring Script for Timing Server

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$DEPLOY_DIR/logs"
DATA_DIR="$DEPLOY_DIR/data"
PID_DIR="$DATA_DIR/pids"

# 监控配置
CPU_THRESHOLD=80        # CPU使用率阈值 (%)
MEMORY_THRESHOLD=80     # 内存使用率阈值 (%)
DISK_THRESHOLD=90       # 磁盘使用率阈值 (%)
RESPONSE_TIMEOUT=10     # 响应超时时间 (秒)

# 告警配置
ALERT_LOG="$LOG_DIR/alerts.log"
MONITOR_LOG="$LOG_DIR/monitor.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    local msg="$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$MONITOR_LOG"
}

log_warn() {
    local msg="$(date '+%Y-%m-%d %H:%M:%S') [WARN] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$MONITOR_LOG"
    echo "$msg" >> "$ALERT_LOG"
}

log_error() {
    local msg="$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$MONITOR_LOG"
    echo "$msg" >> "$ALERT_LOG"
}

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 检查进程是否运行
is_process_running() {
    local pid_file="$1"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        fi
    fi
    return 1
}

# 获取进程CPU和内存使用率
get_process_stats() {
    local pid="$1"
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        # macOS 和 Linux 兼容的 ps 命令
        ps -p "$pid" -o pid,pcpu,pmem,rss,vsz,time,comm 2>/dev/null | tail -1
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源使用情况..."
    
    # CPU 使用率
    if command -v top >/dev/null 2>&1; then
        local cpu_usage
        if [[ "$OSTYPE" == "darwin"* ]]; then
            cpu_usage=$(top -l 1 -n 0 | grep "CPU usage" | awk '{print $3}' | sed 's/%//')
        else
            cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
        fi
        
        if [ -n "$cpu_usage" ] && [ "${cpu_usage%.*}" -gt "$CPU_THRESHOLD" ]; then
            log_warn "CPU使用率过高: ${cpu_usage}% (阈值: ${CPU_THRESHOLD}%)"
        else
            log_info "CPU使用率: ${cpu_usage}%"
        fi
    fi
    
    # 内存使用率
    if command -v free >/dev/null 2>&1; then
        local mem_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
        if [ "${mem_usage%.*}" -gt "$MEMORY_THRESHOLD" ]; then
            log_warn "内存使用率过高: ${mem_usage}% (阈值: ${MEMORY_THRESHOLD}%)"
        else
            log_info "内存使用率: ${mem_usage}%"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        local mem_pressure=$(memory_pressure | grep "System-wide memory free percentage" | awk '{print $5}' | sed 's/%//')
        if [ -n "$mem_pressure" ]; then
            local mem_usage=$((100 - mem_pressure))
            if [ "$mem_usage" -gt "$MEMORY_THRESHOLD" ]; then
                log_warn "内存使用率过高: ${mem_usage}% (阈值: ${MEMORY_THRESHOLD}%)"
            else
                log_info "内存使用率: ${mem_usage}%"
            fi
        fi
    fi
    
    # 磁盘使用率
    local disk_usage=$(df "$DEPLOY_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt "$DISK_THRESHOLD" ]; then
        log_warn "磁盘使用率过高: ${disk_usage}% (阈值: ${DISK_THRESHOLD}%)"
    else
        log_info "磁盘使用率: ${disk_usage}%"
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    local services=(
        "timing-server:$PID_DIR/timing-server.pid"
        "http-api:$PID_DIR/http-api.pid"
        "websocket:$PID_DIR/websocket.pid"
        "frontend:$PID_DIR/frontend.pid"
    )
    
    for service_info in "${services[@]}"; do
        local service_name="${service_info%:*}"
        local pid_file="${service_info#*:}"
        
        if is_process_running "$pid_file"; then
            local pid=$(cat "$pid_file")
            local stats=$(get_process_stats "$pid")
            if [ -n "$stats" ]; then
                local cpu=$(echo "$stats" | awk '{print $2}')
                local mem=$(echo "$stats" | awk '{print $3}')
                log_info "$service_name 运行正常 (PID: $pid, CPU: ${cpu}%, MEM: ${mem}%)"
            else
                log_info "$service_name 运行正常 (PID: $pid)"
            fi
        else
            log_error "$service_name 未运行"
        fi
    done
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    local ports=(
        "8080:HTTP API"
        "8081:WebSocket"
        "3000:Frontend"
    )
    
    for port_info in "${ports[@]}"; do
        local port="${port_info%:*}"
        local service="${port_info#*:}"
        
        if command -v nc >/dev/null 2>&1; then
            if nc -z localhost "$port" 2>/dev/null; then
                log_info "$service 端口 $port 可访问"
            else
                log_warn "$service 端口 $port 不可访问"
            fi
        elif command -v telnet >/dev/null 2>&1; then
            if timeout 3 telnet localhost "$port" </dev/null >/dev/null 2>&1; then
                log_info "$service 端口 $port 可访问"
            else
                log_warn "$service 端口 $port 不可访问"
            fi
        fi
    done
}

# 检查API响应
check_api_response() {
    log_info "检查API响应..."
    
    if command -v curl >/dev/null 2>&1; then
        # 检查HTTP API健康状态
        local api_response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout "$RESPONSE_TIMEOUT" "http://localhost:8080/health" 2>/dev/null || echo "000")
        
        if [ "$api_response" = "200" ]; then
            log_info "HTTP API 响应正常"
        else
            log_warn "HTTP API 响应异常 (状态码: $api_response)"
        fi
        
        # 检查WebSocket连接
        local ws_test=$(curl -s --connect-timeout "$RESPONSE_TIMEOUT" -H "Upgrade: websocket" -H "Connection: Upgrade" "http://localhost:8081" 2>/dev/null && echo "ok" || echo "fail")
        
        if [ "$ws_test" = "ok" ]; then
            log_info "WebSocket 连接正常"
        else
            log_warn "WebSocket 连接异常"
        fi
    fi
}

# 检查日志错误
check_log_errors() {
    log_info "检查最近的错误日志..."
    
    local log_files=(
        "$LOG_DIR/timing-server.log"
        "$LOG_DIR/http-api.log"
        "$LOG_DIR/websocket.log"
        "$LOG_DIR/frontend.log"
    )
    
    local error_count=0
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            local recent_errors=$(tail -100 "$log_file" | grep -i "error\|fail\|exception\|fatal" | wc -l)
            if [ "$recent_errors" -gt 0 ]; then
                log_warn "$(basename "$log_file") 发现 $recent_errors 个错误"
                error_count=$((error_count + recent_errors))
            fi
        fi
    done
    
    if [ "$error_count" -eq 0 ]; then
        log_info "未发现错误日志"
    else
        log_warn "总计发现 $error_count 个错误"
    fi
}

# 生成监控报告
generate_report() {
    local report_file="$LOG_DIR/monitor_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "高精度授时服务器系统监控报告"
        echo "=============================="
        echo "生成时间: $(date)"
        echo ""
        
        echo "系统信息:"
        echo "  操作系统: $OSTYPE"
        echo "  主机名: $(hostname)"
        echo "  运行时间: $(uptime)"
        echo ""
        
        echo "服务状态:"
        check_services 2>&1 | grep -E "(运行正常|未运行)" | sed 's/^/  /'
        echo ""
        
        echo "资源使用:"
        df -h "$DEPLOY_DIR" | tail -1 | awk '{print "  磁盘使用: " $5 " (" $3 "/" $2 ")"}'
        echo ""
        
        echo "最近告警 (最近10条):"
        if [ -f "$ALERT_LOG" ]; then
            tail -10 "$ALERT_LOG" | sed 's/^/  /' || echo "  无告警记录"
        else
            echo "  无告警记录"
        fi
        
    } > "$report_file"
    
    log_info "监控报告已生成: $report_file"
}

# 主监控函数
main() {
    log_info "开始系统监控检查..."
    
    check_system_resources
    check_services
    check_network
    check_api_response
    check_log_errors
    
    if [ "$1" = "--report" ]; then
        generate_report
    fi
    
    log_info "监控检查完成"
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
