#!/bin/bash

# 系统服务安装脚本
# System Service Installation Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    else
        echo "unknown"
    fi
}

# 安装 macOS LaunchAgent
install_macos_service() {
    log_info "安装 macOS LaunchAgent 服务..."
    
    LAUNCH_AGENTS_DIR="$HOME/Library/LaunchAgents"
    SERVICE_FILE="$LAUNCH_AGENTS_DIR/com.timing-server.plist"
    
    # 创建 LaunchAgents 目录
    mkdir -p "$LAUNCH_AGENTS_DIR"
    
    # 复制服务文件
    cp "$DEPLOY_DIR/config/com.timing-server.plist" "$SERVICE_FILE"
    
    # 加载服务
    launchctl unload "$SERVICE_FILE" 2>/dev/null || true
    launchctl load "$SERVICE_FILE"
    
    log_info "macOS 服务安装完成"
    log_info "服务文件: $SERVICE_FILE"
    log_info ""
    log_info "服务管理命令:"
    log_info "  启动服务: launchctl start com.timing-server"
    log_info "  停止服务: launchctl stop com.timing-server"
    log_info "  卸载服务: launchctl unload $SERVICE_FILE"
    log_info "  重新加载: launchctl unload $SERVICE_FILE && launchctl load $SERVICE_FILE"
}

# 安装 Linux systemd 服务
install_linux_service() {
    log_info "安装 Linux systemd 服务..."
    
    SERVICE_FILE="/etc/systemd/system/timing-server.service"
    USER=$(whoami)
    
    # 创建 systemd 服务文件
    sudo tee "$SERVICE_FILE" > /dev/null << EOF
[Unit]
Description=高精度授时服务器系统
Documentation=https://github.com/timing-server/docs
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=forking
User=$USER
Group=$USER
WorkingDirectory=$DEPLOY_DIR
Environment=TIMING_SERVER_HOME=$DEPLOY_DIR
ExecStart=$DEPLOY_DIR/scripts/timing-server-control.sh start
ExecStop=$DEPLOY_DIR/scripts/timing-server-control.sh stop
ExecReload=$DEPLOY_DIR/scripts/timing-server-control.sh restart
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DEPLOY_DIR

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载 systemd
    sudo systemctl daemon-reload
    
    log_info "Linux systemd 服务安装完成"
    log_info "服务文件: $SERVICE_FILE"
    log_info ""
    log_info "服务管理命令:"
    log_info "  启动服务: sudo systemctl start timing-server"
    log_info "  停止服务: sudo systemctl stop timing-server"
    log_info "  重启服务: sudo systemctl restart timing-server"
    log_info "  查看状态: sudo systemctl status timing-server"
    log_info "  开机自启: sudo systemctl enable timing-server"
    log_info "  禁用自启: sudo systemctl disable timing-server"
    log_info "  查看日志: sudo journalctl -u timing-server -f"
}

# 创建快捷命令
create_shortcuts() {
    log_info "创建快捷命令..."
    
    # 创建用户 bin 目录
    USER_BIN="$HOME/.local/bin"
    mkdir -p "$USER_BIN"
    
    # 创建符号链接
    ln -sf "$DEPLOY_DIR/scripts/timing-server-control.sh" "$USER_BIN/timing-server"
    
    # 检查 PATH
    if [[ ":$PATH:" != *":$USER_BIN:"* ]]; then
        log_warn "请将 $USER_BIN 添加到您的 PATH 环境变量中"
        log_warn "在 ~/.bashrc 或 ~/.zshrc 中添加:"
        log_warn "export PATH=\"\$PATH:$USER_BIN\""
        
        # 尝试自动添加到 shell 配置文件
        SHELL_CONFIG=""
        if [ -n "$ZSH_VERSION" ]; then
            SHELL_CONFIG="$HOME/.zshrc"
        elif [ -n "$BASH_VERSION" ]; then
            SHELL_CONFIG="$HOME/.bashrc"
        fi
        
        if [ -n "$SHELL_CONFIG" ] && [ -f "$SHELL_CONFIG" ]; then
            if ! grep -q "$USER_BIN" "$SHELL_CONFIG"; then
                echo "" >> "$SHELL_CONFIG"
                echo "# Timing Server" >> "$SHELL_CONFIG"
                echo "export PATH=\"\$PATH:$USER_BIN\"" >> "$SHELL_CONFIG"
                log_info "已自动添加到 $SHELL_CONFIG"
                log_info "请运行 'source $SHELL_CONFIG' 或重新打开终端"
            fi
        fi
    fi
    
    log_info "快捷命令创建完成"
}

# 主函数
main() {
    echo "高精度授时服务器系统服务安装"
    echo "============================="
    echo ""
    
    OS=$(detect_os)
    
    case "$OS" in
        "macos")
            install_macos_service
            ;;
        "linux")
            install_linux_service
            ;;
        *)
            log_error "不支持的操作系统: $OSTYPE"
            exit 1
            ;;
    esac
    
    create_shortcuts
    
    echo ""
    log_info "系统服务安装完成！"
    echo ""
    log_info "现在您可以使用以下方式管理服务:"
    log_info "1. 使用系统服务命令（如上所示）"
    log_info "2. 使用控制脚本: $DEPLOY_DIR/scripts/timing-server-control.sh"
    log_info "3. 使用快捷命令: timing-server"
    echo ""
}

# 运行安装
main "$@"
