#!/bin/bash

# 定时任务设置脚本
# Cron Job Setup Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOY_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 创建临时crontab文件
TEMP_CRON=$(mktemp)

# 获取当前crontab
crontab -l 2>/dev/null > "$TEMP_CRON" || true

# 添加定时任务
add_cron_job() {
    local schedule="$1"
    local command="$2"
    local description="$3"
    
    # 检查是否已存在
    if grep -q "$command" "$TEMP_CRON" 2>/dev/null; then
        log_warn "定时任务已存在: $description"
        return 0
    fi
    
    # 添加注释和任务
    echo "" >> "$TEMP_CRON"
    echo "# $description" >> "$TEMP_CRON"
    echo "$schedule $command" >> "$TEMP_CRON"
    
    log_info "添加定时任务: $description"
}

# 设置定时任务
setup_cron_jobs() {
    log_info "设置定时任务..."
    
    # 每5分钟检查系统状态
    add_cron_job \
        "*/5 * * * *" \
        "$SCRIPT_DIR/monitor.sh >/dev/null 2>&1" \
        "系统监控检查 (每5分钟)"
    
    # 每小时轮转日志
    add_cron_job \
        "0 * * * *" \
        "$SCRIPT_DIR/logrotate.sh >/dev/null 2>&1" \
        "日志轮转 (每小时)"
    
    # 每天生成监控报告
    add_cron_job \
        "0 6 * * *" \
        "$SCRIPT_DIR/monitor.sh --report >/dev/null 2>&1" \
        "生成监控报告 (每天6点)"
    
    # 每周清理旧日志
    add_cron_job \
        "0 2 * * 0" \
        "find $DEPLOY_DIR/logs/archive -name '*.gz' -mtime +30 -delete 2>/dev/null" \
        "清理30天前的日志 (每周日2点)"
    
    # 每天备份配置文件
    add_cron_job \
        "0 3 * * *" \
        "tar -czf $DEPLOY_DIR/backup/config_backup_\$(date +\\%Y\\%m\\%d).tar.gz -C $DEPLOY_DIR config/ 2>/dev/null" \
        "备份配置文件 (每天3点)"
    
    # 安装新的crontab
    crontab "$TEMP_CRON"
    
    # 清理临时文件
    rm -f "$TEMP_CRON"
    
    log_info "定时任务设置完成"
}

# 显示当前定时任务
show_cron_jobs() {
    echo "当前定时任务:"
    echo "=============="
    crontab -l 2>/dev/null | grep -E "(monitor|logrotate|backup)" || echo "未找到相关定时任务"
}

# 删除定时任务
remove_cron_jobs() {
    log_info "删除定时任务..."
    
    # 获取当前crontab
    crontab -l 2>/dev/null > "$TEMP_CRON" || true
    
    # 删除相关任务
    grep -v -E "(monitor\.sh|logrotate\.sh|config_backup)" "$TEMP_CRON" > "${TEMP_CRON}.new" || true
    
    # 删除相关注释
    grep -v -E "(系统监控检查|日志轮转|生成监控报告|清理.*日志|备份配置文件)" "${TEMP_CRON}.new" > "$TEMP_CRON" || true
    
    # 安装新的crontab
    crontab "$TEMP_CRON"
    
    # 清理临时文件
    rm -f "$TEMP_CRON" "${TEMP_CRON}.new"
    
    log_info "定时任务删除完成"
}

# 主函数
main() {
    case "${1:-setup}" in
        "setup")
            setup_cron_jobs
            show_cron_jobs
            ;;
        "show")
            show_cron_jobs
            ;;
        "remove")
            remove_cron_jobs
            ;;
        *)
            echo "用法: $0 {setup|show|remove}"
            echo ""
            echo "命令:"
            echo "  setup  - 设置定时任务 (默认)"
            echo "  show   - 显示当前定时任务"
            echo "  remove - 删除定时任务"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
