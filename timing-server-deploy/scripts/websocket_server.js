const WebSocket = require('ws');
const http = require('http');

// 创建HTTP服务器
const server = http.createServer();

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
    server,
    perMessageDeflate: false 
});

console.log('=== 高精度授时服务器WebSocket服务 ===');

let clientCount = 0;

// 生成状态数据
function generateStatusData() {
    const now = new Date();
    return {
        type: 'status_update',
        timestamp: now.toISOString(),
        system_status: 'normal',
        sync_status: 'synchronized',
        current_source: 'GPS',
        precision: '±50ns',
        connections: clientCount,
        time: {
            year: now.getFullYear(),
            month: now.getMonth() + 1,
            day: now.getDate(),
            hour: now.getHours(),
            minute: now.getMinutes(),
            second: now.getSeconds(),
            millisecond: now.getMilliseconds()
        },
        hardware: {
            gnss_status: 'active',
            ptp_status: 'synchronized',
            ntp_status: 'active',
            temperature: 25.3,
            voltage: 12.1
        },
        performance: {
            cpu_usage: Math.random() * 20 + 10,
            memory_usage: Math.random() * 30 + 40,
            network_latency: Math.random() * 5 + 1
        }
    };
}

// 处理WebSocket连接
wss.on('connection', function connection(ws, request) {
    clientCount++;
    console.log(`新客户端连接，当前连接数: ${clientCount}`);
    console.log(`客户端地址: ${request.socket.remoteAddress}`);
    
    // 发送欢迎消息
    const welcomeMessage = {
        type: 'welcome',
        message: '连接到高精度授时服务器WebSocket服务',
        server_time: new Date().toISOString(),
        client_id: Math.random().toString(36).substr(2, 9)
    };
    
    ws.send(JSON.stringify(welcomeMessage));
    
    // 立即发送一次状态数据
    ws.send(JSON.stringify(generateStatusData()));
    
    // 设置定期发送状态更新
    const statusInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(generateStatusData()));
        } else {
            clearInterval(statusInterval);
        }
    }, 1000); // 每秒发送一次
    
    // 处理客户端消息
    ws.on('message', function incoming(message) {
        try {
            const data = JSON.parse(message);
            console.log('收到客户端消息:', data);
            
            // 处理不同类型的消息
            switch (data.type) {
                case 'ping':
                    ws.send(JSON.stringify({
                        type: 'pong',
                        timestamp: new Date().toISOString()
                    }));
                    break;
                    
                case 'subscribe':
                    ws.send(JSON.stringify({
                        type: 'subscription_ack',
                        events: data.events || ['status_update'],
                        message: '订阅成功'
                    }));
                    break;
                    
                case 'auth':
                    ws.send(JSON.stringify({
                        type: 'auth_response',
                        success: true,
                        message: '认证成功'
                    }));
                    break;
                    
                default:
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: '未知消息类型'
                    }));
            }
        } catch (error) {
            console.error('解析客户端消息失败:', error);
            ws.send(JSON.stringify({
                type: 'error',
                message: '消息格式错误'
            }));
        }
    });
    
    // 处理连接关闭
    ws.on('close', function close() {
        clientCount--;
        clearInterval(statusInterval);
        console.log(`客户端断开连接，当前连接数: ${clientCount}`);
    });
    
    // 处理连接错误
    ws.on('error', function error(err) {
        console.error('WebSocket连接错误:', err);
        clientCount--;
        clearInterval(statusInterval);
    });
});

// 处理服务器错误
wss.on('error', function error(err) {
    console.error('WebSocket服务器错误:', err);
});

// 启动服务器
const PORT = 8081;
server.listen(PORT, '0.0.0.0', function listening() {
    console.log(`WebSocket服务器启动成功，监听端口: ${PORT}`);
    console.log(`WebSocket地址: ws://localhost:${PORT}`);
    console.log('等待客户端连接...');
});

// 优雅关闭
process.on('SIGINT', function() {
    console.log('\n收到SIGINT信号，正在关闭服务器...');
    
    // 关闭所有WebSocket连接
    wss.clients.forEach(function each(ws) {
        ws.close();
    });
    
    // 关闭服务器
    server.close(function() {
        console.log('WebSocket服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGTERM', function() {
    console.log('\n收到SIGTERM信号，正在关闭服务器...');
    
    // 关闭所有WebSocket连接
    wss.clients.forEach(function each(ws) {
        ws.close();
    });
    
    // 关闭服务器
    server.close(function() {
        console.log('WebSocket服务器已关闭');
        process.exit(0);
    });
});

// 定期输出服务器统计信息
setInterval(() => {
    if (clientCount > 0) {
        console.log(`服务器统计 - 活跃连接: ${clientCount}, 运行时间: ${Math.floor(process.uptime())}秒`);
    }
}, 30000); // 每30秒输出一次
