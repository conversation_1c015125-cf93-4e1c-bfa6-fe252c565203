#!/usr/bin/env node

const http = require('http');
const url = require('url');

const PORT = 8080;

// 模拟数据
const mockData = {
  systemStatus: {
    status: 'normal',
    uptime: '15天 8小时 32分钟',
    temperature: 25.3,
    cpuUsage: 15.2,
    memoryUsage: 42.8,
    diskUsage: 68.5,
    networkStatus: 'connected',
    lastSync: new Date().toISOString()
  },
  timeSourceInfo: {
    currentSource: 'GPS',
    sources: {
      gps: { status: 'active', quality: 'excellent', satellites: 12 },
      ptp: { status: 'standby', quality: 'good', offset: '±10ns' },
      ntp: { status: 'active', quality: 'good', stratum: 2 },
      atomic: { status: 'standby', quality: 'excellent', drift: '±1e-12' }
    },
    precision: '±50ns',
    accuracy: '±1μs'
  },
  signalOutput: {
    channels: [
      { id: 1, name: '1PPS', enabled: true, status: 'active', frequency: '1Hz' },
      { id: 2, name: '10MHz', enabled: true, status: 'active', frequency: '10MHz' },
      { id: 3, name: 'TOD', enabled: false, status: 'inactive', format: 'ASCII' },
      { id: 4, name: 'NTP', enabled: true, status: 'active', port: 123 }
    ],
    totalOutputs: 4,
    activeOutputs: 3
  }
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  
  console.log(`${new Date().toISOString()} - ${req.method} ${path}`);
  
  // 路由处理
  if (path === '/api/v1/auth/login' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { username, password } = JSON.parse(body);
        if (username === 'admin' && password === 'admin123') {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            data: {
              token: 'mock-jwt-token-' + Date.now(),
              refreshToken: 'mock-refresh-token-' + Date.now(),
              user: {
                id: 1,
                username: 'admin',
                role: 'administrator',
                permissions: ['read', 'write', 'admin']
              }
            }
          }));
        } else {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: false,
            error: { message: '用户名或密码错误' }
          }));
        }
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: { message: '请求格式错误' }
        }));
      }
    });
  } else if (path === '/api/v1/auth/user' && req.method === 'GET') {
    // 获取当前用户信息
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        data: {
          id: 1,
          username: 'admin',
          role: 'administrator',
          permissions: ['read', 'write', 'admin']
        }
      }));
    } else {
      res.writeHead(401, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: false,
        error: { message: '未授权访问' }
      }));
    }
  } else if (path === '/api/v1/system/status' && req.method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: mockData.systemStatus
    }));
  } else if (path === '/api/v1/timesource/info' && req.method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: mockData.timeSourceInfo
    }));
  } else if (path === '/api/v1/signal/output' && req.method === 'GET') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: mockData.signalOutput
    }));
  } else {
    // 404 Not Found
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      error: { message: 'API endpoint not found' }
    }));
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`HTTP API服务器已启动，监听端口 ${PORT}`);
  console.log(`服务地址: http://localhost:${PORT}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('HTTP API服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('HTTP API服务器已关闭');
    process.exit(0);
  });
});
