
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高精度授时服务器系统 - 测试报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; font-weight: 700; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        .content { padding: 40px; }
        .summary { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .summary-card { 
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }
        .summary-card:hover { transform: translateY(-5px); }
        .summary-card.success { border-left-color: #28a745; }
        .summary-card.error { border-left-color: #dc3545; }
        .summary-card.info { border-left-color: #007bff; }
        .summary-card h3 { font-size: 1.2rem; margin-bottom: 10px; color: #333; }
        .summary-card .value { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
        .summary-card .label { color: #666; font-size: 0.9rem; }
        .report-section { 
            background: white;
            border-radius: 15px;
            margin-bottom: 30px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .section-header { 
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
        }
        .section-header h2 { color: #333; font-size: 1.5rem; }
        .section-content { padding: 30px; }
        .test-item { 
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid;
            background: #f8f9fa;
        }
        .test-item.success { border-left-color: #28a745; background: #d4edda; }
        .test-item.error { border-left-color: #dc3545; background: #f8d7da; }
        .test-item.warn { border-left-color: #ffc107; background: #fff3cd; }
        .test-item.info { border-left-color: #17a2b8; background: #d1ecf1; }
        .test-time { font-size: 0.85rem; color: #666; margin-bottom: 5px; }
        .test-message { font-weight: 500; }
        .stats { 
            display: flex; 
            justify-content: space-around; 
            text-align: center;
            margin: 20px 0;
        }
        .stat { padding: 15px; }
        .stat-value { font-size: 1.8rem; font-weight: bold; }
        .stat-label { color: #666; font-size: 0.9rem; margin-top: 5px; }
        .footer { 
            text-align: center; 
            padding: 30px;
            background: #f8f9fa;
            color: #666;
        }
        @media (max-width: 768px) {
            .container { margin: 10px; border-radius: 15px; }
            .header { padding: 30px 20px; }
            .header h1 { font-size: 2rem; }
            .content { padding: 20px; }
            .summary { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕐 高精度授时服务器系统</h1>
            <p>测试报告 - 2025/7/26 21:00:16</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="summary-card info">
                    <h3>总测试数</h3>
                    <div class="value">11</div>
                    <div class="label">Total Tests</div>
                </div>
                <div class="summary-card success">
                    <h3>通过测试</h3>
                    <div class="value">9</div>
                    <div class="label">Passed Tests</div>
                </div>
                <div class="summary-card error">
                    <h3>失败测试</h3>
                    <div class="value">2</div>
                    <div class="label">Failed Tests</div>
                </div>
                <div class="summary-card success">
                    <h3>成功率</h3>
                    <div class="value">81.8%</div>
                    <div class="label">Success Rate</div>
                </div>
            </div>
            <div class="report-section">
                <div class="section-header">
                    <h2>后端API测试</h2>
                </div>
                <div class="section-content">
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:45</div>
                        <div class="test-message">=== 开始运行高精度授时服务器系统测试套件 ===</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:45</div>
                        <div class="test-message"></div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:45</div>
                        <div class="test-message">
--- 测试: API服务器连接 ---</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:45</div>
                        <div class="test-message">测试API服务器连接...</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:45</div>
                        <div class="test-message">✅ API服务器连接成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:46</div>
                        <div class="test-message">
--- 测试: 用户认证 ---</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:46</div>
                        <div class="test-message">测试用户认证...</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:46</div>
                        <div class="test-message">✅ 用户认证成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:46</div>
                        <div class="test-message">   Token: mock-jwt-token-17535...</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:47</div>
                        <div class="test-message">
--- 测试: 系统状态API ---</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:47</div>
                        <div class="test-message">测试系统状态API...</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:47</div>
                        <div class="test-message">✅ 系统状态API测试成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:47</div>
                        <div class="test-message">   系统状态: normal</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:47</div>
                        <div class="test-message">   运行时间: 15天 8小时 32分钟</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:48</div>
                        <div class="test-message">
--- 测试: 时间源信息API ---</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:48</div>
                        <div class="test-message">测试时间源信息API...</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:48</div>
                        <div class="test-message">✅ 时间源信息API测试成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:48</div>
                        <div class="test-message">   当前时间源: GPS</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:48</div>
                        <div class="test-message">   精度: ±50ns</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:49</div>
                        <div class="test-message">
--- 测试: 信号输出API ---</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:49</div>
                        <div class="test-message">测试信号输出API...</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:49</div>
                        <div class="test-message">✅ 信号输出API测试成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:49</div>
                        <div class="test-message">   总输出通道: 4</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:49</div>
                        <div class="test-message">   活跃通道: 3</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">
--- 测试: WebSocket连接 ---</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">测试WebSocket连接...</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">✅ WebSocket连接建立成功</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">✅ WebSocket消息接收成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">   消息类型: welcome</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">✅ WebSocket消息接收成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">   消息类型: status_update</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">   系统状态: normal</div>
                    </div>
                    <div class="test-item success">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">✅ WebSocket消息接收成功</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">   消息类型: pong</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:50</div>
                        <div class="test-message">WebSocket连接已关闭</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:51</div>
                        <div class="test-message">
--- 测试: 前端页面访问 ---</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:51</div>
                        <div class="test-message">测试前端页面访问...</div>
                    </div>
                    <div class="test-item error">
                        <div class="test-time">2025/7/26 20:59:51</div>
                        <div class="test-message">❌ 前端页面访问失败: 404</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:52</div>
                        <div class="test-message">
=== 测试结果汇总 ===</div>
                    </div>
                    <div class="test-item info">
                        <div class="test-time">2025/7/26 20:59:52</div>
                        <div class="test-message">总测试数: 7</div>
                    </div>
                    <div class="test-item warn">
                        <div class="test-time">2025/7/26 20:59:52</div>
                        <div class="test-message">通过测试: 6</div>
                    </div>
                    <div class="test-item error">
                        <div class="test-time">2025/7/26 20:59:52</div>
                        <div class="test-message">失败测试: 1</div>
                    </div>
                    <div class="test-item warn">
                        <div class="test-time">2025/7/26 20:59:52</div>
                        <div class="test-message">成功率: 85.7%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>报告生成时间: 2025/7/26 21:00:16</p>
            <p>高精度授时服务器系统 v1.0.0</p>
        </div>
    </div>
</body>
</html>